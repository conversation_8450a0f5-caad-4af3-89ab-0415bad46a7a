var resizeTimeoutFn = 0;
$(window).on('resize', function () {
    clearTimeout(resizeTimeoutFn);
    resizeTimeoutFn = setTimeout(function () {
        loadOpacityDots();
        if ($(window).width() < 768) {
            if ($('.transform-slick-slide-js').length > 0) {
                $('.transform-slick-slide-js').not('.slick-initialized').slick();
                $('.transform-slick-slide-js:not(.full-width-slide)').find('.slick-track').addClass('margin-n30-left');
            }
        }
        //remove tabindex on resize
        $('.focusableElementUnslick-js:not(slick-initialized)').find('[tabindex="-1"]').removeAttr('tabindex');

        forceSkipSameHeightMobile();

        mobileUnslickCountOne.reSize();

        //refresh slick on screen resize to reset its width accordingly
        setTimeout(function () {
            $('.responsive-slick-slider.slick-initialized').not('.opacityDots').slick('setPosition');
            initProductThumbnailModalSlick();
        }, 300)
        //remove display inline block style generated by slick each time it refresh
        setTimeout(function () {
            $('.slider-remove-prev').find('.slick-arrow.slick-disabled').removeAttr('style');
        }, 300);
        //accordionTabPanel thumb height refresh
        setTimeout(function () {
            thumbHeightResize();
        }, 330);
        reSizePlanRate();
        processModalSameHeight();
        
    }, 200);

});

//start offset slick track*
// use this function to offset the first anlast slides of a slider to a certain negative and positive margin left
function offsetSlickTrack($this) {
    if ($(window).width() < 768) {
        let slickTrackOffset = parseInt($this.attr('data-slick-track-offset'));
        let slickTrack = $this.find('.slick-track');
        if (slickTrackOffset) {
            slickTrack.css('margin-left', '-' + slickTrackOffset + 'px');
            $this.on('beforeChange', function (event, slick, currentSlide, nextSlide) {
                var slickSlideCount = $this.find('.slick-slide').length;

                if (nextSlide > 0 && nextSlide < (slickSlideCount - 1)) {
                    $this.find('.slick-track').css("margin-left", "0");
                } else if (nextSlide == (slickSlideCount - 1)) {
                    $this.find('.slick-track').css("margin-left", slickTrackOffset + "px");
                } else if (nextSlide == 0) {
                    $this.find('.slick-track').css("margin-left", "-" + slickTrackOffset + "px");
                }
            });
        }
    }
}
$('.offset-slick-track-js').on('init', function () {
    offsetSlickTrack($(this));
});
//End offset slick track

//Goes back to top when page reloads
$(window).on('unload', function() {
    $(window).scrollTop(0);
});


$(window).on('load', function () {
    //recall same-height function when load is finish
    processSameHeightElements();

    //Accordion Tab 
    accordionTab();
    reSizePlanRate();
});

function reSizePlanRate() {
    var el = $('.plan-rate-container-js');
    el.each(function (element) {
        var items = $(this).find('.plan-item');
        if (window.matchMedia("(max-width: 767px)").matches) {
            items.css('width', '100%');
        } else {
            if (items.length === 4) {
                items.css('width', '25%');
            } else {
                items.css('width', '33.33%');
            }
        }
    });
}

$('.transform-slick-slide-js').on('beforeChange', function (event, slick, currentSlide, nextSlide) {
    var slickSlideCount = $(this).find('.slick-slide').length;
    var $slider = $(this);
    $slider.find('.slick-track').removeClass('margin-n30-left').removeClass('margin-n15-left');
    if (!$slider.hasClass('full-width-slide')) {
        if (nextSlide == 0) {
            $slider.find('.slick-track').addClass('margin-n30-left');
        } else if (nextSlide == (slickSlideCount - 1)) {
            $slider.find('.slick-track').addClass('margin-n15-left');
        }
    }
})

//InfoBlock slider
$('.infoblock-slider').on('beforeChange', function (event, slick, currentSlide, nextSlide) {
    if (window.matchMedia('(max-width: 767px)').matches) {
        var $slider = $(this);
        var slickSlideCount = $slider.find('.slick-slide').length;

        if (nextSlide > 0 && nextSlide < (slickSlideCount - 1)) {
            $slider.find('.slick-track').css("margin-left", "0");
        } else if (nextSlide == (slickSlideCount - 1)) {
            $slider.find('.slick-track').css("margin-left", "15px");
        } else {
            $slider.find('.slick-track').css("margin-left", "");
        }
    }
});

$('.infoblock-slider, .responsive-slick-slider').on('lazyLoaded', function () {
    processSameHeightElements();
});

// Start slick event handler to hide offsreen card slides on infoblock sliders

$('.hiddenInactive').on('init', function () {
    $(this).find('.slick-slide[aria-hidden="true"]').addClass('offscreen');
}); 

function hideInactiveSlides() {
    $('.hiddenInactive').on('afterChange', function () {
        $(this).find('.slick-slide[aria-hidden="true"]').addClass('offscreen');
        $(this).removeClass('sliding');
    });
    $('.hiddenInactive').on('beforeChange', function () {
        $(this).find('.slick-slide[aria-hidden="true"]').removeClass('offscreen');
        $(this).addClass('sliding');
    });
    let mousedown = false;
    $('.hiddenInactive').on('mousedown', function () {
        mousedown = true;
    });
    $('.hiddenInactive').on('mouseup', function () {
        mousedown = false;
    });
    $('.hiddenInactive').on('mousemove', function () {
        if (mousedown) {
            $(this).find('.slick-slide[aria-hidden="true"]').removeClass('offscreen');
        }
    });
}
// End slick event handler to hide offsreen card slides on infoblock sliders

//Start disable tab on link slides to prioritize tab on link
$('.link-slide').on('afterChange', function () {
    let $this = $(this);
    setTimeout(function () {
        $this.find('.slick-slide').attr('tabindex', -1);
    }, 100);
});
$('.link-slide').on('init', function () {
    let $this = $(this);
    setTimeout(function () {
        $this.find('.slick-slide').attr('tabindex', -1);
    }, 100);
});
//End disable tab on link slides to prioritize tab on link

//Delegate slick click event to open tooltip modal
$('.link-slide').on('click', 'span[role="button"]', function (e) {
});
//delegate slick hover event to trigger show tooltip function
$('.link-slide').on('mouseenter mouseleave', '.tooltip-interactive, .tooltip-static', function (e) {
    $(this).tooltip('show');
});
//delegate slick focus event to trigger show tooltip function
$('.link-slide').on('focus', '.tooltip-interactive, .tooltip-static', function (e) {
    $(this).tooltip('show');
    setTimeout(function () {
        $(this).find('.tooltip').attr('tabindex', 0);
    }, 100);
});

// for tooltip circle trigger Mobility-Connected-Car-Landing-Plug-in-connectivity
$('.mobility-circle-icon.mobility-circle-icon').on('shown.bs.tooltip', function () {
    $('.mobility-circle-icon.mobility-circle-icon').removeClass('active');
    $(this).addClass('active');
})

$('.mobility-circle-icon.mobility-circle-icon').on('hidden.bs.tooltip', function () {
    $('.mobility-circle-icon.mobility-circle-icon').removeClass('active');
})

$('document').ready(function () {
    if ($(window).width() < 768) {
        resizeTimeoutFn = setTimeout(function () {
            $('.transform-slick-slide-js.slick-initialized:not(.full-width-slide)').find('.slick-track').addClass('margin-n30-left');
        }, 200);
        
    }
    
    forceSkipSameHeightMobile();
    $('.carousel-navigator-centered').each(function (e) {
        var $this = $(this);
        if ($this.children().length > 1) {
            if ($this.children().length < 3) $this.addClass('two-slides');
            $this.slick();
        }
    });
    
});

// Start HomePage Slider
//var ele = document.getElementById("slider-rotating-carousel-component");
let ele = document.querySelectorAll('.slider-rotating-carousel-component');

if (ele.length != 0) { 

    (function (bell, $) {

        $.widget("rsx.BannerSliderCarousel", {
            version: "0.5",
            widgetEventPrefix: "BannerSliderCarousel",
            links: [],
            options: {
                slidesContainer: '',
                pauseOnFocus: true,
                pauseOnHover: true,
                infinite: true,
                autoplay: true,
                autoplaySpeedMobile: 10000,
                autoplaySpeed: 6000, //default speed for all carousel slides
                dots: true,
                arrows: false,
                customPaging: function (slider, i) {
                    return '<button class="slider-rotating-carousel-button" type="button">' + (i + 1) + '</button>';
                },
                dotsClass: 'slider-rotating-carousel-buttons',
                initialSlide: 0,
                omnitureShow_s_oAPT: '347-0-0',
                omnitureClick_s_oAPT: '348-0-0',
                track_omniture: false,
                adobeTargetCssClass: "at-element-marker"
            },

            _create: function () {
                var self, that;
                self = that = this;
                var canonical = $('link[rel=\'canonical\']').attr("href").split('/').pop(); // added element
                var alternate = $('link[rel=\'alternate\']').attr("href").split('/').pop(); // added element

                if (window.location.pathname === '/' || window.location.pathname === '/Accueil') { self.options.track_omniture = true; }
                //in order to save the tab state on browser back button, it saves tab index in this hidden input

                var progressInterval,
                    percentComplete,
                    progressStep = 50,
                    progressIndicatorLength,
                    progressIndicatorUnit,
                    progressIndicatorTotal,
                    progressIndicatorTotalRounded,
                    overrideMouseOverOut = false;

                this.autoplaySpeed = that.options.autoplaySpeed;

                if (navigator.userAgent.match(/Android/i)
                    || navigator.userAgent.match(/webOS/i)
                    || navigator.userAgent.match(/iPhone/i)
                    || navigator.userAgent.match(/iPad/i)
                    || navigator.userAgent.match(/iPod/i)
                    || navigator.userAgent.match(/BlackBerry/i)
                    || navigator.userAgent.match(/Windows Phone/i)) {
                    this.autoplaySpeed = that.options.autoplaySpeedMobile;
                }

                var playButtonLabel = "Pause rotation of banners";
                var pauseButtonLabel = "Resume rotation of banners";

                if (!alternate && !canonical)
                    canonical = "Accueil";
                this.$window = $(window);
                this.$slidesContainer = this.element.find(self.options.slidesContainer);
                this.$slides = this.$slidesContainer.children();
                this.$pauseButton = this.element.find(".slider-rotating-carousel-pause");

                var playButtonLabel = this.$pauseButton.data("play-label");
                var pauseButtonLabel = this.$pauseButton.data("pause-label");
                // this.$accessibilityLabel = this.$pauseButton.find(".sr-only");
                this.$progressIndicator = this.$pauseButton.find(".slider-rotating-carousel-progress > circle");

                progressIndicatorLength = typeof SVGElement.prototype.getTotalLength !== "undefined" ? Math.round(this.$progressIndicator.get(0).getTotalLength()) : 125;
                progressIndicatorUnit = progressIndicatorLength / 100;

                this.$progressIndicator.css({ "stroke-dasharray": progressIndicatorLength });

                this.pausedThroughButton = false;
                this.rotate = false;
                this.hasFocus = false;
                this.hasHover = false;

                resumeRotation();

                // this.$accessibilityLabel.text(playButtonLabel);

                function pauseRotation() {
                    if (self.rotate) {
                        if (Math.abs(progressIndicatorTotalRounded) < 1) {
                            self.$progressIndicator.addClass("slider-rotating-carousel-progress_initial");
                        }
                        self.$pauseButton.attr('aria-label', playButtonLabel);
                        self.rotate = false;
                    }
                }

                function resumeRotation() {
                    if (!self.rotate) {
                        // if user manually paused the slideshow through the button, do not restart it automatically
                        if (!self.pausedThroughButton && !self.hasFocus && !self.hasHover) {
                            self.$progressIndicator.removeClass("slider-rotating-carousel-progress_initial");
                            self.$pauseButton.attr('aria-label', pauseButtonLabel);
                            self.rotate = true;
                        }
                    }
                }

                this.$pauseButton.on("click tap", function () {
                    
                    var isPaused = self.$pauseButton.attr("data-pressed") === "true";

                    if (typeof s_oTrackPage === "function") { // && isHomePage === "True"
                        s_oTrackPage({ s_oAPT: "647-0-0", s_oBTN: self.$pauseButton.attr('aria-label') });
                    }

                    if (isPaused) {
                        self.pausedThroughButton = false;
                        resumeRotation();
                        self.$pauseButton.attr("data-pressed", false);
                    } else {
                        self.pausedThroughButton = true;
                        pauseRotation();
                        self.$pauseButton.attr("data-pressed", true);
                    }
                });

                this.$slidesContainer.on("mouseenter", function () {

                    if (!!('ontouchstart' in window) === false) {

                        if (!overrideMouseOverOut) {
                            self.hasHover = true;
                            pauseRotation();
                        }
                    }
                }).on("mouseleave", function () {

                    if (!overrideMouseOverOut) {
                        self.hasHover = false;
                        resumeRotation();
                    }
                });

                $(document).on("visibilitychange", function () {
                    if (document.visibilityState === "hidden") {
                        pauseRotation();
                    } else {
                        resumeRotation();
                    }
                });

                var startAutoplay = function () {

                    percentComplete = 0;
                    progressIndicatorTotal = 0;
                    updateProgressIndicator();
                    progressInterval = setInterval(progressIntervalHandler, progressStep);
                };

                var updateProgressIndicator = function () {
                    percentComplete += progressStep / that.autoplaySpeed * 100;
                    progressIndicatorTotal = percentComplete * progressIndicatorUnit * -1 + 1;
                    progressIndicatorTotalRounded = Math.round(progressIndicatorTotal * 10) / 10;
                    self.$progressIndicator.css({ "stroke-dashoffset": progressIndicatorTotalRounded });
                };

                var progressIntervalHandler = function () {

                    if (self.rotate && !self.pausedThroughButton && self.$pauseButton.attr("data-pressed") === "false") {

                        updateProgressIndicator();

                        if (percentComplete >= 100) {
                            self.$slidesContainer.slick('slickNext');
                            resetAutoplayProgress();
                        }
                    }
                };

                var resetAutoplayProgress = function () {
                    clearInterval(progressInterval);
                    startAutoplay();
                };
                this.$slidesContainer.focusin(function () {
                    if (!!('ontouchstart' in window) === false) {
                        if (!overrideMouseOverOut) {
                            that.hasFocus = true;
                            pauseRotation();
                            
                        }
                    }
                }).focusout(function () {
                    if (!overrideMouseOverOut) {
                        that.hasFocus = false;
                        resumeRotation();
                    }
                });

                this.$slider = this.$slidesContainer
                    .on("init", function (event, self) {
                        self.$slider.removeClass("slider-rotating-carousel-height");
                        self.options.initialSlide = self.currentSlide;
                        if (that.options.autoplay) {
                            startAutoplay();
                        }
                        $(this).find('.slider-rotating-carousel-button')
                            .focusin(function () {
                                if (!overrideMouseOverOut) {
                                    pauseRotation();

                                }
                            }).focusout(function () {
                                if (!overrideMouseOverOut) {
                                    resumeRotation();
                                }
                            }).on("keyup", function (e) {

                                if (e.type === "keyup" && (e.which === 37 || e.which === 39)) {
                                    overrideMouseOverOut = true;
                                    $(this).closest('.slider-rotating-carousel-buttons').find('.slick-active .slider-rotating-carousel-button').focus();
                                    progressIndicatorTotalRounded = 0;
                                    pauseRotation();
                                }
                            });

                        $("#slider-rotating-carousel-component .slider-rotating-carousel-button")
                            .on("click tap", function () {
                                if (typeof s_oTrackPage === "function") { // && isHomePage === "True"
                                    s_oTrackPage({ s_oAPT: "647-0-0", s_oBTN: $(this).attr("aria-label") });
                                }
                            });
                        that._syncAdobeTarget(self);
                        $(this).find('.slick-slide.slick-cloned').removeAttr('id');
                    }).slick({
                        pauseOnFocus: self.options.pauseOnFocus,
                        pauseOnHover: self.options.pauseOnHover,
                        slidesToShow: 1,
                        slidesToScroll: 1,
                        infinite: self.options.infinite,
                        adaptiveHeight: self.options.adaptiveHeight,
                        arrows: false,
                        useTransform: false,
                        autoplay: false, //relies on custom implementation
                        waitForAnimate: false,
                        dots: self.options.dots,
                        customPaging: self.options.customPaging,
                        dotsClass: self.options.dotsClass
                    }).on("afterChange", function (event, slick, currentSlide, nextSlide) {
                        that._track(currentSlide);
                        slick.$slider.find('.slick-slide.slick-cloned').removeAttr('id');
                    }).on("beforeChange", function (event, slick, currentSlide, nextSlide) {
                        resetAutoplayProgress();
                    });
                that._syncAdobeTarget(self);
                var CarouselImpressions = setInterval(function () {
                    if (typeof s_oTrackPage === "function" || typeof s_track === "function") {
                        that._track(self.options.initialSlide);
                        clearInterval(CarouselImpressions);
                    }
                }, 100);
            },

            _trackOmniture: function (code, id) {
                if (typeof s_oTrackPage === "function") {
                    s_oTrackPage({ s_oAPT: code, s_oBID: id });
                } else if (typeof s_track === "function") {
                    s_track({ s_oAPT: code, s_oBID: id });
                }
            },

            _track: function (currentSlide) {
                if (this.options.track_omniture && this.links[currentSlide] === undefined) {
                    var banner = $(this.$slides[currentSlide]).find(".js-omni-banner");
                    var omnitureVal = $(banner).data("omni-s_obid");
                    this._trackOmniture(this.options.omnitureShow_s_oAPT, omnitureVal);
                    this.links[currentSlide] = true;
                }
            },
            /**
             * synchronize banner with Adobe to prevent flickering issue or banner copy not complete
             *
             * @param {any} self the item to pass
             */
            _syncAdobeTarget: function (self) {
                if (this.options.infinite) {
                    let leftClonedSlide = self.$slider.find(".slick-slide.slick-cloned").first();
                    let rightClonedSlide = self.$slider.find(".slick-slide.slick-cloned").last();
                    let allSlides = self.$slider.find(".slick-slide");
                    var firstSlide, lastSlide = "";
                    /*mapping first slide and last slide with their cloned slides accordingly*/
                    $.each(allSlides, function (index, $slide) {
                        if ($(this).data("slickIndex") === leftClonedSlide.data("slickIndex") + 1) {
                            firstSlide = $(this);
                        }
                        if ($(this).data("slickIndex") === rightClonedSlide.data("slickIndex") - 1) {
                            lastSlide = $(this);
                        }
                    });
                    /*find adobe target slider and replace by order*/
                    if (lastSlide && lastSlide.find("div").hasClass(this.options.adobeTargetCssClass)) {
                        leftClonedSlide.html(lastSlide.html());
                    }
                    if (firstSlide && firstSlide.find("div").hasClass(this.options.adobeTargetCssClass)) {
                        rightClonedSlide.html(firstSlide.html());
                    }
                    if (leftClonedSlide && leftClonedSlide.find("div").hasClass(this.options.adobeTargetCssClass)) {
                        lastSlide.html(leftClonedSlide.html());
                    }
                    if (rightClonedSlide && rightClonedSlide.find("div").hasClass(this.options.adobeTargetCssClass)) {
                        firstSlide.html(rightClonedSlide.html());
                    }
                }
            }

        });
    })({}, jQuery);

    //$('#slider-rotating-carousel-component').BannerSliderCarousel({
    //    'slidesContainer': ".slider-rotating-carousel",
    //    'autoplay-speed': "6000",
    //    'autoplay-speed-mobile': "6000",
    //    'class': "init"
    //});
    $('.slider-rotating-carousel-component').each(function () {
        $(this).BannerSliderCarousel({
            'slidesContainer': ".slider-rotating-carousel",
            'autoplay-speed': "6000",
            'autoplay-speed-mobile': "6000",
            'class': "init"
        });
    });

}

// End HomePage Slider

// Accessibility fix for banner slider. Add aria-labeledby on each slide

$('.slider-rotating-carousel').each(function () {
    $(this).find('.slick-slide[role="tabpanel"]').each(function (index) {
        $(this).attr('aria-labelledby', 'slick-slide-control0' + index);
    });
});

// Start same-height trigger on modal
$('.same-height-modal-js').on('shown.bs.modal', function () {
    $(this).find('.same-height').each(function () {
        processSameHeightElements($(this));
    });
});


//Start Radio Button with border
$('.graphical_ctrl input').change(function () {
    var $this = $(this);
    if ($this.attr('type') == "radio") {
        //Remove border to radio button
        $this.parent().closest('[aria-labelledby]').find('.radio-container').removeClass('checked-border');

        if ($this.prop('checked')) {
            //Add border to radio button 
            $this.closest('.radio-container').addClass('checked-border');
        }
    }
});

$('.graphical_ctrl input').focus(function () {
    //Add focus class in radio button container
    $(this).closest('.radio-container').addClass('focused-element');
});
$('.graphical_ctrl input').blur(function () {
    //Remove focus class in radio button container
    $(this).closest('.radio-container').removeClass('focused-element');
});
$('.radio-container').click(function (e) {
    var $this = $(this);
    $this.find('.graphical_ctrl input').prop('checked', true);
    $this.find('.graphical_ctrl input').trigger('change');
    $this.find('.graphical_ctrl input').focus();
});

//Tab anchor same-height function
$('.same-height-tab-js').on("click", function () {
    $(".same-height-tabpanel-js").find('.same-height').each(function () {
        processSameHeightElements($(this));
    });
});

$(document).ready(function () {
    $(window).on('load', function () {
        processSameHeightElements();
    });

    //checked if there are checked properties in DOM
    if ($('input[type="radio"][name="radioDevices"]:checked').length > 0) {
        let targetValue = $('input[type="radio"][name="radioDevices"]:checked').attr('aria-controls');
        //find targetValue and add block class to show the content
        $("section").find("#" + targetValue).addClass("block");
        //add checked border class
        $('.radio-container input[type="radio"][name="radioDevices"]:checked').closest('.radio-container').addClass('checked-border');
    }

    // Radio button click event
    $('.radio-container input[type="radio"][name="radioDevices"]').change(function () {
        // Get value of clicked radio button
        inputValue = $(this).attr('aria-controls');
        //checked radio btn if checked  
        $(this).prop('checked', true);
        // Get the DOM id aria-controls
        let targetContainer = $('div[id="' + inputValue + '"]');
        // Hide all other that has no id
        $('.display-radio-container').not(targetContainer).removeClass("block");
        // Show targeted element
        $(targetContainer).addClass("block");
        processSameHeightElements();
    });
});
//End Radio Button with border


//start script for youtube iframe api - used for slick video with youtube player on top -- see mobility connected car page
//not working using document.ready
var slickVideoContainers = document.querySelectorAll('[data-slick-video="main-container"]');
window.videoPlayers = [];
window.initializeVideoPlayers = function () {
    for (var i = 0; i < slickVideoContainers.length; i++) {
        slickVideo(slickVideoContainers[i], i);
    }
}
if (slickVideoContainers.length > 0) {
    youTubeIframeApi();
}
function youTubeIframeApi() {
    var tag = document.createElement('script');
    var firstScriptTag = document.getElementsByTagName('script')[0];
    tag.src = 'https://www.youtube.com/iframe_api';
    firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
    window.onYouTubeIframeAPIReady = function () {
        initializeVideoPlayers();
    }
}
function createVideoPlayer(defaultVideo, index, videoPlayerContainer) {   
    window.videoPlayers[index] = new YT.Player(videoPlayerContainer, {
        videoId: defaultVideo,
    });
}

//End script for youtube iframe api - used for slick video with youtube player on top

//start script for connected car slick video -- applied in mobility connected car page


    window.slickVideo = function (slickVideoContainer, index) {
        var defaultVideo = slickVideoContainer.querySelector('.default-video').dataset.videoId;
        var videoPlayerContainer = slickVideoContainer.querySelector('[data-slick-video="video-player-container"]');
        createVideoPlayer(defaultVideo, index, videoPlayerContainer)
        //pause video when navigating through video items
        $(slickVideoContainer).on('click', '.play-btn-container', function (e) {
            $(this).closest('.video-switch').trigger('click');
        });

        //pause video when navigating through video items
        $(slickVideoContainer).on('focus', '.play-btn-container', function (e) {
            window.videoPlayers[index].pauseVideo();
        });

        //respond to space and enter keys on the keyboard for accessibility
        $(slickVideoContainer).on('keypress', '.play-btn-container', function (e) {
            e.preventDefault();
            let key = e.keyCode;
            if (key == 32 || key == 13) {
                $(this).closest('.video-switch').trigger('click');
            }
        })
        // on video item click event
        $(slickVideoContainer).on('click', '.video-switch', function () {

            //variables
            let youtube = 'https://www.youtube.com/embed/';
            let playerSettings = '?rel=0&modestbranding=1&showinfo=0&enablejsapi=1';
            let videoId = $(this).data('video-id');
            let videoPlayer = slickVideoContainer.querySelector('[data-slick-video="video-player-container"]');
            let image = $(this).find('img');
            //check video status. if playing then stop, if not then play
            if (image.hasClass('playing')) {
                window.videoPlayers[index].pauseVideo();
                image.removeClass('playing');
            }
            else {
                // set youtube source
                videoPlayer.src = youtube + videoId + playerSettings;
                videoPlayer.addEventListener('load', function () {
                    window.videoPlayers[index].playVideo();
                });
                // mark item as active
                $('.car-video-slider img').removeClass('active playing');
                $(this).find('img').addClass('active playing');
                $('.video-title').removeClass('surtitle-black');
                $(this).find('.video-title').addClass('surtitle-black');
            }
        });
    }

//end script for connected car slick video

//START selectDestination Funtion
function destinationAddOnTabsEventListener() {
    if ($('#selectDestination').length > 0) {
        $('#selectDestination').on('change', function () {
            var $el = $(this),
                siblingEl = $el.closest('.destination-selection').next();

            siblingEl.children().hide();
            siblingEl.find('#' + $el.val()).show();
            processSameHeightElements();
        });
    }

}
destinationAddOnTabsEventListener();
//END selectDestination Funtion

function initializeScrollableTable() {
    $('.scrollableContainer-js').on('scroll', function () {
        var $this = $(this);
        var scrollPos = $this.scrollLeft();
        var width = $this.width();
        var scrollWidth = $this.get(0).scrollWidth;
        var container = $this.closest('[class^=scrollableContainerShadow]');
        if (scrollWidth > $this.get(0).clientWidth + 1) {
            if (scrollPos === 0) {
                container.removeClass('left');
            } else {
                container.addClass('left');
            }
            if (scrollPos + width === scrollWidth) {
                container.removeClass('right');
            } else {
                container.addClass('right');
            }
        }
        else {
            $this.closest('[class^=scrollableContainerShadow]').removeClass('left').removeClass('right');
        }
    });
    $(window).resize(function () {
        if (window.matchMedia('(max-width: 991px)').matches) {
            //$('[class^=scrollableContainerShadow]').addClass('right');
             $('.scrollableContainer-js').trigger('scroll');
        }
        else {
            $('.same-height-modal [class^=scrollableContainerShadow]').removeClass('left').removeClass('right');
        }
    });
}

initializeScrollableTable();

//More or Less Accordion Function in mobile view
var displayAccordionMoreAndLess = {
    init: function () {
        var self = this;
        self.addEventListener();
        self.addAriaHidden();
        self.onMobileResize();
    },
    addEventListener: function () {
        $('.expander-description-control').on('click', function (e) {
            var $this = $(this);

            if ($this.attr('aria-expanded') == 'false') {
                $this.prev().css('max-height', '10000px').attr('aria-hidden', 'false');
                $this.attr('aria-expanded', 'true');

            }
            else {
                $this.prev().removeAttr('style').attr('aria-hidden', 'true');
                $this.attr('aria-expanded', 'false');
            }
            e.stopImmediatePropagation();

        });
    },
    onMobileResize: function () {
        var self = this;
        $(window).resize(function () {
            self.addAriaHidden();
        });
    },
    addAriaHidden: function () {
        if (window.matchMedia('(max-width: 767px)').matches) {
            if ($('.expander-description[aria-hidden]').length <= 0) {
                $('.expander-description').attr('aria-hidden', 'true');
            }
        }
        else {
            $('.expander-description').removeAttr('style').removeAttr('aria-hidden');
        }
    }

};
if ($('.expander-description-control').length > 0) {
    displayAccordionMoreAndLess.init();
}

// Start script for modal video player
$(function () {
    $('.slick').on('click', '[data-toggle="video-modal"]', showVideo);
    $('[data-toggle="video-modal"]').on('click', showVideo);
    function showVideo(e) {
        e.preventDefault();
        const videoId = $(this).data('video-id');
        const youtube = 'https://www.youtube.com/embed/';
        const videoSettings = $(this).data('video-settings');
        const modal = $('#video-modal');
        modal.find('iframe').attr("src", youtube + videoId + videoSettings);
        $(modal).modal('show');
    }
    $('#video-modal').on('hidden.bs.modal', function (e) {
        $(this).find('iframe').attr('src', '');
    });
})
// End script for modal video player

//Window resize function for slick -- this is to make the slick slider responsive
//Window resize function for slick -- this is to make the slick slider responsive

$(function () {
    let isUnslick = function (screen, breakpoints) {
        let unslicked = false;
        breakpoints.every(function (breakpoint) {
            if (breakpoint.breakpoint == screen && breakpoint.settings == 'unslick') {
                unslicked = true
                return false;
            }
            else {
                return true;
            }
        });
        return unslicked;
    };
    $(window).resize(function () {
        let responsiveSlickSlider = $('.responsive-slick-slider').not('.slick-initialized');
        let screen = $(window).width() - 1;

        responsiveSlickSlider.each(function () {
            let slick = $(this);
            let breakpoints = $(this).data('slick').responsive;
            if (screen >= 991) {
                if (!isUnslick(991, breakpoints)) {
                    slick.slick();
                }
            }
            if (screen >= 767 && screen < 991) {
                if (!isUnslick(767, breakpoints)) {
                    slick.slick();
                }
            }
            if (screen >= 300 && screen < 767) {
                if (!isUnslick(300, breakpoints)) {
                    slick.slick();
                }
            }
        })
    });
});

// End Window resize function for slick


$(function () {
    // This is to automatically update expand / collapse icon on single expand accordion. Upon clicking other accordion item, this will update the currently active accordion's icon
    $(document).on('hide.bs.collapse', '.single-expand', function (e) {
        $(this).closest('.accordion-wrap').find('.icon').removeClass('icon-small_icon_collapse').addClass('icon-small_icon_expand');
    });
    $(document).on('hide.bs.collapse', '[data-expand-type="single"]', function (e) {
        // for all screen sizes
        let expandIcon = $(this).closest('.accordion-wrap').find('.collapse-trigger').data('icon-expand');
        let collapseIcon = $(this).closest('.accordion-wrap').find('.collapse-trigger').data('icon-collapse');
        $(this).closest('.accordion-wrap').find('.icon:not([data-icon="xs"])').first().removeClass(collapseIcon).addClass(expandIcon);
        //for custom mobile icon
        let expandIconXs = $(this).closest('.accordion-wrap').find('.collapse-trigger').data('icon-expand-xs');
        let collapseIconXs = $(this).closest('.accordion-wrap').find('.collapse-trigger').data('icon-collapse-xs');
        if (expandIconXs && collapseIconXs) {
            $(this).closest('.accordion-wrap').find('[data-icon="xs"]').addClass(expandIconXs).removeClass(collapseIconXs);
        }
    });
    let windowSize = $(window).width();
    $(window).resize(function () {
        windowSize = $(window).width();
    });
    // Accordion custom mobile icon
    $('.collapse-trigger').on('click', function (e) {
        if (windowSize < 760) {
            let accordion = $(this);
            let expandIconXs = $(this).data('icon-expand-xs');
            let collapseIconXs = $(this).data('icon-collapse-xs');
            if (expandIconXs && collapseIconXs) {
                if (accordion.attr('aria-expanded') !== "true") {
                    accordion.closest('.accordion-wrap').find('.icon[data-icon="xs"]').addClass(collapseIconXs).removeClass(expandIconXs);
                }
                else {
                    accordion.closest('.accordion-wrap').find('.icon[data-icon="xs"]').removeClass(collapseIconXs).addClass(expandIconXs);
                }
            }
        }
    });
});

//refresh slick slider inside accordion upon expand
$(document).on('show.bs.collapse', '.collapse-accordion-accessible-toggle', function (e) {
    $(this).find('.responsive-slick-slider.slick-initialized').slick('refresh');
});


// Switch between dynamic title of accordion
$(document).on('show.bs.collapse hide.bs.collapse', '[data-title-type="dynamic"]', function (e) {
    var title = $(this).closest('.accordion-wrap').find('.accordion-title');
    var toggleColor = title.closest('.toggle-title-color');

    var expandTitle = title.data('expand-title');
    var collapseTitle = title.data('collapse-title');
    if (e.type === 'hide') {
        title.html(expandTitle);
        if(toggleColor.length){
            title.toggleClass('txtWhite');
            toggleColor.toggleClass('margin-neg-t-50 margin-neg-55-t-xs');
        }
    }
    else if (e.type === 'show') {
        title.html(collapseTitle);
        if(toggleColor.length){
            title.toggleClass('txtWhite');
            toggleColor.toggleClass('margin-neg-t-50 margin-neg-55-t-xs');
        }
    }
});
$('.clickable-card').on('click', '[data-toggle="modal"]', function (e) {
    e.preventDefault();
});
$('.clickable-card').on('click', '.tooltip-interactive', function (e) {
    e.preventDefault();
});
$('.clickable-card').on('click', '.tooltip-static', function (e) {
    e.preventDefault();
});

// Tooltip modal keypress on mobile view
$(function () {
    var keypressTooltipModal = $('.tooltip-interactive[data-toggle="tooltip"]').siblings($('[data-toggle="modal"]'));
    var keypressTooltipModalStatic = $('.tooltip-static[data-toggle="tooltip"]').siblings($('[data-toggle="modal"]'));
    keypressTooltipModal.on('keypress', function (e) {
        if (e.keyCode == 13) {
            $(this).trigger('click');
        }
    });
    keypressTooltipModalStatic.on('keypress', function (e) {
        if (e.keyCode == 13) {
            $(this).trigger('click');
        }
    });
});

// Side Nav Start
var FloatingSubNav = {
    isMobile: false,
    activeSubNavIndex: 1,
    magicLine: '#magic-line',
    init: function (windowScroll) {

        this.addMagicLine('.subnavgroup');

        // Check if will desktop or not then if desktop apply the floating scroll function
        if (FloatingSubNav.checkIfNotMobile() === false) {
            FloatingSubNav.checkScrollPos(windowScroll);
            $('.subnav-scroll').removeClass('d-none');
            FloatingSubNav.checkScrollableActiveSection(windowScroll);
        } else {
            $('.subnav-scroll').removeClass('d-none');
            FloatingSubNav.positionToRelative('.subnav-scroll');
        }

        FloatingSubNav.bindEvents();

    },

    bindEvents: function () {
        var that = this;

        $('.subnavgroup li a').on('click', function () {
            var sectionToScroll = '#' + $(this).parent().attr('data-section');
            if (typeof scrollOmnitureBegin === 'function') {
                scrollOmnitureBegin();
            }
            that.animateToSection(sectionToScroll);
        });
    },

    addMagicLine: function (element) {
        $(element).append("<li class='listStyleNone' id='magic-line'></li>");

    },

    checkScrollPos: function (scrollPos) {
        scrollPos += 200;
        // Define variables
        var spaceBefore = 135; // space before scrollable area
      
        //var spaceAfter = 50; // space after scrollable area
        var scrollableArea = '.scrollable-area';
        var elementToPositon = '.subnav-scroll';
       
        //var endPosElement = 'footer';
        var totalSpacesBeforeScrollableArea = this.getScrollableAreaTopValue(scrollableArea, spaceBefore);
        //console.log(totalSpacesBeforeScrollableArea);

        // pass scrollable area element instead of always looking for the footer to support cases wherein there's content between the scrollable area and the footer
        // we don't need the hardcoded spaceAfter value anymore
        if (scrollPos >= totalSpacesBeforeScrollableArea && (scrollPos - 200) < this.getScrollableAreaEndValue(elementToPositon, scrollableArea, spaceBefore)) {
            this.positionToFix(elementToPositon, spaceBefore, $('.subnav-scroll').width());
        } else if (scrollPos >= this.getScrollableAreaEndValue(elementToPositon, scrollableArea, spaceBefore)) {
            this.positionToAbsolute(elementToPositon);
        } else {
            this.positionToRelative(elementToPositon);
        }

    },

    checkScrollableActiveSection: function (scrollPos) {
        var that = this;
        var scrollableContents = '.scrollable-contents';
        var subNav = '.subnav-scroll';
        var scrollableContentsLength = $(scrollableContents).find('> div').length;
        var currentIndex = 1;
        scrollPos = Math.ceil(scrollPos) + 100;

        //if (scrollPos === $(window).height()) {
        if ($(window).scrollTop() + $(window).height() === $(document).height()) {
            currentIndex = scrollableContentsLength;
        } else {
            for (var i = 1; i < scrollableContentsLength + 1; i++) {
                if (i === 1) {
                    if (scrollPos < FloatingSubNav.getSectionTopValue(scrollableContents, 1)) {
                        currentIndex = 1;
                        break;
                    }
                } else if (scrollPos >= FloatingSubNav.getSectionTopValue(scrollableContents, i - 1) && scrollPos < FloatingSubNav.getSectionTopValue(scrollableContents, i)) {
                    currentIndex = i;
                    break;
                } else if (scrollPos >= FloatingSubNav.getSectionTopValue(scrollableContents, scrollableContentsLength)) {
                    currentIndex = scrollableContentsLength;
                    break;
                }
            }
        }
        this.activeSubNavIndex = currentIndex;
        this.checkActiveSubNav(this.activeSubNavIndex, subNav);
    },

    getSectionTopValue: function (scrollableSection, ctr) {
        /*var addedViewSpace = 150;
        if ($(scrollableSection).length > 0) {
            return $(scrollableSection).find("#scrollable-content-" + ctr).offset().top + addedViewSpace;
        }*/

        var scrollables = $(scrollableSection), scrollable;
        if (scrollables.length > 0) {
            scrollable = scrollables.find("#scrollable-content-" + ctr);
            return Math.floor(scrollable.offset().top + scrollable.height());
        }
    },

    checkActiveSubNav: function (activeIndex, subNav) {
        $(subNav).find('.subnavgroup li').removeClass('subnav_active');
        $(subNav).find('.subnavgroup li:nth-child(' + activeIndex + ')').addClass('subnav_active');
        if (typeof scrollOmniture === 'function') {
            scrollOmniture($(subNav).find('.subnavgroup li:nth-child(' + activeIndex + ')'), activeIndex);
        }
        this.animateMagicLine(activeIndex, subNav);
    },

    animateMagicLine: function (activeIndex, subNav) {
        var that = this;
        if ($(subNav).length > 0) {
            var target = $(subNav).find('.subnavgroup li:nth-child(' + activeIndex + ') a');
            var topPos = target.position().top;
            var newHeight = target.parent().height();
        }

        $(that.magicLine).stop().animate({
            top: topPos,
            height: newHeight
        });
    },

    animateToSection: function (sectionToScroll) {
        var topPos = $(sectionToScroll).offset().top;

        $('html, body').animate(
            {
                scrollTop: topPos - 100
            },
            500,
            'linear',
            this.animateCallback
        );
    },

    animateCallback: function () {
        if (typeof scrollOmnitureEnd === 'function') {
            scrollOmnitureEnd();
        }
    },

    checkIfNotMobile: function () {

        var windowWidth = $(window).outerWidth();

        windowWidth > 767 ? isMobile = false : isMobile = true;

        return isMobile;
    },

    getScrollableAreaTopValue: function (scrollableArea, spaceBefore) {
        if ($(scrollableArea).length > 0) {
            return $(scrollableArea).position().top + spaceBefore;
        }

    },

    // ScrollableAreaEl compute height
    getScrollableAreaEndValue: function (subNavHeight, scrollableArea, spaceBefore) {
        var scrollableAreaEl = $(scrollableArea);

        return (scrollableAreaEl.outerHeight() + scrollableAreaEl.position().top) - ($(subNavHeight).innerHeight() + spaceBefore);
    },

    positionToFix: function (element, spaceBefore, width) {
        $(element).css({
            'position': 'fixed',
            'top': spaceBefore,
            'bottom': 'auto',
            'max-width': width + 'px'
        });
    },

    positionToRelative: function (element) {
        $(element).css({
            'position': 'relative',
            'top': 'auto',
            'bottom': 'auto',
            'max-width': ''
        });
    },

    positionToAbsolute: function (element) {
        $(element).css({
            'position': 'absolute',
            'top': 'auto',
            'bottom': 0
        });
    }
};


$(window).on('scroll', function () {
    checkScrollLocation();
    //hiding social floats on footer
}).on('resize', function () {
    var subNavScrollEl = $('.subnav-scroll');

    // need to recalculate on resize especially for magic line
    checkScrollLocation();
    // match subnavscroll width with its parent using js since position:fixed ignores the parent width
    subNavScrollEl.width(subNavScrollEl.parent().width());
});

// all function on document ready

$(function () {
    // FocusUntrapper();

    // call scrollable-area FloatingSubNav
    var windowScroll = document.documentElement.scrollTop;

    if ($('.scrollable-area').length > 0) {
        FloatingSubNav.init(windowScroll);
    }
    //FocusUntrapper();

    // Page loader trigger
    $("#show-loader-contained-mask").click(function () {
        // start showing the loader
        showLoaderWithMask('#brf-loader-contained-mask');
    });

    clickDragScroll_table();
   
    loadOpacityDots();

    //checkbox filter
    checkboxFilter();

    //option filter
    radioFilter.initialize();

    //mobile unslick count one, paramater limits the card shown in slick
    mobileUnslickCountOne.initialize(5);

    //Redirects to selected brand
    viewBrandItems();

    //hide inactive slick slide items
    hideInactiveSlides();

    //star rating
    starRating();

    //price range slider
    priceRangeSlider();

    //product slick with thumbnails
    initProductThumbnailModalSlick();

    //This is for click and enter event for color pallete selectors
    switchDeviceColor();

    verticalNav();

    //set province label for simplified header
    setProvinceLabel();

    //return focus on modal trigger upon closing modal
    modalTriggerReturnFocus();
});

//reset the scrollable container location
function checkScrollLocation() {
    if ($('.scrollable-area').length > 0) {
        var windowScroll = window.pageYOffset || document.documentElement.scrollTop;

        // Check if will desktop or not then if desktop apply the floating scroll function
        if (FloatingSubNav.checkIfNotMobile() === false) {
            FloatingSubNav.checkScrollPos(windowScroll);
            FloatingSubNav.checkScrollableActiveSection(windowScroll);
        } else {
            FloatingSubNav.positionToRelative('.subnav-scroll');
        }
    }
}

// Side Nav end

// Click and Drag Scroll Funtions
function clickDragScroll_table() {
    const slider = document.querySelectorAll('.clickdragscroll');
    const sliderCount = slider.length;

    if (sliderCount > 0) {
        let isDown = false;
        let startX;
        let scrollLeft;

        // loop all .clickdragscroll classname
        for (let x = 0; x < sliderCount; x++) {
            let s = slider[x];
            s.addEventListener('mousedown', function (e) {
                isDown = true;
                s.classList.add('active');
                startX = e.pageX - s.offsetLeft;
                scrollLeft = s.scrollLeft;
            });
            s.addEventListener('mouseleave', function () {
                isDown = false;
                s.classList.remove('active');
            });
            s.addEventListener('mouseup', function () {
                isDown = false;
                s.classList.remove('active');
            });
            s.addEventListener('mousemove', function (e) {
                if (!isDown) return;
                e.preventDefault();
                const x = e.pageX - s.offsetLeft;
                const walk = (x - startX) * 3; //scroll-fast
                s.scrollLeft = scrollLeft - walk;
            });
        }
    }
}

//reusable checkbox filter combonent
function checkboxFilter() {
    // on checkbox status change
    $('.checkbox-filter').on('change', function () {
        let $this = $(this);
        let checkboxFilterInstance = $this.closest('.js-checkbox-filter-wrap');
        let partialCards = checkboxFilterInstance.data('partial');
        let cardsToShow = {};
        //add or remove .checked class on its label
        if ($this.prop('checked')) {
            $('label[for=' + $this.prop('id') + ']').addClass('checked');
        }
        else {
            $('label[for=' + $this.prop('id') + ']').removeClass('checked');
        }
        let checkedFilter = $('.checkbox-filter:checked');
        //show all cards if no checkbox is checked.
        //Hide all cards if one or more checkbox is selected then show only matched categories in the next lines of code
        if (checkedFilter.length == 0) {
            showAllCards(checkboxFilterInstance)
        } else {
            hideAllCards();
            let cards = checkboxFilterInstance.find('.card-content');
            //iterate through all cards
            cards.each(function (index) {
                let card = $(this);
                //convert data-category value to array separated by space
                let categories = card.data('category').split(" ");
                //iterate through categories on each card
                checkedFilter.each(function () {
                    let checkBoxValue = $(this).val();
                    if (categories.indexOf(checkBoxValue) > -1) {
                        if (!cardsToShow[index]) {
                            cardsToShow[index] = card;
                        }
                    }
                });
            });
            let showedCards;
            Object.keys(cardsToShow).forEach(function (key) {
                showedCards = checkboxFilterInstance.find('.card-content').not('.d-none');
                if (partialCards) {
                    if (showedCards.length < partialCards) {
                        cardsToShow[key].removeClass('d-none');
                    }
                }
                else {
                    cardsToShow[key].removeClass('d-none');
                }
            });
            let viewMoreWrap = checkboxFilterInstance.find('.js-view-more-wrap');
            //if cards is less than the defined partial cards then hide viewmore button and its wrapper otherwise show.
            Object.keys(cardsToShow).length <= partialCards ? viewMoreWrap.hide() : viewMoreWrap.show();
            //unbind view-more button event
            checkboxFilterInstance.find('.js-view-more').off('click');
            //set view-more button event to show all items in cardsToShow based on the filter
            checkboxFilterInstance.find('.js-view-more').on('click', function (e) {
                e.preventDefault();
                viewMore(cardsToShow);
                viewMoreWrap.hide();
            });
        }
    });
    //function to show all cards
    let showAllCards = function (checkboxFilter) {
        let partialCards = checkboxFilter.data('partial');
        let cards = checkboxFilter.find('.card-content');
        hideAllCards();
        //show cards but limit them to the defined number of partial cards
        cards.each(function (index) {
            let counter = index + 1;
            if (partialCards) {
                if (counter <= partialCards) {
                    $(this).removeClass('d-none');
                }
                let viewMoreWrap = checkboxFilter.find('.js-view-more-wrap');
                //if cards is less than the defined partial cards then hide viewmore button and its wrapper otherwise show.
                cards.length <= partialCards ? viewMoreWrap.hide() : viewMoreWrap.show();
                //unbind view-more button event
                checkboxFilter.find('.js-view-more').off('click');
                //set view-more button event to show all items in cardsToShow based on the filter
                checkboxFilter.find('.js-view-more').on('click', function (e) {
                    e.preventDefault();
                    let cardsToShow = [];
                    //store cards to cardsToShow for later access
                    cards.each(function () {
                        cardsToShow.push($(this));
                    });
                    viewMore(cardsToShow);
                    viewMoreWrap.hide();
                });
            }
            else {
                $(this).removeClass('d-none');
            }
        });
    }
    //function to hide all cards
    let hideAllCards = function () {
        $('.card-content').addClass('d-none');
    }
    //upon loading check for checkbox filter instances and show partial cards
    let checkboxFilters = $('.js-checkbox-filter-wrap');
    checkboxFilters.each(function () {
        showAllCards($(this));
    });
    //view more function
    let viewMore = function (cardsToShow) {
        Object.keys(cardsToShow).forEach(function (key) {
            cardsToShow[key].removeClass('d-none');
        });
    }
}

function viewBrandItems() {
    $(document).on("click", ".view-all-js", function (e) {
        $($(this).attr("data-id")).click();
    });
}

//slick tiles viewAll function
function tilesViewBrandItems() {
  
    setTimeout(function () {
        $('a.txtUnderline.clickable-card.view-all-js').each(function () {
            $(this).on('click', function () {
                $($(this).attr("data-id")).click();
            });
        });
    }, 1000);
    
}

let radioFilter={
    initialize : function(){
        let that = this;
        $('.radio-slick').each(function(){
            let clone = $(this).clone();
            $(this).siblings('.slick-clone').html(clone.html());
            processSameHeightElements();
            $('.slick-clone').hide();
            $('.slick-clone .card-container').addClass(['col-md-4', 'col-sm-6', 'col-xs-12','margin-b-30','pad-h-15','pad-h-xs-0']);
            $('.slick-clone').find('.view-all-js').hide();
        });

        //This is to initially select all-brands radio after from back button
        $('input#All-brands').prop('checked', true);

        $(".slick-clone .tooltip-static").tooltip();
        //This added the id a prefix to cloned items
        this.modifyID();

        $('input.radio-filter').on('change', function () {

            tilesViewBrandItems();
            
            var $this = $(this);
            //add or remove .checked class on its label
            if ($this.is(':checked')) {
                // remove checked class for other labels
                $('label[for!=' + $this.prop('id') + ']').removeClass('checked');
                // add checked class for the selected value
                $('label[for=' + $this.prop('id') + ']').addClass('checked');
            }

            // hides all section
            $(".card-filter .card-section-js").hide();
            $("#bottom-banner-smartphones").hide();

            if($this.attr('data-filters') == 'all-items'){

                that.allBrands($("#"+$this.attr('data-filters')));

                // this shows view all link
                $(".view-all-wrapper").show();

                // this show bottom banner 
                $("#bottom-banner-smartphones").show();

            }else{
                // this hides view all link
                $(".view-all-wrapper").hide();

                // show selected section
                $("#"+$this.attr('data-filters')).show();

                //this shows cloned cards
                that.filter($("#" + $this.attr('data-filters')));
                lazyLoad($("#" + $this.attr('data-filters')));
            }
            mobileUnslickCountOne.reSize();
            
            // this scrolls to top when VIEW ALL text link was clicked
            let filterControls = $("#filter-controls");
            let headerNav = $("header:nth-child(1)");
            let innerHeaderHeight = headerNav.innerHeight();

            if($(window).width() <= 768) {
                $("html, body").animate({ 
                    scrollTop: $(filterControls).offset().top - innerHeaderHeight - 20
                }, "1000");
            } else {
                $("html, body").animate({ 
                    scrollTop: $(filterControls).offset().top - innerHeaderHeight / 1.4
                }, "1000");
            }
            
        });
    },
    filter:function(section){
        section.find(".slick-clone").show();
        section.find(".radio-slick").hide();
    },
    modifyID : function(){
        $('.slick-clone').each(function(){
            $(this).find('.card-container').each(function(){
                let title=$(this).find('h3');
                let currentId=title.attr('id');
                title.attr('id','cloned-'+currentId);

                let cardLink=$(this).find('.card-link-js');
                let currentCardLinkLabel=cardLink.attr('aria-labelledby');
                cardLink.attr('aria-labelledby','cloned-'+currentCardLinkLabel);    
            });
        });
    },
    allBrands : function(section){
        $(".card-filter .card-section-js").show();
        section.find(".slick-clone").hide();
        section.find(".radio-slick").show();
    }
}

let mobileUnslickCountOne = {
    slickContainer : $('.mobileUnslickCountOne'),
    initialize : function(maxCard) {   
        // for first load of the page
        this.slickContainer.on('init', function(event, slick){
            // slideCount = count of slick slides
            if(slick.slideCount == 1 && $(window).width() < 768){
                slick.destroy();
            }else{
                if(slick.slideCount > maxCard){
                    let ctr=1;
                    slick.slickFilter(function(){
                        if(ctr<=maxCard){
                            ctr++;
                           return true; 
                        }else{
                            if($(this).find('.view-all-js').length>0){
                                ctr++;
                                return true; 
                            }
                            ctr++;
                            return false;
                        }
                    });
                }
            }

            // for slider with same-height class 
            if ($(this).find('.same-height').length > 0) {
                $(this).find('.same-height').each(function () {
                    resetSameHeightElements($(this));
                    processSameHeightElements($(this));
                });
            }
        }).slick();
        $('.slider-remove-prev').find('.slick-arrow.slick-disabled').removeAttr('style');

    },
    // function for resize
    reSize: function () {
        let slickContainer = this.slickContainer;
        $.each(slickContainer, function(){
            let count = $(this).find('.slick-slide').length;
            
            if( count == 1 && $(window).width() < 768){
                $(this).slick('unslick');
            } else {
                if(!$(this).hasClass('slick-initialized')){
                        $(this).slick();
                }
                if($(this).hasClass('slick-initialized')){
                    if( $('.radio-filter.checked').siblings('input').val() == 'All brands'){
                        //this counterpart for slick refresh
                        $(this).slick('unslick');
                        $(this).slick();
                    }
                }
            }
        });    
        $('.slider-remove-prev').find('.slick-arrow.slick-disabled').removeAttr('style'); 
        setTimeout(function(){
            processSameHeightElements();
        }, 100);
             
    }
};


// Slider Dots behavior (display only 7)
function loadOpacityDots() {
    $('.box-shadow-slick').each(function (i) {
        var $this = $(this);
        if ($(window).width() > 991) { //if slides <= 3
            var slideCount = $this.find('.slick-slide').length;
            if (slideCount <= 3) {
                if ($this.hasClass('slick-initialized')) {
                    $this.slick('unslick');
                }
            }
        } else if ($(window).width() < 768) {
            var totalCount = $this.find('.slick-dots li').length;
            if (totalCount > 7) { //if dots > 7
                $this.addClass('opacityDots');
                if($this.find('.slickDots-container').length == 0){
                    $this.find('ul.slick-dots').wrap("<div class='slickDots-container'></div>");

                }
                
                $this.find('ul.slick-dots li').each(function (index) {
                    var $list = $(this);
                    $list.addClass('dot-index-' + index);
                });
                
                $this.find('ul.slick-dots li').eq(5).addClass('opacity-50');
                $this.find('ul.slick-dots li').eq(6).addClass('opacity-20');
            }
        }
    });

    var dotOffset = 0;
    var transformXIntervalNext = -18;
    var transformXIntervalPrev = 18;
    $('.opacityDots').on("beforeChange", function(event, slick, currentSlide, nextSlide){
        var $this = $(this);
        var totalCount = $this.find('.slick-dots li').length;
        if(nextSlide < 4){ // first 4 cards
            dotOffset = 0;
            // console.log('first card:', nextSlide+1);
            
            $this.find('ul.slick-dots').css('transform', 'translateX(0px)');
            $this.find('.slick-dots li').removeClass(['opacity-20', 'opacity-50']);
            $this.find('ul.slick-dots li:eq(5)').addClass('opacity-50');
            $this.find('ul.slick-dots li:gt(5)').addClass('opacity-20');
            if(nextSlide == 2){                    
                $this.find('ul.slick-dots li:eq(0)').addClass('opacity-50');
            } else if(nextSlide == 3){
                $this.find('ul.slick-dots li:eq('+ (nextSlide - 2) +')').addClass('opacity-50');
                $this.find('ul.slick-dots li:eq('+ (nextSlide + 2) +')').addClass('opacity-50');
                $this.find('ul.slick-dots li:lt('+ (nextSlide - 2) +')').addClass('opacity-20');
                $this.find('ul.slick-dots li:gt('+ (nextSlide + 2) +')').addClass('opacity-20');
            }

        } else if(nextSlide >= 3 && nextSlide <= totalCount-4){ //middle cards
            // console.log('middle card:', nextSlide+1);

            if(nextSlide >= 4){
                if(nextSlide > currentSlide){
                    dotOffset = dotOffset + transformXIntervalNext;
                    if ($this.find('ul.slick-dots li.dot-index-' + nextSlide).hasClass('opacity-50')) {
                        dotOffset = dotOffset + (transformXIntervalNext);
                        
                    } else if ($this.find('ul.slick-dots li.dot-index-' + nextSlide).hasClass('opacity-20')) {
                        dotOffset = dotOffset + (transformXIntervalNext * 2);
                    }
                } else {
                    if(nextSlide < totalCount-4){
                        dotOffset = dotOffset + transformXIntervalPrev;
                        if ($this.find('ul.slick-dots li.dot-index-' + nextSlide).hasClass('opacity-50')) {
                            dotOffset = dotOffset + (transformXIntervalPrev);
                            
                        } else if ($this.find('ul.slick-dots li.dot-index-' + nextSlide).hasClass('opacity-20')) {
                            dotOffset = dotOffset + (transformXIntervalPrev * 2);
                        }
                    }
                }
                $this.find('ul.slick-dots').css('transform', 'translateX(' + dotOffset + 'px)');
            }
            $this.find('.slick-dots li').removeClass(['opacity-20', 'opacity-50']);
            
            $this.find('ul.slick-dots li:eq('+ (nextSlide - 2) +')').addClass('opacity-50');
            $this.find('ul.slick-dots li:eq('+ (nextSlide + 2) +')').addClass('opacity-50');
            $this.find('ul.slick-dots li:lt('+ (nextSlide - 2) +')').addClass('opacity-20');
            $this.find('ul.slick-dots li:gt('+ (nextSlide + 2) +')').addClass('opacity-20');
            
        } else if(nextSlide > totalCount-4){ //last 3 cards
            // console.log('last card:', nextSlide+1);
            
            if(currentSlide < totalCount-4){
                var i = (totalCount-1) - nextSlide;
                dotOffset = dotOffset + (transformXIntervalNext * i);
                $this.find('ul.slick-dots').css('transform', 'translateX(' + dotOffset + 'px)');
            }

            $this.find('.slick-dots li').removeClass(['opacity-20', 'opacity-50']);
            $this.find('ul.slick-dots li:eq('+ (totalCount-6) +')').addClass('opacity-50');
            $this.find('ul.slick-dots li:lt('+ (totalCount-6) +')').addClass('opacity-20');
            if(nextSlide == totalCount -3){
                $this.find('ul.slick-dots li:last-child').addClass('opacity-50');
            }
        }
    });
}

function forceSkipSameHeightMobile() {
    if (window.matchMedia("(max-width: 767px)").matches) {
        if ($('[data-same-height-skip-mobile]').length > 0) {
            setTimeout(function () {
                $('[data-same-height-skip-mobile]').each(function () {
                    $(this).find('[data-same-height-index]').css({ 'min-height': '' });
                });
            }, 300);

        }
    }
}

//for star rating
function starRating() {
    let starRatingContainer = $(".star-rating");
    starRatingContainer.each(function () { //to be able to have multiple star rating 
        starRatingEvent($(this));
    });
}

function starRatingEvent(value) {
    $(value).find('label').hover(function () {
        let label = $(value).find('label');
        $(value).find('label').removeClass("blue-star");
        label.each(function () {
            let $this = $(this);
            $this.addClass("blue-star");
            if ($this.is(":hover")) {
                return false;
            }
        });
    }, function () { //mouse out
        let label = $(value).find('label');
        label.each(function (key, value) {
            let $this = $(this);
            let rating = $(this).closest(".star-rating").attr("data-rating");
            if (key + 1 > rating) { //select star rating value to add class base on data-rating
                $this.removeClass("blue-star");
            } else {
                $this.addClass("blue-star");
            }
        });
    });
    $('.star-rating input').focus(function () { //focus
        let input = $('.star-rating input');
        let label = $('.star-rating label');
        $('.star-rating label').removeClass("blue-star");
        label.each(function () {
            let $this = $(this);
            let id = $this.attr("for");
            $this.addClass("blue-star");
            if ($('.star-rating input#' + id).is(":focus")) {
                return false;
            }
        });
    });
    $('.star-rating input').on("click", function () { //select data-rating star value
        let $this = $(this);
        let rating = $this.closest(".star-rating");
        rating.attr("data-rating", $this.val());
    });
}

$('.clearStarRating').on("click", function () { //reset star rating
    let rating = $(".star-rating");
    rating.attr("data-rating", "0");
    $('.star-rating label').removeClass("blue-star");
});

//for price range slider
function priceRangeSlider() {
    let priceSlider = $(".slider-range"); 
    priceSlider.each(function () { //to be able to have multiple range slider
        priceRange($(this));
        
    });
 
}

function priceRange(value) {
    //declare data attribure to be reusable
    //console.log($(value).data("min"));
    let minimum = $(value).data("min");
    let maximum = $(value).data("max");
    let rangevalue = $(value).data("stuff");
    let labelleft = $(value).siblings('.label-left'); //select tooltip for specific range slider
    let labelright = $(value).siblings('.label-right');

    let slideritem = $(value).slider({
        range: true,
        min: minimum,
        max: maximum,
        values: rangevalue,
        slide: function (event, ui) { //change value on label/tooltip when sliding
            labelleft.text(ui.values[0] + "$");
            labelright.text(ui.values[1] + "$");
        }, change: function (event, ui) { //change value on label/tooltip on change
            labelleft.text(ui.values[0] + "$");
            labelright.text(ui.values[1] + "$");
        }

    });
    slideritem.find(".ui-slider-handle").first().append(labelleft).hover(function () {
        labelleft.show(); //show minimum value tooltip/label
    });
    slideritem.find(".ui-slider-handle").last().append(labelright).hover(function () {
        labelright.show(); //show maximum value tooltip/label
    });

    //upon load show initial value
    labelleft.text(rangevalue[0] + "$");
    labelright.text(rangevalue[1] + "$");
}


//refresh slick on accordion expand
$('.refresh-tabpanel-slick-js').on('click', function(){
    console.trace();
    let $this = $(this);
    let id = $this.attr('aria-controls');
    let tabpanel = $('#' + id);
    let slickContainer = tabpanel.find('.infoblock-slider.slick-initialized');
    
    tabpanel.attr('tabindex','-1');
    slickContainer.slick('refresh');

    setTimeout(function(){
        processSameHeightElements();
    }, 0);
});



//Product Details page with thumbnail
function initProductThumbnailModalSlick() {
    $('.modal-Product-Details-js').each(function(){
        $(this).click(function () {
            if($('.product-slider').hasClass('slick-initialized')) {
                var currentSlide = $('.product-slider').slick('slickCurrentSlide');
                var prodModalSlider = $('.product-modal-slider');
                setTimeout(function () {
                    prodModalSlider.slick('refresh');
                }, 0);
                prodModalSlider.slick('slickGoTo', currentSlide);

                prodModalSlider.on('init afterChange', function () {
                    let $this = $(this);
                    setTimeout(function () {
                        $this.find('.slick-slide.slick-active').attr('tabindex', 0);
                    }, 100);
                });
            }
        });
    });
}

//Accordion Tab Panel 
function accordionTab() {
    let tabActive = $('.accordionTab.show');
    let activePanel = $("#" + tabActive.data('panel'));
    let activeTabDetail = $(tabActive).find('.accordionTab-detail');

    activePanel.addClass("panelFadeIn");
    activeTabDetail.addClass("tabDescriptionFadeIn");
    accordionthumbHeight($(tabActive));
}

function accordionthumbHeight(e) {
    let tabTrackThumb = $('.tab-track-thumb');
    if (tabTrackThumb.length > 0) {
        setTimeout(function () {
            let position1 = e.position();
            $(tabTrackThumb).css('top', position1.top + 'px');
            $(tabTrackThumb).css('height', e.outerHeight() + 'px');
        }, 5);
        setTimeout(function () {
            let position2 = e.position();
            $(tabTrackThumb).css('height', e.outerHeight() + 'px');
            $(tabTrackThumb).css('top', position2.top + 'px');
        }, 90);
        setTimeout(function () {
            let position3 = e.position();
            $(tabTrackThumb).css('height', e.outerHeight() + 'px');
            $(tabTrackThumb).css('top', position3.top + 'px');
        }, 180);
        setTimeout(function () {
            let position4 = e.position();
            $(tabTrackThumb).css('top', position4.top + 'px');
            $(tabTrackThumb).css('height', e.outerHeight() + 'px');
        }, 330);  
    }
}

function thumbHeightResize() {
    let accordionTabActive = $('.collapse-accordion-accessible-toggle.show');
    let tabParent = $(accordionTabActive).closest('.accordionTab');
    if (tabParent.length > 0) {
        accordionthumbHeight($(tabParent));
    }
}

$(function () {
    let accordionTab = $('.accordionTab a');

    $(accordionTab).each(function () {
        let $activeTab = $(this);
        $activeTab.on('click', function () {
            let target = $(this);
            let tabCount, tabPanel, accordionTab;
            let tabParent = $(target).closest('.accordionTab');
            let tabDetails = $(tabParent).find('.accordionTab-detail');
            let panel = $("#" + tabParent.data('panel'));

            tabPanel = document.getElementsByClassName("accordionTabPanel");

            let tabShow = $("#" + tabDetails[0].id);
            if (tabShow.hasClass("show")) {
                event.stopPropagation();
            }

            for (tabCount = 0; tabCount < tabPanel.length; tabCount++) {
                if (tabPanel[tabCount].id != panel[0].id) { //get unselected panel to hide element
                    document.getElementById(tabPanel[tabCount].id).classList.add("d-sm-none");
                    tabPanel[tabCount].className = tabPanel[tabCount].className.replace("d-sm-block", "");
                    tabPanel[tabCount].className = tabPanel[tabCount].className.replace("panelFadeIn", "");
                }
                else {
                    panel.removeClass("d-sm-none");
                    panel.addClass("d-sm-block");
                    panel.addClass("panelFadeIn");
                }
            }

            accordionTab = document.getElementsByClassName("accordionTab");
            for (tabCount = 0; tabCount < accordionTab.length; tabCount++) {
                if (accordionTab[tabCount].id == tabParent[0].id) { //Get selected Tab to show element
                    tabDetails.addClass("tabDescriptionFadeIn");
                } else {
                    let tabDetailCount;
                    let tablinkActive = document.getElementById(accordionTab[tabCount].id);
                    let tabPanelDetail = $(tablinkActive).find('.accordionTab-detail');
                    for (tabDetailCount = 0; tabDetailCount < tabPanelDetail.length; tabDetailCount++) {
                        tabPanelDetail[tabDetailCount].className = tabPanelDetail[tabDetailCount].className.replace("tabDescriptionFadeIn", "");
                    }
                }
            }
            accordionthumbHeight($(tabParent));
        });
    });
});

// Start fix for slick focus sequence
function updateSlickDots(activeDot, $this) {
    setTimeout(function () {
        activeDot.attr('tabindex', 0);
        $this.find('.slick-dots li:not(".slick-active") button').attr('tabindex', -1);
        $this.find('.slick-dots button').removeAttr('aria-selected');
        activeDot.attr('aria-selected', true);
    });
}
let slickTrigger;
let beforeChangeCurrentSlide;
$(".link-slide").on('afterChange', function (event, slick, currentSlide, nextSlide) {
    lazyLoad($(this));
    if (beforeChangeCurrentSlide != currentSlide) {
        var $this = $(this);
        if (slickTrigger.hasClass('slick-arrow')) {
            let firstActiveCard = $this.find('.slick-active').first();
            firstActiveCard.find('a').focus();
            let activeDot = $this.find('.slick-dots .slick-active button');
            updateSlickDots(activeDot, $this);
        }
        else {
            let activeDot = $this.find('.slick-dots .slick-active button');
            if ($('body').hasClass('is_tabbing')) {
                activeDot.focus();
            }
            updateSlickDots(activeDot, $this);
        }
    }
});
$(".link-slide").on('beforeChange', function (event, slick, currentSlide, nextSlide) {
    beforeChangeCurrentSlide = currentSlide;
    slickTrigger = $(document.activeElement);
});
// End fix for slick focus sequence

//start accessibility fix for slick tabpanel label
//change aria-describedby to aria-label as ibm accessibility checker suggested
function setSlickLabel(slick) {
    setTimeout(function () {
        let tabpanels = slick.find('.slick-slide[role="tabpanel"]');
        tabpanels.each(function () {
            let ariaDescribedBy = $(this).attr('aria-describedby');
            $(this).attr('aria-labelledby', ariaDescribedBy);
            $(this).removeAttr('aria-describedby');
        });
    });
}
$('.infoblock-slider, .responsive-slick-slider').on('init afterChange', function () {
    setSlickLabel($(this));
});
//End accessibility fix for slick tabpanel label

function switchDeviceColor() {

    $("#maincontent").on("click", ".tile-color-round-2", function () {
        $(this).closest(".color-pallette-js").find(".selected-color-pallette").removeClass("selected-color-pallette");
        $(this).closest(".color-pallette-js").find(".tile-color-round-2").attr("aria-checked", "false");
        $(this).addClass("selected-color-pallette");
        $(this).attr("aria-checked", "true");
    });
    
    let ua = window.navigator.userAgent;
    let msie = ua.indexOf("MSIE ");
    if (msie > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./))  // If Internet Explorer, return version number
    {
        $("#maincontent").on("keydown", ".tile-color-round-2", function (e) {
       var keycode = (e.keyCode ? e.keyCode : e.which);   
       if(keycode == 13) {
           $(this).closest(".color-pallette-js").find(".selected-color-pallette").removeClass("selected-color-pallette");
           $(this).closest(".color-pallette-js").find(".tile-color-round-2").attr("aria-checked", "false");
           $(this).addClass("selected-color-pallette");
           $(this).attr("aria-checked", "true");
        }
    });
    }
    else  // If another browser
    {
        $("#maincontent").on("keypress", ".tile-color-round-2", function (e) {
            var keycode = (e.keyCode ? e.keyCode : e.which);
            if (keycode == 13) {
                $(this).closest(".color-pallette-js").find(".selected-color-pallette").removeClass("selected-color-pallette");
                $(this).closest(".color-pallette-js").find(".tile-color-round-2").attr("aria-checked", "false");
                $(this).addClass("selected-color-pallette");
                $(this).attr("aria-checked", "true");
            }
        });
    }
}

function lazyLoad(container) {
    var modalEl = container;
    setTimeout(function () {
        var lazyImages;
        if (typeof jQuery.fn.Lazy === 'function') {
            lazyImages = modalEl.find("img.lazy");
            //if (lazyImages.length > 0) {
            lazyImages.Lazy({ effect: "fadeIn" });
            //}
        }
    }, 100);
}

$(document).on('click', '.actual-tabs-controller-js [role="tab"]', function () {
    lazyLoad($(this).closest('section').find('.tab-panels-container'));
});

$(document).on('keyup', '.actual-tabs-controller-js [role="tab"]', function (e) {
    if (e.which == 37 || e.which == 39) {
        lazyLoad($(this).closest('section').find('.tab-panels-container'));
    }
});

$('.modal').on('show.bs.modal', function (e) {
    var $this = $(this);
    if ($this.find('.carousel-navigator-centered').hasClass('slick-initialized')) {
        $this.find('.carousel-navigator-centered').slick('unslick');
    }
    $this.find('.carousel-navigator-centered').each(function (e) {
        var $this = $(this);
        if ($this.children().length > 1) {
            if ($this.children().length < 3) $this.addClass('two-slides');
            $this.slick();
        }
    });
});

function verticalNav() {
    //Start vertical tab override for link mode
    //Disable tab function vertical tabs to allow link to function
    $('ul.tabs.link-mode li').off('click');

    //Disable tab function on mobile and simulate a click on a link that is set in option's value'
    $('.custom-selection.link-mode').off('change');
    $('.custom-selection.link-mode').on('change', function () {
        let link = $(this).val();
        window.location.href = link;
    });
    //End vertical tab override for link mode
}

//hide provinces popup 
$('.trigger-popup').focusout(function (e) {
    let $thisPopup = $(this);
    let hidePopUp = $('.federal-bar-select-provinces-popup');
    $('.js-current-language').focus(function (e) {
        if (hidePopUp.length > 0) {
            hidePopUp.hide();
            $thisPopup.attr('aria-expanded', 'false'); //expand indicator
        }
    })
    $('.connector-brand a').focus(function (e) {
        if (hidePopUp.length > 0) {
            hidePopUp.hide();
            $thisPopup.attr('aria-expanded', 'false'); //expand indicator
        }
    })
});

// simplified header province selector expanded/collapsed indicater
$('.federal-bar-select-provinces').find('.trigger-popup').on('click touch', function () {
    let $this = $(this);
    let collapsed = $this.attr('aria-expanded');
    if (collapsed) {
        $this.attr('aria-expanded', 'true');
    }
    else {
        $this.attr('aria-expanded', 'false');
    }
});
$(document).on('click touch', function (e) {
    if (!$(e.target).parents().addBack().is('.trigger-popup')) {
        $('.federal-bar-select-provinces').find('.trigger-popup').attr('aria-expanded', 'false');
    }
});
// Set currently selected label for simplified header province selector
function setProvinceLabel() {
    setFocusTimeout(function () {
        let provinces = $('.simplified_header .federal-bar-select-provinces-popup .checkboxes a');

        provinces.each(function () {
            let label = $.trim($(this).find('.label-text').text());
            let selected = " (currently selected region)";
            if ($(this).hasClass('active')) {
                $(this).attr('aria-label', label + selected);
            }
            else {
                $(this).attr('aria-label', label);
            }
        });
    });
}
// supports dismissing of region dropdown when ESC is pressed while trigger button is focused. 
function focusRegion(e) {
    var keyCode = e.keyCode, $el;
    if (27 === keyCode) {
        $('.trigger-popup').focus();
    }
}

//Set delay on processSameHeightElements in modals upon resizing
function processModalSameHeight() {
    setTimeout(function () {
        processSameHeightElements();
    }, 200);

}

//Set tab sequense for small-slick
$('.slick-small').on('init reInit', function () {
    let $this = $(this);
    let slickDots = $this.parent().find('.slick-dots');
    slickDots.appendTo($this.parent()).css('margin-top', '-8px');
    $(".slick-small").each(function (instance) {
        $this = $(this);
        let slickSlide = $this.find('.slick-slide:not(.slick-cloned)');
        slickSlide.each(function () {
            let $this = $(this);
            let index = $this.data('slick-index');
            $this.attr('aria-labelledby', 'slick-slide-control' + (instance + 1) + index);
        })
    });
    $('.slick-cloned').removeAttr('id');
});
$(".slick-small").on('afterChange', function (event, slick, currentSlide, nextSlide) {
    lazyLoad($(this));
    if (beforeChangeCurrentSlide != currentSlide) {
        let $this = $(this);
        if (!slickTrigger.hasClass('slick-arrow')) {
            let activeDot = $this.parent().find('.slick-dots .slick-active button');
            if ($('body').hasClass('is_tabbing')) {
                activeDot.focus();
            }
        }
    }
});
$(".slick-small").on('beforeChange', function (event, slick, currentSlide, nextSlide) {
    beforeChangeCurrentSlide = currentSlide;
    slickTrigger = $(document.activeElement);
});

// Start fix for Modals that are triggered via js .modal('show');
// Returns focus on modal trigger upon closing modal [SPRINT 14.2 - BTC-8499]
function modalTriggerReturnFocus() {
    let modalTrigger;
    $('[data-toggle="modal"]').on("click", function (event) {
        modalTrigger = $(this);
    });
    $('body').on('hidden.bs.modal', '.modal ', function () {
        if (modalTrigger) {
            modalTrigger.focus();
            modalTrigger = "";
        }
    });
}
// End fix for Modals that are triggered via js .modal('show');

// Close tooltip when escape key is pressed
$('[data-toggle="tooltip"]').on('keydown', function (event) {
    if (event.key === "Escape") {
        $(this).tooltip('hide');
    }
});
