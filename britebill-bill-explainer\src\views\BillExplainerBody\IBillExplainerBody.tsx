import { ICSMTransaction, IPBE } from "../../models";

export interface IBillExplainerBody {
    pbe: IPBE;
    pbeCategory: string;
    description: string;
    billCloseDate: string;
    cycleStartDate: string;
    cycleEndDate: string;
    subscriberDetails: any;
    chargeItems: any;
    titleKey: string;
    descriptionKey: string;
    detailedDescKey: string;
    previousPeriodStartDate: string;
    previousPeriodEndDate: string;
    currentPeriodStartDate: string;
    currentPeriodEndDate: string;
    transactions: ICSMTransaction[];
    useLegendsForDiagram: boolean;
    isPBEModalLinkDisabled: boolean;
}