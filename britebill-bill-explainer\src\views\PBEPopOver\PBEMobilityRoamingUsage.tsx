import * as React from "react";
import { P<PERSON><PERSON>eader, P<PERSON>Footer } from "singleban-components";
import { InjectedIntlProps, injectIntl } from "react-intl";
import { IPBE } from "../../models";
import { modalOpenedOmniture } from "../../utils/Utility";



interface Props {
    pbe: IPBE;
    isPBEModalLinkDisabled: boolean;
}

const PBEMobilityRoamingUsage = (props: Props & InjectedIntlProps) => {
    
    const { intl: { formatMessage }, pbe } = props;
    const title = formatMessage({ id: "PBE_MOBILITY_ROAMING" });
    const description = formatMessage({ id: "PBE_MOBILITY_ROAMING_DESCRIPTION" });
    const imageClassName = "icon-05_long_distance_rate_charts_circle";
    const footerItems = [{
        ctaLink: formatMessage({ id: "PBE_MOBILITY_ROAMING_LINK" }, {            
            encryptedAcctNo: pbe?.pbeDataBag?.encryptedIdentifier,
            subNo: pbe?.pbeDataBag?.subNo
        }),
        iconClassName: "icon-17_world",
        titleKey: formatMessage({ id: "PBE_MOBILITY_ROAMING_USAGE_GET_COMPLETE_DETAILS" }),
        ctaTitleKey: formatMessage({ id: "PBE_MOBILITY_ROAMING_USAGE_SEE_ROAMING_TEXT_MESSAGES"  }),
        isFirstRow: true,
        id: "pbe-Moblity-Roaming-Usage-See-Text-Messages"
    }];
    React.useEffect(() => {
        modalOpenedOmniture(props?.pbe?.triggerPbeOmniture, title, description);
    },[title, description]);
    

    return (
        <>
             <PBEHeader descriptionKey={description} titleKey={title} iconClassName={imageClassName} />
             {pbe.pbeDataBag?.showUsageLink && <PBEFooter footerItems={footerItems} isPBEModalLinkDisabled={props.isPBEModalLinkDisabled}/>}
        </>
    );
};


export default (injectIntl(PBEMobilityRoamingUsage));