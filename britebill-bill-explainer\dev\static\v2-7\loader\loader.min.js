"use strict";var BwtkLoader=function(){if(void 0===window.bwtkRequireJS)throw new Error("BwtkLoader needs RequireJS, which was not found.");var e=this,t={baseUrl:"../node_modules/",registryPath:"",devMode:!1,paths:{},aliases:{},additionalDependencies:[],context:""},r=["redux","react","react-dom","scarlet","rxjs","redux-actions","react-redux","react-intl","prop-types","bwtk","redux-observable"],n=window.bwtkRequireJS.require,i=!1;function o(){var r=function(){var e=t.devMode,r={enforceDefine:!0,deps:["bwtk"],baseUrl:t.baseUrl,paths:{redux:"redux/dist/redux"+(e?"":".min"),react:"react/umd/react."+(e?"development":"production.min"),"react-dom":"react-dom/umd/react-dom."+(e?"development":"production.min"),scarlet:"scarlet/dist/scarlet"+(e?"":".min"),rxjs:"rxjs/bundles/Rx"+(e?"":".min"),"redux-actions":"redux-actions/dist/redux-actions"+(e?"":".min"),"react-redux":"react-redux/dist/react-redux"+(e?"":".min"),"react-intl":"react-intl/dist/react-intl"+(e?"":".min"),"prop-types":"prop-types/prop-types"+(e?"":".min"),bwtk:"bwtk/dist/bwtk"+(e?"":".min"),"redux-observable":"redux-observable/dist/redux-observable"+(e?"":".min")},onNodeCreated:function(e){e.setAttribute("crossorigin","anonymous")}};t.context&&(r.context=t.context);Object.keys(t.paths).forEach(function(e){r.paths[e]=t.paths[e]}),window.Intl?r.paths.intl&&delete r.paths.intl:r.paths.intl||(r.paths.intl="intl/dist/Intl.min",r.shim={bwtk:{deps:["intl"]}});Object.keys(t.aliases).length&&(r.map={"*":t.aliases});return r}(),o=r.context,s=n.config(r);window.define=window.bwtkRequireJS.define,define("rxjs/Observable",["rxjs"],function(e){return e}),define("rxjs/Subject",["rxjs"],function(e){return e}),define("rxjs/operator/filter",["rxjs"],function(e){return e.Observable.prototype}),define("rxjs/operator/map",["rxjs"],function(e){return e.Observable.prototype}),define("rxjs/operator/switch",["rxjs"],function(e){return e.Observable.prototype}),define("rxjs/operator/switchMap",["rxjs"],function(e){return e.Observable.prototype}),define("rxjs/observable/from",["rxjs"],function(e){return e.Observable}),define("rxjs/observable/merge",["rxjs"],function(e){return e.Observable}),define("rxjs/observable/of",["rxjs"],function(e){return e.Observable}),o&&(window.bwtkRequireJS[o]={require:s});var a=["bwtk"];r.paths.bundle&&a.push("bundle"),t.additionalDependencies.forEach(function(e){a.push(e)}),a=e.uniqueArray(a),s(a,function(){e.onReady(s,o||void 0),i=!1})}return e.reset=function(){for(var t in n.s.contexts){var r=n.s.contexts[t];for(var i in r.defined)r.require.undef(i);delete n.s.contexts[t],delete window.bwtkRequireJS[t]}return e},e.onReady=function(){},e.setPath=function(n,i){var o=i.replace(/\.js$/i,"");return t.paths[n]=o,n in r||t.additionalDependencies.push(n),e},e.setPaths=function(t){return Object.keys(t).forEach(function(r){e.setPath(r,t[r])}),e},e.setBaseUrl=function(r){return t.baseUrl=r,e},e.addWidget=function(r,n){var i=void 0===n?t.registryPath:n;return i=i.replace("{WIDGET}",r),e.setPath(r,i),t.additionalDependencies.push(r),e},e.setBundle=function(t){return this.setPath("bundle",t),e},e.start=function(t){i=!0,"function"==typeof t&&(e.onReady=t),void 0!==window.BwtkPolyfill?window.BwtkPolyfill.ready(o):o()},e.devMode=function(){return t.devMode=!0,e},e.loading=function(){return i},e.setRegistry=function(r){return t.registryPath=r,e},e.addAlias=function(r,n){return t.aliases[n]=r,e},e.multiVersion=function(){return t.context="_"+Math.random().toString(36).substr(2,9),e},e.uniqueArray=function(e){return e.filter(function(e,t,r){return r.indexOf(e)==t})},e};
