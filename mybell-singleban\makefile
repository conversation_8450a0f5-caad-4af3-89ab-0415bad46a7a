build:
	npm install -g linklocal
	git clone https://gitlab.int.bell.ca/uxp/singleban-components.git
	cd singleban-components;git checkout ${CI_COMMIT_REF_NAME} || git checkout Release; rm package-lock.json; ls; npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run build || true 
	git clone https://gitlab.int.bell.ca/uxp/britebill-bill-explainer.git
	cd britebill-bill-explainer; git checkout ${CI_COMMIT_REF_NAME} || git checkout Release; rm package-lock.json; npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run build || true
	git clone https://gitlab.int.bell.ca/uxp/singleban-bill.git
	cd singleban-bill; git checkout ${CI_COMMIT_REF_NAME} || git checkout Release;rm package-lock.json;  npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run build || true
	git clone https://gitlab.int.bell.ca/uxp/mybell-singleban.git
	cd mybell-singleban; git checkout ${CI_COMMIT_REF_NAME} || git checkout Release;rm package-lock.json;  npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run linklocal; npm run build || true; npm install --package-lock-only --legacy-peer-deps
