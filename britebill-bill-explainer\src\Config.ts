import { Injectable, CommonFeatures, LoggerSeverityLevel } from "bwtk";
const { BaseConfig, configProperty } = CommonFeatures;

@Injectable
export class Config extends BaseConfig<Config> {
  @configProperty("http://127.0.0.1:8883/")
  url: string;

  @configProperty("getBillExplainer")
  billExplainerPath: string;

  @configProperty(LoggerSeverityLevel.All)
  logLevel: LoggerSeverityLevel;

  @configProperty(false)
  mockData: boolean;

  @configProperty("")
  brand: string;

  @configProperty("")
  userID: string;

  @configProperty("")
  Province: string;

  @configProperty("")
  channel: string;

  @configProperty("")
  language: string;

  @configProperty("")
  CSRFToken: string;

  @configProperty(false)
  isUXPMode: boolean;

  @configProperty({})
  links: string;

  //@configProperty("getPBEDataFromActionURL") //uncomment to test action url for PBE locally
  @configProperty("")
  mockedPBEActionURL: string;

  @configProperty("")
  pbeBaseActionURL: string;

  @configProperty(false)
  isPbeModalLinkDisabled: boolean;
}
