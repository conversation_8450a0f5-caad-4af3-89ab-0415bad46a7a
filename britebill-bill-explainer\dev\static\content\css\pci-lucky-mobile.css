body {
    color: #555;
}

.height-20 {
    height: 20px;
}

.height-60 {
    height: 60px;
}

.width-85 {
    width: 85px;
}

.width-465 {
    width: 465px;
}

.width-100-percent-xs {
    width: 100%;
}

.max-width-100-percent {
    max-width: 100%;
}

.line-height-18 {
    line-height: 18px;
}

.line-height-22 {
    line-height: 22px;
}

.position-static {
    position: static;
}
.tooltip.show {
opacity: 100%
}

.tooltip.bs-tooltip-bottom {
    margin-top: 20px;
}

.tooltip-interactive .tooltip.bs-tooltip-bottom .arrow {
    -webkit-filter: drop-shadow(0px -8px 5px rgba(0,0,0,0.12));
    -moz-filter: drop-shadow(0px -8px 5px rgba(0,0,0,0.12));
    filter: drop-shadow(0px -8px 5px rgba(0,0,0,0.12));
}

.tooltip-interactive .arrow {
    border-width: 0 25px 25px;
    border-bottom-color: #b9b9b9;
    left: 50%;
    margin-left: 0;
    top: -20px;
    margin-left: -33px;
}

.tooltip-interactive .arrow:before {
    content: "";
    display: block;
    border-width: 0 25px 25px;
    width: 0;
    height: 0;
    border-color: transparent;
    border-bottom-color: #f9f9f9;
    border-style: solid;
    bottom: -3px;
    position: relative;
}

.tooltip-inner {
    box-shadow: 0 0 11px 0 rgba(0,0,0,0.27);
    -webkit-box-shadow: 0 0 11px 0 rgba(0,0,0,0.27);
    -moz-box-shadow: 0 0 11px 0 rgba(0,0,0,0.27);
    border: 1px solid #e1e1e1;
}

.lm-form-control-select-box.pci-select-box:after {
    font-family: 'pci-lucky' !important;
    top: -3px;
}

.lm-form-control-select.LM_reg {
    font-family:"GTWalsheim", Helvetica, Arial, sans-serif; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;
}

.min-width-80 {
    min-width: 80px;
}

.lm-form-control-select-province:after {
    right: unset;
    top: unset;
    bottom: unset;

}

div.pci-select-box select::-ms-expand {
    display: none;
}

.form-error .lm-form-control-select-box.pci-select-box:after {
    color: #D32020;
}

.top-neg-1 {
    top: -1px;
}

.font-arial {
    font-family: Arial, Helvetica, sans-serif;
    letter-spacing: 0
}


.form-error label {
    color: #D32020;
}
.form-item-note-error {
    padding-top:5px;
    padding-bottom:10px;
}

/* Required to move modal close button*/
.modal-header .close {
    top: 12px;
}

.border-darkBlue-round {
    border: 1px solid #002D72;
    border-radius: 10px;
    padding: 2px 5px;
}

/*Cheque line*/
.cheque-line:after {
    content: "";
    opacity: 1;
    display: block;
    background: #002D72;
    width: 1px;
    height: 20px;
    position: absolute;
    left: 50%;
    top: 20px;
}

.cheque-line-left:after {
    content: "";
    opacity: 1;
    display: block;
    width: 8px;
    background: none;
    border-bottom: 1px solid #002D72;
    border-right: 1px solid #002D72;
    height: 32px;
    position: absolute;
    left: 7px;
    bottom: -33px;
}

.cheque-line-right:after {
    content: "";
    opacity: 1;
    display: block;
    width: 8px;
    background: none;
    border-bottom: 1px solid #002D72;
    border-left: 1px solid #002D72;
    height: 32px;
    position: absolute;
    left: 20px;
    bottom: -33px;
}

/*Greater than Mobile*/
@media (min-width: 768px) {
    .width-90-sm {
        width: 90px;
    }

    .width-300-sm {
        width: 300px;
    }

    .modal-dialog {
        width: 630px;
    }

    .modal-title,
    .modal-body {
        padding-left: 30px;
        padding-right: 30px;
    }

    .modal-body {
        padding-top: 30px;
        padding-bottom: 30px;
    }

    .modal:before {
         height: 100%; 
    }

    .width-380-sm {
        width: 380px;
    }

    .modal-dialog.pci-dialog {
        width: 645px;
        max-width: 100%;
    }

    .cheque-line:after {
        left: 50%;
        top: 20px;
    }

    .cheque-line-left:after {
        left: 13px;
        bottom: -33px;
    }

    .cheque-line-right:after {
        left: 35px;
        bottom: -33px;
    }

}

@media (min-width: 992px) {
    .modal-dialog {
        width: 751px;
    }
}

@media (max-width: 767.98px) {
    .modal.modal-tooltip.modal-dialog{
        position: fixed;
        width: 100%;
        height: 100%;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        overflow: hidden;
    }



    .modal:before, .modal-dialog {
        vertical-align: top;
        text-align: left;
    }

    .modal.modal-tooltip .tooltip-dialog {
        margin: auto 20px;
        -webkit-transform: translate(0%, -50%);
        transform: translate(0%, -50%);
        position: relative;
    }

    .modal-open .modal.modal-tooltip .modal-content {
        min-height: 0;
    }

    .modal-open .modal-backdrop {
        visibility: visible;
    } 

    .line-height-32-xs {
        line-height: 32px;
    }

    .modal .modal-dialog.pci-dialog {
        height: auto;
        max-height: calc(100% - 45px);
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        transition: unset;
        top: 50%;
        transform: translateY(-50%) !important;
        position: relative;
    }

    .modal.fade.show .modal-dialog.pci-dialog {
        top: unset;
        transform: unset !important;
        transition: unset;
    }
    /* End */


}
