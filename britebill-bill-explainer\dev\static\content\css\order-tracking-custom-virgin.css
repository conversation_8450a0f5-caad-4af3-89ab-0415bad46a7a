﻿/*VIRGIN MOBILE CUSTOM CSS*/
.vrgn .h1, .vrgn .h2, .vrgn .h3, .vrgn .h4, .vrgn .h5, .vrgn .h6, .vrgn h1, .vrgn h2, .vrgn h3, .vrgn h4, .vrgn h5, .vrgn h6 {
    line-height: 1.1;
}

.vrgn .border-radius-3 {
    border-radius: 3px;
}

.vrgn .radio-holder {
    height: 25px;
}

.vrgn .spc-btn {
    background-color: #e10a0a;
    padding-bottom: 13px;
    padding-top: 13px;
    border-radius: 0px;
    color: #ffffff;
    border: none;
    line-height: 1.42857143;
}

.vrgn .spc-footer-border-top {
    border-top: 1px solid #ececec;
    height: 94px;
}

.vrgn .spc-footer-border-top .price sup {
    top: -2px !important;
}

.vrgn .spc-option .virgin.highlight {
    border: 3px solid #000000;
    pointer-events: none;
    border-radius: 3px;
    position: absolute;
    height: 100%;
    width: 100%;
    opacity: 0;
    z-index: 1;
    left: 0px;
    top: 0px;
}

.vrgn .spc-option.active-spc-option .virgin.highlight {
    opacity: 1;
}

.vrgn .graphical_ctrl.ctrl_radioBtn input:checked ~ .ctrl_element {
    background: #fff;
    border: 1px solid #c9c9c9;
}

.vrgn .graphical_ctrl input:checked ~ .ctrl_element {
    box-shadow: none;
    background: #cc0000;
    border: 1px solid #cc0000;
}

.vrgn .ctrl_radioBtn .ctrl_element:after {
    left: 5px;
    top: 5px;
    height: 13px;
    width: 13px;
    border-radius: 50%;
    background: #cc0000;
}

.vrgn .spc-option .non-card-input .ctrl_element.radio-button {
    top: -15px;
    left: 0px;
}

.vrgn .spc-option .non-card-label > div {
    margin-left: 0px;
}

.vrgn .slick-dots {
    margin-top: 15px !important;
    padding: 0px !important;
}

.vrgn .prod-carousel-content .slick-prev:before,
.vrgn .prod-carousel-content .slick-next:before {
    top: 3px;
}

.simplified-header.vm-black-bg {
    background-color: #000000;
    height: 75px;
}

.bgGrayLight3 {
    background-color: #ececec;
}


.vg-body {
    margin-bottom: 80px;
}

.vg-footer {
    height: 80px;
    
    position: absolute;
    bottom: 0px;
    width: 100%;
}

.mobility-card > .flex1:first-child {
    margin-right: 7.5px;
}

.mobility-card > .flex1:last-child {
    margin-left: 7.5px;
}

.virgin-card-border {
    border: 1px solid #FCFCFC;
}

.borderRadiusBottom4{border-radius:0 0 4px 4px};


.vm-lightgraytext {
    color: #C6C6C6;
}

.vm-lightgraytext2 {
    color: #cbcbcb;
}

.vm-bluetxt {
    color: #00529C;
}

.virgin-hug-modal-content .modal-header {
    background-color: #ffffff;
    border-bottom: none;
}

.virgin-hug-modal-content .modal-body {
    margin-top: 0px;
    margin-bottom: 0px;
}

.virgin-hug-modal-content .modal-footer {
    border-top: none;
    padding: 30px;
    box-shadow: 0 4px 36px 0 rgba(0,0,0,0.3);
}

.virgin-hug-modal-content  .modal-dialog .bgGrayLight2{
    background-color: #e1e1e1 !important;
}

 .virgin-hug-modal-content .compare-modal .compare-table-wrap .current-solution, .compare-modal .compare-table-wrap .current-solution .compare-modal-heading{
    border-top-left-radius: 0px;
}

 .virgin-hug-modal-content .compare-modal .compare-table-wrap .current-solution .compare-modal-total-monthly, .compare-modal .compare-table-wrap .current-solution{
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
}

 .virgin-hug-modal-content .compare-modal .compare-table-wrap .new-solution .compare-modal-heading, .compare-modal .compare-table-wrap .new-solution{
        border-top-right-radius: 0px;
        border-top-left-radius: 0px;
}

 .virgin-hug-modal-content .compare-modal .compare-table-wrap .new-solution .compare-modal-total-monthly, .compare-modal .compare-table-wrap .new-solution{
    border-bottom-right-radius: 0px;
    border-bottom-left-radius: 0px;
}

.virgin-hug-modal-content .new-solution .compare-modal-heading, .new-solution .compare-modal-total-monthly{
    background-color: #000;
}
 .virgin-hug-modal-content .compare-modal .new-solution .promoWrap .promo {
    background: #CC0000;
    color: #ffffff;
    border: none;
}

.virgin-hug-modal-content .txtRight .txtWhite{
    color: #fff;
}

/* For page 1.3.1 keeping modal in center - START */ 
.modal.fade .modal-dialog.virgin-hug-modal-dialog{
    top: auto;
    margin: 20px auto;
    transform: none !important;
}
/* For page 1.3.1 keeping modal in center - END */

.height160{
    height: 160px !important;
}

/*For Tablet and Mobile - Start*/
@media screen and (max-width:991.98px) {
    .simplified-header.vm-black-bg {
        height: 54px;
    }
    
    .vg-body {
        margin-bottom: 120px;
    }
    
    .vg-footer {
        height: 120px;
    }

}
/*For Tablet and Mobile - End*/

/*For mobile only - Start*/
@media (max-width: 767px) {
    .simplified-header.vm-black-bg {
        height: 54px;
    }

    .vg-body {
        margin-bottom: 175px;
    }
    
    .vg-footer {
        height: 175px;
    }

    .no-side-border-xs {
        border-right: none;
        border-left: none;
    }

    .container.liquid-container.no-pad-xs {
        padding: 0px
    }

    .mobility-card > .flex1:first-child,
    .mobility-card > .flex1:last-child {
        margin: 0px;
    }


}
/*For mobile only - End*/

.icon-check-circled:before {
    content: "\e609";
}




