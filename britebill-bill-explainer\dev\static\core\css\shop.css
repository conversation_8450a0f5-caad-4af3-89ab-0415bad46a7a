/* use mobile-first approach since that's what Bootstrap 4 uses (especially its display utilities) */

/* START Bootstrap class overrides */



/* END Bootstrap class overrides */

/* START native element defaults */

/* Added global generic rule for achor tag that under p and li elements*/
main p a, main li a, .modal p a {
    text-decoration: underline;
}

    main p a:focus, main p a:hover, main li a:focus, main li a:hover, .modal p a:focus, .modal p a:hover {
        text-decoration: none;
    }
/* Added global generic rule for achor tag that under p and li elements*/

/* Override global generic rule */
.more-ways-to-shop li a, .icon-text-list li a, main li .icon ~ * > a, main li .icon ~ a {
    text-decoration: none;
}

    main li .icon ~ * > a:hover, main li .icon ~ * > a:focus, main li .icon ~ a:hover, main li .icon ~ a:focus {
        text-decoration: underline
    }


/* Override global generic rule */
main {
    background: #fff
}

    main a,
    .txtUnderlineOnHover {
        align-items: center;
        color: #00549a;
        display: inline-flex;
    }

        /* for anchor tags inside main, focus only the element with the anchor-text class. to apply the same behavior to elements outside main, use the txtUnderlineOnHover class. */
        main a,
        main a:hover,
        main a:focus,
        main a:not(.txtUnderline) .anchor-icon,
        .txtUnderlineOnHover:not(.txtUnderline),
        .txtUnderlineOnHover:not(.txtUnderline):hover,
        .txtUnderlineOnHover:not(.txtUnderline):focus,
        .txtUnderlineOnHover:not(.txtUnderline) .anchor-icon {
            text-decoration: none;
        }

        main a:hover > .anchor-text,
        main a:focus > .anchor-text,
        .txtUnderlineOnHover:hover > .anchor-text,
        .txtUnderlineOnHover:focus > .anchor-text,
        a:hover .deep-anchor-text,
        a:focus .deep-anchor-text {
            text-decoration: underline;
        }

thead:not(.font-weight-bold) th {
    font-weight: 400;
}

thead th:not(:first-child),
thead td:not(:first-child) {
    box-shadow: inset 1px 0 0 0 #003778;
}

.graphical_ctrl input:checked ~ span {
    font-weight: bold;
    color: #003778;
}

.tooltip {
    font-family: Helvetica,Arial,sans-serif;
}

/* END native element defaults */

/* START generic helpers */
.box-vignette-round-before:before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    box-shadow: inset 0 0 80px 30px rgba(0,0,0,.05);
    pointer-events: none;
    z-index: 1;
}

.box-shadow-hover:hover {
    box-shadow: 0 20px 25px 0 rgba(0,0,0,.2);
}

.column-spacer-15 {
    margin-left: -7.5px;
    margin-right: -7.5px
}

    .column-spacer-15 > div {
        padding-left: 7.5px;
        padding-right: 7.5px;
    }

.column-spacer-30 {
    margin: 0 -15px;
}

.default-line-height {
    line-height: 18px;
}

.inverted,
.inverted *, .inverted a:focus, .inverted a:hover {
    color: #fff;
}

.inverted-border,
.inverted-border * {
    border-color: #fff;
}

.responsive-border-light-grey{
    border-top: 1px solid #d4d4d4;
}

.border-l-light-grey {
    border-left: 1px solid #e1e1e1;
}

.border-b-light-grey {
    border-bottom: 1px solid #e1e1e1;
}

.border-t-light-grey,
.responsive-border-sm-light-grey,
.responsive-border-md-light-grey {
    border-top: 1px solid #e1e1e1;
}

.bg-bell-blue {
    background-color: #00549a;
}

.bg-bell-dark-blue {
    background-color: #002A4D;
}

.bg-bell-dark-cerulean {
    background-color: #003778;
}

.bg-very-light-grey {
    background-color: #f4f4f4;
}

.color-body-default,
.color-medium-grey {
    color: #555;
}

.color-bell-black{
    color: #111;
}

.color-bell-dim-gray {
    color: #6A6A6A;
}

.color-bell-blue,
.surtitle {
    color: #00549a;
}

.color-success {
    color: #378e42;
}

.non-interactive {
    pointer-events: none;
}

.custom-selected > select {
    display: block;
    position: absolute;
    z-index: 3;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    -webkit-appearance: none;
}

.custom-selected > select {
    display: block;
    position: absolute;
    z-index: 3;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    -webkit-appearance: none;
}

.custom-selected select:disabled {
    opacity: 0;
}

.custom-selected select:disabled ~ button {
    background-color: #f4f4f4;
}

.custom-selected select:focus ~ button {
    outline: 2px auto -webkit-focus-ring-color !important
}

.text-break {
    word-wrap: break-word;
}

main li a.anchor-text-internet {
    text-decoration: none;
}

main li a.anchor-text-internet:hover, main li a.anchor-text-internet:focus  {
    text-decoration: underline;
}

.play-btn-container {
    position: absolute;
    top: 50%; left: 50%;
    transform: translate(-50%,-50%);
}

.modal-header {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

/* tablet only */
@media (max-width: 767.98px) {
    table.compact-xs th,
    table.compact-xs td {
        padding: 12px 9px;
    }
}

/* tablet and larger */
@media (min-width: 768px) {
    .modal-content {
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
    }

    .responsive-border-sm-light-grey, .responsive-border-light-grey {
        border-top: 0;
        border-left: 1px solid #e1e1e1;
    }

    .responsive-border-light-grey {
        border-left: 1px solid #d4d4d4;
    }

    .channel-icon-modal .modal-dialog {
        margin-top: 30px;
        margin-bottom: 30px;
        max-height: unset;
        max-height: none;
    }

    .modal-open .modal.channel-icon-modal {
        overflow-y: auto;
    }

    /* Forcing the modal with channel-icon-modal to forcefully display all the contents without scrollable inside the modal body - This is to replicate the behavior on production modal */
    .modal.channel-icon-modal .modal-body {
        max-height: unset !important;
        max-height: none !important;
    }
}

/* tablet only */
@media (min-width: 768px) and (max-width: 991.98px) {
    .height-8-sm {
        height: 66.6666666667%;
    }
}

/* desktop and larger */
@media (min-width: 992px) {

    .responsive-border-md-light-grey {
        border-top: 0;
        border-left: 1px solid #e1e1e1;
    }

    a .deep-anchor-text {
        text-decoration: none;
    }

    a.contact-link {
        pointer-events: none;
    }

}

/* END generic helpers */

/* START component styles and overrides */
.text-tag-positioned-center {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.text-tag {
    text-transform: uppercase;
    background-color: #00549a;
    padding: 3px 8px;
    color: #fff;
    font-size: 10px;
    border-radius: 2px;
    line-height: 14px;
}

.checked-list,
.no-list-style,
.separator-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.checked-list,
.separator-list {
    margin-top: 15px;
}

    .checked-list li,
    .no-list-style li,
    .separator-list li {
        display: flex;
    }

        .checked-list li:not(:first-child),
        .default-list li:not(:first-child),
        .default-list li:not(:first-child) {
            margin-top: 10px;
        }

    .separator-list > li {
        justify-content: space-between;
    }

        .separator-list > li:not(:first-child) {
            border-top: 1px solid #e1e1e1;
            padding-top: 15px;
        }

        .separator-list > li:not(:last-child) {
            padding-bottom: 15px;
        }

.huge-offer .checked-list li [class*=" icon-check-light"],
section[class*="home-phone-package"] .checked-list li [class*=" icon-check-light"] {
    font-size: 9px;
    font-weight: bold;
    margin-right: 7px;
}

        
.column-spacer-30 {
    margin: 0 -15px;
}

.column-spacer-60 {
    margin: 0 -30px;
}

.secondary-offer .img-container {
    min-height: 190px;
    height: 190px;
}

table.striped tbody tr:nth-child(odd),
table.striped-reverse tbody tr:nth-child(even) {
    background: #fff;
}

table.striped tbody tr:nth-child(even),
table.striped-reverse tbody tr:nth-child(odd) {
    background: #f4f4f4;
}

.table-fixed {
    table-layout: fixed;
    width: 100%;
}



.inverted .btn-primary {
    border-color: #fff;
}

/* looks like a link on mobile and like a button on tablet and larger */

form.compact .form-group {
    margin-bottom: 15px;
    padding-right: 15px;
}

    form.compact .form-group label {
        margin-bottom: 5px;
    }

.form-control {
    color: #555;
    font-size: 14px;
    line-height: 18px;
}

    .form-control,
    .form-control:disabled,
    .form-control[readonly] {
        background-color: #f4f4f4;
    }

.form-required:before {
    color: inherit;
}

.form-control-select + span {
    height: auto;
    padding: 13px 10px;
    right: 0;
}

.form-group.quantity-select {
    width: 115px
}

select.colored-selected.has-selected,
select.colored-selected .selected:not(.no-value) {
    color: #00549a;
}

    select.colored-selected.has-selected::-ms-value {
        color: #00549a;
    }

select.colored-selected option:not(.selected) {
    color: #555;
}

.jumping-cta-form-group {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    position: relative;
}

.collapse-text-trigger[aria-expanded=false] .text-expand,
.collapse-text-trigger[aria-expanded=true] .text-collapse {
    display: inline;
}

.collapse-text-trigger[aria-expanded=true] .text-expand,
.collapse-text-trigger[aria-expanded=false] .text-collapse {
    display: none;
}

.partial-collapse {
    min-height: 240px;
    height: 240px;
    overflow: hidden;
}

    .partial-collapse.collapse:not(.show) {
        display: block;
    }

    .partial-collapse.partial-collapse-limit.collapse:not(.show) > li:nth-child(n+5) {
        display: none;
    }

    .partial-collapse.partial-collapse-limit.collapse.show > li:nth-child(n+5) {
        display: list-item;
    }

    .partial-collapse.partial-collapse-limit.collapse {
        height: auto;
        min-height: 0;
    }

    .partial-collapse.collapse.show {
        height: auto;
    }








.big-price,
.small-price {
    color: #00549a;
    font-family: "bellslim_font_heavy", Helvetica, Arial, sans-serif;
    font-size: 40px;
    letter-spacing: -1px;
    line-height: 36px;
    white-space: nowrap;
}

.small-price {
    font-size: 30px;
    letter-spacing: -.75px;
    line-height: 22px;
}

    .big-price span,
    .small-price span {
        font-size: 18px;
        letter-spacing: -.45px;
        line-height: 22px;
        margin-right: 1px;
        position: relative;
        top: 2px;
        vertical-align: top;
    }

    html[lang='fr'] .big-price span,
    html[lang='fr'] .small-price span {
        margin-right: 0;
    }

    .small-price span {
        font-size: 14px;
        letter-spacing: -.35px;
        top: -5px;
    }

        .big-price span:last-of-type,
        .small-price span:last-of-type {
            margin-left: 2px;
        }

        html[lang='fr'] .big-price span:last-of-type,
        html[lang='fr'] .small-price span:last-of-type {
            margin-left: 0;
        }

.sup-align-top-small {
    font-size: 0.65em;
    line-height: 0;
    position: relative;
    top:-.5em;
    vertical-align: baseline;
}

.hero-banner {
    display: flex;
    position: relative;
    width: 100%;
}

.hero-banner-responsive .image-banner {
    width: 300px;
    height: 340px;
}

.hero-banner-8 {
    height: 440px;
}

    .hero-banner-8 .image-banner {
        max-width: 790px;
    }

.hero-banner:not(.even-split) > div {
    height: 440px;
    overflow: hidden;
}

.hero-banner.even-split:not(.product-banner):not(.product-banner-2) > div {
    min-height: 440px;
}

.hero-banner .banner-image {
    overflow: hidden;
    position: relative;
}

.hero-banner:not(.even-split) .banner-image > img {
    width: 100%;
}

.hero-banner.even-split.product-banner-2.banner-img-middle .banner-image > img {
    align-self: center
}

.hero-banner.even-split .banner-image {
    align-items: flex-start;
    display: flex;
    justify-content: center;
    padding: 30px 0;
}

.hero-banner .banner-image > img.lazy-loading {
    position: absolute;
    height: auto;
    width: auto;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.hero-banner .banner-text {
    margin: 0;
    padding-top: 30px;
    padding-bottom: 30px;
    width: 100%;
}

    .hero-banner .banner-text .surtitle {
        margin-bottom: 10px;
    }

.hero-banner:not(.even-split) .banner-text .big-title + div:not(.big-price),
.hero-banner:not(.even-split) .banner-text .title + div:not(.big-price) {
    margin-top: 10px;
}

.hero-banner:not(.even-split) .banner-text .big-price {
    margin-top: 15px;
}

.hero-banner .banner-text .small-text {
    margin: 15px 0 0;
}

.sub-banner > div {
    display: block;
    padding: 0 30px;
}

.sub-banner .banner-text {
    padding-top: 30px;
    padding-bottom: 10px;
}

    .sub-banner .banner-text ul li {
        align-items: center;
    }

        .sub-banner .banner-text ul li:not(:first-child) {
            margin-top: -8px;
        }

.sub-banner .banner-image {
    width: 100%;
    overflow: hidden;
    position: relative;
}

.product-card {
    border: 1px solid #e1e1e1;
    display: flex;
    flex-direction: column;
}

.product-cards .product-card:not(:last-child) {
    margin-bottom: 15px;
}

.product-card .card-details {
    display: flex;
    padding: 30px 15px 15px;
}

.product-card.tablet-change .card-details {
    padding-bottom: 30px;
}

.product-card .card-icon {
    margin-right: 30px;
    text-align: center;
    width: 150px;
}

    .product-card .card-icon img {
        width: 113px;
    }

.product-card .card-heading {
    display: block;
}

    .product-card .card-heading .small-title {
        margin-bottom: 15px;
    }

.product-card .card-features {
    align-items: center;
}

    .product-card .card-features > div:last-child {
        display: inline-block;
    }

.product-card .card-cta-block {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 30px 15px;
}

    .product-card .card-cta-block .card-price {
        display: flex;
        flex-direction: column;
        padding-bottom: 15px;
    }

.product-card.box-shadow-round .card-cta-block {
    border-radius: 0 0 10px 10px;
}

.product-card.tablet-change .card-cta-block .card-price {
    padding-bottom: 30px;
}

.product-card.tablet-change .card-cta-block > div:only-child .card-price {
    padding-bottom: 0;
}

    .product-card.tablet-change .card-cta-block > div:only-child .card-price .big-price {
        margin-bottom: 15px;
    }

.product-card .card-cta-block * + .card-price {
    margin-top: 10px;
}

.product-card .card-cta-block > div:last-child {
    align-items: center;
    display: flex;
}

.product-card .card-cta-block .small-text {
    margin-top: 15px;
}

.graphic-product-cards {
    display: flex;
    flex-direction: column;
}

.graphic-product-card {
    display: inline-block;
    margin-top: 66px;
    position: relative;
}

    .graphic-product-card > div {
        border: 1px solid #e1e1e1;
    }

    .graphic-product-card .card-heading,
    .graphic-product-card .card-details,
    .graphic-product-card .card-cta-block {
        padding: 30px;
    }

    .graphic-product-card .card-heading {
        box-shadow: inset 0 0 80px 30px rgba(0,0,0,.05);
        padding-top: 95px;
    }

        .graphic-product-card .card-heading img {
            height: 150px;
            left: 50%;
            position: absolute;
            top: -66px;
            transform: translateX(-50%);
        }

.graphic-product-card .box-shadow-round .card-heading {
    border-radius: 10px 10px 0 0;
}

.graphic-product-card .card-cta-block {
    background-color: #f4f4f4;
    border-top: 1px solid #e1e1e1;
}

.graphic-product-card .box-shadow-round .card-cta-block {
    border-radius: 0 0 10px 10px;
}

.secondary-offers {
    display: flex;
    flex-direction: column;
}

.secondary-offer {
    display: inline-block;
}

    .secondary-offer > div {
        display: flex;
        flex-direction: column;
        height: 100%;
        justify-content: space-between;
    }

    .secondary-offer:not(.no-box) > div {
        padding: 0 30px;
    }

.secondary-offer .offer-details {
    padding-bottom: 25px;
}

.secondary-offer:not(.no-box) .offer-details {
    padding-top: 15px;
}

.secondary-offer .offer-details .small-title {
    margin-bottom: 15px;
}

.secondary-offer .offer-image {
    align-items: center;
    display: flex;
    height: 190px;
    justify-content: center;
    margin-bottom: 20px;
}

.secondary-offer:not(.no-box) a {
    margin-bottom: 30px;
}

.huge-offer-details > div {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 30px;
    padding-bottom: 40px;
}

.huge-offer-details .title {
    margin-top: 30px;
}

.huge-offer-details > div > div {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 30px;
}

.huge-offer-details .offer-details-col > div:not(:first-child) {
    margin-top: 30px;
}

.huge-offer-details .offer-price-col .big-price {
    margin-top: 20px;
}

.huge-offer-details .offer-price-col .inline-price-text {
    color: #111;
    margin-top: 25px;
}

    .huge-offer-details .offer-price-col .inline-price-text > span {
        color: #00549a;
    }

.huge-offer-details .offer-price-col .small-text {
    margin-top: 10px;
}

.huge-offer-details .offer-price-col .details-trigger-block {
    margin-top: 30px;
    padding-bottom: 15px;
}

.huge-offer-details .offer-price-col .complementary-text {
    display: none;
    margin-top: 15px;
}

.huge-offer-details .offer-price-col .call-icon-block .anchor-text:not(:last-child) {
    display: inline;
}

.huge-offer-details .offer-price-col .call-icon-block .anchor-text:last-child {
    display: none;
}

.additional-details .accordion-heading {
    display: flex;
}

    .additional-details .accordion-heading .anchor-text {
        color: #00549a;
        font-size: 18px;
    }

.additional-details .accordion-body {
    padding-top: 10px;
    padding-left: 30px;
}

.more-ways-to-shop {
    overflow: hidden;
}

    .more-ways-to-shop .container {
        padding-top: 30px;
        padding-bottom: 20px;
    }

    .more-ways-to-shop .anchor-text {
        font-weight: 400;
    }

/* mobile only */
@media (max-width: 767.98px) {
    /* Update height of the header of the modal for shop */
    .modal-header{padding: 10px 20px 10px 15px}

    .hero-banner-responsive .image-banner {
        width: 210px;
        height: 220px;
    }

    .product-card .card-features {
        width: 100%;
        display: inline-flex
    }

    .product-card .card-features div:nth-child(2) {
        max-width: 75%;
    }
       

    .hero-banner > div {
        display: flex;
        flex-direction: column;
    }

    .hero-banner:not(.even-split) > div {
        align-items: center;
        text-align: center;
    }

    .hero-banner.even-split .banner-text {
        padding-bottom: 0;
    }

    .hero-banner .banner-image {
        flex: 1;
        position: relative;
        width: 100%;
    }

        .hero-banner .banner-image > img {
            max-width: 374px;
        }

    .hero-banner:not(.even-split) .banner-text {
        max-width: 280px;
    }

    .hero-banner.even-split .banner-image {
        height: 222px;
        min-height: 222px;
    }

        .hero-banner.even-split .banner-image > img:not(.lazy-loading) {
            height: 100%;
        }

    .hero-banner.even-split.product-banner .banner-image > img:not(.lazy-loading) {
        height: 161px;
    }

    .hero-banner.even-split.product-banner-2 .banner-image > img:not(.lazy-loading) {
        height: 179px;
    }

    .sub-banner .banner-image > img {
        position: relative;
        height: 131px;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
    }

        .sub-banner .banner-image > img.lazy-loading {
            position: relative;
            height: auto;
            width: auto;
            margin-top: 25px;
            margin-bottom: 35px;
        }

    .product-card .card-icon {
        display: none;
    }

    .product-card .card-heading {
        margin-bottom: 10px;
    }

    .product-card .card-cta-block.collapse:not(.show) {
        display: none;
    }

    .graphic-product-cards > div:not(:last-child) {
        margin-bottom: 30px;
    }

    .secondary-offer:first-child {
        margin-bottom: 15px;
    }

    .huge-offer-details h2 {
        align-self: flex-start;
    }

    .huge-offer-details .offer-details-col {
        padding-bottom: 30px;
    }

    .huge-offer-details .offer-price-col {
        padding-top: 30px;
    }

    .more-ways-to-shop h2 {
        padding-left: 15px;
        margin-bottom: 20px;
    }

    .more-ways-to-shop .content-ways-to-shop {
        padding-left: 5px;
    }

    .more-ways-to-shop .content-ways-to-shop li {
        width: 100%;
    }

    .more-ways-to-shop .content-ways-to-shop > li > a .anchor-text {
        margin-left: 15px;
    }

    .more-ways-to-shop .anchor-icon ~ .anchor-text {
        margin-left: 15px;
    }

    .hero-banner-8 {
        height: 210px;
    }

    .border-none-xs{
        border: none;
    }

    .border-t-light-grey-xs{
        border-top: 1px solid #E1E1E1;
    }
}

/* tablet and larger */
@media (min-width: 768px) {
    html:not([lang=fr]) .jumping-cta {
        position: absolute;
        right: 0;
        top: 2px;
    }

    html[lang=fr] .jumping-cta {
        margin-top: 15px;
    }

    .partial-collapse {
        min-height: 200px;
        height: 200px;
        overflow: hidden;
    }

    

    .hero-banner img {
        top: 90px;
    }

    .hero-banner-5 img {
        max-height: 300px;
    }

    .hero-banner > div {
        align-items: center;
        display: flex;
        flex-direction: row;
        position: relative;
    }

    .hero-banner .banner-text {
        display: flex;
        flex-direction: column;
        height: 100%;
        justify-content: center;
        padding-bottom: 45px;
        padding-top: 45px;
        margin-right: 30px;
        width: 40%;
    }

    .hero-banner .banner-image + .banner-text {
        margin-right: 0;
    }

    .hero-banner .banner-text .surtitle {
        margin-bottom: 15px;
        width: 100%;
    }

    .hero-banner:not(.even-split) .banner-text .big-price {
        margin-top: 20px;
    }

    .hero-banner .banner-image {
        align-self: flex-end;
        height: 100%;
        width: 60%;
    }

    .hero-banner:not(.even-split) .banner-image > img:not(.lazy-loading) {
        position: relative;
        top: 92px;
    }

    /* the long-copy modifier class changes the width ratio for text and image from 40:60 to 60:40. it also increases the top offset of the image to accommodate the smaller height of the image (smaller width = smaller height due to aspect ratio lock) */
    .hero-banner.long-copy .banner-text {
        width: 60%;
    }

    .hero-banner.long-copy .banner-image {
        width: 40%;
    }

        .hero-banner.long-copy .banner-image > img {
            top: 167px;
        }

    /* the even-split modifier class changes the width ratio for text and image to 50:50 */
    .hero-banner.even-split .banner-text,
    .hero-banner.even-split .banner-image {
        width: 50%;
    }

    .hero-banner.even-split .banner-text {
        padding-left: 15px;
    }

    .hero-banner.even-split .banner-image {
        padding-bottom: 45px;
        padding-right: 15px;
        padding-top: 45px;
    }

    .hero-banner.even-split.product-banner .banner-image img {
        height: 302px;
    }

    .hero-banner.even-split.product-banner .banner-text {
        padding-right: 0;
    }

    .hero-banner.even-split.product-banner-2 .banner-image {
        /*padding-top: 90px;*/
    }

        .hero-banner.even-split.product-banner-2 .banner-image img {
            height: 209px;
        }

    .hero-banner.even-split.product-banner-2 .banner-text p {
        /*width: 330px;*/
    }

    .sub-banner > div {
        display: flex;
    }

    .sub-banner .banner-text {
        order: 2;
        padding-left: 15px;
        padding-top: 65px;
        padding-bottom: 60px;
    }

        .sub-banner .banner-text ul li > span:last-child {
            color: #111;
            font-size: 18px;
            line-height: 22px;
        }

    .sub-banner .banner-image {
        order: 1;
    }

        .sub-banner .banner-image > img.lazy-loading {
            position: absolute;
            height: auto;
            width: auto;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

    .product-card.tablet-change {
        flex-direction: row;
    }

    .product-card .card-details {
        padding-bottom: 30px;
        padding-left: 30px;
        padding-right: 30px;
    }

    .product-card .card-cta-block {
        padding-left: 30px;
        padding-right: 30px;
    }

    .product-card.tablet-change.box-shadow-round .card-cta-block {
        border-radius: 0 10px 10px 0;
    }

    .product-card.tablet-change .card-cta-block > div:only-child .card-price .big-price {
        margin-bottom: 30px;
    }

    .product-card:not(.tablet-change) .card-heading {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
    }

        .product-card:not(.tablet-change) .card-heading .small-title {
            margin-bottom: 0;
        }

        .product-card:not(.tablet-change) .card-heading a {
            align-self: flex-end;
        }

    .product-card:not(.tablet-change) .card-features {
        display: inline-block;
        margin-right: 50px;
        width: 175px;
    }

        .product-card:not(.tablet-change) .card-features > div:last-child {
            display: block;
        }

    .product-card:not(.tablet-change) .card-cta-block {
        flex-direction: row;
    }

        .product-card:not(.tablet-change) .card-cta-block .card-price {
            padding-bottom: 0;
        }

    .graphic-product-cards {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .graphic-product-card {
        margin-top: 75px;
    }

    .graphic-product-cards > div:nth-child(odd) {
        padding-right: 15px;
    }

    .graphic-product-cards > div:nth-child(even) {
        padding-left: 15px;
    }
        .graphic-product-card .card-heading {
            padding-top: 115px;
        }

            .graphic-product-card .card-heading img {
                height: 190px;
                top: -75px;
            }
    .secondary-offers {
        flex-direction: row;
    }

        .secondary-offers .secondary-offer:first-child {
            padding-right: 15px;
        }

        .secondary-offers .secondary-offer:last-child:not(:first-child) {
            padding-left: 15px;
        }

    .secondary-offer.no-box:first-child {
        padding-left: 25px;
    }

    .secondary-offer.no-box:last-child {
        padding-right: 25px;
    }

    .secondary-offer:not(.no-box) a {
        margin-bottom: 45px;
    }

    .huge-offer-details h2 {
        margin-top: 30px;
    }

    .huge-offer-details > div > div {
        margin-top: 60px;
    }

    .huge-offer-details .offer-details-col {
        padding-right: 40px;
    }

    .huge-offer-details .offer-price-col {
        padding-left: 40px;
    }

    .more-ways-to-shop .content-ways-to-shop > li {
        flex: 1;
        justify-content: center;
        max-width: 215px;
        flex-basis: 100%;
    }

    .more-ways-to-shop .content-ways-to-shop > li:not(:first-child) {
        margin-left: 30px;
    }

    .more-ways-to-shop .content-ways-to-shop > li > a {
        display: block;
    }


    .more-ways-to-shop .content-ways-to-shop > li > a .anchor-text {
        margin-left: 0;
    }

.more-ways-to-shop .anchor-icon ~ .anchor-text {
        margin-left: 0;
    }
}

/* tablet only */
@media (min-width: 768px) and (max-width: 991.98px) {
    .hero-banner .banner-text {
        padding-right: 10px;
    }

    .hero-banner .banner-image + .banner-text {
        padding-right: 0;
    }

    .sub-banner .banner-image > img {
        position: absolute;
        height: 89%;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
    }

    .product-card:not(.tablet-change) .card-cta-block .card-price {
        flex-direction: row;
    }

    .product-card:not(.tablet-change) .card-cta-block .big-price {
        margin-right: 40px;
    }

    .product-card:not(.tablet-change) .card-cta-block .small-text {
        margin-top: 5px;
    }

    .huge-offer-details > div {
        padding-top: 45px;
        padding-bottom: 45px;
    }

    .more-ways-to-shop .container {
        padding-top: 45px;
        padding-bottom: 45px;
    }

    .more-ways-to-shop h2 {
        margin-bottom: 30px;
    }

    .hero-banner-responsive .image-banner {
        height: 385px;
    }

    .hero-banner-4 img {
        height: 210px;
    }

    .hero-banner-8 {
        height: 300px;
    }

    .scrollableContainerShadow-table.shadow-xs:after {
        background: none;
    }
}

/* mobile and tablet only */
@media (max-width: 991.98px) {
    .column-spacer-sm-30 {
        margin: 0 -15px;
    }

    .huge-offer-details img {
        width: 297px;
    }

    .hero-banner-8 .image-banner{
        max-width: 610px;
    }

    .hero-banner-9 {
        max-height: 440px;
    }

    .hero-banner-9 .image-banner {
        max-width: 410px;
    }

    .left.scrollableContainerShadow-table:before {
        width: 46px;
        -webkit-transition: width .5s;
        transition: width .5s;
    }

    .scrollableContainerShadow-table:before {
        width: 0;
        pointer-events: none;
        content: "";
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        z-index: 1;
        background: -moz-linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
        background: -webkit-linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
        background: linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
        -webkit-transition: width .1s;
        transition: width .1s;
    }

    .right.scrollableContainerShadow-table:after {
        width: 46px;
        -webkit-transition: width .5s;
        transition: width .5s;
    }

    .scrollableContainerShadow-table:after {
        width: 0;
        pointer-events: none;
        content: "";
        position: absolute;
        top: 0;
        bottom: 0;
        right: 0;
        z-index: 1;
        background: -moz-linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
        background: -webkit-linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
        background: linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
        -webkit-transition: width .1s;
        transition: width .1s;
    }
}

/* desktop and larger */
@media (min-width: 992px) {

    .partial-collapse {
        min-height: 170px;
        height: 170px;
    }

    .hero-banner .banner-text {
        padding-bottom: 60px;
        padding-left: 44px;
        padding-top: 60px;
    }

    .hero-banner .banner-image {
        padding-right: 44px;
    }

    .hero-banner.even-split .banner-image {
        padding-bottom: 60px;
        padding-top: 60px;
    }

    .hero-banner.long-copy .banner-image > img {
        top: 153px;
    }

    .hero-banner.even-split.product-banner-2 .banner-image {
        /*padding-top: 75px;*/
    }

        .hero-banner.even-split.product-banner-2 .banner-image img {
            height: 271px;
        }

    .sub-banner .banner-text {
        padding-top: 60px;
    }

    .sub-banner .banner-image > img {
        position: absolute;
        height: 89%;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
    }

        .sub-banner .banner-image > img.lazy-loading {
            position: absolute;
            height: auto;
            width: auto;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

    .product-card {
        flex-direction: row;
    }

        .product-card.tablet-change .card-details,
        .product-card:not(.tablet-change) .card-details {
            padding-bottom: 30px;
        }

        .product-card.tablet-change .card-cta-block,
        .product-card:not(.tablet-change) .card-cta-block {
            border-radius: 0 10px 10px 0;
            flex-direction: column;
        }

            .product-card.tablet-change .card-cta-block .card-price,
            .product-card:not(.tablet-change) .card-cta-block .card-price {
                padding-bottom: 35px;
            }

    .graphic-product-card {
        margin-top: 135px;
    }

        .graphic-product-card .card-heading {
            padding-top: 95px;
        }

            .graphic-product-card .card-heading img {
                height: 230px;
                top: -135px;
            }

    .secondary-offer.no-box:first-child {
        padding-left: 35px;
        padding-right: 45px;
    }

    .secondary-offer.no-box:last-child {
        padding-left: 45px;
        padding-right: 35px;
    }

    .huge-offer-details > div {
        padding-top: 60px;
        padding-bottom: 60px;
    }

    .huge-offer-details .title {
        margin-top: 20px;
    }

    .huge-offer-details > div > div {
        flex: auto;
        padding-left: 15px;
        padding-right: 15px;
        margin-top: 45px;
    }

    .huge-offer-details .offer-price-col .complementary-text {
        display: block;
    }

    .additional-details .accordion-wrap {
        padding-left: 15px;
        padding-right: 15px;
    }

    .more-ways-to-shop .container {
        padding-top: 60px;
        padding-bottom: 60px;
    }

    .more-ways-to-shop h2 {
        margin-bottom: 45px;
    }

    .more-ways-to-shop .anchor-text {
        font-size: 18px;
        line-height: 22px;
    }
}

/* extra large desktop and larger */
@media (min-width: 1440px) {
    .hero-banner .banner-text {
        margin-right: 60px;
    }
}



/* END component styles and overrides */

/* DO NOT merge the temporary styles below this comment with the ones above */

/* START the header/nav and footer were not completely rewritten yet but several styles were added to make them look as close to the mockups as possible. these can be removed once those have been updated already */

footer .btn-site-feedback.btn-primary:not(:disabled):not(.disabled):focus {
    background-color: #ccd7e4;
}

/* mobile and tablet */
@media (max-width: 991.98px) {
    .global-navigation .aliant-home .bellSlimSemibold-Nav {
        font-size: 14px;
    }

    .sub-nav-header {
        color: #fff;
        font-size: 12px;
        padding: 12px 50px 12px 30px;
        margin: 0;
    }
}

/* desktop and larger */
@media (min-width: 992px) {
    .global-navigation .connector .connector-brand.aliant a {
        font-size: 26px;
        top: 0;
    }

    .global-navigation .connector .menu-flyout-visible .sub-nav-group.has-two-columns {
        width: 180%;
    }

    .nav-links-two-columns {
        column-count: 2;
        column-gap: 30px;
        column-fill: auto;
    }

        .nav-links-two-columns li {
            display: inline-block;
            vertical-align: top;
        }

    .global-navigation .connector .menu-flyout-visible .sub-nav-item .sub-nav-level4.nav-links-two-columns {
        padding-right: 30px;
    }

        .global-navigation .connector .menu-flyout-visible .sub-nav-item .sub-nav-level4.nav-links-two-columns li > a {
            padding-right: 20px;
        }

    .global-navigation .connector .menu-flyout-visible .sub-nav-group.sub-nav-large {
        width: 320%;
    }

    .global-navigation .connector .menu-flyout-visible .menu-flyout-root {
        min-height: 300px;
    }

    .sub-nav-item {
        overflow-y: hidden;
    }
}

/* END header and/or footer styles */

/* START helper classes used by header and/or footer only. these can be removed once those have been updated already */

.margin-top-n8 {
    margin-top: -8px;
}

.borderRadiusAll20 {
    border-radius: 20px;
}

.txtSize34 {
    font-size: 34px;
}

.line-height-14 {
    line-height: 14px;
}

/*modal override*/

.modal-open .modal{
    overflow-y: auto;
}

@media (min-width: 768px) {
    .modal-dialog {
        max-height: unset;
        max-height: none;
        margin-top: 30px;
        margin-bottom: 30px;
    }
    /*Important is needed since brf has js code that added an inline max-height in the modal-content*/
    .modal .modal-body:not(.allViewScroll) {
        max-height: unset !important;
        max-height: none !important;
        overflow: visible !important;
    }

    .modal.show .modal-dialog.modal-dialog-fullscreen{
        top: 0;
        transform: none;

    }

    .modal-tooltip .modal-dialog{
        overflow: visible;
    }
}


/*Overriding modal style since tooltip needs to be flow outside the modal*/
.modal-tooltip .modal-dialog .modal-content{border-radius:10px}
.modal-tooltip .modal-dialog .modal-header{border-radius: 10px 10px 0 0}

/*modal override*/

/* mobile only */
@media (max-width: 767.98px) {
    .btn-smaller-xs {
        padding: 7px 35px !important;
        line-height: 17px;
    }

    .hero-banner img, .hero-banner-10 .image-banner {
        max-width: 375px;
    }

    .hero-banner-2 .banner-image, .hero-banner-5 .banner-image {
        height: 220px;
        min-height: 220px;
    }

    .hero-banner-2 img, .hero-banner-5 img {
        height: 100%;
    }

    .hero-banner-4 img {
        height: 180px;
    }


    .position-r-5-xs {
        left: auto;
        transform: none;
        right: 5px;
    }
}

/* tablet and larger */
@media (min-width: 768px) {
    .btn-small-sm-up {
        padding: 7px 28px !important;
        line-height: 18px;
    }
}

/* END helper classes used by header and/or footer only */


/**/
.sticky-element.scrollTrue {
    padding-bottom: 17px;
}
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .sticky-element.scrollTrue {
        padding-bottom: 8px;
    }
}
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .sticky-element.scrollTrue{
        padding-bottom: 17px;
    }
}
@-moz-document url-prefix() {
    .sticky-element.scrollTrue {
        padding-bottom: 17px;
    }
}

@supports (-ms-ime-align:auto) {
    .sticky-element.scrollTrue {
        padding-bottom: 17px;
    }
}

/**/