﻿/*last updated 02.8.2019*/

.icon.icon2:before{top:-7px}
.iconContainer4 .icon2:before{top:-2px}
.top-minus-13.icon2:before{top:-13px}

/* Text box number format */
input[type='number']{-moz-appearance:textfield}
input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button{-webkit-appearance:none;-o-appearance:none;-ms-appearance:none;appearance:none;margin:0}

/*custom*/
.new-form-control{color:#555;font-size:14px}
.new-form-field .maskUnMaskPwsBtn{right:12px;top:12px;border:medium none;background:#555;width:auto;height:24px;color:#FFF;border-radius:5px;font-size:12px;font-weight:700}
.new-form-field .passwordMaskUnmask{padding-right:75px}
.passwordMaskUnmask[type="text"] ~ button[button-show="true"], .passwordMaskUnmask[type="password"] ~ button[button-show="false"] {
    display: none;
}

.passwordMaskUnmask[type="password"] ~ button[button-show="true"], .passwordMaskUnmask[type="text"] ~ button[button-show="false"] {
    display: block;
}

.modal-contentv2 {
    background: transparent
}

.modal-contentv2 .modal-header-gray{height:70px}

.passwordValidate > li {
    list-style-image: url(../img/bulletCheck.gif);
}

.passwordValidate ul > li {
    list-style-image: none;
    list-style-type: disc;
    font-size: 30px;
    line-height: 14px;
    color: #e8e8e8;
}

.sbm-order-details_wrapper ul.flex-orderDetails li {
    flex: 1 1 auto;
    position: relative;
}

.flex-orderDetails{flex-flow:row wrap}

.order-item + .order-item{border-left:solid 1px #d4d4d4;margin-left:30px;padding-left:30px}

.sbm-order-details_wrapper ul.flex-orderDetails li > p {
    margin-left: 0;
}

/*captcha placehodler*/
img.captcha-bitmap{height:66.21px;width:241.63px}
.simple-footer{display:-webkit-box;display:-moz-box;display:-ms-flexbox;display:-webkit-flex;display:flex;flex-flow:wrap}
.captcCont{text-align:right;padding-right:10px}

/*errors*/
.alert-list a.txtRed:hover,.alert-list a.txtRed:focus{color:#bd2025}
  ul.neg-marginLeft-4 {
    margin-left: -4px;
    }
  ul.neg-marginLeft-2 {
    margin-left: -2px;
    }
/*text link on edit*/
    .d-inline-flex .txtOnlyDecoration_hover:hover {
        text-decoration: underline;
    }

 /* mobile only */
@media (max-width:767.98px) {
    /*containers*/
    .panel-wrapper{padding:0}


   .txtLeft-xs{text-align:left}
   .txtBold-xs{font-weight:700}

    /*alignment mobile review*/
    /*.pad-left-right-4 { padding-left: 4px; padding-right: 4px;}*/

    /*borders*/
    .no-border-xs{border-radius:0;border:none}
    .border-top-bottom-xs{border-left:none;border-right:none;border-top:1px solid #D4D4D4;border-bottom:1px solid #D4D4D4}
    .border-d4-top-xs{border-left:none;border-right:none;border-top:1px solid #D4D4D4;border-bottom:none}
    .border-d4-bottom-xs{border-left:none;border-right:none;border-top:none;border-bottom:1px solid #D4D4D4}
    .no-border-bottom-xs{border-bottom:0;border-bottom-left-radius:0;border-bottom-right-radius:0}

    .line-height-22-xs{line-height:22px}
    .txtSize30.line-height-22-xs{margin-left:3px;}

    /*custom helpers*/
    .modal-contentv2 .modal-header-gray{height:74px}

    .top-minus-10.icon2:before{top:-10px}

    .focusGrey:hover,.focusGrey:focus{color:#a1a5a6;text-decoration:none}

    /*mobile design for site link list wrapper*/
    .simple-footer .site_links_list_wrapper.m_columnlist li{display:block;text-align:center;padding-bottom:5px}
    .simple-footer .site_links_list_wrapper.m_columnlist ul.site_links_list_cont li > a{margin-right:0}
    .simple-footer .site_links_list_wrapper.m_columnlist ul.site_links_list_cont li > a:after{display:none}
    .captcCont{text-align:center;padding-right:0}

    /*layout styles*/
    .flex-column-reverse-xs {
        flex-direction: column-reverse;
    }
    .form-text-size-postal-code {
         padding-right: 10px;
    }
}
/* mobile and tablet */
@media (max-width: 991.98px) {
}
/*Tablet only*/
@media (min-width: 768px) and (max-width: 991.98px) {
    /*containers*/
    .panel-wrapper{padding:30px}
    /*.panel-wrapper{padding-top:30px;padding-bottom:30px}*/
    .msg-container{width:95%}
    .width-96-sm {width: 96px;}
    .width-120-sm {width: 120px;}
    .width-206-sm {width: 206px;}
    .width-324-sm {	width: 324px;}
}

/*Desktop & tablet only*/
@media (min-width: 768px) {
    .form-text-size-postal-code{flex:0 0 19%;max-width:19%;min-width:150px}
}

/*Desktop only*/
@media (min-width: 992px) {
    .msg-container{width:80%}
    .simple-footer{justify-content:space-between}
    .width-96-md {width: 96px;}
    .width-126-md {width: 126px;}
    .width-280-md {width: 280px;}
    .width-203-md {width: 203px;}
    .width-358-md {width: 358px;}
}
