import { IFetchBillsResPayload, IFetchBillsState } from "../models";

export const filterFetchBillsResponse = (response: IFetchBillsResPayload): IFetchBillsState => {
    return {
        pbeCategory: response.pbeCategory,
        description: response.description,
        billCloseDate: response.billCloseDate,
        startDate: response.startDate,
        endDate: response.endDate,
        subscriberDetails: response.subscriberDetails,
        chargeItems: response.chargeItems,
        currentPeriodEndDate: response.currentPeriodEndDate,
        currentPeriodStartDate: response.currentPeriodStartDate,
        descriptionKey: response.descriptionKey,
        detailedDescKey: response.detailedDescKey,
        previousPeriodEndDate: response.previousPeriodEndDate,
        previousPeriodStartDate: response.previousPeriodStartDate,
        titleKey: response.titleKey,
        transactions: response.transactions,
        useLegendsForDiagram: response.useLegendsForDiagram
    };
};