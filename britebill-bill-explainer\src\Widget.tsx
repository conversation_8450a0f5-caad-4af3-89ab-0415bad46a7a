import * as React from "react";
import { Root } from "react-dom/client";
import { Provider } from "react-redux";
import { ViewWidget, Widget, LoggerConfigKeys, ParamsProvider } from "bwtk";
import { fetchBillsAction, Store } from "./store";
import { App } from "./App";
import { Config } from "./Config";

/**
 * The Main Component
 * It is responsible for the lifecycle of the widget,
 * containing plain javascript/typescript functions that are passed down to other components as properties,
 * initiating any API calls (including CMS), and rendering the App Component.
 * The majority of interfacing with the BWTK will be done at this level.
 */
@Widget
export default class BriteBillExplainerComponent extends ViewWidget {
  /**
   * @constructor
   * @param {Store} store Widget's Redux store
   * @param {Config} config Widget's Config class
   */
  constructor(private store: Store, private config: Config, private widgetParams: ParamsProvider<any, any>) {
    super();
  }

  /**
   * @method init
   * This method is executed before widget's onLoad callback is called (if provided),
   * and before the widget is fully rendered into DOM.
   * At this stage the widget object is already created (fetched and evaluated).
   * All global stores and bwtk functionaly are available here.
   */
  init() {
    this.config.setConfig(LoggerConfigKeys.SeverityLevel, this.config.logLevel);
    if (!this?.widgetParams?.props?.loadedFromOtherWidget) {
      this.store.dispatch(fetchBillsAction({}));
    }
  }



  /**
   * @method destroy
   * The callback is executed by BWTK when the widget is being destroyed.
   * Clean up any custom event listeners, timeouts, etc.
   * At the very least, destroy the Redux store that was created during widget setup.
   */
  destroy() {
    this.store.destroy();
  }

  /**
   * @method render
   * Render the widget into DOM
   *
   * @param {Root} root DOM node into which the widget is being rendered
   */
  render(root: Root) {
    const { store } = this;
    root.render(
      <Provider {...{ store }}>
        <App configLinks={this.config.links} isUXPMode={this.config.isUXPMode} loadedFromOtherWidget={this.widgetParams.props.loadedFromOtherWidget} pbeData={this.widgetParams.props.pbe} isPbeModalLinkDisabled={this.config.isPbeModalLinkDisabled} />
      </Provider>
    );
  }
}
