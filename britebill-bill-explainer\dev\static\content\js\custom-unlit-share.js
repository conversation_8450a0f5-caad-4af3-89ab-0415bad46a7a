// custom-unlit-share

  
function scrollableTable () {
    $('[class^=scrollableContainer]').on('scroll', function () {
        var $this = $(this);
        var scrollPos = $this.scrollLeft();
        var width = $this.width();
        var scrollWidth = $this.get(0).scrollWidth;
        var container = $this.closest('[class^=scrollableContainerShadow]');

        if (scrollPos === 0) {
            container.removeClass('left');
        } else {
            container.addClass('left');
        }

        if (scrollPos + width === scrollWidth) {
            container.removeClass('right');
        } else {
            container.addClass('right');
        }
    });
};


//This script is for the new copy of existing accordion that is accessible. 
//Accordion expand/collapse
function accessibleAccordionToggle() {
    $('.accordion-accessible-toggle').click(function (e) {
        var $this = $(this),
            $toggleIcon = $this.find('span.icon, span.icon2, span.icon3, i, span.icon-uev').first(),
            $expandedAttr = $this.attr('aria-expanded') === "true" ? "false" : "true",
            iconExpand = $this.data('icon-expand') || "icon-expand-bold",
            iconCollapse = $this.data('icon-collapse') || "icon-collapse-bold",
            newIconClass = $this.attr('aria-expanded') === "true" ? iconExpand : iconCollapse;

        $this.parent().closest(".accordion-wrap").find('.collapse-accordion-accessible-toggle').slideToggle();
        $toggleIcon.removeClass(iconExpand + ' ' + iconCollapse);

        $toggleIcon.addClass(newIconClass);
        $this.attr('aria-expanded', $expandedAttr);
    })
};

function addShadowModalFooter() {

    var modalFooter = $(".modal-body-scrollable .modal-footer");

    if (modalFooter.closest('.modal-body-scrollable')) {
        $(this).addClass('top-shadow-scroll');
    }
};

//---END


var KEYS = {
    space: 32,
    enter: 13,
    left: 37,
    right: 39,
    up: 38,
    down: 40,
    home: 36,
    end: 35,
    esc: 27
}, ActualTabs;

// START Tab Control (tabs DO NOT cause page redirect)
ActualTabs = {
    options: {
        tabSelector: '.actual-tabs-controller-js[role=tablist] [role=tab]'
    },
    init: function (config) {
        var extendedOptions = $.extend(this.options, config),
            $tabs = $(extendedOptions.tabSelector),
            $tabList = $tabs.first().parent().closest('[role=tablist]');

        $tabList.data('actualtabs-options', JSON.stringify(extendedOptions));

        this.initTabEvents($tabs);
    }, initTabEvents: function (tabs) {
        var $tabs = tabs ? $(tabs) : $(tabs.tabSelector);

        // toggle attributes and class when a tab is clicked
        $tabs.on('click', this._tabClickListener);

        // automatic tabs automatically change tab when arrow keys are pressed. consider supporting manual tabs in the future if necessary
        $tabs.on('keydown', this._tabKeydownListener);
    }, cleanTabEvents: function (tabs) {
        var $tabs = tabs ? $(tabs) : $(tabs.tabSelector);

        $tabs.off('click', this._tabClickListener);
        $tabs.off('keydown', this._tabKeydownListener);
    }, reinit: function (tabs) {
        var $tabs = $(tabs);

        this.cleanTabEvents($tabs);
        this.initTabEvents($tabs);
    }, _tabClickListener: function () {
        var clickedTab = $(this),
            tabList = clickedTab.parent().closest('.actual-tabs-controller-js'),
            tabs,
            scrollTop,
            tabPanelContainer,
            tabPanels,
            i,
            len;

        if (tabList.hasClass('manual-tabs-js')) {
            // support manual activation in the future if necessary
        } else {
            // toggle attribute and class
            tabs = tabList.find('[role=tab]')
            tabs.attr({
                'aria-selected': 'false',
                'tabindex': '-1'
            }).removeClass('active');
            clickedTab.attr({
                'aria-selected': 'true',
                'tabindex': '0'
            }).addClass('active').filter('a').removeAttr('tabindex');

            // scroll into view horizontally
            scrollTop = $(window).scrollTop();
            // Remove the line of code below as it causes flickering issue on IE
            //clickedTab[0].scrollIntoView();
            $(window).scrollTop(scrollTop);

            // set focus if necessary. this is the case if active tab is changed using left/right/home/<USER>
            if (document.activeElement === this || $(document.activeElement).closest('.actual-tabs-controller-js')[0] === tabList[0]) {
                clickedTab.focus();
            }

            // control tab panel switching if necessary. don't do this for carousels by setting data-carousel-tablist=true
            if (tabList.data('carousel-tablist') !== true) {
                tabPanelContainer = $(tabList.data('tab-panels-container'));
                if (tabPanelContainer.length > 0) {
                    tabPanels = tabPanelContainer.find('[role=tabpanel]').filter(function () { return $(this).parent().closest('[role=tabpanel]', tabPanelContainer[0]).length === 0; });

                    for (i = 0, len = tabs.length; i < len; i++) {
                        if (tabs[i] === this) {
                            tabPanels.eq(i).attr({
                                'tabpanel-selected': 'true',
                                'tabindex': 0
                            });
                        } else {
                            tabPanels.eq(i).attr({
                                'tabpanel-selected': 'false',
                                'tabindex': -1
                            });
                        }
                    }
                }
            }
        }
    }, _tabKeydownListener: function (e) {
        var key = e.which || e.keyCode || 0,
            tabList = $(this).parent().closest('.actual-tabs-controller-js'),
            isVertical = tabList.attr('aria-orientation') === 'vertical', // if tabs are in vertical arrangement, aria-orientation=vertical must be set
            tabs = tabList.find('[role=tab]'),
            index = 0,
            len = tabs.length;

        for (; index < len; index++) {
            if (this === tabs[index]) {
                break;
            }
        }

        if (key === KEYS.home) {
            index = 0;
        } else if (key === KEYS.end) {
            index = len - 1;
        } else {
            // left & right is for horizontal tabs. up & down is for vertical tabs
            if (!isVertical && key === KEYS.left || isVertical && key === KEYS.up) {
                if (index === 0) {
                    index = len - 1;
                } else {
                    index--;
                }
            } else if (!isVertical && key === KEYS.right || isVertical && key === KEYS.down) {
                if (index === len - 1) {
                    index = 0;
                } else {
                    index++;
                }
            } else {
                return;
            }
        }

        e.preventDefault();
        e.stopPropagation();
        tabs.eq(index).trigger('click');
    }
};
// END Tab Control (tabs DO NOT cause page redirect)

// END global and constants



// START document-ready
(function ($) {
    // initialize actual tabs
    ActualTabs.init();

    // header-tab-control is only for group of links that looks like tabs but DO NOT function as tabs
    // set scrollLeft of header-tab-control to make sure the active item is visible in case there's overflow, we'll center it if possible to make it easier to see that the area is scrollable
    $('.header-tab-control').each(function () {
        var scrollableEl = $(this),
            activeEl = scrollableEl.find('li a.active, li a[aria-current]:not([aria-current=false])').first(),
            listEl = activeEl.parent().closest('ul');

        if (activeEl.is(':not([aria-current])')) {
            activeEl.attr('aria-current', 'page');
        }

        scrollableEl.scrollLeft(activeEl.offset().left - listEl.offset().left - listEl.outerWidth() / 2 + activeEl.outerWidth() / 2);
    });

    // users should be able to activate "buttons" using the space key. this is for anchor tags so enter key is already supported
    $('a[role=button]:not(.click-on-space)').on('keypress', function (e) {
        if (KEYS.space === (e.which || e.keyCode || 0)) {
            e.preventDefault();
            e.stopPropagation();
            $(this).trigger("click");
        }
    });

    // select elements now use bell-blue color if it has a selected value and the selected option is also in that color    
    $('select.colored-selected').each(function () {
        // if the colored-selected class is found, automatically set the other class and attributes
        var el = $(this),
            options = el.find('option'),
            selected = options.eq(this.selectedIndex);

        options.not('[selected]').removeClass('selected');
        selected.addClass('selected');

        options.filter('[value=""]').addClass('no-value');

        if (selected.hasClass('no-value')) {
            el.removeClass('has-selected');
        } else {
            el.addClass('has-selected');
        }
    }).on('change', function () {
        // add a change event listener to toggle the classes and attributes accordingly
        var el = $(this),
            selected = el.find('option').eq(this.selectedIndex);

        el.find('option.selected').removeClass('selected').removeAttr('selected');
        if (selected.hasClass('no-value')) {
            el.removeClass('has-selected');
        } else {
            el.addClass('has-selected');
        }
        selected.addClass('selected').attr('selected', 'selected');
        });

})(jQuery);
// END document-ready



$(document).ready(function () {
    scrollableTable();
    accessibleAccordionToggle();
    addShadowModalFooter();

    $('[data-toggle="tooltip"]').each(function () {
        var jQueryEl = $(this),
            title = jQueryEl.attr("title"),
            placementPosition = 'top',
            template,
            data,
            options;

        jQueryEl.on('mouseover', function () {
            if (title == null || title == "") {
                title = jQueryEl.attr("data-original-title");
            }

            if (title != null && title != "") {
                template = '<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner large" tabindex="0"></div></div>';
                data = jQueryEl.data('bs.tooltip');
                let originalPlacement = jQueryEl.attr('data-original-placement');
                if (originalPlacement === undefined) {
                    jQueryEl.attr('data-original-placement', data.config.placement);
                }

                if ((jQueryEl.offset().top - $(window).scrollTop()) < 350) {
                    placementPosition = 'bottom';
                } else {
                    placementPosition = (originalPlacement !== 'bottom' && originalPlacement !== 'top') ? 'top' : originalPlacement;
                }

                if (!data) {
                    // if not initialized, override the placement and template data-attributes. note that old values, if any, will become obsolete
                    jQueryEl.attr('data-placement', placementPosition);
                    jQueryEl.attr('data-template', template);
                } else {
                    // if initialized, let's get the old set of options/config then add/override the placement and template values
                    options = data.config;
                    options.placement = placementPosition;

                    if (title.length > 500) {
                        options.template = template;
                    }

                    jQueryEl.tooltip(options);
                }
            }
        });
    });
});

function fnRadioBtnLabel(el){

    var el = el;
    var el_id = $(el).attr("id");
    var labels =  $(".rateplan-bill-radio-cell .table-label label span");

    //removing bold and text blue style
   $(labels).removeClass("darkBlue3").removeClass("txtBold");

   //adding bgBlueDark and txtBold class to the radio button label
   $(".rateplan-bill-radio-cell >.table-label > label[for="+el_id+"] span").addClass("darkBlue3 txtBold")

}

tooltip_modal = {
    init: function() {
        let mdl = $('.modal-body').first(); // FIXME: should account for multiple hidden modals if any
        const mdlClone = mdl.clone(true,true);
        let oldContent = mdl.html();
        
        let backLink = backLink_createElement();
        
        function tooltip_init() {
            mdl = $('.modal-body');
            let tooltips = mdl.find('.modal-tooltip')
            tooltips.each(function () {
                let tooltip = tooltip_createElement(this);
                tooltip_attachClick(tooltip);
            });
        }

        function tooltip_createElement(oldTooltip) {
            const tooltip = $(oldTooltip).clone();
            $(oldTooltip).addClass('hidden-xs');
            tooltip.addClass('visible-xs');
            tooltip.attr('tabindex', '0');
            tooltip.attr('data-trigger', 'click');
            $(oldTooltip).parent().append(tooltip);

            return tooltip;
        }

        function tooltip_attachClick(tooltip) {
            tooltip.on('click', function(){
                let content = tooltip_generateContent(tooltip);
                backLink_attachClick(backLink);
                mdl.html(backLink);
                mdl.append($('<div class="spacer15"></div>'))
                mdl.append(content)
                mdl.append($('<div class="spacer15"></div>'))
            });

            tooltip.on('keyup', function() {
                tooltip.click();
            });
        }

        function tooltip_generateContent(tooltip) {
            let content = tooltip.attr('data-original-title');
            return content;
        }

        function backLink_createElement() {
            let backLink = $('<div class="middle-align-self">\
                <div class="txtBold">\
                    <a href="#" class="responsive-simplified-header-back txtNoUnderline">\
                        <i class="icon icon-chevron-left txtSize16 inlineBlock" aria-label="back arrow"></i>\
                        <span class="txtSize14 line-height-18 inlineBlock">Back</span>\
                    </a>\
                </div>\
            </div>');

            return backLink;
        }

        function backLink_attachClick(backLink) {
            let link = backLink.find('a');
            link.on('click', function(){
                mdl.html(oldContent);
                mdl.replaceWith(mdlClone);
                tooltip_init();
                tooltip_modal.init_responsive();
            });
        }

        tooltip_init();
    },

    init_responsive: function () {
        let svgX = $('.svg-fo-responsive, .svg-txt-responsive');
        let svgPoints = $('.svg-poly-responsive');
        let svgPath = $('.svg-path-responsive');

        if (breakpoint_xs().matches) {
            svgX.each(function () {
                svg_updateCoor($(this), true);
            });
            svgPoints.each(function () {
                svg_updatePoints($(this), true);
            });

            svgPath.each(function () {
                svg_updatePath($(this), true);
            });
        } else {
            svgX.each(function () {
                svg_updateCoor($(this), false);
            });
            svgPoints.each(function () {
                svg_updatePoints($(this), false);
            });

            svgPath.each(function () {
                svg_updatePath($(this), false);
            });
        }

        function svg_updatePath(element, isMobile) {
            let newD = (isMobile === false) ? svg_getLgD(element) : svg_getXsD(element);
            if (newD !== undefined) element.attr("d", newD);
        }

        function svg_getLgD(element) { return element.attr('lg-d'); }
        function svg_getXsD(element) { return element.attr('xs-d'); }

        function svg_updatePoints(element, isMobile) {
            let newPoints = (isMobile === false) ? svg_getLgPts(element) : svg_getXsPts(element);
            if (newPoints !== undefined) element.attr("points", newPoints);
        }

        function svg_getLgPts(element) { return element.attr('lg-pts'); }
        function svg_getXsPts(element) { return element.attr('xs-pts'); }

        function svg_updateCoor(element, isMobile) {
            let newX = (isMobile === false) ? svg_getLgX(element) : svg_getXsX(element);
            let newY = (isMobile === false) ? svg_getLgY(element) : svg_getXsY(element);

            if (newX !== undefined) element.attr('x', newX);
            if (newY !== undefined) element.attr('y', newY);
        }

        function svg_getLgX(element) { return element.attr('lg-x'); }
        function svg_getXsX(element) { return element.attr('xs-x'); }

        function svg_getLgY(element) { return element.attr('lg-y'); }
        function svg_getXsY(element) { return element.attr('xs-y'); }

        function breakpoint_xs() {
            return window.matchMedia("(max-width: 767px)");
        }
    }
}

tooltip_modal.init_responsive();

window.onresize = function () {
    tooltip_modal.init_responsive();
}

$.fn.createProgressbarTooltip = function(text) {
    let parent = $(this);
    let tooltip = $('.template-progresstooltip').clone();
    tooltip.removeClass('template template-progresstooltip');
    tooltip.find('.txtContent').text(text);
    parent.append(tooltip);
}

function createProgressBar(element, val, tooltip) {
    let bar = $(element).find('.ui-progressbar-value');
    bar.css('display', 'inline-flex');
    bar.css('justify-content', 'center');
    bar.css('align-items', 'center');
    bar.html(val + ' GB');

    let icon = $(element).find('.modal-tooltip');
    bar.append(icon);

    bar.createProgressbarTooltip(tooltip);
}

/**
 * Put parent modal into backdrop, Used when we put modal on top of another modal
 * @param {Element} instigator modal
 * @param {Element} parent modal
 */
function fadeParentModal(instigator, parent) {
    let oldIndex = $(parent).css('z-index');
    $(instigator).on('show.bs.modal', function (e) {
        $(parent).css('z-index', 1);
    });
    $(instigator).on('hide.bs.modal', function (e) {
        $(parent).css('z-index', oldIndex);
    })
}