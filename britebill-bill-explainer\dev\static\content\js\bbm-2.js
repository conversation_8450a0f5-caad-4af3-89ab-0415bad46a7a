var Resized = {
    init: function () {
        var that = this;

        if ($('.inpage-navigation .tablist').length > 0) {
            that.resizeInpageNavigation();
        }
    },

    onResizedOnly: function () {
        CardSlider.resizeCardSlider();
    },

    resizeInpageNavigation: function () {
        var $inpagenavChildren = $('.inpage-navigation .tablist a');
        var totalWidth = 40;

        $inpagenavChildren.each(function (key, value) {
            totalWidth = totalWidth + value.offsetWidth;
        });

        $('.inpage-navigation > div').removeAttr('style');
        if (totalWidth > $('.inpage-navigation .tablist')[0].offsetWidth) {
            $('.inpage-navigation > div').css('width', totalWidth + 'px');
        }
    }
}

var Scrolled = {
    init: function () {
        var that = this;

        that.scrollableContainer();
    },

    scrollableContainer: function () {
        $('[class^=scrollableContainer]').on('scroll', function () {
            var $this = $(this);
            var scrollPos = $this.scrollLeft();
            var width = $this.width();
            var scrollWidth = $this.get(0).scrollWidth;
            var container = $this.closest('[class^=scrollableContainerShadow]');

            if (scrollPos === 0) {
                container.removeClass('left');
            } else {
                container.addClass('left');
            }

            if ((scrollPos + width) === scrollWidth) {
                container.removeClass('right');
            } else {
                container.addClass('right');
            }
        });
    }
}

var Initialized = {
    run: function () {
        CardSlider.initializeCardSlider('.slidingCardsContainer', 3);
        CardSlider.resizeCardSlider();
        CardSlider.improvedCardSliderAccessibility();
    }
}

var CardSlider = {
    initializeCardSlider: function (target, slideInitialViewValue) {

        var that = this;
        var slickSlideCount = $(target).find('.slickSlide').length;

        if ($(target).length > 0) {
            var fnRemoveDotLabels = function (target) {
                var targetEl = target;

                setTimeout(function () {
                    $(targetEl).find('.slick-arrow button').removeAttr('aria-label');
                }, 0);
            };

            var fnAddAriaAccessibilitybyToSlide = function (target, slideView) {
                var targetEl = target,
                    slideCount = $(targetEl).find('.slick-track .slick-slide').length,
                    slideDotsCount = $(targetEl).find('.slick-dots li').length;

                var dotIdArray = [];
                var slideIdArray = [];

                $slider = $(targetEl);
                $(targetEl).find('.slick-track .slick-slide').each(function () {
                    var slide = $(this),
                        slideIndex = slide.index() + 1;

                    $(targetEl).find('.slick-dots li').each(function () {
                        $(this).find('button').removeAttr('aria-label');

                        var dotIndex = $(this).index() + 1;
                        var dotId = $(this).find('button').attr('id');
                        var slideId = slide.attr('id');

                        if (slideIndex > slideView) {
                            if (dotIndex > 1 && ($.inArray(dotId, dotIdArray) === -1)) {
                                slide.attr('aria-describedby', dotId);
                                $(this).find('button').attr('aria-controls', slideId);
                                dotIdArray.push(dotId);
                                return false;
                            }
                        } else {
                            if (dotIndex === 1) {
                                slideIdArray.push(slideId);

                                if (slideIndex === slideView) {
                                    var ariaControlVal = '';
                                    for (var i = 0; i < slideIdArray.length; i++) {
                                        ariaControlVal = ariaControlVal + ' ' + slideIdArray[i];
                                    }
                                    $(this).find('button').attr('aria-controls', ariaControlVal);
                                }

                                slide.attr('aria-describedby', dotId);
                                return false;
                            }
                        }
                    });

                });

            };

            var setMargin = function (target) {
                var slick = $(target);
                var slickTrack = slick.find('.slick-track');
                slickTrack.removeClass('margin-n15-left').removeClass('margin-15-left');
                if (window.matchMedia('(max-width: 767px)').matches) {
                    if (slick.find('.slick-track > div:first-child').attr('tabIndex') === undefined) {
                        slickTrack.addClass('margin-n15-left');
                    } else if (slick.find('.slick-track > div:last-child').attr('tabIndex') === undefined) {
                        slickTrack.addClass('margin-15-left');
                    }
                }
            };

            var fnCheckBreakPoint = function (target, slideView) {
                setTimeout(function () {
                    if (that.getWindowWidth() > 991) {
                        fnAddAriaAccessibilitybyToSlide(target, 3);
                    } else if (that.getWindowWidth() > 767) {
                        fnAddAriaAccessibilitybyToSlide(target, 2);
                    } else {
                        fnAddAriaAccessibilitybyToSlide(target, 1);
                    }
                    setMargin(target);
                }, 0);
            };

            $(target).each(function () {
                var $this = $(this);
                var slickSlideCount = $this.find('.slickSlide').length;
                var itemOnDesktop = parseInt($this.attr('itemOnDesktop')) || 1;
                var itemOnTablet = parseInt($this.attr('itemOnTablet')) || 1;
                var itemOnMobile = parseInt($this.attr('itemOnMobile')) || 1;

                var hasArrowOnTable = $this.attr('tableViewButtons') || true;

                $this.slick({
                    slidesToShow: 3,
                    slidesToScroll: itemOnDesktop,
                    infinite: false,
                    variableWidth: true,
                    dots: true,
                    waitForAnimate: false,
                    speed: 700,
                    responsive: [
                        {
                            breakpoint: 992,
                            settings: {
                                slidesToShow: 2,
                                slidesToScroll: itemOnTablet,
                                variableWidth: true,
                                infinite: false,
                                dots: true,
                                arrows: hasArrowOnTable,
                                focusOnSelect: true
                            }
                        },
                        {
                            breakpoint: 768,
                            settings: {
                                slidesToShow: 1,
                                slidesToScroll: itemOnMobile,
                                infinite: false,
                                dots: true,
                                centerMode: true,
                                centerPadding: '30px 30px 20px 30px',
                                focusOnSelect: true,
                                arrows: false,
                                variableWidth: true
                            }
                        }
                    ]
                }).on('setPosition', function () {
                    CardSlider.resizeCardSlider();
                }).on('beforeChange', function (event, slick, currentSlide, nextSlide) {

                    var focusedEl = $(document.activeElement);
                    var $slickSlider = $(this);

                    $slickSlider.find('.slick-track').removeAttr('aria-live');
                    $slickSlider.find('.slick-slide').removeClass('slider-sr').removeClass('hidden-imp');

                    //if (window.matchMedia("(max-width: 767.98px)").matches && nextSlide != 0) {
                    //    if (!$(slick.$list).hasClass('margin-left-neg-5-xs')) {
                    //        $(slick.$list).addClass('margin-left-neg-5-xs');
                    //    }
                    //}
                    //else {
                    //    $(slick.$list).removeClass('margin-left-neg-5-xs');
                    //}

                    
                    if (window.matchMedia('(max-width: 767px)').matches) {
                        $slickSlider.find('.slick-track').removeClass('margin-n15-left').removeClass('margin-15-left');
                        if (nextSlide == 0) {
                            $slickSlider.find('.slick-track').addClass('margin-n15-left');
                        } else if (nextSlide == (slickSlideCount - 1)) {
                            $slickSlider.find('.slick-track').addClass('margin-15-left');
                        }
                    }

                    if (focusedEl.length > 0 && (focusedEl.attr('id') || "").indexOf('slick-slide-control') > -1) {
                        // focused el was a slick dot so slide was changed using arrow keys. move focus accordingly

                        var slideCount = $slickSlider.find('.slick-slide.slick-active').length;
                        var nSlide = 0;
                        if (nextSlide < currentSlide) {
                            nSlide = (currentSlide - nextSlide) > 1 ? ((currentSlide / slideCount) - 1) : nextSlide;
                        } else {
                            nSlide = (nextSlide - currentSlide) > 1 ? (nextSlide / slideCount) : nextSlide;
                        }

                        $slickSlider.find('.slick-dots li').eq(nSlide).find('button').focus();
                        //fnSwitchDisplayArrow(target);

                    }
                }).on('afterChange', function (event, slick, currentSlide, nextSlide) {
                    var slider = $(this);

                    //Set slick-active and aria-hidden properly when generic-slider
                    var gs = slider.closest('.generic-slider');
                    if (gs.length > 0) {
                        var activeSlide = slider.find('.slick-slide.slick-active');
                        slider.find('.slick-slide').removeClass('slick-active').attr('aria-hidden', "true");
                        var lastSlide = currentSlide + activeSlide.length;
                        for (x = currentSlide; x < lastSlide; x++) {
                            if (slider.find('.slick-slide')[x] != undefined) {
                                //slider.find('.slick-slide')[x].addClass('slick-active').attr('aria-hidden', "false");
                                var slide = slider.find('.slick-slide')[x];
                                slide.className = slide.className + ' slick-active';
                                slide.setAttribute("aria-hidden", "false");
                            }
                        }
                    }



                    //slider.find('.slick-slide').removeAttr('tabindex');

                    var slickDots = slider.find('.slick-dots');

                    if (slickDots.length > 0) {
                        slickDots.find('button').attr('tabindex', '-1');

                        setTimeout(function () {
                            slickDots.find('.slick-active button').attr('tabindex', '0');
                            slickDots.find('li').removeAttr('aria-controls aria-selected id');

                        }, 0);
                    }
                    //slider.find('.slick-slide[aria-hidden="true"]').addClass('hidden-imp');
                    slider.find('.slick-slide[aria-hidden="true"]').addClass('hidden-imp');

                    setTimeout(function () {
                        $slickSlider.find('.slick-slide').attr('role', 'option');
                        slider.find('.slick-track').attr('aria-live', 'polite');
                        slider.find('.slick-slide').addClass('slider-sr');
                        $slickSlider.find('.slick-slide').attr('role', 'tabpanel');
                    }, 300);

                    fnRemoveDotLabels(target);
                    if (typeof slideOmniture === 'function') {
                        slideOmniture($slickSlider);
                    }
                    //fnCheckBreakPoint(target);
                }).on('breakpoint', function () {
                    fnRemoveDotLabels(target);
                    //fnSwitchDisplayArrow(target);
                    fnCheckBreakPoint(target);
                }).on('swipe', function (event, slick, currentSlide, nextSlide) {
                    var $slickSlider = $(this);
                    if (typeof slideOmniture === 'function') {
                        slideOmniture($slickSlider);
                    }
                });
            });



            // initial run
            fnRemoveDotLabels(target);
            //fnSwitchDisplayArrow(target);
            fnCheckBreakPoint(target);


        }

    },

    getWindowWidth: function () {
        var windowWidth = $(window).outerWidth();
        return windowWidth;
    },

    getCardSliderLastIndex: function () {
        $slickSlider = $('.slidingCardsContainer');
        var slideLength = $slickSlider.find('.slick-track .slick-slide:last-child').attr('data-slick-index');
        slideLength = parseInt(slideLength);

        return slideLength;
    },

    getCardSliderLength: function () {
        $slickSlider = $('.slidingCardsContainer');
        var slideLength = $slickSlider.find('.slick-track .slick-slide:last-child').attr('data-slick-index');

        slideLength = parseInt(slideLength) + 1;

        return slideLength;
    },

    getCardSliderMobilePad: function () {
        var mobilePad = $('.slidingCardsSection .container.liquid-container').css('padding-left');
        return parseInt(mobilePad, 10);
    },

    getCardSliderGap: function () {
        $slickSlider = $('.slidingCardsContainer');
        var gap = $slickSlider.find('.slick-slide').css('margin-right');
        return gap = parseInt(gap, 10);
    },

    getCardSliderShowedSpace: function () {
        var showedSpace = 80;
        return showedSpace;
    },

    resizeCardSlider: function () {

        this.resizeCardSliderHeight();

        if (this.getWindowWidth() > 991) {
            this.resizeCardSliderWidthDesktop();
        } else if (this.getWindowWidth() > 767) {
            this.resizeCardSliderWidthTablet();
        } else {
            this.resizeCardSliderWidthMobile();
        }
    },

    resizeCardSliderHeight: function () {
        $slickSlider = $('.slidingCardsContainer');
        $slickSlider.find('.slick-slide').height('auto');

        var slickTrack = $slickSlider.find('.slick-track');
        var slickTrackHeight = $(slickTrack).height();

        //$slickSlider.find('.slick-slide').css('height', slickTrackHeight + 'px');
        $slickSlider.find('.slick-slide').css('height', '');
    },

    resizeCardSliderWidthMobile: function () {
        $slickSlider = $('.slidingCardsContainer');

        $slickSlider.find('.slick-slide').width(this.getWindowWidth() - 60);
    },

    resizeCardSliderWidthTablet: function () {
        $slickSlider = $('.slidingCardsContainer');

        $slickSlider.find('.slick-slide').width(($slickSlider.width() / 2) - (this.getCardSliderGap() / 2) - 5);
    },

    resizeCardSliderWidthDesktop: function () {
        $slickSlider = $('.slidingCardsContainer');
        $slickSlider.find('.slick-slide').width(($slickSlider.width() / 3) - (this.getCardSliderGap() / 2) - 3);
    },

    improvedCardSliderAccessibility: function () {

        $slickSlider = $('.slidingCardsContainer');
        $slickSlider.removeAttr('role');
        var slickTrack = $slickSlider.find('.slick-track');
        if (slickTrack.length > 0) {
            var newId = 'slider-' + (new Date()).getTime();
            slickTrack.attr('id', newId);

            $slickSlider.find('.slick-prev, .slick-next').attr('aria-controls', newId);
            $slickSlider.find('.slick-track').attr('aria-live', 'polite');


            //$slickSlider.find('.slick-track').attr('role', 'listbox');
            //$slickSlider.find('.slick-slide').attr('role', 'option');
            $slickSlider.find('.slick-slide[aria-hidden="true"]').addClass('hidden-imp');
        }
    }
}

// Side Nav Start
var FloatingSubNav = {
    isMobile: false,
    activeSubNavIndex: 1,
    magicLine: '#magic-line',
    init: function (windowScroll) {

        this.addMagicLine('.subnavgroup');

        // Check if will desktop or not then if desktop apply the floating scroll function
        if (FloatingSubNav.checkIfNotMobile() === false) {
            FloatingSubNav.checkScrollPos(windowScroll);
            $('.subnav-scroll').removeClass('d-none');
            FloatingSubNav.checkScrollableActiveSection(windowScroll);
        } else {
            $('.subnav-scroll').removeClass('d-none');
            FloatingSubNav.positionToRelative('.subnav-scroll');
        }

        FloatingSubNav.bindEvents();

    },

    bindEvents: function () {
        var that = this;

        $('.subnavgroup li a').on('click', function () {
            var sectionToScroll = '#' + $(this).parent().attr('data-section');
            if (typeof scrollOmnitureBegin === 'function') {
                scrollOmnitureBegin();
            }
            that.animateToSection(sectionToScroll);
        });
    },

    addMagicLine: function (element) {
        $(element).append("<li class='listStyleNone' id='magic-line'></li>");

    },

    checkScrollPos: function (scrollPos) {
        // Define variables
        var spaceBefore = 45; // space before scrollable area
        //var spaceAfter = 50; // space after scrollable area
        var scrollableArea = '.scrollable-area';
        var elementToPositon = '.subnav-scroll';
        //var endPosElement = 'footer';
        var totalSpacesBeforeScrollableArea = this.getScrollableAreaTopValue(scrollableArea, spaceBefore);

        // pass scrollable area element instead of always looking for the footer to support cases wherein there's content between the scrollable area and the footer
        // we don't need the hardcoded spaceAfter value anymore
        if (scrollPos >= totalSpacesBeforeScrollableArea && scrollPos < this.getScrollableAreaEndValue(elementToPositon, scrollableArea, spaceBefore)) {
            this.positionToFix(elementToPositon, spaceBefore, $('.subnav-scroll').width());
        } else if (scrollPos >= this.getScrollableAreaEndValue(elementToPositon, scrollableArea, spaceBefore)) {
            this.positionToAbsolute(elementToPositon);
        } else {
            this.positionToRelative(elementToPositon);
        }

    },

    checkScrollableActiveSection: function (scrollPos) {
        var that = this;
        var scrollableContents = '.scrollable-contents';
        var subNav = '.subnav-scroll';
        var scrollableContentsLength = $(scrollableContents).find('> div').length;
        var currentIndex = 1;
        scrollPos = Math.ceil(scrollPos);

        //if (scrollPos === $(window).height()) {
        if ($(window).scrollTop() + $(window).height() === $(document).height()){
            currentIndex = scrollableContentsLength;
        } else {
            for (var i = 1; i < scrollableContentsLength + 1; i++) {
                if (i === 1) {
                    if (scrollPos < FloatingSubNav.getSectionTopValue(scrollableContents, 1)) {
                        currentIndex = 1;
                        break;
                    }
                } else if (scrollPos >= FloatingSubNav.getSectionTopValue(scrollableContents, i - 1) && scrollPos < FloatingSubNav.getSectionTopValue(scrollableContents, i)) {
                    currentIndex = i;
                    break;
                } else if (scrollPos >= FloatingSubNav.getSectionTopValue(scrollableContents, scrollableContentsLength)) {
                    currentIndex = scrollableContentsLength;
                    break;
                }
            }
        }
        this.activeSubNavIndex = currentIndex;
        this.checkActiveSubNav(this.activeSubNavIndex, subNav);
    },

    getSectionTopValue: function (scrollableSection, ctr) {
        /*var addedViewSpace = 150;
        if ($(scrollableSection).length > 0) {
            return $(scrollableSection).find("#scrollable-content-" + ctr).offset().top + addedViewSpace;
        }*/

        var scrollables = $(scrollableSection), scrollable;
        if (scrollables.length > 0) {
            scrollable = scrollables.find("#scrollable-content-" + ctr);
            return Math.floor(scrollable.offset().top + scrollable.height());
        }
    },

    checkActiveSubNav: function (activeIndex, subNav) {
        $(subNav).find('.subnavgroup li').removeClass('subnav_active');
        $(subNav).find('.subnavgroup li:nth-child(' + activeIndex + ')').addClass('subnav_active');
        if (typeof scrollOmniture === 'function') {
            scrollOmniture($(subNav).find('.subnavgroup li:nth-child(' + activeIndex + ')'), activeIndex);
        }
        this.animateMagicLine(activeIndex, subNav);
    },

    animateMagicLine: function (activeIndex, subNav) {
        var that = this;
        if ($(subNav).length > 0) {
            var target = $(subNav).find('.subnavgroup li:nth-child(' + activeIndex + ') a');
            var topPos = target.position().top;
            var newHeight = target.parent().height();
        }

        $(that.magicLine).stop().animate({
            top: topPos,
            height: newHeight
        });
    },

    animateToSection: function (sectionToScroll) {
        var topPos = $(sectionToScroll).offset().top;

        $('html, body').animate(
            {
                scrollTop: topPos
            },
            500,
            'linear',
            this.animateCallback
        );
    },

    animateCallback: function () {
        if (typeof scrollOmnitureEnd === 'function') {
            scrollOmnitureEnd();
        }
    },

    checkIfNotMobile: function () {

        var windowWidth = $(window).outerWidth();

        windowWidth > 767 ? isMobile = false : isMobile = true;

        return isMobile;
    },

    getScrollableAreaTopValue: function (scrollableArea, spaceBefore) {
        if ($(scrollableArea).length > 0) {
            return $(scrollableArea).position().top - spaceBefore;
        }

    },

    // ScrollableAreaEl compute height
    getScrollableAreaEndValue: function (subNavHeight, scrollableArea, spaceBefore) {
        var scrollableAreaEl = $(scrollableArea);

        return (scrollableAreaEl.outerHeight() + scrollableAreaEl.position().top) - ($(subNavHeight).innerHeight() + spaceBefore);
    },

    positionToFix: function (element, spaceBefore, width) {
        $(element).css({
            'position': 'fixed',
            'top': spaceBefore,
            'bottom': 'auto',
            'max-width': width + 'px'
        });
    },

    positionToRelative: function (element) {
        $(element).css({
            'position': 'relative',
            'top': 'auto',
            'bottom': 'auto',
            'max-width': ''
        });
    },

    positionToAbsolute: function (element) {
        $(element).css({
            'position': 'absolute',
            'top': 'auto',
            'bottom': 0
        });
    }
};


$(window).on('scroll', function () {
    checkScrollLocation();
    //hiding social floats on footer
}).on('resize', function () {
    var subNavScrollEl = $('.subnav-scroll');

    // need to recalculate on resize especially for magic line
    checkScrollLocation();
    // match subnavscroll width with its parent using js since position:fixed ignores the parent width
    subNavScrollEl.width(subNavScrollEl.parent().width());
});

$(document).ready(function () {
    Initialized.run();
    Resized.init();
    Scrolled.init();
    // FocusUntrapper();

    // call scrollable-area FloatingSubNav
    var windowScroll = document.documentElement.scrollTop;

    if ($('.scrollable-area').length > 0) {
        FloatingSubNav.init(windowScroll);
    }
    FocusUntrapper();

    // Page loader trigger
    $("#show-loader-contained-mask").click(function() {
        // start showing the loader
        showLoaderWithMask('#brf-loader-contained-mask');
    });

    clickDragScroll_table();
});

$(document).ready(function () {
    // custome checkbox label changed to div element
    // click event
    $('.cb-no-wrap-label').not('label').click(function() {
        $(this).find('input[type=checkbox]').click();
    });
    // keypress event
    $('.cb-no-wrap-label').not('label').keypress(function(e) {
        var key = e.which;
        if(key == 13) {
            $('input[type=checkbox]').trigger('click');
        }
        return false;
    });
});

var to = 0;
$(window).resize(function () {

    Resized.init();
    clearTimeout(to);
    to = setTimeout(function () {
        Resized.onResizedOnly();
        $('.slidingCardsContainer').each(function () {
            $(this)[0].slick.refresh();
        });
            
    }, 300);
});

//reset the scrollable container location
function checkScrollLocation(){
    if ($('.scrollable-area').length > 0) {
        var windowScroll = window.pageYOffset || document.documentElement.scrollTop;

        // Check if will desktop or not then if desktop apply the floating scroll function
        if (FloatingSubNav.checkIfNotMobile() === false) {
            FloatingSubNav.checkScrollPos(windowScroll);
            FloatingSubNav.checkScrollableActiveSection(windowScroll);
        } else {
            FloatingSubNav.positionToRelative('.subnav-scroll');
        }
    }
}

// Side Nav end

// Click and Drag Scroll Funtions
function clickDragScroll_table() {
    const slider = document.querySelectorAll('.clickdragscroll');
    const sliderCount = slider.length;

    if(sliderCount > 0) {
        let isDown = false;
        let startX;
        let scrollLeft;

        // loop all .clickdragscroll classname
        for(let x=0; x < sliderCount; x++){
            let s = slider[x];
            s.addEventListener('mousedown', function(e) {
                isDown = true;
                s.classList.add('active');
                startX = e.pageX - s.offsetLeft;
                scrollLeft = s.scrollLeft;
            });
            s.addEventListener('mouseleave', function() {
                isDown = false;
                s.classList.remove('active');
            });
            s.addEventListener('mouseup', function() {
                isDown = false;
                s.classList.remove('active');
            });
            s.addEventListener('mousemove', function(e) {
                if(!isDown) return;
                e.preventDefault();
                const x = e.pageX - s.offsetLeft;
                const walk = (x - startX) * 3; //scroll-fast
                s.scrollLeft = scrollLeft - walk;
            });
        } 
    }  
}


//Functions upon load/Generic Functions
//function to trigger no focus
function FocusUntrapper() {
    if ($(".printAndBlur").length > 0) {
        $(".printAndBlur").keypress(function (event) {
            if (event.keyCode === 13) {
                if (typeof printOmniture === 'function') {
                    printOmniture();
                }
                window.print(); this.blur();
            }
        });
        $(".printAndBlur").click(function () {
            if (typeof printOmniture === 'function') {
                printOmniture();
            }
            window.print(); this.blur();
        });
    }

    if ($(".noFocusBlur").length > 0) {
        $(".noFocusBlur").keypress(function (event) {
            if (event.keyCode === 13) {
                this.blur();
            }
        });
        $(".noFocusBlur").click(function () {
            this.blur();
            $(this).tooltip('hide');
            //alert('hidding');
        });
    }
}



