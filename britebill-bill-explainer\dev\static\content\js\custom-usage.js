// For accessibility accordion. 

function changeAriaAttr() {
    var accordionUsageButton = $('.accordion-usage .accordion-toggle');
    accordionUsageButton.on('click', (function () {
        if (accordionUsageButton.hasClass('toggleOpen')) {
            accordionUsageButton.attr('aria-expanded', 'false');           
            accordionUsageButton.attr("aria-label", "expand " + accordionUsageButton.children().first("span").text() + "");
           
        }
        else {
            accordionUsageButton.attr('aria-expanded', 'true');
        }
    }));  
}


//For targetting IE only changing the attirbutes of SVG text tag

function checkIEChangeAttr() {
    // Sample function that returns boolean in case the browser is Internet Explorer//
    function isIE() {
        ua = navigator.userAgent;
        // MSIE used to detect old browsers and Trident used to newer ones//
        var is_ie = ua.indexOf("MSIE ") > -1 || ua.indexOf("Trident/") > -1;

        return is_ie;
    }

    // Create an condition to show if the browser is IE or not //
    if (isIE()) {
        $(".usageWheelContainer .donutGraph text.chart-label-center-blueLight").attr("y", "33%");
        $(".usageWheelContainer .donutGraph text.chart-label-center-blueLight-bold").attr("y", "48%");
        $(".usageWheelContainer .donutGraph text.chart-label-center-used").attr("y", "57%");
        $(".usageWheelContainer .donutGraph text.chart-label-center-sm").attr("y", "70%");     
        $(".usageWheelContainer .donutGraph text.chart-label-bottom-num").attr("y", "94%");   
        $(".usageWheelContainer .donutGraph text.chart-label-bottom-txt").attr("y", "103%");
    } 
}

//For switch component change attribute toggle 
function changeAriaState() {
    $('.unlit-usage-switch input').on('change', function () {
        var $el = $(this),
        switchElement = $el.closest('.unlit-usage-switch');
        if ($el.is(':checked')) {
            $el.prop('checked');
            switchElement.attr("aria-checked", 'true');
        }
        else {
            $el.removeProp('checked');
            switchElement.attr("aria-checked", 'false');
        }
    });
    $('.unlit-usage-switch').on('keydown', function (e) {
        var key = e.which || e.keyCode;
        if (key === 13 || key === 32) {
            e.preventDefault();
            $(this).click();
            $(this).focus();
        };
    });
}

//Add here to trigger button upon enter/space
//For switch component change attribute toggle 
function enterSpaceClick() {
    $('.enterSpaceTriggerClick').on('keydown', function (e) {
        var key = e.which || e.keyCode;
        if (key === 13 || key === 32) {
            e.preventDefault();
            $(this).click();
            $(body).addClass("modal");
            $(body).addClass("open")
            $(this).focus();
        };
    });
}


$(document).ready(function () {
    changeAriaAttr();
    checkIEChangeAttr();
    changeAriaState();
    enterSpaceClick();
});


(function ($) {
    var parent = $($(".accordPanel")).toArray();
    parent.forEach(function (item) {
        $(item).click(function (event) {
            var span = $(item).find("span.icon");
            if ($(span).hasClass('icon-expand-solid')) {
                $(span).removeClass('icon-expand-solid').addClass('icon-collapse-solid');
            } else if ($(span).hasClass('icon-collapse-solid')) {
                $(span).removeClass('icon-collapse-solid').addClass('icon-expand-solid');
            } else if ($(span).hasClass('icon-expand-bold')) {
                $(span).removeClass('icon-expand-bold').addClass('icon-collapse-bold');
            } else if ($(span).hasClass('icon-collapse-bold')) {
                $(span).removeClass('icon-collapse-bold').addClass('icon-expand-bold');
            }
            event.preventDefault();
        });
    });
})(jQuery);