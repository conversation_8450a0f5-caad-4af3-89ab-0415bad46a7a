import { createAction } from "redux-actions";
import { filterFetchBillsResponse } from "../filter/filterToState";
import { IFetchBillsReqPayload, IFetchBillsResPayload, IFetchBillsState } from "../models";

// const fetchGreetingFulfilledPayload = (greetings: IGreeting[]): IGreeting => {
//   return greetings[Math.floor(Math.random() * greetings.length)];
// };

// export const fetchGreeting = createAction<void>("FETCH_GREETING_MESSAGE") as () => ReduxActions.Action<void>;
// export const fetchGreetingFulfilled = createAction<IGreeting, IGreeting[]>("FETCH_GREETING_MESSAGE_FULLFILLED", fetchGreetingFulfilledPayload) as (greetings: IGreeting[]) => ReduxActions.Action<IGreeting>;

// // Error handling
// export const greetingError = createAction<any>("GREETING_ERROR") as (payload: any) => ReduxActions.Action<any>;

export const fetchBillsAction = createAction<IFetchBillsReqPayload>("FETCH_BILLS") as (payload: IFetchBillsReqPayload) => ReduxActions.Action<IFetchBillsReqPayload>;
export const fetchBillsCompleted = createAction<IFetchBillsState, IFetchBillsResPayload>("FETCH_BILLS_EXPLAINER_COMPLETED", filterFetchBillsResponse) as (payload: IFetchBillsResPayload) => ReduxActions.Action<IFetchBillsState>;
export const fetchBillsFailed = createAction<any>("FETCH_BILLS_EXPLAINER_FAILED") as (payload: any) => ReduxActions.Action<any>;
export const setIsLoadingPBE = createAction<boolean>("SET_IS_LOADING_PBE") as (payload: boolean) => ReduxActions.Action<boolean>;
export const updatePBEDataCache = createAction<{ key: string, value: any }>("UPDATE_PBE_DATA_CACHE") as (payload: { key: string, value: any }) => ReduxActions.Action<{ key: string, value: any }>;
export const getPBEDataFromActionURL = createAction<{ actionUrl: string, cacheKey: string }>("GET_PBE_DATA_FROM_ACTION_URL") as (payload: { actionUrl: string, cacheKey: string }) => ReduxActions.Action<{ actionUrl: string, cacheKey: string }>;
export const getPBEDataFromActionURLFailed = createAction<any>("GET_PBE_DATA_FROM_ACTION_URL_FAILED") as (payload: any) => ReduxActions.Action<any>;
