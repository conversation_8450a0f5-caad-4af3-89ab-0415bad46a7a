.graphical_ctrl {
    position: relative;
    padding-left: 35px;
}

.graphical_ctrl input {
    position: absolute;
    width: 48px;
    z-index: -1;
    height: 48px;
    opacity: 0;
    top: -16px;
    left: -9px;
}

.border-radius-3 {
    border-radius: 3px;
}


.ctrl_element {
    position: absolute;
    top: -3px;
    left: 0;
    height: 25px;
    width: 25px;
    background: #fff;
    /* box-shadow: inset 0 1px 1px 1px rgba(0, 0, 1, .15); */
    border: 1px solid #ccc;
}

.graphical_ctrl input:checked ~ .ctrl_element {
    background: #cc0000;
    border: 1px solid #cc0000;
}

.chk_radius {
    border-radius: 3px;
    cursor: pointer;
}

.ctrl_element:after {
    content: '';
    position: absolute;
    display: none;
}

.graphical_ctrl_checkbox .ctrl_element:after {
    left: 7px;
    top: 1px;
    width: 9px;
    height: 15px;
    border: solid #fff;
    border-width: 0 3px 3px 0;
    display: inline-block;
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
}

.graphical_ctrl input:checked ~ .ctrl_element:after {
    display: block;
}



.graphical_ctrl input:disabled ~ .ctrl_element, .graphical_ctrl input:checked:disabled ~ .ctrl_element {
    background: #e6e6e6;
    opacity: 0.6;
    border: 1px solid #e6e6e6;
    pointer-events: none;
}

.ctrl_radioBtn input:disabled ~ .ctrl_element:after {
    background: #7b7b7b;
    pointer-events: none;
    cursor: not-allowed;
}

.form-switch-toggle span span, .form-switch-toggle input:checked ~ span span:first-child, .form-switch-toggle.form-switch-candy label {
    color: #00549a;
}

.form-red-onoffswitch {
    position: relative;
    width: 70px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.form-red-onoffswitch-checkbox {
    position: absolute;
    height: 28px;
    width: 70px;
    opacity: 0;
}

.form-red-onoffswitch-label {
    display: block;
    overflow: hidden;
    border-radius: 50px;
    height: 30px;
}

.form-red-onoffswitch-checkbox:checked + .form-red-onoffswitch-label .form-red-onoffswitch-inner {
    margin-left: 0;
}

.form-red-onoffswitch-inner {
    display: block;
    width: 200%;
    margin-left: -100%;
}

.form-red-onoffswitch-inner:before, .form-red-onoffswitch-inner:after {
    display: block;
    float: left;
    width: 50%;
    height: 30px;
    padding: 0;
    line-height: 30px;
    font-size: 12px;
    color: white;
    font-family: Trebuchet, Arial, sans-serif;
    font-weight: bold;
    box-sizing: border-box;
}



.form-red-onoffswitch-inner:before, .form-red-onoffswitch-inner:after {
    display: block;
    float: left;
    width: 50%;
    height: 30px;
    padding: 0;
    line-height: 30px;
    font-size: 12px;
    color: white;
    font-family: Trebuchet, Arial, sans-serif;
    font-weight: bold;
    box-sizing: border-box;
}


.form-red-onoffswitch-inner:before {
    content: "YES";
    padding-left: 13px;
    background-color: #cc0000;
    color: #FFFFFF;
}

.form-red-onoffswitch-inner:after {
    content: "NO";
    padding-right: 14px;
    background-color: #000000;
    color: #FFFFFF;
    text-align: right;
}



.form-red-onoffswitch-switch {
    display: block;
    width: 24px;
    margin: 3px;
    background: #D4D4D4;
    position: absolute;
    top: 0;
    bottom: 0;
    border-radius: 50px;
    transition: all 0.3s ease-in 0s;
}

.form-red-onoffswitch-checkbox:checked + .form-red-onoffswitch-label .form-red-onoffswitch-switch {
    right: 0px;
    background: #fff;
}

.graphical_ctrl_checkbox input:disabled ~ .ctrl_element:after {
    border-color: #e6e6e6;
    pointer-events: none;
    cursor: not-allowed;
}

.graphical_ctrl_checkbox input:checked:disabled ~ .ctrl_element:after {
    border-color: #7b7b7b;
}

.graphical_ctrl input:disabled ~ .ctrl_element, .graphical_ctrl input:checked:disabled ~ .ctrl_element {
    background: #e6e6e6;
    opacity: 0.6;
    border: 1px solid #e6e6e6;
    pointer-events: none;
}

.graphical_ctrl.on-dark-bg input:checked ~ .ctrl_element {
    background: #cc0000;
    border: 1px solid #cc0000;
}


.graphical_ctrl.on-dark-bg input:checked ~ .ctrl_element:after {
    border: solid #fff;
    border-width: 0 3px 3px 0;
}

.graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element {
    outline-width: 1px;
    outline-style: dashed;
    outline-color: #4d90fe;
}




