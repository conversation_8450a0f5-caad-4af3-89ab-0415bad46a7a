/*
* jQuery-Calendar Plugin v1.1.1
*
* 2018 (c) <PERSON>
* This software is licensed under the MIT license!
* View LICENSE.md for more information
*/
(function ($) {

    var calendarTable, calendarTableHeader, today = new Date().getDate();

	$.fn.calendar = function (opts) {
		var options = $.extend({
			color: '#308B22',
			months: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'April', '<PERSON>', 'Jun<PERSON>', 'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember'],
			days: ['Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa', 'So'],
			onSelect: function (event) {}
		}, $.fn.calendar.defaults, opts);
		
		return this.each(function () {
            var currentYear, currentMonth, currentDay, currentCalendar;

			
			initCalendar($(this), options);
        });

	};
	
	function initCalendar(wrapper, options) {
		var color = options.color; 
		
		wrapper.addClass('calendar').empty();
        $('<div class="sr-only cover-content">').appendTo(wrapper);
		var header = $('<div>').appendTo(wrapper);
		header.addClass('calendar-header');
		header.css({
			background: color,
			color: createContrast(color)
		});
		
        var buttonLeft = $('<button class="icon-mya icon-arrow_left" role=button aria-label="go to previous month">').appendTo(header);
		buttonLeft.addClass('button').addClass('left');
		//buttonLeft.html(' &lt; '); //used MYA virgin icons
		buttonLeft.bind('click', function () { currentCalendar = $(this).parents('.calendar'); selectMonth(false, options); });
		buttonLeft.bind('mouseover', function () { $(this).css('background', createAccent(color, -20)); });
		buttonLeft.bind('mouseout', function () { $(this).css('background', color); });
		
		var headerLabel = $('<span aria-live="polite">').appendTo(header);
		headerLabel.addClass('header-label')
		headerLabel.html(' Month Year ');
		headerLabel.bind('click', function () { 
			currentCalendar = $(this).parents('.calendar');
			selectMonth(null, options, new Date().getMonth(), new Date().getFullYear());
			
			currentDay = new Date().getDate();
			triggerSelectEvent(options.onSelect);
		});
		
        var buttonRight = $('<button class="icon-mya icon-arrow_right" role=button aria-label="go to next month">').appendTo(header);
		buttonRight.addClass('button').addClass('right');
		//buttonRight.html(' &gt; '); //used MYA virgin icons
		buttonRight.bind('click', function () { currentCalendar = $(this).parents('.calendar'); selectMonth(true, options); });
		buttonRight.bind('mouseover', function () { $(this).css('background', createAccent(color, -20)); });
		buttonRight.bind('mouseout', function () { $(this).css('background', color); });
		
		//var dayNames = $('<table>').appendTo(wrapper);
		//dayNames.append('<thead><th>' + options.days.join('</th><th>') + '</th></thead>');
		//dayNames.css({
		//	width: '100%'
		//});

        calendarTable = $('<table class="calendar-table" aria-readonly="true role="grid">').appendTo(wrapper);
        calendarTableHeader = $('<thead><tr role="row"><th scope="col">' + options.days.join('</th><th scope="col">') + '</th></tr></thead>').appendTo(calendarTable);
		
		//var calendarFrame = $('<div>').appendTo(wrapper);
		//calendarFrame.addClass('calendar-frame');
		
		headerLabel.click();
	}
	
	function selectMonth(next, options, month, year) {
		var tmp = currentCalendar.find('.header-label').text().trim().split(' '), tmpYear = parseInt(tmp[1], 10);
		
		if (month === 0){
			currentMonth = month;
		} else{
			currentMonth = month || ((next) ? ((tmp[0] === options.months[options.months.length - 1]) ? 0 : options.months.indexOf(tmp[0]) + 1) : ((tmp[0] === options.months[0]) ? 11 : options.months.indexOf(tmp[0]) - 1));
		}
		
		currentYear = year || ((next && currentMonth === 0) ? tmpYear + 1 : (!next && currentMonth === 11) ? tmpYear - 1 : tmpYear);
        var calendar = createCalendar(currentMonth, currentYear, options);
        //var frame = calendar.frame();
        var frame = calendar.calendarTable();
		
        //currentCalendar.find('.calendar-frame').empty().append(frame);
        currentCalendar.find('.header-label').text(calendar.label);
        currentCalendar.find('.sr-only').text(calendar.label);
        currentCalendar.find('table').attr("aria-label", calendar.label);
		
	
	}	
	
	function createCalendar(month, year, options) {
		var currentDay = 1, daysLeft = true,
		startDay = new Date(year, month, currentDay).getDay() - 1,
		lastDays = [31, (((year % 4 == 0) && (year % 100 != 0)) || (year % 400 == 0)) ? 29 : 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31], 
		calendar = [];
		
		var i = 0;
		while(daysLeft) {
			calendar[i] = [];
			
			for(var d = 0; d < 7; d++) {
				if(i == 0) {
					if(d == startDay) {
						calendar[i][d] = currentDay++;
						startDay++;
					} else if (startDay === -1) {
            					calendar[i][6] = currentDay++;
            					startDay++;
					}
				} else if(currentDay <= lastDays[month]) {
					calendar[i][d] = currentDay++;
				} else {
					calendar[i][d] = ''; 
					daysLeft = false;
				}
				
				if (currentDay > lastDays[month]) { 
					daysLeft = false; 
				} 
			}
			
			i++;
		}
		
		//var frame = $('<table>').addclass('current');
        //      var framebody = $('<tbody>').appendto(frame);

        calendarTable.empty().append(calendarTableHeader);
        var frameBody = $('<tbody>').appendTo(calendarTable);

        var y = new Date().getFullYear();
        var m = new Date().getMonth();
        var d = new Date().getDate();
        var previousMonth = true;

		for(var j = 0; j < calendar.length; j++) {
			var frameRow = $('<tr>').appendTo(frameBody);
			
			$.each(calendar[j], function (index, item) {
				//var frameItem = $('<td>').appendTo(frameRow);
                //frameItem.text(item);
                if (item) {
                    previousMonth = false;
                    if (new Date(y, m, d).getTime() <= new Date(currentYear, currentMonth, item).getTime()) {
                        frameRow.append('<td role="gridcell" data-date="' + currentYear + ',' + (currentMonth + 1) + ',' + item + '" tabindex="-1"><span>' + item);
                    } else {
                        frameRow.append('<td class="disabled" role="gridcell">-');
                    }
                } else {
                    if (previousMonth) frameRow.append('<td class="disabled" role="gridcell">-');
                }
            });

            
        }

        
		
		//$('td:empty', frame).addClass('disabled');
		//if(currentMonth === new Date().getMonth()) { 
		//	$('td', frame).filter(function () { return $(this).text() === new Date().getDate().toString(); }).addClass('today'); 
		//} 
	
		//return { frame: function () { return frame.clone() }, label: options.months[month] + ' ' + year };

        //$('td:empty', calendarTable).addClass('disabled');
        if (currentMonth === new Date().getMonth()) {
            $('td', calendarTable).filter(function () { return $(this).text() === new Date().getDate().toString(); }).addClass('today').attr('aria-current',true);
        }

        //Set tabindex to 0 of the first available td:not(.today):not(.disabled)
        $('.calendar-table td:not(.today):not(.disabled)').first().attr('tabindex', '0');

        return { calendarTable: function () { return calendarTable.clone() }, label: options.months[month] + ' ' + year };
	}
	
	function triggerSelectEvent(event) {
		var date = new Date(currentYear, currentMonth, currentDay);
			
		var label = [];
		label[0] = (date.getDate() < 10) ? '0' + date.getDate() : date.getDate();
		label[1] = ((date.getMonth() + 1) < 10) ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
		label[2] = (date.getFullYear());

		if(event != undefined) {
			event({date: date, label: label.join('.')});
		}
	}
	
	function createContrast(color) {
		if(color.length < 5) {
			color += color.slice(1);
		}
		
		return (color.replace('#','0x')) > (0xffffff) ? '#222' : '#fff';
	}
	
	function createAccent(color, percent) {
		var num = parseInt(color.slice(1),16), amt = Math.round(2.55 * percent), R = (num >> 16) + amt, G = (num >> 8 & 0x00FF) + amt, B = (num & 0x0000FF) + amt;
		return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 + (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 + (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

    $(document).on('click', '.calendar-table td', function () {
        selectDate($(this))
    });

    $(document).on('keyup', '.calendar-table td', function (e) {
        if (e.which == 13 || e.which == 32) {
            selectDate($(this))
        }
    });

    function selectDate(el) {
        $('.calendar-table td').removeClass('selected').removeAttr('aria-selected').attr('tabindex', '-1');
        el.addClass('selected').attr('aria-selected', true).attr('tabindex', '0');
    }

    $(document).on("keydown", ".calendar-table td", function (e) {

        var currentDate = new Date($(this).attr('data-date'));
        var newDate;
        switch (e.keyCode) {
            case 37:
                newDate = new Date(currentDate.setDate(currentDate.getDate() - 1));
                e.stopPropagation();
                break;
            case 38:
                newDate = new Date(currentDate.setDate(currentDate.getDate() - 7));
                e.stopPropagation();
                break;
            case 39:
                newDate = new Date(currentDate.setDate(currentDate.getDate() + 1));
                e.stopPropagation();
                break;
            case 40:
                newDate = new Date(currentDate.setDate(currentDate.getDate() + 7));
                e.stopPropagation();
                break;
        }
        if (newDate) {
            var formatDate = newDate.getFullYear() + ',' + (newDate.getMonth() + 1) + ',' + newDate.getDate();
            if ($('.calendar-table td[data-date="' + formatDate + '"]').length > 0) {
                $(this).removeClass('selected').removeAttr('aria-selected').attr('tabindex', '-1');
                $('.calendar-table td[data-date="' + formatDate + '"]').addClass('selected').attr('aria-selected', true).attr('tabindex', '0').focus();

            }
        }
        

    });

}(jQuery));
