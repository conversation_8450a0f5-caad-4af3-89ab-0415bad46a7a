/*Generic Classes*/
.minheight-50 {
    min-height: 50px;
}

.form-button-switch-toggle {
    background-color: #fff;
    padding: 4px 3px;
    min-height: 30px;
    margin: 0;
    cursor: pointer;
}

.vm-light-grey-1 {
    color: #8A8A8F;
}

.form-button-switch-toggle input {
    position: absolute;
    opacity: 0;
    z-index: 5;
}

.switch-options span {
    padding: 5px 10px;
    color: #333;
}

.form-button-switch-toggle input:checked ~ .switch-options .option-one, .form-button-switch-toggle input:not(:checked) ~ .switch-options .option-two, .switch-options .option-one.selected {
    border-radius: 3px;
    background-color: #CC0000;
    color: #fff;
}

.btn-tag-filter .icon-close:before {
    top: 1px;
    left: 5px;
}

.btn-tag-filter {
    background-color: #cc0000;
    border: 1px solid #cc0000;
    padding: 5px 15px;
    border-radius: 17px;
}

.btn-tag {
    background-color: #fff;
    border: 1px solid #333333;
    padding: 4px 5px;
    line-height: 10px;
}

.btn-new {
    background-color: #cc0000;
    border: 1px solid #cc0000;
    padding: 4px 5px;
    line-height: 10px;
}

.btn-removed {
    background-color: #999999;
    border: 1px solid #999999;
    padding: 4px 5px;
    line-height: 10px;
}

.btn-primary {
    color: #fff;
    background-color: #cc0000;
    border: 2px solid #cc0000;
}

.btn-primary-inverted {
    color: #000;
    background-color: #fff;
    border: 2px solid #fff;
}

.btn-primary-inverted:hover, .btn-primary-inverted:focus, .btn-primary-inverted:active, .btn-primary-inverted:not(:disabled):not(.disabled).active, .btn-primary-inverted:not(:disabled):not(.disabled):active {
    border: 2px solid #000;
}

.btn {
    border-radius: 3px;
    font-size: 16px;
    font-weight: bold;
    padding: 9px 30px;
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary:not(:disabled):not(.disabled).active, .btn-primary:not(:disabled):not(.disabled):active {
    color: #fff;
    background-color: #eb0000;
    border-color: #eb0000;
}

.btn-primary.disabled, .btn.btn-default.disabled {
    background-color: transparent;
    border-color: #9c9c9c;
    color: #9c9c9c;
    pointer-events: none;
}

.btn-primary-inverted.disabled {
    background-color: transparent;
    border-color: #fff;
    color: #fff;
    pointer-events: none;
    border: 2px solid #fff;
}


.btn-default {
    color: #333333;
    background: none;
    border-color: #333333;
    border: 2px solid #333333;
}

.btn-default {
    color: #333333;
    background: none;
    border-color: #333333;
    border: 2px solid #333333;
}

.btn-default:hover, .btn-default:focus, .btn-default:active, .btn-default:not(:disabled):not(.disabled).active, .btn-default:not(:disabled):not(.disabled):active {
        color: #CC0000;
        background-color: #fff;
        border-color: #CC0000;
}

