
@media (min-width: 1280px) {
    .pad-90-left-lg{
        padding-left: 90px;
    }
}

/*Font Size*/
.txtSize41{
    font-size: 41px;
}

.txtSize28 {
    font-size: 28px;
}

.txtSize36{
    font-size: 36px;
}

/*Line Heaight*/
.line-height-43{
    line-height: 43px;
}

.line-height-22 {
    line-height: 22px;
}

.line-height-28 {
    line-height: 28px;
}

.line-height-34 {
    line-height: 34px;
}

.line-height-18 {
    line-height: 18px;
}

.line-height-24 {
    line-height: 24px;
}

/*Height*/
.height-175{
    height:175px;
}

.min-height-360 {
    height: 360px;
    min-height: 360px;
}

.min-height-400 {
    min-height: 400px;
}

.min-height-470 {
    height: 470px;
    min-height: 470px;
}

.min-height-240 {
    min-height: 240px;
}

/*Width*/
.width-140{
    width: 140px;
}

.max-width-400 {
    max-width: 400px;
}

.max-width-420 {
    max-width: 420px;
}

/*Padding*/
.container.liquid-container.no-pad{
    padding: 0;
}

.pad-90-left {
    padding-left: 90px;
}

/*Opacity*/
.opacity-p48 {
    opacity: 0.48;
}
.opacity-p5{
    opacity: 0.5;
}

/*Buttons*/
.btn-default-2 {
    color: #333333;
    background: none;
    border-color: #333333;
    border: 2px solid #333333;
}

.btn-primary-2 {
    color: #fff;
    background: none;
    border-color: #333333;
    border: 2px solid #fff;
}

    .btn-primary-2:hover, .btn-primary-2:focus {
        color: #fff;
    }

/*Background Color*/
.bgRed3 {
    background-color: #FF0000;
}

/*Border*/
.border-lightGray9{
    border: 1px solid #979797;
}

/*Image Focus*/
.crop-img{
    position: absolute;
    width: auto;
    min-width: 100%;
}

.centerView {
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
}

.rightCenterView {
    right: 0;
    top: 50%;
    transform: translate(0,-50%);
}


/*Alignment*/
.vCenter{
    position: relative;
    top: 50%;
    transform: translateY(-50%);
}


@media (min-width: 768px) and (max-width: 991.98px) {
    /*Padding*/
    .pad-60-left-sm {
        padding-left: 60px;
    }

    /*Images*/
    centerView-sm {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    /*Width*/
    .max-width-400-sm {
        max-width: 400px;
    }

    .max-width-100p-sm {
        max-width: 100%;
    }
}

@media (max-width: 767.98px) {
    /*Padding*/ 
    .no-pad-left-xs{
        padding-left: 0;
    }

    /*Font Size*/
    .txtSize28-xs{
        font-size: 28px;
    }

    /*Line Heaight*/
    .line-height-30-xs {
        line-height: 30px;
    }

    .line-height-18-xs{
        line-height: 18px;
    }

    .line-height-24-xs {
        line-height: 24px;
    }

    /*Width*/
    .max-width-355-xs {
        max-width: 355px;
    }

    .max-width-100p-xs {
        max-width: 100%;
    }

    /*Height*/
    .min-height-530-xs{
        min-height:530px;
    }

    .min-height-450-xs{
        min-height: 450px;
    }

    .min-height-165-xs {
        min-height: 165px;
    }

    /*Buttons*/
    .btn {
        padding: 10px 45px;
    }

    /*Position*/
    .absolute-xs{
        position: absolute;
    }

    /*Images*/
    centerView-xs{
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}