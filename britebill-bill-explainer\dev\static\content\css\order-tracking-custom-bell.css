﻿/*custom scrollbar*/
.scrollAdjustLightBlue::-webkit-scrollbar {
    width: 6px;
}
/* Track */
.scrollAdjustLightBlue::-webkit-scrollbar-track {
    /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);*/
    -webkit-border-radius: 8px;
    border-radius: 8px;
    margin-bottom: 30px;
    margin-top: 15px;
    background: #e1e1e1;

}

/* Handle */
.scrollAdjustLightBlue::-webkit-scrollbar-thumb {
    height: 40px;
    -webkit-border-radius: 8px;
    border-radius: 8px;
    background: #00549A;
    /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.5);*/
}

.scrollAdjustLightBlue {
    overflow: scroll;
    overflow-x: hidden;
}

/*custom scrollbar ended*/


.top-box-shadow {
    box-shadow: 0 0 60px 0 rgba(0,0,0,.3)
}

.footer-seal {
    display: inline-block;
    margin-left: 10px;
    height: 50px;
    width: auto;
}

.strikethrough{
    text-decoration: line-through;
}


/*For Tablet and Mobile - Start*/
@media screen and (max-width:991.98px) { }
/*For Tablet and Mobile - End*/

/*For mobile only - Start*/
@media (max-width: 767px) {  
    .footer-seal {
        margin: 0px;
    }

    
    .scrollAdjustLightBlue-xs::-webkit-scrollbar {
        width: 0 !important;
        -ms-overflow-style: -ms-autohiding-scrollbar;
    }
}

@media (max-width: 767.98px) and (min-width: 320px) {
    .modal .modal-dialog {
        top:0px;
        position:absolute !important;
    }
}
/*For mobile only - End*/


