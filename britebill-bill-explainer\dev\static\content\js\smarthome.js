function initializeScrollableTable() {
    $('.scrollableContainer-js').on('scroll', function () {
        var $this = $(this);
        var scrollPos = $this.scrollLeft();
        var width = $this.width();
        var scrollWidth = $this.get(0).scrollWidth;
        var container = $this.closest('[class^=scrollableContainerShadow]');
        if (scrollWidth > $this.get(0).clientWidth + 1) {
            if (scrollPos === 0) {
                container.removeClass('left');
            } else {
                container.addClass('left');
            }
            if (scrollPos + width === scrollWidth) {
                container.removeClass('right');
            } else {
                container.addClass('right');
            }
        }
        else {
            $this.closest('[class^=scrollableContainerShadow]').removeClass('left').removeClass('right');
        }
    });
    $(window).resize(function () {
        if (window.matchMedia('(max-width: 991px)').matches) {
            $('.scrollableContainer-js').trigger('scroll');
        }
        else {
            $('.same-height-modal [class^=scrollableContainerShadow]').removeClass('left').removeClass('right');
        }
    });
}

initializeScrollableTable();

$('.same-height-modal-js').on('shown.bs.modal', function () {
    $(this).find('.same-height').each(function () {
        processSameHeightElements($(this));
    });
});

//Radio Button with border
$('.graphical_ctrl input').change(function () {
    var $this = $(this);
    if ($this.attr('type') == "radio") {
        //Remove border to radio button
        $this.closest('[role="radiogroup"]').find('.radio-container').removeClass('checked-border');
   
        if ($this.prop('checked')) {
            //Add border to radio button 
            $this.closest('.radio-container').addClass('checked-border');
        }
    }
});

$('.graphical_ctrl input').focus(function () {
    //Add focus class in radio button container
    $(this).closest('.radio-container').addClass('focused-element');
});
$('.graphical_ctrl input').blur(function () {
    //Remove focus class in radio button container
    $(this).closest('.radio-container').removeClass('focused-element');
});
$('.radio-container').click(function (e) {
        var $this = $(this);
        $this.find('.graphical_ctrl input').prop('checked', true);
        $this.find('.graphical_ctrl input').trigger('change');
        $this.find('.graphical_ctrl input').focus();
});

//Tab anchor same-height function
$('.same-height-tab-js').on("click", function () {
    $(".same-height-tabpanel-js").find('.same-height').each(function () {
        processSameHeightElements($(this));
    });
});

$(document).ready(function () {
    $(window).on('load', function () {
        processSameHeightElements();
    });

    //checked if there are checked properties in DOM
    if ($('input[type="radio"][name="radioDevices"]:checked').length > 0) {
        let targetValue = $('input[type="radio"][name="radioDevices"]:checked').attr('aria-controls');
        //find targetValue and add block class to show the content
        $("section").find("#" + targetValue).addClass("block");
        //add checked border class
        $('.radio-container input[type="radio"][name="radioDevices"]:checked').closest('.radio-container').addClass('checked-border');   
    }

    // Radio button click event
    $('.radio-container input[type="radio"][name="radioDevices"]').change(function () {
        // Get value of clicked radio button
        inputValue = $(this).attr('aria-controls');
        //checked radio btn if checked  
        $(this).prop('checked', true);
        // Get the DOM id aria-controls
        let targetContainer = $('div[id="' + inputValue + '"]');
        // Hide all other that has no id
        $('.display-radio-container').not(targetContainer).removeClass("block");
        // Show targeted element
        $(targetContainer).addClass("block");
        processSameHeightElements();
    });
});
