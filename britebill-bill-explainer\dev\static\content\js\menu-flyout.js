/*
  Version: 1.0.0
  Developer: <EMAIL> (BCX)
  Date: August 13 2018
  Description: Once window has completly loaded the page bind the menu-flyout click event behaviour
*/
window.addEventListener("DOMContentLoaded", function() {
  (function($) {  
    var activeClassName = "menu-flyout-item-active";
    var visibleClassName = "menu-flyout-visible";
    var menuExpandedClassName = "menu-flyout-has-been-expanded";
    var componentSelector = ".menu-flyout";
    var menuItemsLevel1Selector = ".sub-nav-root > li";
    var noSubNavSelector = ":not(.no-sub-nav)";
    var connectorAreaSelectorList = [".rsx-connector-area.rsx-active", ".connector-area.active"];

    // Bind click events on the connector area
    bindConnectorArea();
    bindClickOutside();
    bindHideMenuFlyoutOnEscapePress();
    
    // Only bind events when width of screen is minimum 1000px e.g Desktop
    $(window).resize(function() {
      if (window.matchMedia("(min-width: 1000px)").matches) {
        bindConnectorArea();
      } else {
        unbindConnectorArea();
      }
    });

    // Bind click event on the LI nav first level
    $(document).on("click.menu-flyout", componentSelector + " " + menuItemsLevel1Selector + noSubNavSelector , function() {
      var li = $(this);

      // 1. We set the li to be active
      var isAlreadyActive = setLinkActive(li);
      // 2. We then animate the sub navigation 
      animateLinkSubNav(li, menuExpandedClassName, isAlreadyActive);
      // 3. Finally we set the expanded state to the menu-flyout component so that the animation only occurs once
      setComponentExpandedState(li, menuExpandedClassName);       
    });

    // Prevent link with subNav to perform actual behaviour
    $(document).on("click.menu-flyout-a", componentSelector + " " + menuItemsLevel1Selector + noSubNavSelector + " > a" , function(event) {
      event.preventDefault();
    });

   function isDescendant(parentSelector, child) {
    var parent = $(parentSelector).first().get(0);
    var node = child;
    while (node !== null) {
      if (node === parent) {
        return true;
      } else {
        node = node.parentNode;
      }
    }
    return false;
  }

    function bindRSXClickOutside() {
      var parentSelector = ".rsx-connector-area.rsx-active";
      doBindClickOutside(parentSelector);
    }
    function bindBRFClickOutside() {
      var parentSelector = ".connector-area.active";
      doBindClickOutside(parentSelector);
    }

    function doBindClickOutside(parentSelector) {
      $(document).on("click.outside", function(event) {
        if (!isDescendant(parentSelector, event.target)) {
          var component = getComponentFromParentSelector(parentSelector);
          hideMenuFlyout(component);
        }
      });      
    }

    function getComponentFromParentSelector(parentSelector) {
      return $(parentSelector).find(componentSelector);
    }

    function hideMenuFlyout(component) {
      component
        .removeClass(visibleClassName)
        .removeClass(menuExpandedClassName)
        .find(menuItemsLevel1Selector)
        .removeClass(activeClassName)
        .parents("li").first()
        .removeClass("rsx-active")
        .removeClass("active");
    }

    function bindHideMenuFlyoutOnEscapePress() {
      $(document).keyup(function(e) {
        if (e.keyCode == 27) {
          $.each(connectorAreaSelectorList, function(idx, parentSelector) {
            var component = getComponentFromParentSelector(parentSelector);
            hideMenuFlyout(component);

            // PATCH
            $('.rsx-connector-areas .rsx-connector-area').removeClass('.rsx-active');
             $('.connector-areas .connector-area').removeClass('.active');
            $('.rsx-connector-areas .rsx-connector-area:nth-child(2) > a').focus();
            $('.connector-areas .connector-area:nth-child(2) > a').focus();
            setTabindexes();
          });
        }
      });      
    }

    function bindConnectorArea() {
      bindRSXConnectorArea();
      bindBRFConnectorArea();      

      // PATCH
      setTabindexes();   
    }

    function unbindConnectorArea() {
      unbindRSXConnectorArea();
      unbindBRFConnectorArea();      
    }

    function bindClickOutside() {
      bindRSXClickOutside();
      bindBRFClickOutside();
    }

    function unbindRSXConnectorArea() {
      $(document).off("click.rsx-connector-menu-flyout");
    }

    function bindRSXConnectorArea() {
      var activeFrameworkClassName = "rsx-active";
      
      unbindRSXConnectorArea();

      $(document).on("click.rsx-connector-menu-flyout", ".rsx-connector-areas .rsx-connector-area ", function(event) {
        var clickableElement = $(event.target);
        if (clickableElement.parent().hasClass("rsx-connector-area")) {
          event.preventDefault();
        }

        var area = $(this);
        var currentArea = area
                            .siblings()
                            .removeClass(activeFrameworkClassName)
                            .end()
                            .addClass(activeFrameworkClassName);      

		// PATCH
		if (currentArea.hasClass(activeFrameworkClassName)){
			currentArea.find('.sub-nav-level-1').removeAttr('tabindex');
		}

        if (currentArea.attr("class").indexOf("hidden") === -1) {
          area
            .siblings()
            .each(function(idx, areaSibling) {
              var $sibling = $(areaSibling);
              var component = $sibling.find(componentSelector);
              hideMenuFlyout(component);
            });
           
          currentArea.find(componentSelector).addClass(visibleClassName);
        }
      });
    }

    function unbindBRFConnectorArea() {
      $(document).off("click.brf-connector-menu-flyout");
    }

    function bindBRFConnectorArea() {
      var activeFrameworkClassName = "active";

      unbindBRFConnectorArea();

      // PATCH - added new click.menu-flyout
      $(document).on("click.brf-connector-menu-flyout click.menu-flyout", ".connector-areas .connector-area", function(event) {
        var clickableElement = $(event.target);
        
        if (clickableElement.hasClass("bellSlim") || clickableElement.parent().hasClass("connector-area")) {
          event.preventDefault();
        }

        var area = $(this);
        area
          .siblings()
          .removeClass(activeFrameworkClassName)
          .end()
          .addClass(activeFrameworkClassName);        

        // PATCH
		if (area.hasClass(activeFrameworkClassName)){
			area.find('.sub-nav-level-1').removeAttr('tabindex');
		}

        if (area.attr("class").indexOf("hidden") === -1) {
          area
            .siblings()
            .each(function(idx, areaSibling) {
              var $sibling = $(areaSibling);
              var component = $sibling.find(componentSelector);
              hideMenuFlyout(component);
            });

          area.find(componentSelector).addClass(visibleClassName);
        }
      });
    }

    function setLinkActive(li) {      
      var isAlreadyActive = li.hasClass(activeClassName);

      li
        .siblings()
        .removeClass(activeClassName)
        .end()
        .addClass(activeClassName);
      
      return isAlreadyActive;
    }

    function animateLinkSubNav(li, menuExpandedClassName, isAlreadyActive) {    
      var enterAnimationClassName = "enter-animation";      
      var enterOffsetClassName = "enter-offset";
      var enterFadeInClassName = "enter-fadein";
      var subNav, subNavWidth;
      var subNavClassName = "sub-nav-group";
      var subNavSelector = "." + subNavClassName;
      var subNavLargeClassName = "sub-nav-large";    

      var component = li.parents(componentSelector); 

      subNav = li.find(subNavSelector);
      
      var isLargeNav = subNav.hasClass(subNavLargeClassName);

      if (!component.hasClass(menuExpandedClassName)) {
        li
          .addClass(enterOffsetClassName)          
          .addClass(enterAnimationClassName);          

        if (isLargeNav) {
          subNavWidth = "280%";
        } else {
          subNavWidth = "180%";
        }

        subNav  
          .width(0)
          .animate({"width": subNavWidth}, 225, function() {
            li.removeClass(enterAnimationClassName);
            li.removeClass(enterOffsetClassName);   
          });
      } else {
        if (!isAlreadyActive) {
          /*subNav
            .addClass(enterFadeInClassName);
  
          var timerFadeInId = window.setTimeout(function() {
            window.clearTimeout(timerFadeInId); 
            subNav.removeClass(enterFadeInClassName);        
          }, 225);
          */         
        }        
      }
      
    }

    function setComponentExpandedState(li, menuExpandedClassName) {

      // PATCH
      li.parents(componentSelector).find('.sub-nav-group a').attr('tabindex', '-1');
      li.find('.sub-nav-group a').attr('tabindex', '-1').removeAttr('tabindex');

      var timerId = window.setTimeout(function() {
        window.clearTimeout(timerId);
        var component = li.parents(componentSelector);   
        component.addClass(menuExpandedClassName);        
      }, 500);    
    }

    // PATCH
    function setTabindexes(){
    	$(componentSelector + " .sub-nav-level-1").attr('tabindex', '-1');
    	$(menuItemsLevel1Selector + " a").attr('tabindex', '-1');
    }

  })(typeof jQuery !== "undefined" ? jQuery : typeof jQBRF);

}, true);