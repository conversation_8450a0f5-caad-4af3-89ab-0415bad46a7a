﻿/*! jQuery UI - v1.9.2 - 2012-11-23
* http://jqueryui.com
* Includes: jquery.ui.datepicker-fr.js
* Copyright 2012 jQuery Foundation and other contributors; Licensed MIT */
jQuery(function (e) { e.datepicker.regional.fr = { closeText: "Fermer", prevText: "Précédent", nextText: "Suivant", currentText: "Aujourd'hui", monthNames: ["<PERSON><PERSON>", "Février", "Mars", "Avril", "<PERSON>", "<PERSON><PERSON>", "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"], monthNamesShort: ["Janv.", "Févr.", "<PERSON>", "Avril", "<PERSON>", "Juin", "Juil.", "Août", "Sept.", "Oct.", "Nov.", "Déc."], dayNames: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], dayNamesShort: ["Dim.", "Lun.", "<PERSON>.", "Mer.", "Jeu.", "Ven.", "<PERSON>."], dayNamesMin: ["Di", "Lu", "Ma", "Me", "Je", "Ve", "Sa"], weekHeader: "Sem.", dateFormat: "dd/mm/yy", firstDay: 0, isRTL: !1, showMonthAfterYear: !1, yearSuffix: "" }, e.datepicker.setDefaults(e.datepicker.regional.fr) });