@font-face {
  font-family: 'bell-icon';
  src: url(../fonts/shop-icons.eot?#iefix) format('embedded-opentype'),
    url(../fonts/shop-icons.woff) format('woff'),
    url(../fonts/shop-icons.ttf) format('truetype'),
    url(../fonts/shop-icons.svg) format('svg');
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'bell-icon-outline';
  src: url(../fonts/shop-icons.eot?iw8dli);
  src: url(../fonts/shop-icons.eot?#iefixiw8dli) format('embedded-opentype'),
    url(../fonts/shop-icons.ttf?iw8dli) format('truetype'),
    url(../fonts/shop-icons.woff?iw8dli) format('woff'),
    url(../fonts/shop-icons.svg?iw8dli/shop-icons) format('svg');
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'bell-icon2';
  src: url(../fonts/shop-icons.eot?#iefix) format('embedded-opentype'),
    url(../fonts/shop-icons.woff) format('woff'),
    url(../fonts/shop-icons.ttf) format('truetype'),
    url(../fonts/shop-icons.svg) format('svg');
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'bell-icon3';
  src: url(../fonts/shop-icons.eot?#iefix) format('embedded-opentype'),
    url(../fonts/shop-icons.woff) format('woff'),
    url(../fonts/shop-icons.ttf) format('truetype'),
    url(../fonts/shop-icons.svg) format('svg');
  font-weight: 400;
  font-style: normal;
  font-display: block;
}
.icon,
.icon2,
.icon3 {
  font-family: 'bell-icon';
  font-style: normal;
  speak: none;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-o {
  font-style: normal;
  speak: none;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon:before,
.icon2:before {
  font-family: 'bell-icon';
  position: relative;
  top: 0px;
}
.icon3,
.icon3:before {
  font-family: 'bell-icon3';
  position: static;
}
.icon-o:before {
  font-family: 'bell-icon-outline';
}

/* START Global header and footer icons */
.icon-chevron:before,
.icon-chevron-up:before,
.icon-chevron-right:before,
.icon-chevron-down:before,
.icon-chevron-left:before {
  content: '\e012';
  display: inline-block;
}
.icon-chevron-up:before {
  -webkit-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  transform: rotate(-90deg);
  -webkit-transform-origin: 45% 40%;
  -ms-transform-origin: 45% 40%;
  transform-origin: 45% 40%;
}
.icon-chevron-down:before {
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}
.icon-chevron-left:before {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}
.icon-plus:before {
  content: '\e007';
  /* top: 3px !important; */
}
.icon-bell-logo:before {
  content: '\e600';
}
.icon-cart:before {
  content: '\e617';
  top: 3px !important;
}
.icon-silhouette:before {
  content: '\e616';
  top: 3px !important;
}
.icon-voice-search:before {
  content: '\e91f';
}
.icon-magnifying-glass:before {
  content: '\e615';
  top: 0 !important;
}
.icon-mobile-menu:before {
  content: '\e618';
}
.icon-close1:before {
  content: '\e624';
}
.icon-home:before {
  content: '\e61c';
}
.icon-chevron-bold:before {
  content: '\e61d';
}
.icon-o-handset:before {
  content: '\e610';
}
.icon-o-location:before {
  content: '\e599';
}
.icon-location-pin:before {
  content: '\e620';
}
.icon-search:before {
  content: '\e919';
}
.icon-facebook:before {
  content: '\e619';
}
.icon-twitter:before {
  content: '\e612';
}
.icon-blog-en:before {
  content: '\e90e';
}
.icon-linked-in-logo:before {
  content: '\e929';
}
.icon-youtube:before {
  content: '\e928';
}
.icon-back-to-top:before {
  content: '\e925';
}
/* END Global header and footer icons */

.icon-o-locationNoPad:before {
  content: '\e934';
}
.icon-fiber_bl_bg:before {
  content: '\e9c3';
}
.icon-tv_internet_bg:before {
  content: '\e97a';
}
.icon-wi_fi:before {
  content: '\e973';
}
.icon-easy_to_use_app:before {
  content: '\e997';
}
.icon-small_icon_arrow_pill:before {
  content: '\e908';
}
.icon-small_icon_expand:before {
  content: '\e90c';
}
.icon-small_icon_collapse:before {
  content: '\e90b';
}
.icon-o-chat-bubble:before {
  content: '\e604';
}
.icon-close:before {
  content: '\eaa2';
}
.icon-checkmark-circled:before {
  content: '\e921';
}
.icon-payper_viewbl_bg:before {
  content: '\e977';
}
.icon-01-13_user_profile_bg:before {
  content: '\e91d';
}
.icon-security_1_bl:before {
  content: '\e99e';
}
.icon-wirecare_bl_wot:before {
  content: '\e9cb';
}
.icon-web_security:before {
  content: '\e9c6';
}
.icon-parental_contro:before {
  content: '\e9ca';
}
.icon-fraudulent_emails:before {
  content: '\e9c7';
}
.icon-internet_speed:before {
  content: '\e98f';
}
.icon-data_ascending:before {
  content: '\e9c8';
}
.icon-update:before {
  content: '\e9c9';
}
.icon-calendar:before {
  content: '\e961';
}
.icon-support:before {
  content: '\e991';
}
.icon-o-clock:before {
  content: '\e606';
}
.icon-settings_wheels:before {
  content: '\e9a1';
}
.icon-o-headphones:before {
  content: '\e611';
}
.icon-usage:before {
  content: '\e9a2';
}
.icon-support_bl_wot:before {
  content: '\e9a7';
}
.icon-software_bl_wot:before {
  content: '\e9a4';
}
.icon-applications_bl_wot:before {
  content: '\e9a3';
}
.icon-user_guide_bl_wot:before {
  content: '\e9a5';
}
.icon-os_bl_wot:before {
  content: '\e9a6';
}
.icon-check-light:before {
  content: '\e603';
}
.icon-Big-expand:before {
  content: '\e949';
}
.icon-Big-collapse:before {
  content: '\e94a';
}
.icon-exclamation-circled:before {
  content: '\e922';
}
.icon-15-03_home_wireless_phone_bg:before {
  content: '\e91c';
}
.icon-09-02_laptop_bg:before {
  content: '\e91e';
}
.icon-mobile_phone:before {
  content: '\e9a8';
}
.icon-icon-dnld-speed:before {
  content: '\e979';
}
.icon-wifi_bl_wot:before {
  content: '\e97c';
}
.icon-tv_internet_mobile:before {
  content: '\e96d';
}
.icon-stopwatch_bl_wot:before {
  content: '\e97d';
}
.icon-tomorrowtech_bl_wot:before {
  content: '\e97b';
}
.icon-stopwatch_bl_bg:before {
  content: '\e97e';
}
.icon-tomorrow_tech_or_future_bl_bg:before {
  content: '\e95b';
}
.icon-small_icon_call:before {
  content: '\e909';
}
.icon-wireless:before {
  content: '\e9cc';
}
.icon-internet_speed_bg:before {
  content: '\e99b';
}
.icon-video_games:before {
  content: '\e995';
}
.icon-ranked-_n1:before {
  content: '\e996';
}
.icon-icon-download-speed-7:before {
  content: '\e994';
}
.icon-upload:before {
  content: '\e98a';
}
.icon-07-07_unlimited_wot:before {
  content: '\e902';
}
.icon-security_3_bl:before {
  content: '\e9a0';
}
.icon-hints_and_tips:before {
  content: '\e99d';
}
.icon-dnld-speed-2:before {
  content: '\e9cd';
}
.icon-dnld-speed-1:before {
  content: '\e9ce';
}
.icon-email:before {
  content: '\e992';
}
.icon-dnld-speed-9:before {
  content: '\e9c5';
}
.icon-dnld-speed-8:before {
  content: '\e9aa';
}
.icon-speed:before {
  content: '\e989';
}
.icon-speed_1:before {
  content: '\e98e';
}
.icon-data_internet_usage_2:before {
  content: '\e98b';
}
.icon-mcafee_security_software:before {
  content: '\e990';
}
.icon-data_internet_usage_1:before {
  content: '\e993';
}
.icon-small_icon_select_trigger_half:before {
  content: '\e920';
}
.icon-tvpay_perview_bl:before {
  content: '\e951';
}
.icon-icn_OneApp:before {
  content: '\e93d';
}
.icon-crave_tv:before {
  content: '\ea03';
}
.icon-info:before {
  content: '\e954';
}
.icon-modem:before {
  content: '\e999';
}
.icon-smart_network:before {
  content: '\e99a';
}
.icon-easy_to_use_app_w:before {
  content: '\e998';
}
.icon-wall_to_wall_coverage:before {
  content: '\e99c';
}
.icon-Small_Chat_now:before {
  content: '\e9ab';
}
.icon-mobile_tower:before {
  content: '\e98d';
}
.icon-data_internet_usage_6:before {
  content: '\e9a9';
}
.icon-data_internet_usage_3:before {
  content: '\e98c';
}
.icon-Request_a_callback:before {
  content: '\e948';
}
.icon-double_pods_bl_wot:before {
  content: '\ea24';
}
.icon-download_bl_wot:before {
  content: '\ea25';
}
.icon-special_event_bl_bg:before {
  content: '\ea23';
}
.icon-home_bl_bg:before {
  content: '\ea22';
}
.icon-5G_bl_bg:before {
  content: '\ea21';
}
.icon-01-07_group_partipants_bg:before {
  content: '\e911';
}
.icon-fiber_bl_bg:before {
  content: '\e9c3';
}
.icon-video_games_bl_wot:before {
  content: '\ea16';
}
.icon-mobile_tower_signal_bl_bg:before {
  content: '\ea05';
}
.icon-data_internet_usage_5:before {
  content: '\e9cf';
}

/* CUSTOM CSS ICON FROM SHOP */
.checked-list li [class^='icon-'],
.checked-list li [class*=' icon-'] {
  color: #00549a;
  font-size: 16px;
  line-height: 18px;
  margin-right: 10px;
}

.custom-select-icon {
  position: absolute;
  top: 50%;
  right: 15px;
  color: #00549a;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-60%);
}

.huge-offer-details .offer-price-col .call-icon-block {
  margin-top: 15px;
}

.force-icon-font.icon {
  font-family: 'bell-icon';
}

.force-icon-font.icon2 {
  font-family: 'bell-icon2';
}

.force-icon-font.icon:before,
.force-icon-font.icon2:before {
  position: static;
}

.anchor-text + .anchor-icon {
  margin-left: 10px;
}

.anchor-icon ~ .anchor-text {
  margin-left: 10px;
}

/*For Custom Play button hover effect*/
.iconBlock:hover .icon2.path1:before {
  opacity: 0.6;
}
.iconBlock .icon2.path1:before {
  top: -6px;
  left: 2px;
  opacity: 0.3;
  color: #00549a;
}
.iconBlock .icon2.path1.txtBlack:before {
  color: #000;
}
.iconBlock .icon2.path2:before {
  top: -6px;
}

.iconBlockBlack:hover .icon2.path1:before {
  opacity: 0.6;
}
.iconBlockBlack .icon2.path1:before {
  top: -6px;
  opacity: 0.3;
  color: #000;
}
.iconBlockBlack .icon2.path2:before {
  top: -6px;
  left: -2px;
}

.icon-play_hover_multi .icon2.path1,
.icon-play_hover_multi .icon2.path2 {
  font-size: 82px;
}
/*For Custom Play button hover effect*/

footer .footer-icon .icon-o-chat-bubble,
footer .footer-icon .icon-o.icon-o-handset,
footer .footer-icon .icon-o.icon-o-location {
  top: -8px;
  left: 0;
}

/* CUSTOM CSS ICON FROM SHOP */

/* mobile only */
@media (max-width: 767.98px) {
  .more-ways-to-shop .content-ways-to-shop > li > a .anchor-icon {
    flex-shrink: 0;
    height: 60px;
    width: 60px;
  }

  .more-ways-to-shop .content-ways-to-shop > li > a .anchor-icon:before {
    font-size: 60px;
  }
}

/* tablet and larger */
@media (min-width: 768px) {
  .more-ways-to-shop .content-ways-to-shop > li > a .anchor-icon {
    height: 74px;
    margin-bottom: 15px;
    width: 74px;
  }

  .more-ways-to-shop .content-ways-to-shop > li > a .anchor-icon:before {
    font-size: 68px;
  }
}

/* desktop and larger */
@media (min-width: 992px) {
  .huge-offer-details
    .offer-price-col
    .call-icon-block
    .anchor-text:not(:last-child) {
    display: none;
  }

  .huge-offer-details
    .offer-price-col
    .call-icon-block
    .anchor-text:last-child {
    display: inline;
  }
}

@media all and (-ms-high-contrast: active) and (max-width: 480px),
  (-ms-high-contrast: none) and (max-width: 480px) {
  .icon-o-locationNoPad:before {
    /* Enter your style code */
    padding-right: 15px;
  }
}
