import { Injectable, AjaxServices, CommonFeatures, AjaxOptions } from "bwtk";
import { Observable } from "rxjs";
const { BaseClient } = CommonFeatures;

import { Config } from "./Config";
import { IFetchBillsEpicResponse } from "./models";

@Injectable
export class Client extends BaseClient {
  constructor(ajax: AjaxServices, private config: Config) {
    super(ajax);
  }

  getBriteBillBillExplainerApi() {
    const urlToAppend = `${this.config.billExplainerPath}`;
    const response = this.get<IFetchBillsEpicResponse>(urlToAppend, null, this.options);
    return response;
  }

  getBriteBillBillExplainerApiMockData() {
    const briteBillBillExplainerApiMock = require("../dev/mock/PBE_OtherV1.json");
    return (briteBillBillExplainerApiMock) ?
      Observable.of(briteBillBillExplainerApiMock) :
      Observable.throw(briteBillBillExplainerApiMock);
  }

  getPBEActionURLData(actionURL: string) {
    const ajaxOptions = { ...this.options, url: this.config.pbeBaseActionURL }
    const response = this.get<any>(actionURL, null, ajaxOptions);
    return response;
  }

  get options(): AjaxOptions {
    return {
      url: "",
      cache: false,
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        "brand": this.config.brand,
        "userID": this.config.userID,
        "province": this.config.Province,
        "channel": this.config.channel,
        "accept-language": this.config.language,
        "X-CSRF-TOKEN": this.config.CSRFToken,
      }
    };
  }
}
