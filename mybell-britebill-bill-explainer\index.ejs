<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>Widget Demo</title>
    <meta charset="utf8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2,user-scalable=yes">
    <link href="resources/core/css/bootstrap.min.css" rel="stylesheet">
    <link href="resources/content/css/bill-redesign-icons.css" rel="stylesheet">
    <link href="resources/core/css/bell.css" rel="stylesheet">
    <link href="resources/core/css/allBrowsers_framework.css" rel="stylesheet">
    <link href="resources/core/css/global-nav-prime.css" rel="stylesheet">
    <link href="resources/core/css/global-nav-mybell.css" rel="stylesheet">
    <link href="resources/core/css/widgets/slick.css" rel="stylesheet">
    <link href="resources/core/css/carets.css" rel="stylesheet">
    <link href="resources/content/css/bell-slick.css" rel="stylesheet">
    <link href="resources/content/css/bill-redesign.css" rel="stylesheet">
    <script src="resources/core/js/jquery-3.5.1.min.js"></script>
</head>

<body class="bill-redesign-accss">
    <div style="text-align:center;">
        <button id="changeLocale">mybell-britebill-bill-explainer Change page locale to <strong></strong></button>
    </div>

    <br /><br />

    <main id="container" class="bill-redesign bgWhite"></main>

    <script src="../node_modules/bwtk/dist/polyfill/polyfill.min.js"></script>
    <script src="../node_modules/bwtk/dist/requirejs.min.js"></script>
    <script src="../node_modules/bwtk/dist/loader.min.js"></script>
    <script src="resources/core/js/widgets/jquery.lazy.min.js"></script>
    <script src="resources/core/js/widgets/popper.min.js"></script>
    <script src="resources/core/js/widgets/slick.min.js"></script>
    <script src="resources/core/js/bootstrap.min.js"></script>
    <script src="resources/core/js/global-nav.js"></script>
    <script>
        function loadScript(source) {
            var script = document.createElement("script");
            script.src = source;
            document.body.append(script);
        };
        var language = "en";
        var links = {
            PAY_NOW: "#paynow",
            EDIT_NICKNAMES: "#editnicknames"
        };
        document.documentElement.lang = language;
        var config = {
            "singleban/billing/britebill-bill-explainer/url": "http://localhost:8883/",
            "singleban/billing/britebill-bill-explainer/language": language,
            "singleban/billing/britebill-bill-explainer/getBillsList": "mock/getSingleBanBill.json",
            "localization.defaultLocale": language
        };
        var loader = new BwtkLoader();
        loader.setBundle("../mybell-britebill-bill-explainer-bundle.min.js");        
        loader.start(function (require) {
            var bwtk = require("bwtk");
            var bundle = require("bundle");

            bundle.initialize(config, "container");

            var store = bwtk.ServiceLocator.instance.getService(bwtk.CommonServices.Store);
            store.dispatch(bwtk.setLocale(language));

            store.createGlobalActionListener(function (action) {
                console.log(action);
                try{
                    switch(action.type) {
                        case "FETCH_BILLS_COMPLETED":
                            loadScript("resources/core/js/bell.js");
                            loadScript("resources/content/js/bell-slick.js");
                            loadScript("resources/content/js/bill-redesign.js");
                            break;
                        case "INTERNAL_ERROR":
                            console.error("ALERT :: ", action, action.data);
                            break;
                    }
                } catch(error) {
                    console.log(error);
                }
            });
        });

        var updateLocaleButton = function (btn, loc) {
            btn.getElementsByTagName("strong")[0].innerHTML = loc.locale === "en" ? "FR" : "EN";
        }
        
    </script>
</body>

</html>