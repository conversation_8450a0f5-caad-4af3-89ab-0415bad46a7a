// put resize event callback functions here
(function ($) { 
   

    $(window).on('resize', function () {
        // Added condition for unli share sliders
        var card_sliders = $('.infoblock-slider');

    if (card_sliders) {
        if ($(window).width() < 992) {
            card_sliders.not('.slick-initialized').slick();
        }
        removeBtnFirstLastSlider(card_sliders);
    }

    });

    $(window).on('resize', function () {
        // Added condition for unli share sliders
        var card_sliders = $('.infoblock-slider-t');

    if (card_sliders) {
        if ($(window).width() < 992) {
            card_sliders.not('.slick-initialized').slick();
        }
        removeBtnFirstLastSlider(card_sliders);
    }

    });

    $(window).on('resize', function () {
        // Added condition for unli share sliders
        var card_sliders = $('.infoblock-slider-t-details');

    if (card_sliders) {
        if ($(window).width() < 992) {
            card_sliders.not('.slick-initialized').slick();
        }
        removeBtnFirstLastSlider(card_sliders);
    }

    });


    function removeBtnFirstLastSlider(sliders) {
        sliders.each(function (key, item) {
            $(this).find('.slick-arrow.slick-disabled').removeAttr('style');
        });
    }


})(jQuery);

//recall same-height function when load is finish
$(window).on('load', function () {
    processSameHeightElements();
});

$(window).trigger('resize');

$(document).ready(function () {
    $('.accordion-accessible-toggle-unli-share').click(function (e) {
        var $this = $(this),
            $toggleIcon = $this.find('span.icon, i').first(),
            $expandedAttr = $this.attr('aria-expanded') === "true" ? "false" : "true",
            iconExpand = $this.data('icon-expand') || "icon-exapnd-outline-circled",
            iconCollapse = $this.data('icon-collapse') || "icon-collapse-outline-circled",
            newIconClass = $this.attr('aria-expanded') === "true" ? iconExpand : iconCollapse;

        $this.parent().closest(".accordion-wrap").find('.collapse-accordion-accessible-toggle').slideToggle();
        $toggleIcon.removeClass(iconExpand + ' ' + iconCollapse);
        $toggleIcon.addClass(newIconClass);
        $this.attr('aria-expanded', $expandedAttr);
    });

    slickHideCards();
});


/*-------------------------------------*/

//More or Less Accordion Function in mobile view
var displayAccordionMoreAndLess = {
    init: function () {
        var self = this;
        self.addEventListener();
        self.addAriaHidden();
        self.onMobileResize();
    },
    addEventListener: function () {
        $('.expander-description-control').on('click', function (e) {
            var $this = $(this);

            if ($this.attr('aria-expanded') == 'false') {
                $this.prev().css('max-height', '10000px').attr('aria-hidden', 'false');
                $this.attr('aria-expanded', 'true');

            }
            else {
                $this.prev().removeAttr('style').attr('aria-hidden', 'true');
                $this.attr('aria-expanded', 'false');
            }
            e.stopImmediatePropagation();

        });
    },
    onMobileResize: function () {
        var self = this;
        $(window).resize(function () {
            self.addAriaHidden();
        });
    },
    addAriaHidden: function () {
        if (window.matchMedia('(max-width: 767px)').matches) {
            if ($('.expander-description[aria-hidden]').length <= 0) {
                $('.expander-description').attr('aria-hidden', 'true');
            }
        }
        else {
            $('.expander-description').removeAttr('style').removeAttr('aria-hidden');
        }
    }

};
if ($('.expander-description-control').length > 0) {
    displayAccordionMoreAndLess.init();
}



$(function () {
    // This is to automatically update expand / collapse icon on single expand accordion. Upon clicking other accordion item, this will update the currently active accordion's icon
    $(document).on('hide.bs.collapse', '.single-expand', function (e) {
        $(this).closest('.accordion-wrap').find('.icon').removeClass('icon-small_icon_collapse').addClass('icon-small_icon_expand');
    });
    $(document).on('hide.bs.collapse', '[data-expand-type="single"]', function (e) {
        // for all screen sizes
        let expandIcon = $(this).closest('.accordion-wrap').find('.collapse-trigger').data('icon-expand');
        let collapseIcon = $(this).closest('.accordion-wrap').find('.collapse-trigger').data('icon-collapse');
        $(this).closest('.accordion-wrap').find('.icon:not([data-icon="xs"])').first().removeClass(collapseIcon).addClass(expandIcon);
        //for custom mobile icon
        let expandIconXs = $(this).closest('.accordion-wrap').find('.collapse-trigger').data('icon-expand-xs');
        let collapseIconXs = $(this).closest('.accordion-wrap').find('.collapse-trigger').data('icon-collapse-xs');
        if (expandIconXs && collapseIconXs) {
            $(this).closest('.accordion-wrap').find('[data-icon="xs"]').addClass(expandIconXs).removeClass(collapseIconXs);
        }
    });
    let windowSize = $(window).width();
    $(window).resize(function () {
        windowSize = $(window).width();
    });
    // Accordion custom mobile icon
    $('.collapse-trigger').on('click', function (e) {
        if (windowSize < 760) {
            let accordion = $(this);
            let expandIconXs = $(this).data('icon-expand-xs');
            let collapseIconXs = $(this).data('icon-collapse-xs');
            if (expandIconXs && collapseIconXs) {
                if (accordion.attr('aria-expanded') !== "true") {
                    accordion.closest('.accordion-wrap').find('.icon[data-icon="xs"]').addClass(collapseIconXs).removeClass(expandIconXs);
                }

            }
        }
    });
});

// Switch between dynamic title of accordion
$(document).on('show.bs.collapse hide.bs.collapse', '[data-title-type="dynamic"]', function (e) {
    var title = $(this).closest('.accordion-wrap').find('.accordion-title');
    var expandTitle = title.data('expand-title');
    var collapseTitle = title.data('collapse-title');
    if (e.type === 'hide') {
        title.html(expandTitle);
    }
    else if (e.type === 'show') {
        title.html(collapseTitle);
    }
});
$('.clickable-card').on('click', '[data-toggle="modal"]', function (e) {
    e.preventDefault();
});
$('.clickable-card').on('click', '.tooltip-interactive', function (e) {
    e.preventDefault();
});

$(function () {
    var selectHoverList = $('.select-hover');
    selectHoverList.on('mouseenter focus click touch', function () {
        $('.select-hover').next().hide();
        $(this).next().show();
    });

    selectHoverList.next().on('mouseleave', function () {
        $(this).hide();
    });
});


function slickHideCards() {
    // Start slick event handler to hide offsreen card slides on infoblock sliders
    $('.hiddenInactive').on('afterChange', function () {
        var $this = $(this);
        $(this).find('.slick-slide[aria-hidden="true"]').addClass('offscreen');
        setTimeout(function () {
            $this.find('.slick-slide[tabindex=-1]').attr("aria-hidden", true).addClass('offscreen');
        }, 0);

        $(this).removeClass('sliding');

    });
    $('.hiddenInactive').on('beforeChange', function () {
        $(this).find('.slick-slide[aria-hidden="true"]').removeClass('offscreen');

        $(this).addClass('sliding');
    });
    $('.hiddenInactive').on('init', function () {
        $(this).find('.slick-slide[aria-hidden="true"]').addClass('offscreen');
    });
    // End slick event handler to hide offsreen card slides on infoblock sliders
}