import * as React from "react";
import { ExtractedFormattedMessage } from "singleban-components";
import { IBillExplainerHeader } from "./IBillExplainerHeader";

const BillExplainerHeader = (props: IBillExplainerHeader) => {

    const configLink = props.configLinks ? props.configLinks.PREVIOUS_PAGE_URL : "/";
    return (
        <header className="standard-step-flow-header d-flex flex-column">
            <div className="standard-step-flow-header-upper d-flex align-items-center bgBlue txtWhite">
                <div className="container liquid-container d-flex align-items-center pad-v-20 pad-v-sm-10 pad-v-xs-15 pad-h-15">
                    <div className="back col-3 col-sm-2 d-flex align-items-center">
                        <a className="d-inline-flex txtNoUnderline txtWhite" id="mybell_gc_HOME_DESK" href={configLink} title="Bell home page" aria-label="Bell home page">
                            <span className="icon-bill-redesign icon-chevron-left txtSize18 txtSize13-xs"></span>
                            <span className="surtitle txtWhite d-none d-sm-inline pad-l-5"><ExtractedFormattedMessage id="HEADER_BACK" /></span>
                        </a>
                    </div>
                    <div className="col-6 col-sm-8">
                        <div className="d-flex flex-column align-items-center">
                            <h1 className="col1 text-center subtitle-2 txtWhite">
                                <ExtractedFormattedMessage id="BILL_EXPLAINER_HEADER1" />
                            </h1>
                        </div>
                    </div>
                    <div className="col-3 col-sm-2 text-right">
                        {!props.isUXPMode && <button id="btn-logout" className="btn btn-primary-white d-none d-sm-inline" type="button" onClick={() => props.handleLogout()}><ExtractedFormattedMessage id="HEADER_LOGOUT" /></button>}
                    </div>
                </div>
            </div>
        </header>
    );
};

export default BillExplainerHeader;