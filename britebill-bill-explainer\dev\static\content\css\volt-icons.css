/*START Virgin ICON FONTS*/
@font-face {
    font-family: 'volt-icons';
    src: url('../fonts/volt-icons.eot?qkura8');
    src: url('../fonts/volt-icons.eot?qkura8#iefix') format('embedded-opentype'), url('../fonts/volt-icons.ttf?qkura8') format('truetype'), url('../fonts/volt-icons.woff?qkura8') format('woff'), url('../fonts/volt-icons.svg?qkura8#icomoon') format('svg');
    font-weight: normal;
    font-style: normal
}

.volt-icon{font-style:normal;speak:none;font-weight:400;font-variant:normal;text-transform:none;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}
.volt-icon:before{font-family:'volt-icons';position:relative;}

.icon-4G:before {
    content: "\e904";
}
.icon-911:before {
    content: "\e905";
}
.icon-add_ons:before {
    content: "\e906";
}
.icon-android:before {
    content: "\e907";
}
.icon-angel:before {
    content: "\e908";
}
.icon-antenna:before {
    content: "\e909";
}
.icon-apple:before {
    content: "\e90a";
}
.icon-atom:before {
    content: "\e90b";
}
.icon-bag:before {
    content: "\e90c";
}
.icon-battery:before {
    content: "\e90d";
}
.icon-battery_charged:before {
    content: "\e90e";
}
.icon-bbm:before {
    content: "\e90f";
}
.icon-beats:before {
  content: "\e910";
}
.icon-big_X:before {
    content: "\e911";
}

.icon-bill:before {
    content: "\e912";
}
.icon-bill1:before {
    content: "\e913";
}
.icon-bill2:before {
    content: "\e914";
}
.icon-bill3:before {
    content: "\e915";
}
.icon-blackberry:before {
    content: "\e916";
}
.icon-bluetooth:before {
    content: "\e917";
}
.icon-bridge:before {
    content: "\e918";
}
.icon-brokescreen:before {
    content: "\e919";
}
.icon-bulb:before {
    content: "\e91a";
}
.icon-calculator:before {
    content: "\e91b";
}
.icon-calendar:before {
    content: "\e91c";
}
.icon-camera:before {
    content: "\e91d";
}
.icon-canada_leaf:before {
    content: "\e91e";
}
.icon-canada_shop:before {
    content: "\e91f";
}
.icon-card:before {
    content: "\e920";
}
.icon-charger:before {
    content: "\e921";
}
.icon-checkmark:before {
    content: "\e922";
}
.icon-chip:before {
    content: "\e923";
}
.icon-clavardage_endirect:before {
    content: "\e924";
}
.icon-connectivity:before {
    content: "\e925";
}
.icon-controller:before {
    content: "\e926";
}

.icon-dashboard:before {
    content: "\e927";
}
.icon-dashboard2:before {
    content: "\e928";
}
.icon-down_arrow:before {
    content: "\e929";
}
.icon-drink:before {
    content: "\e92a";
}
.icon-earphones:before {
    content: "\e92b";
}
.icon-edit:before {
    content: "\e92c";
}
.icon-email:before {
    content: "\e92d";
}
.icon-expand_m:before {
    content: "\e92e";
}
.icon-eye:before {
    content: "\e92f";
}
.icon-eye2:before {
    content: "\e930";
}
.icon-eye3:before {
    content: "\e931";
}
.icon-flash:before {
    content: "\e932";
}
.icon-funky:before {
    content: "\e933";
}
.icon-gaming:before {
    content: "\e934";
}
.icon-glasses:before {
    content: "\e935";
}
.icon-graph:before {
    content: "\e936";
}
.icon-graph2:before {
    content: "\e937";
}
.icon-hand:before {
    content: "\e938";
}
.icon-hanger:before {
    content: "\e939";
}
.icon-HD:before {
    content: "\e93a";
}
.icon-headphones:before {
    content: "\e93b";
}
.icon-heart:before {
    content: "\e93c";
}
.icon-hearts:before {
    content: "\e93d";
}
.icon-home:before {
    content: "\e93e";
}
.icon-home_phone:before {
    content: "\e93f";
}
.icon-icon_1:before {
    content: "\e940";
}
.icon-icon_2:before {
    content: "\e941";
}
.icon-icon_3:before {
    content: "\e942";
}

.icon-ios:before {
    content: "\e943";
}
.icon-key:before {
    content: "\e944";
}
.icon-keyboard:before {
    content: "\e945";
}
.icon-laugh:before {
    content: "\e946";
}
.icon-link:before {
    content: "\e947";
}
.icon-link_1:before {
    content: "\e948";
}
.icon-lips:before {
    content: "\e949";
}
.icon-lips2:before {
    content: "\e94a";
}
.icon-live_chat:before {
    content: "\e94b";
}
.icon-lock:before {
    content: "\e94c";
}
.icon-love:before {
    content: "\e94d";
}
.icon-LTE:before {
    content: "\e94e";
}
.icon-magic:before {
    content: "\e94f";
}
.icon-messaging:before {
    content: "\e950";
}
.icon-mobile_account:before {
    content: "\e951";
}
.icon-mobile_fire:before {
    content: "\e952";
}
.icon-modem:before {
    content: "\e953";
}
.icon-modem_wifi:before {
    content: "\e954";
}
.icon-modem3:before {
    content: "\e955";
}
.icon-modem2:before {
    content: "\e956";
}
.icon-music:before {
    content: "\e957";
}

.icon-network:before {
    content: "\e958";
}
.icon-network2:before {
    content: "\e959";
}
.icon-network3:before {
    content: "\e95a";
}
.icon-object:before {
    content: "\e95b";
}
.icon-object_2:before {
    content: "\e95c";
}
.icon-object_3:before {
    content: "\e95d";
}
.icon-passport:before {
    content: "\e95e";
}
.icon-password:before {
    content: "\e95f";
}
.icon-pen:before {
    content: "\e960";
}
.icon-phone:before {
    content: "\e961";
}
.icon-phone_1:before {
    content: "\e962";
}
.icon-phone_case:before {
    content: "\e963";
}
.icon-phone_charging:before {
    content: "\e964";
}
.icon-phone2:before {
    content: "\e965";
}
.icon-phone3:before {
    content: "\e966";
}
.icon-pin:before {
    content: "\e967";
}
.icon-plus:before {
    content: "\e968";
}
.icon-Print:before {
    content: "\e969";
}
.icon-profile:before {
    content: "\e96a";
}
.icon-question:before {
    content: "\e96b";
}
.icon-RAM:before {
    content: "\e96c";
}
.icon-recording:before {
    content: "\e96d";
}
.icon-resizescreen:before {
    content: "\e96e";
}
.icon-router:before {
    content: "\e96f";
}
.icon-run:before {
    content: "\e970";
}
.icon-sad:before {
    content: "\e971";
}
.icon-savings:before {
    content: "\e972";
}
.icon-savings1:before {
    content: "\e973";
}

.icon-science:before {
    content: "\e974";
}
.icon-science_2:before {
    content: "\e975";
}
.icon-screaming:before {
    content: "\e976";
}
.icon-screens:before {
    content: "\e977";
}
.icon-search:before {
    content: "\e978";
}
.icon-settings:before {
    content: "\e979";
}
.icon-shining_love:before {
    content: "\e97a";
}
.icon-sick:before {
    content: "\e97b";
}
.icon-SIM:before {
    content: "\e97c";
}
.icon-sleepy:before {
    content: "\e97d";
}
.icon-smile:before {
    content: "\e97e";
}
.icon-smile2:before {
    content: "\e97f";
}
.icon-snapchat:before {
    content: "\e980";
}
.icon-snowflake:before {
    content: "\e981";
}
.icon-snowflake2:before {
    content: "\e982";
}
.icon-star:before {
    content: "\e983";
}
.icon-star_moving:before {
    content: "\e984";
}
.icon-star2:before {
    content: "\e985";
}
.icon-star3:before {
    content: "\e986";
}
.icon-strength:before {
    content: "\e987";
}
.icon-tablet:before {
    content: "\e988";
}
.icon-TL:before {
    content: "\e989";
}
.icon-text:before {
    content: "\e98a";
}
.icon-text_canada:before {
    content: "\e98b";
}
.icon-text_internet:before {
    content: "\e98c";
}
.icon-text_world:before {
    content: "\e98d";
}
.icon-text_world1:before {
    content: "\e98e";
}
.icon-tool:before {
    content: "\e98f";
}
.icon-tools:before {
    content: "\e990";
}
.icon-touch_screen:before {
    content: "\e991";
}
.icon-touch_screen2:before {
    content: "\e992";
}
.icon-trash:before {
    content: "\e993";
}
.icon-travelling:before {
    content: "\e994";
}
.icon-trophe:before {
    content: "\e995";
}
.icon-truck:before {
    content: "\e996";
}
.icon-truck2:before {
    content: "\e997";
}
.icon-tv:before {
    content: "\e998";
}
.icon-tv_wifi:before {
    content: "\e999";
}
.icon-unite:before {
    content: "\e99a";
}
.icon-Unlink:before {
    content: "\e99b";
}
.icon-unlock:before {
    content: "\e99c";
}
.icon-up_arrow:before {
    content: "\e99d";
}
.icon-down_arrow1:before {
    content: "\e9bc";
}
.icon-vibrate:before {
    content: "\e99e";
}
.icon-VIP:before {
    content: "\e99f";
}
.icon-wallet:before {
    content: "\e9a0";
}
.icon-windows:before {
    content: "\e9a1";
}
.icon-world:before {
    content: "\e9a2";
}
.icon-x_box:before {
    content: "\e9a3";
}
.icon-instagram:before {
    content: "\e9a4";
}
.icon-facebook:before {
    content: "\e9a5";
}
.icon-google_plus:before {
    content: "\e9a6";
}
.icon-twitter:before {
    content: "\e9a7";
}
.icon-youtube:before {
    content: "\e9a8";
}
.icon-Down_arrow1:before {
    content: "\e9bd";
}
.icon-Right_arrow:before {
    content: "\e9a9";
}
.icon-Left_arrow:before {
    content: "\e9be";
}
.icon-Big_collapse:before {
    content: "\e9aa";
}
.icon-Big_expand:before {
    content: "\e9ab";
}
.icon-collapse_m:before {
    content: "\e9ac";
}
.icon-plus2:before {
    content: "\e9ad";
}

/* Icon with color path */
.icon-Collapse .path1:before {
    content: "\e9ae";
    color: #2390b9;
}
.icon-Collapse .path2:before {
    content: "\e9af";
    color: #fff;
    margin-left: -1em;
}
.icon-Expand .path1:before {
    content: "\e9b0";
    color: #2390b9;
}
.icon-Expand .path2:before {
    content: "\e9b1";
    color: #fff;
    margin-left: -1em;
}
.icon-warning .path1:before {
    content: "\e9b2";
    color: #c00;
}
.icon-warning .path2:before {
    content: "\e9b3";
    color: #fff;
    margin-left: -1em;
}
.icon-delete_x .path1:before {
    content: "\e9b4";
    color: #999;
}
.icon-delete_x .path2:before {
    content: "\e9b5";
    color: #fff;
    margin-left: -1em;
}
.icon-question_bg .path1:before {
    content: "\e9b6";
    color: #2390b9;
}
.icon-question_bg .path2:before {
    content: "\e9b7";
    color: #fff;
    margin-left: -1em;
}
.icon-Big_check_confirm .path1:before {
    content: "\e9b8";
    color: #2c9e25;
}
.icon-Big_check_confirm .path2:before {
    content: "\e9b9";
    color: #fff;
    margin-left: -1em;
}
.icon-BIG_WARNING .path1:before {
    content: "\e900";
    color: #e99e00;
}
.icon-BIG_WARNING .path2:before {
    content: "\e901";
    color: #fff;
    margin-left: -1em;
}
.icon-Big_info .path1:before {
    content: "\e902";
    color: #2390b9;
}
.icon-Big_info .path2:before {
    content: "\e9ba";
    color: #fff;
    margin-left: -1em;
}
.icon-info .path1:before {
    content: "\e903";
    color: #a4a6a7;
}
.icon-info .path2:before {
    content: "\e9bb";
    color: #fff;
    margin-left: -1em;
}

.icon-Payment .path1:before {
    content: "\e9bf";
    color: #f4f4f4;
}
.icon-Payment .path2:before {
    content: "\e9c0";
    color: #191617;
    margin-left: -3.2890625em;
}
.icon-Payment .path3:before {
    content: "\e9c1";
    color: #0a5296;
    margin-left: -3.2890625em;
}
.icon-Payment .path4:before {
    content: "\e9c2";
    color: #f6a500;
    margin-left: -3.2890625em;
}
.icon-Payment .path5:before {
    content: "\e9c3";
    color: #c1c1c1;
    margin-left: -3.2890625em;
}
.icon-Payment .path6:before {
    content: "\e9c4";
    color: #ff0016;
    margin-left: -3.2890625em;
}
.icon-Payment .path7:before {
    content: "\e9c5";
    color: #ffa916;
    margin-left: -3.2890625em;
}
.icon-Payment .path8:before {
    content: "\e9c6";
    color: #ff6a00;
    margin-left: -3.2890625em;
}

.icon-Payment-cards .path1:before {
    content: "\e9c7";
    color: #f4f4f4;
}
.icon-Payment-cards .path2:before {
    content: "\e9c8";
    color: #0a5296;
    margin-left: -5.2890625em;
}
.icon-Payment-cards .path3:before {
    content: "\e9c9";
    color: #f6a500;
    margin-left: -5.2890625em;
}
.icon-Payment-cards .path4:before {
    content: "\e9ca";
    color: #192169;
    margin-left: -5.2890625em;
}
.icon-Payment-cards .path5:before {
    content: "\e9cb";
    color: #ff0016;
    margin-left: -5.2890625em;
}
.icon-Payment-cards .path6:before {
    content: "\e9cc";
    color: #ffa916;
    margin-left: -5.2890625em;
}
.icon-Payment-cards .path7:before {
    content: "\e9cd";
    color: #ff6a00;
    margin-left: -5.2890625em;
}
.icon-Payment-cards .path8:before {
    content: "\e9ce";
    color: #00adef;
    margin-left: -5.2890625em;
}
.icon-Payment-cards .path9:before {
    content: "\e9cf";
    color: #fff;
    margin-left: -5.2890625em;
}
.icon-close:before {
    content: "\e9d0";
}
.icon-close-outline:before {
    content: "\e9d1";
}
.icon-Big_info_bg1:before {
    content: "\e9d2";
}
.icon-arrow:before {
    content: "\e603";
}
.icon-arrow_right:before {
    content: "\e012";
}
.icon-arrow_left:before {
    content: "\e013";
}

/*Virgin Icon Font paths*/
.icon-VM-logo .path1:before {
    content: "\e9d3";
    color: #fff;
}
.icon-VM-logo .path2:before {
    content: "\e9d4";
    color: #c00;
    margin-left: -1.9658203125em;
}
.icon-VM-logo .path3:before {
    content: "\e9d5";
    color: #000;
    margin-left: -1.9658203125em;
}
.icon-VM-logo .path4:before {
    content: "\e9d6";
    color: #000;
    margin-left: -1.9658203125em;
}
.icon-VM-logo .path5:before {
    content: "\e9d7";
    color: #000;
    margin-left: -1.9658203125em;
}
.icon-VM-logo .path6:before {
    content: "\e9d8";
    color: #000;
    margin-left: -1.9658203125em;
}

.icon-hamburger-menu:before {
    content: "\e9d9";
}

.icon-triangle-down:before {
    content: "\e9da";
}

/* added Jan 08 2020 */
.icon-TV-guide-list:before {
    content: "\e9dc";
}
.icon-TV_App-Icon-neg:before {
    content: "\e9db";
}





































































