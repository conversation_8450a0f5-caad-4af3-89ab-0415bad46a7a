var resizeTimeoutFn = 0;
$(window).on('resize', function () {
    clearTimeout(resizeTimeoutFn);
    resizeTimeoutFn = setTimeout(function () {
        navIconBreadcrumbs();
        modifyBreadcrumbs();
    }, 200);
});
// Start document ready function
$(function () {
    //Start vertical tab override for link mode
    //Disable tab function vertical tabs to allow link to function
    $('ul.tabs.link-mode li').off('click');

    //Disable tab function on mobile and simulate a click on a link that is set in option's value'
    $('.custom-selection.link-mode').off('change');
    $('.custom-selection.link-mode').on('change', function () {
        let link = $(this).val();
        window.location.href = link;
    }); 
    //End vertical tab override for link mode
});
// End document ready function

// Start of navigation breadcrumbs function
function navIconBreadcrumbs() {
    let navBreadcrumbs = $('.nav-breadcrumbs');
    let navIcon = navBreadcrumbs.find('.icon');

    $(navIcon).each(function () {
        let counter;
        for (counter = 0; counter < navIcon.length; counter++) {

            if ($(window).width() < 768) {
                navIcon[counter].className = navIcon[counter].className.replace('icon-chevron-right', 'icon-chevron-left');
                navIcon[counter].className = navIcon[counter].className.replace('margin-l-10', 'margin-l-0');
            } else {
                navIcon[counter].className = navIcon[counter].className.replace('icon-chevron-left', 'icon-chevron-right');
                navIcon[counter].className = navIcon[counter].className.replace('margin-l-0', 'margin-l-10');
            }
        }


    });
}

function modifyBreadcrumbs() {
    let container = $('.nav-breadcrumbs').parent();
    let navBreadcrumbs = $('.nav-breadcrumbs');
    //Added 60px as padding for tablet view
    if (container.width() <= (navBreadcrumbs.width() + 30)) {
        navBreadcrumbs.addClass('dots');
    } else {
        navBreadcrumbs.removeClass('dots');
    }
}

$(window).on('load', function () {
    navIconBreadcrumbs();
    modifyBreadcrumbs();
});


// End of navigation breadcrumbs function

$(function () {
    // This is to automatically update expand / collapse icon on single expand accordion. Upon clicking other accordion item, this will update the currently active accordion's icon
    $(document).on('hide.bs.collapse', '.single-expand', function (e) {
        $(this).closest('.accordion-wrap').find('.icon').removeClass('icon-small_icon_collapse').addClass('icon-small_icon_expand');
    });
    $(document).on('hide.bs.collapse', '[data-expand-type="single"]', function (e) {
        // for all screen sizes
        let expandIcon = $(this).closest('.accordion-wrap').find('.collapse-trigger').data('icon-expand');
        let collapseIcon = $(this).closest('.accordion-wrap').find('.collapse-trigger').data('icon-collapse');
        $(this).closest('.accordion-wrap').find('.icon:not([data-icon="xs"])').first().removeClass(collapseIcon).addClass(expandIcon);
        //for custom mobile icon
        let expandIconXs = $(this).closest('.accordion-wrap').find('.collapse-trigger').data('icon-expand-xs');
        let collapseIconXs = $(this).closest('.accordion-wrap').find('.collapse-trigger').data('icon-collapse-xs');
        if (expandIconXs && collapseIconXs) {
            $(this).closest('.accordion-wrap').find('[data-icon="xs"]').addClass(expandIconXs).removeClass(collapseIconXs);
        }
    });
    let windowSize = $(window).width();
    $(window).resize(function () {
        windowSize = $(window).width();
    });
    // Accordion custom mobile icon
    $('.collapse-trigger').on('click', function (e) {
        if (windowSize < 760) {
            let accordion = $(this);
            let expandIconXs = $(this).data('icon-expand-xs');
            let collapseIconXs = $(this).data('icon-collapse-xs');
            if (expandIconXs && collapseIconXs) {
                if (accordion.attr('aria-expanded') !== "true") {
                    accordion.closest('.accordion-wrap').find('.icon[data-icon="xs"]').addClass(collapseIconXs).removeClass(expandIconXs);
                }
                else {
                    accordion.closest('.accordion-wrap').find('.icon[data-icon="xs"]').removeClass(collapseIconXs).addClass(expandIconXs);
                }
            }
        }
    });
});