﻿@font-face {
    font-family: 'virgin-plus-logo-icons';
    src: url('../fonts/vp-logo.eot?ozi4za');
    src: url('../fonts/vp-logo.eot?ozi4za#iefix') format('embedded-opentype'), url('../fonts/vp-logo.ttf?ozi4za') format('truetype'), url('../fonts/vp-logo.woff?ozi4za') format('woff'), url('../fonts/vp-logo.svg?ozi4za#vp-logo') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

.vp-logo { /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'virgin-plus-logo-icons' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}
    /*Virgin Icon Font paths*/
    .vp-logo .path1:before {
        content: "\e90f";
        color: rgb(255, 255, 255);
    }

    .vp-logo .path2:before {
        content: "\e910";
        margin-left: -2.3525390625em;
        color: rgb(255, 255, 255);
    }

    .vp-logo .path3:before {
        content: "\e911";
        margin-left: -2.3525390625em;
        color: rgb(255, 255, 255);
    }

    .vp-logo .path4:before {
        content: "\e912";
        margin-left: -2.3525390625em;
        color: rgb(255, 255, 255);
    }

    .vp-logo .path5:before {
        content: "\e913";
        margin-left: -2.3525390625em;
        color: rgb(255, 255, 255);
    }

    .vp-logo .path6:before {
        content: "\e914";
        margin-left: -2.3525390625em;
        color: rgb(255, 255, 255);
    }

    .vp-logo .path7:before {
        content: "\e915";
        margin-left: -2.3525390625em;
        color: rgb(255, 255, 255);
    }

    .vp-logo .path8:before {
        content: "\e916";
        margin-left: -2.3525390625em;
        color: rgb(225, 10, 10);
    }

    .vp-logo .path9:before {
        content: "\e917";
        margin-left: -2.3525390625em;
        color: rgb(225, 10, 10);
    }

    .vp-logo .path10:before {
        content: "\e918";
        margin-left: -2.3525390625em;
        color: rgb(225, 10, 10);
    }

    .vp-logo .path11:before {
        content: "\e919";
        margin-left: -2.3525390625em;
        color: rgb(255, 255, 255);
    }

    .vp-logo .path12:before {
        content: "\e91a";
        margin-left: -2.3525390625em;
        color: rgb(255, 255, 255);
    }

    .vp-logo .path13:before {
        content: "\e91b";
        margin-left: -2.3525390625em;
        color: rgb(255, 255, 255);
    }

    .vp-logo .path14:before {
        content: "\e91c";
        margin-left: -2.3525390625em;
        color: rgb(255, 255, 255);
    }
