#connector-search [type="reset"]:after,
#connector-search [type="submit"]:after,
#connector-search #voice_search {
    font-family: 'bell-icon';
    line-height: 1;
}

#connector-search #voice_search {
    width: 30px;
    display: none;
}

#voice_search_trigger,
#voice_search_trigger_footer {
    display: none;
}

body.voice-search-enabled #voice_search_trigger,
body.voice-search-enabled #voice_search_trigger_footer {
    display: block;
}

body.voice-recording-on #connector-search #voice_search,
body.voice-recording-on #voice_search_trigger,
body.voice-recording-on #voice_search_trigger_footer {
    opacity: .5;
}

body.voice-search-enabled #connector-search #voice_search {
    display: block;
}

#connector-search #voice_search:after {
    content: '\e970';
    -webkit-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
    font-size: 18px;
    color: #003778;
    position: relative;
    top: -3px;
}

body.search-active,
body.voice-active {
    margin: 0;
    height: 100%;
    overflow: hidden;
}

#voice-search-overlay {
    display: none;
    position: fixed;
    z-index: 10000;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    text-align: center;
    padding: 150px 20px 50px;
}

body.voice-active #voice-search-overlay {
    display: block;
}

.voice-search-close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    border: none;
    background-color: transparent;
}

.voice-search-close-btn:before,
.voice-search-close-btn:after {
    content: "";
    width: 2px;
    height: 26px;
    display: block;
    background-color: #d4d4d4;
    position: absolute;
    top: 50%;
    left: 50%;
}

.voice-search-close-btn:before {
    transform: translate(-50%, -50%) rotate(45deg);
}

.voice-search-close-btn:after {
    transform: translate(-50%, -50%) rotate(-45deg);
}

.voice-search-overlay-text {
    margin-bottom: 20px;
    font-family: 'bellslim_mediumregular';
    font-weight: 400;
    color: #111;
}

.voice-search-overlay-speak {
    font-size: 32px;
}

#voice-search-overlay p {
    font-size: 19px;
    font-family: 'bellslim_semiboldregular';
    position: absolute;
    top: 265px;
    margin: 0 auto;
    width: 100%;
    letter-spacing: -.5px;
    color: #00549a;
    left: 50%;
    transform: translateX(-50%);
}

.voice-search-icon-wrap-mic {
    color: #0d599b;
    height: 85px;
    width: 85px;
    background-color: #00549a;
    border-radius: 50%;
    border: 2px solid #1365a0;
    border-bottom: none;
    margin: 0 auto;
    box-shadow: 0px 2px 11px -1px rgba(0,0,0,0.2);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.voice-search-icon-wrap-mic svg {
    fill: #fff;
    padding: 5px;
    width: 100%;
}

.voice-search-icon-wrap-inner {
    border-radius: 50%;
    background-color: #9cbdd7;
    width: 130px;
    height: 130px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.voice-search-icon-wrap-outer {
    border-radius: 50%;
    background-color: #dfe9f3;
    width: 185px;
    height: 185px;
    margin: 0 auto;
    position: relative;
}

#voice-search-overlay.interim-recognition-on .voice-search-icon-wrap-inner {
    animation-name: pulsateInner;
    animation-duration: 1s;
    animation-timing-function: ease-in-out;
    animation-iteration-count: infinite;
}

#voice-search-overlay.interim-recognition-on .voice-search-icon-wrap-outer {
    animation-name: pulsateOuter;
    animation-duration: 1s;
    animation-delay: .3s;
    animation-timing-function: ease-in-out;
    animation-iteration-count: infinite;
}

#voice-search-overlay .icon:before {
    display: block;
    font-size: 50px;
    color: #fff;
    left: 50%;
    top: 50%;
    -webkit-transform: translateX(-50%) translateY(-50%);
    transform: translate(-50%, -50%);
}

.connector-voice-button,
.footer-voice-button {
    width: 30px;
    height: 37px;
    display: block;
    background: none;
    position: absolute;
    top: 0;
    right: 35px;
    border: none;
}

.footer-voice-button {
    height: 43px;
    width: 40px;
    padding: 0;
    right: 45px;
    top: 5px;
}

.connector-voice-button {
    display: none;
}

.connector-voice-button:after,
.footer-voice-button:after {
    font-family: 'bell-icon';
    line-height: 1;
    content: '\e970';
    display: block;
    font-size: 18px;
    color: #003778;
    position: relative;
    top: -1px;
}

.footer-voice-button:after {
    font-size: 24px;
    color: #2e2d33;
}

.voice-search-enabled .search-bar-footer [type="reset"] {
    right: 82px;
    margin-top: -29px;
}

/*search results*/
.connector .ui-autocomplete:empty {
    height: 0;
    padding-top: 0;
    padding-bottom: 0;
}
/* search connector*/
.connector .ui-menu .ui-menu-item:focus {
    outline: 1px dotted #6cb6db
}
.connector .ui-autocomplete {
    display: block;
    float: none;
    top: auto;
    right: auto;
    bottom: auto;
    left: auto;
    padding: 10px;
    transition: height .35s cubic-bezier(.55,0,.1,1), padding .35s cubic-bezier(.55,0,.1,1);
    background-color: #fff;
    box-shadow: 0 0 40px rgba(0,0,0, .3);
    position: absolute;
}

.connector ul.ui-autocomplete > li.ui-menu-item {
    padding: 7px 10px;
}

    .connector ul.ui-autocomplete > li.ui-menu-item > a.ui-corner-all {
        text-decoration: none;
        color: #555555;
        cursor: pointer;
        display: block;
    }

    .connector ul.ui-autocomplete > li.ui-menu-item:hover {
        background-color: #e1e1e1;
        border-radius: 3px;
    }

.connector .ui-autocomplete-term {
    font-weight: bold;
}

.connector .ui-autocomplete:empty {
    height: 0;
    padding-top: 0;
    padding-bottom: 0;
}

    .connector .ui-autocomplete:empty:after {
        content: none;
    }

.connector .ui-menu-item,
.connector .ui-menu-item > a {
    color: #000;
}

.connector .ui-menu-item {
    margin-top: 2px;
    margin-bottom: 2px;
}

    .connector .ui-menu-item > a:hover,
    .connector .ui-menu-item > a:active {
        background-color: #e2e2e2;
    }

    .connector .ui-menu-item .ui-autocomplete-term {
        font-weight: bold;
    }

#search-screen {
    display: none;
    position: fixed;
    z-index: 80;
    top: 50px;
    left: 0;
    width: 100vw;
    height: calc(100vh - 50px);
    background-color: rgba(0,0,0,.8);
    opacity: 0;
    transition: opacity .25s cubic-bezier(.55,0,.1,1);
    -webkit-transform: translate(-1000%, -1000%);
    -ms-transform: translate(-1000%, -1000%);
    transform: translate(-1000%, -1000%);
    cursor: pointer;
}
.connector .ui-autocomplete {
    z-index: 99999;
}
.connector .ui-state-active,
.connector .ui-widget-content .ui-state-active,
.connector .ui-widget-header .ui-state-active,
.connector a.ui-button:active,
.connector .ui-button:active,
.connector .ui-button.ui-state-active:hover {
    border: 1px solid #e1e1e1;
    background: #e1e1e1;
    color: #000;
}
/* search bar autocomplete*/
.search-bar-footer-wrap .caret_top-lg::after,
.search-bar-footer-wrap .caret_top-lg.caret_outline::before,
.search-bar-footer-wrap .caret_bottom-lg::after,
.search-bar-footer-wrap .caret_bottom-lg.caret_outline::before {
    left: 70px;
    border-width: 22px;
}

.search-bar-footer-wrap .ui-autocomplete {
    display: block;
    float: none;
    top: 215px !important;
    right: auto;
    bottom: auto;
    left: auto;
    padding: 0px;
    transition: height .35s cubic-bezier(.55,0,.1,1), padding .35s cubic-bezier(.55,0,.1,1);
    background-color: #fff;
    box-shadow: 0 0 40px rgba(0,0,0, .3);
    position: absolute;
}

.search-bar-footer-wrap ul.ui-autocomplete > li.ui-menu-item {
    padding: 10px 20px;
    list-style: none;
    cursor: pointer;
}

    .search-bar-footer-wrap ul.ui-autocomplete > li.ui-menu-item > a.ui-corner-all {
        text-decoration: none;
        color: #555555;
        cursor: pointer;
        display: block;
    }

    .search-bar-footer-wrap ul.ui-autocomplete > li.ui-menu-item:hover {
        background-color: #e1e1e1;
    }

.search-bar-footer-wrap .ui-autocomplete-term {
    font-weight: bold;
}

.search-bar-footer-wrap .ui-autocomplete:empty {
    height: 0;
    padding-top: 0;
    padding-bottom: 0;
}

    .search-bar-footer-wrap .ui-autocomplete:empty:after {
        content: none;
    }

.search-bar-footer-wrap .ui-menu-item,
.search-bar-footer-wrap .ui-menu-item > a {
    color: #000;
}

.search-bar-footer-wrap .ui-menu-item {
    margin: 2px -4px;
}

    .search-bar-footer-wrap .ui-menu-item > a:hover,
    .search-bar-footer-wrap .ui-menu-item > a:active {
        background-color: #e2e2e2;
    }

    .search-bar-footer-wrap .ui-menu-item .ui-autocomplete-term {
        font-weight: bold;
    }

.l-height-26 {
    line-height: 26px;
}


/*Media Queries*/
@media (max-width: 519.98px) {
    .connector .ui-autocomplete {
        position: fixed;
        left: 0;
        width: 100vw;
        height: calc(100vh - 50px);
        padding-top: 20px;
        padding-bottom: 20px;
        box-shadow: inset 0 17px 20px -11px rgba(0,0,0,0.2);
    }
}
@media (min-width: 520px) {
    .connector .ui-autocomplete {
        position: absolute;
    }
}
@media (min-width: 520px) and (max-width: 991.98px) {
    .connector .ui-autocomplete {
        width: 90%;
        left: 50%;
        /*top: 56px;*/
        -webkit-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        transform: translateX(-50%);
    }

    #search-screen {
        display: block;
    }

        #search-screen.active {
            opacity: 1;
            -webkit-transform: none;
            -ms-transform: none;
            transform: none;
        }
}
@media (min-width: 992px) {


    #connector-search [type="search"] {
        display: inline-block;
        border-radius: 18px;
    }

    #connector-search [type="search"],
    #connector-search [type="reset"],
    #connector-search [type="submit"] {
        height: 36px;
    }

    #connector-search [type="reset"],
    #connector-search [type="submit"],
    #connector-search #voice_search {
        position: absolute;
        right: 0;
        left: auto;
        top: 0;
        padding: 0;
        border: 0;
        background: none;
    }

    #connector-search [type="reset"] {
        right: 40px;
    }


    #connector-search [type="submit"] {
        width: 40px;
    }

    .connector .ui-autocomplete {
        top: 55px !important;
        transition: width .35s cubic-bezier(.55,0,.1,1), height .35s cubic-bezier(.55,0,.1,1), padding .35s cubic-bezier(.55,0,.1,1);
    }
}
@media (min-width: 992px) and (max-width: 1239.98px) {
    .connector .ui-autocomplete {
        left: calc(50% + -80px);
        width: 400px;
    }
}
@media (min-width: 1240px) {
    .connector .ui-autocomplete {
        left: 50%;
        width: 400px;
        z-index: 99999;
    }
}
@media(max-width:991.98px) {
    .connector .ui-autocomplete {
        top: 120px;
        z-index: 1110;
    }
}
@media(max-width:999px) {
    #connector-search #voice_search {
        position: absolute;
        top: 0;
        right: 45px;
        height: 55px;
        left: auto;
        width: 50px;
        padding: 0;
        border: 0;
        background: none;
    }

    #connector-search.active #voice_search {
        right: 80px;
    }

    #accessible-connector-search #connector-search [type="reset"]:focus::before,
    #accessible-connector-search #connector-search [type="submit"]:focus::before,
    #accessible-connector-search #connector-search #voice_search:focus::before {
        content: '';
        height: calc(100% - 6px);
        width: calc(100% - 6px);
        position: absolute;
        top: 3px;
        left: 3px;
        display: block;
        box-shadow: 0 0 3px 1px #5fb0fc, 0 0 3px 2px #8ec6fc;
        z-index: 1;
    }
}
@media (min-width: 1000px) {
    #connector-search [type="search"],
    #connector-search [type="reset"],
    #connector-search [type="submit"],
    #connector-search #voice_search {
        height: 36px;
    }

    .footer-voice-button {
        display: block;
    }

    #connector-search [type="reset"],
    #connector-search [type="submit"],
    #connector-search #voice_search {
        position: absolute;
        right: 0;
        left: auto;
        top: 0;
        padding: 0;
        border: 0;
        background: none;
    }

    #connector-search #voice_search {
        right: 34px;
    }

    #connector-search.active #voice_search {
        right: 70px;
    }
}



