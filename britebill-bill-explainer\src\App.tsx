import * as React from "react";
import { connect } from "react-redux";
import { IntlProvider } from "react-intl";

import { IAppOwnProps, IAppStateToProps, IAppDispatchToProps, IAppMergedProps, IStoreState, IRequestStatus } from "./models";
import BillExplainer from "./views";
import PBEModalExplainer from "./views/PBEPopOver";

const AppComponent = (props: IAppMergedProps) => {
  const { localization, fetchBillsStatus, pbeData, loadedFromOtherWidget, pbeCategory, isPbeModalLinkDisabled } = props;
  const { fullLocale, messages, formats } = localization;
  const { description, billCloseDate, startDate, endDate, subscriberDetails, chargeItems, currentPeriodEndDate, titleKey, descriptionKey, detailedDescKey, previousPeriodEndDate, previousPeriodStartDate, currentPeriodStartDate, transactions, useLegendsForDiagram } = props;

  return (
    <IntlProvider locale={fullLocale} messages={messages} formats={formats}>
      <>
        {
          loadedFromOtherWidget ?
            pbeData != null && pbeData.pbeId != null ? <PBEModalExplainer pbe={pbeData} locale={fullLocale} isPBEModalLinkDisabled={props.isPbeModalLinkDisabled} /> : null
            : fetchBillsStatus === IRequestStatus.COMPLETED &&
            <BillExplainer
              configLinks={props.configLinks}
              pbe={pbeData}
              pbeCategory={pbeCategory}
              description={description}
              billCloseDate={billCloseDate}
              cycleStartDate={startDate}
              cycleEndDate={endDate}
              subscriberDetails={subscriberDetails}
              chargeItems={chargeItems}
              isUXPMode={props.isUXPMode}
              currentPeriodEndDate={currentPeriodEndDate}
              currentPeriodStartDate={currentPeriodStartDate}
              descriptionKey={descriptionKey}
              detailedDescKey={detailedDescKey}
              previousPeriodEndDate={previousPeriodEndDate}
              previousPeriodStartDate={previousPeriodStartDate}
              titleKey={titleKey}
              transactions={transactions}
              useLegendsForDiagram={useLegendsForDiagram}
              isPBEModalLinkDisabled={isPbeModalLinkDisabled}
            />
        }
      </>
    </IntlProvider>
  );
};

/**
 * Connect App component to Redux
 */
export const App = connect<IAppStateToProps, IAppDispatchToProps, IAppOwnProps, IStoreState>(
  // Map state to props
  ({ localization, fetchBills, fetchBillsStatus }) => ({
    localization,
    pbeCategory: fetchBills.pbeCategory,
    description: fetchBills.description,
    billCloseDate: fetchBills.billCloseDate,
    startDate: fetchBills.startDate,
    endDate: fetchBills.endDate,
    subscriberDetails: fetchBills.subscriberDetails,
    chargeItems: fetchBills.chargeItems,
    titleKey: fetchBills.titleKey,
    descriptionKey: fetchBills.descriptionKey,
    detailedDescKey: fetchBills.detailedDescKey,
    previousPeriodStartDate: fetchBills.previousPeriodStartDate,
    previousPeriodEndDate: fetchBills.previousPeriodEndDate,
    currentPeriodStartDate: fetchBills.currentPeriodStartDate,
    currentPeriodEndDate: fetchBills.currentPeriodEndDate,
    transactions: fetchBills.transactions,
    useLegendsForDiagram: fetchBills.useLegendsForDiagram,
    fetchBillsStatus
  })
)(AppComponent);

