build:
	git clone https://gitlab.int.bell.ca/uxp/singleban-components.git
	cd singleban-components;git checkout ${CI_COMMIT_REF_NAME} || git checkout Release; rm package-lock.json; ls; npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run build || true
	git clone https://gitlab.int.bell.ca/uxp/britebill-bill-explainer.git
	cd britebill-bill-explainer;git checkout ${CI_COMMIT_REF_NAME} || git checkout Release; rm package-lock.json; ls; npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm install ajv@7.1.1 --save-dev; npm run build || true 
	git clone https://gitlab.int.bell.ca/uxp/mybell-britebill-bill-explainer.git
	cd mybell-britebill-bill-explainer; git checkout ${CI_COMMIT_REF_NAME} || git checkout Release;rm package-lock.json;  npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run build || true; npm install --package-lock-only --legacy-peer-deps
