@font-face{font-family:'pci-virgin';src:url(../fonts/pci-virgin-icons.eot?#iefix) format("embedded-opentype"),url(../fonts/pci-virgin-icons.woff) format("woff"),url(../fonts/pci-virgin-icons.ttf) format("truetype"),url(../fonts/pci-virgin-icons.svg) format("svg");font-weight:400;font-style:normal}

.icon-pci{font-style:normal;speak:none;font-weight:400;font-variant:normal;text-transform:none;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}
.icon-pci, .icon-pci:before {
    font-family: 'pci-virgin';
    position: static
}

.icon-VM-logo .path1:before {
    content: "\e9d3";
    color: #fff;
}
.icon-VM-logo .path2:before {
    content: "\e9d4";
    color: #c00;
    margin-left: -1.9658203125em;
}
.icon-VM-logo .path3:before {
    content: "\e9d5";
    color: #000;
    margin-left: -1.9658203125em;
}
.icon-VM-logo .path4:before {
    content: "\e9d6";
    color: #000;
    margin-left: -1.9658203125em;
}
.icon-VM-logo .path5:before {
    content: "\e9d7";
    color: #000;
    margin-left: -1.9658203125em;
}
.icon-VM-logo .path6:before {
    content: "\e9d8";
    color: #000;
    margin-left: -1.9658203125em;
}

.icon-Big_check_confirm .path1:before {
    content: "\e9b8";
    color: #2c9e25;
}
.icon-Big_check_confirm .path2:before {
    content: "\e9b9";
    color: #fff;
    margin-left: -1em;
}

.icon-BIG_WARNING .path1:before {
    content: "\e902";
    color: #e99e00;
}
.icon-BIG_WARNING .path2:before {
    content: "\e903";
    color: #fff;
    margin-left: -1em;
}

.icon-Big_info_bg1:before {
    content: "\e9d2";
}

.icon-small-warning:before {
    content: "\e900";
}

.icon-twitter:before {
    content: "\e9a7";
}

.icon-youtube:before {
    content: "\e9a8";
}

.icon-facebook:before {
    content: "\e9a5";
}

.icon-instagram:before {
    content: "\e9a4";
}

/*.icon-triangle-down:before {
    content: "\e9da";
    font-family: "pci-virgin";
    font-size: 5px;
    color: #555;
}*/
.icon-Close2x:before {
    content: "\e963";
}
/*Add icon Aug 25, 2020*/
.icon-close:before {
    content: "\e911";
}


