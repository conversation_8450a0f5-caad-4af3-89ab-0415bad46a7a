@font-face {
  font-family: Gibson-Regular;
  src: url(../../core/fonts/Gibson.ttf);
}
@font-face {
  font-family: Gibson-Medium;
  src: url(../../core/fonts/Gibson-SemiBold.ttf);
}

.Gibson-Regular {
  font-family: Gibson-Regular, Arial, Helvetica, sans-serif;
  font-style: normal;
  font-stretch: normal;
}

.Gibson-Medium {
  font-family: Gibson-Medium, Arial, Helvetica, sans-serif;
  font-style: normal;
  font-stretch: normal;
}

body,
p {
  font-family: Gibson-Regular, Arial, Helvetica, sans-serif;
}

.form-control {
  font-size: 14px;
  line-height: 18px;
}

.width-465 {
  width: 465px;
}

.height-20 {
  height: 20px;
}

.border-gray3 {
  border: 1px solid #d7d7d7;
}

.border-gray3-top {
  border-top: 1px solid #d7d7d7;
}

.line-height-18 {
  line-height: 18px;
}

.line-height-26 {
  line-height: 26px;
}

.line-height-28 {
  line-height: 28px;
}

.line-height-32 {
    line-height: 32px;
}

.line-height-42 {
  line-height: 42px;
}

.txtBlack3 {
  color: #333;
}

.txtSize28 {
  font-size: 28px;
}

.txtSize36 {
  font-size: 36px;
}

a.pci-link:link,
a.pci-link:visited {
  color: #cc0000;
}

.border-black-round {
  border: 1px solid #111111;
  border-radius: 10px;
  padding: 2px 5px;
}

.pointer-events-none {
  pointer-events: none;
}

a.icon-info:link,
a.icon-info:visited {
  color: #a1a5a6;
}

.icon-close-modal {
  display: inline-block;
}

.pc-modal,
.pc-modal .modal-content,
.pc-modal .modal-header {
  border-radius: 0;
}

.btn {
  border-radius: 20px;
  line-height: 17px;
  padding: 10px 35px;
}

.btn-pcmobile {
  color: #fff;
  font-size: 14px;
  background-color: #000;
  text-align: center;
}

button:focus {
  background: #383838;
}

button.close {
  background: transparent;
}

button:hover.btn-pcmobile {
  background-color: #383838;
  color: #fff;
  transition: all 400ms ease 0s;
}

input.pad-15 {
    padding: 15px;
}

/* Bell core class start*/
a:focus,
li.ui-menu-item:focus {
  outline-width: 2px;
  outline-style: solid;
  outline-color: #4d90fe !important;
  outline-offset: 2px;
}

.form-label {
  color: #000;
  display: inline-block;
  font-weight: 700;
  margin-bottom: 5px;
  letter-spacing: 0;
}

.form-label > span {
  font-weight: 500;
  color: #555;
}

.form-control {
  border-radius: 0;
  color: #555;
}

/* Bell core class end*/

.height-50 {
  height: 50px;
}

.modal.modal-tooltip .modal-body {
  padding: 0 30px 30px;
}

.tooltip-inner {
  margin: auto;
}

.pc-modal .close {
  font-size: 1.25rem;
}

.pcLogo {
  width: 85px;
}

.simplified-header {
  box-shadow: none;
  height: 65px;
}

/*START Page Loader*/
@-moz-keyframes spin {
  100% {
    -moz-transform: rotate(360deg);
  }
}

@-webkit-keyframes spin {
  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.bg-color-transparent {
  background-color: transparent;
}

.loader-fixed {
  width: 300px;
  left: 50%;
  margin-left: -150px;
  position: fixed;
  top: 35%;
  z-index: 99999;
  /* -webkit-box-shadow: 0 14px 36px 0 rgba(0, 0, 0, 0.3);
  -moz-box-shadow: 0 14px 36px 0 rgba(0, 0, 0, 0.3);
  box-shadow: 0 14px 36px 0 rgba(0, 0, 0, 0.3); */
}

.page-loader-msg {
  display: block;
  float: right;
  align-self: center;
  width: 210px;
}

.page-loader-msg p {
  color: #616161;
}

.loaderOverlayBackground {
  background: #000 none repeat scroll 0 0;
  display: none;
  height: 100%;
  left: 0;
  opacity: 0.7;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 55555;
}

.pagLoader {
  display: none;
}

.loader-container .centerElement {
  margin: 0 auto;
  display: table;
}

.loading-pcmobile {
  background-image: url(../../../../assets/img_tmp/PCI-Compliance-For-Agents/icon_loading.png);
  background-repeat: no-repeat;
  display: inline-block;
  width: 50px;
  height: 50px;
  background-position: center;
  -webkit-animation: spin 1.1s linear infinite;
  -moz-animation: spin 1.1s linear infinite;
  animation: spin 1.1s linear infinite;
}

.loaderOverlayBackground {
  background: #000 none repeat scroll 0 0;
  display: none;
  height: 100%;
  left: 0;
  opacity: 0.7;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 55555;
}

/*END Page Loader*/

@media screen and (max-width: 767.98px) {
  .line-height-22-xs {
    line-height: 22px;
  }

  .line-height-33-xs {
    line-height: 33px;
  }

  .width-70-xs {
    width: 70px;
  }

  .modal.modal-tooltip .tooltip-dialog .close {
    padding: 10px;
  }

  .modal-header .close.pci-tooltip-modal {
    margin-top: -15px;
    margin-right: -20px;
  }

  .modal-dialog .modal-content .modal-header .close {
      margin: 0;
  }
}

@media screen and (min-width: 768px) {
  /*.pci-dialog.modal-dialog {
        width: 645px;
        max-width: 100%;
    }*/
  .txtSize28-sm {
    font-size: 28px;
  }

  .width-290-sm {
    width: 290px;
  }

  .width-300-sm {
    width: 300px;
  }

  .width-380-sm {
    width: 380px;
  }
}

@media (min-width: 768px) and (max-width: 991.98px) {
  /* .width-85-sm {
    width: 85px;
  } */
  .container.pad-h-sm-15 {
    padding-left: 15px;
    padding-left: 15px;
  }
}

@media screen and (max-width: 991.98px) {
  .height-55-sm {
    height: 55px;
  }

  .pcLogo {
    width: 60px;
  }
}

.loader-pcmobile {
  margin: 4em auto;
  font-size: 30px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  position: relative;
  -webkit-animation: load-pcmobile 0.9s infinite ease;
  animation: load-pcmobile 0.9s infinite ease;
}

.loader-pcmobile_text {
  color: #b8b7b8;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes load-pcmobile {
  0%,
  100% {
    box-shadow: 0em -2.6em 0em 0em rgba(239, 134, 140, 1),
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2),
      2.5em 0em 0 0em rgba(255, 255, 255, 0.2),
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.2),
      0em 2.5em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.2),
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em -1.8em 0 0em rgba(239, 134, 140, 0.7);
  }

  12.5% {
    box-shadow: 0em -2.6em 0em 0em rgba(239, 134, 140, 0.7),
      1.8em -1.8em 0 0em rgba(239, 134, 140, 1),
      2.5em 0em 0 0em rgba(255, 255, 255, 0.2),
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.2),
      0em 2.5em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.2),
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2);
  }

  25% {
    box-shadow: 0em -2.6em 0em 0em rgba(255, 255, 255, 0.2),
      1.8em -1.8em 0 0em rgba(239, 134, 140, 0.7),
      2.5em 0em 0 0em rgba(239, 134, 140, 1),
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.2),
      0em 2.5em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.2),
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2);
  }

  37.5% {
    box-shadow: 0em -2.6em 0em 0em rgba(255, 255, 255, 0.2),
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2),
      2.5em 0em 0 0em rgba(239, 134, 140, 0.7),
      1.75em 1.75em 0 0em rgba(239, 134, 140, 1),
      0em 2.5em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.2),
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2);
  }

  50% {
    box-shadow: 0em -2.6em 0em 0em rgba(255, 255, 255, 0.2),
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2),
      2.5em 0em 0 0em rgba(255, 255, 255, 0.2),
      1.75em 1.75em 0 0em rgba(239, 134, 140, 0.7),
      0em 2.5em 0 0em rgba(239, 134, 140, 1),
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.2),
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2);
  }

  62.5% {
    box-shadow: 0em -2.6em 0em 0em rgba(255, 255, 255, 0.2),
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2),
      2.5em 0em 0 0em rgba(255, 255, 255, 0.2),
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.2),
      0em 2.5em 0 0em rgba(239, 134, 140, 0.7),
      -1.8em 1.8em 0 0em rgba(239, 134, 140, 1),
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2);
  }

  75% {
    box-shadow: 0em -2.6em 0em 0em rgba(255, 255, 255, 0.2),
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2),
      2.5em 0em 0 0em rgba(255, 255, 255, 0.2),
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.2),
      0em 2.5em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em 1.8em 0 0em rgba(239, 134, 140, 0.7),
      -2.6em 0em 0 0em rgba(239, 134, 140, 1),
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2);
  }

  87.5% {
    box-shadow: 0em -2.6em 0em 0em rgba(255, 255, 255, 0.2),
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2),
      2.5em 0em 0 0em rgba(255, 255, 255, 0.2),
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.2),
      0em 2.5em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.2),
      -2.6em 0em 0 0em rgba(239, 134, 140, 0.7),
      -1.8em -1.8em 0 0em rgba(239, 134, 140, 1);
  }
}
