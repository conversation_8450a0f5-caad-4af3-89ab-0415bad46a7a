import { combineReducers } from "redux";
import { handleActions } from "redux-actions";
import { createEpicMiddleware } from "redux-observable";

import { Store as BwtkStore, Injectable, CommonFeatures } from "bwtk";
const { BaseStore, actionsToComputedPropertyName } = CommonFeatures;

import * as actions from "./Actions";

const { fetchBillsAction, fetchBillsCompleted, fetchBillsFailed, setIsLoadingPBE, updatePBEDataCache } = actionsToComputedPropertyName(actions);

import { IStoreState, IFetchBillsState, IRequestStatus, IPBEDataCache } from "../models";
import { Epics } from "./Epics";

import { Localization } from "../Localization";
import { setFetchBillsReducer, setFetchBillsStatusReducer, setIsLoadingPBEReducer, setPBEDataCacheReducer } from "./Reducers";

@Injectable
export class Store extends BaseStore<IStoreState> {
  constructor(store: BwtkStore, private epics: Epics, private localization: Localization) {
    super(store);
  }

  get reducer() {
    return combineReducers<IStoreState>({
      fetchBillsStatus: handleActions<IRequestStatus, IRequestStatus>({
        [fetchBillsAction]: setFetchBillsStatusReducer(IRequestStatus.PENDING),
        [fetchBillsFailed]: setFetchBillsStatusReducer(IRequestStatus.FAILED),
        [fetchBillsCompleted]: setFetchBillsStatusReducer(IRequestStatus.COMPLETED)
      }, IRequestStatus.IDLE),
      fetchBills: handleActions<IFetchBillsState, IFetchBillsState>({
        [fetchBillsCompleted]: setFetchBillsReducer()
      }, {
        pbeCategory: "",
        description: "",
        billCloseDate: "",
        startDate: "",
        endDate: "",
        subscriberDetails: {},
        chargeItems: [],
        currentPeriodEndDate: "",
        currentPeriodStartDate: "",
        descriptionKey: "",
        detailedDescKey: "",
        previousPeriodEndDate: "",
        previousPeriodStartDate: "",
        titleKey: "",
        transactions: [],
        useLegendsForDiagram: false
      }),
      localization: this.localization.createReducer() as any,
      isLoadingPBE: handleActions<boolean>({
        [setIsLoadingPBE]: setIsLoadingPBEReducer
      }, false),
      pbeDataCache: handleActions<IPBEDataCache>({
        [updatePBEDataCache]: setPBEDataCacheReducer
      }, {})
    });
  }

  get middlewares() {
    const epicMiddleware = createEpicMiddleware(this.epics.combineEpics());
    return [epicMiddleware];
  }
}
