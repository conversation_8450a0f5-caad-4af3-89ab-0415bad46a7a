.txtSize62{
    font-size: 62px;
}

.txtBlack4{
    color: #0f0f0f;
}

header .skip-to-main-link {
    display: inline-block;
    padding: 9px 12px;
    position: absolute;
    top: -50px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    text-decoration: underline;
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
    transition: top .3s ease-out;
    z-index: 999999999;
    font-size: 13px;
    background-color: #efefef;
    color: #006FE6;
}

header .skip-to-main-link:focus {
    top: 0;
}

footer .skip-to-main-link {
    display: inline-block;
    padding: 9px 12px;
    position: absolute;
    left: -300px;
    text-decoration: underline;
    border-bottom-right-radius: 5px;
    transition: left .3s ease-out;
    background-color: #efefef;
    z-index: 3000;
    font-size: 13px;
    color: #006FE6;
}

footer .skip-to-main-link:focus {
    left: 0;
}

footer .legal-links a:not(:last-child):after {
    background-color: #979797;
    content: "";
    display: inline-block;
    height: 14px;
    margin-left: 20px;
    vertical-align: middle;
    width: 2px;
}

.height-55 {
    height: 55px;
}

.height-45 {
    height: 45px;
}

.height-43 {
    height: 43px;
    width: 117px;
}

.height-96 {
    height: 96px;
}

.height-137 {
  height: 137px;
 
}

.height-149 {
    height: 149px;
}

.height-202 {
    height: 202px;
}

.height-255 {
    height: 255px;
}

.height-308 {
    height: 308px;
}

.border-radius-6 {
    border-radius: 6px
}

.txtSize36 {
    font-size: 36px;
}

.txtSize150 {
    font-size: 150px;
}

.txtSize28 {
    font-size: 28px;
}

.line-height-14 {
    line-height: 14px;
}

.line-height-16 {
    line-height: 16px;
}

.line-height-18 {
    line-height: 18px;
}

.line-height-21 {
    line-height: 21px;
}

.line-height-22 {
    line-height: 22px;
}

.line-height-25 {
    line-height: 25px;
}

.line-height-30 {
    line-height: 30px;
}

.minWidth24 {
    width: 24px;
    min-width: 24px;
}

.img-circle-64 {
    width: 64px;
    height: 64px
}

.btn {
    font-size:14px;
    min-height: 45px;
}

.footer-line-separator {
    border-top: 1px solid #ccc;
    max-width: 360px;
    width: 100%;
    height: 1.2px;
}

.txtRed2 {
    color: #DE1F26;
}

.modal .close {
    color: #398FB7;
}

.modal .scrollAdjust{
    overflow-y: auto;
}

.modal .modal-header {
    border-bottom: none;
}

.modal .modal-body {
    padding: 30px 15px 0 30px;
}

.modal .modal-body-small {
    padding: 30px 15px;
}

.modal .modal-footer{
    display:block;
    border: none;
    padding: 30px 30px 45px 30px;
}

.margin-t-neg-4{
    margin-top:-4px;
}

.verticalMargin-12{
margin-top: 12px;
margin-bottom: 12px;
}
.booking-calendar {
    overflow-x: auto;
}

.booking-calendar-rows div[role="gridcell"][aria-selected="true"], .booking-calendar-rows div[role="gridcell"]:focus {
    outline-width: 2px;
    outline-style: solid;
    outline-color: #4d90fe;
    padding: 2px;
}

div.booking-calendar-box[role="gridcell"][aria-selected="true"] a {
    border: 2px solid #111;
}

div.booking-calendar-box[role="gridcell"][aria-selected="true"] a span {
    font-weight: bold;
    color: #111;
}

.booking-calendar-header > div, .booking-calendar-rows > div, div > div[role="gridcell"] {
    width: 115px;
    min-width: 115px;
}
.booking-calendar-disabled-box {
opacity: 0.5;
}
.form-control-select-box:after {
    font-family: "mya-virgin";
    content: "\e9da";
    font-size: 6px;
    color: #555;
    right: 4px;
    top: 7px;
    padding: 10px;
    height: 30px;
    cursor: pointer;
    background-color: #f4f4f4;
}

.min-width-255{
    min-width: 255px;
}
.access-and-contact-i {
  height: 18px;
  width: 832px;
  color: #111111;
  font-family: Arial;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 18px;
}

.group-10 {
    height: 180px;
}
.calendar-modal td:focus {
outline: none;
}

.sel-1 {
    width:150px;
}
.sel-2 {
    width:170px;
}
.fade{
transition:none;
} 

.rating-buttons-cont a[role="button"] {
    height: calc(100% + 5px);
}

.rating-buttons-cont a[role="button"] span {
    display: flex;
    height: 100%;
}
/* for safari compatibility */
.select_otherbrowser{
-moz-appearance: none;
-webkit-appearance: none;
}
.rating-buttons-cont div[rating-selected] .rating-icon-button .icon-Smile .path1:before, .rating-buttons-cont div[rating-selected] .rating-icon-button .icon-Neutral .path1:before, .rating-buttons-cont div[rating-selected] .rating-icon-button .icon-Sad .path1:before {
    color: #c00;
}

.rating-buttons-cont div[rating-selected] .rating-icon-button .icon-Smile .path2:before, .rating-buttons-cont div[rating-selected] .rating-icon-button .icon-Neutral .path2:before, .rating-buttons-cont div[rating-selected] .rating-icon-button .icon-Sad .path2:before {
    color: #fff;
}

.survey-content-cont, .survey-content-cont #positiveSurveyContent, .survey-content-cont #neutralSurveyContent, .survey-content-cont #negativeSurveyContent {
    display: none;
}

.rating-buttons-cont div[rating-selected] ~ .survey-content-cont {
    display: block;
}

.rating-buttons-cont div[rating-selected="positiveSurveyContent"] ~ .survey-content-cont #positiveSurveyContent,
.rating-buttons-cont div[rating-selected="neutralSurveyContent"] ~ .survey-content-cont #neutralSurveyContent,
.rating-buttons-cont div[rating-selected="negativeSurveyContent"] ~ .survey-content-cont #negativeSurveyContent {
    display: flex;
}

.graphical_ctrl{position:relative;padding-left:35px}
.graphical_ctrl input{position:absolute;width:48px;z-index:-1;height:48px;opacity:0;top:-16px;left:-9px}
.ctrl_element{position:absolute;top:-3px;left:0;height:25px;width:25px;background:#fff; border:1px solid #ccc}
.ctrl_radioBtn .ctrl_element{border-radius:50%}
.graphical_ctrl input:checked:focus ~ .ctrl_element{outline-width:1px;outline-style:dashed;outline-color:#4d90fe;box-shadow:0 0 3px 2px rgba(178,209,228,1)}
.graphical_ctrl input:checked ~ .ctrl_element{background:#111;border:1px solid #111}
.graphical_ctrl input:checked:focus ~ .ctrl_element{background:#111}
.graphical_ctrl input:disabled ~ .ctrl_element{background:#e6e6e6;opacity:.6;border:1px solid #e6e6e6;pointer-events:none}
.ctrl_element:after{content:'';position:absolute;display:none}
.graphical_ctrl input:checked ~ .ctrl_element:after{display:block}
.graphical_ctrl_checkbox .ctrl_element:after {left: 7px;top:1px;width:9px;height:15px;border:solid #fff;border-width:0 3px 3px 0;display:inline-block;-webkit-transform:rotate(45deg);-moz-transform:rotate(45deg);-o-transform:rotate(45deg);transform:rotate(45deg);}
.graphical_ctrl_checkbox input:disabled ~ .ctrl_element:after {border-color: #e6e6e6;pointer-events: none;cursor: not-allowed;}
.graphical_ctrl_checkbox input:checked:disabled ~ .ctrl_element:after {border-color: #7b7b7b;}
.graphical_ctrl input:disabled ~ .ctrl_element, .graphical_ctrl input:checked:disabled ~ .ctrl_element {background: #e6e6e6;opacity: 0.6;border: 1px solid #e6e6e6;pointer-events: none;}
.chk_radius{border-radius:2px;cursor:pointer}
.graphical_ctrl input:checked~.checkbox-text{font-weight: bold;color:#111;}
.graphical_ctrl input:checked~*{font-weight: bold;color:#111;}
	.graphical_ctrl.secondary_checkbox input:checked~.ctrl_element{background: #398FB7;border:1px solid #398FB7}
.graphical_ctrl.secondary_checkbox input:checked:focus~.ctrl_element{background: #398FB7}
.graphical_ctrl.secondary_checkbox input:checked~.checkbox-text{color: #398FB7}
.graphical_ctrl.secondary_checkbox input[type="checkbox"]:focus ~ .ctrl_element, .graphical_ctrl input[type="radio"]:focus ~ .ctrl_element, .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element {
    outline-width: 2px;
    outline-style: solid;
    outline-color: #4d90fe;
    outline-offset: 2px;
}
.checkbox-text{cursor:pointer;}
	.btn-primary.disabled, .btn-primary:disabled {
    border: 1px solid rgba(153,153,153,0);
    border-radius: 3px;
    background-color: rgba(204,0,0,0.5);
}

.btn-primary.disable-gray:disabled {
    background-color: #ccc;
}

.ctrl_radioBtn .ctrl_element:after {
    left: 5px;
    top: 5px;
    height: 13px;
    width: 13px;
    border-radius: 50%;
    background: #fff;
}

.graphical_ctrl input:checked ~ .ctrl_element:after {
    display: block;
}
	

.other-reason textarea {
    height: 137px
}

/*Sticky footer*/
html, body {
  height: 100%;
}
body {
  display: flex;
  flex-direction: column;
}
main {
  flex: 1 0 auto;
}
footer {
  flex-shrink: 0;
}

.pointer-event-none {
    pointer-events: none;
}

.icon-calendar::before {
    display: inline-block;
}

.v-center {
    top: 50%;
    transform: translateY(-50%);
}

/*Media Queries Only*/
@media screen and (max-width: 991.98px) {
    .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element, .graphical_ctrl input[type="radio"]:focus ~ .ctrl_element {
        outline: none;
    }

    .focused-element {
        outline-width: 2px;
        outline-style: solid;
        outline-color: #4d90fe;
        outline-offset: 2px;
    }

    .checkbox-container.checked-border, .radio-container.checked-border {
        border: 2px solid #111;
    }


    .border-gray2-t-sm {
        border-top: 1px solid #e1e1e1;
    }

    .checkbox-container, .radio-container {
        border: 2px solid #D4D4D4;
        background-color: #FFFFFF;
        box-shadow: 0 2px 3px 0 rgba(0,0,0,0.2);
        padding: 24px;
    }

    .checkbox-container {
        height: 66px;
        padding: 15px;
    }

    .checkbox-container .graphical_ctrl_checkbox {
        display: flex;
        align-items: center;
        height: 36px;
    }

    .checkbox-container .graphical_ctrl_checkbox .checkbox-text {
        line-height: 18px;
        margin-top: -8px;
        padding-left: 10px;
    }

    .checkbox-container .graphical_ctrl_checkbox .ctrl_element {
        top:2px;
    }

    .radio-container {
        padding: 15px;
        height: 66px;
        display: flex;
        align-items: center;
    }
    /*.checkbox-container:not(:last-child) {
        margin-bottom:15px;
        
    }*/
    .accessory-option {
        box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 1), 0 0 2px 2px rgba(0,0,0,.2);
    }

    .width-sm-100{
        width: 100%;
    }
    .other-reason textarea {
        height: 137px
    }
}

@media (min-width:992px) {
    .order-2-md {
        order: 2;
    }

    .order-3-md {
        order: 3;
    }

    .margin-l-md-neg-15 {
        margin-left: -15px
    }
}

@media screen and (min-width: 768px) {
    .modal.fade.show .modal-dialog {
        width: 530px;
        max-height: calc(100% - 60px);
        margin: auto;
    }

    .calendar-modal .modal-dialog{
        max-width: 470px;
    }
    .modal .modal-dialog-small, .modal.fade.show .modal-dialog-small {
        width: 410px;
    }

    .width-250-sm {
        width: 248px;
    }
}

@media screen and (min-width: 768px) and (max-width: 991.98px) {
    .modal.fade.show .modal-dialog {
        width: 530px;
        max-height: calc(100% - 60px);
        margin: auto;
    }

    .margin-l-sm-neg-30 {
        margin-left: -30px
    }

    .pad-l-7_half-sm {
        padding-left: 7.5px;
    }

    .pad-r-7_half-sm {
        padding-right: 7.5px;
    }
    .skip-to-main-link-hide {
     display:none;
     visibility: hidden;
	}
}

@media screen and (max-width:767.98px) {
    .radius-10-top-xs{border-radius:10px 10px 0 0 }

    .modal.modal-vm {
        bottom: -400px;
        top: unset;
        transition: all 0.15s linear;
    }

    .modal.show {
        
        bottom: 0;
        max-height: 100%;
        height: auto;
    }

    .modal.fade.show .modal-dialog {
        top: unset;
        transform: unset !important;
        transition: unset;
    }

    .modal .modal-body {
        padding: 30px 0 0 15px;
    }

    .modal .modal-body-small {
        padding: 30px 15px;
    }

    .modal .modal-footer {
        padding: 30px 15px;
    }

    .txtLeft-xs{
        text-align: left;
    }

    .txtSize80-xs {
        font-size: 80px;
    }
    .txtSize100-xs {
        font-size: 100px;
    }
    .width-100-percent-xs {
        width: 100%
    }

    .height-45 {
    height: 43px;
}

    .booking-calendar .width-14 {
    width: 116px;
}
    .skip-to-main-link {
     display: none;
	}
    .skip-to-main-link-hide {
     display:none;
     visibility: hidden;
	}

}
/*Media Queries Only */
	/* Calendar Override */
.calendar-modal .calendar-header{display:flex;justify-content:space-between; align-items:center; height: 45px;}
.calendar-modal .calendar-header .header-label{font-weight:bold;color:#111}
.calendar-modal .calendar-header .button{color:#398FB7;font-size:18px;border:none;background-color:transparent;padding:15px;cursor: pointer}
    .calendar-modal .calendar-header .button:focus {
        outline-width: 1px;
        outline-style: solid;
        outline-color: #4d90fe;
    }
.calendar-modal .calendar-frame{
    display: flex;
    justify-content: center;
}
.calendar-modal .calendar-table{margin: 0 auto}
.calendar-modal td, .calendar-modal th {
    width: 50px;
    height: 50px;
    text-align: center;
}
.calendar-modal td:not(.disabled):not(.today) {
    cursor: pointer;
}
    .calendar-modal td.selected span {
        box-sizing: border-box;
        padding: 6px;
        border: 2px solid #398FB7;
        color: #398FB7;
        font-weight: bold;
    }
    .calendar-modal td.disabled, .calendar-modal td.today {
        color: #999;
        pointer-events: none;
    }
    
/* Calendar Override */