
/*Bell icon fonts*/
@font-face {font-family:'pcm-icon';
    src:url('../fonts/pcm-icon.eot?#iefix') format('embedded-opentype'), url('../fonts/pcm-icon.woff') format('woff'), url('../fonts/pcm-icon.ttf') format('truetype'), url('../fonts/pcm-icon.svg') format('svg');
    font-weight:normal;
    font-style:normal}
    @font-face {font-family:'pcm-icon-outline';
    src:url('../fonts/pcm-icon-outline.eot?iw8dli');
    src:url('../fonts/pcm-icon-outline.eot?#iefixiw8dli') format('embedded-opentype'), url('../fonts/pcm-icon-outline.ttf?iw8dli') format('truetype'), url('../fonts/pcm-icon-outline.woff?iw8dli') format('woff'), url('../fonts/pcm-icon-outline.svg?iw8dli#pcm-icon-outline') format('svg');
    font-weight:normal;
    font-style:normal}
    @font-face {font-family:'pcm-icon2';
    src:url('../fonts/pcm-icon2.eot?#iefix') format('embedded-opentype'), url('../fonts/pcm-icon2.woff') format('woff'), url('../fonts/pcm-icon2.ttf') format('truetype'), url('../fonts/pcm-icon2.svg') format('svg');
    font-weight:normal;
    font-style:normal}
    
    .icon,.icon2,.icon-o,.icon-s {font-style:normal;
    speak:none;
    font-weight:normal;
    font-variant:normal;
    text-transform:none;
    line-height:1;
    -webkit-font-smoothing:antialiased;
    -moz-osx-font-smoothing:grayscale}
    .icon:before {font-family:'pcm-icon';
    /*Baseline alignment to use in text*/
    position:relative;
    top:.1em}
    .icon2:before {font-family:'pcm-icon2';
    /*Baseline alignment to use in text*/
    position:relative;
    top:3px}
    .icon3:before {
        font-family: 'pcm-icon3';
        /*Baseline alignment to use in text*/
        position: relative;
        top: 3px
    }
    
    .icon-o:before {font-family:'pcm-icon-outline'}
    .icon-blue {display:inline-block;
    height:20px;
    line-heighT:20px;
    width:20px;
    color:#fff;
    background-color:#00549a;
    border-radius:50%;
    cursor:pointer}
    a:link .icon:before,a:visited .icon:before,a:hover .icon:before,a:focus .icon:before a:active .icon:before {text-decoration:none;
    display:inline-block}
    .icon-pcm-chat:before {content:"\e60b"}
    .icon-pcm-logo:before {content:"\e600"}
    .icon-cart:before {content:"\e617"}
    .icon-caret:before {content:"\e61a"}
    .icon-check:before {content:"\e602"}
    .icon-car:before {content:"\e623"}
    .icon-check-light:before {content:"\e603"}
    .icon-chat-bubble:before {content:"\e900"}
    .icon-handset:before {content:"\e901"}
    .icon-cell:before {content:"\e622"}
    .icon-close:before {content:"\e624"}
    .icon-close-solid:before {content:"\e60c"}
    .icon-close-outline:before {content:"\e625"}
    .icon-back-to-top:before {content: "\e925"}
    .icon-chevron:before,.icon-chevron-up:before,.icon-chevron-right:before,.icon-chevron-down:before,.icon-chevron-left:before {content:"\e012";
    display:inline-block}
    .icon-chevron-up:before {-webkit-transform:rotate(-90deg);
    -ms-transform:rotate(-90deg);
    transform:rotate(-90deg);
    -webkit-transform-origin:45% 40%;
    -ms-transform-origin:45% 40%;
    transform-origin:45% 40%}
    .icon-chevron-down:before {-webkit-transform:rotate(90deg);
    -ms-transform:rotate(90deg);
    transform:rotate(90deg)}
    .icon-chevron-left:before {-webkit-transform:rotate(180deg);
    -ms-transform:rotate(180deg);
    transform:rotate(180deg)}
    .icon-chevron-bold:before {content:"\e61d"}
    .icon-envelope:before {content:"\e621"}
    .icon-exclamation:before,.icon-i:before {content:"\e604"}
    .icon-i-solid:before {content:"\e60d"}
    .icon-i-solid-white:before {content:"\e902"}
    .icon-heart:before {content:"\e60e"}
    .icon-home:before {content:"\e61c"}
    .icon-i:before {-webkit-transform:rotate(180deg);
    -ms-transform:rotate(180deg);
    transform:rotate(180deg)}
    .icon-location-pin:before {content:"\e620"}
    .icon-magnifying-glass:before {content:"\e615"}
    .icon-magnifying-glass-circled {position:absolute;
    right:15px;
    top:0;
    border:2px solid #0066a4;
    border-radius:50%;
    color:#0066a4;
    width:40px;
    height:40px;
    padding-top:4px}
    .icon-magnifying-glass-circled:before {content:"\e615";
    font-size:20px}
    .icon-minus:before {content:"\e606"}
    .icon-mobile-menu:before {content:"\e618"}
    .icon-play-icon:before {content:"\e608"}
    .icon-x:before,.icon-plus:before {content:"\e007"}
    .icon-x:before {-webkit-transform:rotate(45deg);
    -ms-transform:rotate(45deg);
    transform:rotate(45deg);
    display:inline-block}
    .icon-select-trigger:before {content:"\e601"}
    .icon-select-trigger-chevrons:before {content:"\e61e"}
    .icon-silhouette:before {content:"\e616"}
    .icon-samsung-logo:before {content:"\e605";
    display:block;
    font-size:120px;
    margin-top:-40px;
    margin-bottom:-60px}
    .icon-silhouette-standing:before {content:"\e607"}
    .icon-check-circled:before {content:"\e609"}
    .icon-exclamation-circled:before {content:"\e60a"}
    .icon-resize:before {content:"\e61f"}
    .icon-star:before {content:"\e60f"}
    .icon-check-circled-outline:before {content:"\e610"}
    .icon-linkedin:before {content:"\e611"}
    .icon-twitter:before {content:"\e612";
    left:-1px;
    top:4px}
    .icon-youtube:before {content:"\e613"}
    .icon-google-plus:before {content:"\e614"}
    .icon-facebook:before {content:"\e619";
    left:3px}
    .icon-clock:before {content:"\e61b"}
    .icon-pdf:before {content:"\e903"}
    .icon-details-more-fill:before {
      content: "\ea01";
    }
    /* Some common circle + icon presets */
    .toggle-more {display:inline-block;
    position:relative;
    font-size:18px;
    padding-left:30px;
    line-height:1.3}
    .toggle-more:before {content:'';
    display:block;
    position:absolute;
    width:22px;
    height:22px;
    border:1px solid #0066a4;
    border-radius:50%;
    left:0}
    .toggle-more:after {font-family:'pcm-icon';
    content:'\e007';
    position:absolute;
    top:11px;
    left:11px;
    -webkit-transform:translate(-50%, -50%);
    -ms-transform:translate(-50%, -50%);
    transform:translate(-50%, -50%);
    font-size:11px;
    line-height:1;
    color:#0066a4}
    .more-link {position:relative;
    padding-right:25px;
    display:inline-block}
    .more-link:after {font-family:'pcm-icon';
    content:'\e608';
    position:absolute;
    top:2px;
    right:5px;
    line-height:1}
    .more-link.more-link_before {padding-left:25px}
    .more-link.more-link_before:after {left:0}
    ul.more-info-list{list-style:none;padding-left:0;margin-right:20px;display:table}
    ul.more-info-list li{padding-top:10px}
    ul.more-info-list li:before{content:"\e608"; color:#00549a; font-family:'pcm-icon';display:table-cell;margin-left:-20px}
    ul.more-info-list.linkBlue li:before{color:#00549a}
    ul.more-info-list.linkWhite li:before, ul.more-info-list.linkWhite li a{color:#fff}
    ul.more-info-list li a,ul.more-info-list li p{padding-left:8px;display:table-cell}
    
    
    /* icon helper classes */
    .icon-circle-xsmall,.icon-circle-small,.icon-circle-medium,.icon-circle-large,.icon-circle-xlarge {display:inline-block;
    position:relative;
    width:18px;
    height:18px;
    border:2px solid #00549a;
    border-radius:50%;
    color:#00549a}
    .icon-circle-xsmall .text,.icon-circle-small .text,.icon-circle-medium .text,.icon-circle-large .text,.icon-circle-xlarge .text {display:inline-block;
    text-align:center;
    width:100%}
    .icon-circle-xsmall .text {line-height:14px;
    font-size:10px}
    .icon-circle-small .text {line-height:36px;
    font-size:18px}
    .icon-circle-medium .text {line-height:56px;
    font-size:27px}
    .icon-circle-large .text {line-height:76px;
    font-size:35px}
    .icon-circle-xlarge .text {line-height:136px;
    font-size:55px}
    .icon-circle-xsmall:before,.icon-circle-small:before,.icon-circle-medium:before,.icon-circle-large:before,.icon-circle-xlarge:before {position:absolute;
    top:50%;
    left:50%;
    -webkit-transform:translate(-50%, -50%);
    -ms-transform:translate(-50%, -50%);
    transform:translate(-50%, -50%)}
    .icon-circle-xsmall {width:18px;
    height:18px}
    .icon-circle-small {width:40px;
    height:40px}
    .icon-circle-medium {width:60px;
    height:60px}
    .icon-circle-large {width:80px;
    height:80px}
    .icon-circle-xlarge {width:140px;
    height:140px}
    .icon-xsmall:before,.icon-circle-xsmall:before {font-size:10px}
    .icon-small:before,.icon-circle-small:before {font-size:37px}
    .icon-medium:before,.icon-circle-medium:before {font-size:58px}
    .icon-large:before,.icon-circle-large:before {font-size:78px}
    .icon-xlarge:before,.icon-circle-xlarge:before {font-size:137px}
    .icon-circle-xsmall.icon-circle_solid,.icon-circle-small.icon-circle_solid,.icon-circle-large.icon-circle_solid,.icon-circle-xlarge.icon-circle_solid {border-color:#00549a;
    background-color:#00549a}
    .icon-circle-xsmall.icon-circle_inverse,.icon-circle-small.icon-circle_inverse,.icon-circle-medium.icon-circle_inverse,.icon-circle-large.icon-circle_inverse,.icon-circle-xlarge.icon-circle_inverse {border-color:#fff}
    .icon-circle-xsmall.icon-circle_solid:before,.icon-circle-small.icon-circle_solid:before,.icon-circle-medium.icon-circle_solid:before,.icon-circle-large.icon-circle_solid:before,.icon-circle-xlarge.icon-circle_solid:before,.icon-circle-xsmall.icon-circle_inverse:before,.icon-circle-small.icon-circle_inverse:before,.icon-circle-medium.icon-circle_inverse:before,.icon-circle-large.icon-circle_inverse:before,.icon-circle-xlarge.icon-circle_inverse:before,.icon-circle-xsmall.icon-circle_solid,.icon-circle-small.icon-circle_solid,.icon-circle-medium.icon-circle_solid,.icon-circle-large.icon-circle_solid,.icon-circle-xlarge.icon-circle_solid,.icon-circle-xsmall.icon-circle_inverse,.icon-circle-small.icon-circle_inverse,.icon-circle-medium.icon-circle_inverse,.icon-circle-large.icon-circle_inverse,.icon-circle-xlarge.icon-circle_inverse {color:#fff}
    .icon-circle-xsmall.icon-circle_solid.icon-circle_inverse,.icon-circle-small.icon-circle_solid.icon-circle_inverse,.icon-circle-medium.icon-circle_solid.icon-circle_inverse,.icon-circle-large.icon-circle_solid.icon-circle_inverse,.icon-circle-xlarge.icon-circle_solid.icon-circle_inverse {border-color:#fff;
    background-color:#fff}
    .icon-circle-xsmall.icon-circle_solid.icon-circle_inverse:before,.icon-circle-small.icon-circle_solid.icon-circle_inverse:before,.icon-circle-medium.icon-circle_solid.icon-circle_inverse:before,.icon-circle-large.icon-circle_solid.icon-circle_inverse:before,.icon-circle-xlarge.icon-circle_solid.icon-circle_inverse:before {color:#00549a}
    .icon-circle-small .icon-circle-txt {font-size:24px;
    width:37px;
    text-align:center;
    display:inline-block;
    line-height:1.5}
    [class*="icon-arrow-"] {transition:-webkit-transform .5s cubic-bezier(.55, 0, .1, 1);
    transition:transform .5s cubic-bezier(.55, 0, .1, 1)}
    .icon-size-small {font-size:20px}
    .icon-size-medium {font-size:40px}
    .icon-size-large {font-size:60px}
    .icons-group {white-space:nowrap}
    .icons-group > .icon-o:not(:first-child) {margin-left:-20px}
    /* circled colors */
    .icon-circle_white {border-color:#fff;
    color:#fff}
    /* Outline style icons */
    /*For Global nav menu*/
    .icon_bills-menu:before {
      content: "\e901";
    }
    .icon_homephone-menu:before {
      content: "\e902";
    }
    .icon_internet-menu:before {
      content: "\e903";
    }
    .icon_mobility-menu:before {
      content: "\e904";
    }
    .icon_mobility_bill-menu:before {
      content: "\e905";
    }
    .icon_one_bill-menu:before {
      content: "\e906";
    }
    .icon_pay-menu:before {
      content: "\e907";
    }
    .icon_profile-menu:before {
      content: "\e908";
    }
    .icon_tv-menu:before {
      content: "\e909";
    }
    .icon_user_bills-menu:before {
      content: "\e90a";
    }
    .icon_user_profile-menu:before {
      content: "\e90b";
    }
    .icon_user_security-menu:before {
      content: "\e90c";
    }
    .icon_user_services-menu:before {
      content: "\e90d";
    }
    .icon_view_all-menu:before {
      content: "\e90e";
    }
    /*For Global nav menu END*/
    
    .icon-o-battery:before {content:"\e600"}
    .icon-o-camera:before {content:"\e601"}
    .icon-o-camera-shutter:before {content:"\e602"}
    .icon-o-cellphone:before {content:"\e603"}
    .icon-o-chat-bubble:before {content:"\e604"}
    .icon-o-clock:before {content:"\e606"}
    .icon-o-crescent:before {content:"\e605"}
    .icon-o-cycle:before {content:"\e607"}
    .icon-o-data-0:before {content:"\e608"}
    .icon-o-data-1:before {content:"\e609"}
    .icon-o-data-2:before {content:"\e60a"}
    .icon-o-data-3:before {content:"\e60b"}
    .icon-o-data-4:before {content:"\e60c"}
    .icon-o-envelope:before {content:"\e60d"}
    .icon-o-fibe:before {content:"\e634"}
    .icon-o-gear:before {content:"\e60e"}
    .icon-o-globe:before {content:"\e60f"}
    .icon-o-handset:before {content:"\e610"}
    .icon-o-headphones:before {content:"\e611"}
    .icon-o-homephone:before {content:"\e612"}
    .icon-o-house:before {content:"\e635"}
    .icon-o-houses:before {content:"\e808"}
    .icon-o-id:before {content:"\e613"}
    .icon-o-infinity:before {content:"\e614"}
    .icon-o-laptop:before {content:"\e615"}
    .icon-o-lightning-bolt:before {content:"\e616"}
    .icon-o-location:before {content:"\e617"}
    .icon-o-paper-plane:before {content:"\e618"}
    .icon-o-play-icon:before {content:"\e619"}
    .icon-o-receiver:before {content:"\e61a"}
    .icon-o-shield:before {content:"\e61b"}
    .icon-o-silhouette:before {content:"\e61c"}
    .icon-o-silhouette-id:before {content:"\e61d"}
    .icon-o-speed-0:before {content:"\e61e"}
    .icon-o-speed-1:before {content:"\e61f"}
    .icon-o-speed-2:before {content:"\e620"}
    .icon-o-speed-3:before {content:"\e621"}
    .icon-o-speed-4:before {content:"\e622"}
    .icon-o-tower:before {content:"\e623"}
    .icon-o-tv:before {content:"\e624"}
    .icon-o-up-arrow:before,.icon-o-down-arrow:before {content:"\e625"}
    .icon-o-down-arrow:before {display:inline-block;
    -webkit-transform:rotate(180deg);
    -ms-transform:rotate(180deg);
    transform:rotate(180deg)}
    .icon-o-globe-half:before {content:"\e626"}
    .icon-o-globe-three-quarters:before {content:"\e627"}
    .icon-o-play-few-times-a-week:before {content:"\e628"}
    .icon-o-play-everyday:before {content:"\e629"}
    .icon-o-download-rarely:before {content:"\e62A"}
    .icon-o-download-few-times-a-week:before {content:"\e62B"}
    .icon-o-download-almost-everyday:before {content:"\e62C"}
    .icon-o-email:before {content:"\e62D"}
    .icon-o-music-rarely:before {content:"\e62E"}
    .icon-o-music-few-times-a-week:before {content:"\e62F"}
    .icon-o-music-almost-every-day:before {content:"\e630"}
    .icon-o-silhouette-standing:before {content:"\e631"}
    .icon-o-most-popular:before {content:"\e632"}
    .icon-o-movies:before {content:"\e633"}
    .icon-o-phone-plus:before {content:"\e636"}
    .icon-o-phone-in-hand:before {content:"\e637"}
    .icon-o-tablet:before {content:"\e638"}
    .icon-o-speed-5:before {content:"\e639"}
    .icon-o-bars:before {content:"\e63a"}
    .icon-o-tag:before {content:"\e63b"}
    .icon-o-star:before {content:"\e63c"}
    .icon-o-24:before {content:"\e63d"}
    .icon-o-arbor:before {content:"\e63e"}
    .icon-o-battcharge:before {content:"\e63f"}
    .icon-o-bluetooth:before {content:"\e640"}
    .icon-o-bytablet:before,.icon-o-bring-yotablet:before {content:"\e641"}
    .icon-o-browsesupp:before {content:"\e642"}
    .icon-o-calendar:before {content:"\e643"}
    .icon-o-canada:before {content:"\e644"}
    .icon-o-hands-free:before,.icon-o-handsfree:before {content:"\e645"}
    .icon-o-memorycard:before {content:"\e646"}
    .icon-o-questions-about-bell-mail:before {content:"\e647"}
    .icon-o-sheet:before,.icon-o-screenprotector:before {content:"\e648"}
    .icon-o-smart-acc:before {content:"\e649"}
    .icon-o-browsesupp:before,.icon-o-changemyrateplan:before {content:"\e64a"}
    .icon-o-forgotpassword:before {content:"\e64b"}
    .icon-o-power_symbol:before {content:"\e64c"}
    .icon-o-glossary:before {content:"\e64d"}
    .icon-o-hdtv:before {content:"\e64e"}
    .icon-o-headphone:before {content:"\e64f"}
    .icon-o-heart:before {content:"\e650"}
    .icon-o-hintsandtips:before {content:"\e651"}
    .icon-o-notifyme-limit:before {content:"\e652"}
    .icon-o-changeYourfab5-10:before {content:"\e653"}
    .icon-o-changeyourprogramming:before {content:"\e654"}
    .icon-o-checkmycurrentbalance:before {content:"\e655"}
    .icon-o-chooiceof4packages:before {content:"\e656"}
    .icon-o-cloud:before {content:"\e657"}
    .icon-o-compareprogramming:before {content:"\e658"}
    .icon-o-consoldateyourbills:before {content:"\e659"}
    .icon-o-create_mail_account:before {content:"\e65a"}
    .icon-o-currentinternetusage:before {content:"\e65b"}
    .icon-o-diagnoseandfixpcproblems:before {content:"\e65c"}
    .icon-o-download:before {content:"\e65d"}
    .icon-o-ebill:before {content:"\e65e"}
    .icon-o-edit:before {content:"\e65f"}
    .icon-o-estimateusage:before {content:"\e660"}
    .icon-o-expandcircle:before {content:"\e661"}
    .icon-o-expandx:before {content:"\e662"}
    .icon-o-fiberoptics:before {content:"\e663"}
    .icon-o-find_channel:before {content:"\e664"}
    .icon-o-find_areacode:before {content:"\e665"}
    .icon-o-forgot_username:before {content:"\e666"}
    .icon-o-forgot_youpasscode:before {content:"\e667"}
    .icon-o-forward_yourcalls:before {content:"\e668"}
    .icon-o-french:before {content:"\e669"}
    .icon-o-international:before {content:"\e66a"}
    .icon-o-internetplans:before {content:"\e66b"}
    .icon-o-internet:before {content:"\e66c"}
    .icon-o-keyboard:before {content:"\e66d"}
    .icon-o-leaf:before {content:"\e66e"}
    .icon-o-learnmore_mail:before {content:"\e66f"}
    .icon-o-learnmore_mcAfee:before {content:"\e670"}
    .icon-o-manufacturer_warranty:before {content:"\e671"}
    .icon-o-mic:before {content:"\e673"}
    .icon-o-mobiletv:before {content:"\e674"}
    .icon-o-move_satellite:before {content:"\e675"}
    .icon-o-moving:before {content:"\e676"}
    .icon-o-on_demand:before {content:"\e677"}
    .icon-o-os:before {content:"\e678"}
    .icon-o-receiver2:before {content:"\e679"}
    .icon-o-www:before {content:"\e67a"}
    .icon-o-longdistance_ratecharts:before {content:"\e67b"}
    .icon-o-mac:before {content:"\e67c"}
    .icon-o-overthephone:before {content:"\e67d"}
    .icon-o-paperbill:before {content:"\e67e"}
    .icon-o-phonenumber:before {content:"\e67f"}
    .icon-o-processor:before {content:"\e680"}
    .icon-o-recordapvrshow:before {content:"\e681"}
    .icon-o-repairs-installs-troubleshooting:before {content:"\e682"}
    .icon-o-temporarysuspend:before {content:"\e683"}
    .icon-o-usage:before {content:"\e684"}
    .icon-o-userguilde:before {content:"\e685"}
    .icon-o-usinginternet:before {content:"\e686"}
    .icon-o-voice:before {content:"\e687"}
    .icon-o-watchshowsonline:before {content:"\e688"}
    .icon-o-wifi:before {content:"\e689"}
    .icon-o-windows:before {content:"\e68a"}
    .icon-o-wirecare:before {content:"\e68b"}
    .icon-o-wirelessconnection:before {content:"\e68c"}
    .icon-o-displaysize:before {content:"\e68d"}
    .icon-o-picture:before {content:"\e68e"}
    .icon-o-play:before {content:"\e68f"}
    .icon-o-screensize:before {content:"\e690"}
    .icon-o-secure_internet_connection:before {content:"\e691"}
    .icon-o-signintobellmail:before {content:"\e692"}
    .icon-o-smarttips:before {content:"\e693"}
    .icon-o-smart_touch,.icon-o-smarttouch:before {content:"\e694"}
    .icon-o-smartphonecare:before {content:"\e695"}
    .icon-o-softwareupdate:before {content:"\e696"}
    .icon-o-software:before {content:"\e697"}
    .icon-o-sports:before {content:"\e698"}
    .icon-o-temporarysuspendservice:before {content:"\e699"}
    .icon-o-travellingcanada:before {content:"\e69a"}
    .icon-o-travellingcoverage:before {content:"\e69b"}
    .icon-o-tutorial:before {content:"\e69c"}
    .icon-o-tvpackages:before {content:"\e69d"}
    .icon-o-upgradeoradd:before {content:"\e69e"}
    .icon-o-USflag:before {content:"\e69f"}
    .icon-o-dollarsign_dblcircle:before {content:"\e6a0"}
    .icon-o-family_contact_card:before {content:"\e6a1"}
    .icon-o-calling_card:before {content:"\e6a2"}
    .icon-o-mcafee:before {content:"\e6a3"}
    .icon-o-indent_a_call:before {content:"\e6a4"}
    .icon-o-visual_call_waiting:before {content:"\e6a5"}
    .icon-o-threeway_calling:before {content:"\e6a6"}
    .icon-o-resource_centre:before {content:"\e6a7"}
    .icon-o-lte_world_fastest:before {content:"\e6a8"}
    .icon-o-data-5:before {content:"\e6a9"}
    .icon-o-data-6:before {content:"\e700"}
    .icon-o-speed-5:before {content:"\e701"}
    .icon-o-speed-6:before {content:"\e702"}
    .icon-o-speed-giga:before {content:"\e672"}
    .icon-o-touch_changeplans:before {content:"\e703"}
    .icon-o-4kvideo:before {content:"\e6aa"}
    .icon-o-access_content:before {content:"\e6ab"}
    .icon-o-mobilepay_resistant:before {content:"\e6ac"}
    .icon-o-silentmode:before {content:"\e6ad"}
    .icon-o-speakers:before {content:"\e6ae"}
    .icon-o-notifications:before {content:"\e6af"}
    .icon-o-stylus:before {content:"\e6b0"}
    .icon-o-touch_id:before {content:"\e6b1"}
    .icon-o-water_dust_resistant:before {content:"\e6b2"}
    .icon-o-home_easyaccess:before {content:"\e6b3"}
    .icon-o-multitasking:before {content:"\e6b4"}
    .icon-o-position_recognition:before {content:"\e6b5"}
    .icon-o-physical_size:before {content:"\e6b6"}
    .icon-o-qwerty_keyboard:before {content:"\e6b7"}
    .icon-o-privacy_security:before {content:"\e6b8"}
    .icon-o-connected_devices:before {content:"\e6b9"}
    .icon-o-ptt_dedicated:before {content:"\e6ba"}
    .icon-o-rugged_design:before {content:"\e6bb"}
    .icon-o-aircommands:before {content:"\e6bc"}
    .icon-o-flip_phone:before {content:"\e6bf"}
    .icon-o-ptt_compatible:before {content:"\e6c0"}
    .icon-o-ilw_safety:before {content:"\e6bd"}
    .icon-o-military_grade:before {content:"\e6d4"}
    .icon-o-intrinsically_safe:before {content:"\e6d5"}
    .icon-o-turbo_stick:before {content:"\e6c1"}
    .icon-o-hdmi:before {content:"\e6d7"}
    .icon-o-contacts:before {content:"\e6d8"}
    .icon-o-messages:before {content:"\e6d9"}
    .icon-o-temperature:before {content:"\e6c9"}
    .icon-o-humidity_resistant:before {content:"\e6db"}
    .icon-o-vibration_resistant:before {content:"\e6dc"}
    .icon-o-plugging_landline_phone:before {content:"\e6dd"}
    .icon-o-wireless_charging:before {content:"\e6de"}
    .icon-o-ms_office:before {content:"\e6df"}
    .icon-o-retina_display:before {content:"\e6e0"}
    .icon-o-all_new_design:before {content:"\e6e1"}
    .icon-o-m8motion_coprocessor:before {content:"\e6e2"}
    .icon-o-isight_camera:before {content:"\e6e3"}
    .icon-o-facetime:before {content:"\e6e4"}
    .icon-o-22hrs_batterylife:before {content:"\e6e5"}
    .icon-o-htc_blinkfeed:before {content:"\e6ea"}
    .icon-o-dual_speaker:before {content:"\e6eb"}
    .icon-o-knock_code:before {content:"\e6ec"}
    .icon-o-smart_notice:before {content:"\e6f3"}
    .icon-o-optical_stabilization:before {content:"\e6be"}
    .icon-o-selective_focus:before {content:"\e6ca"}
    .icon-o-gorilla_glass:before {content:"\e6c2"}
    .icon-o-25h_talktime:before {content:"\e710"}
    .icon-o-48h_power:before {content:"\e711"}
    .icon-o-1_3megapixel_camera:before {content:"\e712"}
    .icon-o-2tb_memory:before {content:"\e713"}
    .icon-o-8megapixel_camera:before {content:"\e714"}
    .icon-o-mcafee_good:before {content:"\e715"}
    .icon-o-mcafee_better:before {content:"\e716"}
    .icon-o-mcafee_best:before {content:"\e717"}
    .icon-o-video_game01:before {content:"\e718"}
    .icon-o-video_game02:before {content:"\e719"}
    .icon-o-video_game03:before {content:"\e71a"}
    .icon-o-all_metalbody:before {content:"\e6c3"}
    .icon-o-fast_charging:before {content:"\e6c4"}
    .icon-o-21h_talktime:before {content:"\e6c5"}
    .icon-o-stamina_mode:before {content:"\e6c7"}
    .icon-o-tomorrow_tech:before {content:"\e800"}
    .icon-o-24cc:before {content:"\e801"}
    .icon-o-sport-baseball:before {content:"\e802"}
    .icon-o-movie-ticket:before {content:"\e803"}
    .icon-o-tv-preview:before {content:"\e804"}
    .icon-o-fibe-restart:before {content:"\e805"}
    .icon-o-fibe-on-demand:before {content:"\e806"}
    .icon-o-whats-on:before {content:"\e807"}
    .icon-o-fibetv:before {content:"\e809"}
    .icon-o-kids:before {content:"\e80a"}
    .icon-o-search:before {content:"\e80b"}
    .icon-o-smart-acc2:before {content:"\e80c"}
    .icon-o-recordings:before {content:"\e80d"}
    .icon-o-screen-choice:before {content:"\e80e"}
    .icon-o-screen-choice:before {
        content: "\e901";
    }
    .icon-o-4k-hdtv:before {
        content: "\e937";
    }
    .icon-o-netflix:before {
        content: "\e936";
    }
    .icon-o-fibe-speed:before {
        content: "\e934";
    }
    .icon-o-sound-quality:before {
        content: "\e933";
    }
    .icon-o-restart:before {
        content: "\e92f";
    }
    .icon-o-pvr:before {
        content: "\e930";
    }
    .icon-o-move-tv:before {
        content: "\e931";
    }
    .icon-o-home-wifi:before {
        content: "\e932";
    }
    .icon-o-checklist:before {
        content: "\e92b";
    }
    .icon-o-call-management:before {
        content: "\e92e";
    }
    .icon-o-exclamation-circle-alt:before {
        content: "\e926";
    }
    .icon-o-screen-reader:before {
        content: "\e92a";
    }
    .icon-o-cc:before {
        content: "\e92d";
    }
    .icon-o-hearing-aid:before {
        content: "\e929";
    }
    .icon-o-phone-lock:before {
        content: "\e928";
    }
    .icon-o-contact-list:before {
        content: "\e92c";
    }
    .icon-o-alert:before {
        content: "\e927";
    }
    .icon-o-fastest:before {
        content: "\e91e";
    }
    .icon-o-never:before {
        content: "\e91f";
    }
    .icon-o-arrow-up:before {
        content: "\e920";
    }
    .icon-o-facetime-hd:before {
        content: "\e921";
    }
    .icon-specifications.icon-o-facetime-hd:before {
        top: 60%;
    }
    .icon-o-eye:before {
        content: "\e922";
    }
    .icon-o-wheelchair:before {
        content: "\e925";
    }
    .icon-o-hearing:before {
        content: "\e923";
    }
    .icon-o-puzzle-piece:before {
        content: "\e924";
    }
    .icon-o-anti-spam:before {
        content: "\e916";
    }
    .icon-o-unlimited:before {
        content: "\e917";
    }
    .icon-o-mobilietv-addon:before {
        content: "\e918";
    }
    .icon-o-voicemail:before {
        content: "\e919";
    }
    .icon-o-shield-reliable:before {
        content: "\e91a";
    }
    .icon-o-alarm:before {
        content: "\e91b";
    }
    .icon-o-whole-home-pvr:before {
        content: "\e91c";
    }
    .icon-o-operating-system:before {
        content: "\e91d";
    }
    .icon-o-voice-to-text:before {
        content: "\e915";
    }
    .icon-o-message-center:before {
        content: "\e914";
    }
    .icon-o-battery-charge-h:before {
        content: "\e911";
    }
    .icon-o-close-outline-lite:before {
        content: "\e90f";
    }
    .icon-o-24h-battery:before {
        content: "\e90c";
    }
    .icon-o-colour-display:before {
        content: "\e908";
    }
    .icon-o-high-humidity:before {
        content: "\e909";
    }
    .icon-o-user-plus:before {
        content: "\e90a";
    }
    .icon-o-5mp-camera:before {
        content: "\e90b";
    }
    .icon-o-quick-setup:before {
        content: "\e907";
    }
    .icon-o-glove-wetfingers:before {
        content: "\e905";
    }
    .icon-o-21mp-camera:before {
        content: "\e90e";
    }
    .icon-o-8mp-isight:before {
        content: "\e90d";
    }
    .icon-specifications.icon-o-8mp-isight:before{
        top: 70%;
        left: 70%;
    }
    .icon-o-5mp-camera:before {
        content: "\e90b";
    }
    
    .icon-o-cloud-backup:before {
        content: "\e913";
    }
    .icon-o-upload:before {
        content: "\e65d";
        -moz-transform: rotate(180deg);
        -webkit-transform: rotate(180deg);
        -ms-transform: rotate(180deg);
        -o-transform: rotate(180deg);
        transform: rotate(180deg);
        display:inline-block
    }
    .icon-o-memory-card2:before {
        content: "\e912";
    }
    .icon-o-id-alt:before {
        content: "\e935";
    }
    .icon-data-0:before {
      content: "\e926";
    }
    .icon-data-1:before {
      content: "\e927";
    }
    .icon-data-2:before {
      content: "\e928";
    }
    .icon-data-3:before {
      content: "\e929";
    }
    .icon-data-4:before {
      content: "\e92a";
    }
    .icon-data-5:before {
      content: "\e92b";
    }
    .icon-data-6:before {
      content: "\e92c";
    }
    .icon-data-7:before {
      content: "\e92d";
    }
    .icon-infinity:before {
      content: "\e92e";
    }
    .icon-expand-solid:before {
      content: "\e92f";
    }
    .icon-collapse-solid:before {
      content: "\e930";
    }
     .icon-blog-en:before {
        content: "\e904";
    }
    .icon-blog-fr:before {
        content: "\e905";
    }
    .icon-subscriber:before {
        content: "\e908";
    }
    .icon-contract:before {
        content: "\e907";
    }
    .icon-billing:before {
        content: "\e906";
    }
    .icon-billing-solid:before {
        content: "\e909";
    }
    .icon-billing-account:before {
        content: "\e90a";
    }
    .icon-more-details-pill:before {
        content: "\e90b";
    }
    .icon-more-details-down:before {
        content: "\e90b";
        -webkit-transform: rotate(90deg); /* Safari and Chrome */
        -moz-transform: rotate(90deg);   /* Firefox */
        -ms-transform: rotate(90deg);   /* IE 9 */
        -o-transform: rotate(90deg);   /* Opera */
        transform: rotate(90deg);
        display:inline-block
    }
    .icon-more-details-up:before {
        content: "\e90b";
        -webkit-transform: rotate(-90deg); /* Safari and Chrome */
        -moz-transform: rotate(-90deg);   /* Firefox */
        -ms-transform: rotate(-90deg);   /* IE 9 */
        -o-transform: rotate(-90deg);   /* Opera */
        transform: rotate(-90deg);
        display:inline-block
    }
    .icon-subscriber-outline-circled:before {
        content: "\e90c";
    }
    .icon-info-outline-circled:before {
        content: "\e90d";
    }
    .icon-collapse-outline-circled:before {
        content: "\e90e";
    }
    .icon-exapnd-outline-circled:before {
        content: "\e90f";
    }
    .icon-date-setting:before {
        content: "\e910";
    }
    .icon-pending-transaction:before {
        content: "\e911";
    }
    .icon-billing-outline:before {
        content: "\e912";
    }
    .icon-checkmark-circled:before {
        content: "\e913";
    }
    .icon-contract-outline:before {
        content: "\e914";
    }
    .icon-printer-ouline:before {
        content: "\e915";
    }
    .icon-settings-solid:before {
        content: "\e916";
    }
    .icon-group-subscriber:before {
        content: "\e917";
    }
    .icon-filter:before {
        content: "\e918";
    }
    .icon-search:before {
        content: "\e919";
    }
    .icon-sort:before {
        content: "\e91a";
    }
    .icon-print:before {
        content: "\e91b";
    }
    .icon-download:before {
        content: "\e91c";
    }
    .icon-sort-desc:before {
        content: "\e91d";
    }
    .icon-sort-asc:before {
        content: "\e91e";
    }
    .icon-copyright:before {
        content: "\e91f";
    }
    .icon-csv:before {
        content: "\e920";
    }
    .icon-no-reciever:before {
        content: "\e921";
    }
    .icon-live-tv:before {
        content: "\e922";
    }
    .i-icon:before {
        content: "\e923";
    }
    .icon-x-close:before {
        content: "\e924";
    }
    .icon-gift:before {
      content: "\e931";
    }
    .icon-person:before {
      content: "\e932";
    }
    .icon-warning:before {
      content: "\e933";
    }
    .icon-date:before {
      content: "\e934";
    }
    .icon-target:before {
      content: "\e935";
    }
    .icon-avatar:before {
      content: "\e937";
    }
    .icon-bright-star:before {
      content: "\e936";
    }
    .icon-trash:before {
      content: "\e938";
    }
    .icon-bundles:before {
      content: "\e939";
    }
    .icon-auto:before {
      content: "\e93a";
    }
    .icon-Bell_Business_Advantage:before {
      content: "\e93b";
    }
    .icon-call-dashboard:before {
      content: "\e93c";
    }
    .icon-call-hold:before {
      content: "\e93d";
    }
    .icon-call-manager:before {
      content: "\e93e";
    }
    .icon-Complete-your-package-2:before {
      content: "\e93f";
    }
    .icon-Complete-your-package-3:before {
      content: "\e940";
    }
    .icon-Complete-your-package-4:before {
      content: "\e941";
    }
    .icon-conf-500:before {
      content: "\e942";
    }
    .icon-conf-1000:before {
      content: "\e943";
    }
    .icon-conf-2000:before {
      content: "\e944";
    }
    .icon-conf-event:before {
      content: "\e945";
    }
    .icon-conf-min:before {
      content: "\e946";
    }
    .icon-hunt-groups:before {
      content: "\e947";
    }
    .icon-increase-productivity:before {
      content: "\e948";
    }
    .icon-long-disance:before {
      content: "\e949";
    }
    .icon-Medium-and-large-business:before {
      content: "\e94a";
    }
    .icon-never-miss-opportunity:before {
      content: "\e94b";
    }
    .icon-professional-service:before {
      content: "\e94c";
    }
    .icon-Small-Business:before {
      content: "\e94d";
    }
    .icon-stay-connected:before {
      content: "\e94e";
    }
    .icon-toll-free:before {
      content: "\e94f";
    }
    .icon-voicemail:before {
      content: "\e950";
    }
    .icon-Wholesale:before {
      content: "\e951";
    }
    .icon-Complete-your-package-1:before {
      content: "\e952";
    }
    
    .icon-three-way-calling:before {
      content: "\e953";
    }
    .icon-call-blocking:before {
      content: "\e954";
    }
    .icon-speed-dial:before {
      content: "\e955";
    }
    .icon-call-display:before {
      content: "\e956";
    }
    .icon-call-line-blocking:before {
      content: "\e957";
    }
    .icon-call-forwarding:before {
      content: "\e958";
    }
    .icon-call-park:before {
      content:"\e959";
    }
    .icon-call-trace:before {
      content: "\e95a";
    }
    .icon-call-transfer:before {
      content: "\e95b";
    }
    .icon-call-waiting:before {
      content: "\e95c";
    }
    .icon-mobile-integration:before {
      content: "\e95d";
    }
    .icon-mobile-app:before {
      content: "\e95e";
    }
    .icon-find-me:before {
      content: "\e95f";
    }
    .icon-last-numer-redial:before {
      content: "\e960";
    }
    .icon-simultaneously-ring:before {
      content: "\e961";
    }
    .icon-enterprise-directory:before {
      content: "\e962";
    }
    .icon-remote:before {
      content: "\e963";
    }
    .icon-donot-disturb:before {
      content: "\e964";
    }
    .icon-o-creditcard:before {
      content: "\e965";
    }
    .icon-speed:before {
      content: "\e639";
    }
    .icon-infinity-no-pad:before {
      content: "\e966";
    }
    .icon-Bell_Business_Advantage-circled:before {
      content: "\e967";
    }
    .icon-cellphone-nopad:before {
      content: "\e603";
    }
    .icon-chat-bubble-nopad:before {
      content: "\e604";
    }
    .icon-globe-nopad:before {
      content: "\e60f";
    }
    .icon-handset-nopad:before {
      content: "\e610";
    }
    .icon-headphones-nopad:before {
      content: "\e611";
    }
    .icon-laptop-nopad:before {
      content: "\e615";
    }
    .icon-tv-nopad:before {
      content: "\e624";
    }
    .icon-upload-nopad:before {
      content: "\e625";
    }
    .icon-download-few-times-a-week-nopad:before {
      content: "\e62b";
    }
    .icon-memory-card-nopad:before {
      content: "\e646";
    }
    .icon-edit-nopad:before {
      content: "\e65f";
    }
    .icon-fiberoptics-nopad:before {
      content: "\e663";
    }
    .icon-learnmoreaboutmcAfee-nopad:before {
      content: "\e670";
    }
    .icon-mic-nopad:before {
      content: "\e673";
    }
    .icon-overthephone-nopad:before {
      content: "\e67d";
    }
    .icon-phonenumber-nopad:before {
      content: "\e67f";
    }
    .icon-repairs-installs-troubleshooting-nopad:before {
      content: "\e682";
    }
    .icon-wifi-nopad:before {
      content: "\e689";
    }
    .icon-secureinternetconnection-nopad:before {
      content: "\e691";
    }
    .icon-family_contact_card-nopad:before {
      content: "\e6a1";
    }
    .icon-mcAfee-nopad:before {
      content: "\e6a3";
    }
    .icon-threeway-calling-nopad:before {
      content: "\e6a6";
    }
    .icon-mcafee-better-nopad:before {
      content: "\e716";
    }
    .icon-mcafee-best-nopad:before {
      content: "\e717";
    }
    .icon-fibe-on-demand-nopad:before {
      content: "\e806";
    }
    .icon-fibetv-nopad:before {
      content: "\e809";
    }
    .icon-search-nopad:before {
      content: "\e80b";
    }
    .icon-48installation:before {
      content: "\e900";
    }
    .icon-calling-features:before {
      content: "\e901";
    }
    .icon-download-speed-1:before {
      content: "\e902";
    }
    .icon-download-speed-2:before {
      content: "\e903";
    }
    .icon-download-speed-3:before {
      content: "\e904";
    }
    .icon-download-speed-4:before {
      content: "\e905";
    }
    .icon-download-speed-5:before {
      content: "\e906";
    }
    .icon-longdistance-features:before {
      content: "\e907";
    }
    .icon-wireless-connection:before {
      content: "\e908";
    }
    .icon-clock:before {
      content: "\e909";
    }
    .icon-storage-nopad:before {
      content: "\e90a";
    }
    .icon-existing-customer-nopad:before {
      content: "\e90b";
    }
    .icon-online-store-nopad:before {
      content: "\e90c";
    }
    .icon-stress-free-nopad:before {
      content: "\e90d";
    }
    .icon-web-design-nopad:before {
      content: "\e90e";
    }
    .icon-web-hosting-nopad:before {
      content: "\e90f";
    }
    .icon-installation-nopad:before {
      content: "\e910";
    }
    .icon-alarm-nopad:before {
      content: "\e911";
    }
    .icon-email-account-nopad:before {
      content: "\e912";
    }
    .icon-move-nopad:before {
      content: "\e913";
    }
    .icon-protection-nopad:before {
      content: "\e914";
    }
    .icon-restart-nopad:before {
      content: "\e915";
    }
    .icon-transfer-nopad:before {
      content: "\e916";
    }
    .icon-voicemail-nopad:before {
      content: "\e917";
    }
    .icon-call-forwarding-nopad:before {
      content: "\e918";
    }
    .icon-speed-range:before {
      content: "\e919";
    }
    .icon-most-popular-speed:before {
      content: "\e91a";
    }
    .icon-calendar31:before {
      content: "\e91b";
    }
    .icon-album-star:before {
      content: "\e91c";
    }
    .icon-modem-nopad:before {
      content: "\e91d";
    }
    .icon-security-software-nopad:before {
      content: "\e91e";
    }
    .icon-four-choice-nopad:before {
      content: "\e91f";
    }
    .icon-memorycardsingle-nopad:before {
      content: "\e920";
    }
    .icon-memorycardtwo-nopad:before {
      content: "\e921";
    }
    .icon-memorycardthree-nopad:before {
      content: "\e922";
    }
    .icon-memorycardfour-nopad:before {
      content: "\e923";
    }
    .icon-wireless-connection-two-nopad:before {
      content: "\e924";
    }
    .icon-newsletter-nopad:before {
      content: "\e925";
    }
    .icon-filemanager-nopad:before {
      content: "\e926";
    }
    .icon-ftpmanager-nopad:before {
      content: "\e927";
    }
    .icon-sharing-nopad:before {
      content: "\e928";
    }
    .icon-speech-bubble-nopad:before {
      content: "\e929";
    }
    .icon-conferencing-nopad:before {
      content: "\e92a";
    }
    
    .icon-user-1:before {
      content: "\e92d";
    }
    .icon-user-2:before {
      content: "\e92e";
    }
    .icon-user-3:before {
      content: "\e932";
    }
    .icon-user-4:before {
      content: "\e92f";
    }
    .icon-user-5:before {
      content: "\e92b";
    }
    .icon-user-6:before {
      content: "\e92c";
    }
    .icon-user-7:before {
      content: "\e931";
    }
    .icon-user-8:before {
      content: "\e930";
    }
    .icon-user-9:before {
      content: "\e933";
    }
    
    .icon-user-1-2:before {
      content: "\e934";
    }
    .icon-user-1-3:before {
      content: "\e935";
    }
    .icon-user-1-4:before {
      content: "\e936";
    }
    .icon-user-1-5:before {
      content: "\e937";
    }
    .icon-user-1-6:before {
      content: "\e938";
    }
    .icon-user-1-7:before {
      content: "\e939";
    }
    .icon-movies:before {
      content: "\e93a";
    }
    .icon-gaming:before {
      content: "\e93b";
    }
    .icon-music-stream:before {
      content: "\e93c";
    }
    .icon-web-browsing:before {
      content: "\e93d";
    }
    .icon-envelope2:before {
      content: "\e93e";
    }
    .icon-sort-dec-sm:before {
      content: "\e93f";
    }
    .icon-sort-asc-sm:before {
      content: "\e940";
    }
    
    .icon-wifi-circled:before {
      content: "\e941";
    }
    .icon-mcafee-circled:before {
      content: "\e942";
    }
    .icon-install-circled:before {
      content: "\e943";
    }
    .icon-email-circled:before {
      content: "\e944";
    }
    .icon-download-speed-6:before {
      content: "\e945";
    }
    .icon-smart-touch-nopad:before {
      content: "\e946";
    }
    .icon-mobile-tv-nopad:before {
      content: "\e947";
    }
    .icon-bundles:before {
      content: "\e948";
    }
    .icon-alert-circled:before {
      content: "\e949";
    }
    .icon-check-balance-nopad:before {
      content: "\e94a";
    }
    .icon-call-return-nopad:before {
      content: "\e94b";
    }
    .icon-call-screen-nopad:before {
      content: "\e94c";
    }
    .icon-call-ident-nopad:before {
      content: "\e94d";
    }
    .icon-line-hunting-nopad:before {
      content: "\e94e";
    }
    .icon-call-waiting-nopad:before {
      content: "\e94f";
    }
    .icon-light-bulb-nopad:before {
      content: "\e950";
    }
    .icon-alldayhours-nopad:before {
      content: "\e951";
    }
    .icon-dnld-speed-0:before {
      content: "\e952";
    }
    .icon-dnld-speed-1:before {
      content: "\e953";
    }
    .icon-dnld-speed-2:before {
      content: "\e954";
    }
    .icon-dnld-speed-3:before {
      content: "\e955";
    }
    .icon-dnld-speed-4:before {
      content: "\e956";
    }
    .icon-dnld-speed-5:before {
      content: "\e957";
    }
    .icon-dnld-speed-6:before {
      content: "\e958";
    }
    .icon-dnld-speed-7:before {
      content: "\e959";
    }
    .icon-dnld-speed-8:before {
      content: "\e95a";
    }
    .icon-dnld-speed-9:before {
      content: "\e95b";
    }
    .icon-dnld-speed-10:before {
      content: "\e95c";
    }
    .icon-business-phone-nopad:before {
      content: "\e95d";
    }
    /*webconferencing page icons*/
    .icon-webconferencing-capabilities:before {
      content: "\e95e";
    }
    .icon-webconferencing-conferencing:before {
      content: "\e95f";
    }
    .icon-webconferencing-infinity:before {
      content: "\e960";
    }
    .icon-webconferencing-participants:before {
      content: "\e961";
    }
    .icon-webconferencing-recording:before {
      content: "\e962";
    }
    .icon-webconferencing-sharing:before {
      content: "\e963";
    }
    .icon-webconferencing-support:before {
      content: "\e964";
    }
    .icon-home-phone:before {
      content: "\e9bd";
    }
    .icon-internet:before {
      content: "\e9be";
    }
    .icon-mobile:before {
      content: "\e9bf";
    }
    .icon-tablet:before {
      content: "\e9c0";
    }
    
    /*2pt outline*/
    .icon-data-7.outline2pt:before {
      content: "\e9c6";
    }
    .icon-data-6.outline2pt:before {
      content: "\e9c7";
    }
    .icon-data-5.outline2pt:before {
      content: "\e9c8";
    }
    .icon-data-4.outline2pt:before {
      content: "\e9c9";
    }
    .icon-data-3.outline2pt:before {
      content: "\e9ca";
    }
    .icon-data-2.outline2pt:before {
      content: "\e9cb";
    }
    .icon-data-1.outline2pt:before {
      content: "\e9cc";
    }
    .icon-download-speed-0.outline2pt:before {
      content: "\e9cd";
    }
    .icon-download-speed-1.outline2pt:before {
      content: "\e9ce";
    }
    .icon-download-speed-2.outline2pt:before {
      content: "\e9cf";
    }
    .icon-download-speed-3.outline2pt:before {
      content: "\e9d0";
    }
    .icon-download-speed-4.outline2pt:before {
      content: "\e9d1";
    }
    .icon-download-speed-5.outline2pt:before {
      content: "\e9d2";
    }
    .icon-download-speed-6.outline2pt:before {
      content: "\e9d3";
    }
    .icon-download-speed-7.outline2pt:before {
      content: "\e9d4";
    }
    .icon-download-speed-8.outline2pt:before {
      content: "\e9d5";
    }
    .icon-download-speed-9.outline2pt:before {
      content: "\e9d6";
    }
    .icon-download-speed-10.outline2pt:before {
      content: "\e9d7";
    }
    .icon-infinity.outline2pt:before {
      content: "\e9d8";
    }
    .icon-upload-speed.outline2pt:before {
      content: "\e9db";
    }
    .icon-download-speed.outline2pt:before {
      content: "\e9d9";
    }
    .icon-wi-fi.outline2pt:before {
      content: "\e9dd";
    }
    .icon-data-usage.outline2pt:before {
      content: "\e9de";
    }
    .icon-email.outline2pt:before {
      content: "\e9df";
    }
    .icon-expand.outline2pt:before {
      content: "\e9e0";
    }
    .icon-collapse.outline2pt:before {
      content: "\e9e1";
    }
    .icon-close.outline2pt:before {
      content: "\e9e2";
    }
    .icon-info.outline2pt:before {
      content: "\e9e3";
    }
    .icon-success.outline2pt:before {
      content: "\e9e4";
    }
    .icon-warning.outline2pt:before {
      content: "\e9e5";
    }
    .icon-hints-tips.outline2pt:before {
      content: "\e9e6";
    }
    .icon-McAfee-best.outline2pt:before {
      content: "\e9e7";
    }
    .icon-McAfee-better.outline2pt:before {
      content: "\e9e8";
    }
    .icon-McAfee-good.outline2pt:before {
      content: "\e9e9";
    }
    .icon-mostpopular.outline2pt:before {
      content: "\e9ea";
    }
    .icon-movie.outline2pt:before {
      content: "\e9eb";
    }
    .icon-moving.outline2pt:before {
      content: "\e9ec";
    }
    .icon-music.outline2pt:before {
      content: "\e9ed";
    }
    .icon-other-features.outline2pt:before {
      content: "\e9ee";
    }
    .icon-talk-to-us-now.outline2pt:before {
      content: "\e9ef";
    }
    .icon-troubleshooting.outline2pt:before {
      content: "\e9f0";
    }
    .icon-video-games.outline2pt:before {
      content: "\e9f1";
    }
    .icon-webbrowsing.outline2pt:before {
      content: "\e9f2";
    }
    .icon-arrow.outline2pt:before {
      content: "\e9f3";
    }
    .icon-e-mail-circled.outline2pt:before {
      content: "\e9f5";
    }
    .icon-movies-circled.outline2pt:before {
      content: "\e9f6";
    }
    .icon-music-circled.outline2pt:before {
      content: "\e9f7";
    }
    .icon-photos-circled.outline2pt:before {
      content: "\e9f8";
    }
    .icon-security-circled.outline2pt:before {
      content: "\e9fb";
    }
    .icon-settings-circled.outline2pt:before {
      content: "\e9fa";
    }
    .icon-videos-circled.outline2pt:before {
      content: "\e9f9";
    }
    .icon-wi-fi-circled.outline2pt:before {
      content: "\e9f4";
    }
    /*2pt icons end*/
    
    
    .icon-VR:before {
      content: "\e9fc";
    }
    .icon-o-VR:before {
      content: "\e900";
    }
    .icon-rocket:before {
      content: "\e9fd";
    }
    .icon-tower:before {
      content: "\e9fe";
    }
    .icon-tablet-smartphone:before {
      content: "\e9dc";
    }
    
    /*webconferencing page icons end*/
    
    .icon-download-bold:before {
      content: "\e9ba";
    }
    .icon-speed-lte-nopad:before {
      content: "\e965";
    }
    .icon-circle-better-consistency:before {
      content: "\e966";
    }
    .icon-circle-faster-speed:before {
      content: "\e967";
    }
    .icon-circle-tomorrow-technology:before {
      content: "\e968";
    }
    .icon-software-update-nopad:before {
      content: "\e969";
    }
    .icon-conf-min-nopad:before {
      content: "\e96a";
    }
    .icon-collapse-bold:before {
      content: "\e96b";
    }
    .icon-expand-bold:before {
      content: "\e96c";
    }
    .icon-bold-call-blocking:before {
      content: "\e96d";
    }
    .icon-bold-call-forwarding:before {
      content: "\e96e";
    }
    .icon-bold-call-display:before {
      content: "\e96f";
    }
    .icon-bold-call-forwarding2:before {
      content: "\e970";
    }
    .icon-bold-call-hold:before {
      content: "\e971";
    }
    .icon-bold-call-hold:before {
      content: "\e971";
    }
    .icon-bold-call-line-blocking:before {
      content: "\e972";
    }
    .icon-bold-call-park:before {
      content: "\e973";
    }
    .icon-bold-call-return:before {
      content: "\e974";
    }
    .icon-bold-call-transfer:before {
      content: "\e975";
    }
    .icon-bold-call-screen:before {
      content: "\e976";
    }
    .icon-bold-call-trace:before {
      content: "\e977";
    }
    .icon-bold-call-waiting:before {
      content: "\e978";
    }
    .icon-bold-line-hunting:before {
      content: "\e979";
    }
    .icon-bold-dont-disturb:before {
      content: "\e97a";
    }
    .icon-bold-enterprise-directory:before {
      content: "\e97b";
    }
    .icon-bold-find-me:before {
      content: "\e97c";
    }
    .icon-bold-ident-call:before {
      content: "\e97d";
    }
    .icon-bold-last-number:before {
      content: "\e97e";
    }
    .icon-bold-mobile-app:before {
      content: "\e97f";
    }
    .icon-bold-mobile-integration:before {
      content: "\e980";
    }
    .icon-bold-remote:before {
      content: "\e981";
    }
    .icon-bold-simultaneously-ring:before {
      content: "\e982";
    }
    .icon-bold-speed-dial:before {
      content: "\e983";
    }
    .icon-bold-three-way-calling:before {
      content: "\e984";
    }
    .icon-bold-visual-call:before {
      content: "\e985";
    }
    .icon-bold-voice-dialing:before {
      content: "\e986";
    }
    .icon-bold-voicemail:before {
      content: "\e987";
    }
    .icon-bold-appointment:before {
      content: "\e988";
    }
    .icon-bold-disk-manager:before {
      content: "\e989";
    }
    .icon-bold-file-manager:before {
      content: "\e98a";
    }
    .icon-bold-ftp-manager:before {
      content: "\e98b";
    }
    .icon-bold-newsletter:before {
      content: "\e98c";
    }
    .icon-bold-online-builder:before {
      content: "\e98d";
    }
    .icon-bold-photo-gallery:before {
      content: "\e98e";
    }
    .icon-bold-search:before {
      content: "\e98f";
    }
    .icon-bold-sell-online:before {
      content: "\e990";
    }
    .icon-play-triangle:before {
      content: "\e991";
    }
    .icon-most-popular:before {
        content: "\e992";
    }
    .icon-most-popular-oulined:before {
      content: "\e993";
    }
    .icon-email-marketing:before {
      content: "\e994";
    }
    .icon-auto-attendant:before {
      content: "\e995";
    }
    .icon-bold-btc-app:before {
      content: "\e996";
    }
    .icon-bold-call-park2:before {
      content: "\e997";
    }
    .icon-bold-call-transfer2:before {
      content: "\e998";
    }
    .icon-bold-find-me2:before {
      content: "\e999";
    }
    .icon-bold-glossary:before {
      content: "\e99a";
    }
    .icon-bold-ident-call2:before {
      content: "\e99b";
    }
    .icon-bold-last-number-redial2:before {
      content: "\e99c";
    }
    .icon-bold-line-hunting2:before {
      content: "\e99d";
    }
    .icon-bold-remote-office:before {
      content: "\e99e";
    }
    .icon-bold-visual-call2:before {
      content: "\e99f";
    }
    .icon-bold-voice-dialing2:before {
      content: "\e9a0";
    }
    .icon-bold-voicemail-email:before {
      content: "\e9a1";
    }
    .icon-btc-app:before {
      content: "\e9a2";
    }
    .icon-call-dashboard2:before {
      content: "\e9a3";
    }
    .icon-call-park2:before {
      content: "\e9a4";
    }
    .icon-design-tools:before {
      content: "\e9a5";
    }
    .icon-device-storage:before {
      content: "\e9a6";
    }
    .icon-email-marketing2:before {
      content: "\e9a7";
    }
    .icon-find-me2:before {
      content: "\e9a8";
    }
    .icon-hunt-groups2:before {
      content: "\e9a9";
    }
    .icon-last-number-redial2:before {
      content: "\e9aa";
    }
    .icon-network-security:before {
      content: "\e9ab";
    }
    .icon-phone-number-reservation:before {
      content: "\e9ac";
    }
    .icon-reliability:before {
      content: "\e9ad";
    }
    .icon-remote-office:before {
      content: "\e9ae";
    }
    .icon-virtual-phone-system:before {
      content: "\e9af";
    }
    .icon-visual-call2:before {
      content: "\e9b0";
    }
    .icon-voice-dialing2:before {
      content: "\e9b1";
    }
    .icon-high-definition:before {
      content: "\e9b2";
    }
    .icon-new-customer:before {
      content: "\e9b3";
    }
    .icon-three-devices-tablet:before {
      content: "\e9b4";
    }
    .icon-storage2-nopad:before {
      content: "\e9b5";
    }
    .icon-bandwidth-1:before {
      content: "\e9b6";
    }
    .icon-bandwidth-2:before {
      content: "\e9b7";
    }
    .icon-bandwidth-3:before {
      content: "\e9b8";
    }
    .icon-bandwidth-4:before {
      content: "\e9b9";
    }
    .icon-download-nopad:before {
      content: "\e9bb";
    }
    .icon-list-circle:before {
      content: "\e9bc";
    }.icon-multiple:before {
      content: "\e9c1";
    }.icon-current_bill:before {
      content: "\e9c2";
    }.icon-detailed_bill:before {
      content: "\e9c3";
    }
     .icon-caret-top:before {
      content: "\e9c4";
    }
     .icon-mcafee-good-nopad:before {
      content: "\e9c5";
    }
     .icon-list-star:before {
      content: "\e9da";
    }
     .icon-scroll-left:before {
      content: "\e9ff";
    }
     .icon-scroll-right:before {
      content: "\ea00";
    }
    .icon-exclamation-notification:before {
      content: "\ea02";
    }
    .icon-locked-data:before {
      content: "\ea03";
    }
    .icon-tv-no-pad:before {
      content: "\e6c8";
    }
    .icon-internet-no-pad:before {
      content: "\e6cb";
    }
    .icon-mobility-no-pad:before {
      content: "\e6cc";
    }
    .icon-homephone-no-pad:before {
      content: "\e6cd";
    }
    .icon-chevron-left2:before {
      content: "\ea04";
    }
    .icon-circle-alert:before {
      content: "\ea05";
    }
    .icon-circle-apple:before {
      content: "\ea06";
    }
    .icon-circle-clipboard:before {
      content: "\ea07";
    }
    .icon-circle-delivery:before {
      content: "\ea08";
    }
    .icon-circle-film:before {
      content: "\ea09";
    }
    .icon-circle-pin-number:before {
      content: "\ea0a";
    }
    .icon-circle-receiver:before {
      content: "\ea0b";
    }
    .icon-circle-record:before {
      content: "\ea0c";
    }
    .icon-circle-remote:before {
      content: "\ea0d";
    }
    .icon-circle-satellite:before {
      content: "\ea0e";
    }
    .icon-circle-usb:before {
      content: "\ea0f";
    }
    .icon-circle-video:before {
      content: "\ea10";
    }
    .icon-circle-receiver2:before {
      content: "\ea11";
    }
    .icon-circle-tv-guide:before {
      content: "\ea12";
    }
    .icon-circle-upgrade:before {
      content: "\ea13";
    }
    .icon-channel-lineup:before {
      content: "\ea14";
    }
    .icon-cell-phone2:before {
      content: "\ea15";
    }
    .icon-collapse2:before {
      content: "\ea16";
    }
    .icon-expand2:before {
      content: "\ea17";
    }
    .icon-edit-small:before {
      content: "\ea18";
    }
    .icon-home-phone2:before {
      content: "\ea19";
    }
    .icon-hyperlink-chain:before {
      content: "\ea1a";
    }
    .icon-laptop2:before {
      content: "\ea1b";
    }
    .icon-tiny-collapse:before {
      content: "\ea1c";
    }
    .icon-tiny-expand:before {
      content: "\ea1d";
    }
    .icon-trash-icon:before {
      content: "\ea1e";
    }
    .icon-tv-screen2:before {
      content: "\ea1f";
    }
    .icon-unlink-account:before {
      content: "\ea20";
    }
    .icon-thunder-boost:before {
      content: "\ea21";
    }
    .icon-solid-silhouette:before {
      content: "\ea22";
    }
    .icon-o-paper-plane-no-pad:before {
        content: "\e6ce";
    }
    .icon-Add-ons:before {
        content: "\e90f";
    }
    .icon-change-my-rate-plan-no-pad:before {
        content: "\e6cf";
    }
    .icon-boost-circle-lightning:before {
      content: "\ea40";
    }
    .icon-boost-dnld-speed-0:before {
      content: "\ea41";
    }
    .icon-boost-dnld-speed-1:before {
      content: "\ea42";
    }
    .icon-boost-dnld-speed-2:before {
      content: "\ea43";
    }
    .icon-boost-dnld-speed-3:before {
      content: "\ea44";
    }
    .icon-boost-dnld-speed-4:before {
      content: "\ea45";
    }
    .icon-boost-dnld-speed-5:before {
      content: "\ea46";
    }
    .icon-boost-dnld-speed-6:before {
      content: "\ea47";
    }
    .icon-boost-dnld-speed-7:before {
      content: "\ea48";
    }
    .icon-boost-dnld-speed-8:before {
      content: "\ea49";
    }
    .icon-boost-dnld-speed-9:before {
      content: "\ea4a";
    }
    .icon-boost-dnld-speed-10:before {
      content: "\ea4b";
    }
    .icon-boost-upload-speed:before {
      content: "\ea4c";
    }
    
    .icon-bundle:before {
        content: "\ea24";
    }
    .icon-Call-control:before {
        content: "\ea25";
    }
    .icon-Long-distance:before {
        content: "\ea26";
    }
    .icon-Message-manage:before {
        content: "\ea27";
    }
    .icon-password:before {
        content: "\ea28";
    }
    .icon-Phonecare:before {
        content: "\ea29";
    }
    .icon-sharegroup:before {
        content: "\ea2a";
    }
    .icon-small-share-group:before {
        content: "\ea2b";
    }
    .icon-usage-cirlced:before {
        content: "\ea2c";
    }
    .icon-usage:before {
        content: "\ea2d";
    }
    .icon-Voice-dialing:before {
        content: "\ea2e";
    }
    .icon-Wirecare:before {
        content: "\ea2f";
    }
    .icon-wifi:before {
        content: "\ea30";
    }
    .icon-add-device:before {
      content: "\ea23";
    }
    .icon-play-no-pad:before {
      content: "\e6d0";
    }
    .icon-Share_group:before {
      content: "\ea31";
    }
    .icon-connected_car:before {
      content: "\ea32";
    }
    .icon-Calendar:before {
      content: "\ea33";
    }
    .icon-data-usage-no-pad:before {
      content: "\ea34";
    }
    .icon-AdditionalChannels:before {
      content: "\ea35";
    }
    .icon-IndividualChannels:before {
      content: "\ea36";
    }
    .icon-SportPackages:before {
      content: "\ea37";
    }
    .icon-YourMoviesAndSeries:before {
      content: "\ea38";
    }
    .icon-YourTVPackage:before {
      content: "\ea39";
    }
    .icon-International:before {
      content: "\ea3a";
    }
    .icon-lightning-fast:before {
      content: "\ea3b";
    }
    .icon-laptop-checklist:before {
      content: "\ea3c";
    }
    .icon-checklist-standalone:before {
      content: "\ea3d";
    }
    .icon-trash-icon-thin:before {
      content: "\ea3e";
    }
    .icon-hyperlink-chain-thin:before {
      content: "\ea3f";
    }
    .icon-SpecializedHDThemes:before {
      content: "\ea4e";
    }
    .icon-circle-silhouette:before {
      content: "\ea4d";
    }
    .icon-circle-multiple-homes:before {
      content: "\ea4f";
    }
    .icon-circle-location:before {
      content: "\ea50";
    }
    .icon-circle-home:before {
      content: "\ea51";
    }
    .icon-circle-cellphone-chart-up:before {
      content: "\ea52";
    }
    .icon-circle-cellphone:before {
      content: "\ea53";
    }
    .icon-circle-building:before {
      content: "\ea54";
    }
    .icon-circle-bellaliant:before {
      content: "\ea55";
    }
    .icon-circle-bellmts:before {
      content: "\ea56";
    }
    .icon-cicle-clock:before {
      content: "\ea57";
    }
    .icon-Product_Catalog:before {
      content: "\ea58";
    }
    .icon-New_Customer:before {
      content: "\ea59";
    }
    .icon-Filter:before {
      content: "\ea5a";
    }
    .icon_fridge:before {
      content: "\ea5b";
    }
    .icon-HomeBundle:before {
      content: "\ea5c";
    }
    .icon-bell:before {
      content: "\ea5d";
    }
    .icon-homephone:before {
      content: "\ea5e";
    }
    .icon-FibeTV:before {
      content: "\ea5f";
    }
    .icon-SatTV:before {
      content: "\ea60";
    }
    .icon-Internet:before {
      content: "\ea61";
    }
    .icon-Bell-aliant:before {
      content: "\ea62";
    }
    .icon-Bell-MTS:before {
      content: "\ea63";
    }
    .icon-building-outlined:before {
      content: "\ea64";
    }
    .icon-cellphone-chart-up-outlined:before {
      content: "\ea65";
    }
    .icon-Silhouette-outlined:before {
      content: "\ea66";
    }
    .icon-cellphone-outlined:before {
      content: "\ea67";
    }
    .icon-location-outlined:before {
      content: "\ea68";
    }
    .icon-home-outlined:before {
      content: "\ea69";
    }
    .icon-Clock-outlined:before {
      content: "\ea6a";
    }
    .icon-Search:before {
      content: "\ea6b";
    }
    .icon-ProductCatalogue:before {
      content: "\ea6c";
    }
    .icon-phone-increase-circled:before {
      content: "\ea6d";
    }
    .icon-Personal:before {
      content: "\ea6e";
    }
    .icon-mobility-circled:before {
      content: "\ea6f";
    }
    .icon-AccountManagement:before {
      content: "\ea70";
    }
    .icon-location-circled:before {
      content: "\ea71";
    }
    .icon-HardwareUpgrade:before {
      content: "\ea72";
    }
    .icon-corporate-circled:before {
      content: "\ea73";
    }
    .icon-AddLine:before {
      content: "\ea74";
    }
    .icon-AccountMigration:before {
      content: "\ea75";
    }
    .icon-FIBE_stroke:before {
      content: "\ea76";
    }
    .icon-Bell_MTS_stroke:before {
      content: "\ea77";
    }
    .icon-Bell_aliant_stroke:before {
      content: "\ea78";
    }
    .icon-moving-solid:before {
      content: "\ea79";
    }
    /*.icon-moving-outline:before {
      content: "\ea7a";
    }*/
    .icon-cart-circled:before {
      content: "\ea7a";
    }
    /*Multicolour icons*/
    /*Neutral emoji active*/
    .icon-emoji-neutral-active .path1:before {
      content: "\ea7f";
      color: #00549a;
    }
    .icon-emoji-neutral-active .path2:before {
      content: "\ea80";
      color: #fff;
      margin-left: -1em;
    }
    .icon-emoji-neutral-active .path3:before {
      content: "\ea81";
      color: #fff;
      margin-left: -1em;
    }
    .icon-emoji-neutral-active .path4:before {
      content: "\ea82";
      color: #fff;
      margin-left: -1em;
    }
    /*Neutral emoji inactive*/
    .icon-emoji-neutral .path1:before {
      content: "\ea7b";
      color: #d4d4d4;
    }
    .icon-emoji-neutral .path2:before {
      content: "\ea7c";
      color: #555;
      margin-left: -1em;
    }
    .icon-emoji-neutral .path3:before {
      content: "\ea7d";
      color: #555;
      margin-left: -1em;
    }
    .icon-emoji-neutral .path4:before {
      content: "\ea7e";
      color: #555;
      margin-left: -1em;
    }
    /*Sad emoji*/
    .icon-emoji-sad .path1:before {
      content: "\ea83";
      color: #d4d4d4;
    }
    .icon-emoji-sad .path2:before {
      content: "\ea84";
      color: #555;
      margin-left: -1em;
    }
    .icon-emoji-sad .path3:before {
      content: "\ea85";
      color: #555;
      margin-left: -1em;
    }
    .icon-emoji-sad .path4:before {
      content: "\ea86";
      color: #555;
      margin-left: -1em;
    }
    /*Sad emoji active*/
    .icon-emoji-sad-active .path1:before {
      content: "\ea87";
      color: #00549a;
    }
    .icon-emoji-sad-active .path2:before {
      content: "\ea88";
      color: #fff;
      margin-left: -1em;
    }
    .icon-emoji-sad-active .path3:before {
      content: "\ea89";
      color: #fff;
      margin-left: -1em;
    }
    .icon-emoji-sad-active .path4:before {
      content: "\ea8a";
      color: #fff;
      margin-left: -1em;
    }
    /*Smiley emoji*/ 
    .icon-emoji-smiley .path1:before {
      content: "\ea8b";
      color: #d4d4d4;
    }
    .icon-emoji-smiley .path2:before {
      content: "\ea8c";
      color: #555;
      margin-left: -1em;
    }
    .icon-emoji-smiley .path3:before {
      content: "\ea8d";
      color: #555;
      margin-left: -1em;
    }
    .icon-emoji-smiley .path4:before {
      content: "\ea8e";
      color: #555;
      margin-left: -1em;
    }
    /*Smiley emoji active*/ 
    .icon-emoji-smiley-active .path1:before {
      content: "\ea8f";
      color: #00549a;
    }
    .icon-emoji-smiley-active .path2:before {
      content: "\ea90";
      color: #fff;
      margin-left: -1em;
    }
    .icon-emoji-smiley-active .path3:before {
      content: "\ea91";
      color: #fff;
      margin-left: -1em;
    }
    .icon-emoji-smiley-active .path4:before {
      content: "\ea92";
      color: #fff;
      margin-left: -1em;
    }
    .icon-Audio:before {
      content: "\ea94";
    }
    .icon-Cases:before {
      content: "\ea95";
    }
    .icon-CCAndBatteries:before {
      content: "\ea96";
    }
    .icon-Essentials:before {
      content: "\ea97";
    }
    .icon-HandsFree:before {
      content: "\ea98";
    }
    .icon-IOT:before {
      content: "\ea99";
    }
    .icon-SaleAndClearance:before {
      content: "\ea9a";
    }
    .icon-ScreenProtection:before {
      content: "\ea9b";
    }
    .icon-Wearables:before {
      content: "\ea93";
    }
    .icon-morning:before {
      content: "\ea9c";
    }
    .icon-evening:before {
      content: "\ea9d";
    }
    .icon-afternoon:before {
      content: "\ea9e";
    }
    .icon-all-day:before {
      content: "\ea9f";
    }
    .icon-turbo_stick:before {
      content: "\eaa0";
    }
    .icon-tablet-landscape:before {
      content: "\eaa1";
    }
    .icon-BellMTS-logo:before {
      content: "\eaa2";
    }
    .icon-mobile-update-outline:before {
      content: "\eaa3";
    }
    .icon-sales-and-clearance-solid:before {
      content: "\eaa4";
    }
    .icon-mobile-internet:before {
      content: "\eaa5";
    }
    .icon-no-data:before {
      content: "\eaa6";
    }
    .icon-no-voice:before {
      content: "\eaa7";
    }
    .icon-personal-mobile:before {
      content: "\eaa8";
    }
    .icon-prepaid-mobility:before {
      content: "\eaa9";
    }
    .icon-province-wide:before {
      content: "\eaaa";
    }
    .icon-mobility_promotions:before {
        content: "\eaab";
    }
    .icon-MTS_migration:before {
        content: "\eaac";
    }
    .icon-order_dashboard:before {
        content: "\eaad";
    }
    .icon-product-catalogue:before {
        content: "\eaae";
    }
    .icon-order_search:before {
        content: "\eaaf";
    }
    .icon-direction:before {
        content: "\eab0";
    }
    .icon-add_device_account:before {
        content: "\eab1";
    }
    .icon-bell_Mobility_Small_Business:before {
        content: "\eab2";
    }
    .icon-new_Mobility_customer:before {
        content: "\eab3";
    }
    .icon-upgrade_device:before {
        content: "\eab4";
    }
    .icon-keypad:before {
        content: "\eab5";
    }
    .icon-small_circle_cart:before {
        content: "\eab6";
    }
    .icon-small_circle_cart_with_arrow:before {
        content: "\eab7";
    }
    .icon-small_circle_chat_now:before {
        content: "\eab8";
    }
    .icon-small_circle_contact:before {
        content: "\eab9";
    }
    .icon-small_circle_contact_solid:before {
        content: "\eaba";
    }
    .icon-small_circle_request_callback:before {
        content: "\eabb";
    }
    .icon-small_circle_request_callback_solid:before {
        content: "\eabc";
    }
    .icon-small-cart:before {
        content: "\eabd";
    }
    .icon-small-chat:before {
        content: "\eabe";
    }
    .icon-small-contact:before {
        content: "\eabf";
    }
    .icon-small-request-callback:before {
        content: "\eac0";
    }
    .icon-touchpad_icon_v1:before {
        content: "\eac1";
    }
    .icon-touchpad_icon_v2:before {
        content: "\eac2";
    }
    .icon-user_digital_pin_bl_ot:before {
        content: "\eac3";
    }
    .icon-Account_level_detail:before {
        content: "\eac4";
    }
    .icon-Customer_details:before {
        content: "\eac5";
    }
    .icon-Services:before {
        content: "\eac6";
    }
    .icon-Special_instructions:before {
        content: "\eac7";
    }
    .icon-status_Declined:before {
        content: "\eac8";
    }
    .icon-status_Declined_small:before {
        content: "\eac9";
    }
    .icon-status_Not_Set_Up:before {
        content: "\eaca";
    }
    .icon-status_Not_Set_Up_small:before {
        content: "\eacb";
    }
    
    .icon-status_Declined_small_mc .path1:before {
        content: "\eacc";
        color: #00549a;
    }
    
    .icon-status_Declined_small_mc .path2:before {
        content: "\eacd";
        color: #fefefe;
        margin-left: -1em;
    }
    
    .icon-status_Declined_small_mc .path3:before {
        content: "\eace";
        color: #fefefe;
        margin-left: -1em;
    }
    
    .icon-status_Not_Set_Up_small_mc .path1:before {
        content: "\eacf";
        color: #00549a;
    }
    
    .icon-status_Not_Set_Up_small_mc .path2:before {
        content: "\ead0";
        color: #fff;
        margin-left: -1em;
    }
    
    .icon-checkmark_bl_ot:before {
        content: "\ead1";
    }
    
    .icon-user_digital_pin_multi .path1:before {
        content: "\ead2";
        color: #000;
    }
    
    .icon-user_digital_pin_multi .path2:before {
        content: "\ead3";
        color: #fff;
        margin-left: -1em;
    }
    
    /*progress steps*/
    .steps .icon-circle-xsmall {height:13px;
    width:13px}
    .steps.complete .icon-circle-xsmall, .steps.active .icon-circle-xsmall {height:30px;
    width:30px;
    border:8px solid #fff}
    .steps.next .icon-circle-xsmall {height:30px;
    width:30px}
    .steps .icon.icon-check::before {font-size:11px}
    .icon-blue {color:#0066a4}
    .txt-blue {color:#003778}
    .bg-gray1 {background-color:#babec2 !important}
    .border-bg-gray1 {border-color:#babec2 !important}
    .align-center {text-align:center}
    .margin-2-top {margin-top:2px}
    .margin-3-top {margin-top:3px}
    .margin-10-right {margin-right: 10px;}
    .bg-transparent {background-color: transparent !important}
    .checkout .sm-section-header-bg-layer1, .sm-section-header-bg-layer2 {bottom:-80px}
    .steps-line {position:relative;
    top:23px;
    margin:0 100px;
    height:2px}
    .steps-line.one-step {margin:0 200px;}
    .steps-line.two-steps {margin:0 300px;}
    .steps-line.three-steps {margin:0 200px;}
    .steps-line.four-steps {margin:0 150px;}
    .steps-line.five-steps {margin:0 125px;}
    .steps-line.six-steps {margin:0 100px;}
    .steps-progress-1 {position:relative;
    top:21px;
    margin:0 100px;
    width:200px;
    height:4px}
    .steps-progress-2 {position:relative;
    top:21px;
    margin:0 100px;
    width:200px;
    height:4px}
    .steps-progress-3 {position:relative;
    top:21px;
    margin:0 100px;
    width:400px;
    height:4px}
    .steps-progress-4 {position:relative;
    top:21px;
    margin:0 100px;
    width:600px;
    height:4px}
    .steps-progress-5 {position:relative;
    top:21px;
    margin:0 100px;
    width:800px;
    height:4px}
    .steps-progress-6 {position:relative;
    top:21px;
    margin:0 100px;
    width:1000px;
    height:4px}
    .mobile-progressbar {display:none;
    width:100%;
    margin:0px 0 0 0}
    .mobile-bar-left {background-color:#fff;
    height:4px}
    .mobile-bar-right {background-color:#babec2;
    height:2px;
    margin-top:1px}
    .steps.left-side .icon-circle-xsmall {position:relative;
    left:-8px;
    top:-10px;
    height:15px;
    width:15px;
    overflow:hidden}
    .steps.right-side .icon-circle-xsmall {position:relative;
    right:-8px;
    top:-9px;
    height:15px;
    width:15px;
    overflow:hidden}
    .steps.active.mobile .icon-circle-xsmall {position:relative;
    top:-16px}
    
    /* modals */
    .modal-content {
     border-radius:0px;
    -webkit-box-shadow:0 0 30px rgba(0,0,0,0.3);
    -moz-box-shadow:0 0 30px rgba(0,0,0,0.3);
    box-shadow:0 0 30px rgba(0,0,0,0.3);}
    .modal-header .close {margin-top:-7px;
    margin-right:-15px}
    .modal-lg .modal-header .close {margin-top:-7px;margin-right:-17px}
    .modal-lg.bell-modal-lg .modal-header .close {padding:15px;margin:-5px -15px -15px -18px;}
    .modal .modal-md .close{padding:15px;margin:-7px -15px -15px -18px;}
    
    .modal.modal-tooltip {z-index:99999}
    .modal.modal-tooltip .modal-body {padding:0px 40px 40px 40px}
    .modal-body{padding: 30px;}
    .modalSelectNav {width:90%;
    margin:auto}
    .close {opacity:1}
    .close:hover,.close:focus {opacity:1}
    .modal-footer {text-align:left;
    border-top:none}
    .modal-footer .btn + .btn {margin-bottom:0;
    margin-left:0}
    button.close:focus {border:1px dotted}
    .modal-content{border:0}
    .modal-title.line-height-20{line-height:23px;margin-top:-3px}
    .unfocus, .unfocus:focus{border:0px;outline:0}
    .modal .modal-md .close {margin-top: -20px;}
    .modal-header-gray{
        height:74px;
        background-color:#e1e1e1
    }
    .modal-header-blue{
        height:74px;
        background-color:#00549a
    }
    .modal-dialog{
      width:710px
    }

    
    /*End Modals*/
    
.bgVirginRegistrationImg {background:#000;
    background-image:url(../../content/img/background1.jpg);
    background-position:center 0;
    background-repeat:no-repeat}
    .titleTabMob {background-image:none;
    font-size:48px;
    text-align:left;
    line-height:50px;
    padding:70px 0}
    .virginLogo {width:85px}
    
    /* helpers */
    .bgVirginGrayDark {background-color:#2D2D2D}
    .bgVirginGrayMed {background-color:#666}
    .bgVirginBlue {background-color:#00b4e1}
    .marginTop07 {margin-top:7px}
    .bgGray20 {background-color:#efefef}
    .pad-5{padding:5px}
    .pad-5-left{padding-left:5px}
    .pad-5-right{padding-right:5px}
    .pad-5-top{padding-top:5px}
    .pad-5-bottom{padding-bottom:5px}
    .pad-10{padding:10px}
    .pad-10-left{padding-left:10px}
    .pad-10-right{padding-right:10px}
    .pad-10-top{padding-top:10px}
    .pad-10-bottom{padding-bottom:10px}
    .pad-15{padding:15px}
    .pad-15-top{padding-top:15px}
    .pad-15-bottom{padding-bottom:15px}
    .pad-15-left{padding-left:15px}
    .pad-15-right{padding-right:15px}
    .pad-20{padding:20px}
    .pad-20-left{padding-left:20px}
    .pad-20-right{padding-right:20px}
    .pad-20-top{padding-top:20px}
    .pad-20-bottom{padding-bottom:20px}
    .pad-30{padding:30px}
    .pad-30-top{padding-top:30px}
    .pad-30-bottom{padding-bottom:30px}
    .pad-30-left{padding-left:30px}
    .pad-30-right{padding-right:30px}
    .pad-40{padding:40px}
    .pad-40-left{padding-left:40px}
    .pad-40-right{padding-right:40px}
    .pad-40-left{padding-left:40px}
    .pad-40-right{padding-right:40px}
    .pad-40-top{padding-top:40px}
    .pad-40-bottom{padding-bottom:40px}
    .pad-60{padding:60px}
    .pad-60-top{padding-top:60px}
    .pad-60-bottom{padding-bottom:60px}
    .pad-60-left{padding-left:60px}
    .pad-60-right{padding-right:60px}
    
    .vPaddingL06{padding-left:6px}
    .vPaddingL10 {padding-left:10px}
    .vPaddingL30 {padding-left:30px}
    .vPaddingR85 {padding-right:85px}
    .paddingR20 {padding-right:20px}
    .paddingR30 {padding-right:30px}
    .paddingL30 { padding-left:30px}
    .spacer35 {height:35px}
    .spacer45 {height:45px}
    .spacer60 {height:60px}
    .spacer90 {height:90px}
    .spacer170 {height:170px}
    .spacer250 {height:250px}
    .vSpacer150 {width:150px}
    .vSpacer160 {width:160px}
    .wordBreak {word-break:break-all}
    .flipFloatLR {float:left}
    .flipTxtAlignRL {text-align:left}
    .inlineBlock {display:inline-block}
    .noMarginBottom{margin-bottom:0}
    .marginR-40{margin-right:-40px}
    .marginCompensator{margin-left:-21px}
    .paddingL26-md-lg{padding-left:26px}
    .txtUnderline{text-decoration:underline}
    .txtGrayLight2{color:#999}
    .txtRedV, .txtRedV a, a.txtRedV :active,  a.txtRedV:visited,  a.txtRedV:hover {color:#FF0000}
    .pad-20lg-30xs{padding:30px}
    .border-container{border:1px solid #dcdcdc}

    .margin-15{margin: 15px;}
    
    /* footer */
    .footer {margin-top:-20px}
    ul.footerList {padding-left:0}
    ul.footerList li {display:inline-block;
    padding-right:25px}
    ul.footerList li a {text-decoration:none;
    color:#999}
    ul.footerList li a:hover {color:#cbcbcb}
    a {color:#2390b8;
    text-decoration:none}
    .txtRed, .txtRed a, .txtRed a:active, .txtRed a:visited, .txtRed a:hover {color:#bf1a18}
    a:hover,a:focus {color:#2390b8;
    text-decoration:underline}
    .txtNoUnderline, a:hover.txtNoUnderline, a.txtNoUnderline:hover,.txtHoverUnderline, .txtHoverUnderline a, .txtHoverUnderline a:active, .txtHoverUnderline a:visited, .txtHoverUnderline a:link{text-decoration:none}
    a:hover.txtRed,a:focus.txtRed {color:#bf1a18;
    text-decoration:underline}
    /* END footer */
    
    /* panels */
    .panel {border-radius:0;
    -webkit-box-shadow:0 0 0 rgba(0,0,0,.05);
    box-shadow:0 0 0 rgba(0,0,0,.05)}
    .panel-default {border-color:#eaeaea}
    .panel-body-no-paddingLR-md-lg{padding:20px 0}
    .panel-default.bgRadioPanel {background:#fff;
    color:#333;
    cursor:pointer}
    .panel-default.bgRadioPanel.active {background:#333;
    color:#fff}
    
    /* modals */
    .modal-header {border-top-left-radius:6px;
    border-top-right-radius:6px}
    .modal-header .close {margin-top:-26px;
    margin-right:-20px}
    .modal-lg .modal-header .close {margin-top:-26px;
    margin-right:-37px}
    
    .modal-lg .modal-header .close.x-inner,.modal-header .close.x-inner {
        margin-top:4px;
        margin-right:0px
    }
    
   
    .modal.modal-tooltip {z-index:99999}
    .modal.modal-tooltip .modal-body {padding:0px 30px 25px 30px}
    .modalSelectNav {width:90%;
    margin:auto}
    .close {opacity:1}
    .close:hover,.close:focus {opacity:1}
    .modal-footer {text-align:left;     
    border-top:none}
    .modal-footer .btn + .btn {margin-bottom:0;
    margin-left:0}
    button.close:focus {border:1px solid}
    
    /* tooltip */
    .tooltip-inner {max-width:250px;
    padding:20px 40px;
    color:#555;
    text-align:left;
    background-color:#FFF;
    border-radius:0;
    -webkit-box-shadow:0px 0px 5px 5px rgba(0,0,0,0.2);
    -moz-box-shadow:0px 0px 5px 5px rgba(0,0,0,0.2);
    box-shadow:0px 0px 5px 5px rgba(0,0,0,0.2)}
    .tooltip{min-width:250px}
    .tooltip.top .tooltip-arrow {border-top-color:#FFF}
    .tooltip.top-left .tooltip-arrow {border-top-color:#FFF}
    .tooltip.top-right .tooltip-arrow {border-top-color:#FFF}
    .tooltip.right .tooltip-arrow {border-right-color:#FFF}
    .tooltip.left .tooltip-arrow {border-left-color:#FFF}
    .tooltip.bottom .tooltip-arrow {border-bottom-color:#FFF}
    .tooltip.bottom-left .tooltip-arrow {border-bottom-color:#FFF}
    .tooltip.bottom-right .tooltip-arrow {border-bottom-color:#FFF}
    .tooltip.in {filter:alpha(opacity=100);
    opacity:1}
    
    .col-lg-1, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-md-1, .col-md-10, .col-md-11, .col-md-12, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-xs-1, .col-xs-10, .col-xs-11, .col-xs-12, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9 {padding-right:0;
    padding-left:0}
    ol, ul {padding-left:17px}
    /* form controls */
    .form-control {border-radius:0;
    padding:18px 12px;
    color:#333}
    .has-error .form-control:focus {border-color:#fe0000}
    .has-error .form-control {border-color:#fe0000}
    label {font-weight:normal}
    .radio {margin-bottom:15px}
    .has-error .checkbox, .has-error .checkbox-inline, .has-error .control-label, .has-error .help-block, .has-error .radio, .has-error .radio-inline, .has-error.checkbox label, .has-error.checkbox-inline label, .has-error.radio label, .has-error.radio-inline label {color:#fe0000}
    .help-block {color:#333}
    .variableWidthInput {width:100%}
    .variableWidthInput2 {width:88%}
    .form-control:focus {
      border-color: #34a8d6;
      box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset, 0 0 8px rgba(52, 168, 214, 0.6);
    }
    
    /* Buttons */
    .btn {border-radius:3px;
    font-size:16px;
    font-weight:bold;
    padding:12px 18px}
    .btn-primary {color:#fff;
    background-color:#cc0000;
    border:2px solid #cc0000}
    .btn-primary:focus,.btn-primary.focus {color:#fff;
    background-color:#eb0000;
    border-color:#eb0000}
    .btn-primary:hover {color:#fff;
    background-color:#eb0000;
    border-color:#eb0000}
    .btn-primary:active,.btn-primary.active,.open > .dropdown-toggle.btn-primary {color:#fff;
    background-color:#eb0000;
    border-color:#eb0000}
    .btn-primary:active:hover,.btn-primary.active:hover,.open > .dropdown-toggle.btn-primary:hover,.btn-primary:active:focus,.btn-primary.active:focus,.open > .dropdown-toggle.btn-primary:focus,.btn-primary:active.focus,.btn-primary.active.focus,.open > .dropdown-toggle.btn-primary.focus {color:#fff;
    background-color:#eb0000;
    border-color:#eb0000}
    .btn-primary:active,.btn-primary.active,.open > .dropdown-toggle.btn-primary {background-image:none}
    .btn-primary.disabled:hover,.btn-primary[disabled]:hover,fieldset[disabled] .btn-primary:hover,.btn-primary.disabled:focus,.btn-primary[disabled]:focus,fieldset[disabled] .btn-primary:focus,.btn-primary.disabled.focus,.btn-primary[disabled].focus,fieldset[disabled] .btn-primary.focus {background-color:#eb0000;
    border-color:#eb0000}
    .btn.btn-primary.disabled,.btn.btn-default.disabled{background-color:transparent;
    border-color:#9c9c9c;
    color:#9c9c9c}
    .btn-primary .badge {color:#337ab7;
    background-color:#fff}
    .btn-default {color:#000;
    background:none;
    border-color:#000;
    border:2px solid #000}
    .btn-default:focus,.btn-default.focus {color:#333;
    background-color:#eeeeee;
    border-color:#eeeeee}
    .btn-default:hover {color:#000;
    background-color:#999;
    border-color:#999}
    .btn-default:active,.btn-default.active,.open > .dropdown-toggle.btn-default {color:#333;
    background-color:#eeeeee;
    border-color:#eeeeee}
    .btn-default:active:hover,.btn-default.active:hover,.open > .dropdown-toggle.btn-default:hover,.btn-default:active:focus,.btn-default.active:focus,.open > .dropdown-toggle.btn-default:focus,.btn-default:active.focus,.btn-default.active.focus,.open > .dropdown-toggle.btn-default.focus {color:#333;
    background-color:#d4d4d4;
    border-color:#8c8c8c}
    .btn-default:active,.btn-default.active,.open > .dropdown-toggle.btn-default {background-image:none}
    .btn-default.disabled:hover,.btn-default[disabled]:hover,fieldset[disabled] .btn-default:hover,.btn-default.disabled:focus,.btn-default[disabled]:focus,fieldset[disabled] .btn-default:focus,.btn-default.disabled.focus,.btn-default[disabled].focus,fieldset[disabled] .btn-default.focus {background-color:#fff;
    border-color:#ccc}
    .btn-default .badge {color:#fff;
    background-color:#333}
    .btn.btn-topNav {font-size:14px}
    .btn-topNav:hover, .btn-topNav:focus {color:#34A8D6}
    .btn-topNav {padding:0 20px;
    border-right:1px solid #414141}
    .topNav > .btn-topNav:first-child {border-left:1px solid #414141}
    .btn.btn-modalSubNav {border-radius:6px;
    font-size:13px;
    padding:1px 15px;
    font-weight:bold;
    color:#2390B8;
    background-color:#e2e2e2}
    .btn.btn-modalSubNav:hover, .btn.btn-modalSubNav.active, .btn.btn-modalSubNav:focus {color:#FFF;
    background-color:#727272;
    -webkit-box-shadow:inset 0px 2px 13px 0px rgba(0,0,0,0.75);
    -moz-box-shadow:inset 0px 2px 13px 0px rgba(0,0,0,0.75);
    box-shadow:inset 0px 2px 13px 0px rgba(0,0,0,0.75);
    outline:none}
    .variableWidthButton {float:left;
    width:auto}
    .btn-transparent, .btn-transparent:active, .btn-transparent:visited, .btn-transparent:focus {
        display: inline-block;
        padding: 12px 17px;
        border-radius: 3px;
        border: 2px solid #fff;
        color: #fff;
        font-weight: bold;
        font-size: 16px;
        line-height: 1em;
        cursor: pointer;
        background: transparent;
        text-decoration: none;
    }
    .btn-transparent:hover {
        color: #000;
        background-color: #eee;
        border-color: #eee;
        text-decoration: none;
    }
    .container {padding-right:0;
    padding-left:0}
    
    .vm-container.container{padding:0 20px;}
    
    /* show/hide password button */
    .maskUnMaskPwsBtn {right:12px;
    top:7px;
    border:medium none;
    background:#bbbec3;
    width:auto;
    height:25px;
    color:#000;
    border-radius:5px;
    font-size:11px}
    
    /*Virgin header and footer Styles*/
    
   
    .footerVM {bottom:0;
    left:0}
    .footerVM a:hover {color:#cbcbcb}
    .vm-copyRightLine {text-align:right}
    
    /*modal window vertical cetnering*/
    .modal {text-align:center;
    padding:0 10px}
    .modal:before {content:'';
    display:inline-block;
    height:100%;
    vertical-align:middle;
    margin-right:-4px}
    .modal-dialog {display:inline-block;
    text-align:left;
    vertical-align:middle}
    .modal-title{font-family:FuturaLT-CondensedLight,"Trebuchet MS",Arial,Helvetica,sans-serif;
      font-size: 30px; line-height: 28px; 
    }
    .modal-title .close-icon{  -ms-transform: rotate(45deg); /* IE 9 */
      transform: rotate(45deg);}
    /*modal window vertical centering*/
    .vm-panel-body {padding:20px 30px;
    content:" ";
    display:table;
    width:100%}
    .vm-loader {width:100%;
    height:100%;
    background-image:url(../../content/img/vmSpinner.gif);
    background-position:center center;
    background-repeat:no-repeat}
    select{padding:9px;
    border:1px solid #ccc}
    
    /*fix for firefox input elements which show no text at all using native bootstrap.*/
    .firefoxFix {height:38px; 
    padding:6px 12px}
    
    .hidden-tooltip-target-xs{display:inline-block}
    .hidden-tooltip-target-lg{display:none}
    .modal.modal-tooltip .modal-header{border-bottom:0px}
    
    /*hamburger icon */
    .navbar, .navbar-inverse {border-radius:0;
    border:none;
    margin-bottom:0;
    width:80%}
    .navbar-nav{float:right}
    .nav li {display:inline}
    .navbar-header{height:60px}
    .navbar-inverse{background-color:#2D2D2D!important;
    float:right}
    .navbar-inverse .navbar-nav_bvr > li > a{color:#fff}
    .navbar-inverse .navbar-nav_bvr > li > a:hover {color:#2390b8}
    .bvr_spacer60_xs35{height:60px}
    
    @media screen and (max-width:1239px){
    .modal.modal-tooltip {position:fixed;
    width:100%;
    top:50%;
    left:50%;
    -webkit-transform:translate(-50%, -50%);
    transform:translate(-50%, -50%)}
    .hidden-tooltip-target-xs {display:none}
    .hidden-tooltip-target-lg {display:inline-block}
    }
    
    /*New svg loader using an svg spinner: January 18 2016*/
    .loading-indicator-circle {
        display: inline-block;
        width: 37px;
        height: 37px;
        margin-right:10px;
        vertical-align: middle;
        -webkit-animation:spin 1.1s linear infinite;
        -moz-animation:spin 1.1s linear infinite;
        animation:spin 1.1s linear infinite;
    }
    @-moz-keyframes spin { 100% { -moz-transform: rotate(360deg); } }
    @-webkit-keyframes spin { 100% { -webkit-transform: rotate(360deg); } }
    @keyframes spin { 100% { -webkit-transform: rotate(360deg); transform:rotate(360deg); } }
    
    .loader-fixed {
        width:350px;
        left: 50%;
        margin-left: -175px;
        position: fixed;
        top: 45%;
        padding:20px;
        z-index: 99999;
        -webkit-box-shadow:0 0 40px rgba(0,0,0,0.4);
        -moz-box-shadow:0 0 40px rgba(0,0,0,0.4);
        box-shadow:0 0 40px rgba(0,0,0,0.4);
    }
    /*Solid icons with content dark background*/
    .icon.icon-bgWhite:before {
        left:-1px;
        position: relative;
        top: -0.2em;
    }
    .icon.icon-bgWhite span {
        display:inline-block;
        float:left;
        width:0.8em;
        height:0.8em;
        background-color:#fff;
        border-radius:50%;
        margin-right:-1em;
        margin-top:0.2em
    }
    /*End*/
    .noRadius {border-radius:0}
    .noBorder{border:0}
    .bgGrayVirginModalHeader{background-color:#dbdbdb}
    
    
    /*Tabs*/
    ul.tabs {
        display: table;
        table-layout: fixed;
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
    }
    ul.tabs li.active_tabs {
        background-color: #333;
        z-index: 2;
    }
    ul.tabs li.active_tabs label {
        position: relative;
        top: -5px;
    }
    ul.tabs li.active_tabs .active-tab-top {
        background-color: #333;
        display: block;
        height: 10px;
        left: 0px;
        opacity: 1;
        position: absolute;
        top: -10px;
        width: 100%;
        z-index: -1;
    }
    ul.tabs li.active_tabs::before {
        content: "";
        height: 100%;
        opacity: 1;
        position: absolute;
        right: -10px;
        top: 0;
        width: 10px;
        content: "";
        position: absolute;
        top: 0px;
    }
    .active_tabs::after {
        content: "";
        position: absolute;
        bottom: -15px;
        border-width: 15px 15px 0;
        border-style: solid;
        border-color: #333 transparent;
        display: block;
        width: 0;
        left: 0;
        right: 0;
        margin-left: auto;
        margin-right: auto;
    }
    ul.tabs li {
        border-right: 2px solid #fff;
        text-align: center;
    }
    ul.tabs li.last-active-tab {
        border-right: 0px solid transparent;
    }
    ul.tabs li{
        cursor: pointer;
        padding: 15px 20px;
        background-color: #666;
        color: #fff;
        font-size: 16px;
        display: table-cell;
        vertical-align: middle;
        float: none;
        position: relative;
    }
    
    ul.tabs li> i {
        font-size: 55px;
        vertical-align: middle;
        margin: 0 auto;
        display: table;
    }
    .tabH {
        display: inline-block;
    }
    ul.tabs li .tabH a:hover, ul.tabs li .tabH a:focus{
        color:#fff;
        text-decoration:none
    }
    .tab-content {
        display: none;
    }
    .tab-content.first {
        display: block;
    }
    #secondaryNav #tabs li a:focus{color:#fff;background-color:#42caf1}
    #secondaryNav #tabs li.active a:focus{color:#000;background-color:#efefef}
    
    /*Custom hamburger select form mobile devices*/
    select.custom-selection {
        -webkit-appearance:none;
        -moz-appearance:none;
        cursor:pointer;
        color:#fff;
        background-color:#333;
        padding:15px;
        padding-top: 10px;
        padding-bottom: 10px;
        height:50px;
        font-size:15px;
    }
    option.tab_selection{
        background-color:#333;
        color:#fff;
        font-size:14px
    }
    
    div.selection-box {position:relative;}
    div.selection-box:after {
        font-family: "bell-icon";
        content:'\e618';
        font-size:21px;
        background-color:#333;
        color:#fff;
        right:0px; top:1px;
        padding:10px 18px;
        height:48px;
        position:absolute;
        pointer-events:none;
    }
    div.selection-box:before {
        content:'';
        position:absolute;
        pointer-events:none;
        display:block;
    }
    div.selection-box.search-arrow-down:after {
        content: "\e618";
    }
    /*END custom hamburger select form mobile devices END*/
    
    .txtTab{font-size:26px;text-transform:uppercase}
    .whiteBox{ height: 155px;}
    .height388{height: 388px;}
    
    /*Skip to main content for accessibility*/
    header .skip-to-main-link {
        display: inline-block;
        padding: 9px 12px;
        position: absolute;
        top: -50px;
        left: 50%;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%);
        text-decoration: underline;
        border-bottom-right-radius: 5px;
        border-bottom-left-radius: 5px;
        transition: top .3s ease-out;
        z-index: 3000;
        font-size: 13px;
        background-color: #efefef;
        color:#2390b8
    }
    header .skip-to-main-link:focus {
        top: 0;
    }
    footer .skip-to-main-link{
        display: inline-block;
        padding: 9px 12px;
        position: absolute;
        left: -300px;
        text-decoration: underline;
        border-bottom-right-radius: 5px;
        transition: left .3s ease-out;
        background-color: #efefef;
        z-index: 3000;
        font-size: 13px;
        color:#2390b8
    }
    footer .skip-to-main-link:hover{
        color:#2390b8
    }
    footer .skip-to-main-link:focus {
        left: 0;
    }
    
    
    /* media queries keep at the bottom*/
    @media screen and (max-width:991px) {
    .txtTab{font-size:16px;font-family:Helvetica, Arial, sans-serif;text-transform:none}
    .pad-20lg-30xs{padding:20px}
    .vm-container.container{padding:0}
    .bgVirginRegistrationImg {background:#FFF;
    background-image:none}
    ul.footerList li {display:block}
    .vm-copyRightLine {text-align:left}
    .titleTabMob {background:#000;
    background-image:url(../../content/img/backgroundTabMob.jpg);
    background-position-x:right;
    background-position-Y:-75px;
    background-repeat:no-repeat;
    font-size:38px;
    text-align:left;
    padding:35px 0}
    .panel-body, .panel-body-no-paddingLR-md-lg {padding:20px 20px}
    .vm-panel-body {padding:20px 0px}
    .variableWidthButton {float:left;
    width:100%}
    .modal:before, .modal-dialog {vertical-align:top}
    .modal {padding-top:65px}
    
    .modal.modal-vm:before, .modal-dialog{vertical-align:middle}
    .modal.modal-vm {padding-top:0px}
    
    .marginCompensator{margin-left:0}
    .paddingL26-md-lg{padding-left:0}
    .modal {padding-top:65px}
    .Changeorder-xs {-webkit-transform:rotate(180deg);
    -moz-transform:rotate(180deg);
    -ms-transform:rotate(180deg);
    -o-transform:rotate(180deg);
    transform:rotate(180deg);
    margin-left:0!important}
    .Changeorder-xs > [class*="col-"] {-webkit-transform:rotate(-180deg);
    -moz-transform:rotate(-180deg);
    -ms-transform:rotate(-180deg);
    -o-transform:rotate(-180deg);
    transform:rotate(-180deg)}
    .rotateText{-webkit-transform:rotate(-180deg); 
    -moz-transform:rotate(-180deg); 
    -ms-transform:rotate(-180deg); 
    -o-transform:rotate(-180deg); 
    transform:rotate(-180deg);
    width:100%;
    float:left;
    vertical-align:top}
    }
    @media screen and (max-width:767px) {
        .headingTxt{ text-align: left;font-size: 35px;
            float: left; text-align: left; padding: 15px;}
      .whiteBox{ height: 100%;}
      .height388{height:100%;}
      .height167 {height:167px };
    .flipFloatLR {float:right}
    .modal:before, .modal-dialog {vertical-align:top}
    .modal {padding-top:65px}
    .panel-body-no-paddingLR-md-lg {padding:20px 30px}
    /*hamburger icon*/
    .navbar, .navbar-inverse {position:absolute;
    right:0;
    width:auto}
    .nav>li>a{padding:10px 7px}
    .bvr_spacer60_xs35{height:35px}
    .btn-topNav{border-right:none}
    .modal.modal-vm:before, .modal-dialog{vertical-align:top;}
    .modal.modal-vm {padding:0px;}
    .modal.modal-vm .modal-dialog{margin:0;width:100%;height:auto;position:relative;bottom: 0;background-color:#fff}
    .modal.modal-vm .modal-dialog .modal-body{overflow-y: visible;}
    .modal.modal-vm .modal-dialog .modal-content{
    border:none;-webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;}
    }
    @media screen and (max-width:520px) {
    .txtTab{font-size:14px;font-family:Helvetica, Arial, sans-serif;text-transform:none}
    .footerVM .container, .titleTabMob {padding-left:10px}
    .virginLogo {margin-left:10px}
    .topNav {padding-right:10px}
    .flipFloatLR {float:right}
    .flipFloatRL {float:left}
    .flipTxtAlignRL {text-align:left}
    .panel-body {padding:20px 20px}
    .vm-panel-body {padding:20px}
    .panel-body-no-paddingLR-md-lg {padding:20px 30px}
    .titleTabMob {text-align:center;
    font-size:26px;
    padding:35px 0;
    line-height:30px;
    background-size:519px 235px;
    background-position-y:-15px;
    background-repeat:no-repeat}
    .variableWidthButton {float:none;
    width:100%}
    .modal {padding-top:65px}
    .modal.modal-tooltip {position:fixed;
    width:100%;
    top:50%;
    left:50%;
    -webkit-transform:translate(-50%, -50%);
    transform:translate(-50%, -50%)}
    .hidden-tooltip-target-xs{display:none}
    .hidden-tooltip-target-lg{display:inline-block}
    }
    @media screen and (max-width:443px) {
    .col-xxs-10 {width:87%;
    float:left}
    }
    @media (min-width:768px) {
    .variableWidthInput {width:160px}
    .variableWidthInput2 {width:160px}
    .flipFloatLR {float:left}
    .flipFloatRL {float:left}
    .flipTxtAlignRL {text-align:right}
    .panel-body {padding:20px 30px}
    .height210{height: 220px;}
    .headingTxt{ text-align: center; font-size: 1.75em;
        margin: .67em 0;
    }}
    }
    @media (min-width:992px) {
    .variableWidthInput {width:160px}
    .variableWidthInput2 {width:160px}
    .flipFloatLR {float:left}
    .flipFloatRL {float:right}
    .flipTxtAlignRL {text-align:right}
    .modal-dialog.modal-lg {width:900px}
    .height210{height: 210px;}
    }
    @media (min-width:1200px) {
    .variableWidthInput {width:160px}
    .variableWidthInput2 {width:160px}
    .flipFloatLR {float:left}
    .flipTxtAlignRL {text-align:right}
    }
    
    /*to overide out-of-the-box bootstrap*/
    .container {padding-right:0;padding-left:0}
    @media (min-width:520px) {
    .container {width:100%;height: 100%;position: relative;}
    }
    @media (min-width:768px) {
    .container {width:100%;height: 100%;position: relative;}
    }
    @media (min-width:992px) {
    .container {width:100%;height: 100%;position: relative;}
    }
    @media (min-width:1200px) {
    .container {width:100%;height: 100%;position: relative;}
    }
    
    