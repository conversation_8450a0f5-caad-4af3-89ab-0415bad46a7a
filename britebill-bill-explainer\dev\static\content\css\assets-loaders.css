.loader-fixed {
    width: 320px;
    left: 0;
    margin-left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    position: fixed;
    top: 45%;
    padding: 20px;
    z-index: 99999;
    -webkit-box-shadow: 0 0 40px rgba(0,0,0,0.4);
    -moz-box-shadow: 0 0 40px rgba(0,0,0,0.4);
    box-shadow: 0 0 40px rgba(0,0,0,0.4);
}

.loading-indicator-circle {
    display: inline-block;
    width: 37px;
    height: 37px;
    margin-right: 10px;
    vertical-align: middle;
    -webkit-animation: spin 1.1s linear infinite;
    -moz-animation: spin 1.1s linear infinite;
    animation: spin 1.1s linear infinite;
}

.loader-fixed.small-loader {
    text-align: center;
    width: auto;
    margin-left: -50px;
    max-width: 120px;
    padding: 20px 10px;
    box-shadow: none;
}

.top-35-percent {
    top: 35%;
}

@-moz-keyframes spin {
    100% {
        -moz-transform: rotate(360deg);
    }
}

@-webkit-keyframes spin {
    100% {
        -webkit-transform: rotate(360deg);
    }
}

@keyframes spin {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}