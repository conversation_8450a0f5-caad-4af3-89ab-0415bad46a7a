//Add event handler for when page/section loaders are shown
$('body').on('keydown keyup keypress click', fnClickAndKeyNavBlocker);
$('body').on('keydown keyup keypress click', '*', fnClickAndKeyNavBlocker);
function fnClickAndKeyNavBlocker(e) {
    // block click and key events while masked
    if ('click' === e.type || 9 === e.keyCode || 13 === e.keyCode || 32 === e.keyCode) {
        if ($('body').hasClass('masked')) {
            e.stopImmediatePropagation();
            e.preventDefault();
            return;
        }
    }
}

// show page loader with mask
$("#show-brf-loader-mask").click(function () {
    showLoaderWithMask('#brf-page-loader-mask');
});

// hide page loader mask
// hideLoaderWithMask('#brf-page-loader-mask')


function showLoaderWithMask(loaderSelector) {
    $('body').addClass('masked');
    $('<div class="loaderOverlayBackground transparent"></div>').appendTo('body').fadeIn();
    $(loaderSelector).show();
}

function hideLoaderWithMask(loaderSelector) {
    $('body').removeClass('masked');
    $('.loaderOverlayBackground').remove();
    $(loaderSelector).hide();
}


$(document).ready(function () {
    // this intialization adds the tooltip to the body and should be used for static-text tooltips that doesn't need to receive focus (read through aria-describedby)
    $('.tooltip-static').tooltip();

    // START focusable tooltips for screen reader compatibility
    $('.tooltip-static').on('shown.bs.tooltip', function () {
        $(this).find('.tooltip').attr('tabindex', 0);
    });
    // END focusable tooltips for screen reader compatibility

});