/*
Rules:
1. Please keep the fonts grouped togehter at the top of the file.
2. Please keep the media queries grouped at the bottom of the file.

BasisGrotesquePro fonts*/
/*Regular*/
@font-face {
  font-family: "BasisGrotesquePro";
  src: url("../fonts/BasisGrotesquePro-Regular.eot");
  src: url("../fonts/BasisGrotesquePro-Regular.woff") format("woff"),
    url("../fonts/BasisGrotesquePro-Regular.woff2") format("woff2"),
    url("../fonts/BasisGrotesquePro-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
/*Medium*/
@font-face {
  font-family: "BasisGrotesquePro-med";
  src: url("../fonts/BasisGrotesquePro-Medium.eot");
  src: url("../fonts/BasisGrotesquePro-Medium.woff") format("woff"),
    url("../fonts/BasisGrotesquePro-Medium.woff2") format("woff2"),
    url("../fonts/BasisGrotesquePro-Medium.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
/*Bold*/
@font-face {
  font-family: "BasisGrotesquePro-bold";
  src: url("../fonts/BasisGrotesquePro-Bold.eot");
  src: url("../fonts/BasisGrotesquePro-Bold.woff") format("woff"),
    url("../fonts/BasisGrotesquePro-Bold.woff2") format("woff2"),
    url("../fonts/BasisGrotesquePro-Bold.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
/*Black*/
@font-face {
  font-family: "BasisGrotesquePro-Black";
  src: url("../fonts/BasisGrotesquePro-Black.eot");
  src: url("../fonts/BasisGrotesquePro-Black.woff") format("woff"),
    url("../fonts/BasisGrotesquePro-Black.woff2") format("woff2"),
    url("../fonts/BasisGrotesquePro-Black.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
/*Light*/
@font-face {
  font-family: "BasisGrotesquePro-Light";
  src: url("../fonts/BasisGrotesquePro-Light.eot");
  src: url("../fonts/BasisGrotesquePro-Light.woff2") format("woff"),
    url("../fonts/BasisGrotesquePro-Light.woff2") format("woff2"),
    url("../fonts/BasisGrotesquePro-Light.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
.nwtel_reg {
  font-family: "BasisGrotesquePro", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.nwtel_med {
  font-family: "BasisGrotesquePro-med", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.nwtel_bold {
  font-family: "BasisGrotesquePro-bold", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.nwtel_black {
  font-family: "BasisGrotesquePro-Black", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.nwtel_light {
  font-family: "BasisGrotesquePro-Light", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.default-font,
.sans-serif {
  font-family: "Helvetica", Arial, sans-serif;
  font-weight: normal;
}

/* icons */
[class^="icon-"], [class*=" icon-"],
[class^="icon2-"], [class*=" icon2-"],
.icon2[class^="icon-"], .icon2[class*=" icon-"]  {
    /* use !important to prevent issues with browser extensions that change fonts */
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: relative;
    top: -2px;
}

/* Body */
body {
  background-color: #f0f4f5;
  font-size: 14px;
  color: #333;
  font-weight: normal;
  letter-spacing: 0px;
}
/* Container */
.container {
  padding-right: 11px;
  padding-left: 11px;
}
.container.liquid-container {
  width: 100%;
}
.container-flex-box {
  display: flex;
  display: -webkit-flex;
  flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
}
/* row */
.row {
  margin-left: -11px;
  margin-right: -11px;
}

/* Headers */
h1, .h1 {
  font-size: 32px;
  line-height: 36px;
}
h2, .h2 {
  font-size: 24px;
  line-height: 28px;
}
h3, .h3 {
  font-size: 18px;
  line-height: 22px;
}
h4, .h4 {
  font-size: 16px;
  line-height: 20px;
}
h5, .h5 {
  font-size: 14px;
  line-height: 18px;
}
p {
  font-size: 14px;
  line-height: 18px;
}

.h1, .h2, .h3, .h4, .h5,
h1, h2, h3, h4, h5 {
  margin-top: 0;
}

/*Default text formatting*/
.default-text-format h1 {
  font-size: 32px;
  line-height: 36px;
  font-family: "BasisGrotesquePro-med", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333333;
}
.default-text-format h1 b {
  font-family: "BasisGrotesquePro-bold", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: normal;
}
.default-text-format h2 {
  font-size: 24px;
  line-height: 28px;
  font-family: "BasisGrotesquePro-med", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333333;
}
.default-text-format h2 b {
  font-family: "BasisGrotesquePro-bold", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: normal;
}
.default-text-format h3 {
  font-size: 18px;
  line-height: 22px;
  font-family: "BasisGrotesquePro-med", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333333;
}
.default-text-format h2:not(:first-child) {
  margin-top: 30px;
}
.default-text-format h3 b {
  font-family: "BasisGrotesquePro-bold", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: normal;
}
.default-text-format h4 {
  font-size: 16px;
  line-height: 20px;
  font-family: "BasisGrotesquePro-reg", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333;
}
.default-text-format p {
  font-size: 14px;
  line-height: 18px;
}

.default-text-format main ol, .default-text-format main ul,
main .default-text-format ol, main .default-text-format ul {
  padding-left: 30px;
}
.default-text-format main a, main .default-text-format a {
  text-decoration: underline;
}
.default-text-format main a:hover, main .default-text-format a:hover {
  text-decoration: none;
}
/*Default text formatting end*/

/*text Colours*/
.txtFunGreen { color: #008542; }
.txtMossGreen { color: #b7ddbd; }
.txtSeaGreen { color: #378e42; }
.txtSeaGreen2 { color: #339043; }
.txtRed { color: #bd2025; }
.txtMineShaft { color: #333333; }
.txtAlto { color: #d4d4d4; }
.txtTintSubtleGrey { color: #f0f4f5; }

/*Background colours*/
.bg-transparent { background-color: transparent; }
.bgFunGreen { background-color: #008542; }
.bgMossGreen { background-color: #b7ddbd; }
.bgSeaGreen { background-color: #378e42; }
.bgSeaGreen2 { background-color: #339043; }
.bgMineShaft { background-color: #333333; }
.bgAlto { background-color: #d4d4d4; }
.bgTintSubtleGrey { background-color: #f0f4f5; }
.bgGreen {background-color: #00aa55}
.bgYellow {background-color: #ffd668}
.bgRed { background-color: #d32020 }
.bgRed2 {background-color: #bd2025;}

/* Columns */
.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12 {
  padding: 0px;
}

/* table */
.table {
  display: table;
  height: 100%;
}
.table-cell {
  display: table-cell;
}
.valign-middle {
  vertical-align: middle;
}
.valign-top {
  vertical-align: top !important;
}
.table-cell {
  display: table-cell !important;
  float: none !important;
}

/* Helpers */
.inlineFlex, .inline-flex {
  display: inline-flex;
}

.txtNoUnderline {
  text-decoration: none !important;
}

.align-center {
  text-align: center;
}

.no-break-white-space {
  white-space: nowrap;
}

/* line heights */
.line-height-12 {
  line-height: 12px;
}
.line-height-14 {
  line-height: 14px;
}
.line-height-18 {
  line-height: 18px;
}
.line-height-20 {
  line-height: 20px;
}
.line-height-22 {
  line-height: 22px;
}
.line-height-24 {
  line-height: 24px;
}
.line-height-28 {
  line-height: 28px;
}

/* anchor */
a,
a:focus,
a:hover {
  color: #00865d;
  text-decoration: none;
}
a.bread-crumb:focus,
a.bread-crumb:hover,
a.txtUnderlineHover:hover {
  text-decoration: underline;
}

/*New svg loader using an svg spinner*/
.loader-desc {
  width: 210px;
}
.loading-indicator-circle {
  display: inline-block;
  width: 37px;
  height: 37px;
  margin-right: 10px;
  vertical-align: middle;
  -webkit-animation: spin 1.1s linear infinite;
  -moz-animation: spin 1.1s linear infinite;
  animation: spin 1.1s linear infinite;
}
@-moz-keyframes spin {
  100% {
    -moz-transform: rotate(360deg);
  }
}
@-webkit-keyframes spin {
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.loader-fixed {
  width: 300px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  z-index: 99999;
  -webkit-box-shadow: 0 0 40px rgba(0, 0, 0, 0.4);
  -moz-box-shadow: 0 0 40px rgba(0, 0, 0, 0.4);
  box-shadow: 0 0 40px rgba(0, 0, 0, 0.4);
}

/* Container */
.nwtel-logo-container {
  padding-bottom: 20px;
  padding-top: 20px;
}

/*Forms*/
.nwtel-form-control {
  background-color: #ffffff;
  border: 1px solid #949596;
  border-radius: 0px;
  color: #333333;
  font-family: "BasisGrotesquePro";
  font-size: 16px;
  height: 50px;
  line-height: 22px;
  padding: 0 14px;
  min-width: 200px;
}
.nwtel-form-control::placeholder {
  font-size: 14px;
  line-height: 22px;
  color: #808080;
}
.nwtel-form-control:focus{
  border-color: #000000;
}

.nwtel-input-placeholder:focus::-webkit-input-placeholder {
  color: transparent;
  transition-timing-function: ease-in;
  transition: 0.4s;
}

.nwtel-form-control:disabled {
  border-color: #eeeeee;
  outline: none;
  pointer-events: none;
  background-color: white;
}
.nwtel-form-control:disabled::-webkit-input-placeholder {
  color: #cccccc;
}

.nwtel-form-control-select-box:after {
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  position: absolute;
  right: 15px;
  top: -2px;
  bottom: 0;
  height: 14px;
  margin: 16px auto;
  font-size: 10px;
  padding: 0;
  color: #000;
  pointer-events: none;
}

/* buttons */
.btn-nwtel-theme,
.btn-nwtel-theme:not(:disabled):not(.disabled):active,
.btn-nwtel-theme:not(:disabled):not(.disabled):focus{
  padding: 14.5px 25px;
  border: none;
  border-radius: 0;
  box-shadow: none;
  font-family: "BasisGrotesquePro-med", Helvetica, Arial, sans-serif;
  font-size: 18px;
  font-weight: 500;
  line-height: 21px;
  color: #000000;
  background-color: #B7DDBD;
}
.btn-nwtel-theme:not(:disabled):not(.disabled):hover{
  background: #00865d;
  color: #fff;
}
.btn-nwtel-theme.disabled,
.btn-nwtel-theme:disabled,
.btn-nwtel-theme.disabled:active,
.btn-nwtel-theme:disabled:active,
.btn-nwtel-theme.disabled:focus,
.btn-nwtel-theme:disabled:focus{
  color: #88a3a6;
  background-color: #f0f4f5;
  cursor: default;
  opacity: 1;
}

/* button select */
.nwtel-form-control.btn-select {
  min-width: 200px;
  width:100%;
  overflow:hidden;
  white-space:nowrap;
  text-overflow: ellipsis;
  display: flex;
  justify-content: space-between;
  color: #000;
}
.nwtel-form-control.btn-select .btn-select-value {
    align-self: center;
    overflow:hidden;
    white-space:nowrap;
    text-overflow: ellipsis;
}
.nwtel-form-control.btn-select .btn-select-arrow {
    align-self: center;
    font-size: 10px;
}
.nwtel-form-control.btn-select ~ .btn-select-list {
    display: none;
    background-color: white;
    color: black;
    clear: both;
    list-style: none;
    padding: 0;
    margin: 0;
    border-top: none !important;
    position: absolute;
    left: 0px;
    right: 0px;
    top: 38px;
    z-index: 999;
    border: 1px solid #949596;
}
.nwtel-form-control.btn-select ~ .btn-select-list {
  padding-top: 10px;
  padding-bottom: 10px;
}
.nwtel-form-control.btn-select ~ .btn-select-list [role="option"] {
    text-align: left;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    padding: 6px 8px;
    margin: auto 10px;
}
.nwtel-form-control.btn-select ~ .btn-select-list [role="option"]:hover, .nwtel-form-control.btn-select ~ .btn-select-list [role="option"]:focus {
    background-color: #e6e6e6;
}
.nwtel-form-control.btn-select ~ .btn-select-list [role="option"]:focus {
  outline: none;
  box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #000, 0 0 2px 5px #000;
}

.nwtel-disabled-button {
  position: relative;
  right: 30px;
  top: 0;
  bottom: 0;
  height: 14px;
  margin: auto;
  font-size: 14px;
  pointer-events: none;
  background: none;
  border: none;
  padding: 0;
}

.nwtel-form-control-select {
  -webkit-appearance: none;
  -moz-appearance: none;
  cursor: pointer;
}

.date-picker-input.nwtel-form-control {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Error */
.form-error::placeholder {
  color: #bd2025;
}
.form-error .nwtel-form-control:focus {
  border-color: #bd2025;
}

.error-message {
  color: #bd2025;
  display: none;
}
.form-error .error-message {
  color: #bd2025;
  display: block;
}
.form-error input,
.form-error select,
.form-error textarea {
  color: #bd2025;
  border: 2px solid #bd2025;
}
.form-error input::placeholder {
  color: #bd2025;
}
.form-error label span {
  color: #bd2025;
}

/* Text Area */
textarea {
  display: block;
  width: 100% !important;
  min-height: 100px;
  line-height: 1;
  background-color: #fff;
  background-image: none;
  border: 1px solid #949596;
  padding: 20px;
  border-radius: 0px;
}
textarea:focus {
  border-color: #ababab;
  outline: none;
}

/*Check Boxes*/
.nwtel-checkbox-input {
  position: absolute;
  width: 48px;
  z-index: 1;
  height: 48px;
  opacity: 0;
  top: -16px;
  left: -9px;
}
.nwtel-check-element {
  position: absolute;
  top: 0;
  left: 0;
  height: 24px;
  width: 24px;
  background: #fff;
  box-shadow: inset 0 1px 3px 0 rgba(0, 0, 0, 0.2);
  border: 1px solid #949596;
  border-radius: 4px;
}
.nwtel-check-element:before {
  color: white;
  font-size: 11px;
  position: relative;
  left: 3px;
  top: 2px;
}
.nwtel-checkbox-input:checked ~ .nwtel-check-element {
  background: #00865d;
  border: 0;
  box-shadow: inset 0 1px 3px 0 rgba(0, 0, 0, 0.14);
}
.nwtel-checkbox-input:checked ~ .nwtel-check-control {
  color: #000000;
  font-family: "BasisGrotesquePro-med";
  font-weight: 500;
}
.nwtel-checkbox-input:disabled ~ .nwtel-check-element {
  border-color: #e5e5e5;
}

/*Radio Buttons*/
.nwtel-radio-element {
  position: absolute;
  top: 0;
  left: 0;
  height: 24px;
  width: 24px;
  background: #fff;
  box-shadow: inset 0 1px 1px 1px rgba(0, 0, 1, 0.05);
  border: 1px solid #949596;
  border-radius: 35px;
}
.nwtel-radio-element:before {
  content: "";
  width: 12px;
  height: 12px;
  background: #fff;
  position: absolute;
  top: 5px;
  left: 5px;
  border-radius: 100%;
}
.nwtel-checkbox-input:checked ~ .nwtel-radio-element {
  background: #00865d;
  border: 1px solid #00865d;
}
.nwtel-checkbox-input:disabled ~ .nwtel-radio-element {
  border-color: #e5e5e5;
}

/* Check Boxes and Radio Buttons */
.nwtel-graphical-ctrl {
  position: relative;
}
.nwtel-check-control {
  position: relative;
  padding-left: 40px;
  top: 2px;
  font-weight: normal;
  cursor: pointer;
}
.nwtel-checkbox-input:disabled ~ .nwtel-check-control {
  color: #b2b2b2;
  font-weight: normal;
}
/*Forms end*/

/*Accordion*/
.accordion-items li {
  list-style: none;
}
.accordion-items {
  border: none;
  border-bottom: 1px solid #949596;
  margin: 0 !important;
  border-radius: 0 !important;
}
.accordion-items:last-child {
  border: none;
}
.accordion-items .accordion-body {
  border-top: 1px solid #949596;
  background-color: #f9f9f9;
}
.accordion-items .accordion-inner {
  padding: 30px;
}
.accordion-items a {
  display: block;
  padding: 17px 15px;
  text-decoration: none;
  width: 100%;
}
.accordion-items a .icon-nwtel-collapse {
  top: 3px;
}
.accordion-items a:hover {
  text-decoration: none;
}
.accordion-items li:last-child {
  border-bottom: none;
}
.accordion-items .text {
  width: calc(100% - 75px);
  display: inline-block;
  vertical-align: text-top;
}
/*More info accordion*/
.more-info-toggle {
  margin: 10px 0;
  display: block;
}
.more-info-toggle [class^="icon-"] {
  margin-right: 10px;
}
.more-info-toggle[aria-expanded="true"] .icon-expand-small {
  display: none;
}
.more-info-toggle[aria-expanded="true"] .icon-collapse-small {
  display: inline-block;
}
.more-info-toggle[aria-expanded="false"] .icon-expand-small {
  display: inline-block;
}
.more-info-toggle[aria-expanded="false"] .icon-collapse-small {
  display: none;
}
/*END: Accordion*/

/*modal window vertical centering*/
.modal {
  text-align: center;
  padding: 0;
  z-index: 9999999;
}
.modal:before {
  content: "";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  margin-right: -4px;
}
.modal-dialog {
  display: inline-block;
  text-align: left;
  vertical-align: middle;
}

/*Tool Tips*/
.tooltip.fade.in {
  opacity: 1;
}
.tooltip {
  min-width: 240px;
  padding: 0;
}
.tooltip-inner {
  background-color: #f9f9f9;
  color: #666;
  border: 2px solid #b9b9b9;
  padding: 30px;
  font-size: 14px;
  max-width: 300px;
  -webkit-box-shadow: 2px 0px 3px 1px rgba(0, 0, 0, 0.1);
  box-shadow: 2px 0px 3px 1px rgba(0, 0, 0, 0.1);
  text-align: left;
}
/*Tooltip bottom*/
.tooltip.bottom {
  top: calc(100% - 50px);
}
.tooltip.bottom .tooltip-arrow {
  border-width: 0 15px 15px;
  border-bottom-color: #b9b9b9;
  left: 50%;
  margin-left: 0;
  top: -10px;
  margin-left: -15px;
}
.tooltip.bottom .tooltip-arrow:before {
  content: "";
  display: block;
  border-width: 0 15px 15px;
  width: 0;
  height: 0;
  border-color: transparent;
  border-bottom-color: #f9f9f9;
  border-style: solid;
  left: -15px;
  bottom: -3px;
  position: relative;
}
/*Tooltip top*/
.tooltip.top {
  margin-top: -12px;
}
.tooltip.bottom {
  margin-top: 12px;
}
.tooltip.left {
  margin-left: -10px;
}
.tooltip.right {
  margin-left: 10px;
}
.tooltip.top .tooltip-arrow {
  border-width: 15px 15px 15px 15px;
  border-top-color: #b9b9b9;
  left: 50%;
  margin-left: 0;
  bottom: -25px;
  margin-left: -15px;
}
.tooltip.top .tooltip-arrow:before {
  content: "";
  display: block;
  border-width: 15px 15px 15px 15px;
  width: 0;
  height: 0;
  border-color: transparent;
  border-top-color: #f9f9f9;
  border-style: solid;
  left: -15px;
  top: -18px;
  position: relative;
}
/*Tooltip Left*/
.tooltip.left .tooltip-arrow {
  top: 50%;
  right: -8px;
  margin-top: -14px;
  border-width: 15px 0 15px 15px;
  border-left-color: #b9b9b9;
}
.tooltip.left .tooltip-arrow:before {
  content: "";
  display: block;
  border-width: 15px 0px 15px 15px;
  width: 0;
  height: 0;
  border-color: transparent;
  border-left-color: #f9f9f9;
  border-style: solid;
  right: 18px;
  top: -15px;
  position: relative;
}
/*Tooltip Right*/
.tooltip.right .tooltip-arrow {
  top: 50%;
  left: -23px;
  margin-top: -14px;
  border-width: 15px 15px 15px 15px;
  border-right-color: #b9b9b9;
}
.tooltip.right .tooltip-arrow:before {
  content: "";
  display: block;
  border-width: 15px 15px 15px 15px;
  width: 0;
  height: 0;
  border-color: transparent;
  border-right-color: #f9f9f9;
  border-style: solid;
  left: -12px;
  top: -15px;
  position: relative;
}

/* Skip to main */
header .skip-to-main-link {
  display: inline-block;
  padding: 9px 12px;
  position: absolute;
  top: -50px;
  left: 45%;
  -webkit-transform: translateX(0%);
  transform: translateX(0%);
  text-decoration: none;
  border-bottom-right-radius: 8px;
  transition: top 0.3s ease-out;
  -webkit-transition: top 0.3s ease-out;
  z-index: 3000;
  font-size: 11px;
  background: #e6e6e6;
  text-decoration: underline;
}
header .skip-to-main-link:focus {
  top: 0;
}
footer .skip-to-main-link {
  display: inline-block;
  padding: 7px 12px;
  position: absolute;
  left: -300px;
  text-decoration: underline;
  border-bottom-right-radius: 8px;
  transition: left 0.3s ease-out;
  -webkit-transition: left 0.3s ease-out;
  background-color: #e6e6e6;
  z-index: 3000;
  font-size: 13px;
}
footer .skip-to-main-link:focus {
  left: 0;
}
/* Skip to main - END */
@media (max-width: 520px) {
  .container {
    padding-right: 15px;
    padding-left: 15px;
  }
  .tooltip {
    min-width: 50px;
  }
  .tooltip-inner {
    padding: 15px;
    font-size: 12px;
  }
}
@media (min-width: 520px) {
  .container {
    width: 520px;
  }
}

@media (min-width: 320px) and (max-width: 767.98px) {
  .modal-lg,
  .modal-md,
  .modal-sm {
    width: 100%;
  }
  .accordion-items .accordion-inner {
    padding: 15px;
  }
  .modal:before {
    height: 60%;
  }
  /*Helper  */
  .hidden-xs {
    display: none;
  }
}

@media (min-width: 768px) and (max-width: 991.98px) {
  .container {
    width: 768px;
  }
  .container.liquid-container {
    width: 100%;
  }
  .modal-dialog {
    margin: 30px auto;
  }
  .modal-dialog {
    margin: 30px auto;
  }
  .modal-content {
    min-height: inherit;
  }
}

@media (min-width: 992px) {
  .container {
    width: 970px;
  }

  .skip-to-main-link,
  header .skip-to-main-link,
  footer .skip-to-main-link {
    display: none;
  }
}

@media (min-width: 1000px) {
  .desktop-hidden {
    display: none !important;
  }
}

@media screen and (max-width: 1100px) {
  .modal:before {
    height: 50%;
  }
}

/* tooltip fix for ios. ios doesn't recognize click event on the body but adding this lets it do that */
@supports (-webkit-overflow-scrolling: touch) {
  body {
    cursor: pointer;
  }
} /* CSS specific to iOS devices */
