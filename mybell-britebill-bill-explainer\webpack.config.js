const { siteTemplate } = require("webpack-common");
const package = require("./package.json");
const path = require("path");
 
module.exports = (env) =>
  siteTemplate(
    package,
    {
      site: path.resolve(__dirname, "src"),
      node_modules: path.resolve(__dirname, "node_modules"),
      dist: path.resolve(__dirname, "dist"),
    }, // PATHS
    {
        output: {
            path: path.resolve(__dirname, "../Bundles/", package.domain, package.name),
        },
    }, // override
    {}, // buildOnlyOverride
    {}, // debugOnlyOverride
    {} // config
  );