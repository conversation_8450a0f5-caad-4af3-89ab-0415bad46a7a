import { Init, RenderWidget } from "bwtk";

export function initialize(config: any, root: string, loader: any, debug) {
  config = { ...config, "loader.staticWidgetMappings": {
    "singleban-bill": {
      factory: () => require("singleban-bill"),
      namespace: "singleban/billing"
    },
    "britebill-bill-explainer": {
      factory: () => require("britebill-bill-explainer"),
      namespace: "singleban/billing"
    }
  }};

  Init(config);
  const bwtk = require("bwtk");
  const loc = bwtk.ServiceLocator.instance.getService(bwtk.CommonServices.Localization);

  loc.preloadLocaleData({
    "singleban-bill": "SingleBAN/Billing/singleban-bill",
    "britebill-bill-explainer": "SingleBAN/Billing/britebill-bill-explainer"
  });

  RenderWidget("singleban-bill", _(root) as any);

  function _(root) {
    return document.getElementById(root);
  }
}