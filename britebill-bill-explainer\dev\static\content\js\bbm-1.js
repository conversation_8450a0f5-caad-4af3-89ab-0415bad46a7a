

// All resource

function clearCheckbox($this) {
    $this.closest('.dropdownList').find('input[type="checkbox"]').prop('checked', false);
    $this.closest('.dropdownList').find('.graphical_ctrl').removeClass('active');
    var label = $this.closest('.custom_dropdown').find('button').attr('filterby');
    label = label.toLowerCase().replace(/\b[a-z]/g, function (letter) {
        return letter.toUpperCase();
    });
    $this.closest('.dropdownList').find('.label_text').html(label);
    $this.closest('.custom_dropdown').find('button').html(label);
}

// All resource ends

//Update arrow in footer
function resizeArrow() {
    var containerHeight = $('footer.v2 .footer-main-links-lobs li.active').outerHeight() || 0;
    if (containerHeight > 0) {
        var arrowHeight = containerHeight / 2;
        var arrowPosition = (0 - containerHeight);
        $('head').append('<style>footer.v2 .footer-main-links-lobs li.active:after{border-width:' + arrowHeight + 'px;right:' + arrowPosition + 'px;}</style>');
    }
}

function resizeTextContainer() {
    if (window.matchMedia('(max-width: 767px)').matches) {
        $('.textWithSocialMediaContainer').find('.txtContainer').css("min-height", "");
    } else {
        var socialIconHeight = $('.socialLinksContainer').find('.social_icons_cont').height() || 0;
        var txtContainerHeight = $('.textWithSocialMediaContainer').find('.txtContainer').height() || 0;
        if (txtContainerHeight < socialIconHeight) {
            socialIconHeight -= 9;
            $('.textWithSocialMediaContainer').find('.txtContainer').css("min-height", socialIconHeight + 'px');
        }
    }
   
}

$(document).ready(function () {

    var to = 0;
    $(window).resize(function () {
        clearTimeout(to);
        to = setTimeout(function () {
            resizeArrow(); 
            resizeTextContainer()
        }, 150);
    });
    
    //Resizing Text in Article Section
    resizeTextContainer()

    //Global Variables
    var cardEl = '.card-clickable',
        cardMainLinkEl = '.card-clickable-mainlink',
        hoverAbleEl = '.card-clickable a:not(.card-clickable-mainlink),.card-clickable button',
        clickableEl = 'a, input, textarea, button, select, [tabindex]:not([tabindex="-1"]), iframe';
    //Click Events
    $(document).on('click', cardEl, function (e) {
        var el = $(this).find(cardMainLinkEl), target = $(e.target);
        e.stopPropagation();
        if (target.is(clickableEl) || el.length < 1) {
            return;
        }
        el.focus().get(0).click();
    });


    //customer dropdown
    
    //added the touch event or click to document
    $(document).on("touchend click", function(e){
        //check if anything other than the custom dropdown is clicked then hide the dropdowns if any
        if (!$(event.target).closest('.custom_dropdown').length) {
            $('.custom_dropdown .dropdownList:not(.hide)').addClass('hide');
            $('.custom_dropdown button').attr('aria-expanded', 'false');
        }
    });
     
    $('a[trigger-pop]').on('click touch', function (e) {
    
        if($(this).next().is(':visible')) {
            $(this).next().hide();
        }
        else {
            $('a[trigger-pop]').next().hide();
            $(this).next().show();
            e.stopPropagation();
        }
    });


    //added touch end event to the custom dropdown button and label to show hide the dropdown
    var handled = false;
    $(document).on("touchend click", '.custom_dropdown .form-control-select , .custom_dropdown .dropdown_label', function(e){
        e.stopImmediatePropagation();

        //for touch devices
        if(e.type == "touchend") {
            handled = true;
            HandleClickTouchAction(this);
        }
        //for desktop click handling
        else if(e.type == "click" && !handled) {
            //skip for the custom dropdown .js elem 
            HandleClickTouchAction(this);
        }
        else {
            handled = false;
        }
    });

    //handle the filters vs sortby
    function HandleClickTouchAction(elem) {
        //switch between the custom dropdown
        const ComboBox = $(elem).closest('.custom_dropdown').hasClass( "withCheckbox" );
        if(ComboBox){
            handleDropdown(elem);
        }else{
            handleDropdownClosingOthers(elem);
        }
    }
    
    //fx to show or hide dropdown based on the click of button or lsit
    function handleDropdown(elem) {  
        var $dropdown = $(elem).closest('.custom_dropdown.withCheckbox').find('.dropdownList');
        var $button = $(elem).closest('.custom_dropdown.withCheckbox').find('button');

        if($dropdown.hasClass('hide')){
            $('.custom_dropdown .dropdownList:not(.hide)').addClass('hide');
            $('.custom_dropdown button').attr('aria-expanded', 'false');
            $dropdown.removeClass('hide');
            $button.attr('aria-expanded', 'true');
        }else{
            //will triggered if button is clicked or the label is clicked
            $dropdown.addClass('hide');
            $button.attr('aria-expanded', 'false');
        } 
    }

    //close other dropdowns for sortby
    function handleDropdownClosingOthers(elem){
            var $dropdown = $(elem).closest('.custom_dropdown').find('.dropdownList');
            var $button = $(elem).closest('.custom_dropdown').find('button');
                    
            // specific dropdown label clicked for the sortby
            if($(elem).hasClass('dropdown_label')){
                if(!$dropdown.hasClass('hide')){
                    $dropdown.addClass('hide');
                    $button.attr('aria-expanded', 'false');
                }
            }
            else{
                // // hide check  box dropdownz
                $('.custom_dropdown.withCheckbox .dropdownList:not(.hide)').addClass('hide');
                $('.custom_dropdown.withCheckbox button').attr('aria-expanded', 'false');

                //sortby handle for the button click on dropdown not showing at first
                $button.attr('aria-expanded', 'true');
                $dropdown.removeClass('hide');
            }
    }

    $(document).on('change', '.dropdown_item input[type="checkbox"]', function (e) {
        var $this = $(this); 
        var count = $this.closest('.dropdownList').find('input[type="checkbox"]:checked').length;
        var label = $this.closest('.custom_dropdown').find('button').attr('filterby') + (count > 0 ? ' (' + count + ')' : ''); 
        label = label.toLowerCase().replace(/\b[a-z]/g, function (letter) {
            return letter.toUpperCase();
        });
        $this.closest('.dropdownList').find('.label_text').html(label);
        $this.closest('.custom_dropdown').find('button').html(label);
        e.stopPropagation();
    });

    $(document).on('click', '.clearBtn', function (e) {
        clearCheckbox($(this));
        e.stopPropagation();
    });

    $(document).on('keypress', '.clearBtn', function (e) {
        if (e.which === 13) {
            clearCheckbox($(this));
            e.stopPropagation();
        }
    });

    $(document).on('click', '.custom_dropdown div:not([aria-multiselectable="true"]) li[role="option"]', function (e) {
        $(this).closest('.custom_dropdown').find('.dropdownList').addClass('hide');
        e.stopPropagation();
    });

    $(document).on('change', '.custom_dropdown li input[type="checkbox"]', function (e) {
        this.setAttribute('aria-selected', this.checked);
        this.setAttribute('aria-checked', this.checked);
        if (this.checked) {
            $(this).closest('label').addClass('active');
        } else {
            $(this).closest('label').removeClass('active');
        }
        e.stopPropagation();
    });

    //clearing the filters
    $(document).on('click', '.clearAllFilters:not(.disabled)', function (e) {
        $('.clearAllFilters').addClass('d-none').removeClass('d-block');
        $('.clearBtn').each(function (el) {
            clearCheckbox($(this));
        });
        $('button[aria-haspopup="listbox"]').each(function (el) {
            var elem = $(this).closest('.custom_dropdown');
            var label = elem.find('.label_text').html();
            elem.find('button').html(elem.find('ul li:first-child').html());
            elem.find('li').attr('aria-selected', 'false').removeClass('focused');
            elem.find('li:first-child').attr('aria-selected', 'true').addClass('focused');
            elem.find('.dropdownList').attr('aria-activedescendant', elem.find('li:first-child').attr('id'));

        });
        
    });

    $(document).on('keyup', '.clearAllFilters:not(.disabled)', function (e) {
        if (e.which === 13) {
            $('.clearAllFilters:not(.disabled)').trigger('click');
        }
    });

    // $(document).on('click', '.custom_dropdown:not(.withCheckbox) .dropdownList', function (e) {
    //     var cont = $(this).closest('.custom_dropdown');
    //     $(this).addClass('hide');
    // });

    $(document).on('click', '.dropdownList[aria-multiselectable="false"] ul li', function () {
        var el = $(this);
        var ddList = el.closest('.dropdownList');
        el.closest('.dropdownList').find('li[role="option"]').attr('aria-selected', 'false').removeClass('focused');
        el.attr('aria-selected', 'true').addClass('focused');
    });
    //customer dropdown ends 

    var isKeyPress = false;

    $(document).on('keypress', 'button.slick-arrow', function (e) {
        if (e.which === 13) {
            isKeyPress = true;
        }
    });

    $(document).on('click', 'button.slick-arrow', function (e) {
        if (!isKeyPress) {
            $(this).blur();
        }
        isKeyPress = false;
    });

    resizeArrow();

    setTimeout(function () {
        $(document).find('.graphical_ctrl_checkbox input:disabled').closest('.graphical_ctrl_checkbox').removeClass('pointer');
    }, 100);
});
// End document ready