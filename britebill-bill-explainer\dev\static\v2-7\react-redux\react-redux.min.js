!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("react"),require("redux")):"function"==typeof define&&define.amd?define(["exports","react","redux"],e):e(t.ReactRedux={},t.React,t.Redux)}(this,function(t,e,n){"use strict";"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;function r(t,e){return t(e={exports:{}},e.exports),e.exports}function o(t){return function(){return t}}var i=function(){};i.thatReturns=o,i.thatReturnsFalse=o(!1),i.thatReturnsTrue=o(!0),i.thatReturnsNull=o(null),i.thatReturnsThis=function(){return this},i.thatReturnsArgument=function(t){return t};var s=i;var u=function(t,e,n,r,o,i,s,u){if(!t){var p;if(void 0===e)p=Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var a=[n,r,o,i,s,u],c=0;(p=Error(e.replace(/%s/g,function(){return a[c++]}))).name="Invariant Violation"}throw p.framesToPop=1,p}},p="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",a=r(function(t){t.exports=function(){function t(t,e,n,r,o,i){i!==p&&u(!1,"Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types")}function e(){return t}t.isRequired=t;var n={array:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e};return n.checkPropTypes=s,n.PropTypes=n,n}()}),c=a.shape({trySubscribe:a.func.isRequired,tryUnsubscribe:a.func.isRequired,notifyNestedSubs:a.func.isRequired,isSubscribed:a.func.isRequired}),d=a.shape({subscribe:a.func.isRequired,dispatch:a.func.isRequired,getState:a.func.isRequired}),f=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},l=Object.assign||function(t){for(var e=1;arguments.length>e;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},h=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)},y=function(t,e){var n={};for(var r in t)0>e.indexOf(r)&&Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n},b=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e};function v(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"store",r=arguments[1]||n+"Subscription",o=function(t){function o(e,r){f(this,o);var i=b(this,t.call(this,e,r));return i[n]=e.store,i}return h(o,t),o.prototype.getChildContext=function(){var t;return(t={})[n]=this[n],t[r]=null,t},o.prototype.render=function(){return e.Children.only(this.props.children)},o}(e.Component);return o.propTypes={store:d.isRequired,children:a.element.isRequired},o.childContextTypes=((t={})[n]=d.isRequired,t[r]=c,t),o}var m=v(),P=r(function(t,e){var n,r,o,i,s,u,p,a;t.exports=(n={childContextTypes:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},r={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},o=Object.defineProperty,i=Object.getOwnPropertyNames,s=Object.getOwnPropertySymbols,u=Object.getOwnPropertyDescriptor,a=(p=Object.getPrototypeOf)&&p(Object),function t(e,c,d){if("string"!=typeof c){if(a){var f=p(c);f&&f!==a&&t(e,f,d)}var l=i(c);s&&(l=l.concat(s(c)));for(var h=0;l.length>h;++h){var y=l[h];if(!(n[y]||r[y]||d&&d[y])){var b=u(c,y);try{o(e,y,b)}catch(t){}}}return e}return e})}),g=function(t,e,n,r,o,i,s,u){if(!t){var p;if(void 0===e)p=Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var a=[n,r,o,i,s,u],c=0;(p=Error(e.replace(/%s/g,function(){return a[c++]}))).name="Invariant Violation"}throw p.framesToPop=1,p}},O=null,S={notify:function(){}};var w=function(){function t(e,n,r){f(this,t),this.store=e,this.parentSub=n,this.onStateChange=r,this.unsubscribe=null,this.listeners=S}return t.prototype.addNestedSub=function(t){return this.trySubscribe(),this.listeners.subscribe(t)},t.prototype.notifyNestedSubs=function(){this.listeners.notify()},t.prototype.isSubscribed=function(){return!!this.unsubscribe},t.prototype.trySubscribe=function(){var t,e;this.unsubscribe||(this.unsubscribe=this.parentSub?this.parentSub.addNestedSub(this.onStateChange):this.store.subscribe(this.onStateChange),this.listeners=(t=[],e=[],{clear:function(){e=O,t=O},notify:function(){for(var n=t=e,r=0;n.length>r;r++)n[r]()},get:function(){return e},subscribe:function(n){var r=!0;return e===t&&(e=t.slice()),e.push(n),function(){r&&t!==O&&(r=!1,e===t&&(e=t.slice()),e.splice(e.indexOf(n),1))}}}))},t.prototype.tryUnsubscribe=function(){this.unsubscribe&&(this.unsubscribe(),this.unsubscribe=null,this.listeners.clear(),this.listeners=S)},t}(),C=0,T={};function x(){}function E(t){var n,r,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=o.getDisplayName,s=void 0===i?function(t){return"ConnectAdvanced("+t+")"}:i,u=o.methodName,p=void 0===u?"connectAdvanced":u,a=o.renderCountProp,v=void 0===a?void 0:a,m=o.shouldHandleStateChanges,O=void 0===m||m,S=o.storeKey,E=void 0===S?"store":S,R=o.withRef,j=void 0!==R&&R,q=y(o,["getDisplayName","methodName","renderCountProp","shouldHandleStateChanges","storeKey","withRef"]),N=E+"Subscription",M=C++,D=((n={})[E]=d,n[N]=c,n),U=((r={})[N]=c,r);return function(n){g("function"==typeof n,"You must pass a component to the function returned by "+p+". Instead received "+JSON.stringify(n));var r=n.displayName||n.name||"Component",o=s(r),i=l({},q,{getDisplayName:s,methodName:p,renderCountProp:v,shouldHandleStateChanges:O,storeKey:E,withRef:j,displayName:o,wrappedComponentName:r,WrappedComponent:n}),u=function(r){function s(t,e){f(this,s);var n=b(this,r.call(this,t,e));return n.version=M,n.state={},n.renderCount=0,n.store=t[E]||e[E],n.propsMode=!!t[E],n.setWrappedInstance=n.setWrappedInstance.bind(n),g(n.store,'Could not find "'+E+'" in either the context or props of "'+o+'". Either wrap the root component in a <Provider>, or explicitly pass "'+E+'" as a prop to "'+o+'".'),n.initSelector(),n.initSubscription(),n}return h(s,r),s.prototype.getChildContext=function(){var t;return(t={})[N]=(this.propsMode?null:this.subscription)||this.context[N],t},s.prototype.componentDidMount=function(){O&&(this.subscription.trySubscribe(),this.selector.run(this.props),this.selector.shouldComponentUpdate&&this.forceUpdate())},s.prototype.componentWillReceiveProps=function(t){this.selector.run(t)},s.prototype.shouldComponentUpdate=function(){return this.selector.shouldComponentUpdate},s.prototype.componentWillUnmount=function(){this.subscription&&this.subscription.tryUnsubscribe(),this.subscription=null,this.notifyNestedSubs=x,this.store=null,this.selector.run=x,this.selector.shouldComponentUpdate=!1},s.prototype.getWrappedInstance=function(){return g(j,"To access the wrapped instance, you need to specify { withRef: true } in the options argument of the "+p+"() call."),this.wrappedInstance},s.prototype.setWrappedInstance=function(t){this.wrappedInstance=t},s.prototype.initSelector=function(){var e=t(this.store.dispatch,i);this.selector=function(t,e){var n={run:function(r){try{var o=t(e.getState(),r);(o!==n.props||n.error)&&(n.shouldComponentUpdate=!0,n.props=o,n.error=null)}catch(t){n.shouldComponentUpdate=!0,n.error=t}}};return n}(e,this.store),this.selector.run(this.props)},s.prototype.initSubscription=function(){O&&(this.subscription=new w(this.store,(this.propsMode?this.props:this.context)[N],this.onStateChange.bind(this)),this.notifyNestedSubs=this.subscription.notifyNestedSubs.bind(this.subscription))},s.prototype.onStateChange=function(){this.selector.run(this.props),this.selector.shouldComponentUpdate?(this.componentDidUpdate=this.notifyNestedSubsOnComponentDidUpdate,this.setState(T)):this.notifyNestedSubs()},s.prototype.notifyNestedSubsOnComponentDidUpdate=function(){this.componentDidUpdate=void 0,this.notifyNestedSubs()},s.prototype.isSubscribed=function(){return!!this.subscription&&this.subscription.isSubscribed()},s.prototype.addExtraProps=function(t){if(!(j||v||this.propsMode&&this.subscription))return t;var e=l({},t);return j&&(e.ref=this.setWrappedInstance),v&&(e[v]=this.renderCount++),this.propsMode&&this.subscription&&(e[N]=this.subscription),e},s.prototype.render=function(){var t=this.selector;if(t.shouldComponentUpdate=!1,t.error)throw t.error;return e.createElement(n,this.addExtraProps(t.props))},s}(e.Component);return u.WrappedComponent=n,u.displayName=o,u.childContextTypes=U,u.contextTypes=D,u.propTypes=D,P(u,n)}}var R=Object.prototype.hasOwnProperty;function j(t,e){return t===e?0!==t||0!==e||1/t==1/e:t!=t&&e!=e}function q(t,e){if(j(t,e))return!0;if("object"!=typeof t||null===t||"object"!=typeof e||null===e)return!1;var n=Object.keys(t);if(n.length!==Object.keys(e).length)return!1;for(var r=0;n.length>r;r++)if(!R.call(e,n[r])||!j(t[n[r]],e[n[r]]))return!1;return!0}var N="object"==typeof global&&global&&global.Object===Object&&global,M="object"==typeof self&&self&&self.Object===Object&&self;N||M||Function("return this")(),Function.prototype.toString.call(Object);function D(t){return function(e,n){var r=t(e,n);function o(){return r}return o.dependsOnOwnProps=!1,o}}function U(t){return null!==t.dependsOnOwnProps&&void 0!==t.dependsOnOwnProps?!!t.dependsOnOwnProps:1!==t.length}function _(t,e){return function(e,n){var r=function(t,e){return r.dependsOnOwnProps?r.mapToProps(t,e):r.mapToProps(t)};return r.dependsOnOwnProps=!0,r.mapToProps=function(e,n){r.mapToProps=t,r.dependsOnOwnProps=U(t);var o=r(e,n);return"function"==typeof o&&(r.mapToProps=o,r.dependsOnOwnProps=U(o),o=r(e,n)),o},r}}var I=[function(t){return"function"==typeof t?_(t):void 0},function(t){return t?void 0:D(function(t){return{dispatch:t}})},function(t){return t&&"object"==typeof t?D(function(e){return n.bindActionCreators(t,e)}):void 0}];var W=[function(t){return"function"==typeof t?_(t):void 0},function(t){return t?void 0:D(function(){return{}})}];function F(t,e,n){return l({},n,t,e)}var k=[function(t){return"function"==typeof t?function(t){return function(e,n){var r=n.pure,o=n.areMergedPropsEqual,i=!1,s=void 0;return function(e,n,u){var p=t(e,n,u);return i?r&&o(p,s)||(s=p):(i=!0,s=p),s}}}(t):void 0},function(t){return t?void 0:function(){return F}}];function A(t,e,n,r){return function(o,i){return n(t(o,i),e(r,i),i)}}function H(t,e,n,r,o){var i=o.areStatesEqual,s=o.areOwnPropsEqual,u=o.areStatePropsEqual,p=!1,a=void 0,c=void 0,d=void 0,f=void 0,l=void 0;function h(o,p){var h,y,b=!s(p,c),v=!i(o,a);return a=o,c=p,b&&v?(d=t(a,c),e.dependsOnOwnProps&&(f=e(r,c)),l=n(d,f,c)):b?(t.dependsOnOwnProps&&(d=t(a,c)),e.dependsOnOwnProps&&(f=e(r,c)),l=n(d,f,c)):v?(h=t(a,c),y=!u(h,d),d=h,y&&(l=n(d,f,c)),l):l}return function(o,i){return p?h(o,i):(d=t(a=o,c=i),f=e(r,c),l=n(d,f,c),p=!0,l)}}function K(t,e){var n=e.initMapStateToProps,r=e.initMapDispatchToProps,o=e.initMergeProps,i=y(e,["initMapStateToProps","initMapDispatchToProps","initMergeProps"]),s=n(t,i),u=r(t,i),p=o(t,i);return(i.pure?H:A)(s,u,p,t,i)}function L(t,e,n){for(var r=e.length-1;r>=0;r--){var o=e[r](t);if(o)return o}return function(e,r){throw Error("Invalid value of type "+typeof t+" for "+n+" argument when connecting component "+r.wrappedComponentName+".")}}function V(t,e){return t===e}var Y=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.connectHOC,n=void 0===e?E:e,r=t.mapStateToPropsFactories,o=void 0===r?W:r,i=t.mapDispatchToPropsFactories,s=void 0===i?I:i,u=t.mergePropsFactories,p=void 0===u?k:u,a=t.selectorFactory,c=void 0===a?K:a;return function(t,e,r){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},u=i.pure,a=void 0===u||u,d=i.areStatesEqual,f=void 0===d?V:d,h=i.areOwnPropsEqual,b=void 0===h?q:h,v=i.areStatePropsEqual,m=void 0===v?q:v,P=i.areMergedPropsEqual,g=void 0===P?q:P,O=y(i,["pure","areStatesEqual","areOwnPropsEqual","areStatePropsEqual","areMergedPropsEqual"]),S=L(t,o,"mapStateToProps"),w=L(e,s,"mapDispatchToProps"),C=L(r,p,"mergeProps");return n(c,l({methodName:"connect",getDisplayName:function(t){return"Connect("+t+")"},shouldHandleStateChanges:!!t,initMapStateToProps:S,initMapDispatchToProps:w,initMergeProps:C,pure:a,areStatesEqual:f,areOwnPropsEqual:b,areStatePropsEqual:m,areMergedPropsEqual:g},O))}}();t.Provider=m,t.createProvider=v,t.connectAdvanced=E,t.connect=Y,Object.defineProperty(t,"__esModule",{value:!0})});
