﻿/*-------------------------------
Custom Styles for Accessibility Fixes
--------------------------------*/

/*************************************Header and Footer Overrides***************************************************/
/*General Outline overrides*/
.accss-styles-init .header-outline-override .connector-brand > a[href],
.accss-styles-init .header-outline-override .connector-areas > li > a[href],
.accss-styles-init .header-outline-override .location-area-wrap > a[href],
.accss-styles-init .header-outline-override .login-area-wrap > div > a[href],
.accss-styles-init .header-outline-override a.secondary-nav-lob, .accss-styles-init .header-outline-override a.secondary-nav-lob,
.accss-styles-init .header-outline-override .third-level-nav > li > a[href],
.accss-styles-init .footer-outline-override .footerList li > a[href],
.accss-styles-init .header-outline-override.gn-non-responsive .connector-location a[href] {
    position: relative;
    outline: none !important;
}

    .accss-styles-init .header-outline-override .connector-brand > a[href]:focus::before, .accss-styles-init .header-outline-override .connector-brand > a[href]:active::before,
    .accss-styles-init .header-outline-override .connector-areas > li > a[href]:focus::before, .accss-styles-init .header-outline-override .connector-areas > li > a[href]:active::before,
    .accss-styles-init .header-outline-override .location-area-wrap > a[href]:focus::before, .accss-styles-init .header-outline-override .location-area-wrap > a[href]:active::before,
    .accss-styles-init .header-outline-override .login-area-wrap > div > a[href]:focus::before, .accss-styles-init .header-outline-override .login-area-wrap > div > a[href]:active::before,
    .accss-styles-init .header-outline-override a.secondary-nav-lob:focus::before, .accss-styles-init .header-outline-override a.secondary-nav-lob:active::before,
    .accss-styles-init .header-outline-override .third-level-nav > li > a[href]:focus::before, .accss-styles-init .header-outline-override .third-level-nav > li > a[href]:active::before,
    .accss-styles-init .footer-outline-override .footerList li > a[href]:focus::before, .accss-styles-init .footer-outline-override .footerList li > a[href]:active::before {
        content: '';
        height: calc(100% + 6px);
        width: calc(100% + 6px);
        position: absolute;
        top: -3px;
        left: -3px;
        display: block;
        box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
        z-index: 1;
    }

    .accss-styles-init .header-outline-override.gn-non-responsive .connector-brand > a[href]:focus::before, .accss-styles-init .header-outline-override .connector-brand > a[href]:active::before,
    .accss-styles-init .header-outline-override.gn-non-responsive .connector-areas > li > a[href]:focus::before, .accss-styles-init .header-outline-override .connector-areas > li > a[href]:active::before,
    .accss-styles-init .header-outline-override.gn-non-responsive .location-area-wrap > a[href]:focus::before, .accss-styles-init .header-outline-override .location-area-wrap > a[href]:active::before,
    .accss-styles-init .header-outline-override.gn-non-responsive .login-area-wrap > div > a[href]:focus::before, .accss-styles-init .header-outline-override .login-area-wrap > div > a[href]:active::before,
    .accss-styles-init .header-outline-override.gn-non-responsive a.secondary-nav-lob:focus::before, .accss-styles-init .header-outline-override a.secondary-nav-lob:active::before,
    .accss-styles-init .header-outline-override.gn-non-responsive .third-level-nav > li > a[href]:focus::before, .accss-styles-init .header-outline-override .third-level-nav > li > a[href]:active::before,
    .accss-styles-init .footer-outline-override.gn-non-responsive .footerList li > a[href]:focus::before, .accss-styles-init .footer-outline-override .footerList li > a[href]:active::before,
    .accss-styles-init .header-outline-override.gn-non-responsive .connector-location a[href]:focus::before, .accss-styles-init .header-outline-override.gn-non-responsive .connector-location a[href]:active::before {
        content: '';
        height: calc(100% + 3px);
        width: calc(100% + 8px);
        position: absolute;
        top: -1px;
        left: -4px;
        display: block;
        box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
        z-index: 1
    }

.accss-styles-init .header-outline-override .connector-lob > a[href]:not(.connector-lob-no-href):focus, .accss-styles-init .header-outline-override .connector-lob > a[href]:not(.connector-lob-no-href):active,
.accss-styles-init .header-outline-override .connector-lob li > a[href]:focus, .accss-styles-init .header-outline-override .connector-lob li > a[href]:active,
.accss-styles-init .header-outline-override .js-connector-search-form > input:focus, .accss-styles-init .header-outline-override .js-connector-search-form > input:active,
.accss-styles-init .header-outline-override .js-connector-search-form > button:focus, .accss-styles-init .header-outline-override .js-connector-search-form > button:active,
.accss-styles-init .header-outline-override .connector-active-secondary-nav .secondary-nav-dropdown a[href]:focus, .accss-styles-init .header-outline-override .connector-active-secondary-nav .secondary-nav-dropdown a[href]:active,
.accss-styles-init .header-outline-override .login-area-wrap .connector-login-modal a[href]:focus, .accss-styles-init .header-outline-override .login-area-wrap .connector-login-modal a[href]:active {
    outline: none !important;
    box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
}

.accss-styles-init .header-outline-override #connector-search.accss-nav-search-form {
    z-index: 2;
}

    .accss-styles-init .header-outline-override #connector-search.accss-nav-search-form > input[type="search"] {
        height: 56px;
    }

.accss-styles-init .skip-to-main-link.bg-light-gray, .accss-styles-init .skip-to-main-link.bg-light-gray {
    background-color: #efefef !important;
    color: #212121 !important;
}

.accss-styles-init .skip-to-main-link:focus, .accss-styles-init .skip-to-main-link:active {
    outline: none !important;
    box-shadow: 0 0 0px 2px #257fa3, 0 0 2px 2px #257fa3, 0 0 4px 3px #fff, 0 0 0px 4px #fff;
}


/*Specific Outline overrides*/
.accss-styles-init .header-outline-override .connector-brand #mybell_gc_HOME_DESK:focus::before {
    top: -7px;
}

.accss-styles-init .header-outline-override .connector-areas > li > a[href]:focus::before, .accss-styles-init .header-outline-override .connector-areas > li > a[href]:active::before {
    top: -8px;
}

.accss-styles-init .header-outline-override .connector-lob li > a[href]:focus, .accss-styles-init .header-outline-override .connector-lob li > a[href]:active {
    box-shadow: 0 0 0px 3px #131313, 0 0 0px 3px #131313, 0 0 3px 5px #257fa3, 0 0 3px 5px #257fa3;
}

.accss-styles-init .header-outline-override .js-connector-search-form > button#search_submit {
    height: 20px;
    bottom: 0;
    margin: auto;
}

.accss-styles-init .header-outline-override:not(.gn-non-responsive) .js-connector-search-form > button#search_submit::after {
    float: left;
    padding-left: 20px;
    line-height: 0;
}

.accss-styles-init .header-outline-override.gn-non-responsive .js-connector-search-form > button#search_submit:focus:after {
    color: #fff;
}

.accss-styles-init .header-outline-override .login-area-wrap .login-register-button,
.accss-styles-init .header-outline-override .login-area-wrap .log-out-button-menu,
.accss-styles-init .header-outline-override .connector-active-secondary-nav li:not(.active) .secondary-nav-lob {
    color: #fff !important;
}

    .accss-styles-init .header-outline-override .login-area-wrap .login-register-button:hover,
    .accss-styles-init .header-outline-override .login-area-wrap .log-out-button-menu:hover {
        color: #34A8D6 !important;
    }

.accss-styles-init .header-outline-override .connector-lob-flyout {
    padding-bottom: 23px;
}

.accss-styles-init .header-outline-override .connector-lob > ul {
    padding-bottom: 7px;
}

.accss-styles-init .header-outline-override .connector-active-secondary-nav .secondary-nav-lob.dropdown-active + .secondary-nav-dropdown {
    display: block !important;
    z-index: 19;
}

.accss-styles-init .header-outline-override.gn-non-responsive .connector-brand .icon-VM-logo.inlineBlock {
    display: inline-block;
}

/* Simple header Overrides */
.accss-styles-init .simple-header-outline-override li,
.accss-styles-init .simple-header-outline-override a[href] {
    position: relative;
    outline: none !important;
}

    .accss-styles-init .simple-header-outline-override li:focus,
    .accss-styles-init .simple-header-outline-override a[href]:focus::before,
    .accss-styles-init .simple-header-outline-override a[href]:active::before {
        content: '';
        height: calc(100% + 6px);
        width: calc(100% + 6px);
        position: absolute;
        top: -3px;
        left: -3px;
        display: block;
        box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
        z-index: 1;
    }
/* Specific for paybill preauthorize Header back JBT Start */
.accss-styles-init a.outline-header-back {
    width: 67px;
    padding: 0px 8px 2px 2px;
    margin: 27px auto auto 0px;
    position: absolute;
}

.accss-styles-init a.outline-header-back-payinfo {
    width: 171px;
    height: 25px;
    padding: 0px 8px 2px 2px;
    margin: 27px auto auto 0px;
    position: absolute;
}
/* Specific for paybill preauthorize Header back JBT End */

/* Specific for paybill preauthorize Modal Close button JBT start */
.accss-styles-init .close.top-10-lg {
    top: 6px;
}
/* Specific for paybill preauthorize Modal Close button JBT end */
.accss-styles-init .accss-simplified-header-focus-outline a.simplified-header-back {
    width: auto;
    margin-top: 10px;
}
/* Simple header for DataBlockSettings CAA */
.accss-styles-init .accss-simplified-header-focus-outline .accss-simplified-header-back {
    width: auto;
    padding: 0;
    padding-right: 10px;
    margin: 22px 18px 12px;
}

    .accss-styles-init a.outline-header-back:focus, .accss-styles-init a.outline-header-back-payinfo:focus,
    .accss-styles-init .accss-simplified-header-focus-outline a.simplified-header-back:focus,
    .accss-styles-init .accss-simplified-header-focus-outline .accss-simplified-header-back:focus {
        outline: none;
        box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
    }

.accss-styles-init .accss-header-btn-pad {
    padding: 9px 30px;
}
/*login tracker component in myprofile override*/
.login-details-panel .panel-content .header-container {
    padding: 15px 30px !important;
}

    .login-details-panel .panel-content .header-container h2 {
        margin-top: 10px;
    }

/*Desktop Only Overrides*/
@media screen and (min-width: 1000px) {
    /*Header and Footer Overrides*/
    .accss-styles-init .header-outline-override .connector-lob > ul {
        display: inline-block;
    }

    .accss-styles-init .header-outline-override .connector-lob > a[href].no-flyout-menu:focus, .accss-styles-init .header-outline-override .connector-lob > a[href].no-flyout-menu:active {
        box-shadow: 0 0 0px 3px #131313, 0 0 0px 3px #131313, 0 0 3px 5px #257fa3, 0 0 3px 5px #257fa3;
        display: inline-block;
    }
}

/*Mobile and Table Only Overrides*/
@media screen and (max-width: 999.98px) {
    /*Header and Footer Overrides*/
    .accss-styles-init .header-outline-override .connector-mobile-bar a.connector-brand {
        left: 0;
    }

    .accss-styles-init .header-outline-override .connector-mobile-bar .connector-nav-open-button {
        border-radius: 0;
        margin-right: 0;
    }

    .accss-styles-init .header-outline-override .connector-mobile-bar a.connector-brand {
        padding-top: 0;
        margin-top: 10.5px;
    }

    .accss-styles-init .header-outline-override .connector-areas > li > a[href]:focus::before, .accss-styles-init .header-outline-override .connector-areas > li > a[href]:active::before {
        height: calc(100% + -3px);
        top: 4px;
    }

    .accss-styles-init .header-outline-override .connector-lob li > a[href]:focus, .accss-styles-init .header-outline-override .connector-lob li > a[href]:active {
        box-shadow: 0 0 3px 2px #2794BE, 0 0 3px 2px #257fa3;
    }

    .accss-styles-init .header-outline-override .connector-mobile-bar a.connector-brand:focus, .accss-styles-init .header-outline-override .connector-mobile-bar a.connector-brand:active,
    .accss-styles-init .header-outline-override .connector-mobile-bar .connector-nav-open-button:focus, .accss-styles-init .header-outline-override .connector-mobile-bar .connector-nav-open-button:active,
    .accss-styles-init .header-outline-override .connector-settings-mobile > li > a[href]:focus, .accss-styles-init .header-outline-override .connector-settings-mobile > li > a[href]:active,
    .accss-styles-init .header-outline-override .connector-lob-flyout-content .button-on-mobile > a[href]:focus, .accss-styles-init .header-outline-override .connector-lob-flyout-content .button-on-mobile > a[href]:active {
        outline: none !important;
        box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
    }

    .accss-styles-init .header-outline-override .connector-lob-flyout-content .connector-lob > a {
        margin: 3px 0;
        padding: 7px 40px 7px 25px;
    }

    .accss-styles-init .header-outline-override .connector-lob-flyout-content .connector-lob > ul > li > a {
        margin: 3px 0;
        padding: 7px 20px 7px 35px !important;
    }

    .accss-styles-init .header-outline-override .connector-lob-flyout-content .connector-lob:not(.active) .connector-lob-dropdown-mobile {
        display: none;
    }

    /*Utility Overrides*/
    .accss-styles-init .hidden-on-mobile {
        display: none;
    }
}
/*************************************END - Header and Footer Overrides***************************************************/

/************************************Reusable CSS**************************************************/
.accss-styles-init .margin-10-left {
    margin-left: 10px;
}

.accss-styles-init .display-inlineBlock {
    display: inline-block;
}

.accss-styles-init .white-on-bg-dark {
    color: #fff;
}

.accss-styles-init .lightred-on-bgwhite {
    color: #EB0000;
}

/*BG COLOR - #E2E2E2*/
.accss-styles-init .blue-on-lightgray {
    color: #1A6B89;
}

.accss-styles-init .links-blue-on-bg-white {
    color: #007CAD !important;
}

.accss-styles-init .links-blue-on-bg-gray,
.accss-styles-init .accss-group .links-blue-on-bg-gray,
.accss-styles-init .accss-group .links-blue-on-bg-gray .icon,
.accss-styles-init .accss-group .links-blue-on-bg-gray .caret:after {
    color: #016D98 !important;
}

    .accss-styles-init .links-blue-on-bg-white:hover,
    .accss-styles-init .links-blue-on-bg-gray:hover {
        color: #0d5f7d !important;
    }

.accss-styles-init a.link-lightred-on-bg-gray {
    color: #DB0000;
}

    .accss-styles-init a.link-lightred-on-bg-gray:hover {
        color: #bf1a18;
    }

.accss-styles-init .button-border-on-bg-white-on-hover:hover,
.accss-styles-init .button-border-on-bg-white-on-hover:active {
    color: #FFFFFF !important;
    background-color: #333333 !important;
    border-color: #333333 !important;
}

/*Class override for underline Links*/
.accss-styles-init a.underlinedLink-blue-on-bg-white {
    color: #007CAD !important;
    text-decoration: underline !important;
}

    .accss-styles-init a.underlinedLink-blue-on-bg-white:focus, .accss-styles-init a.underlinedLink-blue-on-bg-white:hover {
        color: #0d5f7d !important;
        text-decoration: none !important;
    }


.accss-styles-init .override-links-underline-container a,
.accss-styles-init a.override-link-underline {
    text-decoration: underline !important;
}

    .accss-styles-init .override-links-underline-container a:focus,
    .accss-styles-init a.override-link-underline:focus,
    .accss-styles-init .override-links-underline-container a:hover,
    .accss-styles-init a.override-link-underline:hover {
        text-decoration: none !important;
    }

/*Main Content Overrides*/
.accss-styles-init .main-outline-override a[href],
.accss-styles-init .main-outline-override button,
.accss-styles-init .main-outline-override [tabindex="0"] {
    position: relative;
    outline: none !important;
}

    .accss-styles-init .main-outline-override a[href]:focus::before, .accss-styles-init .main-outline-override a[href]:active::before,
    .accss-styles-init .main-outline-override button:focus::before, .accss-styles-init .main-outline-override button:active::before,
    .accss-styles-init .main-outline-override [tabindex="0"]:focus::before, .accss-styles-init .main-outline-override [tabindex="0"]:active::before {
        content: '';
        height: calc(100% + 6px);
        width: calc(100% + 6px);
        position: absolute;
        top: -3px;
        left: -3px;
        display: block;
        box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
        z-index: 1;
    }

.accss-styles-init .accss-link-override-bg-gray-1 a[href]:not(.accss-link-icon):not(.txtNoUnderline):not(.btn):not([role=button]) {
    text-decoration: underline;
    color: #016D98 !important;
}

.accss-styles-init .accss-link-override-bg-gray-2 a[href]:not(.accss-link-icon):not(.txtNoUnderline):not(.btn):not([role=button]) {
    text-decoration: underline;
    color: #186277 !important;
}

.accss-styles-init .accss-link-override.bgWhite a[href]:not(.accss-link-icon):not(.txtNoUnderline):not(.btn):not([role=button]),
.accss-styles-init .accss-all-links-override a:not(.accss-link-icon):not(.txtNoUnderline):not(.btn) {
    text-decoration: underline;
    color: #007CAD !important;
}

    .accss-styles-init .accss-link-override.bgWhite a[href]:not(.accss-link-icon):not(.txtNoUnderline):not(.btn):not([role=button]):hover, .accss-styles-init .accss-link-override.bgWhite a[href]:not(.accss-link-icon):not(.txtNoUnderline):not(.btn):not([role=button]):focus,
    .accss-styles-init .accss-all-links-override a:not(.accss-link-icon):not(.txtNoUnderline):not(.btn):hover, .accss-styles-init .accss-all-links-override a:not(.accss-link-icon):not(.txtNoUnderline):not(.btn):focus,
    .accss-styles-init .accss-link-override-bg-gray-1 a[href]:not(.accss-link-icon):not(.txtNoUnderline):not(.btn):not([role=button]):hover, .accss-styles-init .accss-link-override-bg-gray-1 a[href]:not(.accss-link-icon):not(.txtNoUnderline):not(.btn):not([role=button]):focus,
    .accss-styles-init .accss-link-override-bg-gray-2 a[href]:not(.accss-link-icon):not(.txtNoUnderline):not(.btn):not([role=button]):hover, .accss-styles-init .accss-link-override-bg-gray-2 a[href]:not(.accss-link-icon):not(.txtNoUnderline):not(.btn):not([role=button]):focus {
        text-decoration: none;
        color: #0d5f7d;
    }

.accss-styles-init .accss-link-underline-override {
    text-decoration: underline;
}

    .accss-styles-init .accss-link-underline-override:hover, .accss-styles-init .accss-link-underline-override:focus {
        text-decoration: none;
    }

.accss-styles-init .nounderline-important a:hover, .accss-styles-init a.nounderline-important:hover, .accss-styles-init .nounderline-important a:focus, .accss-styles-init .default-underline-important a {
    text-decoration: none !important;
}

    .accss-styles-init .default-underline-important a:hover, .accss-styles-init .default-underline-important a:focus {
        text-decoration: underline !important;
    }

/* background-color: #FFFFFF - white */
.accss-styles-init .accss-focus-outline-override-white-bg a[href]:focus, .accss-styles-init .accss-focus-outline-override-white-bg a[href]:active,
.accss-styles-init .accss-focus-outline-override-white-bg button:focus, .accss-styles-init .accss-focus-outline-override-white-bg button:active,
.accss-styles-init .accss-focus-outline-override-white-bg input:focus, .accss-styles-init .accss-focus-outline-override-white-bg input:active,
/*JBT add classes for select element.*/
.accss-styles-init .accss-focus-outline-override-white-bg select:focus, .accss-styles-init .accss-focus-outline-override-white-bg select:active,
.accss-styles-init .accss-focus-outline-override-white-bg input[type="checkbox"]:focus ~ .blue-onoffswitch-label, .accss-styles-init .accss-focus-outline-override-white-bg input[type="checkbox"]:active ~ .blue-onoffswitch-label,
.accss-styles-init .accss-focus-outline-override-white-bg input:focus ~ .ctrl_element, .accss-styles-init .accss-focus-outline-override-white-bg input:active ~ .ctrl_element,
.accss-styles-init .accss-focus-outline-override-white-bg [tabindex="0"]:focus, .accss-styles-init .accss-focus-outline-override-white-bg [tabindex="0"]:active,
.accss-styles-init .accss-focus-outline-override-white-bg select:focus, .accss-styles-init .accss-focus-outline-override-white-bg select:active {
    outline: none !important;
    box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3 !important;
}

/* background-color: #EFEFEF- light grey*/
.accss-styles-init .accss-focus-outline-override-light-grey-bg a[href]:focus, .accss-styles-init .accss-focus-outline-override-light-grey-bg a[href]:active,
.accss-styles-init .accss-focus-outline-override-light-grey-bg button:focus, .accss-styles-init .accss-focus-outline-override-light-grey-bg button:active,
.accss-styles-init .accss-focus-outline-override-light-grey-bg input:focus, .accss-styles-init .accss-focus-outline-override-light-grey-bg input:active,
.accss-styles-init .accss-focus-outline-override-light-grey-bg select:focus, .accss-styles-init .accss-focus-outline-override-light-grey-bg select:active,
.accss-styles-init .accss-focus-outline-override-light-grey-bg [tabindex="0"]:focus, .accss-styles-init .accss-focus-outline-override-light-grey-bg [tabindex="0"]:active,
.accss-styles-init .accss-focus-outline-override-light-grey-bg-element:focus, .accss-styles-init .accss-focus-outline-override-light-grey-bg-element:active {
    outline: none !important;
    box-shadow: 0 0 0px 3px #EFEFEF, 0 0 2px 3px #EFEFEF, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3 !important;
}

/* background-color: ##E5E5E5 - grey*/
.accss-styles-init .accss-focus-outline-override-grey-bg a[href]:focus, .accss-styles-init .accss-focus-outline-override-grey-bg a[href]:active,
.accss-styles-init .accss-focus-outline-override-grey-bg button:focus, .accss-styles-init .accss-focus-outline-override-grey-bg button:active,
.accss-styles-init .accss-focus-outline-override-light-grey-bg select:focus, .accss-styles-init .accss-focus-outline-override-grey-bg select:active,
.accss-styles-init .accss-focus-outline-override-grey-bg input:focus, .accss-styles-init .accss-focus-outline-override-grey-bg input:active,
.accss-styles-init .accss-focus-outline-override-grey-bg [tabindex="0"]:focus, .accss-styles-init .accss-focus-outline-override-grey-bg [tabindex="0"]:active,
.accss-styles-init .accss-focus-outline-override-grey-bg-element:focus, .accss-styles-init .accss-focus-outline-override-grey-bg-element:active {
    outline: none !important;
    box-shadow: 0 0 0px 3px #E5E5E5, 0 0 2px 3px #E5E5E5, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3 !important;
}

/* background-color: #333333 - dark grey*/
.accss-styles-init .accss-focus-outline-override-dark-grey-bg a[href]:focus, .accss-styles-init .accss-focus-outline-override-dark-grey-bg a[href]:active,
.accss-styles-init .accss-focus-outline-override-dark-grey-bg button:focus, .accss-styles-init .accss-focus-outline-override-dark-grey-bg button:active,
.accss-styles-init .accss-focus-outline-override-dark-grey-bg input:focus, .accss-styles-init .accss-focus-outline-override-dark-grey-bg input:active,
.accss-styles-init .accss-focus-outline-override-dark-grey-bg input[type="checkbox"]:focus ~ .blue-onoffswitch-label, .accss-styles-init .accss-focus-outline-override-dark-grey-bg input[type="checkbox"]:active ~ .blue-onoffswitch-label,
.accss-styles-init .accss-focus-outline-override-dark-grey-bg input:focus ~ .ctrl_element, .accss-styles-init .accss-focus-outline-override-dark-grey-bg input:active ~ .ctrl_element,
.accss-styles-init .accss-focus-outline-override-dark-grey-bg [tabindex="0"]:focus, .accss-styles-init .accss-focus-outline-override-dark-grey-bg [tabindex="0"]:active {
    outline: none !important;
    box-shadow: 0 0 0px 3px #333, 0 0 2px 3px #333, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3 !important;
}

/* background-color: #000000 - black */
.accss-styles-init .accss-focus-outline-override-black-bg a[href]:focus, .accss-styles-init .accss-focus-outline-override-black-bg a[href]:active,
.accss-styles-init .accss-focus-outline-override-black-bg button:focus, .accss-styles-init .accss-focus-outline-override-black-bg button:active,
.accss-styles-init .accss-focus-outline-override-black-bg input:focus, .accss-styles-init .accss-focus-outline-override-black-bg input:active,
.accss-styles-init .accss-focus-outline-override-black-bg [tabindex="0"]:focus, .accss-styles-init .accss-focus-outline-override-black-bg [tabindex="0"]:active {
    outline: none !important;
    box-shadow: 0 0 0px 3px #000, 0 0 2px 3px #000, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
}

/* background-color: #cc0000 - red */
.accss-styles-init .accss-focus-outline-override-red-bg a[href]:focus, .accss-styles-init .accss-focus-outline-override-red-bg a[href]:active,
.accss-styles-init .accss-focus-outline-override-red-bg button:focus, .accss-styles-init .accss-focus-outline-override-red-bg button:active,
.accss-styles-init .accss-focus-outline-override-red-bg input:focus, .accss-styles-init .accss-focus-outline-override-red-bg input:active,
.accss-styles-init .accss-focus-outline-override-red-bg [tabindex="0"]:focus, .accss-styles-init .accss-focus-outline-override-red-bg [tabindex="0"]:active {
    outline: none !important;
    box-shadow: 0 0 0px 3px #cc0000, 0 0 2px 3px #cc0000, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
}

/* no padding */
.accss-styles-init .accss-focus-outline-override a[href]:focus, .accss-styles-init .accss-focus-outline-override a[href]:active,
.accss-styles-init .accss-focus-outline-override button:focus, .accss-styles-init .accss-focus-outline-override button:active,
.accss-styles-init .accss-focus-outline-override input:focus, .accss-styles-init .accss-focus-outline-override input:active,
.accss-styles-init .accss-focus-outline-override [tabindex="0"]:focus, .accss-styles-init .accss-focus-outline-override [tabindex="0"]:active {
    outline: none !important;
    box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
}

/* pseudo before padding, any background color */
.accss-styles-init .accss-focus-outline-override-pad a[href],
.accss-styles-init .accss-focus-outline-override-pad button,
.accss-styles-init .accss-focus-outline-override-pad input,
.accss-styles-init .accss-focus-outline-override-pad select,
.accss-styles-init .accss-focus-outline-override-pad [tabindex="0"] {
    position: relative;
    outline: none !important;
    box-shadow: none !important;
}

    .accss-styles-init .accss-focus-outline-override-pad a[href]:focus::before, .accss-styles-init .accss-focus-outline-override-pad a[href]:active::before,
    .accss-styles-init .accss-focus-outline-override-pad button:focus::before, .accss-styles-init .accss-focus-outline-override-pad button:active::before,
    .accss-styles-init .accss-focus-outline-override-pad input:focus::before, .accss-styles-init .accss-focus-outline-override-pad input:active::before,
    .accss-styles-init .accss-focus-outline-override-pad select:focus::before, .accss-styles-init .accss-focus-outline-override-pad select:active::before,
    .accss-styles-init .accss-focus-outline-override-pad [tabindex="0"]:focus::before, .accss-styles-init .accss-focus-outline-override-pad [tabindex="0"]:active::before {
        content: '';
        height: calc(100% + 6px);
        width: calc(100% + 6px);
        position: absolute;
        top: -3px;
        left: -3px;
        display: block;
        box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
        z-index: 1;
    }
/************************************MyPLAN*************************************************/
.accss-styles-init .main-outline-override-v2 .graphical_ctrl input:focus ~ .ctrl_element::before {
    content: '';
    height: calc(100% + 6px);
    width: calc(100% + 6px);
    position: absolute;
    top: -3px;
    left: -3px;
    display: block;
    box-shadow: 0 0 3px 1px #5fb0fc, 0 0 3px 2px #8ec6fc;
    z-index: 1;
}

.accss-styles-init .text-gray-on-bg-white {
    color: #757575;
}

/* Text blue same color as links */
.accss-styles-init .accss-text-blue-on-bg-white {
    color: #007CAD !important
}

/* bg-color #F4F4F4/  */
.accss-styles-init .accss-text-blue-on-bg-grey {
    color: #016D98 !important
}

/*For generic black button hover override*/
.button-color-override .button-black-hover:hover,
.button-color-override a.btn-default:active,
.accss-styles-init a.btn-black:focus,
.accss-styles-init a.btn-black:hover {
    color: #fff;
    background-color: #333;
    border-color: #333;
}
/* Line height */
.accss-styles-init .accss-line-height-1-2 {
    line-height: 1.2;
}
/* Overflow hidden */
.accss-styles-init .accss-overflow-unset {
    overflow: unset !important;
}
/* width - fit content */
.accss-styles-init .accss-width-fit-content {
    width: fit-content;
}

.accss-styles-init .accss-override-width-content {
    display: inline-block;
    clear: both;
    float: left;
}

.accss-styles-init .accss-border-box {
    box-sizing: border-box;
}

.accss-styles-init .margin-v-19 {
    margin-right: 19px;
    margin-left: 19px;
}

.accss-styles-init .accss-text-transform-none {
    text-transform: none;
}

/* For checkbox */
.accss-styles-init .accss-checkbox-graphical_ctrl-white-bg input:focus ~ .ctrl_element, .accss-styles-init .accss-checkbox-graphical_ctrl-white-bg input:active ~ .ctrl_element {
    outline: none !important;
    box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
}
/* Table cellspacing */
.accss-styles-init .accss-table-pad-space-0 {
    border-collapse: separate;
    border-spacing: 0;
}
/************************************END - Reusable CSS*********************************************/
/*************************************Specific Page/Components Overrides***************************************************/
/*For Unlock Sim Overrides*/
.unlock-sim-override .unlock-sim-link {
    color: #007CAD !important;
}

/*Color contrast override in my profile side menu*/
.vm-container a.menu-tab-head {
    color: #56C0E8 !important;
}

.vm-container a.menu-tab-item {
    color: #016D98 !important;
}

.vm-container .panelContent .darkBlueLink a {
    color: #007CAD !important;
}


/*Color contrast override on button and links myserices mobility overrview*/
.link-color-override a.more-link,
.link-color-override .overview-content a,
.link-color-override .accordion-inner .accordion-toggle {
    color: #007CAD;
}

.button-color-override a.btn-default:hover,
.button-color-override a.btn_Second:hover,
.button-color-override .button-black-hover:active,
.button-color-override a.btn-default:active,
.button-color-override a.btn_Second:active {
    color: #fff;
    background-color: #333;
    border-color: #333;
}


/* Focus outlin form mobility overview */
.accss-styles-init .footer-focus-adjust .border-white > a[href] {
    position: static;
}

    .accss-styles-init .footer-focus-adjust .border-white > a[href]:focus::before {
        box-shadow: 0 0 3px 2px #2794be, 0 0 3px 2px #257fa3;
        height: calc(100% + 6px);
        width: calc(100% + 6px);
        left: -3px;
        top: -3px;
    }

.accss-styles-init .footer-focus-adjust .footer-redeem-text > a[href]:focus::before {
    box-shadow: 0 0 3px 2px #2794be, 0 0 3px 2px #257fa3;
}

/* TV SLICK SLIDE FOCUS OUTLINE */
.accss-styles-init .tv-slick-content-fallback {
    display: inline-block;
    padding: 15px;
}

.accss-searchbar-height-override:active, .accss-searchbar-height-override:focus {
    height: 55px !important;
    top: 4px !important;
}

.accss-icon-height-override {
    margin-top: 21px !important;
}

.accss-searchbarNon-height-override:active, .accss-searchbarNon-height-override:focus {
    height: 48px !important;
    top: 4px !important;
}

.accss-iconNon-height-override {
    margin-top: 18px !important;
}

.accss-styles-init .accss-box-shadow-none {
    box-shadow: none !important;
}

.accss-styles-init .tv-slick-focus-outline {
    margin: 5px !important;
    padding: 0 !important;
}

/* Focus outline overrides on personalization tiles*/
.accss-styles-init .main-outline-override .PersonalizationTilesContainer
.personalization-slider-container .slick-next,
.accss-styles-init .main-outline-override .PersonalizationTilesContainer
.personalization-slider-container .slick-prev {
    position: absolute;
}

.accss-styles-init .main-outline-override .PersonalizationTilesContainer
.personalization-slider-container .slick-arrow:focus {
    box-shadow: none;
}

.accss-styles-init .main-outline-override .PersonalizationTilesContainer
.personalization-slider-container .slick-next:focus::before {
    content: '\e90c';
    height: 100%;
    width: 100%;
    position: relative;
    top: 0;
    left: 0;
    display: inline;
    z-index: 1;
    box-shadow: 0 0 3px 1px #000, 0 0 3px 2px #000;
}

.accss-styles-init .main-outline-override .PersonalizationTilesContainer
.personalization-slider-container .slick-prev:focus::before {
    content: '\e90c';
    transform: rotate(180deg);
    height: auto;
    width: auto;
    position: relative;
    top: 0;
    left: 0;
    display: table-cell;
    z-index: 1;
    box-shadow: 0 0 3px 1px #000, 0 0 3px 2px #000;
}

.accss-styles-init .accss-focus-outline-newline {
    display: block;
    line-height: 1.75;
}

.accss-styles-init .accss-focus-text-footer {
    float: none;
}

.accss-styles-init .accss-focus-icon-footer {
    display: block;
}

.accss-styles-init .accss-focus-icon-social:focus::before {
    height: calc(100% + 36px) !important;
    width: calc(100% + 4px) !important;
    top: -13px !important;
    left: -4px !important;
    border-radius: 50%;
}

.accss-styles-init .accss-focus-icon-social-last:focus::before {
    height: calc(100% + 36px) !important;
    width: calc(100% + 8px) !important;
    top: -13px !important;
    left: -4px !important;
    border-radius: 50%;
}

.accss-styles-init accss-table-links-color-bgwhite-bggray a {
    color: #0d5f7d;
}

.accss-styles-init .accss-focus-overide-height-footer:focus::before {
    height: calc(100% + -7px) !important;
}

.accss-styles-init .accss-focus-icon-social-mobile:focus::before {
    height: calc(100% + 33px) !important;
    width: calc(100% + 0px) !important;
    top: 1px !important;
    left: -2px !important;
    border-radius: 50%;
}

.accss-styles-init .accss-focus-icon-social-mobile-last:focus::before {
    height: calc(100% + 33px) !important;
    width: calc(100% + 0px) !important;
    top: 1px !important;
    left: 0px !important;
    border-radius: 50%;
}

/*.accss-styles-init .main-outline-override .PersonalizationTilesContainer
.personalization-slider-container a[href]:focus {
    outline: none !important;
    box-shadow: 0 0 0px 2px #000, 0 0 2px 2px #000, 0 0 4px 3px #fff, 0 0 0px 4px #fff;
}*/
/*focus override for download and print button */
.accss-styles-init .main-outline-override-v2 button.btn-download,
.accss-styles-init .main-outline-override-v2 button.btn-print {
    position: relative;
    outline: none !important;
}

    .accss-styles-init .main-outline-override-v2 button.btn-download:focus::before,
    .accss-styles-init .main-outline-override-v2 button.btn-print:focus::before {
        content: '';
        height: calc(100% + 6px);
        width: calc(100% + 6px);
        position: absolute;
        top: -3px;
        left: -3px;
        display: block;
        box-shadow: 0 0 3px 1px #000, 0 0 3px 2px #000;
        z-index: 1;
    }

.accss-styles-init .main-outline-override-v2 a[href]:focus,
.accss-styles-init .main-outline-override-v2 button:focus,
.accss-styles-init .main-outline-override-v2 [tabindex="0"]:focus {
    outline: none !important;
    box-shadow: 0 0 0px 2px #000, 0 0 2px 2px #000, 0 0 4px 3px #fff, 0 0 0px 4px #fff;
}


/*ACCSS-1411 - remove the duplicate main tags and sections and style override*/
.accss-styles-init .main-outline-override-v2 .bill_messages .panel-messages > div {
    display: flex;
    margin-top: 15px;
}

.accss-styles-init .main-outline-override-v2 .bill_messages .panel-messages > div {
    padding-bottom: 15px !important;
    margin-bottom: 17px !important;
    border-bottom: 1px solid #d4d4d4 !important;
}

    .accss-styles-init .main-outline-override-v2 .bill_messages .panel-messages > div > .articleHeader {
        margin-bottom: 10px !important;
        padding: 0 12px 0 0 !important;
    }

    .accss-styles-init .main-outline-override-v2 .bill_messages .panel-messages > div > .messageItem {
        padding: 0 !important;
    }

    .accss-styles-init .main-outline-override-v2 .bill_messages .panel-messages > div > .articleHeader,
    .accss-styles-init .main-outline-override-v2 .bill_messages .panel-messages > div > .messageItem {
        line-height: 19px;
    }


.accss-styles-init .main-outline-override-v2 .billOverviewSummary .currentBillColumn,
.accss-styles-init .main-outline-override-v2 .billOverviewSummary .previousBillColumn {
    overflow: visible;
}

.accss-styles-init .main-outline-override-v2 .billOverviewSummary .panel-column:nth-child(even) > .currentBillColumn > ul > li {
    padding-left: 22px;
}

/*ACCSS-1207 - added override for links color contrast usage tiles*/
.accss-styles-init .myUsageVirgin .usage-item .tile-header a.txtLinkUnderline,
.accss-styles-init .mobility-data-usage-card .widSubHead a {
    color: #007CAD !important;
}

/*ACCSS-1120 - added override for links color contrast */
.accss-styles-init .accordion-body .txtVirginBlue {
    color: #007CAD !important;
}

.accss-styles-init .main-outline-override-v2 .bill_messages .accordionArticleMessage {
    display: flex;
    margin-top: 10px;
}


.accss-styles-init .main-outline-override-v2 .bill_messages .accordionArticleMessage {
    padding-bottom: 15px !important;
    margin-bottom: 17px !important;
    border-bottom: 1px solid #d4d4d4 !important;
}

    .accss-styles-init .main-outline-override-v2 .bill_messages .accordionArticleMessage .articleHeader {
        margin-bottom: 10px !important;
        padding: 0 12px 0 0 !important;
    }

    .accss-styles-init .main-outline-override-v2 .bill_messages .accordionArticleMessage .messageItem {
        padding: 0 !important;
    }

    .accss-styles-init .main-outline-override-v2 .bill_messages .accordionArticleMessage .articleHeader,
    .accss-styles-init .main-outline-override-v2 .bill_messages .accordionArticleMessage .messageItem {
        line-height: 19px;
    }



@media screen and (max-width: 999px) {
    .accss-styles-init .main-outline-override-v2 .billOverviewSummary .panel-body .currentBillContainer .bgGrayLight5,
    .accss-styles-init .main-outline-override-v2 .billOverviewSummary .panel-body .previousBillContainer .bgGrayLight5,
    .accss-styles-init .main-outline-override-v2 .billMobileSummary .panel-body .currentBillContainer .bgGrayLight5,
    .accss-styles-init .main-outline-override-v2 .billMobileSummary .panel-body .previousBillContainer .bgGrayLight5 {
        background-color: #ececec !important;
    }

    .accss-styles-init .main-outline-override-v2 .billOverviewSummary .panel-body .currentBillContainer,
    .accss-styles-init .main-outline-override-v2 .billOverviewSummary .panel-body .previousBillContainer,
    .accss-styles-init .main-outline-override-v2 .billMobileSummary .panel-body .currentBillContainer,
    .accss-styles-init .main-outline-override-v2 .billMobileSummary .panel-body .previousBillContainer {
        border-color: #ececec !important;
    }

        .accss-styles-init .main-outline-override-v2 .billOverviewSummary .panel-body .currentBillContainer .currentBillColumn,
        .accss-styles-init .main-outline-override-v2 .billOverviewSummary .panel-body .currentBillContainer .previousBillColumn {
            border: none !important
        }


    .accss-styles-init .billOverviewSummary .panel-body section .bgGray2, .billMobileSummary .panel-body section .bgGray2 {
        background-color: #e2e2e2
    }


    .accss-styles-init .billOverviewSummary .panel-body section header, .accss-styles-init .billOverviewSummary .panel-body section > div, .accss-styles-init .billMobileSummary .panel-body section header, .accss-styles-init .billMobileSummary .panel-body section > div {
        border: none
    }
}


.accss-styles-init .billMobileSummary section > div .aside, .accss-styles-init .billMobileSummary section > div .summary {
    width: 50%
}

.accss-styles-init .billMobileSummary section > div .aside {
    display: -ms-flexbox;
    -ms-flex-pack: initial;
    -ms-flex-align: center;
    display: -moz-box;
    -moz-box-pack: initial;
    -moz-box-align: center;
    display: -webkit-box;
    -webkit-box-pack: initial;
    -webkit-box-align: center;
    display: box;
    box-pack: initial;
    box-align: center;
    padding-left: 20px
}

@media screen and (max-width: 639px) {
    .accss-styles-init .billMobileSummary section > div .aside {
        padding-top: 3px;
        padding-left: 14px;
        width: 56%;
        -webkit-box-align: start;
        -webkit-box-pack: center;
        -webkit-box-orient: vertical
    }
}

.accss-styles-init .billMobileSummary section > div .summary {
    padding: 17px 17px 17px 20px;
    border-color: #d4d4d4
}

    .accss-styles-init .billMobileSummary section > div .summary .icon {
        top: -2px;
        margin-left: 20px
    }

@media screen and (max-width: 639px) {
    .accss-styles-init .billMobileSummary section > div .summary {
        padding: 18px 9px 16px 0;
        width: 33%
    }

        .accss-styles-init .billMobileSummary section > div .summary .icon {
            top: -1px;
            margin-left: 10px
        }
}


.accss-styles-init .billMobileSummary section.detailedBill .taxesRow div.box-container:not(:first-of-type) {
    border-top: none
}

.accss-styles-init .billMobileSummary section.detailedBill .taxesRow div.box-container .aside {
    padding-left: 45px
}

@media screen and (max-width: 639px) {
    .accss-styles-init .billMobileSummary section.detailedBill .taxesRow div.box-container .aside {
        padding-left: 40px
    }
}

.accss-styles-init .billMobileSummary section.detailedBill .taxesRow div.box-container .summary {
    padding-right: 45px
}

@media screen and (max-width: 639px) {
    .accss-styles-init .billMobileSummary section.detailedBill .taxesRow div.box-container .summary {
        padding-right: 30px
    }
}

@media screen and (max-width: 999px) {

    .accss-styles-init .billOverviewSummary .panel-body section header, .accss-styles-init .billOverviewSummary .panel-body section div.box-container, .accss-styles-init
    .billMobileSummary .panel-body section header, .accss-styles-init .billMobileSummary .panel-body section div.box-container {
        border: none
    }

    .accss-styles-init .billOverviewSummary .panel-body section > div.box-container .summary, .accss-styles-init .billMobileSummary .panel-body section > dev .summary {
        border-color: #fff
    }
}

@media screen and (max-width:600px) {
    /*JBT media*/
    .accss-styles-init a.outline-header-back, .accss-styles-init a.outline-header-back-payinfo {
        width: 25px;
    }
}
/*1220 - Calendar datepicker accessibility css overrides*/
.accss-styles-init.pfu-style .product_usage_details #timePeriod_from_date.bg-white,
.accss-styles-init.pfu-style .product_usage_details #timePeriod_to_date.bg-white {
    background: #fff;
}

.accss-styles-init.pfu-style .ui-datepicker-trigger {
    margin-left: -23px;
    padding: 0 2px 2px 2px;
}

    .accss-styles-init.pfu-style .ui-datepicker-trigger:focus, .accss-styles-init.pfu-style .ui-datepicker-trigger:active,
    .accss-styles-init.pfu-style .ui-datepicker .ui-datepicker-prev:focus, .accss-styles-init.pfu-style .ui-datepicker .ui-datepicker-prev:active,
    .accss-styles-init.pfu-style .ui-datepicker .ui-datepicker-next:focus, .accss-styles-init.pfu-style .ui-datepicker .ui-datepicker-next:active {
        outline: none !important;
        box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
    }

.accss-styles-init.pfu-style .ui-widget-header .ui-datepicker-prev > .ui-icon, .accss-styles-init.pfu-style .ui-widget-header .ui-datepicker-next > .ui-icon {
    background-image: url(../../web/common/all_languages/all_regions/skin/ui-images/ui-icons_222222_256x240.png);
}

.accss-styles-init.pfu-style .ui-widget-content .ui-state-highlight:focus, .accss-styles-init.pfu-style .ui-widget-content .ui-state-highlight:active {
    outline: none !important;
    box-shadow: 0 0 0px 2px #257fa3, 0 0 0px 2px #257fa3;
}

.accss-styles-init.pfu-style #downloadCsv:focus, .accss-styles-init.pfu-style #downloadCsv:active {
    outline: none !important;
    box-shadow: 0 0 0px 2px #fff, 0 0 0px 2px #fff, 0 0 3px 4px #2A9DCB, 0 0 3px 4px #2A9DCB;
    text-decoration: none;
}

/* Mobility Overview Underline */
/*.accss-styles-init .myaccount-mobility-overview .show-hide-toggle {
    text-decoration: none;
}

.accss-styles-init .myaccount-mobility-overview .show-hide-toggle:hover span:not(.icon-plus-solid),
.accss-styles-init .myaccount-mobility-overview .show-hide-toggle:focus span:not(.icon-plus-solid) {
    text-decoration: underline;
}

.accss-styles-init .myaccount-mobility-overview .icon.more-link:hover span, .accss-styles-init .myaccount-mobility-overview .icon.more-link:focus span,
.accss-styles-init .myaccount-mobility-overview .icon.more-link:hover, .accss-styles-init .myaccount-mobility-overview .icon.more-link:focus {
    text-decoration: none;
}

.accss-styles-init .myaccount-mobility-overview .prepaid-my-device-links li a:hover, .accss-styles-init .myaccount-mobility-overview .prepaid-my-device-links li a:focus,
.accss-styles-init .myaccount-mobility-overview .prepaid-my-device-links li:hover, .accss-styles-init .myaccount-mobility-overview .prepaid-my-device-links li:focus {
    text-decoration: none;
}

.accss-styles-init .footer-redeem-text a:hover, .accss-styles-init .footer-redeem-text a:focus {
    text-decoration: none;
}

.accss-styles-init .mobility-unbilled-usage-card .table-display ul li span.txtUnderline:hover,
.accss-styles-init .mobility-unbilled-usage-card .table-display ul li span.txtUnderline:focus {
    text-decoration: none;
}

.accss-styles-init #mob-overview-plan-addons .offer-zone .personalizedOfferContainer .media .media-body a.txtUnderline:hover,
.accss-styles-init #mob-overview-plan-addons .offer-zone .personalizedOfferContainer .media .media-body a.txtUnderline:focus {
    text-decoration: none;
}

.accss-styles-init .personalization-tile-cta-container > a.txtUnderline:hover,
.accss-styles-init .personalization-tile-cta-container > a.txtUnderline:focus {
    text-decoration: none;
}

.accss-styles-init .personalization-tile-content-container .personalization-tile-details-container .personalization-details-link.txtUnderline:hover,
.accss-styles-init .personalization-tile-content-container .personalization-tile-details-container .personalization-details-link.txtUnderline:focus {
    text-decoration: none;
}*/

/* MyPlan Underline */
/*.accss-styles-init #myaccount-mobility-service a[id="Pending Request_1"]:hover,
.accss-styles-init #myaccount-mobility-service a[id="Pending Request_1"]:focus {
    text-decoration: none;
}*/

.accss-styles-init #mob-overview-plan-addons .icon-info-solid.txtVirginBlue.txtSize14.tooltip-interactive,
.accss-styles-init #mob-overview-plan-addons .icon-minus-solid.txtVirginBlue.txtSize16,
.accss-styles-init #mob-overview-plan-addons .icon-plus-solid.txtVirginBlue.txtSize16 {
    top: -1px;
}

.accss-styles-init #mob-overview-plan-addons .icon-info-solid.txtVirginBlue.txtSize20 {
    top: 0px;
}

.accss-styles-init .mobility-usage-overview .icon_info_Overview {
    margin-bottom: 3px;
    margin-top: 3px;
}

.accss-styles-init #mob-overview-device .flex.list-unstyled .icon-checkmark-circled.bannerCheckmarkOverview {
    position: relative;
    top: -2px;
}

.accss-styles-init #mob-overview-plan-addons .personalizedOfferContainer .text-tag.txtCapital.txtWhite.txtBold {
    position: relative;
    top: -2px;
}

.accss-styles-init #mob-overview-plan-addons .txtBlack-3.table-container .promo-label-sm {
    position: relative;
    top: -1px;
}

/* Mobility Plans and Features */
.accss-styles-init .accss-link-override .prepaid-my-device-links li {
    text-decoration: none;
}

/* Post Prod bug 174056 fix */
.accss-styles-init #recoveryConfirmation .close {
    margin-top: 0;
    margin-right: 0;
}

/* graphical_ctrl bug 168375 fix */
.accss-styles-init .accss-graphical-ctrl {
    display: inline-block;
    margin-bottom: 5px;
}

/* Bug 184650 fix */
@media screen and (max-width: 999px) {
    .accss-styles-init #recoveryConfirmation .modal-dialog.modal-md {
        vertical-align: middle;
        width: auto;
    }
}

/* Bug 184650 fix */
@media (max-width: 767px) and (min-width: 320px) {
    .accss-styles-init .modal .modal-md .close {
        margin: auto !important;
    }
}

/*ACCSS 2380 2381  */
@media screen and (max-width: 1248px) {
    .accss-styles-init .accss-custom-skip-to-main {
        left: -441px;
    }

        .accss-styles-init .accss-custom-skip-to-main:focus {
            margin-left: 88px;
            position: static;
            float: left
        }
}

@media screen and (min-width: 1248px) {
    .accss-styles-init .accss-custom-skip-to-main1 {
        left: -441px;
    }

        .accss-styles-init .accss-custom-skip-to-main1:focus {
            margin-left: 88px;
            position: static;
            float: left
        }
}
/*ACCSS 2380 2381 */

.accss-styles-init .accss-footer-color {
    color: #999;
}
/*ACCSS-2624*/
.ico-shrink div > span.accss-styles-init {
    margin-right: 3px;
    margin-top: 2px;
}

/* ACCSS-2619 */
.accss-styles-init #overview-base-parent .accordion-heading h2.virginUltraReg.txtBlack2.no-margin {
    text-transform: none;
    line-height: 1.2;
    font-size: 22px;
}

/*ACCSS-2833*/
.accss-sim-modal-title {
    font-size: 20px !important;
    margin-top: 5px;
    margin-bottom: 5px;
    padding-right: 20px;
    text-transform: none;
}

/**ACCSS-1214 Internet / TV Footer **/
.accss-styles-init Btxt-bgGray {
    text-decoration: none;
    color: #0d5f7d;
}

/* ACCSS-2619 */
.upgrade-plan-cards .inlineBlock.pad-30-left.no-pad-xs.content-width.valign-top h2.txtBlack2.virginUltraReg {
    text-transform: none;
    line-height: 1.2;
    font-size: 22px;
}

/*TFS 187393*/
.product_overview.product_section.clear.accss-styles-init > div > div.floatL > a {
    color: #007CAD !important;
    text-decoration: underline;
}

.product_overview.product_section.clear.accss-styles-init > div > div.floatR > a {
    color: #007CAD !important;
    text-decoration: underline;
}

.product_overview.product_section.clear.accss-styles-init > div > div.floatL > a:hover {
    text-decoration: none;
}

.product_overview.product_section.clear.accss-styles-init > div > div.floatR > a:hover {
    text-decoration: none;
}

/* ACCSS-2864 */
.accss-styles-init .footerVM.bgVirginGrayDark.col1.hidden-print.footer-outline-override.accss-main-footer > .skip-to-main-link:focus {
    text-transform: initial;
    transform: inherit;
    top: auto;
}

/* ACCSS-2878 */
.accss-styles-init .accss-link-override .package-desc li span[tabindex="0"] {
    text-decoration: underline;
    color: #007CAD !important;
}

    .accss-styles-init .accss-link-override .package-desc li span[tabindex="0"]:hover,
    .accss-styles-init .accss-link-override .package-desc li span[tabindex="0"]:focus {
        text-decoration: none;
        color: #0d5f7d;
    }

/* ACCSS-2868 */
.accss-styles-init #myaccount-change-feature .wss-slick-slider-data.col-eq .slick-track .graphical_ctrl.ctrl_radioBtn .ctrl_element.data-addon-border {
    outline: none;
    left: 0px;
    top: -3px;
}

/* ACCSS-2869 */
.accss-styles-init span.myaccount-feature-selection #tab1 .sr-only.radio-slick-description {
    display: none;
}

.accss-styles-init span:not(.myaccount-feature-selection) #tab1 #radio-slick-legend {
    display: none;
}

.accss-styles-init .tab-content.accss-link-override .accordionContent a[href]:not(.accss-link-icon):not(.txtNoUnderline):not(.btn):not([role=button]),
.accss-styles-init .tab-content.accss-link-override .accordionContent a[href] span:not(.accss-link-icon):not(.txtNoUnderline):not(.btn):not([role=button]) {
    text-decoration: underline;
    color: #007CAD;
}

/* ACCSS-1043 */
.pfu-style #footer .contents {
    overflow: visible !important;
}

.pfu-style #footer ul.bottom-links {
    overflow: visible !important;
}
/* END - ACCSS-1043 */

/* Internet ChangePlan */
.accss-styles-init .accss-info-text-header {
    margin-bottom: .5rem;
    margin-top: 0;
    line-height: 1.2;
}

.accss-styles-init .accss-changeplan-preview {
    color: #fff;
    text-decoration: underline;
}

    .accss-styles-init .accss-changeplan-preview:focus,
    .accss-styles-init .accss-changeplan-preview:hover {
        color: #007CAD;
        text-decoration: none;
    }

/* ACCSS-3179 */
.accss-styles-init.virgin .offer-zone .nba .slickSlide .margin-l-15 a, /*Focus Outline - Offers (Learn more / Get this offer)*/
.accss-styles-init.virgin .LandingPageOffers.virign .offer-zone .nba .recommendation-item.row.offer-wrapper a.txtUnderline.margin-t-5.block.links-blue-on-bg-white /*Focus Outline - Selected Offer (Learn more)*/ {
    display: inline-block;
    clear: both;
    float: left;
}

.accss-styles-init.virgin .mobility-hug-select-device-plan .conatiner .mobility-hug-device-list .liquid-container.theme-virgin .row .tablist a:focus, /*Focus Outline (Phones / Base Phones / Tablets)*/
.accss-styles-init.virgin .prod-filter.theme-virgin .container .prod-filter-brands button:focus, /*Focus Outline (All Brands / Samsung / Apple / Google / More) */
.accss-styles-init.virgin .row.device-showcase-wrap-row .options-and-pricing-wrap .options-and-pricing .specs .links a:focus /*Focus Outline (Device details / Change device) */ {
    outline: 0 !important;
    box-shadow: 0 !important;
}

    /*Focus Outline (Phones / Base Phones / Tablets)*/
    .accss-styles-init.virgin .mobility-hug-select-device-plan .conatiner .mobility-hug-device-list .liquid-container.theme-virgin .row .tablist a:focus span {
        outline: none !important;
        box-shadow: 0 0 0px 3px #f4f4f4, 0 0 2px 3px #f4f4f4, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3 !important;
    }

/*Footer Links*/
.accss-styles-init .virgin-footer .footer-links a {
    padding: 0 !important;
    margin: 0 15px;
}

.accss-styles-init .bell-search-field-button #SEARCH_ICON:focus .volt-icon, .accss-styles-init .bell-search-field-button #SEARCH_ICON:active .volt-icon {
    outline: none !important;
    box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3 !important;
}

.accss-styles-init.virgin .prod-filter.theme-virgin .container .prod-filter-brands button:focus span, /*Focus Outline (All Brands / Samsung / Apple / Google / More) */
.accss-styles-init.virgin .row.device-showcase-wrap-row .options-and-pricing-wrap .options-and-pricing .specs .links a:focus span /*Focus Outline (Device details / Change device) */ {
    outline: none !important;
    box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3 !important;
}
/* specific for Paybill PreAuthorize JBT Start*/
a.focus-outline-cancel-link:focus {
    padding-right: 2px;
    margin-right: 28px;
}

a.focus-outline-cancel-link, .nontext-decoration:focus {
    text-decoration: none !important;
}
/* specific for Paybill PreAuthorize JBT End*/

/* END - ACCSS-3179 */
/* ACCSS-2892 */
.accss-styles-init #footerLogout:hover {
    color: #bf1a18;
    background-color: transparent;
    border-color: #bf1a18;
}

.accss-styles-init #APP_LOGOUT_CLOSE:hover, #APP_LOGOUT_CLOSE:focus,
#APP_EXIT_CONTINUE:hover, #APP_EXIT_CONTINUE:focus {
    color: #000;
    background-color: #999;
    border-color: #999;
}
/* END - ACCSS-2892 */

/* ACCSS-3213 */
.accss-styles-init .accss-members-banner a {
    display: inline-block;
}
/* END - ACCSS-3213 */

/* ACCSS-3191 / ACCSS-3201 / ACCSS-3209 */
.accss-styles-init#virgin-profile-bg-color .virginuser-profile-manager #myProfileSideNav .tabs.tabs_vertical a.menu-tab-head {
    padding: 0 !important;
    margin: 10px 0 10px 0;
}

/*---------TV CHANNEL LINEUP td LINKS--------------*/
.accss-styles-init #my-channel-lineup #channelList td a {
    color: #0d5f7d;
}

/*ACCSS-3196*/
.accss-styles-init .customerdigitalpin .tooltip-interactive::before {
    margin: 0 !important;
}

/*TFS 195087*/
.accss-styles-init.virgin-MyAccount-BG a.txtVirginBlue.link-nounderlive-hover span.icon-edit-small.accss-edit-override:before {
    content: "\e945" !important;
}

/* ACCSS-3195 and ACCSS-3198 */
.accss-styles-init.virgin div.tooltip.right.accss-tooltip {
    margin-left: 25px !important;
}

/* ACCVIRDCX-131 */
/* ACCVIRDCX-131 */
#myaccount-mobility-unlocksim .unlock-sim-override .unlock-sim-link:focus::before,
#maincontent .container.liquid-container #btnBack:focus::before {
    content: "";
    box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
    height: calc(100% + 6px);
    width: calc(100% + 6px);
    position: absolute;
    display: block;
    z-index: 1;
    left: -3px;
    top: -3px;
}

#maincontent .container.liquid-container #btnBack:focus::before {
    height: calc(100% + 8px);
}

#myaccount-mobility-unlocksim .unlock-sim-override .unlock-sim-link:focus,
#maincontent .container.liquid-container #btnBack:focus {
    position: relative;
    outline: none;
}

#myaccount-mobility-unlocksim .unlock-sim-override .unlock-sim-link:focus,
#myaccount-mobility-unlocksim .unlock-sim-override .unlock-sim-link:hover {
    text-decoration: none;
}

#myaccount-mobility-unlocksim .unlock-sim-override .btn.btn-primary:focus {
    box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
}

#myaccount-mobility-unlocksim .unlock-sim-override .btn.btn-primary {
    outline: none;
}

/* ACCVIRDCX-129 */
#accountInfo #changeRatePlan a.btn-generic:focus {
    box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
    border-radius: 4px;
}

#accountInfo #reviewChanges .note a:focus {
    box-shadow: 0 0 0px 3px #feffe5, 0 0 2px 3px #feffe5, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
}

#accountInfo #reviewChanges #terms-details a:focus,
#accountInfo #changeRatePlan #agreeTerms:focus {
    box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
    outline: none !important;
}

#accountInfo #pageTabs ul li a:focus {
    box-shadow: 0 0 0px 3px #f2f2f2, 0 0 2px 3px #f2f2f2, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
}

#accountInfo #changeRatePlan .error-preauthor-payment a:focus,
#accountInfo #T911error a:focus {
    box-shadow: 0 0 0px 3px #fefde6, 0 0 2px 3px #fefde6, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
}

#accountInfo #changeRatePlan a.btn-generic span,
#accountInfo #reviewChanges #terms-details a,
#accountInfo #changeRatePlan a.btn-generic,
#accountInfo #reviewChanges .note a {
    outline: none !important;
}

#master-container #footer .bottom-links a:focus {
    box-shadow: 0 0 0px 3px #000, 0 0 2px 3px #000, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
}

#master-container #footer .bottom-links a {
    position: relative;
    left: 6px;
}

/* ACCVIRDCX-128 */
.myaccount-mobility-device .upgrade-my-device .icon.more-link:focus::before,
.myaccount-mobility-device a#VIEW_MY_AGREEMENT:focus::before,
.myaccount-mobility-device .device-content a:focus::before {
    content: "";
    box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
    height: calc(100% + 6px);
    width: calc(100% + 6px);
    position: absolute;
    display: block;
    z-index: 1;
    left: -3px;
    top: -3px;
}

.myaccount-mobility-device .upgrade-my-device .icon.more-link:focus,
.myaccount-mobility-device .upgrade-my-device .icon.more-link:hover,
.myaccount-mobility-device a#VIEW_MY_AGREEMENT:focus,
.myaccount-mobility-device a#VIEW_MY_AGREEMENT:hover,
.myaccount-mobility-device .device-content a:focus,
.myaccount-mobility-device .device-content a:hover {
    text-decoration: none;
    outline: none;
}

.myaccount-mobility-device a#VIEW_MY_AGREEMENT,
.myaccount-mobility-device .device-content a {
    position: relative;
}

.myaccount-mobility-device .upgrade-my-device .icon.more-link {
    padding-right: 0px;
}

.myaccount-mobility-device a.btn.btn-primary:focus {
    box-shadow: 0 0 0px 3px #f4f4f4, 0 0 2px 3px #f4f4f4, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
    outline: none;
}

/* ACCVIRDCX-127 */
#myaccount-change-feature .myaccount-feature-selection .container.liquid-container.basic-container .same-H-container-2 input#topNavSearch:focus,
#myaccount-change-feature .fixed-footer.viewport-notify .btn.btn-primary:focus {
    box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
    outline: none;
}

#myaccount-change-feature .myaccount-feature-selection .container.liquid-container.basic-container .same-H-container-2 a:focus::before,
#myaccount-change-feature .fixed-footer.viewport-notify a:focus::before {
    content: "";
    box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
    height: calc(100% + 6px);
    width: calc(100% + 6px);
    position: absolute;
    display: block;
    z-index: 1;
    left: -3px;
    top: -3px;
}

#myaccount-change-feature .fixed-footer.viewport-notify a#cance1_2 {
    padding-right: 0px;
    margin-right: 30px;
}

#myaccount-change-feature .myaccount-feature-selection .container.liquid-container.basic-container .same-H-container-2 li {
    text-decoration: none;
}

#myaccount-change-feature .myaccount-feature-selection .container.liquid-container.basic-container .same-H-container-2 a,
#myaccount-change-feature .fixed-footer.viewport-notify a {
    position: relative;
    outline: none;
}

    #myaccount-change-feature .myaccount-feature-selection .container.liquid-container.basic-container .same-H-container-2 a:focus,
    #myaccount-change-feature .myaccount-feature-selection .container.liquid-container.basic-container .same-H-container-2 a:hover {
        text-decoration: none;
        outline: none;
    }

/* ACCVIRDCX-125 */
.liquid-container.same-H-service .bgWhite.box-border-gray-light.child-service .btn.btn-primary:focus {
    box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
}

    .liquid-container.same-H-service .bgWhite.box-border-gray-light.child-service .btn.btn-primary:focus:before {
        box-shadow: none;
    }

.header-outline-override .skip-to-main-link {
    color: #fff;
}

/* ACCVIRDCX-119 */
#unblockdata-notification .btn.btn-default:focus,
#unblockdata-notification .btn.btn-primary:focus {
    box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
    outline: none;
}

#hide-payment-detail-xs:focus span, #payment-detail-xs:focus span,
#hide-payment-detail-xs:focus, #payment-detail-xs:focus,
#hide-payment-detail-xs:hover span, #payment-detail-xs:hover span,
#hide-payment-detail-xs:hover, #payment-detail-xs:hover {
    text-decoration: none;
}

.container.liquid-container .bill-component .payment-detail a,
.container.liquid-container .bill-component .trigger-popup a {
    position: relative;
}

    .container.liquid-container .bill-component .payment-detail a:focus::before,
    .container.liquid-container .bill-component .trigger-popup a:focus::before {
        content: "";
        box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
        height: calc(100% + 6px);
        width: calc(100% + 6px);
        position: absolute;
        display: block;
        z-index: 1;
        left: -3px;
        top: -3px;
    }

    .container.liquid-container .bill-component #payment-detail-xs:focus,
    .container.liquid-container .bill-component .payment-detail a:focus,
    .container.liquid-container .bill-component .payment-detail a:hover,
    .container.liquid-container .bill-component .trigger-popup a:focus,
    .container.liquid-container .bill-component .trigger-popup a:hover {
        text-decoration: none;
        outline: none;
    }

        .container.liquid-container .bill-component .trigger-popup a:focus span,
        .container.liquid-container .bill-component .trigger-popup a:hover span,
        .container.liquid-container .bill-component #payment-detail-xs:focus span {
            text-decoration: none;
        }

.container.liquid-container .bill-component #payMyBill:focus,
.container.liquid-container .bill-component #seeCurrentBill:focus {
    box-shadow: 0 0 0px 3px #333, 0 0 2px 3px #333, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
    outline: none;
}

.myaccount-mobility-overview .mobility-unbilled-usage-card .table-display ul li span.txtRed.txtUnderline:focus::before,
.myaccount-mobility-overview .mobility-data-usage-card #notificationBox_sm_learnMore:focus span.txtUnderline::before,
.myaccount-mobility-overview .mobility-data-usage-card .donutGraphFull .LMDonut_Graph_overview:focus::before,
.myaccount-mobility-overview .mobility-unbilled-usage-card .table-display a.icon.more-link:focus::before,
.myaccount-mobility-overview .mobility-data-usage-card .product-img-wrap .widSubHead a:focus::before,
.myaccount-mobility-overview #mob-overview-plan-addons #btnViewDetails_SO_MP1:focus::before,
.myaccount-mobility-overview .footer-focus-adjust .footer-redeem-text > a:focus::before,
.myaccount-mobility-overview #EDIT_MOBILITY_DATA_BLOCK_SETTINGS_DESKTOP:focus::before,
.myaccount-mobility-overview .link-underline-override.icon.more-link:focus::before,
.myaccount-mobility-overview .footer-focus-adjust .border-white > a:focus::before,
.myaccount-mobility-overview #mob-overview-device a:not(.btn):focus::before,
.myaccount-mobility-overview #viewBillDetails_SO_TCC1:focus::before {
    content: "";
    box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
    height: calc(100% + 6px);
    width: calc(100% + 6px);
    position: absolute;
    display: block;
    z-index: 1;
    left: -3px;
    top: -3px;
}

#mob-overview-plan-addons .custom-nested-accordion .accordion-toggle .icon-plus-solid {
    padding-right: 0px;
}

.myaccount-mobility-overview a.link-underline-override.icon.more-link {
    padding-right: 0px;
    margin-right: 5px;
}

.myaccount-mobility-overview .mobility-data-usage-card #notificationBox_sm_learnMore span.txtUnderline {
    position: relative;
}

.myaccount-mobility-overview .mobility-unbilled-usage-card .table-display ul li span.txtRed.txtUnderline,
.myaccount-mobility-overview .mobility-data-usage-card #notificationBox_sm_learnMore {
    position: relative;
    outline: none;
}

.myaccount-mobility-overview .link-underline-override.icon.more-link:focus::before,
.myaccount-mobility-overview #viewBillDetails_SO_TCC1:focus::before {
    outline: none;
}

.myaccount-mobility-overview .mobility-unbilled-usage-card .table-display ul li span.txtRed.txtUnderline:focus,
.myaccount-mobility-overview .mobility-unbilled-usage-card .table-display ul li span.txtRed.txtUnderline:hover,
.myaccount-mobility-overview .mobility-data-usage-card #notificationBox_sm_learnMore:focus span.txtUnderline,
.myaccount-mobility-overview .mobility-data-usage-card #notificationBox_sm_learnMore:hover span.txtUnderline,
.myaccount-mobility-overview .mobility-unbilled-usage-card .table-display a.icon.more-link:focus,
.myaccount-mobility-overview .mobility-unbilled-usage-card .table-display a.icon.more-link:hover,
.myaccount-mobility-overview #mob-overview-plan-addons #btnViewDetails_SO_MP1:focus,
.myaccount-mobility-overview #mob-overview-plan-addons #btnViewDetails_SO_MP1:hover,
.myaccount-mobility-overview .footer-focus-adjust .footer-redeem-text > a:focus,
.myaccount-mobility-overview .footer-focus-adjust .footer-redeem-text > a:hover,
.myaccount-mobility-overview #EDIT_MOBILITY_DATA_BLOCK_SETTINGS_DESKTOP:focus,
.myaccount-mobility-overview #EDIT_MOBILITY_DATA_BLOCK_SETTINGS_DESKTOP:hover,
.myaccount-mobility-overview .link-underline-override.icon.more-link:focus,
.myaccount-mobility-overview .link-underline-override.icon.more-link:hover,
.myaccount-mobility-overview #mob-overview-device a:not(.btn):focus,
.myaccount-mobility-overview #mob-overview-device a:not(.btn):hover,
.myaccount-mobility-overview #viewBillDetails_SO_TCC1:focus,
.myaccount-mobility-overview #viewBillDetails_SO_TCC1:hover {
    text-decoration: none;
    outline: none;
}

.myaccount-mobility-overview #mob-overview-device .notificationContent .icon.more-link,
.myaccount-mobility-overview #viewBillDetails_SO_TCC1 {
    padding-right: 0px;
}

.myaccount-mobility-overview #mob-overview-device .prepaid-my-device-links li > a > span,
.myaccount-mobility-overview #mob-overview-device .prepaid-my-device-links li {
    text-decoration: none;
}

    .myaccount-mobility-overview #mob-overview-device .prepaid-my-device-links li > a,
    .myaccount-mobility-overview #mob-overview-plan-addons #btnViewDetails_SO_MP1,
    .myaccount-mobility-overview .footer-focus-adjust .footer-redeem-text > a {
        position: relative;
    }

.myaccount-mobility-overview .mobility-data-usage-card #add_data_flex_overage:focus,
.myaccount-mobility-overview #Upgrade_Device_SO_STA1.btn.btn-primary:focus {
    box-shadow: 0 0 0px 3px #f4f4f4, 0 0 2px 3px #f4f4f4, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
    outline: none;
}

.myaccount-mobility-overview .btn.btn-default:focus,
.myaccount-mobility-overview .btn.btn-primary:focus,
#mob-overview-plan-addons .icon-info-solid:focus {
    box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
    outline: none;
}

.myaccount-mobility-overview .footer-focus-adjust .border-white > a > img,
.myaccount-mobility-overview .footer-focus-adjust .border-white > a {
    outline: none;
}

/* ACCVIRDCX-105 */
.myaccount-mobility-service .upgrade-plan-cards .btn.btn-primary:focus {
    box-shadow: 0 0 0px 3px #f4f4f4, 0 0 2px 3px #f4f4f4, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
    outline: none;
}

.myaccount-mobility-service #my-service a:focus::before {
    content: "";
    box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
    height: calc(100% + 6px);
    width: calc(100% + 6px);
    position: absolute;
    display: block;
    z-index: 1;
    left: -3px;
    top: -3px;
}

.myaccount-mobility-service #my-service a {
    position: relative;
    outline: none;
}

#myaccount-mobility-service #overview-base-parent .icon-info-solid.tooltip-interactive:focus::after {
    content: "";
    box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
    height: calc(100% + 6px);
    width: calc(100% + 6px);
    position: absolute;
    display: block;
    z-index: 1;
    left: -3px;
    top: -3px;
}

#myaccount-mobility-service #overview-base-parent .icon-info-solid.tooltip-interactive {
    outline: none;
}

/* ACCVIRDCX-101 */
#myaccount-change-feature .vsteps-views .icon-o.icon-circle_solid:focus {
    box-shadow: 0 0 0px 3px #cc0000, 0 0 2px 3px #cc0000, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
    outline: none;
}

#myaccount-change-feature .accordion-group .accordion-body .current-addon:focus + .ctrl_element.chk_radius {
    box-shadow: 0 0 0px 3px #333, 0 0 2px 3px #333, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
    outline: none;
}

.modal-dialog #cf_el_Logout_header:focus > span,
.modal-dialog #cf_el_Logout_header > span:focus {
    box-shadow: 0 0 0px 3px #e1e1e1, 0 0 2px 3px #e1e1e1, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
    outline: none;
}

.modal-dialog #cf_el_Logout_header {
    box-shadow: none !important;
}

.modal-dialog .btn.btn-primary:focus,
.modal-dialog .btn.btn-default:focus {
    box-shadow: 0 0 0px 3px #f4f4f4, 0 0 2px 3px #f4f4f4, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
    outline: none;
}

#myaccount-change-feature .tab_container_accordion .accordionContent.accss-all-links-override a.txtUnderline:not([data-toggle="modal"]):focus::before,
#myaccount-change-feature .tab_container_accordion .accordionContent.accss-all-links-override a.txtUnderline[data-toggle="modal"]:focus span::before,
#myaccount-change-feature .accordion-group.bgBlackLight-2 .accordion-tog-1.accordPanel-1:focus::before,
#myaccount-change-feature .myaccount-feature-selection .accordionTab li .tabs_span a:focus::before {
    content: "";
    box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
    height: calc(100% + 6px);
    width: calc(100% + 6px);
    position: absolute;
    display: block;
    z-index: 1;
    left: -3px;
    top: -3px;
}

#myaccount-change-feature .tab_container_accordion .accordionContent.accss-all-links-override .prepaid-my-device-links a:focus span,
#myaccount-change-feature .tab_container_accordion .accordionContent.accss-all-links-override .prepaid-my-device-links a:hover span,
#myaccount-change-feature .tab_container_accordion .accordionContent.accss-all-links-override a.txtUnderline:focus span,
#myaccount-change-feature .tab_container_accordion .accordionContent.accss-all-links-override a.txtUnderline:hover span,
#myaccount-change-feature .tab_container_accordion .accordionContent.accss-all-links-override a.txtUnderline:focus,
#myaccount-change-feature .tab_container_accordion .accordionContent.accss-all-links-override a.txtUnderline:hover {
    text-decoration: none;
}

#myaccount-change-feature .tab_container_accordion .accordionContent.accss-all-links-override a.txtUnderline[data-toggle="modal"] span,
#myaccount-change-feature .tab_container_accordion .accordionContent.accss-all-links-override a.txtUnderline,
#myaccount-change-feature .myaccount-feature-selection .accordionTab li .tabs_span {
    position: relative;
}

    #myaccount-change-feature .tab_container_accordion .accordionContent.accss-all-links-override a.txtUnderline[data-toggle="modal"] span,
    #myaccount-change-feature .tab_container_accordion .accordionContent.accss-all-links-override a.txtUnderline,
    #myaccount-change-feature .accordion-group.bgBlackLight-2 .accordion-tog-1.accordPanel-1,
    #myaccount-change-feature .myaccount-feature-selection .accordionTab li .tabs_span a {
        outline: none;
    }

#myaccount-change-feature .tab_container_accordion .accordionContent.accss-all-links-override .graphical_ctrl_checkbox input[type="checkbox"]:focus + .ctrl_element.chk_radius,
#myaccount-change-feature .tab_container_accordion .accordionContent.accss-all-links-override .slick-slider .col-box .data-feature:focus + .ctrl_element.pointer,
#myaccount-change-feature .tab_container_accordion .accordionContent.accss-all-links-override .slick-slider .slick-arrow:focus {
    box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
    outline: none;
}

/* Bill History/Comparison Focus Outline - Print this Page */
.accss-styles-init .mybill-print-this-page:focus, .accss-styles-init .mybill-print-this-page:active,
.accss-styles-init .mybill-print-this-page:focus, .accss-styles-init .mybill-print-this-page:active {
    position: absolute;
    left: 454px;
    width: 128px;
    height: 23px;
    padding-top: 0px;
    top: 18px;
    border-radius: 0px;
}

    .accss-styles-init .mybill-print-this-page:focus .icon-print, .accss-styles-init .mybill-print-this-page:active .icon-print,
    .accss-styles-init .mybill-print-this-page:focus .icon-print, .accss-styles-init .mybill-print-this-page:active .icon-print {
        position: absolute;
        top: 4px;
    }

/* Bill History Focus Outline - Table Header */
.accss-styles-init #billHistoryTable th:focus, .accss-styles-init #billHistoryTable th:active {
    outline: none !important;
    box-shadow: none !important;
}

.accss-styles-init .bill-history-table #ShowMessageIcon:focus span:not(.carret), .accss-styles-init .bill-history-table #ShowMessageIcon:active span:not(.carret) {
    width: 20px;
    margin: auto;
}

.accss-styles-init .bill-history-table th.sortedDesc:focus span:not(.carret), .accss-styles-init .bill-history-table th.sortedDesc:active span:not(.carret),
.accss-styles-init .bill-history-table th.sortedAsc:focus span:not(.carret), .accss-styles-init .bill-history-table th.sortedAsc:active span:not(.carret) {
    outline: none !important;
    box-shadow: 0 0 0px 3px #2d2d2d, 0 0 2px 3px #2d2d2d, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3 !important;
    position: absolute;
    top: 34%;
    width: 83%;
}

.accss-styles-init .bill-history-table th:focus span:not(.carret), .accss-styles-init .bill-history-table th:active span:not(.carret) {
    outline: none !important;
    box-shadow: 0 0 0px 3px #cc0000, 0 0 2px 3px #cc0000, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3 !important;
}

.accss-styles-init .bill-history-table #ShowMessageIcon.sortedDesc:focus span:not(.carret), .accss-styles-init .bill-history-table #ShowMessageIcon.sortedDesc:active span:not(.carret),
.accss-styles-init .bill-history-table #ShowMessageIcon.sortedAsc:focus span:not(.carret), .accss-styles-init .bill-history-table #ShowMessageIcon.sortedAsc:active span:not(.carret) {
    position: absolute !important;
    padding-right: 43% !important;
    top: 43% !important;
    left: 47% !important;
}

/* Bill Comparison Focus Outline - View bill link */
.accss-styles-init .bill-comparison-table #footerRow a:focus, .accss-styles-init .bill-comparison-table #footerRow a:active {
    outline: none !important;
    box-shadow: none !important;
}

    .accss-styles-init .bill-comparison-table #footerRow a:focus span, .accss-styles-init .bill-comparison-table #footerRow a:active span {
        outline: none !important;
        box-shadow: 0 0 0px 3px #222, 0 0 2px 3px #222, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3 !important;
        position: relative;
        width: 72px;
        padding: 2px 25px 3px 0px;
        left: 25px;
    }

/* Bill Comparison Focus Outline - Table Rows with Icon */
.accss-styles-init .bill-comparison-table a[role="button"] {
    position: relative;
    padding: 2px 2px 3px 40px;
    right: 40px;
}

.accss-styles-init .bill-comparison-table #dataRow .icon {
    padding-bottom: 5px;
}

/* Bill Comparison Focus Outline - Table Rows Content */
.accss-styles-init .bill-comparison-table #dataRow div:focus, .accss-styles-init .bill-comparison-table #dataRow div:active {
    outline: none !important;
    box-shadow: none !important;
}

    .accss-styles-init .bill-comparison-table #dataRow div:focus .block span, .accss-styles-init .bill-comparison-table #dataRow div:active .block span,
    .accss-styles-init .bill-comparison-table #dataRow div:focus .txtBlack2 span, .accss-styles-init .bill-comparison-table #dataRow div:active .txtBlack2 span {
        outline: none !important;
        box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3 !important;
    }

    .accss-styles-init .bill-comparison-table #dataRow div:focus .txtBlack2 + .block span, .accss-styles-init .bill-comparison-table #dataRow div:active .txtBlack2 + .block span {
        padding-top: 15px;
    }

    .accss-styles-init .bill-comparison-table #dataRow div:focus .double-span span, .accss-styles-init .bill-comparison-table #dataRow div:active .double-span span {
        outline: none !important;
        box-shadow: none !important;
    }

    .accss-styles-init .bill-comparison-table #dataRow div:focus .double-span, .accss-styles-init .bill-comparison-table #dataRow div:active .double-span {
        outline: none !important;
        box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3 !important;
    }

/* Bill Overview Focus Outline - Download and Print Button */
.accss-styles-init .main-outline-override-v2 .bill-header-focus-outline button.btn-download:focus::before, .accss-styles-init .main-outline-override-v2 .bill-header-focus-outline button.btn-print:focus::before,
.accss-styles-init .main-outline-override-v2 .bill-header-focus-outline button.btn-download:active::before, .accss-styles-init .main-outline-override-v2 .bill-header-focus-outline button.btn-print:active::before,
.accss-styles-init .main-outline-override-v2 .billOverviewSummary .btn-download:focus::before, .accss-styles-init .main-outline-override-v2 .billOverviewSummary .btn-download:active::before {
    outline: none !important;
    box-shadow: none !important;
}

.accss-styles-init .bill-dropdown-button:focus, .accss-styles-init .bill-dropdown-button:active {
    height: 25px !important;
    padding-top: 0px !important;
}

    .accss-styles-init .bill-dropdown-button:focus span.caret, .accss-styles-init .bill-dropdown-button:active span.caret {
        position: absolute !important;
        top: 7px !important;
    }

    .accss-styles-init .bill-dropdown-button:focus span.icon-download-bold, .accss-styles-init .bill-dropdown-button:active span.icon-download-bold {
        position: absolute !important;
        top: 3px !important;
    }

    .accss-styles-init .bill-dropdown-button:focus span.icon-print, .accss-styles-init .bill-dropdown-button:active span.icon-print {
        position: absolute !important;
        top: 4px !important;
    }

.accss-styles-init .bill-header-focus-outline .dropdown-menu {
    top: 50% !important;
    margin-top: 15px !important;
}

.accss-styles-init #billOverviewDetail .servicesRow > .outline {
    border-color: #257fa3 !important;
}

.accss-styles-init #billOverviewDetail .servicesRow:focus, #billOverviewDetail .servicesRow:active, #billOverviewDetail .servicesRow:hover {
    outline: none !important;
    box-shadow: none !important;
}

/* Bill Overview Focus Outline - Get The App */
.accss-styles-init .marketing-banner-inner a:focus, .accss-styles-init .marketing-banner-inner a:active {
    padding-top: 12px !important;
    height: 50px !important;
}

/* Bill Overview Focus Outline - STUFF OUR LAWYERS MADE US SAY  */
.accss-styles-init .bill-footer .legalMessages button:focus, .accss-styles-init .bill-footer .legalMessages button:active {
    border: 0px;
    margin: 0px;
}

/* Bill Overview and Bill Comparison Focus Outline - Guided Tour Modal  */
.accss-styles-init .accss-focus-outline-override-grey-bg .mybill-btnclose:focus, .accss-styles-init .accss-focus-outline-override-grey-bg .mybill-btnclose:active {
    padding: 0px;
    margin: 20px !important;
}

/*Paybill Review cancel link Focus Outline CDA*/
.accss-styles-init .reviewBill-cancel-link {
    padding-right: 0px;
    margin-right: 30px;
}

/*Remove Profile logout link Focus Outline CDA*/
.accss-styles-init .delete-profile-logout-link {
    margin-right: 15px;
}

    .accss-styles-init .delete-profile-logout-link span {
        padding-right: 0px;
    }

/* ACCVIRDCX-155 and ACCVIRDCX-157 */
.edit-marketing-preferences a.accordion-toggle,
.edit-online-marketing-preferences a.accordion-toggle {
    display: inline-block;
    width: 100%;
}

/* ACCVIRDCX-157 */
.accss-styles-init #prefs_link {
    text-decoration: underline !important;
}

/* ACCVIRDCX-139 */
.accss-styles-init a#show-warning-alert.btn.txtUnderline {
    padding: 0;
    margin: 9px 30px;
    border-radius: 0;
}


/*ACCVIRDCX - 177,180 - LOGINHELP,LINKACCOUNT PAGE FOCUS OUTLINE OVERRIDE*/
.accss-styles-init .accss-focus-outline-override-pad input[type="radio"].pos-absolute {
    position: absolute;
}

    .accss-styles-init .accss-focus-outline-override-pad input[type="radio"].pos-absolute:focus::before {
        border-radius: 50%;
    }

.accss-styles-init .accss-focus-outline-override-pad button.modal-closebtn-override:focus::before {
    height: calc(100% + 12px);
    width: calc(100% + 12px);
    top: -3px;
    left: -2px;
    border-radius: 50%;
}
/*ACCVIRDCX - 177,180 - LOGINHELP,LINKACCOUNT PAGE FOCUS OUTLINE OVERRIDE*/
.accss-styles-init .accss-focus-outline-override-pad.override-navbar-collapse > li {
    border-right: 1px solid #414141;
    height: 60px;
}

    .accss-styles-init .accss-focus-outline-override-pad.override-navbar-collapse > li > a {
        padding: 0;
        margin: 15px;
        height: auto;
        border: none;
    }

/*ACCVIRDCX - 187 - Error Icon CDA*/
.accss-styles-init .error-icon-edit-nickname {
    display: flex;
    justify-content: center;
    align-items: center;
}

/*************************************END - Specific Page/Components Overrides***************************************************/
/************************************GLOBAL COMPONENT CSS OVERRIDES**************************************************/
/* Personalization Tiles - LINKS */
.accss-styles-init .personalization-slider-container .personalization-tile-content-container a {
    text-decoration: underline;
    color: #007CAD !important;
    position: relative;
    outline: none !important;
}

    .accss-styles-init .personalization-slider-container .personalization-tile-content-container a:hover,
    .accss-styles-init .personalization-slider-container .personalization-tile-content-container a:focus {
        text-decoration: none;
        color: #0d5f7d;
    }


.accss-styles-init .personalization-wrapper.liquid-wrap .personalization-slider-container.responsive .slick-next:focus, .accss-styles-init .personalization-wrapper.liquid-wrap .personalization-slider-container.responsive .slick-next:active,
.accss-styles-init .personalization-wrapper.liquid-wrap .personalization-slider-container.responsive .slick-prev:focus, .accss-styles-init .personalization-wrapper.liquid-wrap .personalization-slider-container.responsive .slick-prev:active {
    outline: none !important;
    box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
}

.accss-styles-init .personalization-slider-container .personalization-tile-content-container a:active::before,
.accss-styles-init .personalization-slider-container .personalization-tile-content-container a:focus::before {
    content: '';
    height: calc(100% + 6px);
    width: calc(100% + 6px);
    position: absolute;
    top: -3px;
    left: -3px;
    display: block;
    box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
    z-index: 1;
}

/* echat focus outline */
.accss-styles-init .eChat-bot-container {
    outline: none !important;
    box-shadow: none !important;
}

    .accss-styles-init .eChat-bot-container:focus::before, .accss-styles-init .eChat-bot-container:active::before {
        content: '';
        height: calc(100% + 8px);
        width: calc(100% + 8px);
        position: absolute;
        top: -4px;
        left: -4px;
        display: block;
        box-shadow: 0 0 3px 2px #257fa3, 0 0 3px 2px #257fa3;
        z-index: 1;
        border-radius: 50%;
    }

/* Footer - black  */

.accss-styles-init .footer-links-black-bg-grey a:hover, .accss-styles-init .footer-links-black-bg-grey a:focus, .accss-styles-init .footer-links-black-bg-grey a:active {
    color: #868686;
}

.accss-styles-init #footer.accss-focus-outline-override-black-bg .bottom-links .copyright {
    color: #CBCBCB;
}

/************************************END GLOBAL COMPONENT CSS OVERRIDES**************************************************/
