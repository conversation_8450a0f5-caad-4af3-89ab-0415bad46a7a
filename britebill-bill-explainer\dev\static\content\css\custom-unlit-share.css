﻿/*Version 1 Apr.9, 2021 12:16PM*/

/* .sharegroup-flow-override  .faq-container div > a {
    color:#a1a5a6;
} */
.width-fit {
    width: fit-content;
    width: -moz-fit-content;
    justify-content: center;
}
.faq-container div > a:hover{
    text-decoration: none;
}

.faq-container div > a:focus{
    text-decoration: none;
    
}

.faq-container div > a:focus > span{
   color:#111111;
   text-decoration: none;
}

.faq-container div > a:focus > span.icon3{
    color:#00549a;
    text-decoration: none;
 }

  .sharegroup-flow-override .slick-prev, .sharegroup-flow-override .slick-next{
     top:calc(50% - 40px);
 } 


.margin-t-2{
    margin-top: 2px;
}

.p-no-margin{
    margin:0;
    padding:0;
}

.adjust-top{
    margin-top:-10px;
}

.custom-unlit-space-space-around {
    justify-content: space-around;
}

.text-align-end {
    text-align: end;
}

.line-height-22{
    line-height: 22px;
}

.custom-top-2 {
    top: 6px
}

.custom-pad-h-10 {
    padding-left: 10px !important;
    padding-right: 10px !important;
}

.imagewh-60{
    height: 60px;
    width: 60px;
}

.modal-header-lgray {
    height: 70px;
    background-color: #f0f0f0;
}

.margin-neg-30-t {
    margin-top:-30px;
} 



/* datepicker custom css */
#datepicker-start-date {
    background-color: #fff;
    cursor: pointer;
}

    #datepicker-start-date:-moz-read-only { /* For Firefox */
        background-color: #fff;
    }

    #datepicker-start-date:read-only {
        background-color: #fff;
    }

#datepicker-end-date {
    background-color: #fff;
    cursor: pointer;
}

    #datepicker-end-date:-moz-read-only { /* For Firefox */
        background-color: #fff;
    }

    #datepicker-end-date:read-only {
        background-color: #fff;
    }

.ui-datepicker-trigger {
    cursor: pointer;
    text-decoration: none;
}

.form-control-select + span.icon-select-trigger {
    top: 2px;
}
/* datepicker custom css */

/*For scrollable container*/
.scrollableContainer {
    width: 100%;
    overflow: hidden;
    overflow-x: scroll;
    position: relative;
    scrollbar-color: #00549A #a1a5a6;
    scrollbar-width: thin;
    scrollbar-highlight-color: #00549A;
    padding:0;
}


@media (min-width:1232px) {
    .scrollableContainer {
        width: 100%;
        overflow: hidden;
        overflow-x: hidden;
        position: relative;
        scrollbar-color: #00549A #a1a5a6;
        scrollbar-width: thin;
        scrollbar-highlight-color: #00549A;
    }


    .scrollableContainerShadow:after {
        background:none!important;
    }

    .right.scrollableContainerShadow:after {
        width: 0px!important;
    }

    .scrollableContainer.dailyUsageGraph,
    .scrollableContainer.monthsTrend{
        width: 100%;
        overflow: unset;
        position: relative;
        scrollbar-color: #00549A #a1a5a6;
        scrollbar-width: thin;
        scrollbar-highlight-color: #00549A;
    }

    
}

.scrollableContainerShadow {
    position: relative
}

.left.scrollableContainerShadow:before {
    width: 46px;
    -webkit-transition: width .5s;
    transition: width .5s
}

.scrollableContainerShadow:before {
    width: 0;
    pointer-events: none;
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    background: -moz-linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
    background: -webkit-linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
    background: linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
    -webkit-transition: width .1s;
    transition: width .1s
}

.right.scrollableContainerShadow:after {
    width: 46px;
    -webkit-transition: width .5s;
    transition: width .5s
}

.scrollableContainerShadow:after {
    width: 0;
    pointer-events: none;
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: 0;
    background: -moz-linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
    background: -webkit-linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
    background: linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
    -webkit-transition: width .1s;
    transition: width .1s
}

.scrollableContainer > inner {
    min-width: 580px
}

.scrollableContainer > .width-1150 {
    min-width: 1150px !important;
}

.scrollableContainer > .width-800 {
    min-width: 800px !important;
}

.scrollableContainer::-webkit-scrollbar-track {
    background-color: #BABEC2
}

.scrollableContainer::-webkit-scrollbar {
    height: 8px;
    background-color: #F5F5F5
}

.scrollableContainer::-webkit-scrollbar-thumb {
    background-color: #00549A
}

@media screen and (max-width:806px) {
    .scrollableContainerShadow:before {
        z-index: 0;
    }
}



/*For Usage table container*/
.shareGroupDetails {
    display:block;
    width:100% !important;
}

    .shareGroupDetails .sharedTable_Heading .activated_shared,
    .shareGroupDetails .sharedTable_Heading,
    .shareGroupDetails .sharedTable_Row {
        display: flex;
    }

        .shareGroupDetails .sharedTable_Heading .activated_shared,
        .shareGroupDetails .sharedTable_Heading .shared-table-Cell {
            padding: 12px 15px;
            align-items: center;
            display: flex;
        }

        .shareGroupDetails .sharedTable_Heading .shared-table-Cell .spacer17 {
            display:none;
        }

        .shareGroupDetails .sharedTable_Heading .activated_shared .shared-table-Cell,
        .shareGroupDetails .sharedTable_Heading .shared-table-Cell,
        .shareGroupDetails .sharedTable_Row .shared-table-Cell {
            flex-basis: 33.33%;
        }

    .shareGroupDetails .sharedTable .shared-table-Cell {
        flex-basis: 33.33%;
        padding: 10px;
        align-items:center;
    }




@media screen and (max-width:767.98px) {
    .shareGroupDetails {
        width: 1000px;
    }
 
    .infoblock-slider.slick-initialized .slick-slide {
        max-width: 290px;
    }  

    .infoblock-slider-t.slick-initialized .slick-slide {
        max-width: 260px;
    } 

    .infoblock-slider-t-details.slick-initialized .slick-slide {
        max-width: 260px;
    } 

    .singlesub-card {
        width:260px;
    }

    .cardType-2  > div:nth-child(1){
        padding: 30px 15px 0px !important;
    }

    /* .slick-dots {
        margin-top:15px;
    } */

    .infoblock-slider-t-details .slick-dots {
        color: #555;
    }

    .device-card {
        width:290px;
    }
}

/*For tooltip, modal*/

.toolTipWrapper .tooltipS .tooltiptext {
    padding: 20px !important;
}

.tooltipPointer .icon.i-icon {
    top: -5px !important;
    text-align: center;
}

.modal-header .close {
    float: right;
    font-size: 24px;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    filter: alpha(opacity=20);
    opacity: .2;
}

.modal-dialog .modal-content {
    transition: opacity 0.1s;
    box-shadow: rgba(50, 50, 50, 0.38) 5px 7px 30px 0px;
    -webkit-box-shadow: 10px 15px 30px 0 rgba(50, 50, 50, 0.38);
    -moz-box-shadow: 10px 15px 30px 0 rgba(50, 50, 50, 0.38);
    box-shadow: 10px 15px 30px 0 rgba(50, 50, 50, 0.38);
    border-radius: 15px !important;
}

.modal-sharegroup .modal-dialog {
    width: 800px !important;
}

.table.table-bordered > tbody > tr > td, 
.table.table-bordered > tbody > tr > th, 
.table.table-bordered > thead > tr > td, 
.table.table-bordered > thead > tr > th, 
.table > tfoot > tr > td {
    padding: 12px 14px !important;
}

.margin-40 {
    margin: 0px 40px;
}


/*For utility classes on Mobile*/
@media screen and (max-width:767.98px) {
    .hide-xs {
        display: none !important;
    }

    .show-xs {
        display: block !important;
    }

    .margin-30-xs-top {
        margin-top: 30px;
    }

    .margin-20-xs-top {
        margin-top: 20px;
    }

    .margin-10-xs-top {
        margin-top: 10px;
    }
}

/* For HUG-Unlimited Share */
.width486{
    max-width: 486px;
    width: 100%;
    margin: 0 auto;
}
.width475 {
    max-width: 475px;
    width: 100%;
    margin: 0 auto;
}
.width630 {
    max-width: 630px;
    width: 100%;
    margin: 0 auto;
}
.width400 {
    max-width: 400px;
    width: 100%;
    margin: 0 auto;
}
.width300 {
    max-width: 300px;
    width: 100%;
}

/* START Custom slick dots for Infoblock carousel */

.infoblock-slider .slick-dots li.slick-active button {
    background-color: #555555;
}

.infoblock-slider .slick-dots li button {
    border: 1px solid #555555;
    opacity: 1;
}

.infoblock-slider .slick-dots button {
    background-color: #ffffff;
}

.infoblock-slider .slick-prev {
    left: -30px;
}

.infoblock-slider .slick-next {
    right: -30px;
}


/* END Custom slick dots for Infoblock carousel */

/* START Custom slick for Infoblock carousel */
.infoblock-slider .slick-list {
    padding-bottom: 25px;
}


/* START Custom Arrows */
.infoblock-slider .slick-arrow.slick-disabled {
    display: none;
}

.infoblock-slider-t .slick-arrow.slick-disabled {
    display: none;
}

.infoblock-slider-ts .slick-arrow.slick-disabled {
    display: none;
}



.infoblock-slider .slick-prev, .infoblock-slider .slick-next {
    background: #fff;
    border: 1px solid #E1E1E1;
    box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
    margin: 0px 15px;
    margin-top: -15px;
    opacity: 1;
    transform: translateY(-50%);
}

.infoblock-slider .slick-next:before {
    top: 12px;
}

.infoblock-slider .slick-prev:hover,
.infoblock-slider .slick-prev:focus,
.infoblock-slider .slick-next:hover,
.infoblock-slider .slick-next:focus {
    background: #fff;
    border: 1px solid #00549A;
    box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
    color: #00549a;
}

.infoblock-slider .slick-prev:hover:before,
.infoblock-slider .slick-prev:focus:before,
.infoblock-slider .slick-next:hover:before,
.infoblock-slider .slick-next:focus:before {
    color: #00549a;
    opacity: 1;
    
}

.infoblock-slider-t .slick-next:before {
    position:static;
}

.infoblock-slider-t .slick-prev:before {
    position:static;
}

.infoblock-slider-ts .slick-next:before {
    position:static;
}

.infoblock-slider-ts .slick-prev:before {
    position:static;
}


.infoblock-slider .slick-arrow.slick-disabled {
    display: none;
}

.infoblock-slider-t .slick-arrow.slick-disabled {
    display: none;
}

.infoblock-slider .slick-prev:focus, .infoblock-slider .slick-next:focus, .infoblock-slider .slick-next:focus:before, .infoblock-slider .slick-prev:focus:before {
    background: #fff;
    color: #00549a;
}
/* END Custom Arrows */

.infoblock-slider-t .slick-slide {
    max-width: 310px;
}



/* END Custom slick for Infoblock carousel */


.lnkUnderline,
.lnkNoUnderline:hover {
    text-decoration: underline;
}

.lnkNoUnderline,
.lnkUnderline:hover {
    text-decoration: none;
}

.spacer0{height:0}
.spacer1{height:1px}
.spacer3{height:3px}
.spacer5{height:5px}
.spacer8{height:8px}
.spacer9{height:9px}
.spacer10{height:10px}
.spacer11{height:11px}
.spacer12{height:12px}
.spacer15{height:15px}
.spacer20{height:20px}
.spacer30{height:30px}
.spacer40{height:40px}
.spacer60{height:60px}


/*Sharegroup flow overrides*/
/*@media (min-width: 992px) {
    .sharegroup-flow-override .infoblock-slider > div {
        flex-basis: 32.22%;
        max-width: 32.22%;
    }

    .sharegroup-flow-override .infoblock-slider-t-details > div {
        flex-basis: 32.22%;
        max-width: 32.22%;
    }

    .sharegroup-flow-override .infoblock-slider-t .cardslide2 {
        flex-basis: 32.22%;
        max-width: 32.22%;
    }

    .sharegroup-flow-override .infoblock-slider-t .cardslide {
        flex-basis: 32.22%;
        max-width: 32.22%;
    }

}*/ 


.sharegroup-flow-override .infoblock-slider-t .slick-dots,
.sharegroup-flow-override .infoblock-slider-t-details .slick-dots{
    margin: 10px auto;
}

.sharegroup-flow-override .infoblock-slider-t .slick-dots li.slick-active button {
    background:#555;
}

.sharegroup-flow-override .infoblock-slider-ts .slick-dots li.slick-active button {
    background:#555;
}

.sharegroup-flow-override .infoblock-slider-t-details .slick-dots li.slick-active button {
    background:#555;
}




@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {

    .wraptext {
        display: block;
        width: 70%;
        flex: 0;                           
    }

    .sharegroup-flow-override .order-device {
        margin-left:50px;
    }

    .faq-container div > a:focus span.faq-title{
        color:#000;
       
     }
     
}

.sharegroup-flow-override .infoblock-slider-t-details #slick-slide00,
.sharegroup-flow-override .infoblock-slider-t-details #slick-slide01,
.sharegroup-flow-override .infoblock-slider-t-details #slick-slide02,
.sharegroup-flow-override .infoblock-slider-t-details .slick-list {
    overflow: initial;
}

.unls-fcolor-6b6b6b{
    color: #6B6B6B;
}

.lineHeight-38{
    line-height: 38px;

}

.lineHeight-20{
    line-height: 20px;

}

.lineHeight-22{
    line-height: 22px;

}


.lineHeight-26{
    line-height: 26px;

}

.cardslide > div:nth-child(2) > .btn {
    padding: 10px 30px;
    min-height: 35px;
}

.sharegroup-flow-override .btncart{
    width:108px;
    height: 35px;
   
}

.txtheader-content{
    font-weight: 900;
    letter-spacing: -0.4px;
    color: #111111;
}

.pending-details-text1{
    height: 18px;
    width: 92px;
    color: #111111;
    font-family: Arial;
    font-size: 14px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 18px;
}

.pending-details-text2{
    height: 18px;
    width: 87px;
    color: #555555;
    font-family: Arial;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 18px;
}

.pending-details-text3{
    height: 22px;
    width: 111px;
    color: #111111;
    font-family: Arial;
    font-size: 18px;
    letter-spacing: 0;
    line-height: 22px;
}

.no-margin-bottom{
    margin-bottom:0px;
}

.modal-body-pendingdetails{
    margin-top:45px;
    margin-bottom: 45px;
}

.sharegroup-flow-override {
    overflow-x: hidden !important;
}

.btnconf{
    width: 87px;
    height: 35px;
}

.btnconflog{
    width: 121px;
    height: 35px;
}

.infoblock-slider-t-details .tooltip-inner {
    padding:20px;
    text-align: center;
}


.sharegroup-tooltip .tooltip-inner {
    padding: 20px;
    text-align: center;
}

.sharegroup-flow-override .tooltip-wrap{
    overflow: visible;
}

.sharegroup-tooltip .modal-dialog
{
    overflow: visible;
}

.modal.modal-tooltip .sharegroup-modal{
    padding:0 20px 0;
    margin-top:-10px;
}

/** TABLET TO LARGE DESKTOP */
@media (min-width:768px) {
    .alignment-proposition .responsive-slick-slider .singlesub-card {
        margin: 7px;
    }

    .alignment-proposition .singlesub-card {
        width: 228px;
        min-height: 278px;
    }

    .alignment-proposition .section-slider {
        display: flex;
        justify-content: center;
    }

    .alignment-proposition .cnt-slider {
        max-width: 955px;
    }

    #slider-2 {
        width: 470px;
    }

    .slick-arrow {
        margin:0px 12px;
    }

    .slick-sg-v1 .singlesub-card {
        max-width: 223px;
    }

    .slick-sg-v1 .singlesub-card:first-child {
        margin-left:0px;
    }
    .slick-sg-v1 .singlesub-card:last-child {
        margin-right: 0px;
    }
}

@media (min-width: 768px) and (max-width:991px) {
    .alignment-proposition .responsive-slick-slider .singlesub-card {
        margin: 7px;
    }

    .alignment-proposition .singlesub-card {
        width: 228px;
        height: 278px;
    }

    .alignment-proposition .section-slider {
        display: flex;
        justify-content: center;
    }

    .alignment-proposition .cnt-slider {
        max-width: 955px;
    }

        .alignment-proposition .cnt-slider .slick-list {
            max-width: 712px;
        }

        .pad-v-22h {
            padding-top:22px;
            padding-bottom: 22px;
        }    
}


/* DESKTOP MEDIA QUERY */

@media (min-width: 992px) {
    .shareGroupDetailsContainer .scrollableContainer {
        overflow-x:hidden;
    }

    .shareGroupDetailsContainer .scrollableContainerShadow::before {
        background:none;
    }

    .shareGroupDetailsContainer .right.scrollableContainerShadow:after {
        width: 0px;
    }

    .infoblock-slider > div {
        max-width: 310px;
    }

    .infoblock-slider-t-details > div {
        max-width: 310px;
    }

    .infoblock-slider-t .cardslide2 {
        max-width: 310px;
    }

    .infoblock-slider-t .cardslide {
        max-width: 310px;
    }

    .singlesub-card {
        max-width: 228px;
        min-height: 278px;
    }
    
    .add-new-line {
        max-width:465px;
        height:220px;
    }

    .faq-cont{
       width:630px;
    }

    .pending-cont{
        height: 122px;
    }

    .device-card {
        max-width:465px;
    }

    .sharedetails-cont{
        width: 465px;
    }

    .share-details-cont{
        width: 310px;
    }

    .pad-v-22h {
        padding-top:22px;
        padding-bottom: 22px;
    }


    .slick-sg-v1 {
        width: 940px;
    }

    .cmp-slick.infoblock-slider-t {
        height: auto;
    }
}

/* TABLET MEDIA QUERY */

@media (min-width:768px) and (max-width:991.98px) {
    .shareGroupDetails {
        width: 992px;
    }

    .infoblock-slider.slick-initialized .slick-slide {
        /* max-width: 347px; */
        max-width: 226px;
    }

    .infoblock-slider-t.slick-initialized .slick-slide {
        max-width: 347px;
    }

    .infoblock-slider-t-details.slick-initialized .slick-slide {
        max-width: 346px;
    }

    .add-new-line > div {
        width: 346px;
        height: 260px;
    }

    .infoblock-slider-t .slick-list {
        overflow: visible;
    }

    .device-card {
        max-width:462px;
    }

    .singlesub-card {
        width: 226px;
    }

    .faq-cont {
        width:585px;
    }

    .pending-cont {
        height: 140px;
    }

    .sharedetails-cont{
        width: 346px;
        height: 260px;
    }

    .share-details-cont{
        width: 346px;
        height: 292px;
    }
}

/* MOBILE MEDIA QUERY */

@media (min-width: 320px) and (max-width: 767.98px) {
    .sharegroup-flow-override .slider-mobile-container {
        padding-right: 0;
        padding-left : 0;
    }

    .sharegroup-flow-override .slider-mobile-container .infoblock-slider-t .slick-list {
        padding-left:15px;
    }

    .sharegroup-flow-override .slider-mobile-container .infoblock-slider-ts .slick-list {
        padding-left:15px;
    }

   
    .sharegroup-flow-override .infoblock-slider.slick-initialized .slick-slide {
        max-width: 2.12%;
    }  

    .infoblock-slider-t .slick-arrow.slick-disabled {
        display: none;
    }

    .infoblock-slider-ts .slick-arrow.slick-disabled {
        display: none;
    }

    /* .slick-track{
        height: 373px;
    } */

    .faq-cont{
        width:290px;
    }

    .singlesub-card {
        width: 290px;
    }

    .device-card {
        max-width:290px;
    }

    .share-details-cont{
        height: 292px;
    }

    .sharedetails-cont{
        width: 290px;
        height: 261px;
    }

    .sharedetails-cont1{
        width: 290px;
        height: 212px;
    }

    .btndetails{
        width: 135px;
        height: 35px;
    }

    .share-details-cont{
        min-width: 260px;
        width: calc(100vw - 120px);
        height: 292px;
    }

    .sharegroup-modal-5 .modal {
        left: 50% !important;
        top: 50% !important;
        transform: translate(-50%, -50%) !important;
    }
    
    .modal-header-lgray {
        height: 78px;
        background-color: #f0f0f0;
    }

    .modal-body-pendingdetails{
        margin-top:30px;
        margin-bottom: 30px;
    }

    .modal-sharegroup .scrollAdjust{
        padding-top: 0px;
        margin-right: 0px;
    }

}

@media screen and (max-width: 991.98px) { 
    .hidden-sm {display: none}
    .txtCapital-sm{text-transform:uppercase}

    .infoblock-slider .slick-list {
        overflow: visible;
        padding-bottom: 5px;
    }
}

@media (min-width: 1240px) {
    .infoblock-slider > div {
        max-width: 390px;
    }

    /* .infoblock-slider-t > div {
        max-width: 390px;
    } */

    .infoblock-slider-ts .cardslide {
        max-width: 390px;
    }

    .infoblock-slider-t-details > div {
        max-width: 390px;
    }

    .infoblock-slider-t .cardslide {
        max-width: 390px;
    }

    .sharedetails-cont{
        width: 585px;
        height: 220px;
    }

    .infoblock-slider-ts .share-details-cont{
        width:390px;
    }

    
}

@media (min-width: 1200px) {

    .infoblock-slider-t-details > div {
        max-width: 390px;
    }

    .infoblock-slider-t .cardslide {
        max-width: 390px;
    }

    .infoblock-slider-ts .share-details-cont {
        width: 390px;
    }
}

@media (max-width: 768px) {
    .cmp-slick-md-hideArrow .slick-arrow {
        display:none !important;
    }
}

.cmp-slick {
    justify-content: center;
}

.cmp-slick .slick-prev.slick-disabled {
    display: none !important;
}

footer.foot-t-1 .search-bar-footer .search-btn {
    position: unset;
}

.btn-add-member {
    overflow-wrap:anywhere;
}


/*Override for slick carousel - hidding the cards not active*/

.sharegroup-flow-override .cmp-slick .share-details-cont {
    position: static;
    box-shadow: 0 6px 25px 0 rgb(0 0 0 / 8%);
}

.sharegroup-flow-override .cmp-slick .share-details-cont .share-details-title {
    display:flex;
    flex-direction:column;
    text-align:left;
}

.sharegroup-flow-override .cmp-slick .slick-list {
    overflow:visible;
    overflow-x: hidden;
}


.sharegroup-flow-override .unlit-usage-tooltip-modal .modal-body {
    padding: 0 28px;
}


@media (min-width: 1240px) {
    .hiddenInactive {
        margin-left: -30px;
        margin-right: -30px;
    }

        .hiddenInactive .slick-slide.offscreen {
            visibility: hidden;
        }

        .hiddenInactive .slick-list {
            padding-left: 30px;
            padding-right: 30px;
            overflow: hidden;
            padding-top: 30px;
            padding-bottom: 50px;
            margin-top: -30px;
            margin-bottom: -50px;
        }

        .hiddenInactive .slick-next {
            right: -5px;
            margin-top: 3px;
        }

        .hiddenInactive .slick-prev {
            left: -5px;
            margin-top: 3px;
        }


    .sharegroup-flow-override .cmp-slick.sliding:before,
    .sharegroup-flow-override .cmp-slick.sliding:after {
        content: "";
        display: block;
        position: absolute;
        top: 0;
        height: 100%;
        background: #000;      
        z-index: 80;
    }

    .sharegroup-flow-override .cmp-slick.sliding:before {
        transform: translateX(-100%);
        width: 100%;
        left: 30px;
        background: linear-gradient(90deg, rgba(255,255,255,1) 98%, rgba(255,255,255,0) 100%);
    }


    .sharegroup-flow-override .cmp-slick.sliding:after {
        width: 100%;
        right: 30px;
        background: linear-gradient(270deg, rgba(255,255,255,1) 98%, rgba(255,255,255,0) 100%);
        transform: translateX(100%);
    }

    .sharegroup-flow-override .cmp-slick .slick-dots {
        z-index:200;
    }

}

/*Override for sharegroup link accordion*/
.mybell-mobility-sharegroup .modal .modal-body .rate-plan-current-share-group {
    overflow: hidden;
}

.mybell-mobility-sharegroup .modal .modal-body .rate-plan-current-share-group .card-footer {
    height: 100%;
}

/*Fix for member details height when on portrait mode*/
@media screen and (orientation:portrait) {
    #member_details_slick .share-details-cont {
        height: 100%;
    }
}