﻿/***styles copied from shop.css not present in bell.css starts***/
main a,
.txtUnderlineOnHover {
    align-items: center;
    color: #00549a;
    display: inline-flex;
}
    /* for anchor tags inside main, focus only the element with the anchor-text class. to apply the same behavior to elements outside main, use the txtUnderlineOnHover class. */
    main a:not(.txtUnderline),
    main a:not(.txtUnderline):hover,
    main a:not(.txtUnderline):focus,
    main a:not(.txtUnderline) .anchor-icon,
    .txtUnderlineOnHover:not(.txtUnderline),
    .txtUnderlineOnHover:not(.txtUnderline):hover,
    .txtUnderlineOnHover:not(.txtUnderline):focus,
    .txtUnderlineOnHover:not(.txtUnderline) .anchor-icon {
        text-decoration: none;
    }

    main a:hover > .anchor-text,
    main a:focus > .anchor-text,
    .txtUnderlineOnHover:hover > .anchor-text,
    .txtUnderlineOnHover:focus > .anchor-text {
        text-decoration: underline;
    }

h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0;
}

p {
    margin-bottom: 15px;
}

.big-title,
.title,
.small-title,
.subtitle-2,
.subtitle-2-reg,
.surtitle,
.surtitle-black {
    max-width: 100%;
    width: auto;
    color: #111;
}
.big-title,
.title,
.small-title {
    font-family: "bellslim_font_black", Helvetica, Arial, sans-serif;
    font-weight: 400;
    letter-spacing: -.4px;
    line-height: 28px;
}

.small-title {
    font-size: 22px;
    line-height: 24px;
}

.form-control-select + span {
    height: auto;
    padding: 13px 10px;
    right: 0;
}
ul.no-list-style {
    list-style: none;
    margin: 0;
    padding: 0;
}

/***styles copied from shop.css not present in bell.css ends***/
.col35-minus-20px {
    width: calc(35% - 20px);
}

.col35-plus-175px {
    width: calc(35% + 175px);
}

.width-175 {
    width: 175px;
}

.error label {
    color: #BD2025;
}

.show-msg .form-group {
    display: flex;
}

    .show-msg .form-group.error label {
        margin-top: -21px;
    }

@media(min-width: 320px) and (max-width: 767.98px) {   
    .show-msg .form-group {
        display: block;
    }

    .modal-notes-footer {
        padding: 30px 15px;
        margin: 0 -15px;
    }
    .small-title {
        font-family: "bellslim_font_black", Helvetica, Arial, sans-serif;
        font-size: 24px;
        line-height: 26px;
    }
    .brfpad .container.container-pad-h-0-xs {
        padding-left: 0;
        padding-right: 0;
    }

    .sms-verification-cta-sm {
        width: calc(100% + 30px);
        position: relative;
        left: -15px;
    }

    .review-tooltip {
        display: none;
    }

    .rate-plan-box .ctrl_radioBtn .ctrl_element {
        top: 3px;
        left: 0;
    }
    .rate-plans-col-mar .rate-plan-box {
        margin: 0;
    }
    .lower-payment-boxes{
        margin: 0;
    }
}

/************************************/
/*** decvice details modal begins ***/
.device-details-modal .modal-body {
    padding-left: 0;
    padding-right: 0;
    margin-bottom: 0;
}

.device-details-modal .device-image {
    max-width: 116px;
    height: auto;
}

.device-details-modal .device-details-links ul li a {
    justify-content: space-between;
    padding: 25px 30px;
    width: 100%;
    border-top: 1px solid #e1e1e1;
}

.device-details-modal .device-details-links ul li a .small-title {
    font-size: 22px;
    color: #111;
}

.device-details-modal .device-details-links ul li a:hover {
    text-decoration: none;
}

.device-details-modal .device-details-links ul li a span.icon3 {
    font-size: 16px;
    color: #00549a;
}

.device-details-modal .device-details-links ul li a svg {
    padding-right: 3px;
}

.device-details-modal .device-details-links ul li a .bazaarvoice-stars {
    padding-left: 25px;
}

.device-features-modal .small-title {
    font-size: 22px;
}

.device-features-modal .features .title {
    font-size: 24px;
    font-weight: bold;
    display: block;
}

.device-features-modal .features .text {
    display: block;
}

.device-features-modal .features-row {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
    padding-top: 20px;
    align-items: center;
}

.device-features-modal .features-column-icon {
    width: 60px;
    flex: 0;
}

.device-features-modal .features-column-text {
    flex: 1;
}

.device-features-modal .features-column-text,
.device-features-modal .features-column-icon {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
}

.device-features-modal .features-column-text span {
    display: block;
}

.device-features-modal .features-title {
    font-size: 18px;
    font-weight: bold;
    color: #111;
    padding-bottom: 5px;
}

.device-memory-modal .memory-capacity-title {
    font-size: 20px;
    font-weight: bold;
    color: #111;
}

.device-memory-modal .memory-capacity span:after {
    background-color: #ccc;
    content: "";
    display: inline-block;
    height: 18px;
    margin: 0 15px;
    vertical-align: middle;
    width: 1px;
}

.device-memory-modal .memory-capacity span:last-child:after {
    margin: 0;
    width: 0px;
}

.device-memory-modal .device-memory-subsection {
    padding-top: 35px;
}

.device-memory-modal .device-memory-subsection h4 {
    font-size: 14px;
    font-weight: bold;
    color: #111;
}

.device-memory-modal .device-memory-subsection p {
    margin-top: 10px;
    margin-bottom: 0;
}

.device-memory-modal .data-usage-examples {
    padding-top: 30px;
}

.device-memory-modal .data-usage-examples,
.device-memory-modal .data-usage-examples .category,
.device-memory-modal .data-usage-examples .category-data,
.technical-specifications-modal .technical-specifications {
display: -webkit-flex;
flex-wrap: wrap;
-webkit-flex-wrap: wrap
}

.device-memory-modal .data-usage-examples .category:nth-child(odd) {
    flex: 1 1 45%;
}

.device-memory-modal .data-usage-examples .category:nth-child(even) {
    flex: 1 1 55%;
}

.device-memory-modal .data-usage-examples .category:last-child .category-data {
    padding-bottom: 0;
}

.device-memory-modal .data-usage-examples .category:last-child,
.device-memory-modal .data-usage-examples .category:nth-last-child(2):nth-child(odd) {
    flex: 0 0 45%;
}

.device-memory-modal .data-usage-examples .category-title,
.technical-specifications-modal .heading {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
    align-items: center;
}

.technical-specifications-modal .heading {
    border-bottom: 1px solid #d4d4d4;
    padding-bottom: 5px;
}

.device-memory-modal .data-usage-examples .category-data,
.technical-specifications-modal .technical-specifications {
    padding-top: 15px;
    padding-bottom: 25px;
}

.device-memory-modal .data-usage-examples .category-data-name,
.technical-specifications-modal .technical-specifications .technical-specifications-name {
    color: #111;
    font-weight: bold;
}

.technical-specifications-modal .technical-specifications .technical-specifications-data {
    text-align: right;
}

.device-memory-modal .data-usage-examples .category-data-name,
.device-memory-modal .data-usage-examples .category-data-usage {
    padding-bottom: 10px;
}

.technical-specifications-modal .technical-specifications .technical-specifications-name,
.technical-specifications-modal .technical-specifications .technical-specifications-data {
    padding-bottom: 15px;
}

.device-memory-modal .data-usage-examples .category-title .category-title-name {
    font-size: 18px;
    color: #111;
    margin-left: 20px;
}

.technical-specifications-modal .heading .heading-name {
    font-size: 18px;
    color: #111;
    margin-left: 10px;
}

.technical-specifications-modal .technical-specifications-category:nth-child(odd) .technical-specifications > div:nth-child(odd) {
    flex: 0 0 50%;
}

.technical-specifications-modal .technical-specifications-category:nth-child(odd) .technical-specifications > div:nth-child(even) {
    flex: 0 0 50%;
}

.technical-specifications-modal .technical-specifications-category:nth-child(even) .technical-specifications > div:nth-child(odd) {
    flex: 0 0 50%;
}

.technical-specifications-modal .technical-specifications-category:nth-child(even) .technical-specifications > div:nth-child(even) {
    flex: 0 0 50%;
}

.technical-specifications-modal .technical-specifications-wrapper {
    padding-top: 25px;
}

.more-about-device-modal .iphone11-img {
    display: block;
    max-width: 100%;
    height: auto;
}

.more-about-device-modal .modal-body {
    margin-bottom: 0;
}

.more-about-device-modal .more-about-device-title {
    padding-top: 35px;
    padding-bottom: 0;
}

.reviews-modal .reviews-title {
    padding-top: 35px;
    padding-bottom: 10px;
}

@media (min-width: 767.99px) {
    .device-memory-modal .data-usage-examples .category:nth-child(odd) .category-data > div:nth-child(odd) {
        flex: 1 1 45%;
    }

    .device-memory-modal .data-usage-examples .category:nth-child(odd) .category-data > div:nth-child(even) {
        flex: 1 1 55%;
    }

    .device-memory-modal .data-usage-examples .category:nth-child(even) .category-data > div:nth-child(odd) {
        flex: 1 1 35%;
    }

    .device-memory-modal .data-usage-examples .category:nth-child(even) .category-data > div:nth-child(even) {
        flex: 1 1 65%;
    }
}

@media (min-width: 320px) and (max-width: 767.98px) {
    .device-details-modal .device-details-links ul li a {
        padding-left: 15px;
        padding-right: 15px;
    }

    .device-details-modal .device-image {
        max-width: 105px;
    }

    .device-details-modal .device-details-links ul li a .bazaarvoice-stars {
        display: block;
        padding-left: 0;
        padding-top: 10px;
    }

    .device-features-modal .features-row,
    .device-features-modal .features-column-icon,
    .device-features-modal .features-column-text {
        display: block;
        width: 100%;
    }

    .device-features-modal .features-column-text {
        padding: 10px 0;
    }

    .device-features-modal .features-section {
        padding-left: 0;
        padding-right: 0;
    }

    .device-memory-modal .data-usage-examples,
    .device-memory-modal .data-usage-examples .category {
        display: block;
        width: 100%;
    }

    .device-memory-modal .data-usage-examples .category .category-data > div:nth-child(odd) {
        flex: 1 1 40%;
    }

    .device-memory-modal .data-usage-examples .category .category-data > div:nth-child(even) {
        flex: 1 1 60%
    }

    .more-about-device-modal .more-about-device-title {
        padding-bottom: 10px;
    }
}
/*** decvice details modal ends ***/
/**********************************/
/*** compare modal begins ***/
.compare-modal .compare-table-wrap .compare-modal-row-block {
    display: -webkit-flex;
    flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
}

.compare-modal .compare-table-wrap .current-solution,
.compare-modal .compare-table-wrap .new-solution {
    border: 1px solid #d4d4d4;
}

.compare-modal .compare-table-wrap .compare-modal-heading {
    text-align: center;
    vertical-align: middle;
    font-family: "bellslim_mediumregular", Helvetica, Arial, sans-serif;
    border-bottom: 1px solid #d4d4d4;
}

.compare-modal .compare-modal-title {
    font-family: "bellslim_mediumregular", Helvetica, Arial, sans-serif;
    font-size: 20px;
}

.compare-modal .compare-table-wrap .compare-modal-heading span {
    display: block;
}

.compare-modal .compare-table-wrap .compare-modal-heading .heading {
    font-size: 20px;
}

.compare-modal .compare-table-wrap .compare-modal-phone .compare-modal-row-block > div:nth-child(odd) {
    flex: 0 0 25%;
}

.compare-modal .compare-table-wrap .compare-modal-phone .compare-modal-row-block > div:nth-child(even) {
    flex: 0 0 75%;
}

.compare-modal .compare-table-wrap .compare-modal-device .compare-modal-row-block > div:nth-child(odd),
.compare-modal .compare-table-wrap .compare-modal-rateplan .compare-modal-row-block > div:nth-child(odd),
.compare-modal .compare-table-wrap .compare-modal-addons .compare-modal-row-block > div:nth-child(odd) {
    flex: 0 0 75%;
}

.compare-modal .compare-table-wrap .compare-modal-device .compare-modal-row-block > div:nth-child(even),
.compare-modal .compare-table-wrap .compare-modal-rateplan .compare-modal-row-block > div:nth-child(even),
.compare-modal .compare-table-wrap .compare-modal-addons .compare-modal-row-block > div:nth-child(even) {
    flex: 0 0 25%;
}

.compare-modal .compare-table-wrap .compare-modal-total-monthly .compare-modal-row-block > div {
    flex: 0 0 50%;
}

.compare-modal img {
    display: block;
    max-width: 100%;
    height: auto;
}

.compare-modal hr {
    margin: 0;
}

.compare-modal .compare-table-wrap .compare-modal-total-monthly {
    vertical-align: middle;
    border-top: 1px solid #d4d4d4;
}

.compare-modal .compare-table-wrap .compare-modal-total-monthly .price {
font-family: "bellslim_mediumregular", Helvetica, Arial, sans-serif;
}

.compare-modal .compare-table-wrap .compare-modal-total-monthly .price sup {
    top: -10px;
}

.compare-modal .modal-body {
    margin-bottom: 0;
}

.compare-modal .modal-footer {
    margin-bottom: 0;
    border-top: 1px solid #d4d4d4;
    display: block;
}

.compare-modal .promoWrap span:not(.promo) {
    display: block;
}

.compare-modal .promoWrap .promo {
    width: auto;
    margin: auto;
    padding: 0 7px;
    font-size: 10px;
    text-transform: uppercase;
    display: inline-block;
    margin-top: 5px;
}

.compare-modal .current-solution .promoWrap .promo {
    color: #fff;
    background: #555;
}

.compare-modal .new-solution .promoWrap .promo {
    color: #fff;
    background: #00549A;
}

@media (min-width: 767.99px) {
    .compare-modal .compare-table-wrap .current-solution {
        border-right: none;
    }
}

@media (min-width: 320px) and (max-width: 767.98px) {
    .compare-modal .compare-table-wrap .new-solution {
        margin-top: 30px
    }
}

/*** compare modal ends ***/
/*********************************/

/*** payments modal starts ***/
/**********************************/

.lower-payment-links li {
    padding: 24px 0;
    border-top: 1px solid #D4D4D4;
}

.margin-left-auto {
    margin-left: auto;
}

.inline-100 {
    display: inline-block;
    width: 100%;
}

.lower-payment-links .icon-chevron {
    float: right;
}

.modal-notes-footer {
    border-radius: 0 0 9px 9px;
    padding: 30px;
    background-color: #f0f0f0;
    margin: 0 -30px;
}

.accordionContainer > ul > li:not(:last-child) {
    border-bottom: 1px solid #d4d4d4;
}

.accordionContainer li a[aria-expanded=true] span:not(.icon) {
    font-size: 14px;
    color: #111;
}

/*** payments modal ends ***/
/**********************************/

/**CSS for modal dialogues starts(copied from shop.css not present in bell.css)**/
.modal-dialog,
.modal-content {
    border-radius: 10px 10px 0 0;
}

.modal-content {
    height: 100%;
}

.modal-header {
    align-items: center;
    background-color: #f0f0f0;
    border-radius: 10px 10px 0 0;
    height: 74px;
    padding: 15px 20px 15px 15px;
}

.modal-dialog .modal-content .modal-header .close {
    margin: 0 0 0 15px;
    padding: 0;
}

.modal-body {
    margin-bottom: 30px;
    margin-top: 30px;
    padding: 0 15px;
}

    .modal-body.no-margin-bottom {
        margin-bottom: 0;
    }

.modal-open .modal-body.scrollAdjust {
    padding-bottom: 0;
}

.modal.modal-status .modal-dialog {
    border-radius: 10px;
    box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
    max-width: calc(100% - 30px);
    position: relative;
}

.modal.modal-status .modal-content {
    border-radius: 10px;
}

.modal.modal-status .modal-body {
    padding: 0 30px;
}

.modal-status-icon {
    font-size: 30px;
}
/* webkit only hack: custom scroll (.scrollAdjust) only works on webkit browsers so only adjust the margin and padding for those browsers */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .modal-open .modal-body.scrollAdjust:not(*:root) {
        margin-right: 10px;
        padding-right: 20px;
    }
}

@media(min-width:768px) {
    .modal-dialog,
    .modal-content {
        border-radius: 10px;
    }

    .modal .modal-dialog {
        max-height: calc(100% - 120px);
    }

    .modal-header {
        border-radius: 9px 9px 0 0;
        height: 70px;
        padding: 0 30px;
    }

    .modal-body {
        padding: 0 30px;
    }

    .modal.modal-status .modal-body {
        padding-top: 10px;
        padding-bottom: 15px;
    }

    .modal-status-icon {
        font-size: 36px;
    }

    .small-title {
        font-family: "bellslim_font_black", Helvetica, Arial, sans-serif;
        font-size: 24px;
        line-height: 26px;
    }
}
/**CSS for modal dialogues ends**/
/**********************************/


/**********************************/
/**CSS for review summary page starts**/
.flex-grid-no-spacing .row {
    margin: 0;
    display: flex !important;
}

.flex-grid-no-spacing .col-xl,
.flex-grid-no-spacing .col-xl-auto,
.flex-grid-no-spacing .col-xl-12,
.flex-grid-no-spacing .col-xl-11,
.flex-grid-no-spacing .col-xl-10,
.flex-grid-no-spacing .col-xl-9,
.flex-grid-no-spacing .col-xl-8,
.flex-grid-no-spacing .col-xl-7,
.flex-grid-no-spacing .col-xl-6,
.flex-grid-no-spacing .col-xl-5,
.flex-grid-no-spacing .col-xl-4,
.flex-grid-no-spacing .col-xl-3,
.flex-grid-no-spacing .col-xl-2,
.flex-grid-no-spacing .col-xl-1,
.flex-grid-no-spacing .col-lg,
.flex-grid-no-spacing .col-lg-auto, .flex-grid-no-spacing .col-lg-12, .flex-grid-no-spacing .col-lg-11, .flex-grid-no-spacing .col-lg-10, .flex-grid-no-spacing .col-lg-9, .flex-grid-no-spacing .col-lg-8, .flex-grid-no-spacing .col-lg-7, .flex-grid-no-spacing .col-lg-6, .flex-grid-no-spacing .col-lg-5, .flex-grid-no-spacing .col-lg-4, .flex-grid-no-spacing .col-lg-3, .flex-grid-no-spacing .col-lg-2, .flex-grid-no-spacing .col-lg-1, .flex-grid-no-spacing .col-md, .flex-grid-no-spacing .col-md-auto, .flex-grid-no-spacing .col-md-12, .flex-grid-no-spacing .col-md-11, .flex-grid-no-spacing .col-md-10, .flex-grid-no-spacing .col-md-9, .flex-grid-no-spacing .col-md-8, .flex-grid-no-spacing .col-md-7, .flex-grid-no-spacing .col-md-6, .flex-grid-no-spacing .col-md-5, .flex-grid-no-spacing .col-md-4, .flex-grid-no-spacing .col-md-3, .flex-grid-no-spacing .col-md-2, .flex-grid-no-spacing .col-md-1, .flex-grid-no-spacing .col-sm, .flex-grid-no-spacing .col-sm-auto, .flex-grid-no-spacing .col-sm-12, .flex-grid-no-spacing .col-sm-11, .flex-grid-no-spacing .col-sm-10, .flex-grid-no-spacing .col-sm-9, .flex-grid-no-spacing .col-sm-8, .flex-grid-no-spacing .col-sm-7, .flex-grid-no-spacing .col-sm-6, .flex-grid-no-spacing .col-sm-5, .flex-grid-no-spacing .col-sm-4, .flex-grid-no-spacing .col-sm-3, .flex-grid-no-spacing .col-sm-2, .flex-grid-no-spacing .col-sm-1, .flex-grid-no-spacing .col, .flex-grid-no-spacing .col-auto, .flex-grid-no-spacing .col-12, .flex-grid-no-spacing .col-11, .flex-grid-no-spacing .col-10, .flex-grid-no-spacing .col-9, .flex-grid-no-spacing .col-8, .flex-grid-no-spacing .col-7, .flex-grid-no-spacing .col-6, .flex-grid-no-spacing .col-5, .flex-grid-no-spacing .col-4, .flex-grid-no-spacing .col-3, .flex-grid-no-spacing .col-2, .flex-grid-no-spacing .col-1 {
    padding: 0;
}
.price {
    font-size: 30px;
    color: #00549a;
    display: block;
    white-space: nowrap;
    line-height: .8;
    letter-spacing: -1px;
    font-family: "bellslim_semiboldregular", Helvetica, Arial, sans-serif;
}

.icon-circle-70 {
    width: 70px;
    height: 70px;
    background-color: #00549a;
    display: inline-block;
    position: relative;
    border: 2px solid #00549a;
    border-radius: 50%;
    color: #fff;
    font-size: 32px;
}
.icon-circle-70:before {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.price sup {
    top: -.70em;
    font-size: 14px;
    letter-spacing: 0;
}

.terms-scroll {
    height: 75px;
    transition: height .3s ease-out;
}

.terms-scroll.show {
    height: 400px !important;
    transition: height .3s ease-out
}

.terms-scroll.collapse {
    display: block;
    overflow-y: auto;
}

.blur {
    opacity: .3;
}

.form-up-carret:before {
    position: absolute;
    content: "";
    display: block;
    width: 0;
    height: 0;
    left: 48%;
    top: 0;
    border: 10px solid transparent;
    border-bottom: 10px solid #f4f4f4;
}

.simple-header h1 {
    line-height: initial;
}

.width-75-inline {
    width: 75px;
    display: inline-block;
}

.review-tooltip {
    display: inline-block;
}

.small-view-terms a, .small-view-terms a:hover, .small-view-terms a:focus {
    text-decoration: underline;
}
/**CSS for review summary page ends**/
/**********************************/
.accordionContainer > ul > li:not(:last-child) {
    border-bottom: 1px solid #d4d4d4;
}

.accordionContainer li a[aria-expanded=true] span:not(.icon) {
    font-size: 14px;
    color: #111;
}

/*** device listing starts ***/
.responsive-simplified-header h3.bellSlim, .responsive-simplified-header h1.bellSlim {
    line-height: initial;
    font-size: 24px;
}

.responsive-simplified-header button {
    font-size: 15px;
    padding: 8px 31px;
}

.device-listing-carousel-title-wrap {
    align-items: baseline;
}

.device-listing-tablist .tablist .active span {
    border-bottom: 4px solid #00549a;
    padding-bottom: 13px;
    color: #111;
}

.device-listing-header .back .icon:before {
    top: -3px;
}

.device-listing-header h1{
    letter-spacing: 0.3px;
}

.device-listing-accordion .card-body{
    padding: 6px;
}

.device-listing-accordion .accordionButton.open .icon-exapnd-outline-circled:before {
    content: "\e90b";
}

.device-listing-accordion .icon-exapnd-outline-circled{
    font-weight: normal;
}

.device-listing-accordion .accordionButton {
    align-items: center;
}

.prod-tile-content .prod-tile-promo .prod-tile-promo-title {
    color: #00549A;
    font-weight: bold;
    text-transform: uppercase;
    padding-top: 5px;
    padding-bottom: 10px;
}

@media (min-width: 767.99px) {
    .prod-tile-content .prod-tile-note {
        letter-spacing: 0.5px;
    }
}

@media (min-width: 320px) and (max-width: 767.98px) {
    .device-listing-top-message {
        background: #fff;
        padding-bottom: 30px;
    }
    .device-listing-top-message .heading {
        padding-top: 20px;
    }
    .device-listing-select-device-title{
        padding-top: 30px;
    }
    .device-listing-select-device-title .title {
        font-size: 28px;
    }
    .device-listing-carousel-title-wrap .title {
        font-size: 26px;
    }
    .prod-tile-content .prod-tile-note {
        letter-spacing: 0.2px;
    }
}
/*** device listing ends ***/
/**********************************/

/**device details Lower payment**/
.range-slider-wrapper input[type=range].range-slider {
    margin: 10px 0;
    width: 100%;
}

.range-slider-wrapper .range-wrap {
    position: relative;
}

.range-slider-wrapper .range-slider-label {
    display: block;
    text-align: center;
}

.range-slider-wrapper input[type=range].range-slider {
    -webkit-appearance: none;
    width: 100%;
    height: 10px;
    background: #e1e1e1;
    outline: none;
    -webkit-transition: .2s;
    transition: opacity .2s;
    border-radius: 5px;
}

input[type=range].range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #ffffff;
    cursor: pointer;
    box-shadow: 0 6px 25px 0 rgba(0,0,0,0.2);
}

.graphical_ctrl_checkbox.mod .ctrl_element:after {
    left: 8px;
    top: 2px;
    width: 8px;
    height: 14px;
}

.rate-plan-box li {
    margin-top: 5px;
}

.rate-plan-box .icon {
    flex-basis: 32px;
    flex-grow: 0;
    flex-shrink: 0;
    text-align: center;
}

.rate-plan-box .current-plan {
    padding: 3px 8px;
    position: absolute;
    transform: translate(-50%,-50%);
    color: #ffffff;
    top: 0;
    left: 50%;
    font-size: 10px;
}

.tablist-pills-container {
    display: flex;
    font-size: 16px;
    justify-content: center;
}
.tablist-pills-container ul {
    background-color: #F4F4F4;
    border: 1px solid #FFFFFF;
    border-radius: 30px;
    display: flex;
    flex-direction: row;
    padding: 5px;
    list-style: none;
    margin: 0;
    color: #00549A;
    text-align: center;
}
.tablist-pills-container ul li {
    padding: 10px 25px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}
.tablist-pills-container ul li[aria-selected="true"] {
    border: 1px solid #E1E1E1;
    border-radius: 30px;
    background-color: #FFFFFF;
    box-shadow: 0 0 25px 0 rgba(0,0,0,0.12);
    color: #111;
}
.info-yellow-icon {
    color: #DFA32A;
}
.rate-plans-col-mar .rate-plan-box {
    margin: 0 7.5px;
}
.lower-payment-boxes{
    margin: 0 15px;
}
.price-cancel:before{
    content:" ";
    position:absolute;
    width:100%;
    top:50%;
    transform:translateY(-50%);
    height:1px;
    border:.5px solid #555555;
}
.device-deatils-margin{
    margin:0 40px;
}
/**device details Lower payment**/
/**confirmation**/
.two-col-with-border .col-sm-6:first-child{
    border-right:1px solid #e1e1e1;
}
/**confirmation**/

/**Change address modals**/
.change-address-modal .modal-footer {
    padding: 30px;
    background: #f0f0f0;
    border-top:1px solid #D4D4D4;
    display:block;
}
.modal.toaster .modal-dialog {
    position:absolute;
    top:90px;
    left:50%;
    transform:translateX(-50%);
    width:500px;
}
.modal.toaster .modal-body {
    padding: 15px;
    margin: 0;
}
.modal.toaster .modal-dialog, .modal.toaster .modal-content {
    border-radius: 2px;
    border-bottom: 2px solid #339043;
}
.modal.toaster .close {
    font-size: 10px;
    margin-top: -5px;
    margin-right:-5px;
}
/**Change address modals**/
/**css for tsliding tabs from shop.css**/
.tab-panels-container [role="tabpanel"][aria-hidden="true"]:not(.slick-slide) {
    display: none
}
/**css for tsliding tabs**/
/**css for price dock**/
.price-dock{   
    padding:15px 0 20px 0;
    width:100%;
}
.price-dock > div{
    padding:0 10px;
    border-right:1px solid #d4d4d4;
    text-align:center;
}
.price-dock .price , .price.txtHeavy{
    font-family: "bellslim_font_heavy",Helvetica,Arial,sans-serif;
}
.price-dock > div:last-child{
    border-right:none;
}
.price-dock.sticky {
    position: fixed;
    top: 0;
    left: 0;
    border-radius: 0 0 10px 10px;
    z-index: 50;
    border-bottom:1px solid #e1e1e1;
    display:none;
}
.validation-message {
    display: none;
}
.one-time-charges .table-footer {
    margin-left: -30px;
    width: calc(100% + 60px);
}
/**css for price dock**/
@media(min-width: 320px) and (max-width: 767.98px) {
 .rate-plan-box .ctrl_radioBtn .ctrl_element {
    top: 3px;
    left: 0;
}
.rate-plans-col-mar .rate-plan-box {
    margin: 0;
}

.lower-payment-boxes {
    margin: 0;
}

.device-deatils-margin {
    margin: 0;
}
.two-col-with-border .col-sm-6:first-child {
    border: none;
}

.modal .modal-dialog {
    height: auto;
    max-height: calc(100% - 45px);
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
}

.modal.toaster .modal-dialog {
    top: 42px;
    bottom: auto;
    width: calc(100% - 30px);
}
.one-time-charges .table-footer {
    margin-left: -15px;
    width: calc(100% + 30px);
}
}
@media(min-width:1200px) {
    .device-deatils-margin {
        margin: 0 135px;
    }
    .price-dock.sticky{
        width:1200px;
        left:50%;
        transform:translateX(-50%);
    }
}

