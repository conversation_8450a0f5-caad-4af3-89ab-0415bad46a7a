.mode_manitoba .global-navigation .connector-brand a:before {
    content: "\e91d";
}

.mode_aliant .global-navigation .connector-brand {
    margin-top: 0;
}

.mode_aliant .global-navigation .connector-brand a:before {
    content: "\ea17";
    font-size: 50px;
}

/*v1.1*/ 

/*START COVEO SEARCH ICON FONTS*/
@font-face{font-family:'bell-icon';src:url(../fonts/coveo-search-icons.eot?#iefix) format("embedded-opentype"),url(../fonts/coveo-search-icons.woff) format("woff"),url(../fonts/coveo-search-icons.ttf) format("truetype"),url(../fonts/coveo-search-icons.svg) format("svg");font-weight:400;font-style:normal}
.icon{font-style:normal;speak:none;font-weight:400;font-variant:normal;text-transform:none;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}
.icon:before{font-family:'bell-icon';position:relative;top:0px;}

/*Icons*/
.icon-voice-search:before {
    content: "\e91f";
}


header {
    background: #00549a;
}

.l2-separator {
    border-top: 1px solid #D4D4D4;
}

.global-navigation #connector-search [type="search"]::-ms-clear {
    display: none;
}

.global-navigation ::-webkit-search-cancel-button {
    -webkit-appearance: none;
}

.global-navigation .container {
    width: 960px;
}

.global-navigation a:focus,
.global-navigation .btn:focus,
.global-navigation li.ui-menu-item:focus {
    outline: -webkit-focus-ring-color auto 5px;
}

.global-navigation .caret {
    border-top: medium none;
}

.global-navigation .connector a, .global-navigation .connector a:link, .global-navigation .connector a:visited, .global-navigation .connector a:hover, .global-navigation .connector a:active {
    text-decoration: none;
}

/*Login area*/
.connector-logged-in-modal, .connector-login-modal {
    display: none;
    position: absolute !important;
    background: #fff;
    z-index: 30;
    padding: 20px;
    box-shadow: 0 0 40px rgba(0,0,0, .5);
}

.global-navigation.gn-shop .connector-logged-in-modal, .global-navigation.gn-shop .connector-login-modal {
    padding: 30px;
}

    .connector-logged-in-modal, .connector-login-modal.no-pad {
        padding: 0px;
    }

.icon-blue-circle-login {
    height: 48px;
    width: 48px;
    border-radius: 50%;
    background-color: #00549a;
    color: #fff;
    display: table-cell;
    vertical-align: bottom;
    text-align: center;
}

.iconleft-flyout {
    width: 58px !important;
    padding-right: 10px
}

.login-desktop-hidden {
    display: none !important
}

.log-in-form-chevron-position {
    position: absolute;
    right: 0;
    font-size: 14px;
    top: 4px
}


/* Start skip to main content*/
.global-navigation .skip-to-main-link:focus, .simple-blue-header .skip-to-main-link:focus {
    top: 0;
}

.global-navigation a.skip-to-main-link, .simple-blue-header a.skip-to-main-link {
    color: #ffffff;
}

.global-navigation .skip-to-main-link, .simple-blue-header .skip-to-main-link {
    display: inline-block;
    padding: 9px 12px;
    position: absolute;
    top: -50px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    text-decoration: none;
    border-bottom-right-radius: 8px;
    transition: top 0.3s ease-out;
    z-index: 3000;
    color: #fff;
    text-transform: uppercase;
    font-size: 11px;
    background: #2d2e33;
    text-decoration:underline
}

/* End skip to main content*/

/* Start Federal Bar Area */
.global-navigation .federal-bar {
    background: #2d2e33;
    height: 33px;
    padding: 9px 0;
    display: block;
}

.global-navigation .federal-bar-links a,
.global-navigation .federal-bar-links a:link,
.global-navigation .federal-bar-links a:visited {
    color: #babec2;
    text-decoration: none;
}

    .global-navigation .federal-bar-links a.active,
    .global-navigation .federal-bar-links a.active:link,
    .global-navigation .federal-bar-links a.active:visited,
    .global-navigation .federal-bar-links a:hover,
    .global-navigation .federal-bar-links a:focus {
        color: #fff;
        text-decoration: none;
    }

.global-navigation .federal-bar-store-locator {
    display: inline-block;
    position: relative;
}

.global-navigation .store-locator-section-links li .label-text {
    font-size: 13px;
    color: #555555;
    text-decoration: none;
    white-space: nowrap;
}

    .global-navigation .store-locator-section-links li .label-text:hover {
        color: #555555;
    }

.global-navigation .store-locator-section-links li:not(:first-child) {
    padding-top: 10px;
}

.global-navigation .federal-bar-store-locator-popup.federal-bar-links {
    width: 360px;
}

.global-navigation .federal-bar-store-locator-popup {
    display: none;
    position: absolute;
    right: -65px;
    background: #fff;
    z-index: 100;
    padding: 30px;
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.5);
    top: 26px;
    width: 360px;
    text-transform: none;
}

.global-navigation.gn-shop .federal-bar-store-locator-popup {
    right: -55px;
}

.global-navigation.gn-shop .search-filter-container label{
    margin-bottom:10px;
    font-family: Helvetica, Arial, sans-serif;
    font-size: 14px;
    color: #111;
}

.global-navigation.gn-shop .filter-stores-title {
    font-weight: bold;
    margin-bottom: 15px;
    font-size:14px;
    color:#111
}

.global-navigation.gn-shop .connector-settings a:hover {
    text-decoration: underline;
}

.global-navigation
.federal-bar-store-locator.active
.federal-bar-store-locator-popup {
    display: block;
}

.global-navigation
.federal-bar-store-locator
.federal-bar-store-locator-popup.federal-bar-links.caret::after {
    left: calc(50% + 75px);
    top:3px
}

.global-navigation
.federal-bar-store-locator-popup.federal-bar-links
.store-locator-section {
    display: none;
}

.global-navigation .federal-bar-store-locator-popup .store-locator-section {
    display: block;
}

.global-navigation
.federal-bar-store-locator-popup.federal-bar-links
.store-locator-section-links {
    display: block;
}

.global-navigation
.federal-bar-store-locator-popup
.store-locator-section-links {
    display: none;
}

.global-navigation .federal-bar ul,
.global-navigation .connector ul {
    padding: 0;
    margin: 0;
}

    .global-navigation .federal-bar ul > li,
    .global-navigation .connector ul > li {
        list-style-type: none;
    }

.global-navigation .federal-bar-mobile .custom-select-trigger > .icon {
    -webkit-transform: translateY(-55%) rotate(90deg);
    -ms-transform: translateY(-55%) rotate(90deg);
    transform: translateY(-55%) rotate(90deg);
    color: #97989c;
}

.global-navigation .checkboxes_absolute .checkbox {
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 3px;
    box-shadow: inset 1px 1px 0 0 rgba(0, 0, 0, 0.1);
    display: inline-block;
    width: 22px;
    height: 22px;
    border: 1px solid #ccc;
    background-color: #fff;
    transition: background-color 10ms cubic-bezier(0.17, 0.67, 0.83, 0.67);
}

.global-navigation .radios .label,
.checkboxes .label {
    margin-left: 0;
    position: relative;
    color: #212121;
    font-weight: normal;
    vertical-align: top;
    cursor: default;
}


.global-navigation label {
    margin: 0;
}

.global-navigation .radios_absolute.radios .label-text,
.checkboxes_absolute.checkboxes .label-text {
    padding-left: 35px;
    -webkit-transform: translateY(2px);
    -ms-transform: translateY(2px);
    transform: translateY(2px);
}

.global-navigation div.checkboxes label.active .checkbox {
    background-color: #003778;
    border-color: #003778;
}

.global-navigation .radios .label.focused .radio,
.global-navigation .checkboxes .label.focused .checkbox {
    outline: 0;
    box-shadow: 0 0 3px 2px rgba(178, 209, 228, 1);
}

.global-navigation .checkboxes .label:not(.disabled) {
    cursor: pointer;
}

.global-navigation .federal-bar-store-locator-popup a.button {
    box-sizing: border-box;
    display: inline-block;
    position: relative;
    margin: 15px 0;
    padding: 10px 36px;
    vertical-align: middle;
    background-color: #003778;
    font-size: 16px;
    line-height: 1;
    text-align: center;
    text-decoration: none !important;
    color: #fff;
    border: 2px solid #003778;
    border-radius: 24px;
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.55, 0, 0.1, 1);
    width: 100%;
}

    .global-navigation .federal-bar-store-locator-popup a.button:hover,
    .federal-bar-store-locator-popup a.button:focus {
        background-color: #00549a;
        border-color: #00549a;
    }

footer .federal-bar-link-provinces {
    position: absolute;
    top: 30px;
    z-index: 100;
    width: 250px;
    display: none;
    background-color: white;
    padding: 15px 10px;
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.3);
}
footer.gf-shop .federal-bar-link-provinces {
    right:-37px
}

footer .caret {
    border-top: medium none;
}

footer .federal-bar-link-provinces.caret:after {
    /*border-width: 9px;*/
    transform: translateX(20px) translateY(-100%);
    -webkit-transform: translateX(20px) translateY(-100%);
    -ms-transform: translateX(20px) translateY(-100%);
}

footer .footer-link-provinces.federal-bar-link-provinces.caret:after {
    /*border-width: 9px;*/
    transform: translateX(35px) translateY(-100%);
    -webkit-transform: translateX(35px) translateY(-100%);
    -ms-transform: translateX(35px) translateY(-100%);
}
footer.gf-shop .footer-link-provinces.federal-bar-link-provinces.caret:after {
    /*border-width: 9px;*/
    transform: translateX(71px) translateY(-100%);
    -webkit-transform: translateX(71px) translateY(-100%);
    -ms-transform: translateX(71px) translateY(-100%);
}

.footer-link-provinces {
    right: -75px;
}


footer .footer-head .federal-bar-link-provinces.caret:after {
    border-width: 9px;
    transform: translateX(15px) translateY(-100%);
    -webkit-transform: translateX(15px) translateY(-100%);
    -ms-transform: translateX(15px) translateY(-100%);
}

.global-navigation #connector-search #voice_search:focus:before {
    box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}

.global-navigation #connector-search {
    width: 210px;
}

    .global-navigation #connector-search [type="reset"],
    #connector-search [type="submit"],
    #connector-search #voice_search {
        position: absolute;
        right: 5px;
        left: auto;
        top: 0;
        padding: 0;
        border: 0;
        background: none;
    }

    .global-navigation #connector-search [type="reset"] {
        right: 30px;
        width: 40px;
        display: none;
    }

        .global-navigation #connector-search [type="reset"] .icon {
            opacity: .5;
            font-size: 18px;
        }

        .global-navigation #connector-search [type="reset"].active {
            display: block;
        }

.global-navigation .header-preferences {
    display: inline-block;
}

.bell-custom-select > select.js-province-mobile {
    opacity: 1;
    background: transparent;
    border: none;
    padding: 14px;
}

.global-navigation .footer-header-preferences-buttons {
    position: relative;
}

    .global-navigation .footer-header-preferences-buttons select {
        left: 0;
        opacity: 0;
        position: absolute;
        top: 0;
        width: 25px;
        display: none;
    }

.global-navigation .federal-bar-link-provinces .label,
footer .federal-bar-link-provinces .label {
    text-transform: initial;
    padding: 5px 5px 3px 7px;
    text-transform: none;
    cursor:pointer;
}
.global-navigation .federal-bar-link-provinces .label.disabled,
footer .federal-bar-link-provinces .label.disabled {
    cursor:default;
}

.global-navigation .radios:not(.radios_absolute) .label-text,
.global-navigation .checkboxes:not(.checkboxes_absolute) .label-text,
footer .radios:not(.radios_absolute) .label-text,
footer .checkboxes:not(.checkboxes_absolute) .label-text {
    -webkit-transform: translateY(-5px);
    -ms-transform: translateY(-5px);
    transform: translateY(-5px);
    padding-left: 4px;
}

.global-navigation .federal-bar-link-provinces .label .label-text,
footer .federal-bar-link-provinces .label .label-text {
    font-size: 13px;
    color: #555555;
}

.global-navigation .federal-bar-link-provinces .label.active .label-text,
footer .federal-bar-link-provinces .label.active .label-text {
    color: #00549a;
    font-weight: bold;
}

.global-navigation .radios .label-text,
.global-navigation .checkboxes .label-text,
footer .radios .label-text,
footer .checkboxes .label-text {
    display: inline-block;
    line-height: 1;
}

.global-navigation .federal-bar-link-provinces .label:hover,
.global-navigation .federal-bar-link-provinces .label:focus,
footer .federal-bar-link-provinces .label:hover,
footer .federal-bar-link-provinces .label:focus {
    background: #e1e1e1;
    border-radius: 3px;
}

.global-navigation .federal-bar-link-provinces .checkbox,
footer .federal-bar-link-provinces .checkbox {
    border: none;
    background-color: transparent;
    box-shadow: none;
}

    footer .federal-bar-link-provinces .checkbox:after {
        color: #00549a;
        background: none;
        font-size: 12px;
        font-weight: bold;
    }

.global-navigation .radios label.active .radio:after, .global-navigation .checkboxes label.active .checkbox:after {
    opacity: 1;
    height: 10px;
    width: 10px;
}

.global-navigation .radio:after, .global-navigation .checkbox:after,
footer .radio:after, footer .checkbox:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
    opacity: 0;
    transition: opacity 10ms cubic-bezier(.17, .67, .83, .67);
    background-color: #fff;
    border-radius: 50%;
}

.global-navigation a.active .checkbox:after,
footer a.active .checkbox:after {
    opacity: 1;
    top: 53%;
    color: #00549a;
}

.global-navigation .checkbox:after,
footer .checkbox:after {
    font-family: 'bell-icon';
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: '\e603';
    color: #fff;
    background-color: transparent;
    border-radius: inherit;
    font-size: 11px;
}

.global-navigation .radio,
.global-navigation .checkbox,
footer .radio,
footer .checkbox {
    display: inline-block;
    position: relative;
    width: 22px;
    height: 22px;
    border: 1px solid #ccc;
    background-color: #fff;
    border-radius: 50%;
    transition: background-color 10ms cubic-bezier(0.17, 0.67, 0.83, 0.67);
}

footer .footerLinkSeparator1:not(:last-child):after {
    content: " | ";
    font-size: 14px;
    color: #b4b4b4;
    position: absolute;
    margin-left: 10px;
}

footer .footerLinkSeparator1:not(:first-child) {
    margin-left: 20px;
}

/* End of Federal Bar Area */
/* Start of Connector */
.global-navigation .connector {
    position: relative;
    background: #00549a;
}

.global-navigation .radios input[type="radio"],
.global-navigation .checkboxes input[type="checkbox"] {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none;
}

/* End of Connector */
/* Start Right Connecter Area */
.global-navigation .connector-cart-button, .global-navigation .connector-brand {
    font-family: 'bell-icon';
    font-style: normal;
    speak: none;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.global-navigation .connector-brand, .global-navigation .connector-brand-show {
    padding-left: 0;
    margin-left: 0;
}

.global-navigation .connector-area {
    background: #00549a;
    border-bottom: 1px solid #003778;
}

.global-navigation .connector .connector-brand a {
    color: #fff;
    top: 4px;
}

.global-navigation .connector-brand a:before {
    content: '\e600';
    line-height: 1.5;
}

.global-navigation .aliant .connector-brand a:before {
    line-height: 1.2;
}

.global-navigation.bce .connector-brand a:before {
    content: "\eaa0";
    font-family: 'bell-icon2';
    font-size: 32px;
}

footer .img_recaptcha{
    height: 60px;
    width:256px;
}

@media (max-width: 991.98px) {
header .skip-to-main-link, 
footer .skip-to-main-link {
    display:none!important
}

    .global-navigation.bce .connector-brand:after {
        content: "\eaa0";
        font-family: 'bell-icon2';
        font-size: 25px;
    }
}

.global-navigation .connector-area.active div > a {
    color: #fff;
}

.global-navigation .connector-search-wrap {
    float: left;
    margin-top: 0px;
    margin-right: 10px;
    position: relative;
}

.global-navigation .hideAutocomplete.active:focus {
    opacity: 1;
}

.global-navigation .connector-cart-button {
    padding: 0;
    margin: 0;
    font-size: 27px;
    bottom: 0;
    color: #ffffff;
}

    .global-navigation .connector-cart-button:hover {
        color: #c2cedf;
    }

.global-navigation #connector-search [type="reset"],
.global-navigation #connector-search [type="submit"] {
    cursor: pointer;
}

    .global-navigation #connector-search [type="submit"]:after {
        content: "\e615";
        -webkit-transform: translateX(-50%) translateY(-50%);
        -ms-transform: translateX(-50%) translateY(-50%);
        transform: translateX(-50%) translateY(-50%);
        font-size: 16px;
        color: #fff;
    }

    .global-navigation #connector-search [type="reset"]:after,
    #connector-search [type="submit"]:after {
        font-family: "bell-icon";
        line-height: 1;
    }

    .global-navigation #connector-search [type="reset"]:after,
    #connector-search [type="reset"]:before,
    #connector-search [type="submit"]:after {
        display: block;
        position: absolute;
        top: 50%;
        left: 50%;
    }

    .global-navigation #connector-search [type="reset"] .icon {
        color: #fff;
    }

    .global-navigation #connector-search [type="reset"]:focus .icon {
        opacity: 1;
    }

/* End Right Connecter Area */

.global-navigation #connector-search [type="search"] {
    display: inline-block;
    border-radius: 18px;
}

.global-navigation input[type="search"]::-webkit-input-placeholder {
    color: #999999;
}

.global-navigation input[type="search"]::-moz-placeholder {
    color: #999999;
}

.global-navigation input[type="search"]:-ms-input-placeholder {
    color: #999999;
}

.global-navigation input[type="search"]:-moz-placeholder {
    color: #999999;
}

.global-navigation .connector-area div > a {
    color: #c2cedf;
    display: inline-block;
    margin-right: 15px;
    margin-left: 10px;
    top: -1px;
    position: relative;
}

    .global-navigation .connector-area div > a:hover {
        color: #ffffff;
    }

    .global-navigation .connector-area div > a span {
        letter-spacing: -1px;
        line-height: 32px;
    }

.global-navigation .shopping-cart-button.active .shopping-cart-popup {
    display: block;
}

.global-navigation .shopping-cart-popup {
    right: -15px;
    top: 40px;
}

.global-navigation .popup.caret:after {
    border-width: 12px;
    left: calc(50% + 87px);
}
.global-navigation.gn-shop .popup.caret:after {
    left: calc(50% + 253px);
}

.global-navigation .popup.province-selector.caret:after {
    border-width: 12px;
    left: calc(50%);
    top: 3px
}
.global-navigation.gn-shop .popup.province-selector.caret:after {
    left: calc(50% + 18px);
}

.global-navigation .federal-bar-link-small-business, .global-navigation .federal-bar-link-enterprise, .global-navigation .shopping-cart-popup, .global-navigation .login-footer-popup {
    position: absolute;
    top: 45px;
    z-index: 100;
    width: 230px;
    display: none;
    background-color: white;
    padding: 20px;
    box-shadow: 0 0 40px rgba(0,0,0, .3);
}

.global-navigation .caret, .federal-bar-link-provinces, .global-navigation .federal-bar-store-locator-popup, .global-navigation .connector-logged-in-modal.active, .global-navigation .connector-login-modal.active {
    height: auto;
}

.global-navigation .shopping-cart-popup .icon-circle-large {
    left: 50%;
    transform: translateX(-50%);
}


.global-navigation a,
.global-navigation a:hover {
    color: #00549a;
}

body.voice-search-enabled #connector-search #voice_search {
    display: block;
}

.global-navigation #connector-search [type="submit"] {
    width: 40px;
}

.global-navigation #connector-search #voice_search {
    right: 30px;
}

    .global-navigation #connector-search #voice_search:after {
        content: "?";
        -webkit-transform: translateX(-50%) translateY(-50%);
        -ms-transform: translateX(-50%) translateY(-50%);
        transform: translateX(-50%) translateY(-50%);
        font-size: 18px;
        color: #003778;
    }

.global-navigation #connector-search [type="search"] {
    position: relative;
    width: 100%;
    padding-right: 45px;
    padding-left: 20px;
    border: 0;
    background-color: #013678;
    color: #fff;
}

.global-navigation .connector-login-button {
    max-width: 230px;
}


.global-navigation #connector-search [type="search"]::placeholder {
    color: #fff;
}

.global-navigation #connector-search [type="search"], .global-navigation #connector-search [type="reset"], .global-navigation #connector-search [type="submit"] {
    height: 36px;
}

.global-navigation .footer-login-btn, .global-navigation .connector-login-button {
    white-space: normal;
}

.global-navigation .menu-flyout-visible .sub-nav-level-1 {
    font-size: 15px;
}

.global-navigation .menu-flyout-visible .sub-nav-item ul > li a {
    font-size: 13px;
}

.global-navigation .menu-flyout-visible .sub-nav-level-1:after {
    content: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAANCAYAAACUwi84AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyRpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoTWFjaW50b3NoKSIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDozQTdGOTE3RDZFMDkxMUU4ODU1MkE2RDY0QTMwNUEyMyIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDozQTdGOTE3RTZFMDkxMUU4ODU1MkE2RDY0QTMwNUEyMyI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjNBN0Y5MTdCNkUwOTExRTg4NTUyQTZENjRBMzA1QTIzIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjNBN0Y5MTdDNkUwOTExRTg4NTUyQTZENjRBMzA1QTIzIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Vo8LxgAAAZJJREFUeNpMUEsvA2EUvfP55q1GR6tECYJ6rTyasJCQSCxEIiQeYWHDmv9gKSzEokIkrCQWVlJBjNZKKhYI2oSUeCeD6mg7046vXnGX955z7jmHmlw7hFdNbwYKkCwyfi2eBIqCv0HRuOGe2zzdn/ee+qLxZEMWT4Np/gOkTJPhGQxqWIXl3YuNSEyvEjlMQN8oZBVZf3djsTu/LPft6vzetrB95ovpqRoLz3wpoWTKBC1hHNSX2buKyh3GTfApZ0W58H4kjBKBxYDSMnoyBWShuF25rc4Kx2Pw/KFgSQkqxF91RkvvKPA0BtnCQqFNDFcVyMdHty9Dj6FnKYKgAxnkBYMR2AggW2BR4PJpOKpqINozobbQOoXTkdO5WToD1gPhVcUX6uFkAfraXGN2C+dBCFEgCQzsnNwtp4+0hYOBVtdYnsR7Ih8JQMSl5D26XvTuhYZwFg+D7ZXjDonzqNE4USb1vsf0uq1AeIRUDf1tFRNOWZh50wjzp2/M0tjf2VQ6y3NYc1qFafU98cX8nU8BBgAJEJrPXAOh1QAAAABJRU5ErkJggg==);
}

.global-navigation .menu-flyout {
    display: none;
}

    .global-navigation .menu-flyout.menu-flyout-visible {
        display: block;
    }

.global-navigation .menu-flyout-visible .sub-nav-root li > ul.sub-nav-group {
    display: none;
}

.global-navigation .bellSlimSemibold-Nav {
    border-right: solid 1px #004978;
    border-left: solid 1px #004978;
    margin-top: 3px;
    padding-left: 9px;
    letter-spacing: -1px;
}

.global-navigation .connector-settings-mobile > li {
    background: #00549a;
    border-bottom: 1px solid #003778;
    position: relative;
}

    .global-navigation .connector-settings-mobile > li > div > .icon {
        position: absolute;
        color: #fff;
        font-size: 22px;
        top: 9px;
        left: 18px;
        cursor: pointer;
        pointer-events: none;
    }

    .global-navigation .connector-settings-mobile > li > div > a {
        display: block;
        padding: 10px 20px 11px 49px;
        font-size: 17px;
    }

.global-navigation .preferences-section {
    font-size: 12px;
    text-transform: uppercase;
}

.global-navigation .federal-bar-mobile .custom-select-trigger-label {
    margin: 0 25px 0 0;
}

.preferences-section {
    font-size: 12px;
    text-transform: uppercase;
}

.custom-select-mobile-federal-bar {
    display: inline-block;
    position: relative;
    z-index: 2;
    background: none;
    border: 0;
    height: auto;
}

    .custom-select-mobile-federal-bar > select {
        display: block;
        position: absolute;
        z-index: 3;
        top: 0;
        left: 0px;
        width: 77px;
        height: 100%;
        opacity: 0;
        cursor: pointer;
        -webkit-appearance: none;
    }

.custom-select-trigger.mobile-federal-province > .icon {
    top: 51% !important
}




/* Start Logged In */

.global-navigation .connector-area a.active span {
    font-family: bellslim_semiboldregular;
    color: #fff;
}

    .global-navigation .connector-area a.active span::after {
        background-color: #fff;
        bottom: -2px;
        content: "";
        display: block;
        height: 2px;
        left: 0;
        position: absolute;
        right: 0;
    }

.global-navigation .connector-active-secondary-nav li:hover .secondary-nav-dropdown {
    display: block !important;
}

.global-navigation .connector-active-secondary-nav li.active a.trigger-dropdown span:after {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    right: 0;
    bottom: -7px;
    height: 2px;
    background-color: #fff;
}

.global-navigation .connector-active-secondary-nav a:focus {
    outline: 1px solid #2672cb;
}
/* End Logged In */

.global-navigation #initial-lang-region, .global-navigation #initial-lang-reigon-backdrop {
    display: none;
    position: fixed;
}

.global-navigation .menu-flyout-overlay {
    background-color: transparent;
    left: 0;
    width: 100vw;
    height: 100vh;
    position: fixed;
    z-index: 19;
    display: none;
}

/* Connector - settings */
.connector-settings-mobile > li {
    background: #00549a;
    border-bottom: 1px solid #003778;
    position: relative;
}

    .connector-settings-mobile > li > a {
        display: block;
        padding: 15px 20px;
        font-size: 17px;
        padding-left: 50px;
    }

    .connector-settings-mobile > li > .icon {
        position: absolute;
        color: #fff;
        font-size: 22px;
        top: 9px;
        left: 18px;
    }

.button.connector-log-out-button,
.button.connector-profile-button,
.button.connector-login-button {
    margin: -2px 0 0;
    padding: 8px 20px;
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    float: left;
}


.button.connector-log-out-button,
.button.connector-profile-button {
    margin: 7px 0 0;
    border-radius: 0;
    padding: 3px 12px;
    max-width: 145px;
    color: #fff;
}

    .button.connector-log-out-button:hover,
    .button.connector-log-out-button:focus,
    .button.connector-profile-button:hover,
    .button.connector-profile-button:focus {
        text-decoration: underline;
        color: #fff;
    }

.button.connector-profile-button {
    max-width: 120px;
}

.button.connector-log-out-button,
.button:hover.connector-log-out-button {
    border-left: 1px solid #c2cedf;
}


.connector-login-modal.user-control-menu {
    padding: 0px;
}

.connector-login-modal a.more-link,
.connector-login-modal p a,
.connector-login-modal p a:link,
.connector-login-modal p a:visited,
.connector-login-modal p a:hover,
.connector-login-modal p a:active,
.connector-logged-in-modal p a,
.connector-logged-in-modal p a:link,
.connector-logged-in-modal p a:visited,
.connector-logged-in-modal p a:hover,
.connector-logged-in-modal p a:active {
    color: #00549a;
    text-decoration: initial;
}


    .connector-login-modal p a:hover,
    .connector-logged-in-modal p a:hover {
        color: #00549a;
        text-decoration: underline;
    }


.connector-login-modal .form-control,
.connector-logged-in-modal .form-control {
    background: #fff;
}

.connector-login-modal p,
.connector-logged-in-modal p {
    margin-top: 5px;
    margin-bottom: 5px;
    font-size: 12px;
}

.connector-login-modal a.more-link {
    margin-left: 10px;
}

    .connector-login-modal a.more-link.no-margin {
        margin-left: 0px;
    }

.connector-login-modal.user-control-menu {
    width: 270px;
    top: 40px;
    right: -48px;
    border: 0
}

    .connector-login-modal.user-control-menu a {
        padding: 12px 15px 12px 8px;
        display: block;
        border-bottom: 1px solid #d4d4d4
    }

        .connector-login-modal.user-control-menu a:hover, .connector-login-modal.user-control-menu a:focus {
            background-color: #f4f4f4
        }

.loggedin-selection .my-services-login {
    color: #555;
    font-size: 14px;
}

.loggedin-selection:hover .my-services-login, .loggedin-selection:focus .my-services-login {
    color: #00549a;
    text-decoration: underline
}

.loggedin-selection .icon-pos-logged-in {
    display: table;
    height: 100%;
    font-size: 28px;
    width: 40px;
    text-align: center;
    position: relative;
    top: 2px;
    margin: -5px 0;
}

.login-button.user-options.aliant-user .loggedin-selection .icon-pos-logged-in {
    text-align: left;
    width: 34px;
}

.login-button.user-options.aliant-user .loggedin-selection .my-services-login {
    color: #00549a;
}

.login-ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 120px;
    display: inline-block
}

    .login-ellipsis ~ .icon.icon-chevron.chevron-down {
        display: inline-block;
        position: relative;
        top: 0px
    }

.lob-nickname {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 160px;
    display: block
}

.lob-nickname-mobile {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: block
}

.connector-login-modal {
    width: 350px;
    top: 54px;
    right: 0
}

    .connector-login-modal::before {
        left: calc(50% + 30px);
    }

    .connector-login-modal.aliant {
        width: 650px;
        top: 67px;
    }

.connector-logged-in-modal {
    width: 250px;
    top: 67px;
}


.login-aliant-divider {
    height: 360px;
}

.border-right-1 {
    border-right: 1px solid #dadada;
}

.connector-logged-in-modal.active,
.connector-login-modal.active {
    display: block;
}

.connector-logged-in-modal .connector-login-modal_title,
.connector-login-modal .connector-login-modal_title {
    color: #000;
    margin-bottom: 20px;
    margin-bottom: 20px;
    margin-top: 10px;
    font-size: 23px;
}

.connector-logged-in-modal .form-label,
.connector-login-modal .form-label {
    font-weight: normal;
}


.connector-cart-button {
    background: none;
    border: none;
    color: #fff;
    position: relative;
    padding: 13px;
    line-height: 1;
    font-size: 22px;
}

    .connector-cart-button:hover {
        color: #c2cedf;
        text-decoration:none!important
    }

.connector-cart-count {
    position: absolute;
    height: 16px;
    width: 16px;
    font-size: 10px;
    background-color: #08affd;
    color: #fff;
    border-radius: 50%;
    box-shadow: 0 0 5px rgba(0,0,0, .3);
    text-align: center;
    line-height: 2;
    display: block;
    font-family: Arial, Helvetica, sans-serif;
    line-height:16px
}

.connector-settings .connector-cart-count {
    right: -6px;
    top: 0px;
}

.connector-settings-mobile .connector-cart-count {
    left: 30px;
    top: 7px;
}

.connector-login-modal.aliant {
    right: 0px;
    top: 52px;
}
.global-navigation.gn-shop .connector-login-modal.aliant {
    right: 0px;
    top: 58px;
}

    .connector-login-modal.aliant.login-moi {
        right: 1px;
        top: 49px;
    }

.aliant-login-modal-mobile {
    margin-top: 55px;
}

.left-side-fly-out h3, .right-side-fly-out h3 {
    font-size: 21px
}

.aliant.caret_top-right:after,
.aliant.caret_top-right.caret_outline:before {
    left: calc(50% + 255px);
}

.connector-logged-in.aliant.caret_top-right:after,
.connector-logged-in.aliant.caret_top-right.caret_outline:before {
    left: calc(50% + 275px);
}

.global-navigation #connector-search-button {
    display: block;
    position: absolute;
    top: 0;
    right: 50px;
    border: 0;
    background: none;
    font-size: 19px;
    font-style: normal;
    speak: none;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #fff;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin: 8px 10px;
    transition: background-color .25s cubic-bezier(.55,0,.1,1);
    padding: 0;
}

    .global-navigation #connector-search-button:focus {
        outline-color: #4d90fe !important;
    }

.global-navigation #topNavSearch:focus {
    outline-color: #4d90fe !important;
}



.global-navigation .connector-nav-open-button {
    z-index: 60;
    border: 0;
    color: #fff;
    background: none;
    font-size: 20px;
    position: absolute;
    right: 0;
    top: 0;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    margin: 8px 5px;
    padding: 0;
}

    .global-navigation .connector-nav-open-button .icon-mobile-menu {
        display: inline-block;
    }

    .global-navigation .connector-nav-open-button .icon-plus {
        display: none;
    }

.global-navigation #connector-search {
    position: relative;
    width: 99%;
    height: 100%;
}

.global-navigation #connector-search, .global-navigation #connector-search-cancel {
    display: table-cell;
}

.global-navigation .menu-flyout-visible .sub-nav-item .sub-nav-level4 li > a {
    font-size: 14px;
    padding-left: 20px;
    padding-right: 20px;
}

/*footer styles*/
.footer-language-pref, .social-links {
    float: right;
}

.social-links {
    display: flex;
    align-items: center;
}

.footer-main-links-lobs li {
    padding: 12px 20px;
    position: relative;
}

    .footer-main-links-lobs li.active {
        background-color: #00549a;
    }

        .footer-main-links-lobs li.active a {
            color: #fff;
        }

        .footer-main-links-lobs li.active:after {
            -moz-border-bottom-colors: none;
            -moz-border-left-colors: none;
            -moz-border-right-colors: none;
            -moz-border-top-colors: none;
            border-color: rgba(0, 84, 154, 0) rgba(0, 84, 154, 0) rgba(0, 84, 154, 0) #00549a;
            border-image: none;
            border-style: solid;
            border-width: 28px;
            content: " ";
            height: 0;
            pointer-events: none;
            position: absolute;
            right: -56px;
            width: 0;
            top: 50%;
            margin-top: -29px;
        }

.custom-select-trigger-footer {
    border: 0px solid #fff;
    background-color: transparent;
}

.footer-language-pref-mobile {
    display: none;
}

.footer-icon {
    height: 48px;
    width: 48px;
    font-size: 46px;
    background-color: #00549a;
    color: #fff;
    border-radius: 50%;
    display: inline-block;
    padding: 5px 0px 0px 0px;
    margin-left: -4px;
}

a .footer-icon:hover, a .footer-icon-social:hover {
    background-color: #003778;
}

a .footer-icon-social.footer-icon-social-linkedIn:hover,
a .footer-icon-social.footer-icon-social-Youtube:hover {
    background-color: #fff;
}

    a .footer-icon-social.footer-icon-social-linkedIn:hover i,
    a .footer-icon-social.footer-icon-social-Youtube:hover i {
        color: #003778;
    }

.footer-icon.white-o {
    background-color: #fff;
    color: #00549a;
    font-size: 26px;
    padding: 8px 0px 0px 0px;
}

a .footer-icon.white-o:hover, a .footer-icon-social.white-o:hover {
    background-color: #b3c4d8;
}

footer .footer-sub-links li.active a {
    text-decoration: underline;
}

.footer-icon-social {
    height: 48px;
    width: 48px;
    color: #fff;
    border-radius: 50%;
    display: inline-block;
}

    .footer-icon-social.white-o {
        background-color: #fff;
        color: #00549a;
    }

    .footer-icon-social .icon-envelope, .footer-icon-social .icon-youtube {
        top: 5px;
        left: 11px;
    }

    .footer-icon-social .icon-linkedin {
        top: 9px;
        left: 13px;
    }
    .footer-icon-social .icon-linked-in-logo {
        margin-top: -3px;
    }

    .footer-icon-social .icon-facebook {
        top: 10px;
        left: 14px;
    }

    .footer-icon-social .icon-twitter {
        top: 10px;
        left: 14px;
    }

    .footer-icon-social .icon-blog-en, .footer-icon-social .icon-blog-fr {
        top: 5px;
        left: 7px;
    }

    .footer-icon-social .icon-linkedin_bg {
        font-size: 48px;
        top: -8px;
    }

    .footer-icon-social .icon-youtube_bg {
        font-size: 48px;
        top: -8px;
    }

.footer-icon .icon-o.icon-o-handset, .footer-icon .icon-o.icon-o-location {
    top: -5px;
    left: 1px;
}

.footer-icon .icon-o-chat-bubble {
    top: -3px;
    left: 2px;
}

footer input[type="search"]::-webkit-search-decoration,
footer input[type="search"]::-webkit-search-cancel-button,
footer input[type="search"]::-webkit-search-results-button,
footer input[type="search"]::-webkit-search-results-decoration {
    -webkit-appearance: none;
}

.footer-icon-label {
    top: -15px;
    padding-left: 10px;
}

    .footer-icon-label.white-label {
        top: -2px;
        padding-left: 10px;
    }

.search-bar-footer [type="search"]::-ms-clear {
    display: none;
}

.search-bar-footer [type="reset"] {
    position: relative;
    float: right;
    margin-top: -26px;
    background: none;
    border: 0;
    right: 40px;
    width: 40px;
    display: none;
}

.search-bar-footer [type="submit"] {
    position: relative;
    margin-top: -53px;
    border: 0;
    float: right;
}

    .search-bar-footer [type="reset"]:after,
    .search-bar-footer [type="reset"]:before,
    .search-bar-footer [type="submit"]:after {
        display: block;
        position: absolute;
        top: 50%;
        left: 0%;
    }

    .search-bar-footer [type="reset"]:after,
    .search-bar-footer [type="submit"]:after {
        font-family: 'bell-icon';
        line-height: 1;
    }

.search-bar-footer [type="reset"]:focus .icon {
    opacity: 1;
}

.search-bar-footer [type="reset"] .icon {
    color: #bbb;
    font-size: 18px;
}

.search-bar-footer .search-btn {
    background: none;
}

.search-bar-footer input[type="search"]::-webkit-input-placeholder {
    color: #787878;
}

.search-bar-footer input[type="search"]::-moz-placeholder {
    color: #787878;
}

.search-bar-footer input[type="search"]:-ms-input-placeholder {
    color: #787878;
}

.search-bar-footer input[type="search"]:-moz-placeholder {
    color: #787878;
}

.no-break-white-space {
    white-space: nowrap;
}


/* search bar autocomplete*/
.search-bar-footer-wrap .caret_top-lg::after,
.search-bar-footer-wrap .caret_top-lg.caret_outline::before,
.search-bar-footer-wrap .caret_bottom-lg::after,
.search-bar-footer-wrap .caret_bottom-lg.caret_outline::before {
    left: 70px;
    border-width: 22px;
}

.search-bar-footer-wrap .ui-autocomplete {
    display: block;
    float: none;
    top: 215px !important;
    right: auto;
    bottom: auto;
    left: auto;
    padding: 0px;
    transition: height .35s cubic-bezier(.55,0,.1,1), padding .35s cubic-bezier(.55,0,.1,1);
    background-color: #fff;
    box-shadow: 0 0 40px rgba(0,0,0, .3);
    position: absolute;
}

.search-bar-footer-wrap ul.ui-autocomplete > li.ui-menu-item {
    padding: 10px 20px;
    list-style: none;
    cursor: pointer;
}

    .search-bar-footer-wrap ul.ui-autocomplete > li.ui-menu-item > a.ui-corner-all {
        text-decoration: none;
        color: #555555;
        cursor: pointer;
        display: block;
    }

    .search-bar-footer-wrap ul.ui-autocomplete > li.ui-menu-item:hover {
        background-color: #e1e1e1;
    }

.search-bar-footer-wrap .ui-autocomplete-term {
    font-weight: bold;
}

.search-bar-footer-wrap .ui-autocomplete:empty {
    height: 0;
    padding-top: 0;
    padding-bottom: 0;
}

    .search-bar-footer-wrap .ui-autocomplete:empty:after {
        content: none;
    }

.search-bar-footer-wrap .ui-menu-item,
.search-bar-footer-wrap .ui-menu-item > a {
    color: #000;
}

.search-bar-footer-wrap .ui-menu-item {
    margin: 2px -4px;
}

    .search-bar-footer-wrap .ui-menu-item > a:hover,
    .search-bar-footer-wrap .ui-menu-item > a:active {
        background-color: #e2e2e2;
    }

    .search-bar-footer-wrap .ui-menu-item .ui-autocomplete-term {
        font-weight: bold;
    }

footer .l-height-26 {
    line-height: 26px;
}


/*tooltips*/
.tool-tip {
    color: #555;
    background-color: #fff;
    display: none;
    opacity: 0;
    z-index: 0;
    padding: 10px;
    position: absolute;
    margin-left: -55px;
    -webkit-box-shadow: 0px 0px 19px 10px rgba(0,0,0,0.2);
    -moz-box-shadow: 0px 0px 19px 10px rgba(0,0,0,0.2);
    box-shadow: 0px 0px 19px 10px rgba(0,0,0,0.2);
}

footer .tool-tip,
footer .tool-tip.top {
    left: 50%;
}

    footer .tool-tip:after {
        position: absolute;
        left: 34px;
        bottom: -33px;
        margin-left: 0px;
        content: ' ';
        border: 18px solid transparent;
        border-top-color: #fff;
    }
/* on hover of element containing tooltip default*/

footer *:not(.on-focus):hover > .tool-tip,
footer .on-focus input:focus + .tool-tip {
    display: block;
    opacity: 1;
}
/* tool tip slide out */

footer *:not(.on-focus) > .tool-tip.slideIn,
footer .on-focus > .tool-tip {
    /*display: block;*/
}

.on-focus > .tool-tip.slideIn {
    z-index: -1;
}

footer .on-focus > input:focus + .tool-tip.slideIn {
    z-index: 1;
}

    footer *:not(.on-focus):hover > .tool-tip.slideIn,
    footer *:not(.on-focus):hover > .tool-tip.slideIn.top,
    .footer on-focus > input:focus + .tool-tip.slideIn,
    footer .on-focus > input:focus + .tool-tip.slideIn.top {
        bottom: 140%;
    }


footer .btn.btn-primary.call-to-action {
    border: none;
    padding: 0 20px 0 0;
    font-size: 14px;
}

footer .scrollToTop.btn-primary:focus,
footer .scrollToTop.btn-primary:not(:disabled):not(.disabled):active {
    background-color: #ccd7e4;
    color: #00549a;
}

footer .footer-c-t-a .btn.btn-primary.call-to-action:focus {
    background-color: transparent;
}

body.is_tabbing footer .footer-c-t-a .btn.btn-primary.call-to-action:focus {
    outline: none !important;
}

footer .btn.btn-primary-white.call-to-action {
    border: none;
    padding: 0 20px 0 0;
    font-size: 14px;
}

footer span.footer-icon-label:hover {
    text-decoration: underline;
}

.footer-login-btn {
    float: right;
}

.footer-links-white a {
    color: #fff;
}

.footer-c-t-a .btn.btn-primary-white.call-to-action:hover, .footer-c-t-a .btn.btn-primary-white.call-to-action:active:focus, .footer-c-t-a .btn.btn-primary.call-to-action:hover {
    background-color: transparent;
    box-shadow: none;
    color: #003778
}

.footer-login-btn, .connector-login-button {
    white-space: normal
}

.footer-login-btn {
    max-width: 145px;
}

.footer-main-links-lobs li:first-of-type {
    padding-top: 0px;
}

.footer-main-links-lobs li.active:first-of-type {
    padding-top: 13px;
}

footer .skip-to-main-link {
    display: inline-block;
    padding: 7px 12px;
    position: absolute;
    left: -300px;
    text-decoration: underline;
    border-bottom-right-radius: 8px;
    transition: left .3s ease-out;
    background-color: #e1e1e1;
    z-index: 3000;
    font-size: 13px;
    color: #00549a;
}

    footer .skip-to-main-link:focus {
        left: 0;
    }

.brf-footer-backtotop-trigger-mobile {
    position: fixed;
    right: 52px;
    bottom: 12px;
    z-index: 100;
    border-radius: 50%;
    background-color: rgba(255,255,255,.9);
    height: 50px;
    width: 50px;
    -webkit-box-shadow: 0 0 8px 6px rgba(0,0,0,.15);
    -moz-box-shadow: 0 0 8px 6px rgba(0,0,0,.15);
    box-shadow: 0 0 8px 6px rgba(0,0,0,.15);
    display: none;
}

.brf-footer-backtotop-mobile .icon.icon-back-to-top:before {
    top: 10px;
    right: -15px;
}


/* Footer overrides */
footer .search-bar-footer {
    position: relative;
}

    footer .search-bar-footer [type="search"] {
        background-color: #fff;
        border-radius: 40px;
        height: 44px;
        color: #555;
        position: relative;
        width: 100%;
        padding: 11px 76px 11px 16px;
        display: inline-block;
        border: 2px solid #d4d4d4;
        top: 5px;
    }

    .search-bar-footer.search-bar-rounded [type="search"] {
        background-color: #f0f0f0;
        color: #111;
        height: 43px;
        border-radius: 5px;
    }

    footer .search-bar-footer .search-btn {
        margin-right: 5px;
        background: none;
        position: relative;
        margin-top: -53px;
        border: 0;
        float: right;
        right: 10px;
        top: 21px;
    }

    footer .search-bar-footer .voice-search-btn {
        margin-right: 5px;
        background: none;
        position: relative;
        margin-top: -57px;
        border: 0;
        float: right;
        right: 50px;
        top: 21px;
    }

    footer .search-bar-footer [type="reset"] {
        position: relative;
        float: right;
        margin-top: -26px;
        background: none;
        border: 0;
        right: 40px;
        width: 40px;
        display: none;
        top: -3px;
    }

footer .footer-icon-label {
    padding-left: 15px;
}
/*social icons*/
footer .social_icons_wrapper .icon_background {
    display: table !important
}

    footer .social_icons_wrapper .icon_background .icon {
        display: table-cell;
        vertical-align: middle;
        text-align: center;
    }

        footer .social_icons_wrapper .icon_background .icon:before {
            top: 0;
            left: 0
        }

.scrollToTop.mobile {
    border: none;
    box-shadow: 0 1px 29px 0 rgba(0,0,0,0.25);
    -webkit-box-shadow: 0 1px 29px 0 rgba(0,0,0,0.25);
    -moz-box-shadow: 0 1px 29px 0 rgba(0,0,0,0.25);
    bottom: 15px;
    right: 15px;
    padding-top: 0px;
}



@media (max-width: 1239.98px) {
    .global-navigation .connector-area div > a {
        margin: 0 30px;
    }

    .global-navigation .connector-search-wrap {
        width: 170px;
    }
}

@media (min-width: 1240px) {
    .global-navigation .container {
        max-width: 1200px;
        width: 100%;
    }

    .global-navigation .connector-area div > a {
        margin: 0 20px;
    }

    .global-navigation #connector-search {
        width: 300px;
    }

    .global-navigation .connector-area div > a {
        margin: 0 30px;
    }
}

@media (min-width: 1000px) {
    .global-navigation #connector-search-cancel, .global-navigation #connector-search-button {
        display: none;
    }

    .global-navigation .connector-settings {
        float: right;
        margin-top: 22px;
    }

    .col-align-middle > [class^="col-"], .col-align-middle > [class*=" col-"] {
        display: table-cell;
        float: none;
        vertical-align: middle;
    }
}

@media (min-width: 992px) {

    .global-navigation .connector-area div > a span {
        font-size: 26px;
    }

    .global-navigation .menu-flyout-visible .sub-nav-item .sub-nav-level4 li > a.active-sub-nav-item {
        color: #555;
        text-decoration: underline;
    }

        .global-navigation .menu-flyout-visible .sub-nav-item .sub-nav-level4 li > a.active-sub-nav-item:hover {
            color: #00549a;
        }

    .global-navigation .connector-search-wrap {
        float: left;
        margin-top: -1px;
        margin-right: 30px;
    }

    .connector-mobile-bar, .connector-settings-mobile, .connector-nav-close-button, .federal-bar-link-provinces {
        display: none;
    }

    .global-navigation .federal-bar-link-provinces.active {
        display: block;
    }

    .global-navigation .shopping-cart-button {
        padding-right: 0;
        padding-left: 10px;
        margin-top: -3px;
        margin-left: 6px;
        font-size: 27px;
        bottom: -3px;
    }

    .global-navigation .menu-flyout-visible + .menu-flyout-overlay {
        display: block;
    }

    .global-navigation .federal-bar-mobile {
        display: none;
    }

    /* Start Menu */
    .global-navigation .connector-mobile-bar {
        border: none !important;
        position: relative;
        height: 55px;
    }

    .global-navigation .connector-area {
        height: auto;
        border-bottom: none;
    }

    .global-navigation .connector-area {
        position: relative !important;
        height: 54px;
        display: inline-block;
        overflow: inherit;
        max-height: 1000px;
        border-bottom: none;
    }

    .global-navigation .connector-brand {
        margin-right: 48px;
        font-size: 34px;
        padding: inherit;
        height: 54px;
    }

        .global-navigation .connector-brand > a {
            position: relative;
        }

    .global-navigation .aliant .connector-brand {
        font-size: 28px;
    }

    .global-navigation .connector-brand-home {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0,0,0,0);
        border: 0;
    }

    .global-navigation ul.connector-areas {
        display: inline-block;
    }

    .global-navigation .connector-settings {
        float: right;
        margin-top: 22px;
    }

    .global-navigation .connector-nav {
        width: auto;
        position: static;
        float: left;
        margin-top: 13px;
        background: none;
        -webkit-transform: none;
        -ms-transform: none;
        transform: none;
        overflow: visible;
    }

        .global-navigation .connector-nav > ul {
            font-size: 0;
        }

    .global-navigation .connector-cart-button, .global-navigation .connector-login-button, .global-navigation .connector-log-out-button {
        float: left;
    }

    .global-navigation .menu-flyout {
        opacity: 0;
        height: 0;
        max-height: 0;
        overflow: hidden;
        position: absolute;
        z-index: -1;
        -webkit-transition: opacity 0.25s;
        -o-transition: opacity 0.25s;
        transition: opacity 0.25s;
    }

        .global-navigation .menu-flyout.menu-flyout-visible {
            opacity: 1;
            position: absolute;
            z-index: 20;
            background-color: #f0f0f0;
            width: 214px;
            padding-top: 0;
            padding-bottom: 0;
            -webkit-box-shadow: 0 3px 16px 2px rgba(0, 0, 0, 0.23);
            box-shadow: 0 3px 16px 2px rgba(0, 0, 0, 0.23);
            left: 50%;
            margin-left: -107px;
            top: 55px;
            height: auto;
            max-height: none;
            overflow: visible;
        }

    .global-navigation .menu-flyout-visible .sub-nav-root {
        padding-top: 20px;
    }

    .global-navigation .menu-flyout-visible .menu-flyout-root {
        position: relative;
        min-height: 250px;
    }

        .global-navigation .menu-flyout-visible .menu-flyout-root:after {
            bottom: 100%;
            left: 50%;
            border: solid transparent;
            content: " ";
            height: 0;
            width: 0;
            position: absolute;
            pointer-events: none;
            border-color: rgba(255, 255, 255, 0);
            border-bottom-color: #f0f0f0;
            border-width: 14px;
            margin-left: -14px;
        }



    .global-navigation .menu-flyout-visible .sub-nav-root > li:last-child {
        padding-bottom: 10px;
    }

    .global-navigation .menu-flyout-visible .sub-nav-header {
        padding: 29px 30px 0 30px;
        font-weight: bold;
        color: #111;
        display: flex;
        font-size: 12px;
        margin: 0;
    }

    .global-navigation .menu-flyout-visible .sub-nav-desktop {
        display: block;
    }

    .global-navigation .menu-flyout-visible .sub-nav-mobile {
        display: none;
    }

    .global-navigation .menu-flyout-visible .sub-nav-level-1 {
        padding: 10px 20px;
        display: block;
        border-left: 4px solid #f0f0f0;
        position: relative;
        line-height: normal;
    }

    .global-navigation .menu-flyout-visible a {
        padding: 10px 20px;
        display: block;
        border-left: 4px solid #f0f0f0;
        position: relative;
        line-height: normal;
    }

    .global-navigation .menu-flyout.menu-flyout-visible li.menu-flyout-item-active > .sub-nav-level-1 {
        border-left-color: #00549a;
        background-color: white;
        font-weight: bold;
        -webkit-box-shadow: -1px 4px 8px -4px rgba(0,0,0,0.1);
        box-shadow: -1px 4px 8px -4px rgba(0,0,0,0.1);
    }

    .global-navigation .menu-flyout-visible .no-sub-nav .sub-nav-level-1,
    .global-navigation .menu-flyout-visible .no-sub-nav a {
        color: #00549a;
    }

    .global-navigation .menu-flyout-visible .sub-nav-group {
        width: 180%;
        background-color: white;
        z-index: -1;
        -webkit-box-shadow: 0px 3px 16px 2px rgba(0,0,0,0.23);
        box-shadow: 0px 3px 16px 2px rgba(0,0,0,0.23);
        opacity: 0;
        -webkit-transition: opacity .225s;
        -o-transition: opacity .225s;
        transition: opacity .225s;
    }

    .global-navigation
    .menu-flyout.menu-flyout-visible
    li.menu-flyout-item-active >
    .sub-nav-group {
        position: absolute;
        left: 214px;
        top: 0;
        height: 100%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        opacity: 1;
    }

    .global-navigation
    .menu-flyout.menu-flyout-visible
    li.menu-flyout-item-active
    .sub-nav-group .sub-nav-level4 li.menu-flyout-item-active >
    .sub-nav-group {
        position: absolute;
        left: 214px;
        top: 0;
        height: 100%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        opacity: 1;
        padding-left: 0;
        padding-right: 0;
        padding-top: 0;
    }

        .global-navigation
        .menu-flyout.menu-flyout-visible
        li.menu-flyout-item-active
        .sub-nav-group .sub-nav-level4 li.menu-flyout-item-active >
        .sub-nav-group .sub-nav-level5 {
            padding-top: 20px;
            width: 100%;
            padding-left: 0;
            padding-right: 0;
        }

    .global-navigation .menu-flyout-visible .sub-nav-large {
        width: 230%;
    }

    .global-navigation .menu-flyout-visible .no-sub-nav .sub-nav-level-1::after {
        content: none;
    }

    .global-navigation .menu-flyout-visible .sub-nav-item ul {
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        padding-left: 30px;
        padding-right: 30px;
        height: 100%;
        padding-top: 30px;
    }

    .global-navigation .menu-flyout-visible .sub-nav-item .sub-nav-links-two-columns, .global-navigation .menu-flyout-visible .sub-nav-item .sub-nav-links-three-columns {
        -webkit-column-gap: 30px;
        column-gap: 30px;
        -webkit-column-fill: auto;
        column-fill: auto;
        height: 280px;
    }

    .global-navigation .menu-flyout-visible .sub-nav-links-two-columns {
        columns: 2;
        -webkit-columns: 2;
        -moz-columns: 2;
        list-style-position: inside;
    }

    .global-navigation .menu-flyout-visible .sub-nav-item {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        width: 0;
        -webkit-transition: opacity .225s, -webkit-transform .225s;
        transition: opacity .225s, -webkit-transform .225s;
        -o-transition: opacity .225s, transform .225s;
        transition: opacity .225s, transform .225s;
        transition: opacity .225s, transform .225s, -webkit-transform .225s;
    }

    .global-navigation .menu-flyout-visible .sub-nav-level-1:after {
        color: #00549a;
        position: absolute;
        top: 50%;
        margin-top: -6px;
        right: 15px;
    }

    .global-navigation .menu-flyout.menu-flyout-visible li.menu-flyout-item-active > .sub-nav-level-1:after {
        content: "";
    }

    .global-navigation .menu-flyout-visible .sub-nav-item:not(:last-child) ul {
        border-right: 1px solid #e2e2e2;
    }

    .global-navigation .menu-flyout-visible .sub-nav-item ul > li a {
        display: block;
        color: #555;
        line-height: normal;
    }

        .global-navigation .menu-flyout-visible .sub-nav-item ul > li a:hover {
            color: #00549a;
            text-decoration: underline;
        }

    .global-navigation .menu-flyout-visible .sub-nav-item .sub-nav-level4 {
        padding-top: 20px;
        padding-left: 0;
        padding-right: 0;
        padding-bottom: 20px;
    }

        .global-navigation .menu-flyout-visible .sub-nav-item .sub-nav-level4 li > a {
            padding: 10px 50px 10px 20px;
            display: block;
            position: relative;
            line-height: normal;
            border-left: 0;
        }

        .global-navigation .menu-flyout-visible .sub-nav-item .sub-nav-level4 li .sub-nav-level5 li > a {
            padding: 10px 20px;
            border-left: 0;
        }

        .global-navigation .menu-flyout-visible .sub-nav-item .sub-nav-level4 li.menu-flyout-item-active .sub-nav-item-2 {
            background-color: white;
            font-weight: bold;
            color: #00549a;
            -webkit-box-shadow: -2px 1px 4px 0px rgba(0,0,0,0.1);
            box-shadow: -2px 1px 4px 0px rgba(0,0,0,0.1);
            text-decoration: none;
        }

        .global-navigation .menu-flyout-visible .sub-nav-item .sub-nav-level4 li .sub-nav-item-2:after {
            color: #00549a;
            position: absolute;
            top: 50%;
            margin-top: -6px;
            right: 15px;
            font-family: 'bell-icon';
            content: "\e012";
            font-size: 12px;
            font-weight: bold;
        }

        .global-navigation .menu-flyout-visible .sub-nav-item .sub-nav-level4 li.menu-flyout-item-active .sub-nav-item-2:after {
            content: '';
        }
    /* End Menu */
}

@media (max-width: 991.98px) {
    .global-navigation.gn-shop .connector-login-modal.aliant {
        display: none !important;
    }
    body.connector-active {
        overflow: hidden
    }

    .login-desktop-hidden {
        display: block !important
    }

    .global-navigation #connector-search [type="search"] {
        position: relative;
        width: 100%;
        padding-right: 70px;
        padding-left: 15px;
        border: 0;
        background-color: #fff;
        color: #111;
    }

        .global-navigation #connector-search [type="search"]::placeholder {
            color: #555;
        }

    .global-navigation #connector-search [type="reset"] .icon {
        color: #00549a;
    }

    .global-navigation .connector-nav {
        left: 0;
    }

        .global-navigation .connector-nav.nav-right {
            right: 0;
        }

    .footer-select-province {
        padding-top: 0;
    }

    .global-navigation #connector-search [type="search"], .global-navigation #connector-search [type="reset"], .global-navigation #connector-search [type="submit"] {
        height: 56px;
    }

    .global-navigation .connector-mobile-bar .ui-autocomplete-input::-webkit-input-placeholder {
        color: #000;
    }

    .global-navigation .connector-mobile-bar .ui-autocomplete-input::-moz-placeholder {
        color: #000;
    }

    .global-navigation .connector-mobile-bar .ui-autocomplete-input:-ms-input-placeholder {
        color: #000;
    }

    .global-navigation .connector-mobile-bar .ui-autocomplete-input:-moz-placeholder {
        color: #000;
    }

    .global-navigation .connector-area div > a.login-desktop-hidden span {
        font-size: 17px;
        font-family: Arial, sans-serif;
    }

    .global-navigation a,
    .global-navigation a:hover {
        color: #ffffff;
    }

    .global-navigation .connector > .container {
        background-color: #00549a;
        z-index: 1100;
        position: relative;
    }

    .global-navigation .container {
        width: 100%;
        max-width: 100%;
        margin: 0;
        /*padding: 0;*/
    }

    .global-navigation .federal-bar {
        display: none;
    }

    .global-navigation .connector-mobile-bar {
        margin: 0;
        position: relative;
        border-bottom: 1px solid #003778 !important;
        height: 55px;
    }

    .global-navigation .connector-active-lob-title {
        position: relative;
        font-size: 19px;
        line-height: 1;
        letter-spacing: -.8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-left: 68px;
        margin-right: 100px;
        padding-top: 10px;
        padding-bottom: 10px;
        color: #c2cedf;
    }

    .global-navigation .connector-brand {
        position: absolute;
        top: 0;
        left: 15px;
        font-size: 0;
        text-decoration: none;
        text-decoration: none;
        border-bottom: none;
        padding: 0;
    }

        .global-navigation .connector-brand:after {
            content: '\e600';
            font-size: 26px;
            line-height: 2.1;
        }

    .global-navigation #connector-search {
        position: relative;
        width: 100%;
        display: block;
    }

        .global-navigation #connector-search [type="search"] {
            border-radius: 0;
            box-shadow: none;
        }

    .global-navigation #connector-search-button {
        display: block;
        position: absolute;
        top: 0px;
        right: 50px;
        border: 0;
        background: none;
        font-size: 19px;
        font-style: normal;
        speak: none;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        color: #fff;
        cursor: pointer;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin: 8px 5px;
        transition: background-color .25s cubic-bezier(.55,0,.1,1);
        padding: 0;
    }

        .global-navigation #connector-search-button.active {
            background-color: #002c6b;
        }

    .global-navigation .connector-settings .connector-brand, .global-navigation .connector-nav-open-button {
        display: block !important;
    }

    .global-navigation .connector-nav-open-button {
        border: 0;
        color: #fff;
        background: none;
        font-size: 20px;
        position: absolute;
        right: 0;
        top: 0;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        margin: 8px 5px;
        padding: 0;
        cursor: pointer;
        z-index: 60;
    }

        .global-navigation .connector-nav-open-button.active {
            background-color: #002c6b;
        }

        .global-navigation .connector-nav-open-button .icon-plus {
            display: none;
        }

        .global-navigation .connector-nav-open-button .icon-mobile-menu {
            display: inline-block;
        }

        .global-navigation .connector-nav-open-button.active .icon-plus {
            display: inline-block;
        }

        .global-navigation .connector-nav-open-button .icon-plus:before {
            display: inline-block;
            -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
        }

        .global-navigation .connector-nav-open-button.active .icon-mobile-menu {
            display: none;
        }

    .global-navigation .connector-login-button {
        display: none;
    }

    .screen {
        position: fixed;
        z-index: 1000;
        top: 0;
        left: 0;
        bottom: 100%;
        right: 0;
        opacity: 0;
        background-color: rgba(0,0,0,.6);
    }

    .connector-active .screen {
        bottom: 0;
        opacity: 1;
    }

    .global-navigation .connector-nav {
        display: none;
        position: fixed;
        top: 55px;
        bottom: 0;
        background: #2d2e33;
        width: 300px;
        -webkit-transform: translateX(-300px);
        -ms-transform: translateX(-300px);
        transform: translateX(-300px);
        z-index: 50;
        overflow: auto;
    }

    .connector-active .global-navigation .connector-nav {
        -webkit-transform: none;
        -ms-transform: none;
        transform: none;
        display: block;
    }

    /* Start Shadow */
    .global-navigation .connector-area.connector-area_first > div:first-child {
        -webkit-box-shadow: inset 0px 11px 17px 0px rgba(0, 0, 0, 0.25);
        -moz-box-shadow: inset 0px 11px 17px 0px rgba(0, 0, 0, 0.25);
        box-shadow: inset 0px 11px 17px 0px rgba(0, 0, 0, 0.25);
    }
    /* End Shadow */

    .global-navigation #connector-search [type="reset"],
    .global-navigation #connector-search #voice_search {
        right: 8px;
    }

    .global-navigation #connector-search [type="submit"]:after {
        color: #00549a;
        font-size: 16px;
    }

    .global-navigation .connector-search-wrap {
        position: absolute;
        width: 100%;
        z-index: 55;
        top: 55px;
        left: 0;
        display: none;
    }

        .global-navigation .connector-search-wrap.active {
            display: block;
        }

    .global-navigation .connector-areas li > div,
    .global-navigation .connector-settings-mobile li > div {
        padding: 1px;
        width: 100%;
    }

    .global-navigation .federal-bar-mobile li > div {
        width: auto;
    }

    .global-navigation .connector.connector-search-active .connector-nav {
        top: 111px;
    }

    .global-navigation .connector-area div > a {
        margin: 0;
        color: #fff;
    }

    .global-navigation .connector-area.active div > a::after,
    .global-navigation
    .connector-area.active
    .menu-flyout-visible
    .menu-flyout-item-active
    .sub-nav-level-1:after {
        top: 48px;
        left: 28px;
        border: solid transparent;
        content: " ";
        height: 0;
        width: 0;
        position: absolute;
        z-index: 11;
        pointer-events: none;
        border-left: 13px solid transparent;
        border-right: 13px solid transparent;
        border-top: 11px solid #00549a;
    }

    .global-navigation
    .connector-area.active
    .menu-flyout-visible
    .menu-flyout-item-active
    .sub-nav-level-1:after {
        transform: none;
        border-top: 11px solid #003778;
    }

    .global-navigation .connector-single-link a > h3 {
        font-size: 18px;
        font-family: sans-serif;
        font-weight: normal;
        letter-spacing: normal;
        line-height: 1.1;
    }

    .global-navigation .connector-single-link a {
        padding: 17px 40px 6px 24px;
        display: block;
        position: relative;
    }

    .global-navigation .connector-area div > a {
        position: relative;
        font-family: "bell-slim";
        letter-spacing: 0.4px;
        display: block;
        padding: 11px 35px 6px 14px;
        font-size: 20px;
        margin-bottom: 1px;
    }

    .global-navigation .connector-area_find-store:after {
        font-family: "bell-icon";
        content: "\e620";
        color: #fff;
        font-size: 18px;
        font-style: normal;
        speak: none;
        font-weight: normal;
        font-variant: normal;
        position: absolute;
        text-transform: none;
        line-height: 1;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        top: 26px;
        -webkit-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
        right: 12px;
        opacity: 1;
        transition: opacity 0.3s cubic-bezier(0.55, 0, 0.1, 1), transform 0.2s cubic-bezier(0.55, 0, 0.1, 1);
        cursor: pointer;
        pointer-events: none;
    }

    .global-navigation .menu-flyout-visible .sub-nav-desktop {
        display: none;
    }

    .global-navigation .menu-flyout-visible .sub-nav-mobile {
        display: block;
    }

    .global-navigation .menu-flyout.menu-flyout-visible {
        display: block;
        position: relative;
        background-color: #003778;
    }

    .global-navigation .menu-flyout-visible .sub-nav-level-1 {
        font-size: 18px;
        padding: 14px 40px 9px 24px;
        display: block;
        position: relative;
    }

        .global-navigation .menu-flyout-visible .sub-nav-level-1:after,
        .sub-nav-root .sub-nav-group .sub-nav-item li a::after {
            font-family: "bell-icon";
            content: "\e012";
            color: #fff;
            font-size: 11px;
            font-style: normal;
            speak: none;
            font-weight: normal;
            font-variant: normal;
            text-transform: none;
            line-height: 1;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 14px;
        }

    .global-navigation
    .menu-flyout-visible
    li:not(.no-sub-nav)
    .sub-nav-level-1:after {
        transform: translateY(-50%) rotate(90deg);
    }

    .global-navigation
    .menu-flyout-visible
    .menu-flyout-item-active
    .sub-nav-level-1:after {
        content: " ";
    }

    .global-navigation
    .menu-flyout-visible
    li.menu-flyout-item-active >
    ul.sub-nav-group {
        display: block;
        background: #002c6b;
    }

    .global-navigation .connector-areas > li:first-child,
    .menu-flyout-root > ul > li:first-child,
    .menu-flyout-root > ul > li > ul > li:first-child {
        -webkit-box-shadow: inset 0 11px 17px 0 rgba(0, 0, 0, 0);
        -moz-box-shadow: inset 0 11px 17px 0 rgba(0, 0, 0, 0);
        box-shadow: inset 0 11px 17px 0 rgba(0, 0, 0, 0);
    }

    .global-navigation .sub-nav-root li:first-child > a {
        -webkit-box-shadow: inset 0 11px 17px 0 rgba(0, 0, 0, 0.11);
        -moz-box-shadow: inset 0 11px 17px 0 rgba(0, 0, 0, 0.11);
        box-shadow: inset 0 11px 17px 0 rgba(0, 0, 0, 0.11);
    }

    .global-navigation .menu-flyout .sub-nav-root > li > ul > li > ul > li > a {
        box-shadow: none;
    }

    .global-navigation .sub-nav-group > li,
    .global-navigation .sub-nav-group > li > a,
    .global-navigation .sub-nav-group > li > ul > li {
        border-bottom: 1px solid #07225F;
    }

        .global-navigation .sub-nav-group > li > ul > li .sub-nav-level5 li {
            border-bottom: 0;
        }

        .global-navigation .sub-nav-group > li > ul > li:last-child,
        .global-navigation .sub-nav-group > li:last-child {
            border-bottom: 0;
        }

    .global-navigation .sub-nav-root .sub-nav-group .sub-nav-item ul a {
        color: #c2cedf;
        text-decoration: none;
    }
        .global-navigation .sub-nav-root .sub-nav-group .sub-nav-item ul a.active-sub-nav-item {
            color: #ffffff;
            text-decoration: underline;
        }

        .global-navigation .sub-nav-root .sub-nav-group .sub-nav-item li a:hover {
            color: #ffffff;
        }

    .global-navigation .sub-nav-root .sub-nav-group .sub-nav-item a,
    .sub-nav-root .sub-nav-group a {
        font-size: 18px;
        padding: 14px 40px 9px 34px;
        position: relative;
        display: block;
    }

    .global-navigation .connector-area {
        position: relative;
    }

        .global-navigation .connector-area:after, .global-navigation .connector-lob:after, .global-navigation .connector-lob > ul > li:after, .global-navigation .connector-lob > ul > li > ul > li:after {
            font-family: 'bell-icon';
            content: "\e012";
            color: #fff;
            font-size: 13px;
            font-style: normal;
            speak: none;
            font-weight: normal;
            font-variant: normal;
            text-transform: none;
            line-height: 1;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            position: absolute;
            top: 50%;
            -webkit-transform: translateY(-50%);
            -ms-transform: translateY(-50%);
            transform: translateY(-50%);
            right: 15px;
            opacity: 1;
        }

        .global-navigation .connector-area:not(.connector-area_first).active:after {
            opacity: 0;
            display: none;
        }

        .global-navigation .connector-area.connector-area_first.active:after {
            opacity: 1;
            transform: rotate(90deg);
        }

        .global-navigation .connector-area:after {
            top: 19px;
            transform: rotate(90deg);
            cursor: pointer;
        }

    .global-navigation ul.federal-bar-mobile {
        background-color: #2d2e33;
        padding-top: 8px;
        padding-bottom: 60px;
    }

    .global-navigation .federal-bar-mobile > li a:link,
    .global-navigation .federal-bar-mobile > li a:visited,
    .global-navigation .federal-bar-mobile > li a:hover,
    .global-navigation .federal-bar-mobile > li a:active {
        display: block;
        padding: 13px 15px 14px 14px;
        font-size: 12px;
        text-transform: uppercase;
        color: #97989c;
        position: relative;
    }

    .global-navigation .federal-bar-mobile .custom-select-trigger {
        border: none;
        padding: 13px 15px 14px 14px;
        color: #97989c;
    }

    .global-navigation .custom-select-trigger > .icon {
        position: absolute;
        top: 50%;
        right: 16px;
    }

    .global-navigation select:focus ~ .custom-select-trigger.bg-transparent {
        outline: -webkit-focus-ring-color auto 5px;
    }

    .global-navigation #connector-search [type="reset"] {
        width: 30px;
        right: 40px;
    }

    .global-navigation #connector-search [type="submit"] {
        width: 45px;
        right: 0px;
    }

    .global-navigation .sub-nav-group {
        max-width: 100%;
    }
}

@media (min-width: 768px) {
    .global-navigation #initial-lang-region {
        width: 480px;
        background-color: #fff;
        left: 50%;
        transform: translate(-50%, 0);
        top: 50%;
        z-index: 5000;
    }
}

@media (max-width: 767.98px) {
    .global-navigation .connector-nav {
        width: 100%;
    }
    .mobile-my-account-login{
        border-right:none;
        border-bottom: 1px solid #dadada;
        padding-bottom:30px
    }
    .mobile-my-bell-login{
        border-right:none;
        padding-top:30px
    }
    .footer-icon-label.relative.no-pad-left-sm{
        padding-left: 0;
    }

}


/* ==========================================================
    
    BRF 3 Global Nav

============================================================= */

.global-navigation .skip-to-main-link:hover, .simple-blue-header.skip-to-main-link:hover {
    color: #fff;
}
/*.global-navigation .connector-area div > a span.txtSize20 {
    font-size: 20px;
    letter-spacing: 0px;
}*/

.global-navigation .connector-area div > a:last-child {
    margin-right: 10px;
}

.global-navigation .connector-area.active div > a {
    border-bottom: 2px solid #fff;
}

.connector-active-lob {
    overflow-x: auto;
    background: #003778;
    padding: 21px 5px 13px;
    position: relative;
    box-shadow: inset 0.7px 0.7px 2px 0 rgba(0,0,0,0.24), 0px 0px 0px 0 rgba(0,0,0,0.24);
}

.connector-active-lob.contact-right {
    padding: 13px 5px 13px;
}

    .connector-active-lob > .container > a {
        text-decoration: none;
    }

    .connector-active-lob a > h3 {
        font-family: 'bell-slim';
    }

    .connector-active-lob a > h3 {
        font-size: 20px;
        line-height: 1.1;
        margin-bottom: 0;
        margin-top: -4px;
    }

    .connector-active-lob h3 {
        color: #fff;
    }

    .connector-active-lob ul {
        display: table;
        margin-bottom: 0;
    }

        .connector-active-lob ul > li {
            display: table-cell;
            vertical-align: middle;
            text-align: center;
            white-space: nowrap;
        }

            .connector-active-lob ul > li.active > a,
            .connector-active-lob ul > li a:hover {
                color: #fff;
                border-bottom: 1px solid #fff;
                padding-bottom: 7px;
                text-decoration: none;
            }

        .connector-active-lob ul a {
            display: block;
            position: relative;
            font-size: 14px;
            line-height: 1;
            color: #c2cedf;
            padding-bottom: 7px;
        }

        .connector-active-lob.contact-right ul a {
            padding-bottom: 0px;
        }

            .connector-active-lob ul a:focus {
                text-decoration: none;
            }

.global-navigation .bellSlimSemibold-Nav {
    border-left: 0;
    border-right: 0;
}

.global-navigation .container {
    max-width: 1200px;
    width: 100%;
}

.connector-active-lob ul {
    margin-left: auto;
}

.global-navigation .menu-flyout-visible .sub-nav-item ul > li a {
    font-size: 14px;
}

.global-navigation .menu-flyout-visible .sub-nav-level-1 {
    font-size: 14px;
    padding-left: 20px;
    padding-right: 50px;
}

.global-navigation .menu-flyout-visible a {
    font-size: 14px;
    padding-left: 20px;
    padding-right: 20px;
}

    .global-navigation .menu-flyout-visible a:hover:not(.sub-nav-level-1) {
        text-decoration: underline;
    }

.global-navigation .menu-flyout-visible .sub-nav-item ul,
.global-navigation .menu-flyout-visible .sub-nav-header {
    padding-left: 20px;
    padding-right: 20px;
}

.global-navigation .menu-flyout.menu-flyout-visible,
.global-navigation .menu-flyout-visible .sub-nav-group {
    -webkit-box-shadow: 0 6px 25px 0 rgba(0,0,0,0.2);
    box-shadow: 0 6px 25px 0 rgba(0,0,0,0.2);
}

.connector-active-lob .container {
    display: flex;
}

.global-navigation #connector-search [type="submit"]:after {
    content: "\e615";
}

.global-navigation .connector-nav {
    margin-top: 0px;
    height: 81px;
}

.global-navigation ul.connector-areas {
    display: block;
    float: right;
    margin-left: auto;
    margin-top: 24px;
}

.global-navigation .connector-area {
    height: inherit;
}

.global-navigation .connector-brand {
    margin-top: 10px;
}

.global-navigation .menu-flyout-visible .menu-flyout-root:after {
    margin-left: -5px;
}

.global-navigation .menu-flyout.menu-flyout-visible {
    top: 57px;
}

.global-navigation .menu-flyout-visible .sub-nav-group {
    width: 100%;
}

.global-navigation .menu-flyout.menu-flyout-visible li.menu-flyout-item-active .sub-nav-group {
    box-shadow: 8px 7px 25px 0px rgba(0,0,0,0.2), 0px 0px 0px 0px rgba(0,0,0,0.2), 0px 0px 0px 0px rgba(0,0,0,0.2), 0px 0px 0px 0px rgba(0,0,0,0.2);
}

.global-navigation .menu-flyout.menu-flyout-visible .sub-nav-level4 li.menu-flyout-item-active > .sub-nav-group li .sub-nav-level5 {
    padding-bottom: 15px;
}

.global-navigation .menu-flyout-visible .no-sub-nav .sub-nav-level-1::after {
    content: none;
}

.global-navigation *:not(.on-focus):hover > .tool-tip.slideIn, .global-navigation *:not(.on-focus):hover > .tool-tip.slideIn.top {
    display: block;
    opacity: 1;
    left: 100%;
    margin-left: 10px;
    transform: translateX(-50%);
    bottom: 53px;
    text-align: end;
    padding-bottom: 5px;
}

.tool-tip.entered {
    display: block;
    opacity: 1;
    left: 100%;
    margin-left: 10px;
    transform: translateX(-50%);
    text-align: end;
    padding-bottom: 5px;
}

.social_icons_wrapper ul.social_icons_cont a {
    display: block
}

/*Section from coveo-search-custom.css*/
.global-navigation #connector-search {
    width: 100%;
    max-width: 220px;
    min-width: 130px;
}

    .global-navigation #connector-search .CoveoSearchButton .coveo-search-button-svg {
        width: 16px;
        height: 15px;
    }

.global-navigation .connector-search-wrap .magic-box .magic-box-input .magic-box-underlay {
    height: 100%;
}

.global-navigation .connector-search-wrap .magic-box-input > input {
    padding-left: 15px;
    height: 36px;
    font-size: 14px;
    padding-bottom: 0;
    text-indent: 0;
}

.global-navigation .connector-search-wrap #connector-search.CoveoSearchbox .icon-close-solid:before {
    top: 0;
}

.global-navigation .connector-search-wrap #connector-search .CoveoSearchButton .coveo-magnifier-circle-svg {
    color: #00549a;
}

.global-navigation .connector-search-wrap #connector-search .CoveoSearchButton:hover .coveo-magnifier-circle-svg {
    fill: #00549a;
}

.global-navigation .connector-search-wrap #connector-search .magic-box .magic-box-suggestions {
    left: -100%;
    right: 0;
    top: 48px;
    overflow: visible;
    border: none;
    padding: 10px;
    box-shadow: 0 0 40px rgba(0,0,0,.3);
}

    .global-navigation .connector-search-wrap #connector-search .magic-box .magic-box-suggestions:after {
        top: -24px;
        border: 12px solid transparent;
        border-bottom: 12px solid white;
        position: absolute;
        content: "";
        display: block;
        width: 0;
        height: 0;
        left: 75%;
        -webkit-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        transform: translateX(-50%);
    }

    .global-navigation .connector-search-wrap #connector-search .magic-box .magic-box-suggestions .magic-box-suggestion {
        border: none;
        padding: 7px 10px;
        margin: 2px 0;
        font-size: 14px;
        color: #555;
    }

        .global-navigation .connector-search-wrap #connector-search .magic-box .magic-box-suggestions .magic-box-suggestion:hover {
            background-color: #e1e1e1;
            border-radius: 3px;
        }

.global-navigation .connector-search-wrap #connector-search .magic-box .coveo-omnibox-selectable.coveo-omnibox-selected {
    background-color: #e1e1e1;
    border-radius: 3px;
}

.global-navigation .connector-search-wrap .CoveoSearchbox .magic-box .magic-box-input > input:focus {
    /*outline: -webkit-focus-ring-color auto 5px;*/
    outline: 2px solid white !important;
    outline-offset: 5px;
}
/*Section from coveo-search-custom.css*/

/*Section from CoveoFullSearchNewDesign.css*/
.CoveoSearchInterface {
    min-width: 300px;
}

    .CoveoSearchInterface > * {
        display: none;
        visibility: hidden;
    }

.coveo-after-initialization > * {
    display: inherit;
    visibility: inherit;
}

.coveo-after-initialization meter,
.coveo-after-initialization progress,
.coveo-after-initialization input,
.coveo-after-initialization textarea,
.coveo-after-initialization keygen,
.coveo-after-initialization select,
.coveo-after-initialization button {
    display: inline-block;
}

/*
* @param direction vertical or horizontal
* @param position type of positioning to apply (relative/absolute)
*/

/*
* @param $selector css selector on which to apply the icon. Can be '&' if the icon should be applied on the current element;
* @param $size size of the icon to use
*/

.CoveoSearchButton {
    color: white;
    border: 1px solid #bcc3ca;
    border-left: none;
    text-decoration: none;
    text-align: center;
    vertical-align: middle;
    overflow: hidden;
    height: 50px;
    width: 60px;
    cursor: pointer;
    line-height: 0;
}

    .CoveoSearchButton:before {
        content: '';
        vertical-align: middle;
        height: 100%;
        display: inline-block;
    }

    .CoveoSearchButton .coveo-search-button,
    .CoveoSearchButton .coveo-search-button-loading {
        vertical-align: middle;
        animation: none;
        display: inline-block;
    }

    .CoveoSearchButton .coveo-search-button-loading-svg {
        display: none;
    }

    .CoveoSearchButton:hover .coveo-magnifier-circle-svg {
        fill: #ecad00;
    }

.coveo-search-button-svg {
    width: 18px;
    height: 18px;
    color: #1d4f76;
}

.coveo-executing-query .CoveoSearchButton .coveo-search-button-svg {
    display: none;
}

.coveo-executing-query .CoveoSearchButton .coveo-search-button-loading-svg {
    display: inline;
    color: #1d4f76;
    width: 18px;
    height: 18px;
    -webkit-animation-name: coveo-spin;
    -moz-animation-name: coveo-spin;
    animation-name: coveo-spin;
    -webkit-animation-timing-function: linear;
    -moz-animation-timing-function: linear;
    animation-timing-function: linear;
    -webkit-animation-duration: 1s;
    -moz-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-iteration-count: infinite;
    -moz-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
}

/*
* @param direction vertical or horizontal
* @param position type of positioning to apply (relative/absolute)
*/

/*
* @param $selector css selector on which to apply the icon. Can be '&' if the icon should be applied on the current element;
* @param $size size of the icon to use
*/

.CoveoSearchInterface {
    min-height: 100%;
    position: relative;
    margin: 0;
    font-family: "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif, sans-serif;
    font-size: 15px;
    color: #313a45;
}

    .CoveoSearchInterface .highlight {
        font-weight: bold;
    }

    .CoveoSearchInterface input[type='text']::-ms-clear {
        width: 0;
        height: 0;
    }

    .CoveoSearchInterface input:focus,
    .CoveoSearchInterface textarea:focus,
    .CoveoSearchInterface select:focus {
        outline: none;
    }

    .CoveoSearchInterface input[type='text'],
    .CoveoSearchInterface textarea {
        color: #67768b;
    }

    .CoveoSearchInterface .coveo-tab-disabled {
        display: none;
    }

    .CoveoSearchInterface * {
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
    }

    .CoveoSearchInterface .coveo-error {
        color: #dc291e;
    }

    .CoveoSearchInterface.coveo-hidden {
        display: none;
    }

.coveo-visible-to-screen-reader-only {
    position: absolute;
    left: -10000px;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

/*
* @param direction vertical or horizontal
* @param position type of positioning to apply (relative/absolute)
*/

/*
* @param $selector css selector on which to apply the icon. Can be '&' if the icon should be applied on the current element;
* @param $size size of the icon to use
*/

.magic-box {
    position: relative;
    text-align: left;
    color: #212121;
    border: 1px solid #9e9e9e;
}

    .magic-box .magic-box-input {
        background: #fff;
        /*height: 48px;*/
        overflow: visible;
        position: relative;
    }

        .magic-box .magic-box-input .magic-box-underlay,
        .magic-box .magic-box-input > input {
            color: #212121;
            text-align: left;
            padding: 12px 49px 12px 0;
            text-indent: 12px;
            font-family: arial, sans-serif;
            font-size: 16px;
            line-height: 24px;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 48px;
            white-space: pre;
            overflow: auto;
            box-sizing: border-box;
            display: block;
        }

        .magic-box .magic-box-input input {
            background: none;
            border: none;
            resize: none;
            outline: 0;
        }

            .magic-box .magic-box-input input::-ms-clear {
                width: 0;
                height: 0;
            }

        .magic-box .magic-box-input .magic-box-underlay {
            color: transparent;
            overflow: hidden;
        }

            .magic-box .magic-box-input .magic-box-underlay > span {
                text-indent: 0;
            }

            .magic-box .magic-box-input .magic-box-underlay span {
                display: inline-block;
                vertical-align: top;
            }

                .magic-box .magic-box-input .magic-box-underlay span[data-value=''] {
                    height: 24px;
                }

            .magic-box .magic-box-input .magic-box-underlay .magic-box-ghost-text {
                visibility: hidden;
                color: #bdbdbd;
            }

            .magic-box .magic-box-input .magic-box-underlay .magic-box-error {
                border-bottom: solid #f44336 2px;
            }

                .magic-box .magic-box-input .magic-box-underlay .magic-box-error.magic-box-error-empty {
                    position: relative;
                }

                    .magic-box .magic-box-input .magic-box-underlay .magic-box-error.magic-box-error-empty:before {
                        content: ' ';
                        border-bottom: solid #f44336 2px;
                        position: absolute;
                    }

            .magic-box .magic-box-input .magic-box-underlay:after {
                content: '';
                display: inline-block;
            }

    .magic-box .magic-box-clear {
        position: relative;
        float: right;
        height: 48px;
        line-height: 48px;
        text-align: center;
        font-size: 24px;
        transition: width 0.3s;
        cursor: pointer;
        background: #fff;
        width: 0;
        overflow: hidden;
    }

        .magic-box .magic-box-clear .magic-box-icon:before {
            color: #9e9e9e;
            font-family: arial, sans-serif;
            content: 'X';
            font-size: 24px;
        }

        .magic-box .magic-box-clear:before {
            content: '';
            border-left: #e0e0e0 1px solid;
            position: absolute;
            top: 15%;
            bottom: 15%;
            left: 0;
        }

    .magic-box.magic-box-notEmpty .magic-box-clear {
        width: 48px;
    }

    .magic-box.magic-box-hasFocus .magic-box-underlay .magic-box-ghost-text {
        visibility: visible;
    }

    .magic-box.magic-box-hasFocus .magic-box-suggestions.magic-box-hasSuggestion {
        display: block;
    }

    .magic-box .magic-box-suggestions {
        clear: both;
        position: absolute;
        top: 100%;
        left: -1px;
        right: -1px;
        overflow: hidden;
        background: #fff;
        z-index: 1;
        display: none;
        font-family: arial, sans-serif;
        border: 1px solid #9e9e9e;
    }

        .magic-box .magic-box-suggestions .magic-box-suggestion-seperator,
        .magic-box .magic-box-suggestions .magic-box-suggestion {
            opacity: 1;
            transition: opacity 0.3s;
        }

        .magic-box .magic-box-suggestions.magic-box-suggestions-loading .magic-box-suggestion-seperator,
        .magic-box .magic-box-suggestions.magic-box-suggestions-loading .magic-box-suggestion {
            opacity: 0;
        }


.CoveoOmnibox.magic-box .magic-box-suggestions {
    z-index: 3;
}


/*
* @param direction vertical or horizontal
* @param position type of positioning to apply (relative/absolute)
*/

/*
* @param $selector css selector on which to apply the icon. Can be '&' if the icon should be applied on the current element;
* @param $size size of the icon to use
*/

.CoveoSearchbox {
    display: -webkit-box;
    display: -moz-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
}

    .CoveoSearchbox.coveo-inline {
        overflow: hidden;
    }

        .CoveoSearchbox.coveo-inline .magic-box {
            overflow: visible;
        }

    .CoveoSearchbox .CoveoSearchButton {
        z-index: 2;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
    }

    .CoveoSearchbox .magic-box {
        -webkit-flex-grow: 1;
        -moz-flex-grow: 1;
        flex-grow: 1;
        -ms-flex-positive: 1;
        border: thin solid #bcc3ca;
        border-radius: 2px;
    }

        .CoveoSearchbox .magic-box .magic-box-clear-svg {
            width: 15px;
            height: 15px;
            color: #1d4f76;
        }

        .CoveoSearchbox .magic-box .magic-box-clear:before {
            border-left: none;
        }

        .CoveoSearchbox .magic-box .magic-box-clear .magic-box-icon:before {
            content: '';
        }

        .CoveoSearchbox .magic-box .magic-box-input {
            border-radius: 2px;
        }

            .CoveoSearchbox .magic-box .magic-box-input > input {
                color: #67768b;
            }

.CoveoSettings ~ .CoveoSearchbox {
    margin-right: 76px;
}

.CoveoSearchInterface.coveo-waiting-for-query .CoveoSearchbox {
    margin: 0;
}

    .CoveoSearchInterface.coveo-waiting-for-query .CoveoSearchbox .coveo-media-max-width-480 {
        margin: 0 20px;
    }

@media (max-width: 480px) {
    .CoveoSearchInterface.coveo-waiting-for-query .CoveoSearchbox {
        margin: 0 20px;
    }
}

.magic-box .magic-box-input .magic-box-underlay .magic-box-ghost-text,
.magic-box.magic-box-hasFocus .magic-box-input .magic-box-underlay .magic-box-ghost-text {
    display: none;
}

.coveo-query-syntax-disabled.magic-box .magic-box-input .magic-box-underlay span,
.coveo-query-syntax-disabled .magic-box-highlight-container {
    display: none;
}

/*Section from CoveoFullSearchNewDesign.css*/

/* ==========================================================
    
    Responsive
    Navigation Overrides

============================================================= */
@media(max-width : 1420.98px) {
    .global-navigation :not(.on-focus):hover > .tool-tip.slideIn, .global-navigation :not(.on-focus):hover > .tool-tip.slideIn.top {
        transform: translateX(-100%);
    }

    .global-navigation .tool-tip:after {
        left: auto;
        right: 11px;
    }

    .tool-tip.entered {
        transform: translateX(-100%);
    }
}

/*@media(max-width : 1290.98px) {
        .global-navigation .container {
            padding: 0 45px;
        }
    }*/

@media (min-width: 1240px) {
    .connector-active-lob ul {
        max-width: 950px;
    }
}



@media (min-width: 1000px) {
    .connector-active-lob {
        overflow-x: hidden;
        overflow-y: hidden;
        padding-left: 0;
        padding-right: 0;
    }

        .connector-active-lob > .container > a {
            display: block;
            float: left;
        }

        .connector-active-lob ul > li {
            white-space: normal;
        }

            .connector-active-lob ul > li:not(:last-of-type) {
                padding-right: 20px;
            }
}

@media (min-width: 992px) {
    .global-navigation .federal-bar {
        height: 35px;
    }

    .global-navigation .menu-flyout-visible .sub-nav-level-1:after {
        font-family: 'bell-icon';
        content: "\e012";
        font-size: 12px;
        font-weight: bold;
    }

    .global-navigation .connector-area.active-lob div > a {
        border-bottom: 2px solid #fff;
    }

        .global-navigation .connector-area.active-lob div > a span {
            color: #fff;
        }

    nav > ul.connector-areas > li:nth-child(1) > div:nth-child(1) > a {
        margin-left: 0;
    }

    
}


@media (max-width : 999.98px) {
    .connector-active-lob > .container {
        margin: 0;
    }

    .connector-active-lob ul > li {
        padding: 0 10px;
    }

        .connector-active-lob ul > li:first-child.active {
            padding-left: 0;
        }

    .connector-active-lob ul {
        padding-left: 0px;
    }
}

@media (min-width : 992px) and (max-width : 999.98px) {
    .global-navigation .connector-brand {
        margin-left: 5px;
    }
    .global-navigation.gn-shop .connector-active-lob ul > li:last-of-type {
        padding-right: 0;
    }
}

@media (max-width : 991.98px) {
    .login-button.hidden-mobile {
        display: none !important
    }

    a.connector-lob-no-href.hoverBorderUnderlineWhite:hover,
    .global-navigation .connector-area.active div > a {
        border-bottom: 0;
    }

    .connector-active-lob ul {
        float: left;
        margin-left: 0;
    }

    .connector-active-lob.contact-right ul {
        float: right;
        margin-left: auto;
    }

    .connector-active-lob > .container > a {
        display: none;
    }

    a.connector-lob-no-href.hoverBold:hover {
        font-weight: normal;
    }

    .connector-active-lob ul > li:first-child {
        padding-left: 0;
    }

    .global-navigation .connector-brand {
        left: 0;
    }

    .connector-active-lob {
        padding: 20px 0 12px;
    }

    .global-navigation .connector-area div > a {
        padding: 12px 35px 10px 14px;
    }

    .global-navigation .connector-area:not(.connector-area_first).active.bce-no-flyout-open:after {
        opacity: 1;
    }

    .global-navigation .connector-area:not(.connector-area_first).active.bce-no-flyout-open div > a:after {
        display: none;
    }

    .global-navigation .menu-flyout.menu-flyout-visible .sub-nav-level4 li.menu-flyout-item-active > .sub-nav-group {
        box-shadow: none;
    }



    .global-navigation #connector-search-button {
        right: 30px;
    }

    .global-navigation .connector-nav-open-button {
        right: -10px;
        margin: 8px 0;
    }

    /*.global-navigation .container {
            padding: 0 30px;
        }*/

    .global-navigation .connector-mobile-bar {
        border-bottom: 0 !important;
    }

    .global-navigation .connector-brand {
        margin-top: 1px;
    }


    .global-navigation .bellSlimSemibold-Nav {
        padding-left: 0;
        margin-top: 6px;
    }

    .global-navigation .aliant .bellSlimSemibold-Nav {
        color: #fff;
        font-size: 15px;
        margin-top: 4px;
    }

    .global-navigation #connector-search-button .icon-magnifying-glass:before {
        /*font-family: 'bell-icon2';
            content: "\eaa1";*/
        color: #fff;
        font-size: 19px;
        /*top: -3px;*/
    }

    /*.global-navigation #connector-search-button {
        margin-top: 5px;
    }*/

    .global-navigation .connector-nav {
        height: 100%;
    }

    .global-navigation ul.connector-areas {
        display: block;
        float: none;
        margin-left: 0;
        margin-top: 0;
    }

    .global-navigation .connector-nav-open-button .icon-plus:before {
        /*font-family: 'bell-icon2';*/
        content: "\e624";
        transform: rotate(0deg);
    }

    .global-navigation .connector-nav-open-button.active {
        width: 40px;
        height: 40px;
        font-size: 15px;
        margin-top: 8px;
    }

    .global-navigation .connector-areas > li:first-child {
        box-shadow: inset 0 15px 16px 0 rgba(0,0,0,0.23), inset 0 0px 0 0 #00215E, 0 0 50px 0 rgba(0,0,0,0.2);
    }

    .global-navigation .connector-nav-open-button.active .icon-plus {
        font-weight: bold;
        display: block;
    }

    .global-navigation .connector-area div > a {
        padding-left: 15px;
        padding-right: 55px;
        margin: 0;
    }

    .global-navigation .connector-area:after {
        font-family: 'bell-icon';
        /*content: "\eaa3";
            transform: translateY(-50%) rotate(0deg);*/
        font-size: 12px;
        right: 20px;
        top: 50%;
    }

    .global-navigation .federal-bar-mobile > li a:link {
        padding: 15px;
    }


    .global-navigation .connector-area.active div > a::after, .global-navigation .connector-area.active .menu-flyout-visible .menu-flyout-item-active .sub-nav-level-1:after {
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        border-top: 8px solid #00549a;
        left: 25px;
        top: 42px;
    }

    .global-navigation .menu-flyout-visible .sub-nav-level-1,
    .global-navigation .menu-flyout-visible a {
        font-size: 15px;
        padding-left: 22px;
        padding-right: 50px;
        padding-top: 18px;
        padding-bottom: 10px;
        /*border-bottom: 1px solid #002B6A;*/
    }

        .global-navigation .menu-flyout-visible a:not(.sub-nav-level-1) {
            display: block;
        }

            .global-navigation .menu-flyout-visible a:not(.sub-nav-level-1):after {
                content: "\e012";
                font-family: 'bell-icon';
                right: 18px;
                margin-top: 0;
                position: absolute;
            }

        .global-navigation .menu-flyout-visible a:hover:not(.sub-nav-level-1) {
            text-decoration: none;
        }

    .global-navigation .menu-flyout-visible .sub-nav-level4 li:not(.no-sub-nav) .sub-nav-item-2:after {
        transform: translateY(-50%) rotate(90deg);
    }

    .global-navigation .menu-flyout-visible .sub-nav-level4 li.menu-flyout-item-active:not(.no-sub-nav) .sub-nav-item-2:after {
        transform: translateY(-50%) rotate(270deg);
        content: '';
    }

    .global-navigation .sub-nav-root li.menu-flyout-item-active:last-child > a:after {
        margin-top: 0;
    }

    .global-navigation .sub-nav-root li:last-child:first-child > a:after {
        margin-top: 0;
    }

    .global-navigation .menu-flyout-visible .sub-nav-root .sub-nav-group .sub-nav-item a, .sub-nav-root .sub-nav-group a {
        padding-left: 30px;
        padding-top: 18px;
        padding-bottom: 10px;
    }

    .global-navigation .menu-flyout-visible .sub-nav-root .sub-nav-group .sub-nav-level5 li a {
        padding-left: 65px;
    }

    .global-navigation .menu-flyout-visible .sub-nav-root .sub-nav-group .sub-nav-level5 li a {
        box-shadow: none;
    }

    .global-navigation .menu-flyout-visible li:not(.no-sub-nav) .sub-nav-level-1:after {
        /*content: "\eaa5";
            font-family: 'bell-icon2';*/
        right: 20px;
    }

    .global-navigation .menu-flyout-visible .no-sub-nav .sub-nav-level-1::after {
        /*font-family: 'bell-icon2';
            content: "\eaa5";*/
        right: 20px;
    }

    .global-navigation .connector-area.active .menu-flyout-visible .menu-flyout-item-active .sub-nav-level-1:after {
        top: 42px;
        border-top: 8px solid #053976;
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        left: 25px;
    }

    /*
        .global-navigation .connector-area.active .menu-flyout-visible li.menu-flyout-item-active:first-child .sub-nav-level-1:after {
            top: 42px;
        }

        .global-navigation .connector-area.active .menu-flyout-visible li.menu-flyout-item-active:first-child:last-child .sub-nav-level-1:after {
            top: 49px;
        }

        */

    .global-navigation .menu-flyout-visible .sub-nav-group .sub-nav-item ul > li a {
        font-size: 15px;
        padding-left: 35px;
        padding-right: 50px;
    }

    .global-navigation .menu-flyout-visible .sub-nav-level-1:after, .sub-nav-root .sub-nav-group .sub-nav-item li a::after {
        right: 18px;
        margin-top: 0;
    }

    .global-navigation .menu-flyout-visible .sub-nav-item ul {
        padding-left: 0;
        padding-right: 0;
    }

    .global-navigation .menu-flyout.menu-flyout-visible {
        top: 0;
    }

    .global-navigation .connector-active-lob-title {
        margin-left: 53px;
        font-size: 19px;
    }

    .global-navigation .connector-area.active-lob div > a {
        border-bottom: 0;
    }

    .global-navigation .connector-area div > a span {
        font-family: "bellslim_mediumregular", Helvetica, Arial, sans-serif;
        letter-spacing: -1px !important;
    }

    .global-navigation *:not(.on-focus):hover > .tool-tip.slideIn, .global-navigation *:not(.on-focus):hover > .tool-tip.slideIn.top {
        transform: translateX(-50%);
    }

    .tool-tip:after {
        left: 32px;
        right: auto;
    }

    .tool-tip.entered {
        transform: translateX(-50%);
    }

    .footer-language-pref-mobile {
        display: inline-block;
    }

    footer .social-links {
        float: right;
    }

    .global-navigation nav .connector-areas > .connector-area.no-sub-nav:after {
        content: '';
    }

    
}

@media (min-width: 992px) and (max-width: 1100.98px) {
    .global-navigation #connector-search {
        width: 100%;
        max-width: 205px;
        min-width: 120px;
    }

    .global-navigation .connector-area div > a {
        margin: 0 15px;
    }
}

@media(min-width: 1101px) {
    .global-navigation #connector-search {
        width: 100%;
        max-width: 220px;
        min-width: 130px;
    }

}

/* ==========================================================
    
    START - BCE Footer Navigation

============================================================= */

.Footer_Grey {
    background-color: #F4F4F4;
}

    .Footer_Grey > .Footer_Container {
        padding: 45px;
    }

    .Footer_Grey ol, .Footer_Grey ul {
        padding: 0;
        margin: 0;
        list-style: none;
    }

        .Footer_Grey ol li, .Footer_Grey ul li {
            display: inline-block;
            position: relative;
        }

        /*Footer Bread Crumbs*/
        .Footer_Grey ol.Footer_Breadcrumbs li.Footer_BCItem > i {
            margin: 0 10px;
        }

        .Footer_Grey ol.Footer_Breadcrumbs li.Footer_BCItem:last-child > i {
            display: none;
        }

        .Footer_Grey ol.Footer_Breadcrumbs li.Footer_BCItem.active > a {
            color: #111111;
            text-decoration: none;
            cursor: default;
        }

    /*Footer Links List*/
    .Footer_Grey .Footer_links_list {
        columns: 4;
    }

        .Footer_Grey .Footer_links_list > .Footer_LLColumn {
            display: block;
            margin-top: 5px;
        }

            .Footer_Grey .Footer_links_list > .Footer_LLColumn:first-child {
                margin-top: 0;
            }

    /*Footer Email List*/
    .Footer_Grey .Footer_email_list > .Footer_ELColumn {
        width: calc(25% - 25px);
        margin-right: 30px;
        display: block;
    }

        .Footer_Grey .Footer_email_list > .Footer_ELColumn:last-child {
            margin-right: 0;
        }

    /*Footer Site Links*/
    .Footer_Grey ul.Footer_Sitelinks li > a {
        margin: 0 15px;
    }

    .Footer_Grey ul.Footer_Sitelinks li:first-child > a {
        margin-left: 0;
    }

    .Footer_Grey ul.Footer_Sitelinks li:last-child > a {
        margin-right: 0;
    }

    .Footer_Grey ul.Footer_Sitelinks li > .divider {
        color: #E1E1E1;
    }

    /*Footer Social Links*/
    .Footer_Grey ul.Footer_SocialIcons {
        text-align: left;
    }

        .Footer_Grey ul.Footer_SocialIcons li .icon_background {
            height: 36px;
            width: 36px;
            background-color: #00549a;
            color: #fff;
            border-radius: 50%;
            display: inline-block;
            text-align: center;
        }

            .Footer_Grey ul.Footer_SocialIcons li .icon_background > i {
                font-size: 20px;
                top: 4px;
            }

/*Footer Legal Links*/
.site_links_list_wrapper ul.site_links_list_cont li > a {
    margin-right: 15px;
    margin-left: 0;
}

    .site_links_list_wrapper ul.site_links_list_cont li > a:after {
        content: "";
        display: inline-block;
        width: 1px;
        height: 16px;
        vertical-align: middle;
        background-color: #d4d4d4;
        margin: 0px 0 2px 15px;
    }

.site_links_list_wrapper ul.site_links_list_cont li:last-child > a:after {
    display: none;
}

/*Footer Media Queries*/
@media (min-width: 1240px) {
    .Footer_Grey > .Footer_Container {
        padding: 45px 0;
    }
}

/*Footer Media Queries*/
@media (min-width: 992px) {
    /*Footer Social Links*/
    .Footer_Grey ul.Footer_SocialIcons {
        text-align: right;
    }
}

@media (min-width: 768px) and (max-width: 991.98px) {
    /*Footer Links List*/
    .Footer_Grey .Footer_links_list {
        columns: 3;
    }

    /*Footer email List*/
    .Footer_Grey .Footer_email_list > .Footer_ELColumn {
        width: calc(33.33% - 25px);
        margin-top: 30px;
    }

        .Footer_Grey .Footer_email_list > .Footer_ELColumn:nth-child(-n+3) {
            margin-top: 0;
        }

        .Footer_Grey .Footer_email_list > .Footer_ELColumn:nth-child(3) {
            margin-right: 0;
        }
}

@media (max-width: 767.98px) {
    .footer-current-province {
        display: none;
    }

    footer .social-links {
        float: left;
    }

    .Footer_Grey > .Footer_Container {
        padding: 30px;
    }

    /*Footer Links List*/
    .Footer_Grey .Footer_links_list {
        columns: 1;
    }

    /*Footer email List*/
    .Footer_Grey .Footer_email_list > .Footer_ELColumn {
        width: 100%;
        margin-top: 30px;
        margin-right: 0;
    }

        .Footer_Grey .Footer_email_list > .Footer_ELColumn:first-child {
            margin-top: 0;
        }

    /*Footer Site Links*/
    .Footer_Grey ul.Footer_Sitelinks li > a {
        margin: 0 5px;
    }

    .Footer_Grey ul.Footer_Sitelinks li:first-child > a {
        margin-left: 0;
    }

    .Footer_Grey ul.Footer_Sitelinks li:last-child > a {
        margin-right: 0;
    }

    footer .btn.btn-primary.call-to-action,
    footer .btn.btn-primary.call-to-action:active,
    footer .btn.btn-primary.call-to-action:focus {
        font-size: 15px;
        margin-bottom: 0;
    }

    .footer-language-pref {
        float: left;
    }

    .footer-login-btn {
        float: left;
    }

    footer .btn.btn-primary.call-to-action,
    footer .btn.btn-primary.call-to-action:active,
    footer .btn.btn-primary.call-to-action:focus {
        color: #003778;
        background-color: transparent;
        border: 2px solid #003778;
        font-size: 15px;
        padding: 12px 32px 12px 32px;
        text-align: center;
        cursor: pointer;
        width: 100%;
    }

        footer .btn.btn-primary.call-to-action:hover,
        footer .btn.btn-primary.call-to-action:active:focus {
            color: #00549a;
            border-color: #00549a;
            background-color: transparent;
        }

    footer .btn.btn-primary-white.call-to-action,
    footer .btn.btn-primary-white.call-to-action:active,
    footer .btn.btn-primary-white.call-to-action:focus {
        background-color: transparent;
        border: 2px solid #fff;
        color: #fff;
        font-size: 18px;
        padding: 12px 32px 12px 32px;
        text-align: center;
        cursor: pointer;
        width: 100%;
        margin-bottom: 10px;
    }

    .footer-c-t-a .btn.btn-primary-white.call-to-action:hover, .footer-c-t-a .btn.btn-primary-white.call-to-action:active:focus {
        border-color: #b3c4d8;
        color: #fff;
        background-color: #3376ae;
        text-decoration: none;
    }

    .footer-icon-label {
        padding-left: 0px;
        top: 0px;
    }
}

/* ==========================================================
    
    END - Footer Navigation

============================================================= */


/*Shop global nav header/footer styles*/
.global-navigation.gn-shop .fed-links{
    margin-right:15px;
    float:left;
}
.global-navigation.gn-shop .federal-bar-links.federal-bar-links_left .fed-links:last-child{
    margin-right:0px;
}
.global-navigation.gn-shop .fed-links .active{
    color:#fff;
}
.global-navigation.gn-shop .fed-links.has-flyout{
    position:relative;
    display:inline-block;
}
.global-navigation.gn-shop .federal-bar-links.federal-bar-links_left .fed-links.has-flyout .federal-bar-link-small-business.popup {
    left: -75px;
}
.global-navigation.gn-shop .federal-bar-links.federal-bar-links_left .fed-links.has-flyout .federal-bar-link-enterprise.popup {
    left: -107px;
}
.global-navigation.gn-shop .federal-bar-links.federal-bar-links_left .fed-links.has-flyout .federal-bar-link-small-business.popup,
.global-navigation.gn-shop .federal-bar-links.federal-bar-links_left .fed-links.has-flyout .federal-bar-link-enterprise.popup{
    top: 26px;
    width:280px;
    padding:30px;
}
.global-navigation.gn-shop .fed-links.has-flyout .popup.caret:after {
    left: calc(50%);
    top:3px;
}
.global-navigation.gn-shop .fed-links.has-flyout .federal-bar-link-small-business.popup.caret:after {
    left: calc(50% - 20px);
}
.global-navigation.gn-shop .federal-bar-link-small-business a,
.global-navigation.gn-shop .federal-bar-link-enterprise a{
    font-size:18px;
    color:#00549a;
    text-transform:none;
    margin-bottom:5px;
    display:block;
}
.global-navigation.gn-shop .federal-bar-link-small-business .aliant-services,
.global-navigation.gn-shop .federal-bar-link-enterprise .aliant-services{
    margin-bottom:15px;
    font-size:14px
}
.global-navigation.gn-shop .federal-bar-link-small-business .aliant-services:last-child,
.global-navigation.gn-shop .federal-bar-link-enterprise .aliant-services:last-child{
    margin-bottom:0px;
}

.global-navigation.gn-shop .federal-bar-link-small-business,
.global-navigation.gn-shop .federal-bar-link-enterprise{
    text-transform:none;
}
.global-navigation.gn-shop .federal-bar-link-small-business a:hover,
.global-navigation.gn-shop .federal-bar-link-enterprise a:hover,
.global-navigation.gn-shop .federal-bar-link-small-business a:focus,
.global-navigation.gn-shop .federal-bar-link-enterprise a:focus{
    color:#003778;
    text-decoration:underline
}
.global-navigation.gn-shop .connector-settings-mobile > li.connector-area.no-icon > div > a {
    padding-left: 15px;
}

.global-navigation .aliant .shopping-cart-button .shopping-cart-caret:after {
    left: calc(50% + 87px);
}

.global-navigation .aliant .shopping-cart-button.active .shopping-cart-caret > a, .global-navigation .federal-bar .aliant-services.aliant-link-section > a {
    text-decoration: none;
}

.global-navigation .aliant .shopping-cart-button.active .shopping-cart-caret > a:hover > span, .global-navigation .aliant .shopping-cart-button.active .shopping-cart-caret > a:focus > span
, .global-navigation .federal-bar .aliant-services.aliant-link-section > a:hover > span:first-child, .global-navigation .federal-bar .aliant-services.aliant-link-section > a:focus > span:first-child {
    text-decoration: underline;
}

.global-navigation.gn-shop .fixed-active-lob {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 999999;
    -webkit-box-shadow: 0px 10px 15px -1px rgba(0,0,0,0.45);
    -moz-box-shadow: 0px 10px 15px -1px rgba(0,0,0,0.45);
    box-shadow: 0px 10px 15px -1px rgba(0,0,0,0.45);
}
body.fixed-nav{
    padding-top:56px
}
.global-navigation.gn-shop .bell-stores-options a{
    color:#555;
}
.global-navigation.gn-shop .bell-stores-options a:hover{
    color:#00549a;
    text-decoration:underline
}

.global-navigation.gn-shop .federal-bar-store-locator-popup {
    width: 250px;
}
.global-navigation.gn-shop .federal-bar-store-locator .federal-bar-store-locator-popup.federal-bar-links.caret::after {
    left: calc(50% + 20px);
    top: 3px;
}

.global-navigation.gn-shop .federal-bar-store-locator-popup.filter-options {
    width: 360px;
    -webkit-transition: all 100ms ease;
    -moz-transition: all 100ms ease;
    -ms-transition: all 100ms ease;
    -o-transition: all 100ms ease;
    transition: all 100ms ease;
}
.global-navigation.gn-shop .federal-bar-store-locator .federal-bar-store-locator-popup.filter-options.federal-bar-links.caret::after {
    left: calc(50% + 75px);
    top: 3px;
}

.global-navigation.gn-shop .connector-search-wrap {margin-right: 10px;}
.global-navigation.gn-shop #connector-search [type="search"] {background-color: #fff;color: #111;box-shadow: inset 2px 0 3px -1px rgba(0,0,0,.46);}

.global-navigation.gn-shop #connector-search [type="search"]::placeholder {
    color: #999;
}
.global-navigation.gn-shop #connector-search [type="submit"]:after {
    font-size: 18px;
    color: #003778;
}
.global-navigation.gn-shop #connector-search [type="submit"], #connector-search #voice_search {
    margin-top: 0px;
    right:3px;
}
.global-navigation.gn-shop #connector-search [type="reset"] .icon {
    color: #555;
}
.global-navigation.gn-shop #connector-search [type="search"],
.global-navigation.gn-shop .connector-search-wrap .CoveoSearchbox .magic-box .magic-box-input > input {
    padding-right: 90px;
}

.global-navigation.gn-shop .connector-search-wrap .CoveoSearchbox .magic-box .magic-box-input>input {
    max-width: 300px;
}
.global-navigation.gn-shop .icon-close-solid:before {
    top:1px;
}
.global-navigation.gn-shop #connector-search .CoveoSearchButton {
    right: 3px;
    height: 36px;
    width: 30px;
    position: absolute;
    left: auto;
    top: 0;
    padding: 0;
    border: 0;
    background: none;
    margin-right: 5px;
}
.global-navigation.gn-shop .icon-voice-search:before {
    color: #003778;
    position: absolute;
    right: 40px;
    z-index: 1;
    font-size: 18px;
    top: 9px;
}
.global-navigation.gn-shop #connector-search #voice_search {
    right: 3px;
}

    .global-navigation.gn-shop #connector-search #voice_search:focus {
        box-shadow: none;
    }

.global-navigation.gn-shop .connector-search-wrap #connector-search.CoveoSearchbox .icon-close-solid:before {
    top: -1px;
    right: 0px;
}
.global-navigation.gn-shop #connector-search [type="reset"] {
    right: 55px;
}
.global-navigation.gn-shop #connector-search .CoveoSearchButton .coveo-search-button-svg {
    width: 18px;
    height: 18px;
}
.global-navigation.gn-shop #connector-search {
    width: 300px;
    max-width:300px
}
.global-navigation.gn-shop .connector-area.active div > a {
    border-bottom: none;
}
.global-navigation.gn-shop .icon.icon-plus:before{
    top:-1px
}
.global-navigation.gn-shop .fixed-active-lob {
    z-index: 1101;
}

.global-navigation.gn-shop .connector-area.connector-area_find-store{
    display:none
}
.global-navigation.gn-shop ul.connector-areas > li.connector-area:nth-child(2){
    margin-left:-30px
}
.global-navigation.gn-shop .button.connector-login-button{
    margin:0;
    padding: 7px 20px;
}
/*Section from coveo-search-custom.css*/
.global-navigation .connector-search-wrap .magic-box .magic-box-input .magic-box-underlay {
    height: 100%;
}
/*Section from coveo-search-custom.css*/
/*footer styles*/
footer.gf-shop .search-bar-footer .search-btn {
    margin-top: -57px;
}
footer.gf-shop .icon.icon-chevron-bold:before, 
footer.gf-shop .icon.icon-home:before{top:-2px}
footer.gf-shop .search-bar-footer [type="search"]{
    top:0
}
footer.gf-shop .footer-links-three-columns li {
    padding-bottom:5px
}
footer.gf-shop .button.connector-log-out-button, 
footer.gf-shop .button.connector-profile-button, 
footer.gf-shop .button.connector-login-button {
    float: right;
    margin:0
}
footer.gf-shop .btn.btn-primary-white.call-to-action, 
footer.gf-shop .btn.btn-primary-white.call-to-action:active, 
footer.gf-shop .btn.btn-primary-white.call-to-action:focus {
    font-size: 14px;
}
footer.gf-shop.bgBlue-inverted .footer-icon {
    font-size:26px
}
footer.gf-shop.bgBlue-inverted .footer-icon > span:before{
    top:4px
}
footer.gf-shop .legal-links{
    line-height:21px;
    font-size:12px;
}
footer.gf-shop .legal-links a{
    color:#555;
    white-space: nowrap;
}
footer.gf-shop .legal-links.txtBlue a{
    color:#00549a;
}
footer.gf-shop .legal-links a:hover,
footer.gf-shop .legal-links a:focus{
    color:#003778;
}
footer.gf-shop .legal-links a::after, 
footer.gf-shop .v-divider::after {
    background-color: #b4b4b4;
    content: "";
    display: inline-block;
    height: 12px;
    margin: 0px 5px 0 8px;
    vertical-align: middle;
    width: 1px;
}
footer.gf-shop .footer-language-pref .v-divider:after{
    margin: -2px 5px 0 5px;
}
footer.gf-shop .legal-links a:last-child:after{
    margin: 0;
    width: 0px;
}

/*Blue inverted footer*/
footer.gf-shop.bgBlue-inverted{
    background-color:#00549a;
}
footer.gf-shop.bgBlue-inverted .footer-icon > span,
footer.gf-shop.bgBlue-inverted .footer-icon-social > span:before{
    color:#00549a;
}
footer.gf-shop.bgBlue-inverted a.scrollToTop{
    color:#003778;
}
footer.gf-shop.bgBlue-inverted a,
footer.gf-shop.bgBlue-inverted a:hover,
footer.gf-shop.bgBlue-inverted a:focus,
footer.gf-shop.bgBlue-inverted .small-text,
footer.gf-shop.bgBlue-inverted .small-title,
footer.gf-shop.bgBlue-inverted p{
    color:#fff
}
footer.gf-shop.bgBlue-inverted .spacer1.bgGrayLight6{
    background-color:#3c79b0;
}
footer.gf-shop.bgBlue-inverted .footer-icon,
footer.gf-shop.bgBlue-inverted .footer-icon-social{
    background-color:#fff;
}
footer.gf-shop.bgBlue-inverted .footer-icon-social:hover{
    opacity:0.8;
}
footer.gf-shop.bgBlue-inverted .btn-primary-white.call-to-action:hover,
footer.gf-shop.bgBlue-inverted .btn-primary-white.call-to-action:active:focus{
    opacity:0.8;
    color:#fff;
    text-decoration:underline
}
footer.gf-shop.bgBlue-inverted .skip-to-main-link{
    color:#00549a;
}
footer.gf-shop.bgBlue-inverted .connector-btn-small{
    float:right
}

@media (min-width: 992px){
.global-navigation.gn-shop .connector-settings {
    margin-top: 23px;
    }
.global-navigation.gn-shop .menu-flyout-visible .menu-flyout-root:after {
    margin-left: -14px;
}
.global-navigation.gn-shop .menu-flyout.menu-flyout-visible {
            margin-left: -100px;
        }
.global-navigation.gn-shop nav > ul.connector-areas > li:nth-child(1) > div.menu-flyout.menu-flyout-visible:nth-child(2) {
    margin-left: -112px;
}
.global-navigation.gn-shop .menu-flyout-visible .sub-nav-item .sub-nav-level4 li > a {
    padding: 0px 30px 0px 30px;
    font-size:13px
}
.global-navigation.gn-shop .menu-flyout-visible .sub-nav-header {
    padding-left: 30px;
    padding-right: 30px;
    margin-bottom:-10px
}
.global-navigation.gn-shop .menu-flyout-visible .sub-nav-item .sub-nav-level4 {
    padding-top: 30px;
}
.global-navigation.gn-shop  .menu-flyout-visible .sub-nav-item ul > li {
    margin-bottom: 15px;
}
.global-navigation.gn-shop .connector-area.active.active-lob div > a {
    border-bottom: 2px solid #fff;
}
}
@media (max-width: 991.98px) {
footer.gf-shop .btn.btn-primary-white.call-to-action:hover{
    text-decoration:none
}
footer.gf-shop .social-links-group{
    padding:30px 0
}
.global-navigation.gn-shop .connector-area.active div > a::after, 
.global-navigation .connector-area.active .menu-flyout-visible .menu-flyout-item-active .sub-nav-level-1:after {
    top: 55px;
    border-top: 8px solid #005091;
}
.global-navigation.gn-shop .connector-area.active .menu-flyout-visible .menu-flyout-item-active .sub-nav-level-1:after {
    border-top: 8px solid #003471;
    top: 47px;
}
.global-navigation.gn-shop .menu-flyout-visible li.menu-flyout-item-active > ul.sub-nav-group {
    display: block;
    background: #07225f;
}
.global-navigation.gn-shop .sub-nav-header {
    font-size: 14px;
}
.global-navigation.gn-shop ul.connector-areas > li.connector-area:nth-child(2){
    margin-left:0px
}
.global-navigation.gn-shop .connector-area.connector-area_find-store{
    display:block
}
.global-navigation.gn-shop .menu-flyout-visible .sub-nav-level-1, .global-navigation.gn-shop .menu-flyout-visible a, 
.global-navigation.gn-shop .menu-flyout-visible .sub-nav-root .sub-nav-group .sub-nav-item a, .sub-nav-root .sub-nav-group a{
    padding-bottom:15px;
    padding-top:15px;
    }
.global-navigation.gn-shop .connector-nav {
    right: 0;
    left:unset
}
.global-navigation.gn-shop .connector-search-wrap .CoveoSearchbox .magic-box .magic-box-input > input {
    max-width: 100%;
    }
.global-navigation.gn-shop #connector-search [type="search"], 
.global-navigation.gn-shop .connector-search-wrap .CoveoSearchbox .magic-box .magic-box-input > input {
    padding-right: 100px;
    }
.global-navigation.gn-shop .icon-voice-search:before {
    top: 19px;
    }
.global-navigation.gn-shop #connector-search [type="reset"] {
    right: 62px;
    }
.global-navigation.gn-shop .connector-area:after {
    margin-top: -6px;
    }
.global-navigation.gn-shop .connector-area.connector-area_find-store:after {
    content: "";
}
.global-navigation.gn-shop .connector-area.connector-area_find-store:before, .global-navigation.gn-shop .connector-area.connector-area_find-store.active:before {
    content: "\e620";
    font-family:bell-icon;
    font-size:18px;
    color:#fff;
    position:absolute;
    right: 17px;
    top: 20px;
}

.global-navigation.gn-shop .connector-settings-mobile > li > .icon {
    top: 8px;
    }
.global-navigation.gn-shop .connector-settings-mobile > li > a {
    padding-left: 53px;
    }
.global-navigation.gn-shop .menu-flyout-visible .sub-nav-level-1:after, .sub-nav-root .sub-nav-group .sub-nav-item li a:after {
    /*margin-top: 3px;*/
    }
}

@media (max-width: 767.98px) {
    footer.gf-shop.bgBlue-inverted .connector-btn-small{
    float:left;
    margin-top: 15px;
}

footer.gf-shop .login-footer-btn{
    width:100%;
    max-width:100%;
    flex: 0 0 100%;
}
footer.gf-shop .btn-primary-white.call-to-action.margin-b-xs-0{
    margin-bottom:0
}
footer.gf-shop .log-in-area .small-title{
    font-family:"Helvetical", Arial, sans-serif !important;
    font-size:18px;
    font-weight:bold
}
}

@media (min-width: 768px) and (max-width: 991.98px) {
    .global-navigation.gn-shop .connector-area:after, 
    .global-navigation.gn-shop .menu-flyout-visible li:not(.no-sub-nav) .sub-nav-level-1:after,
    .global-navigation.gn-shop .menu-flyout-visible .sub-nav-level-1:after, 
    .global-navigation.gn-shop .sub-nav-root .sub-nav-group .sub-nav-item li a::after{
    font-size: 12px;
    right: 30px;
}
    .global-navigation.gn-shop .connector-area.connector-area_find-store:before, .global-navigation.gn-shop .connector-area.connector-area_find-store.active:before {
    right: 27px;
}
}
@media (min-width: 992px) and (max-width: 1239px){
.global-navigation.gn-shop #connector-search, .global-navigation.gn-shop .connector-search-wrap {
    width: 210px;
    max-width:210px
    }
}
@media (min-width: 992px){
    /*Section from coveo-search-custom.css*/
    .global-navigation .connector-search-wrap .CoveoSearchbox .magic-box {
        border: none;
    }

        .global-navigation .connector-search-wrap .CoveoSearchbox .magic-box .magic-box-input {
            border-radius: 18px;
            height: 36px;
        }

            .global-navigation .connector-search-wrap .CoveoSearchbox .magic-box .magic-box-input > input {
                position: relative;
                width: 100%;
                border: 0;
                background-color: #fff;
                color: #111;
                padding-top: 0;
                display: inline-block;
                border-radius: 18px;
                box-shadow: inset 2px 0 3px -1px rgba(0, 0, 0, 0.46);
            }

    .global-navigation .connector-search-wrap .CoveoSearchbox a.CoveoSearchButton.coveo-accessible-button {
        position: absolute;
        right: 5px;
        left: auto;
        top: -7px;
        padding: 0;
        border: 0;
        background: none;
    }
    /*Section from coveo-search-custom.css*/
}
@media (max-width: 991.98px){
    /*Section from coveo-search-custom.css*/
    .global-navigation .connector-search-wrap .magic-box-input > input {
        height: 55px;
        color: #111;
        padding-top: 0;
        font-size: 16px;
    }

    .CoveoSearchbox .magic-box {
        border-radius: 0 !important;
        border: none !important;
    }

        .CoveoSearchbox .magic-box .magic-box-input {
            height: 55px;
            border-radius: 0 !important;
        }

    .global-navigation #connector-search {
        width: 100%;
        min-width: 100%;
    }

    .global-navigation.gn-shop #connector-search .CoveoSearchButton {
        height: 55px;
        margin-right: -5px;
        width: 45px;
    }

    .global-navigation .connector-search-wrap #connector-search.CoveoSearchbox .icon-close-solid:before {
        top: 0px;
    }

    .global-navigation .connector-search-wrap #connector-search .magic-box .magic-box-suggestions {
        left: 0;
        top: 68px;
        width: 90%;
        left: 50% !important;
        -webkit-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        transform: translateX(-50%);
        z-index: 61;
    }

        .global-navigation .connector-search-wrap #connector-search .magic-box .magic-box-suggestions:after {
            left: 50%;
            -webkit-transform: translateX(-50%);
            -ms-transform: translateX(-50%);
            transform: translateX(-50%);
        }
}

/*----Start SELECT PROVINCE (BTC-5788)--*/
.global-navigation .federal-bar-select-provinces {
    display: inline-block;
    position: relative;
}
    
.global-navigation .federal-bar-select-provinces-popup {
    display: none;
    position: absolute;
    right: -36px;
    z-index: 100;
    background-color: white;
    padding: 15px 10px;
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.3);
    top: 26px;
    width: 250px;
    text-transform: none;
}
.global-navigation.gn-shop .federal-bar-select-provinces > .federal-bar-select-provinces-popup.caret::after {
    left: calc(50% + 82px);
    top: 3px;
}

.global-navigation .federal-bar-select-provinces .label,
footer .federal-bar-select-provinces .label {
    text-transform: initial;
    padding: 5px 5px 3px 7px;
    text-transform: none;
    cursor: pointer;
}

.global-navigation .federal-bar-select-provinces .label.disabled,
footer .federal-bar-select-provinces .label.disabled {
    cursor: default;
}

.global-navigation .federal-bar-select-provinces .label .label-text,
footer .federal-bar-select-provinces .label .label-text {
    font-size: 13px;
    color: #555555;
}

.global-navigation .federal-bar-select-provinces .label:hover,
.global-navigation .federal-bar-select-provinces .label:focus,
footer .federal-bar-select-provinces .label:hover,
footer .federal-bar-select-provinces .label:focus {
    background: #e1e1e1;
    border-radius: 3px;
}

.global-navigation .federal-bar-select-provinces .label.active .label-text,
footer .federal-bar-select-provinces .label.active .label-text {
    color: #00549a;
    font-weight: bold;
}

.global-navigation .federal-bar-select-provinces .checkbox,
footer .federal-bar-select-provinces .checkbox {
    border: none;
    background-color: transparent;
    box-shadow: none;
}
/*----END SELECT PROVINCE --*/



