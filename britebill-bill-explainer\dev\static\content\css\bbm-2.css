﻿@import 'ent-icons.css';

/* CSS With Important Attribute */

/*visibility styles*/
/* .transparent {
    opacity: 0 !important;
} */

/*font weight switch*/
.txtBold.bellSlim,.txtBold.bellSlimBlack,.txtBold.bellSlimHeavy{font-weight:400;}
/*background colours*/
.bgGray-imp{background-color:#ccc !important}
.bgGray20 {background-color: #babec2 !important}

/* alignment*/
.valign-middle {vertical-align: middle !important}
.valign-top {vertical-align: top !important}

/*positioning*/
.absolute-imp{position:absolute !important}
.pos-fixed-top-imp{position:fixed !important;top:0}

/*vertical paddings*/ /*Note we do not use top and bottom padding because is not consitent results in every browser. use spacers as an alternative for padding top bottom*/
.vPadding20-imp{padding:0 20px !important}

/*Padding*/
.no-pad-imp {padding: 0 !important}
.no-pad-right-imp {padding-right: 0 !important}
.no-pad-top-imp {padding-top: 0 !important}
.no-pad-bottom-imp {padding-bottom: 0 !important}
.pad-7-right-imp {padding-right: 7px !important}
.pad-8-left-imp {padding-left: 8px !important}
.pad-10-left-imp {padding-left: 10px !important}
.pad-10-right-imp {padding-right: 10px !important}
.pad-10-top-imp {padding-top : 10px !important}
.pad-10-bottom-imp {padding-bottom: 10px !important}
.pad-15-left-imp {padding-left: 15px !important}
.pad-15-right-imp {padding-right: 15px !important}
.pad-20-imp {padding: 20px !important}
.pad-25-right-imp {padding-right: 25px !important}
.pad-30-imp {padding: 30px !important;}
.pad-30-right-imp {padding-right: 30px !important}
.pad-30-left-imp {padding-left: 30px !important}
.pad-40-imp {padding: 40px !important}

/*Default outline of focused elements*/
.focus-default-outline:focus {outline: 5px auto -webkit-focus-ring-color !important;}

/*For Tablet and Mobile*/
@media screen and (max-width: 991.98px) {
    .no-pad-left-imp-sm{padding-left:0!important}
    .pad-15-left-sm-imp {padding-left: 15px !important}
    .pad-15-right-sm-imp {padding-right: 15px !important}
    .pad-30-right-imp-sm{padding-right:30px!important}

}

/*For Tablet only*/
@media (min-width:768px) and (max-width:991.98px) {
    .pad-20-40-sm-imp{padding: 20px 40px !important}
}

/*For Desktop*/
@media (min-width:992px) {
    .pad-30-md-imp {padding:30px !important}
}

/*For larger Desktop*/
@media screen and (min-width:1200px) {
    .d-lg-list-item {display: list-item !important;}
}

/*For mobile only*/
@media screen and (max-width:767.98px) {
    .no-pad-LR-xs-imp {
        padding-left: 0;
        padding-right: 0 !important
    }

    .no-pad-imp-xs {
        padding: 0 !important
    }

    .no-pad-right-imp-xs {
        padding-right: 0 !important
    }

    .no-pad-left-imp-xs {
        padding-left: 0 !important
    }

    .no-pad-top-imp-xs {
        padding-top: 0 !important
    }

    .no-pad-bottom-imp-xs {
        padding-bottom: 0 !important
    }

    .pad-7-right-imp-xs {
        padding-right: 7px !important
    }

    .pad-8-left-imp-xs {
        padding-left: 8px !important
    }

    .pad-10-left-imp-xs {
        padding-left: 10px !important
    }

    .pad-10-right-imp-xs {
        padding-right: 10px !important
    }

    .pad-10-bottom-imp-xs {
        padding-bottom: 10px !important
    }

    .pad-15-left-xs-imp {
        padding-left: 15px !important;
    }

    .pad-20-imp-xs {
        padding: 20px !important
    }


    .pad-25-right-imp-xs {
        padding-right: 25px !important;
    }

    .pad-30-imp-xs {
        padding: 30px !important;
    }


    .pad-30-right-imp-xs {
        padding-right: 30px !important;
    }

    .pad-40-imp-xs {
        padding: 40px !important
    }

    .no-margin-xs-imp {
        margin: 0 !important
    }



}

/* reCAPTCHA */
.captcCont {
    max-width: 256px;
    margin: auto;
}

.width-100-percent{
    width: 100%;
}

/* Sub Navigation Start */
.subnavgroup li a {
    padding-top: 9px;
    padding-bottom: 11px;
    display: block;
    cursor: pointer;
}

.subnav-wrap {
    border-left: 1px solid #e1e1e1;
    padding-right: 30px;
}

#magic-line {
    position: absolute;
    width: 4px;
    background-color: #00549A;
    height: 52px;
    left: 0;
    top: 0;
}

.subnavgroup .subnav_active a{color:#111}
.subnavgroup li:not(.subnav_active) a:hover {
    color: #003778;
    text-decoration: underline
}
/* Sub Navigation End*/
/* for the long article container image */
.long-article-container-text-image .img-container img {
    min-height: 320px;
    border-radius: 10px;
}

/* ENT icon library */
.global-navigation #connector-search [type="submit"]:after,
.global-navigation .connector-brand a:before,
.global-navigation .connector-brand:after,
.slick-prev:before, .slick-next:before,
.global-navigation .menu-flyout-visible .sub-nav-level-1:after {
    font-family: 'bell-icon';
}

/* ENT footer search button hover */
.search-bar-footer-wrap .search-btn:hover {
    cursor: pointer;
}

/*
    Tablist
    InPageNavigation
*/

.inpage-navigation {
    overflow-x: auto;
}

.tablist {
    padding-left: 0;
}

.tablist .active span {
    border-bottom: 4px solid #00549a;
    padding-bottom: 13px;
    color: #111;
}

/*custom_6ColumnTable css*/
.table.custom_6ColumnTable {
    border: none;
}

.scrollable-table-desktop .table.custom_6ColumnTable {
    width: 1300px;
}

.scrollableContainer.scrollable-table-desktop {
    width: 100%;
    overflow: hidden;
    overflow-x: scroll;
    position: relative;
}

.scrollableContainerShadow.scrollable-shadow-desktop {
    position: relative;
}

.left.scrollableContainerShadow.scrollable-shadow-desktop:before {
    width: 46px;
    -webkit-transition: width .5s;
    transition: width .5s;
}

.scrollableContainerShadow.scrollable-shadow-desktop:before {
    width: 0;
    pointer-events: none;
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    background: -moz-linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
    background: -webkit-linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
    background: linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
    -webkit-transition: width .1s;
    transition: width .1s;
}

.right.scrollableContainerShadow.scrollable-shadow-desktop:after {
    width: 46px;
    -webkit-transition: width .5s;
    transition: width .5s;
}

.scrollableContainerShadow.scrollable-shadow-desktop:after {
    width: 0;
    pointer-events: none;
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: 1;
    background: -moz-linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
    background: -webkit-linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
    background: linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
    -webkit-transition: width .1s;
    transition: width .1s;
}

.scrollable-table-desktop.scrollableContainer > table {
    min-width: 630px;
}

.scrollable-table-desktop.scrollableContainer::-webkit-scrollbar-track {
    background-color: #BABEC2;
}

.scrollable-table-desktop.scrollableContainer::-webkit-scrollbar {
    height: 8px;
    background-color: #F5F5F5;
}

.scrollable-table-desktop.scrollableContainer::-webkit-scrollbar-thumb {
    background-color: #00549A;
}


    .table.custom_6ColumnTable thead th {
        width: calc(48% / 3);
        min-height: 60px;
        height: 100%;
        padding: 20px;
        font-weight: normal;
        vertical-align: middle;
        border: none;
        display: inline-block;
    }

    .table.custom_6ColumnTable thead th:not(:last-child) {
        border-right: 1px solid #003778;
    }

    .table.custom_6ColumnTable thead th:first-child {
        border-left: 1px solid #003778;
    }

    .table.custom_6ColumnTable thead th.no-border-right:not(:last-child) {
        border-right: 0;
    }

    .table.custom_6ColumnTable th:first-child, .table.custom_6ColumnTable tbody td:first-child {
        width: 21.4%;
    }

    .table.custom_6ColumnTable th:nth-child(5), .table.custom_6ColumnTable tbody td:nth-child(5) {
        width: 17.3%;
    }

    .table.custom_6ColumnTable th:last-child, .table.custom_6ColumnTable tbody td:last-child {
        width: 13.3%;
    }

    .scrollable-table-desktop .table.custom_6ColumnTable th:first-child,
    .scrollable-table-desktop .table.custom_6ColumnTable tbody td:first-child,
    .scrollable-table-desktop .table.custom_6ColumnTable th:nth-child(2), 
    .scrollable-table-desktop .table.custom_6ColumnTable tbody td:nth-child(2) {
        width: 200px
    }

    .scrollable-table-desktop .table.custom_6ColumnTable th:nth-child(3),
    .scrollable-table-desktop .table.custom_6ColumnTable tbody td:nth-child(3) {
        width: 300px;
    }

    .scrollable-table-desktop .table.custom_6ColumnTable th:nth-child(4),
    .scrollable-table-desktop .table.custom_6ColumnTable tbody td:nth-child(4),
    .scrollable-table-desktop .table.custom_6ColumnTable th:nth-child(5),
    .scrollable-table-desktop .table.custom_6ColumnTable tbody td:nth-child(5),
    .scrollable-table-desktop .table.custom_6ColumnTable th:nth-child(6),
    .scrollable-table-desktop .table.custom_6ColumnTable tbody td:nth-child(6) {
        width: 200px;
    }

    .scrollable-table-desktop .table.custom_6ColumnTable .custom_2ColumnHead th:first-child {
        width: 400px;
    }

    .scrollable-table-desktop .table.custom_6ColumnTable .custom_2ColumnHead th:last-child {
        width: 900px;
    }

    .scrollable-table-desktop .table.custom_6ColumnTable thead {
        border-bottom: 1px solid #003778;
    }

    .table.custom_6ColumnTable tbody td {
        width: calc(48% / 3);
        border: none;
        display: inline-block;
        min-height: 50px;
    }

    .table.custom_6ColumnTable tbody td {
        border-right: 1px solid #dee2e6;
    }

        .table.custom_6ColumnTable tbody td:first-child {
            border-left: 1px solid #dee2e6;
        }

.table.custom_6ColumnTable tbody tr > td {
    padding: 15px 20px 14px;
}

.table.custom_6ColumnTable tbody tr {
    border-bottom: 1px solid #dee2e6
}

.table.custom_6ColumnTable tbody tr > td {
    display: flex;
    align-items: center;
}

.table.custom_6ColumnTable tbody tr:nth-child(odd) {
    background-color: #fff
}

/*Tooltip overrides*/
.tooltip-interactive.sm-tooltip-cont > .tooltip {
    width: 100%;
    min-width: 150px;
    font-family: Helvetica, Arial, sans-serif;
}

    .tooltip-interactive.sm-tooltip-cont > .tooltip > .tooltip-inner {
        width: 100%;
        padding: 15px 15px 15px 20px;
        box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
        border: 1px solid #E1E1E1;
    }

    .tooltip-interactive.sm-tooltip-cont > .tooltip.bs-tooltip-left .arrow {
        margin-top: 5px;
        -moz-filter: drop-shadow(1px 0px 0px rgba(0,0,0,0.15));
        filter: drop-shadow(1px 0px 0px rgba(0,0,0,0.15));
    }

        .tooltip-interactive.sm-tooltip-cont > .tooltip.bs-tooltip-left .arrow::before {
            border-width: 10px 0 10px 10px;
            top: 50%;
            transform: translateY(-60%);
        }

    

.tooltip-interactive.sm-tooltip-cont > .tooltip {
    width: auto;
    min-width: 0;
}

    .tooltip-interactive.sm-tooltip-cont > .tooltip > .tooltip-inner {
        white-space: nowrap;
        padding: 15px 20px;
        width: auto;
    }

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    /* IE10+ CSS styles go here */
    .tooltip-interactive.sm-tooltip-cont > .tooltip.bs-tooltip-bottom .arrow::before {
        top: 15px;
        border-width: 15px 15px 15px;
        border-right-color: #fff;
        border-top-color: transparent;
        border-bottom-color: #fff;
        transform: rotate(225deg);
        box-shadow: 7px 7px 15px 0px rgba(0,0,0,0.12);
    }

    .tooltip-interactive.sm-tooltip-cont > .tooltip.bs-tooltip-bottom .arrow{
        box-shadow: none;
        width: 0;
    }
}


/*Icons Override*/
ul.social_icons_cont li .icon_background .icon2.icon-mail_nobg:before {
    top: 18%;
}

/*floating icons override*/
.social_icons_wrapper.pos-fixed-top-imp ul.social_icons_cont {
    margin-top: 30px;
}
.social_icons_wrapper  ul.social_icons_cont.flickrfix {
    margin-top: 30px;
}

/*lists custom spacing*/
ul.list-custom-spacing > li, ol.list-custom-spacing > li {
    margin-bottom: 7px;
    padding-left: 8px;
}

    ul.list-custom-spacing > li:last-child, ol.list-custom-spacing > li:last-child {
        padding-left: 8px;
        margin-bottom: 0;
    }


ul.alert-list > li.error {
    padding-bottom: 5px;
}

.alert-list .error a:hover {
    color: #bd2025;
    text-decoration: none;
}

/*
    Generic Styles
*/
.zIndex-2 {
    z-index: 2;
}

.zIndex-20 {
    z-index: 20;
}

.top10{
    top:10px;
}
.box-50 {
    width: 50px;
    min-width: 50px;
    height: 50px;
}

.mainNonFloatcontent .mainContent {
    max-width: 590px;
    /*width: calc(100% - 370px);*/
}

.right-0 {
    right: 0;
}

.txtSize36 {
    font-size: 36px;
}

.maxWidth-70-minWidth-630 {
    max-width: 70%;
    min-width: 630px;
}

.letter-spacing-neg-000400 {
    letter-spacing: -0.4px;
}

.letter-spacing-neg-000500 {
    letter-spacing: -0.5px;
}

.letter-spacing-neg-000700 {
    letter-spacing: -0.7px;
}

.letter-spacing-0 {
    letter-spacing: 0;
}

.line-height-14 {
    line-height: 14px;
}

.line-height-18 {
    line-height: 18px;
}

.line-height-21 {
    line-height: 21px;
}

.line-height-20 {
    line-height: 20px;
}


.line-height-22 {
    line-height: 22px;
}

.line-height-23 {
    line-height: 23px;
}

.line-height-26 {
    line-height: 26px;
}

.line-height-46{
    line-height: 46px;
}

textarea.form-control.height-120 {
    height: 120px;
}


.bgGray20.change-onHover:hover{
    background-color: #989898 !important; 
}

.before-top-neg-2:before{
    top: -2px;
}

.pad-27-left{
    padding-left: 27px;
}

.border-radius-top-10 {
    border-radius: 10px 10px 0 0;
}

.border-radius-bottom-10 {
    border-radius: 0 0 10px 10px;
}

.border-radius-10 {
    border-radius: 10px;
}

.before-top-0:before {
    top: 0;
}

.list-style-disc {
    list-style-type : disc;
}

.list-style-dashed {
    list-style-type : none;
    text-indent : -24px;
}

    .list-style-dashed:before {
        content: "\2013";
        text-indent: -5px;
        padding-right: 10px;
    }

.txtSize74 {
    font-size: 74px;
}

.txtSize72 {
    font-size : 72px;
}

.box-shadow-gray {
    box-shadow: 0 6px 25px 0 rgba(0,0,0,0.12);
}
 

.accordion-wrap .icon-collapse-outline-circled:before {
    content: "\e90e";
    font-weight: 500;
}

.accordion-wrap .icon-exapnd-outline-circled:before {
    content: "\e90f";
    font-weight: 500;
}

/* Checkbox Override */
.contactUs-form .graphical_ctrl_checkbox .ctrl_element:after {
    left: 7px;
    top: 3px;
    width: 8px;
    height: 14px;
}


/* Cards */
/* Cards Slider */
.slidingCardsContainer .slick-track {
    display: flex;
    overflow: hidden;
    padding: 20px 30px 35px 30px;
}

.slidingCardsContainer .slick-slide {
    height: auto;
}

.slidingCardsContainer .slick-slide:not(:last-child) {
    margin-right: 15px;
}

.slidingCardsContainer .slick-slide {
    box-shadow: 0 0px 15px 0 rgba(0,0,0,0.12);
    border-radius: 10px;
    display: flex;
}

/* .slidingCardsContainer .slick-list {
    padding: 20px 0 20px;
    margin: 0 -30px;
    padding: 0 30px;
} */

.slidingCardsContainer .slick-prev:hover, .slidingCardsContainer .slick-next:hover, .slidingCardsContainer .slick-prev:focus, .slidingCardsContainer .slick-next:focus, .slidingCardsContainer .slick-prev, .slidingCardsContainer .slick-next {
    opacity: 1;
}

    .slidingCardsContainer .slick-prev:hover:before, .slidingCardsContainer .slick-next:hover:before, .slidingCardsContainer .slick-prev:focus:before, .slidingCardsContainer .slick-next:focus:before {
        opacity: .75;
        color: #00549A
    }

    .slidingCardsContainer .slick-next.slick-disabled, .slidingCardsContainer .slick-prev.slick-disabled {
        opacity: 0
    }

.slidingCardsContainer .slick-next,
.slidingCardsContainer .slick-prev {
    height: 100%;
    top: 0;
    border-radius: 0;
    width: 100px;
}

.slidingCardsContainer .slick-next {
    background: linear-gradient(to left, #f4f4f4 50%, transparent 50%), radial-gradient(ellipse at 90% 50%, rgba(0,0,0,0.2), transparent 68%, transparent);
    box-shadow: 20px 0px 0px 0px #f4f4f4;
}

.slidingCardsContainer .slick-prev {
    background: linear-gradient(to right, #f4f4f4 50%, transparent 50%), radial-gradient(ellipse at 10% 50%, rgba(0,0,0,0.2), transparent 68%, transparent);
    box-shadow: -20px 0px 0px 0px #f4f4f4;
}

.slick-prev:before, .slick-next:before {
    top: 50%;
}

.slidingCardsContainer .slick-prev:before, .slidingCardsContainer .slick-next:before {
    font-size: 27px;
    opacity: 1;
    margin-top : -16px;
}

.slidingCardsContainer .slick-next {
    right: 0px;
}

.slidingCardsContainer .slick-prev {
    left: 0px;
}

.slidingCardsContainer .slick-slide > div, .slidingCardsContainer .slick-slide .slickSlide, .slidingCardsContainer .slick-slide .slickSlide .cards {
    height: 100%;
    width: 100%;
}

.slidingCardsContainer .slick-dots li button {
    background-color: transparent;
    border-color: #999;
}

    .slidingCardsContainer .slick-dots li button:focus {
        outline: -webkit-focus-ring-color auto 5px !important;
        outline: 1px solid #2672cb !important;
        opacity: 1
    }

.slidingCardsContainer .slick-dots li.slick-active button {
    background-color: #999;
}

.slidingCardsSection .slick-dots {
    margin-top: 5px;
    position: absolute;
    bottom: 9px;
}

/* .slidingCardsContainer .slick-slide[aria-hidden=true] {
    visibility: hidden;
} */

.slidingCardsContainer > button[aria-disabled="true"] {
    display: none !important;
}

.tilesContent {
    height: calc(100% - 175px);
}

/* slidingCardsSection */
.slidingCardsSection button.slick-arrow {
    height: 50px;
    width: 50px;
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
    background-color: white;
}

.slidingCardsSection .slick-next {
    background: none;
    box-shadow: none;
    right: -20px;
}

.slidingCardsSection .slick-prev:before, .slidingCardsSection .slick-next:before {
    right: 10px;
    top: 0;
    position: relative;
    right: 0;
    left: 0;
    margin-top: 0;
    font-size: 22px;
}

.slidingCardsSection .slick-prev {
    background: none;
    box-shadow: none;
    left: -20px;
}

.slidingCardsSection button.slick-arrow:hover {
    border: 2px solid #2672cb;
    box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
    background-color: white;
}


/*Cards*/
.cards {
    position: relative;
    height: 100%
}

.cardsWithImage .cardsImage {
    background-color: #babec2;
    height: 176px;
    position: relative;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
}

    .cardsWithImage .cardsImage img {
        margin: 0;
        position: absolute;
        top: 50%;
        left: 50%;
        margin-right: -50%;
        transform: translate(-50%,-50%);
        max-height: 176px;
    }

.cards .cardsContent {
    padding: 30px 30px 30px 30px;
}

    .cards .cardsContent span {
        font-size: 12px;
        line-height: 18px
    }

    .cards .cardsContent h3 {
        line-height: 18px;
    }

.cards.cardsWithImage .cardsContent p {
    color: #666
}

.cards a:hover span,
.cards a:hover p,
.cards a:hover i,
.cards a:hover i:before,
.cards a:focus span,
.cards a:focus p,
.cards a:focus i,
.cards a:focus i:before {
    text-decoration: none;
    display: inline-block;
}

.slidingCardsContainer {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
}

/* SearchBar */

.searchBox {
    position: relative;
    padding: 9px 50px 9px 20px;
    margin: 0;
    background-color: #fff;
    color: #111;
    height: 44px;
    border-radius: 2px;
    display: inline-block;
    border: 1px solid #d4d4d4;
    box-shadow: inset 0.7px 0.7px 3px 0 rgba(0, 0, 0, 0.24);
    border-radius: 20px;
}

.searchBar button {
    background-color: transparent;
    border: none;
}

.searchBar button:focus, .searchBar input:focus {
    outline-width: 2px;
    outline-style: solid;
    outline-color: #4d90fe !important;
    outline-offset: 2px;
}

.searchBar input:focus {
    outline-offset: 0;
}

.searchBar button[type="reset"] {
    font-size: 16px;
    opacity: 0.5;
}

    .searchBar button[type="reset"]:focus {
        opacity: 1;
    }

.searchButtonContainer {
    top: 0;
    right: 0;
}

.btn-pad-radius {
    padding: 9px 35px;
    border-radius: 20px;
    border: none;
}

/* Custom select single icon */
.form-control-select + span.select-single-icon {
    position: absolute;
    transform: translateY(-50%) rotate(180deg);
    font-size: 6px;
    color: #00549a;
    right: 15px;
    height: 44px;
    top: 15px;
}

.mainContent {
    max-width: 590px;
}

.textarea-resize-none {
    resize: none;
}

/* h2 bellSlimBlack letter-spacing */
h2.bellSlimBlack {
    letter-spacing: -0.4px;
}

h2.bellSlimBlack.txtBold {
    font-weight: normal;
}

/* click and drag scroll */
.clickdragscroll.active {
    cursor: grabbing;
    cursor: -webkit-grabbing;
    transform: scale(1);
}

.clickdragscroll {
    cursor: pointer;
}

.scrollable-area .subnav-scroll{
    width: 100% !important;
}

@media (min-width: 1441px) {
    .bannerContainer.v2 {
        min-height: 440px;
    }

    /* slidingCardsSection */
    .slidingCardsSection button.slick-arrow {
        background: none;
        box-shadow: none;
        font-weight: 600;
    }

    .slidingCardsSection .slick-next{
        right: -60px;
    }

    .slidingCardsSection .slick-prev {
        left: -60px;
    }
} 

.parentImgCont.v2 {
    width: 100%;
    margin: 0;
    overflow: hidden;
}

.v2 .banner-crop-img {
    position: absolute;
    width: 100%;
    max-width: 100%
}
.footer-icon .icon-o.icon-o-handset, .footer-icon .icon-o.icon-o-location {
    top: -2px;
     left: 0px; 
}


/*For Tablet and Mobile*/
@media screen and (max-width: 991.98px) {

    .slidingCardsContainer .slick-next {
        right: -30px;
    }

    .slidingCardsContainer .slick-prev {
        left: -30px;
    }
    .slidingCardsContainer .slick-list {
        /* padding: 20px 0 20px; */
        margin: 0 -30px;
        padding: 0 30px;
    }
    .global-navigation #connector-search-button .icon-magnifying-glass:before,
    .global-navigation .connector-nav-open-button .icon-plus:before,
    .global-navigation .connector-area:after,
    .global-navigation .menu-flyout-visible li:not(.no-sub-nav) .sub-nav-level-1:after,
    .global-navigation .menu-flyout-visible .sub-nav-level-1:after, .sub-nav-root .sub-nav-group .sub-nav-item li a::after,
    .global-navigation .menu-flyout-visible .no-sub-nav .sub-nav-level-1::after
    {
        font-family: 'bell-icon';
    }

    .contactUs-form .graphical_ctrl_checkbox .ctrl_element:after {
        left: 7px;
        top: 3px;
        width: 8px;
        height: 14px;
    }
    /* for the long article container image */
    .long-article-container-text-image .img-container img{
        min-height: 235px;
    }
}


/* Media queries */
/* 768 Below */
@media (max-width: 767.98px) {
    .slidingCardsContainer .slick-slide[aria-hidden=true] {
        visibility: inherit;
    }

    /*Table scrollable on mobile*/
    .scrollableContainer {
        width: 100%;
        overflow: hidden;
        overflow-x: scroll;
        position: relative;
    }

    .scrollableContainerShadow {
        position: relative;
    }

    .left.scrollableContainerShadow:before {
        width: 46px;
        -webkit-transition: width .5s;
        transition: width .5s;
    }

    .scrollableContainerShadow:before {
        width: 0;
        pointer-events: none;
        content: "";
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        z-index: 1;
        background: -moz-linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
        background: -webkit-linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
        background: linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
        -webkit-transition: width .1s;
        transition: width .1s;
    }

    .right.scrollableContainerShadow:after {
        width: 46px;
        -webkit-transition: width .5s;
        transition: width .5s;
    }

    .scrollableContainerShadow:after {
        width: 0;
        pointer-events: none;
        content: "";
        position: absolute;
        top: 0;
        bottom: 0;
        right: 0;
        z-index: 1;
        background: -moz-linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
        background: -webkit-linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
        background: linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);
        -webkit-transition: width .1s;
        transition: width .1s;
    }

    .scrollableContainer > table {
        min-width: 630px;
    }

    .scrollableContainer::-webkit-scrollbar-track {
        background-color: #BABEC2;
    }

    .scrollableContainer::-webkit-scrollbar {
        height: 8px;
        background-color: #F5F5F5;
    }

    .scrollableContainer::-webkit-scrollbar-thumb {
        background-color: #00549A;
    }

    .mainNonFloatcontent .mainContent {
        min-width: 0;
        width: 100%;
    }
    /*Table scrollable on mobile*/


    /*lists custom spacing*/
    ul.list-custom-spacing > li, ol.list-custom-spacing > li {
        margin-bottom: 5px;
    }

        ul.list-custom-spacing > li:last-child, ol.list-custom-spacing > li:last-child {
            margin-bottom: 0;
        }



    /*Generic CSS*/
    .maxWidth-70-minWidth-630 {
        max-width: 100%;
        min-width: 0;
    }

    .no-border-xs {
        border: 0;
    }

    .line-height-22-xs {
        line-height: 22px;
    }

    .line-height-26-xs {
        line-height: 26px;
    }
    .line-height-18-xs {
        line-height: 18px;
    }

    .letter-spacing-neg-000400-xs {
        letter-spacing: -0.4px;
    }

    .relative-xs{
        position: relative;
    }

    .flex-direction-column-reverse-xs {
        flex-direction: column-reverse;
    }

    .flex-1-1-auto-xs {
        flex: 1 1 auto;
    }
    .banner-crop-img {
        min-width: 767px;
    }

    /* Features accordion */
    dl dt:first-child {
        padding-top: 20px !important;
    }
    dl dt:not(:first-child) {
        padding-top: 30px !important;
    }
    /* Features accordion ends */

    .parentImgCont img.banner-crop-img.leftView-35-xs {
        top: 80%;
        left: 35%;
        transform: translate(-35%,-50%);
    }

    .letter-spacing-normal-xs {
        letter-spacing: normal;
    }

    .txtSize60-xs {
        font-size: 60px;
    }

    .arial-xs {
        font-family: Arial, sans-serif;
        letter-spacing: 0
    }

    .font-bold-xs {
        font-weight : bold;
    }

    .width-100-percent-xs{width:100%;}
    
    .width-66-xs {width: 66px;}

    .txtLeft-xs {
        text-align: left;
    }

    .d-xs-none {display: none;}
    .d-xs-block {display:block!important;}

    .justify-center-xs {
        justify-content: center;
    }

    .border-top-bottom-xs {
        border-left: none;
        border-right: none;
        border-top: 1px solid #D4D4D4;
        border-bottom: 1px solid #D4D4D4;
    }

    .negative-15margin-xs {
        margin: 0 -15px;
    }

    .no-border-xs {
        border-radius: 0;
        border: none;
    }

    /*.slidingCardsContainer .slick-list {
        margin: 0 0 0 -15px;
        padding: 0 0;
    }*/

    .slidingCardsContainer .slick-track {
        padding: 20px 30px 35px 0px;
    }

    /*.slidingCardsContainer .slick-list {
        margin-left: -30px;
        overflow: visible
    }*/

    .slidingCardsContainer .slick-list.margin-left-neg-5-xs {
        margin-left: -5px;
        
    }

    textarea.form-control.height-134-xs {
        height: 134px;
    }

    /* Side Nav hide */
    #magic-line {
        display: none;
    }

    .subnav-wrap {
        border: 1px solid #D4D4D4;
        margin-bottom: 30px;
        border-radius: 10px;
        padding-top: 25px;
        padding-right: 0;
    }

    .subnavgroup {
        padding: 0 15px;
    }

    .subnavgroup li a {
        padding-top: 0;
        padding-bottom: 15px;
        display: block;
        cursor: pointer;
    }

    /* for the long article container image */
    .long-article-container-text-image .img-container img{
        min-height: 150px;
    }

}

/* 640 Below this is only for slidingCardsContainer*/
@media (max-width: 639.98px) {

    /*Cards breakpoints*/
    .slidingCardsContainer {
        padding: 0
    }

    .cards .cardsContent h3 {
        font-size: 14px
    }

    /* .slidingCardsContainer .slick-list {
        margin-left: -30px;
        overflow: visible
    } */

    .slidingCardsContainer .slick-slide:first-child {
        margin-left: 0
    }

}


@media (min-width:768px) { /* Tablet and larger */
    /*Generic CSS*/
    .force-visible-sm {
        display: block !important;
    }

    .bgBlue-sm {
        background-color: #00549a;
    }

    .bgGray19-sm {
        background-color: #f4f4f4;
    }

    .txtWhite-sm {
        color: #ffffff;
    }

    .flex-w-03950-sm {
        flex: 0 0 39.5%;
    }

    .flex-w-06050-sm {
        flex: 0 0 60.5%;
    }

    .max-width-3950-md {
        max-width: 39.5%;
    }

    .max-width-6050-md {
        max-width: 60.5%;
    }

    .pad-15-top-bottom-sm {
        padding-top: 15px;
        padding-bottom: 15px;
    }

    .pad-30-left-right-sm {
        padding-left: 30px;
        padding-right: 30px;
    }

    .pad-0-top-sm-up {
        padding-top: 0;
    }

    .pad-0-top-bottom-sm-up {
        padding-top: 0;
        padding-bottom: 0;
    }

    .pad-15-left-sm-up {
        padding-left: 15px;
    }

    .pad-30-right-sm-up {
        padding-right: 30px;
    }
    .v2 .centerView {
        top: 70%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .width-66-sm {width: 66px;}
    .width-136-sm {width: 136px;}
    .width-280-sm {width: 280px;}
    .width-434-sm {width: 434px;}

    .flex-column-sm{
        display: flex;
        flex-direction: column;
    }

    .margin-top-15-sm {
        margin-top: 15px;
    }

    .line-height-38-sm-up{
        line-height: 38px;
    }

    .max-width-500-sm{
        max-width: 500px;
    }

    /* Scrollable Contents */
    .scrollable-area-content > div {
        flex: 1;
    }

        .scrollable-area-content > div:first-child {
            max-width: 30%
        }

        .scrollable-area-content > div:last-child {
            max-width: 70%
        }

    .scrollable-contents {
        min-width: 630px;
    }
    /* Scrollable Contents end */

}




@media (min-width:992px) { /* Desktop and larger */
    /*Generic CSS*/
    .flex-w-03150-md {
        flex: 0 0 31.5%;
    }

    .flex-w-06850-md {
        flex: 0 0 68.5%;
    }

    .max-width-3150-md {
        max-width : 31.5%;
    }

    .max-width-6850-md {
        max-width: 68.5%;
    }

    /* Banner */
    .bannerTitle {
        margin-left: 13%;
    }
    .global-navigation .menu-flyout-visible .sub-nav-item .sub-nav-level4 li .sub-nav-item-2:after {
        font-family: 'bell-icon'
    }
    .btn-pad-radius {
        padding: 11px 30px;
        border-radius: 20px;
        border: none;
    }

    
    .width-66-md {width: 66px;}
    .width-136-md {width: 136px;}
    .width-280-md {width: 280px;}
    .width-434-md {width: 434px;}

    /* div.accaccordion-wrap .accessible-accordion div:first-child {
        display: block !important;
    } */
}

/*For Tablet only*/
@media (min-width:768px) and (max-width:991.98px) {
    .scrollable-contents {
        min-width: 465px;
    }
}