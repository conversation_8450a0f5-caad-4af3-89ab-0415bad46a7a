;(function ($) {
    
    var pluginName = 'productCarousel',
        defaults = {
            bell: {
                tilesMobile: 1,
                tilesTablet: 2,
                tilesDesktop: 3,
                minTablet: 768,
                minDesktop: 992
            },
            slick: {
                infinite: false,
                dots: true,
                adaptiveHeight: true
            }            
        };

    this.currentScreenWidth = "";

    function Plugin(element, options) {

        this.element = element;
        this.$element = $(element);
        this.options = $.extend({}, defaults, options);

        this._defaults = defaults;
        this._name = pluginName;

        this.init();
    }

    Plugin.prototype.init = function () {

        var self = this;

        this.matchMobile = window.matchMedia('(max-width: ' + (this.options.bell.minTablet - 1) + 'px)');
        this.matchTablet = window.matchMedia('(min-width: ' + this.options.bell.minTablet + 'px) and (max-width: ' + (this.options.bell.minDesktop - 1) + 'px)');
        this.matchDesktop = window.matchMedia('(min-width: ' + this.options.bell.minDesktop + 'px)');

        $(window).resize(debounce(onWindowResize.bind(self), 200)); 

        this.$element.slick(this.options.slick);
        
        var windowOnResizeWithContext = onWindowResize.bind(self);
        windowOnResizeWithContext();        
    };
    
    $.fn[pluginName] = function (options) {

        return this.each(function () {
            if (!$.data(this, 'plugin_' + pluginName)) {
                 $.data(this, 'plugin_' + pluginName, new Plugin(this, options));
            }
        });
    }

    function onWindowResize() {

        var newScreenWidth = this.matchMobile.matches ? "mobile" : (this.matchTablet.matches ? "tablet" : "desktop");

        if(newScreenWidth !== this.currentScreenWidth){

            this.currentScreenWidth = newScreenWidth;

            this.$element.slick('unslick');            

            if(this.matchMobile.matches){
                this.options.slick.slidesToShow = this.options.bell.tilesMobile;
                this.options.slick.slidesToScroll = this.options.bell.tilesMobile;

            }else if(this.matchTablet.matches){
                this.options.slick.slidesToShow = this.options.bell.tilesTablet;
                this.options.slick.slidesToScroll = this.options.bell.tilesTablet;                        

            }else{
                this.options.slick.slidesToShow = this.options.bell.tilesDesktop;
                this.options.slick.slidesToScroll = this.options.bell.tilesDesktop;
            }            

            this.$element.children('.prod-tile-empty').remove();

            var tileCount = this.$element.children().length;
            var blankTilesToAdd = (tileCount % this.options.slick.slidesToShow === 0) ? 0 : this.options.slick.slidesToShow - tileCount % this.options.slick.slidesToShow;

            if(blankTilesToAdd !== 0){
                for(var i = 0; i < blankTilesToAdd; i++){
                    this.$element.append('<div class="prod-tile-empty" aria-hidden="true"></div>');
                }
            }

            this.$element.slick(this.options.slick);
            
            this.$element.find(".tooltip-interactive").each(function(){
                $(this).tooltip();
            });
    
        }       
    }

    function debounce(func, wait, immediate) { var timeout; return function () { var context = this, args = arguments; var later = function () { timeout = null; if (!immediate) func.apply(context, args) }; var callNow = immediate && !timeout; clearTimeout(timeout); timeout = setTimeout(later, wait); if (callNow) func.apply(context, args) } };    


})(jQuery);