
// TreeGrid - start
function TreeGrid(treegridElem, doAllowRowFocus, doStartRowFocus) {
	function initAttributes() {

        //Polyfill IE- forEach
        if (window.NodeList && !NodeList.prototype.forEach) {
            NodeList.prototype.forEach = Array.prototype.forEach;
        }

        // process non-collapsible parent rows
        treegridElem.querySelectorAll('.not-collapsible-row').forEach(function (row) {
            row.setAttribute('aria-expanded', 'true');
        });

        // Make sure focusable elements are not in the tab order
        // They will be added back in for the active row
        setTabIndexOfFocusableElems(treegridElem, -1);

        // Add tabindex="0" to first row, "-1" to other rows
        // We will use the roving tabindex method since aria-activedescendant
        // does not work in IE
        var rows = getAllRows();
        var index = rows.length;
        var startRowIndex = doStartRowFocus ? 0 : -1;

        while (index--) {
            if (doAllowRowFocus) {
                rows[index].tabIndex = index === startRowIndex ? 0 : -1;
            }
            else {
                setTabIndexForCellsInRow(rows[index], -1);
                moveAriaExpandedToFirstCell(rows[index]);
            }
        }

        if (doStartRowFocus) {
            return;
        }

        // Start with cell focus
        var firstCell = getNavigableCols(rows[0])[0];
        setTabIndexForCell(firstCell); 

    }

    function setTabIndexForCell(cell, tabIndex) {
        var focusable = getFocusableElems(cell)[0] || cell;
        focusable.tabIndex = tabIndex;
    }

    function setTabIndexForCellsInRow(row, tabIndex) {
        var cells = getNavigableCols(row);
        var cellIndex = cells.length;
        while (cellIndex--) {
            setTabIndexForCell(cells[cellIndex], tabIndex);
        }
    }

    function getAllRows() {
        var nodeList = treegridElem.querySelectorAll('tbody.billing-treegrid-tbody > tr');
        return Array.prototype.slice.call(nodeList);
    }

    function getFocusableElems(root) {
        // textarea not supported as a cell widget as it's multiple lines
        // and needs up/down keys
        // These should all be descendants of a cell
        var nodeList = root.querySelectorAll('a,button,input,td>[tabindex]');
        return Array.prototype.slice.call(nodeList);
    }

    function setTabIndexOfFocusableElems(root, tabIndex) {
        var focusableElems = getFocusableElems(root);
        var index = focusableElems.length;
        while (index--) {
            focusableElems[index].tabIndex = tabIndex;
        }
    }

    function getAllNavigableRows() {
        var nodeList = treegridElem.querySelectorAll('tbody.billing-treegrid-tbody > tr:not([class~="hidden"])');
        // Convert to array so that we can use array methods on it
        return Array.prototype.slice.call(nodeList);
    }

    function getNavigableCols(currentRow) {
        var nodeList = currentRow.querySelectorAll('td[role="gridcell"]');
        return Array.prototype.slice.call(nodeList);
    }

    function restrictIndex(index, numItems) {
        if (index < 0) {
            return 0;
        }
        return index >= numItems ? index - 1 : index;
    }

    function focus(elem) {
        elem.tabIndex = 0; // Ensure focusable
        elem.focus();
    }

    function focusCell(cell) {
        // Check for focusable child such as link or textbox
        // and use that if available
        var focusableChildren = getFocusableElems(cell);
        focus(focusableChildren[0] || cell);
    }

    // Restore tabIndex to what it should be when focus switches from
    // one treegrid item to another
    function onFocusIn(event) {
        var newTreeGridFocus =
            event.target !== window && treegridElem.contains(event.target) &&
            event.target;

        // The last row we considered focused
        var oldCurrentRow = enableTabbingInActiveRowDescendants.tabbingRow;
        if (oldCurrentRow) {
            enableTabbingInActiveRowDescendants(false, oldCurrentRow);
        }
        if (doAllowRowFocus && onFocusIn.prevTreeGridFocus &&
            onFocusIn.prevTreeGridFocus.localName === 'td') {
            // Was focused on td, remove tabIndex so that it's not focused on click
            onFocusIn.prevTreeGridFocus.removeAttribute('tabindex');
        }

        if (newTreeGridFocus) {
            // Stayed in treegrid
            if (oldCurrentRow) {
                // There will be a different current row that will be
                // the tabbable one
                oldCurrentRow.tabIndex = -1;
            }

            // The new row
            var currentRow = getRowWithFocus();
            if (currentRow) {
                currentRow.tabIndex = 0;
                // Items within current row are also tabbable
                enableTabbingInActiveRowDescendants(true, currentRow);
            }
        }

        onFocusIn.prevTreeGridFocus = newTreeGridFocus;
    }

    // Set whether interactive elements within a row are tabbable
    function enableTabbingInActiveRowDescendants(isTabbingOn, row) {
        if (row) {
            setTabIndexOfFocusableElems(row, isTabbingOn ? 0 : -1);
            if (isTabbingOn) {
                enableTabbingInActiveRowDescendants.tabbingRow = row;
            }
            else {
                if (enableTabbingInActiveRowDescendants.tabbingRow === row) {
                    enableTabbingInActiveRowDescendants.tabbingRow = null;
                }
            }
        }
    }

    // The row with focus is the row that either has focus or an element
    // inside of it has focus
    function getRowWithFocus() {
        return getContainingRow(document.activeElement);
    }

    function getContainingRow(start) {
        var possibleRow = start;
        if (treegridElem.contains(possibleRow)) {
            while (possibleRow !== treegridElem) {
                if (possibleRow.localName === 'tr') {
                    return possibleRow;
                }
                possibleRow = possibleRow.parentElement;
            }
        }
    }

    function isRowFocused() {
        return getRowWithFocus() === document.activeElement;
    }

    // Note: contenteditable not currently supported
    function isEditableFocused() {
        var focusedElem = document.activeElement;
        return focusedElem.localName === 'input';
    }

    function getColWithFocus(currentRow) {
        if (currentRow) {
            var possibleCol = document.activeElement;
            if (currentRow.contains(possibleCol)) {
                while (possibleCol !== currentRow) {
                    if (possibleCol.localName === 'td') {
                        return possibleCol;
                    }
                    possibleCol = possibleCol.parentElement;
                }
            }
        }
    }

    function getLevel(row) {
        return row && parseInt(row.getAttribute('aria-level'));
    }

    // Move backwards (direction = -1) or forwards (direction = 1)
    // If we also need to move down/up a level, requireLevelChange = true
    // When
    function moveByRow(direction, requireLevelChange) {
        var currentRow = getRowWithFocus();
        var requiredLevel = requireLevelChange && currentRow &&
            getLevel(currentRow) + direction;
        var rows = getAllNavigableRows();
        var numRows = rows.length;
        var rowIndex = currentRow ? rows.indexOf(currentRow) : -1;
        // When moving down a level, only allow moving to next row as the
        // first child will never be farther than that
        var maxDistance = requireLevelChange && direction === 1 ? 1 : NaN;

        if (requiredLevel == 0) return;

        // Move in direction until required level is found
        do {
            if (maxDistance-- === 0) {
                return; // Failed to find required level, return without focus change
            }
            rowIndex = restrictIndex(rowIndex + direction, numRows);
        }
        while (requiredLevel && requiredLevel !== getLevel(rows[rowIndex]));

        if (!focusSameColInDifferentRow(currentRow, rows[rowIndex])) {
            focus(rows[rowIndex]);
        }
    }

    function focusSameColInDifferentRow(fromRow, toRow) {
        var currentCol = getColWithFocus(fromRow);
        if (!currentCol) {
            return;
        }

        var fromCols = getNavigableCols(fromRow);
        var currentColIndex = fromCols.indexOf(currentCol);

        if (currentColIndex < 0) {
            return;
        }

        var toCols = getNavigableCols(toRow);
        // Focus the first focusable element inside the <td>
        focusCell(toCols[currentColIndex]);
        return true;
    }

    function moveToExtreme(direction) {
        var currentRow = getRowWithFocus();
        if (!currentRow) {
            return;
        }
        var currentCol = getColWithFocus(currentRow);
        if (currentCol) {
            moveToExtremeCol(direction, currentRow);
        }
        else {
            // Move to first/last row
            moveToExtremeRow(direction);
        }
    }

    function moveByCol(direction) {
        var currentRow = getRowWithFocus();
        if (!currentRow) {
            return;
        }
        var cols = getNavigableCols(currentRow);
        var numCols = cols.length;
        var currentCol = getColWithFocus(currentRow);
        var currentColIndex = cols.indexOf(currentCol); 
        // First right arrow moves to first column
        var newColIndex = (currentCol || direction < 0) ? currentColIndex +
            direction : 0;
        // Moving past beginning focuses row
        if (doAllowRowFocus && newColIndex < 0) {
            focus(currentRow);
            return;
        }
        newColIndex = restrictIndex(newColIndex, numCols);
        focusCell(cols[newColIndex]);
    }

    function moveToExtremeCol(direction, currentRow) {
        // Move to first/last col
        var cols = getNavigableCols(currentRow);
        var desiredColIndex = direction < 0 ? 0 : cols.length - 1;
        focusCell(cols[desiredColIndex]);
    }

    function moveToExtremeRow(direction) {
        var rows = getAllNavigableRows();
        var newRow = rows[direction > 0 ? rows.length - 1 : 0];
        if (!focusSameColInDifferentRow(getRowWithFocus(), newRow)) {
            focus(newRow);
        }
    }

    function doPrimaryAction() {
        var currentRow = getRowWithFocus();
        if (!currentRow) {
            return;
        }

        //// If row has focus, open message
        //if (currentRow === document.activeElement) {
        //    alert('Message from ' + currentRow.children[2].innerText + ':\n\n' +
        //        currentRow.children[1].innerText);
        //    return;
        //}

        // If first col has focused, toggle expand/collapse
        toggleExpanded(currentRow);
    }

    function toggleExpanded(row) {
        var cols = getNavigableCols(row);
        var currentCol = getColWithFocus(row);
        if ((row === document.activeElement || currentCol === cols[3]) && isExpandable(row)) {
            changeExpanded(!isExpanded(row), row);
        }
    }


    //function getAllnotCollapsibleRows() {

    //    var rows = treegridElem.querySelectorAll('tbody > tr.not-collapsible-row');

    //    alert(rows.length);

    //}


    function changeExpanded(doExpand, row) {
        var currentRow = row || getRowWithFocus();

        //if (currentRow.classList.contains("not-collapsible-row")) {
        //    return;
        //}

        if (!currentRow) {
            return;
        }
        var currentLevel = getLevel(currentRow);
        var rows = getAllRows();
        var currentRowIndex = rows.indexOf(currentRow);
        var didChange;
        var doExpandLevel = [];
        doExpandLevel[currentLevel + 1] = doExpand;
       
        while (++currentRowIndex < rows.length) {
            var nextRow = rows[currentRowIndex];
            var rowLevel = getLevel(nextRow);
            if (rowLevel <= currentLevel) {
                break; // Next row is not a level down from current row
            }
            // Only expand the next level if this level is expanded
            // and previous level is expanded
            doExpandLevel[rowLevel + 1] =
                doExpandLevel[rowLevel] &&
                isExpanded(nextRow);
            var willHideRow = !doExpandLevel[rowLevel];
            var isRowHidden = nextRow.classList.contains('hidden');

            //if (nextRow.classList.contains("not-collapsible-row")) {
            //    noOfNotCollapsibleRows.push(nextRow);
            //}

            if (willHideRow !== isRowHidden) {
                if (willHideRow) {
                    nextRow.classList.add('hidden');
                }
                else {
                    nextRow.classList.remove('hidden');
                }
                didChange = true;
            }
        }

        //alert(noOfNotCollapsibleRows.join(","));

        if (didChange) {
            setAriaExpanded(currentRow, doExpand);
            return true;
        }


    }

    // Mirror aria-expanded from the row to the first cell in that row
    // (TBD is this a good idea? How else will screen reader user hear
    // that the cell represents the opportunity to collapse/expand rows?)
    function moveAriaExpandedToFirstCell(row) {
        var expandedValue = row.getAttribute('aria-expanded');
        var firstCell = getNavigableCols(row)[0];
        if (expandedValue) {
            firstCell.setAttribute('aria-expanded', expandedValue);
            row.removeAttribute('aria-expanded');
        }
    }

    function getAriaExpandedElem(row) {
        return doAllowRowFocus ? row : getNavigableCols(row)[0];
    }

	function isCollapsible(row) {
		return !row.classList.contains('not-collapsible-row');
	}

    function setAriaExpanded(row, doExpand) {
		var elem = getAriaExpandedElem(row),
			isHovered,
			isFocused;

		elem.setAttribute('aria-expanded', doExpand);

		if (elem.classList.contains('billing-treegrid-row-services') || elem.classList.contains('billing-treegrid-row-user')) {
			if (doExpand) {
				removeExpandForDetails(elem);
			} else {
				//Polyfill IE- matches
				if (!Element.prototype.matches) {
					Element.prototype.matches = Element.prototype.msMatchesSelector;
				}

				isHovered = elem.matches(':hover') || elem.querySelector(':hover') !== null;
				isFocused = elem.matches(':focus');

				if (isHovered || isFocused) {
					injectExpandForDetails(elem);
				}
			}
		}

		if (!elem.classList.contains('billing-treegrid-row-services')) {
			return;
		}

		if (doExpand) {
			return;
		}
    }

	function isExpandable(row) {
        var  elem = getAriaExpandedElem(row);

		return isCollapsible(row) && elem.hasAttribute('aria-expanded');
    }

    function isExpanded(row) {
        var elem = getAriaExpandedElem(row);
        return elem.getAttribute('aria-expanded') === 'true';
    }

    function onKeyDown(event) {
        var ENTER = 13;
        var UP = 38;
        var DOWN = 40;
        var LEFT = 37;
        var RIGHT = 39;
        var HOME = 36;
        var END = 35;
        var SPACE = 32;
        var CTRL_HOME = -HOME;
        var CTRL_END = -END;

        var numModifiersPressed = event.ctrlKey + event.altKey + event.shiftKey +
            event.metaKey;

        var key = event.keyCode;

        if (numModifiersPressed === 1 && event.ctrlKey) {
            key = -key; // Treat as negative key value when ctrl pressed
        }
        else if (numModifiersPressed) {
            return;
        }

        switch (key) {
            case DOWN:
                moveByRow(1);
                break;
            case UP:
                moveByRow(-1);
                break;
            case LEFT:
                if (isEditableFocused()) {
                    return;  // Leave key for editable area
                }
				if (isRowFocused()) {
					isCollapsible(getRowWithFocus()) && changeExpanded(false) || moveByRow(-1, true);
                }
                else {
                    moveByCol(-1);
                }
                break;
            case RIGHT:
                if (isEditableFocused()) {
                    return;  // Leave key for editable area
                }

                // If row: try to expand
                // If col or can't expand, move column to right
                if (!isRowFocused() || !changeExpanded(true)) {
                    moveByCol(1);
                }
                break;
            case CTRL_HOME:
                moveToExtremeRow(-1);
                break;
            case HOME:
                if (isEditableFocused()) {
                    return;  // Leave key for editable area
                }
                moveToExtreme(-1);
                break;
            case CTRL_END:
                moveToExtremeRow(1);
                break;
            case END:
                if (isEditableFocused()) {
                    return;  // Leave key for editable area
                }
                moveToExtreme(1);
                break;
            case ENTER:
                doPrimaryAction();
                break;
			case SPACE:
				doPrimaryAction();
                break;
            default:
                return;
        }

        // Important: don't use key for anything else, such as scrolling
        if (!$(event.target).is('a, button')) {
            event.preventDefault();
        }
    }

    function onRowClick(event) {
        var row = getContainingRow(event.target);
        
        if (row) {
            if (isExpandable(row)) {
                changeExpanded(!isExpanded(row), row);
            }

            // Click support of links nested in billing-treegrid
            if (!$(event.target).is('a, button')) {
               event.preventDefault();              
            }
        }
    }

    initAttributes();
    treegridElem.addEventListener('keydown', onKeyDown);
    treegridElem.addEventListener('click', onRowClick);
    // Polyfill for focusin necessary for Firefox < 52
    window.addEventListener(window.onfocusin ? 'focusin' : 'focus',
        onFocusIn, true);
}

document.addEventListener('DOMContentLoaded', function () {
    if ($('#billing-treegrid').length) {
        var doAllowRowFocus = true;
        var doStartRowFocus = true;
        TreeGrid(document.getElementById('billing-treegrid'), doAllowRowFocus, doStartRowFocus);
    }
});


// Switch between dynamic title of accordion
$(document).on('show.bs.collapse hide.bs.collapse', '[data-title-type="dynamic"]', function (e) {
    if ($(this).is(e.target)) {
        var title = $(this).closest('.accordion-wrap').find('.accordion-title');
        var expandTitle = title.data('expand-title');
        var collapseTitle = title.data('collapse-title');
        if (e.type === 'hide') {
            title.html(expandTitle);
        }
        else if (e.type === 'show') {
            title.html(collapseTitle);
        }
    }
});

function hideDisclosureElements($expandedDisclosureElements) {
	if ($expandedDisclosureElements.length > 0 && $expandedDisclosureElements.collapse) {
        $expandedDisclosureElements.collapse('hide');

        if ($expandedDisclosureElements.prev('.collapse-trigger').length > 0) {
            $expandedDisclosureElements.prev('.collapse-trigger').focus();
        }
	}
}

$(document).on('click', function (e) {
	var $target = $(e.target),
		$gridHeaderWrap,
		$gridHeaderWrapDisclosureTriggers,
		$gridDownloadWrap,
		$gridDownloadWrapDisclosureTriggers;

	$gridHeaderWrap = $('#billing-treegrid-header-print-download-wrap');
	$gridHeaderWrapDisclosureTriggers = $gridHeaderWrap.find('[data-toggle="collapse"]');

	if ($target.closest($gridHeaderWrapDisclosureTriggers).length === 0) {
		hideDisclosureElements($gridHeaderWrap.find('.collapse.show'));
	}

	$gridDownloadWrap = $('#billing-download-wrap');
	$gridDownloadWrapDisclosureTriggers = $gridDownloadWrap.find('[data-toggle="collapse"]');

	if ($target.closest($gridDownloadWrapDisclosureTriggers).length === 0) {
		hideDisclosureElements($gridDownloadWrap.find('.collapse.show'));
	}
});

$(document).on('focusout', '#billing-treegrid-header-print-download-wrap, #billing-download-wrap td.billing-download-wrap', function (e) {
	var $relatedTarget = $(e.relatedTarget),
		isFocusStillInGroup,
		$target = $(e.target),
		$gridHeaderWrap,
		$gridDownloadWrap;

	if (($gridHeaderWrap = $target.closest('#billing-treegrid-header-print-download-wrap')).length > 0) {
		$relatedTarget = $(e.relatedTarget);
		isFocusStillInGroup = $relatedTarget.closest($gridHeaderWrap).length > 0;

		if (isFocusStillInGroup) {
			return;
		}

		hideDisclosureElements($gridHeaderWrap.find('.collapse.show'));
	} else if (($gridDownloadWrap = $target.closest('#billing-download-wrap td')).length > 0) {
		$relatedTarget = $(e.relatedTarget);
		isFocusStillInGroup = $relatedTarget.closest('td')[0] === $gridDownloadWrap[0];

		if (isFocusStillInGroup) {
			return;
		}

		hideDisclosureElements($gridDownloadWrap.find('.collapse.show'));
	}
});

$(document).on('keydown', function (e) {
	var key = e.which || e.keyCode || 0;

	if (27 === key) {
		hideDisclosureElements($('#billing-treegrid-header-print-download-wrap .collapse.show, #billing-download-wrap .collapse.show'));
	}
});

// START document-ready
(function ($) {
	// toggle visibility of the sr-only and injectable elements used by the grid for accessibility
    setTimeout(function () {
        $('.billing-treegrid').on('mouseenter focusin', function () {
            $('#sr-only-expand-text, #sr-only-not-collapsible-text').removeClass('d-none');
        }).on('mouseleave focusout', function () {
            var $this = $(this);

            if (this.contains(document.activeElement) || $this.has(':hover').length > 0) {
                return;
            }

            $('#sr-only-expand-text, #sr-only-not-collapsible-text').addClass('d-none');
        });

        //Append "Expand your details" to row on hover/focus
		$('.billing-treegrid-row-services, .billing-treegrid-row-user').on('mouseenter', function () {
            var $this = $(this);

            if ($this.is('[aria-expanded="true"], :focus')) return;

			injectExpandForDetails($this);
		}).on('mouseleave', function () {
			var $this = $(this);

			if ($this.is(':focus')) return;

			removeExpandForDetails($this);
		}).on('focus', function () {
			var $this = $(this);

			if ($this.is('[aria-expanded="true"], :hover') || $this.has(':hover').length > 0) return;

			injectExpandForDetails($this);
		}).on('blur', function () {
			var $this = $(this);

			if ($this.is(':hover') || $this.has(':hover').length > 0) return;

			removeExpandForDetails($this);
		});

        //not collapsible row
		$('.not-collapsible-row').attr('aria-describedby', 'sr-only-not-collapsible-text');
    }, 200);

    //set event handler for tooltip enter keypress
    tooltipKeypress();

    // users should be able to activate "buttons" using the space key. this is for anchor tags so enter key is already supported
    $('a[role=button]:not(.click-on-space)').on('keypress', function (e) {
        if (KEYS.space === (e.which || e.keyCode || 0)) {
            e.preventDefault();
            e.stopPropagation();
            $(this).trigger("click");
        }
    });

    $(document).on('keydown', '.js-click-on-enter-or-space-delegate', function (e) {
        var $this,
            key = e.which || e.keyCode || 0;
        if (KEY.SPACE === key || KEYS.ENTER === key) {
            e.preventDefault();
            e.stopPropagation();

            $this = $(this);

            if ($this.is('a')) {
                this.click();
            } else {
                $this.click();
            }
        }
    });

    // Stop propagation for collapsing Accordion when tooltip trigger is selected
    $(document).on('keydown', '.collapse-trigger .tooltip-interactive, .collapse-trigger .tooltip-static', function (e) {
        if (e.keyCode === 32 || e.keyCode === 13) {
            e.preventDefault();
            e.stopPropagation();
            $(this).trigger("click");
        }
    }).on('click', '.collapse-trigger .tooltip-interactive, .collapse-trigger .tooltip-static', function (e) {
        e.stopPropagation();
    });

    //Toggle Message function
    $('#message-button-1').click(function (e) {
        $('.message').toggleClass("overflow-information");
        var messagebutton = $(this);
        var collapsetitle = messagebutton.find('.message-title').data('collapse-title');
        var expandtitle = messagebutton.find('.message-title').data('expand-title');
        var expandIcon = messagebutton.data('icon-expand');
        var collapseIcon = messagebutton.data('icon-collapse'); 

        if (expandIcon && collapseIcon) {
            if (messagebutton.attr('aria-expanded') !== "true") {
                messagebutton.find('.message-title').html(collapsetitle);
                messagebutton.find('.icon').addClass(collapseIcon).removeClass(expandIcon);
                messagebutton.attr('aria-expanded', 'true');
                
            }
            else {
                messagebutton.find('.message-title').html(expandtitle);
                messagebutton.find('.icon').removeClass(collapseIcon).addClass(expandIcon);
                messagebutton.attr('aria-expanded', 'false');
                
            }
        }
    });

    $('.js-aria-combobox-select-wrapper:not(.js-aria-combobox-select-wrapper-initialized)').addClass('js-aria-combobox-select-wrapper-initialized').each(function () {
			var $wrapper = $(this),
				$trigger = $wrapper.find('.aria-combobox-select-trigger'),
				$list = $('#' + $trigger.attr('aria-controls')),
				fnHideListbox = function (doFocusTrigger) {
					$trigger.attr('aria-expanded', 'false');
					$list.addClass('hide d-none');

					if (doFocusTrigger) {
						$trigger.focus();
					}
				}, fnShowListbox = function () {
					$trigger.attr('aria-expanded', 'true');
					$list.removeClass('hide d-none');
					$list.find('li.custom-highlight').removeClass('custom-highlight');
					$list.find('#' + $list.attr('aria-activedescendant')).addClass('custom-highlight');
					$list.attr('tabindex', '-1').focus();
				}, fnSetValueFromOption = function ($selOption) {
					var oldValue = $wrapper.find('.aria-combobox-select-trigger-value').text(),
						newValue = $selOption.text();

					if (oldValue !== newValue) {
						$wrapper.find('.aria-combobox-select-trigger-value').text(newValue);

						// fire custom change event on the wrapper in case something wants to listen to it
						$wrapper.trigger('change', {
							oldValue: oldValue,
							newValue: newValue,
							selectedOption: $selOption
						});
					}
				};

			$list.find('li').each(function () {
				var $option = $(this),
					id = $option.attr('id'),
					isSelected;

				if (!id) {
					id = uuidv4();
					$option.attr('id', id);
				}

				isSelected = $option.hasClass('custom-selected');
				if (isSelected) {
					$list.data('cached-selected', id);
					$list.find('li').not($option).removeClass('custom-selected');
					fnSetValueFromOption($option);
				}

				$option.on('click', function () {
					var selectedId = $option.attr('id');

					$list.find('li.custom-selected').removeClass('custom-selected');
					$option.addClass('custom-selected');
					$list.data('cached-selected', selectedId).attr('aria-activedescendant', selectedId);
					fnSetValueFromOption($option);
					fnHideListbox(true);
				});
			});

			$trigger.on('click', function () {
				var isExpanded = $trigger.attr('aria-expanded') === 'true',
					selectedId;

				if (isExpanded) {
					fnHideListbox();
				} else {
					selectedId = $list.data('cached-selected');
					$list.attr('aria-activedescendant', selectedId || $list.find('li').first().attr('id'));
					fnShowListbox();
				}
			});

			$trigger.on('keydown', function (e) {
				var key = e.which || e.keyCode || 0,
					isExpanded = $trigger.attr('aria-expanded') === 'true',
					selectedId,
					$option,
					KEYS = {
						'DOWN': 40,
						'UP': 38
                    };	
                
				if (!isExpanded) {
					if (KEYS.DOWN === key) {
						e.preventDefault();
						e.stopPropagation();
						selectedId = $list.data('cached-selected');
						if (selectedId) {
							$option = $list.find('#' + selectedId).next();
							if ($option.length === 0) {
								$option = $list.find('#' + selectedId);
							}
						} else {
							$option = $list.find('li').first();
						}
						$list.attr('aria-activedescendant', $option.attr('id'));
						fnShowListbox();
					} else if (KEYS.UP === key) {
						e.preventDefault();
						e.stopPropagation();
						selectedId = $list.data('cached-selected');
						if (selectedId) {
							$option = $list.find('#' + selectedId).prev();
							if ($option.length === 0) {
								$option = $list.find('#' + selectedId);
							}
						} else {
							$option = $list.find('li').first();
						}
						$list.attr('aria-activedescendant', $option.attr('id'));
						fnShowListbox();
					}
				}		
			});

			$list.on('keydown', function (e) {
				var key = e.which || e.keyCode || 0,
					isExpanded = $trigger.attr('aria-expanded') === 'true',
					selectedId,
					$option,
					KEYS = {
						'ESCAPE': 27,
						'ENTER': 13,
						'SPACE': 32,
						'DOWN': 40,
						'UP': 38,
						'END': 35,
						'HOME': 36,
						'TAB': 9
					};

                if (isExpanded) {
                    if (KEYS.ESCAPE === key) {
                        e.preventDefault();
                        e.stopPropagation();
                        fnHideListbox(true);
                        $trigger.focus();
                    } else if (KEYS.ENTER === key || KEYS.SPACE === key) {
                        e.preventDefault();
                        e.stopPropagation();
                        $option = $list.find('li.custom-highlight');
                        selectedId = $option.attr('id');
                        $list.data('cached-selected', selectedId).attr('aria-activedescendant', selectedId);
                        fnSetValueFromOption($option);
                        fnHideListbox(true);
                    } else if (KEYS.DOWN === key) {
                        e.preventDefault();
                        e.stopPropagation();
                        $option = $list.find('li.custom-highlight').removeClass('custom-highlight').next();
                        if ($option.length === 0) {
                            $option = $list.find('li').last();
                        }
                        $list.attr('aria-activedescendant', $option.addClass('custom-highlight').attr('id'));
                    } else if (KEYS.UP === key) {
                        e.preventDefault();
                        e.stopPropagation();
                        $option = $list.find('li.custom-highlight').removeClass('custom-highlight').prev();
                        if ($option.length === 0) {
                            $option = $list.find('li').first();
                        }
                        $list.attr('aria-activedescendant', $option.addClass('custom-highlight').attr('id'));
                    } else if (KEYS.END === key) {
                        e.preventDefault();
                        e.stopPropagation();
                        $option = $list.find('li.custom-highlight').removeClass('custom-highlight').siblings().last();
                        $list.attr('aria-activedescendant', $option.addClass('custom-highlight').attr('id'));
                    } else if (KEYS.HOME === key) {
                        e.preventDefault();
                        e.stopPropagation();
                        $option = $list.find('li.custom-highlight').removeClass('custom-highlight').siblings().last();
                        $list.attr('aria-activedescendant', $option.addClass('custom-highlight').attr('id'));
                    } else if (KEYS.TAB === key) {
                        fnHideListbox();
                    }
                }
            });

			$(document).on('click', function (e) {
				var $target = $(e.target);

				if ($target.closest($trigger).length === 0 && $target.closest($list).length === 0) {
					fnHideListbox();
				}
        });
    });

    function uuidv4() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }


	$(window).scroll(updateBillingGridStickyNavDeferred);

    $(window).add('.modal').scroll(function () {

        var $mobileExpandTriggers = $('.bell-services-modal-user-btn:visible, .bell-service-compare-modal-user-btn:visible');
        var stickyNav = $('#billing-grid-sticky-nav');

        if ($mobileExpandTriggers.length > 0) {
            var $rowServicesMob = $mobileExpandTriggers.find('> div > div:first-child');
            var stickyNavUserServiceMob = $('.billing-grid-sticky-nav-service-mob')[0];
            $rowServicesMob.each(function () {
                if (this.getBoundingClientRect().top < 50) {
                    stickyNavUserServiceMob.innerHTML = this.outerHTML;
                    stickyNav.removeClass("d-none");
                }
            });
        }  

        $('.bell-services-modal .modal-header').each(function () {
            if (isInViewport(this)) {
                stickyNav.addClass("d-none");
            } 
        });

    });
    // END - Billing Grid Header Sticky Nav - Mobile

    function fallbackCopyTextToClipboard(text, $container) {
        var textArea = document.createElement("textarea"),
            containerEl = $container != null ? $container[0] : document.body;

        textArea.value = text;

        // Avoid scrolling to bottom
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";

        containerEl.appendChild(textArea);

        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
        } catch (err) {
            console.error('Unable to copy', err);
        } finally {
            containerEl.removeChild(textArea);
        }
    }

    function copyTextToClipboard(text, $container) {
        if (!navigator.clipboard) {
            fallbackCopyTextToClipboard(text, $container);
            return;
        }

        navigator.clipboard.writeText(text);
    }

    $(".js-copy-customerid").on('click', function (e) {
        copyTextToClipboard($.trim($("#customer-id").text()), $('#pay-your-bill-dialog'));
        $('.copy-customer-id').append('<div aria-live="assertive" class="sr-only js-announce-copy-customer-id"></div>');

        setTimeout(function () {
            $('.js-announce-copy-customer-id').text('Copied ' + $("#customer-id").text() + ' to Clipboard');
        }, 10);

        setTimeout(function () {
            $('.js-announce-copy-customer-id').remove();
        }, 3000);
    });

    $('div.bell-services-mobility-modal .pbe-tag').closest('div.pad-b-10', 'ul').addClass('pbe-spacer').closest('ul', '.collapse-accordion-accessible-toggle').addClass('pad-b-5');

    initTileCarousel();

    var isMobileView = !window.matchMedia('(min-width: 768px)').matches;
	var toggleServicesModalDisplay,
		fnTimeoutResizePBE;

    $(window).resize(function () {
        clearTimeout(toggleServicesModalDisplay);
        toggleServicesModalDisplay = setTimeout(function () {
            if (window.matchMedia('(min-width: 768px)').matches) {
                if (isMobileView) {
                    isMobileView = false;
                    $('.bell-services-modal').modal('hide');

                    if ($('#billing-grid-sticky-nav:visible').length > 0) {
                        updateBillingGridStickyNavDeferred();
                    }
                }
            } else {
                isMobileView = true;
            }
		}, 200);

		clearTimeout(fnTimeoutResizePBE);
		fnTimeoutResizePBE = setTimeout(function () {
			fixPBELayout(false);
		}, 200);
    });

    $(document).on('shown.bs.modal', '.nested-dialog:has(.same-height-wrap)', function (e) {
        $(this).find('.same-height').trigger('resize');
    });
 
	// START PBE
	fixPBELayout(true);
	// END PBE
    
    //fix scrollable background of modal
    $(document).on('shown.bs.modal', '.modal', function (e) {
        $('html').addClass('overflow-hidden');
        if ($('body').css('padding-right') > '17px') {
            $('body').css('padding-right', '0px');
            $('body').addClass('modalScrollablePad');
            $('#container').addClass('modalScrollablePad');
        }
    }).on('hide.bs.modal', '.modal', function (e) {
        $('html').removeClass('overflow-hidden');
        $('body').removeClass('modalScrollablePad');
        $('#container').removeClass('modalScrollablePad');
    }).on('hidden.bs.modal', '.modal', function () {
        setTimeout(function () {
            $('body').css('padding-right', '');
        }, 400);
    });

    $(document).on('hidden.bs.modal', '.js-multiple-bills-download', function (e) {
        $('.multiple-bills-download-btn').focus();
    });

	// no need to call on document ready. just call after your PBE tooltip widget loads
    initPBETooltip();

    var $summaryTimelime = $('.js-summary-changes-timeline-container'),
        isInitialState = true;

    if ($summaryTimelime.length > 0) {
        initsummaryTimeline();
    } 

    $(window).resize(function () {
        if ($summaryTimelime.length > 0) {
            if (window.matchMedia('(min-width: 768px)').matches) {
                isInitialState = true;
            }

            if (isInitialState) {
                initsummaryTimeline();
            }  
        } 
    });

    $(document).on('click', '.js-summary-changes-timeline-load-more-btn', function () {
        var $summaryTimelimeList = $('.summary-changes-timeline-item'),
            numToShow = $('.summary-changes-timeline-list').data('num-to-show'),
            summaryTimelimeListLength = $summaryTimelimeList.length,
            $summaryTimelimeLoadMoreContainer = $('.summary-changes-timeline-load-more-container'),
            $summaryTimelimeListVisible = $summaryTimelimeList.filter(':visible'),
            nowShowing,
            showing = $summaryTimelimeListVisible.length;

        $summaryTimelimeList.slice(showing - 1, showing + numToShow).show();
        nowShowing = $summaryTimelimeList.filter(':visible').length;

        if (nowShowing >= summaryTimelimeListLength) {
            $summaryTimelimeLoadMoreContainer.hide();
        } 

        addClassToVisibleTimelineListitem();

        isInitialState = false;
    });

    var previousSelectedSortByTimeline;

    $(document).on('focus', '.js-sort-by-timeline', function () {
        previousSelectedSortByTimeline = this.value;
    }).on('change', '.js-sort-by-timeline', function () {
        if (previousSelectedSortByTimeline != this.value) {
            sortTimelineList($("ol.summary-changes-timeline-list"));
        }

        previousSelectedSortByTimeline = this.value;
        $('.js-sort-by-timeline').val(this.value);
    });

    $(document).on('click', '.payment-list-due-btn', function (e) {
        var $this = $(this),
            expandedState = $this.attr('aria-expanded'),
            $container = $('#' + $this.attr('aria-controls')),
            $listItem = $container.find('.payment-list-due-date-item'),
            hideTitle = $this.find('.pymntlist-title').data('collapse-title'),
            showTitle = $this.find('.pymntlist-title').data('expand-title'),
            showIcon = $this.data('icon-expand'),
            hideIcon = $this.data('icon-collapse');

        if (expandedState === 'true') {
            $this.find('.pymntlist-title').html(showTitle);
            $this.find('.icon').removeClass(hideIcon).addClass(showIcon);
            setTimeout(function () {
                $this.attr('aria-expanded', 'false');
            }, 100);
            hidePaymentListDisplay($container);

        } else {
            $this.find('.pymntlist-title').html(hideTitle);
            $this.find('.icon').addClass(hideIcon).removeClass(showIcon);
            setTimeout(function () {
                $this.attr('aria-expanded', 'true');
            }, 100);
            $listItem.show();
        }
    });

    var pageCounter = 1;
    $(document).on('click', '.tour-next-btn', function () {
        var previousPage = 0,
            $contentHeader;

        if (pageCounter < 6) {
            pageCounter++;
            previousPage = pageCounter - 1;


            $(this).closest('.reactive-tour-content' + previousPage).addClass('d-none');

            if (pageCounter > 1) {
                $(this).closest('.tour-previous-content').parent().removeClass('d-none');
            }

            $(this).closest('.reactive-tour-container').find('.reactive-tour-content' + pageCounter).removeClass('d-none');

            $('.reactive-tour-content-pagination').text(pageCounter + '/6');

            $contentHeader = $(this).closest('.reactive-tour-header');

            $contentHeader.find('.reactive-tour-content-pagination').text(pageCounter + '/6');

            $contentHeader.find('.close').focus();
        }
    });

    $(document).on('click', '.tour-previous-btn', function () {
        var previousPage = 0;

        if (pageCounter <= 6) {
            pageCounter--;
            previousPage = pageCounter + 1;

            $(this).closest('.reactive-tour-content' + previousPage).addClass('d-none');

            if (pageCounter > 1) {
                $(this).parent().removeClass('d-none');
            } else if (pageCounter == 1) {
                $(this).parent().addClass('d-none');
            }

            $(this).closest('.reactive-tour-container').find('.reactive-tour-content' + pageCounter).removeClass('d-none');

            $('.reactive-tour-content-pagination').text(pageCounter + '/6');

            $contentHeader = $(this).closest('.reactive-tour-header');

            $contentHeader.find('.reactive-tour-content-pagination').text(pageCounter + '/6');

            $contentHeader.find('.close').focus();
        }
    });

    $(document).on('hidden.bs.modal', '#reactive-tour-dialog', function (e) {

        pageCounter = 1;

        var $content = $('.reactive-tour-container .reactive-tour-content', this),
            $tooltipTrigger;
           
        $content.addClass('d-none');
        $content.first().removeClass('d-none');

        //Return focus to tooltip
        $tooltipTrigger = $('.bill-tour-tooltip');

        $tooltipTrigger.focus();
        $tooltipTrigger.tooltip('hide');
    });

    $(document).on('hidden.bs.modal', '.reactive-tour-dialog', function (e) {
        pageCounter = 1;

        var $content = $('.reactive-tour-container .reactive-tour-content', this),
            $tooltipTrigger,
            maxTour = $('.reactive-tour-container .reactive-tour-content').last().data('tour-content');

        $content.removeClass('active');

        $pagination = $(this).find('.reactive-tour-dialog-navigation-content');
        $pagination.find('.tour-pagination-text-mobile').text(1 + '/' + maxTour);
        $pagination.find('.tour-pagination-wrapper .sr-only').html('Content 1 of ' + maxTour);
        $pagination.find('.tour-prev-btn-mobile').addClass('disabled').attr({ "disabled": "disabled", "aria-disabled": "true", "tabindex":"-1" });
        $pagination.find('.tour-next-btn-mobile').removeClass('disabled').removeAttr('disabled').removeAttr('aria-disabled').removeAttr('tabindex');

        //Return focus to tooltip
        $tooltipTrigger = $('.bill-tour-tooltip');

        $tooltipTrigger.focus();
        $tooltipTrigger.tooltip('hide');
    });

    $(document).on('shown.bs.modal', '.reactive-tour-dialog', function (e) {
        pageCounter = 1;

        var $content = $('.reactive-tour-container .reactive-tour-content', this),
            $tooltipTrigger;

        $content.first().addClass('active');

        //Return focus to tooltip
        $tooltipTrigger = $('.bill-tour-tooltip');

        $tooltipTrigger.focus();
        $tooltipTrigger.tooltip('hide');
    });

    $(document).on('click', '.tour-next-btn, .tour-next-btn-mobile', function () {
        var $reactiveTourHeader = $(this).closest('.reactive-tour-header'),
            $currentTour = $reactiveTourHeader.find('.reactive-tour-container .reactive-tour-content.active'),
            maxTour = $reactiveTourHeader.find('.reactive-tour-container .reactive-tour-content').last().data('tour-content'),
            currentPage = $currentTour.data('tour-content'),
            $nextPage = $reactiveTourHeader.find('.reactive-tour-container .reactive-tour-content[data-tour-content="' + (currentPage + 1) + '"]');
        
        if (currentPage == (maxTour - 1)) {
            $('.reactive-tour-dialog-navigation .tour-next-btn-mobile').addClass('disabled').attr({"disabled":"disabled", "aria-disabled":"true", "tabindex":"-1" });
        }

        if (currentPage < maxTour) {
            $currentTour.removeClass('active');
            $('.tour-pagination-text-mobile').html((currentPage + 1) + '/' + maxTour);
            $('.tour-prev-btn, .tour-prev-btn-mobile').removeClass('disabled').removeAttr('disabled').removeAttr('aria-disabled').removeAttr('tabindex');;
            $nextPage.addClass('active');
            $('.tour-content-modal-title').html($nextPage.find('.tour-content-title').html());

            $contentHeader = $(this).closest('.reactive-tour-header');
            $contentHeader.find('.tour-pagination-text-mobile').text((currentPage + 1) + '/' + maxTour);
            $contentHeader.find('.tour-pagination-wrapper .sr-only').html('Content ' + (currentPage + 1) + ' of ' + maxTour)
            $contentHeader.find('.close').focus();
        }
    });

    $(document).on('click', '.tour-prev-btn, .tour-prev-btn-mobile', function () {
        var $reactiveTourHeader = $(this).closest('.reactive-tour-header'),
            $currentTour = $reactiveTourHeader.find('.reactive-tour-container .reactive-tour-content.active'),
            maxTour = $reactiveTourHeader.find('.reactive-tour-container .reactive-tour-content').last().data('tour-content'),
            currentPage = $currentTour.data('tour-content'),
            $previousPage = $reactiveTourHeader.find('.reactive-tour-container .reactive-tour-content[data-tour-content="' + (currentPage - 1) + '"]');
        
        if (currentPage == 2) {
            $('.reactive-tour-dialog-navigation .tour-prev-btn-mobile').addClass('disabled').attr({ "disabled": "disabled", "aria-disabled": "true", "tabindex":"-1" });
        }

        if (currentPage > 1) {
            $currentTour.removeClass('active');
            $('.tour-pagination-text-mobile').html((currentPage - 1) + '/' + maxTour);
            $('.tour-next-btn, .tour-next-btn-mobile').removeClass('disabled').removeAttr('disabled').removeAttr('aria-disabled').removeAttr('tabindex');;
            $previousPage.addClass('active');
            $('.tour-content-modal-title').html($previousPage.find('.tour-content-title').html());

            $contentHeader = $(this).closest('.reactive-tour-header');
            $contentHeader.find('.tour-pagination-text-mobile').text((currentPage - 1) + '/' + maxTour);
            $contentHeader.find('.tour-pagination-wrapper .sr-only').html('Content ' + (currentPage - 1) + ' of ' + maxTour)
            $contentHeader.find('.close').focus();
        }
    });

    setPaymentListVisibility();
    initSliderCarousel();
    initProactiveTourDialog();
    processImportantMessage();

    insertListTitleBeforeList();


    $(document).on('click', '.bell-services-modal-user-btn.collapse-trigger[aria-expanded=false]', function () {
        setTimeout(removeEmptyServiceFooterContainer, 200);
    });

    removeEmptyTipsTextTagMarginBottom();

    var $body = $('body');

    $body.on('shown.bs.modal', '.ds-pbe-modal', function () {
        if ($body.data('js-same-width-delegate-initialized') !== true) {
            processSameWidth('.js-same-width-delegate-item');
        }

        fixPBELayout(false);
        processSameHeightElements();
    });

    var fnTimeoutPBEModalResize;

    $(window).resize(function () {
        clearTimeout(fnTimeoutPBEModalResize);
        fnTimeoutPBEModalResize = setTimeout(function () {
            processSameWidth('.js-same-width-delegate-item');
        }, 200);
    });

})(jQuery);

function removeEmptyTipsTextTagMarginBottom() {
    var $emptyTipsTextTagContainer = $('.tips-text-tag-container:empty');

    if ( $emptyTipsTextTagContainer.length === 0) {
        return;
    }

    $emptyTipsTextTagContainer.parent('div').css('margin-bottom', '0');
}

function insertListTitleBeforeList() {
    var $lists = $('.js-value-footer-services-list ul');

    if ($lists.length === 0) {
        return;
    }

    $lists.each(function () {
        var $list = $(this),
            $listTitle = $list.find('.list-title');

        if ($listTitle.length) {
            var $listItem = $listTitle.closest('li');

            $listItem.remove();
            $listTitle.insertBefore($list);
        }
    });
}

function processImportantMessage() {
    var $messages = $('.message.js-important-message:not(.js-important-message-initialized)').addClass('js-important-message-initialized'),
        $lastIndexer = $messages.last().find('.message-count'),
		$messageLines = $messages.find('p, > span > span:first-child, > div > span:first-child'),
        $boldElements = $messages.find('b'),
        $messagesMobile = $('.message-mob.js-important-message:not(.js-important-message-initialized)').addClass('js-important-message-initialized'),
        $lastIndexerMobile = $messagesMobile.last().find('.message-count'),
		$messageLinesMobile = $messagesMobile.find('p, > span > span:first-child, > div > span:first-child'),
        $boldElementsMobile = $messagesMobile.find('b');

    $lastIndexer.clone().addClass('invisible').prependTo($messageLines);
    $boldElements.wrap('<strong></strong>').contents().unwrap();

    $lastIndexerMobile.clone().addClass('invisible').prependTo($messageLinesMobile);
    $boldElementsMobile.wrap('<strong></strong>').contents().unwrap();
}

function fixSliderAccessibilityAttributes($sliderCarouselContainer) {
    var $sliderCarousel = $sliderCarouselContainer.find('.slider-rotating-carousel'),
        $carouselNavigation = $sliderCarousel.find('.slider-rotating-carousel-buttons'),
        $carouselBtn = $carouselNavigation.find('.slider-rotating-carousel-button');

    setTimeout(function () {
        $carouselBtn.each(function () {
            var $this = $(this);

            if ($this.closest('li').hasClass('slick-active')) {
                $this.attr('aria-selected', 'true').attr('tabindex', '0');
            }
            else {
                $this.attr('aria-selected', 'false').attr('tabindex', '-1');
            }
        });
    }, 0);
}

function initSliderAccessibilityAttributes($sliderCarouselContainer) {
    var $sliderCarousel = $sliderCarouselContainer.find('.slider-rotating-carousel'),
        $slideTabpanel = $sliderCarousel.find('.slick-slide:not(.slick-cloned)'),
        $carouselNavigation = $sliderCarousel.find('.slider-rotating-carousel-buttons'),
        $carouselBtn = $carouselNavigation.find('.slider-rotating-carousel-button'),
        carouselNavLabel = $sliderCarouselContainer.data('carousel-nav-label'),
        slideRoleDescription = $sliderCarouselContainer.data('slide-role-description');

    $carouselNavigation.attr('aria-label', carouselNavLabel);

    setTimeout(function () {
        $carouselNavigation.attr('role', 'tablist');

        $carouselBtn.each(function () {
            var $this = $(this);

            $this.closest('li').attr('role', 'presentation');
        });

        $slideTabpanel.each(function (i) {
            var $this = $(this);

            $this.attr('role', 'tabpanel').attr('id', 'slick-slide-' + i)
                .attr('aria-roledescription', slideRoleDescription)
                .attr('aria-labelledby', 'slick-slide-control-' + i);
        });
    }, 0);
}

function updatePauseBtnLeftStyle() {
	var $carouselButtonsWidth = $('.proactive-tour-carousel-component .slider-rotating-carousel-buttons').width() + 4,
		carouselPauseBtn = $('.slider-rotating-carousel-pause');

	if (window.matchMedia('(min-width: 768px)').matches) {
		carouselPauseBtn.css('left', 'calc((50% + ' + $carouselButtonsWidth + 'px) - 50px)').css('right', '0').css('transform', 'none');
	} else {
		carouselPauseBtn.css('left', 'auto').css('right', '0').css('transform', 'none');
	}
}

var AUTOPLAY_INTERVAL_IN_MS = 50;

function updateProgressIndicator($sliderCarouselContainer, $sliderCarousel) {
	var autoPlaySpeed = $sliderCarousel.data('autoplay-speed'),
		dataAutoplayIntervalValues = $sliderCarouselContainer.data('autoplay-interval-values'),
		percentComplete = dataAutoplayIntervalValues.percentComplete,
		progressIndicatorUnit = dataAutoplayIntervalValues.progressIndicatorUnit,
		progressIndicatorTotal,
		progressIndicatorTotalRounded;

	percentComplete += AUTOPLAY_INTERVAL_IN_MS / autoPlaySpeed * 100;
	dataAutoplayIntervalValues.percentComplete = percentComplete;
	$sliderCarouselContainer.data('autoplay-interval-values', dataAutoplayIntervalValues);

	progressIndicatorTotal = percentComplete * progressIndicatorUnit * -1 + 1;
	progressIndicatorTotalRounded = Math.round(progressIndicatorTotal * 10) / 10;

	$sliderCarouselContainer.find('.slider-rotating-carousel-pause circle').css('stroke-dashoffset', progressIndicatorTotalRounded);

	return percentComplete;
}

function progressIntervalHandler($sliderCarouselContainer, $sliderCarousel) {
    var percentComplete = updateProgressIndicator($sliderCarouselContainer, $sliderCarousel),
        $sliderRotatingCarousel = $sliderCarouselContainer.find('.slider-rotating-carousel'),
        slidesCount = $sliderRotatingCarousel.find('.slick-slide:not(.slick-cloned)').length,
        $currentSlide = $sliderRotatingCarousel.find('.slick-slide.slick-current:not(.slick-cloned)');

    if (percentComplete >= 100) {
        if (($currentSlide.data('slick-index') + 1) === slidesCount) {
            $sliderRotatingCarousel.slick('slickGoTo', 0);
        }
        else {
            $sliderRotatingCarousel.slick('slickNext');
        }
    }
};

function startAutoPlay($sliderCarouselContainer) {
	var isAutoplaying = $sliderCarouselContainer.data('autoplaying'),
		$sliderCarousel = $sliderCarouselContainer.find('.slider-rotating-carousel');

	if (isAutoplaying === true) return;

	$sliderCarouselContainer.data('autoplaying', true);

	$sliderCarouselContainer.data('autoplay-interval', setInterval(function () {
		progressIntervalHandler($sliderCarouselContainer, $sliderCarousel);
	}, AUTOPLAY_INTERVAL_IN_MS));
}

function stopAutoPlay($sliderCarouselContainer) {
	var isAutoplaying = $sliderCarouselContainer.data('autoplaying');

	if (isAutoplaying !== true) return;

	$sliderCarouselContainer.data('autoplaying', false);

	clearInterval($sliderCarouselContainer.data('autoplay-interval'));
}

function resetAutoPlay($sliderCarouselContainer, doRepaint) {
	var isAutoplaying = $sliderCarouselContainer.data('autoplaying'),
		dataAutoplayIntervalValues = $sliderCarouselContainer.data('autoplay-interval-values');

	if (isAutoplaying === true) {
		stopAutoPlay($sliderCarouselContainer);
	}
	
	dataAutoplayIntervalValues.percentComplete = 0;
	dataAutoplayIntervalValues.progressIndicatorTotal = 0;

	$sliderCarouselContainer.data('autoplay-interval-values', dataAutoplayIntervalValues);

	if (doRepaint === true) {
		$sliderCarouselContainer.find('.slider-rotating-carousel-pause circle').css('stroke-dashoffset', '0px');
	}
}

function checkAutoPlay($sliderCarouselContainer) {
	var forceAutoPlay = $sliderCarouselContainer.data('force-autoplay'),
		$focusedEl;

	if (forceAutoPlay === true) {
		startAutoPlay($sliderCarouselContainer);
	} else if (forceAutoPlay === false) {
		stopAutoPlay($sliderCarouselContainer);
	} else {
        $focusedEl = $(document.activeElement);

		if (($focusedEl.is('.slider-rotating-carousel-pause') || $focusedEl.closest('.slider-rotating-carousel').length === 0)
			&& (!$sliderCarouselContainer.hasClass('mouse-within') || $sliderCarouselContainer.find('.slider-rotating-carousel-pause').hasClass('mouse-within'))) {			
			startAutoPlay($sliderCarouselContainer);
		} else {
			stopAutoPlay($sliderCarouselContainer);
		}
	}
}

function sortTimelineList(list) {
    var listItems = list.children('li'),
        i = 1,
        newListItems;
    list.append(listItems.get().reverse());

    newListItems = list.children('li');
    newListItems.each(function () {
        $(this).attr('aria-posinset', i++);
    });

    initsummaryTimeline();
    addClassToVisibleTimelineListitem();
}

function addClassToVisibleTimelineListitem() {
    var $summaryTimelimeList = $('.summary-changes-timeline-item'),
        $summaryTimelimeListVisible = $summaryTimelimeList.filter(':visible');

    $summaryTimelimeList.each(function () {
        $(this).removeClass('item-visible').removeClass('item-partially-visible').removeAttr('tabindex').removeAttr('aria-hidden');
        $(this).find('a:visible').removeAttr('tabindex');
    });

    $summaryTimelimeListVisible.each(function (e) {
        if ($(this).is(':visible')) {
            $(this).addClass("item-visible");

            if ($summaryTimelimeListVisible.length != $summaryTimelimeList.length && window.matchMedia('(max-width: 767.98px)').matches) {
                if (e === ($summaryTimelimeListVisible.length - 1)) {
                    $(this).addClass('item-partially-visible').attr('tabindex', '-1').attr('aria-hidden', 'true');
                    $(this).find('a[href], button, input, textarea, select, details, [tabindex]').attr('tabindex', '-1');
                }
            }
        }
    });
}

function initsummaryTimeline() {
    var $summaryTimelimeList = $('.summary-changes-timeline-item'),
        numToShowOnLoadMobile = $('.summary-changes-timeline-list').data('visible-items-onload-mobile') + 1,
        summaryTimelimeListLength = $summaryTimelimeList.length,
        $summaryTimelimeLoadMoreContainer = $('.summary-changes-timeline-load-more-container'),
        $summaryTimelimeLoadMoreBtn = $('.summary-changes-timeline-load-more-btn');

    if (window.matchMedia('(max-width: 767.98px)').matches) {
        $summaryTimelimeList.hide();

        if ($summaryTimelimeLoadMoreBtn.length > 0) {
            if (summaryTimelimeListLength > numToShowOnLoadMobile) {
                $summaryTimelimeLoadMoreContainer.show();
            }
            else {
                $summaryTimelimeLoadMoreContainer.hide();
            }
        }

        /*+ 1 for partially view item*/
        $summaryTimelimeList.slice(0, numToShowOnLoadMobile).show();
    }
    else {
        $summaryTimelimeList.show();
    }

    addClassToVisibleTimelineListitem();
}

function initPBETooltip() {
	$(document).off('click.pbetooltip').on('click.pbetooltip', '.grid-pbe-tooltip-close', function (e) {
		e.stopPropagation();
		e.preventDefault();
		$(this).closest('.grid-pbe-tooltip').tooltip('hide');
	});

	$('.grid-pbe-tooltip:not(.grid-pbe-tooltip-initialized)')
		.addClass('grid-pbe-tooltip-initialized')
		.on('show.bs.tooltip', function (e) {
			var $tooltip = $(this);

			if ($tooltip.data('prevent-showing') === true) {
				$tooltip.data('prevent-showing', false);
				return false;
			}

            $tooltip.data('trigger-element', document.activeElement);
            $('tooltip.in, .tooltip.show').tooltip('hide');
		}).on('hide.bs.tooltip', function (e) {
			var $tooltip = $(this),
				lastFocusedElement = $tooltip.data('trigger-element');

			if ($(document.activeElement).closest('.grid-pbe-tooltip-content').length > 0) {
				$tooltip.data('prevent-showing', true);
				setTimeout(function () {
					if (lastFocusedElement) {
						$tooltip.data('prevent-showing', true);
						lastFocusedElement.focus();
					}
				}, 400);
			}
		}).each(function () {
			var $this = $(this);

			$this.tooltip({
				container: $this
			});
		});
}

function fixPBELayout(skipClean) {
    var $pbeWrapper = $('.pbe-wrapper');

	if (skipClean !== true) {
		cleanPBELayout($pbeWrapper);
	}

	window.maxTopPBE = 0;
	window.strCalcMaxTopPBE = '';

	$pbeWrapper.each(function () {
		var $this = $(this),
			$splitWrappers = $this.find('.js-split-wrapper');

		window.maxTopPBE = 0;

		// process split bars per column
		$splitWrappers.each(processSplit);

		// align bar charts of two columns
		$splitWrappers.find('.bars-container').css('top', strCalcMaxTopPBE);

		// align labels of two columns
        alignPBEColumns($this);

        processLastPaymentLabels();

		// ready to show
		$splitWrappers.removeClass('invisible-force');
	});
}

function cleanPBELayout($wrappers) {
	var $pbeWrapper = $wrappers || $('.pbe-wrapper');

	$pbeWrapper.find('[style*=min-height]').css('min-height', '');
}

function processSplit() {
    var MIN_BAR_WIDTH = '13px',
		$this = $(this),
        percentage = $this.data('js-split-percentage'),
        percentage2 = $this.data('js-split-percentage-2'),
		percentageLeft,
		percentageRight,
		numDividerWidth = $this.data('split-divider-width') || 0,
		strDividerWidth = numDividerWidth + 'px',
		numDividerMargin = $this.data('split-divider-margin') || 0,
		strDividerMargin = numDividerMargin + 'px',
		$labelGroupTop,
        $labelGroupBottom,
        MIN_DIVIDER_LABEL_WIDTH = '100px',
        top,
        strCalcTop,
        MAX_LABEL_OFFSET = '25px',
        containerMinusDividerWidth,
        leftWidth,
        rightWidth,
        rightLabelWidth,
        leftBarWidth,
        leftLabelOffset,
        tempPercent,
        minPercentage,
        centerBarWidth,
        centerLabelOffset,
        isScrollable,
        dividerLabelClass,
        dividerLabelCssObj,
        centerLabelWidth,
        containerWidth,
        percentageCenter,
        centerWidth,
        rightBarWidth;
    if (percentage == null) {
        // if split percentage is not set, show the right bar completely and hide the left bar and divider
        $this.find('.split-bar-left').css('display', 'none');
        $this.find('.split-bar-right').css('width', '100%').css('border-radius', '4px').find('> div').css('border-radius', '4px');
        $this.find('.split-bar-divider').addClass('d-none-force');
        $this.find('.split-divider-indicator').addClass('d-none-force');
        $this.find('.split-divider-label').addClass('d-none-force');
        // left-align the right labels
        $this.find('.split-right-label').css({
            'width': 'calc(100% - ' + MAX_LABEL_OFFSET + ')',
            'left': MAX_LABEL_OFFSET,
            'right': 'auto',
            'text-align': 'left',
            'padding-left': '0',
            'padding-right': '25px'
        });
        $this.find('.label-indicator-line-top, .label-indicator-line-bottom').removeClass('align-self-end').addClass('align-self-start');
    } else if (percentage2 == null) {
        isScrollable = $this.closest('.pbe-inner-scrollable').length > 0;
        percentage = Math.min(Math.max(parseFloat(percentage), 0), 100);
        percentageLeft = percentage / 100;
        percentageRight = 1 - percentageLeft;

        containerMinusDividerWidth = '(100% - ' + strDividerWidth + ')';
        leftWidth = containerMinusDividerWidth + ' * ' + percentageLeft;
        rightWidth = containerMinusDividerWidth + ' * ' + percentageRight;
        rightLabelWidth = 'min(max(' + containerMinusDividerWidth + ' * ' + (1 - (percentageRight / 2)) + ' + ' + strDividerWidth + ', 100% - ' + MAX_LABEL_OFFSET + '), 100% - ' + MIN_BAR_WIDTH + ' / 2)';
        rightLabelWidthNew = 'min(max(' + containerMinusDividerWidth + ' * ' + (1 - (percentageRight / 2)) + ' + ' + strDividerWidth + ', 100% / 2)';
		leftBarWidth = 'min(max(' + leftWidth + ', ' + MIN_BAR_WIDTH + '), ' + containerMinusDividerWidth + ' - ' + MIN_BAR_WIDTH + ')';
        leftLabelOffset = 'min(max(' + containerMinusDividerWidth + ' * ' + (percentageLeft / 2) + ', ' + MIN_BAR_WIDTH + ' / 2), ' + MAX_LABEL_OFFSET + ')';

        // resize bars
        $this.find('.split-bar-left').css('width', leftBarWidth);
        $this.find('.split-bar-right').css('width', 'min(max(' + rightWidth + ', ' + MIN_BAR_WIDTH + '), ' + containerMinusDividerWidth + ' - ' + MIN_BAR_WIDTH + ')');
        // show and resize the divider
        $this.find('.split-bar-divider').removeClass('d-none-force').css('width', strDividerWidth);
        // reposition and resize the divider indicator
        $this.find('.split-divider-indicator').removeClass('d-none-force').css({
            'left': leftBarWidth,
            'width': strDividerWidth
        });
        $this.find('.split-divider-indicator > div').css('margin', '0 ' + strDividerMargin);
        // right align the right labels
        $this.find('.split-right-label').css({
            'width': 'calc(' + rightLabelWidth + ')',
            'text-align': isScrollable ? '' : 'right'
        });
        // right align the right labels
		$this.find('.split-right-label-DS').css({
            'width': 'calc(' + rightLabelWidthNew + ')',
            'text-align': isScrollable ? '' : 'right'
        });
        // left align the left labels
        $this.find('.split-left-label').css({
            'width': 'calc(100% - ' + leftLabelOffset + ')',
            'left': leftLabelOffset,
            'text-align': 'left'
        });
        $this.find('.split-right-label .label-indicator-line-top, .split-right-label .label-indicator-line-bottom').removeClass('align-self-start').addClass('align-self-end');
        $this.find('.split-left-label .label-indicator-line-top, .split-left-label .label-indicator-line-bottom').removeClass('align-self-end').addClass('align-self-start');
        // resize and align the divider label
        if (percentage >= 50) {
            dividerLabelClass = isScrollable ? 'right' : 'text-right pad-l-10';
            dividerLabelCssObj = { 'width': 'min(' + leftWidth + ' + ' + strDividerWidth + ' - ' + strDividerMargin + ', 100% - ' + MIN_BAR_WIDTH + ')' };
        } else if(percentage == 37 && $(".split-right-label-DS")[0]){
			dividerLabelClass = isScrollable ? 'right' : 'text-right pad-l-10';
            dividerLabelCssObj = { 'width': 'min(' + leftWidth + ' + ' + strDividerWidth + ' - ' + strDividerMargin + ', 100% - ' + MIN_BAR_WIDTH + ')' };
		} else {
            dividerLabelClass = isScrollable ? 'left' : 'text-left pad-r-10';
            dividerLabelCssObj = {
                'width': 'min(100% - (' + leftWidth + ' + ' + strDividerMargin + '), 100% - ' + MIN_BAR_WIDTH + ')',
                'left': 'min(max(' + leftWidth + ' + ' + strDividerMargin + ', ' + MIN_BAR_WIDTH + '), 100% - ' + MIN_DIVIDER_LABEL_WIDTH + ')'
            };
        }
        $this.find('.split-divider-label').addClass(dividerLabelClass).css(dividerLabelCssObj);
    } else {
        isScrollable = $this.closest('.pbe-inner-scrollable').length > 0;
        percentage = Math.min(Math.max(parseFloat(percentage), 0), 100);
        percentage2 = Math.min(Math.max(parseFloat(percentage2), 0), 100);
        tempPercent = Math.max(percentage, percentage2);
        percentage = Math.min(percentage, percentage2);
        percentage2 = tempPercent;
        containerWidth = $this.outerWidth();
        minPercentage = parseInt(MIN_BAR_WIDTH) / containerWidth;
        percentageLeft = Math.min(Math.max(percentage / 100, minPercentage), 1 - minPercentage - minPercentage);
        percentageCenter = Math.min(Math.max((percentage2 - percentage) / 100, minPercentage), 1 - percentageLeft - minPercentage);
        percentageRight = 1 - percentageLeft - percentageCenter;
        containerMinusDividerWidth = '(100% - ' + strDividerWidth + ' * 2)';
        leftWidth = containerMinusDividerWidth + ' * ' + percentageLeft;
        rightWidth = containerMinusDividerWidth + ' * ' + percentageRight;
        centerWidth = containerMinusDividerWidth + ' * ' + percentageCenter;
        rightBarWidth = 'min(max(' + rightWidth + ', ' + MIN_BAR_WIDTH + '), ' + containerMinusDividerWidth + ' - ' + MIN_BAR_WIDTH + ')';
        rightLabelWidth = 'min(max(' + containerMinusDividerWidth + ' * ' + (1 - (percentageRight / 2)) + ' + ' + strDividerWidth + ' * 2, 100% - ' + MAX_LABEL_OFFSET + '), 100% - ' + MIN_BAR_WIDTH + ' / 2)';
        leftBarWidth = 'min(max(' + leftWidth + ', ' + MIN_BAR_WIDTH + '), ' + containerMinusDividerWidth + ' - ' + MIN_BAR_WIDTH + ')';
        leftLabelOffset = 'min(max(' + containerMinusDividerWidth + ' * ' + (percentageLeft / 2) + ', ' + MIN_BAR_WIDTH + ' / 2), ' + MAX_LABEL_OFFSET + ')';
        centerBarWidth = 'min(max(' + centerWidth + ', ' + MIN_BAR_WIDTH + '), ' + containerMinusDividerWidth + ' - ' + MIN_BAR_WIDTH + ')';
        centerLabelOffset = 'calc(' + leftBarWidth + ' + ' + strDividerWidth + ' + min(max(' + containerMinusDividerWidth + ' * ' + (percentageCenter / 2) + ', ' + MIN_BAR_WIDTH + ' / 2), ' + MAX_LABEL_OFFSET + ')' + ')';
        centerLabelWidth = 'calc( 100% - (' + centerLabelOffset + '))';
        if (percentageLeft + minPercentage > 0.5) {
            centerLabelWidth = centerLabelOffset;
            centerLabelOffset = '';
        }

        // resize bars
        $this.find('.split-bar-left').css('width', leftBarWidth);
        $this.find('.split-bar-right').css('width', rightBarWidth);
        $this.find('.split-bar-center').css('width', centerBarWidth);
        // show and resize the divider
        $this.find('.split-bar-divider').removeClass('d-none-force').css('width', strDividerWidth);
        // reposition and resize the divider indicator
        $this.find('.split-divider-indicator').removeClass('d-none-force').css('width', strDividerWidth)
            .find('> div').css('margin', '0 ' + strDividerMargin);
        $this.find('.split-divider-indicator').eq(0).css('left', leftBarWidth);
        $this.find('.split-divider-indicator').eq(1).css('left', 'calc(100% - (' + containerMinusDividerWidth + ' * ' + percentageRight + ' + ' + strDividerWidth + ')');
        // right align the right labels
        $this.find('.split-right-label').css('width', 'calc(' + rightLabelWidth + ')');
        // left align the left labels
        $this.find('.split-left-label').css({
            'width': 'calc(100% - ' + leftLabelOffset + ')',
            'left': leftLabelOffset
        });
        // left align the center labels
        $this.find('.split-center-label').css({ //TODO
            'width': centerLabelWidth,
            'left': centerLabelOffset
        });
        $this.find('.split-right-label .label-indicator-line-top, .split-right-label .label-indicator-line-bottom').removeClass('align-self-start').addClass('align-self-end');
        $this.find('.split-left-label .label-indicator-line-top, .split-left-label .label-indicator-line-bottom').removeClass('align-self-end').addClass('align-self-start');
        $this.find('.split-divider-label').eq(0).addClass('right').css('width', 'min(' + leftWidth + ' + ' + strDividerWidth + ', 100% - ' + MIN_BAR_WIDTH + ')');
        $this.find('.split-divider-label').eq(1).addClass('left').css({
            'width': 'min(' + rightWidth + ' + ' + strDividerWidth + ', 100% - ' + MIN_BAR_WIDTH + ')',
            'left': 'min(' + leftBarWidth + ' + ' + strDividerWidth + ' + ' + centerBarWidth + ', 100% - ' + MIN_DIVIDER_LABEL_WIDTH + ')',
        });
        if (percentageLeft + minPercentage > 0.5) {
            $this.find('.split-center-label').removeClass('left').addClass('right').find('.label-indicator-line-bottom').addClass('align-self-end');
        }
        $this.find('.split-origin-label').css('position', 'relative');
        if ($this.find('.bars-container').hasClass('single-bar') === true) {
            if (parseFloat($this.find('.split-divider-label').eq(1).css('left')) <= parseFloat(MIN_DIVIDER_LABEL_WIDTH)) {
                $this.find('.split-divider-label').eq(0).removeClass('right').addClass('left').css({
                    'position': 'relative',
                    'left': 'calc(' + leftBarWidth + ' + ' + strDividerWidth + ' / 2)',
                    'width': 'calc(100% - (' + leftBarWidth + ' + ' + strDividerWidth + ' / 2))'
                }).find('.label-indicator-line-divider').addClass('align-self-start').css('min-height', 'calc(' + $this.find('.split-divider-label').eq(1).height() + 'px + 30px)');
            } else if (parseFloat($this.find('.split-divider-label').eq(0).css('width')) >= (containerWidth - parseFloat(MIN_DIVIDER_LABEL_WIDTH))) {
                $this.find('.split-divider-label').eq(1).removeClass('left').addClass('right').css({
                    'position': 'relative',
                    'left': '',
                    'width': 'calc(100% - (' + rightBarWidth + ' + ' + strDividerWidth + ' / 2))'
                }).find('.label-indicator-line-divider').addClass('align-self-end')
                    .css('min-height', 'calc(' + $this.find('.split-divider-label').eq(0).height() + 'px + 30px)');
            }
        } else {
            $this.find('.split-divider-label').eq(0).css('bottom', '-34px');
            if (parseFloat($this.find('.split-divider-label').eq(1).css('left')) <= parseFloat(MIN_DIVIDER_LABEL_WIDTH)) {
                $this.find('.split-divider-label').eq(0).removeClass('right').addClass('left').css({
                    'left': 'calc(' + leftBarWidth + ' + ' + strDividerWidth + ' / 2)',
                    'width': 'calc(100% - (' + leftBarWidth + ' + ' + strDividerWidth + ' / 2))'
                }).find('.label-indicator-line-divider').addClass('align-self-start').css('min-height', 'calc(' + $this.find('.split-divider-label').eq(1).height() + 'px + 30px + 34px)');
            } else if (parseFloat($this.find('.split-divider-label').eq(0).css('width')) >= (containerWidth - parseFloat(MIN_DIVIDER_LABEL_WIDTH))) {
                $this.find('.split-divider-label').eq(1).removeClass('left').addClass('right').css({
                    'left': '',
                    'width': 'calc(100% - (' + rightBarWidth + ' + ' + strDividerWidth + ' / 2))'
                });
            }
        }
    }

    // equalize height of divider/origin label and right label's indicator
    $labelGroupTop = $this.find('.label-group-top');
    $labelGroupBottom = $this.find('.label-group-bottom');
    fixSplitLabelAligments($labelGroupTop, '.split-divider-label', '.label-indicator-line-top > div');
    fixSplitLabelAligments($labelGroupBottom, '.split-origin-label, .split-end-label', '.split-left-label .label-indicator-line-bottom > div, .split-right-label .label-indicator-line-bottom > div');
    if (percentage2 != null) {
        fixSplitLabelAligments($labelGroupBottom, '.split-center-label', '.split-origin-label > .label-indicator-line-origin, .split-right-label .label-indicator-line-bottom > div');
    }

    // resize and align the origin label
    $this.find('.split-origin-label').css({
        'width': 'calc(100% - ' + leftLabelOffset + ')',
        'left': isScrollable ? leftLabelOffset : ''
    });
    // resize and align the end label
    $this.find('.split-end-label').css('width', 'calc(' + rightLabelWidth + ')');
    // reposition the bars
    top = $this.find('.upper-line-container').outerHeight() + $this.find('.label-group-top').outerHeight();
    strCalcTop = 'calc(' + top + 'px)';
    if (top >= maxTopPBE) {
        maxTopPBE = top;
        strCalcMaxTopPBE = strCalcTop;
    }
    $this.find('.bars-container').css('top', strCalcTop);
}

function fixSplitLabelAligments($labelGroup, leftLabelSelector, rightLabelIndicatorSelector) {
    var $labelLeft,
        $labelRightIndicator,
        $labels,
        heightLeft;

	if ($labelGroup == null || $labelGroup.length === 0) {
		return;
    }

	$labelLeft = $labelGroup.find(leftLabelSelector);
	$labelRightIndicator = $labelGroup.find(rightLabelIndicatorSelector);
	$labels = $labelLeft.add($labelRightIndicator);

	$labels.add($labelGroup).css('min-height', '');

    if ($labelLeft.length > 0 && $labelRightIndicator.length > 0) {
        heightLeft = $labelLeft.toArray().reduce(function (minHeight, currLabel) {
            return Math.min(minHeight, $(currLabel).outerHeight());
        }, Number.MAX_SAFE_INTEGER);
        $labels.css('min-height', heightLeft + 'px');

		if($(".split-right-label-DS")[0]){
	        $labels.css('min-height', 36 + 'px');
        }

    } else if ($labelLeft.length > 0) {
        heightLeft = $labelLeft.toArray().reduce(function (minHeight, currLabel) {
                return Math.min(minHeight, $(currLabel).outerHeight());
        }, Number.MAX_SAFE_INTEGER);
		$labels.add($labelGroup).css('min-height', heightLeft + 'px');
    } else if ($labelRightIndicator.length > 0) {
        $labelRightIndicator.css('min-height', '47px');
        if ($labelRightIndicator.closest('.split-left-label, .split-right-label').hasClass('flex-column') == false) {
            heightLeft = $labelRightIndicator.closest('.split-left-label, .split-right-label').find('>div:not(.label-indicator-line-top, .label-indicator-line-bottom)').outerHeight() + 'px';
            $labelRightIndicator.css('min-height', heightLeft);
        }
	}
}

function alignPBEColumns($wrapper) {
	var $topLabelGroups = $wrapper.find('.pbe-col .label-group-top'),
        maxHeight = 0;

	$topLabelGroups.each(function () {
		var height = $(this).outerHeight();

		if (height > maxHeight) {
			maxHeight = height;
		}
	});

    $topLabelGroups.css('min-height', maxHeight);

	alignPBEColumnLabels($wrapper);
}

function alignPBEColumnLabels($wrapper) {
	var $pbeColumns,
		$leftCol,
        $rightCol,
        $rightCol2,
		$leftColLabelLeft,
		$leftColLabelRight,
		$leftColLabelRightIndicator,
		$rightColLabelRight,
		$rightColLabelRightIndicator,
		tempMax,
		leftColLabelHeight,
		rightColLabelHeight,
		barCountLeft,
		barCountRight,
        offsetDiff,
        $leftColTopDivider,
        $rightColTopRight,
        $rightColTopRightIndicator,
        $rightCol2TopLeft,
        $rightCol2TopLeftIndicator,
        $leftColLabelTopRight,
        $leftColLabelTopRightIndicator;

	if ($wrapper == null || $wrapper.length === 0) {
		return;
	}

	$pbeColumns = $wrapper.find('.pbe-col');
	$leftCol = $pbeColumns.eq(0);
    $rightCol = $pbeColumns.eq(1);
    $rightCol2 = $pbeColumns.eq(2);

	if ($pbeColumns.length < 2) {
		return;
	}

	// we currently don't have a use case where right columns have top labels so process bottom labels only
	$leftColLabelLeft = $leftCol.find('.label-group-bottom .split-origin-label');
	$leftColLabelRight = $leftCol.find('.label-group-bottom .split-right-label');
	$leftColLabelRightIndicator = $leftColLabelRight.find('.label-indicator-line-bottom > div');
    $leftColTopDivider = $leftCol.find('.label-group-top .split-divider-label');
    $leftColLabelTopRight = $leftCol.find('.label-group-top .split-right-label');
    $leftColLabelTopRightIndicator = $leftColLabelTopRight.find('.label-indicator-line-top > div');

	$rightColLabelRight = $rightCol.find('.label-group-bottom .split-right-label');
    $rightColLabelRightIndicator = $rightColLabelRight.find('.label-indicator-line-bottom > div');
    $rightColTopRight = $rightCol.find('.label-group-top .split-right-label');
    $rightColTopRightIndicator = $rightColTopRight.find('.label-indicator-line-top > div');

    $rightCol2TopLeft = $rightCol2.find('.label-group-top .split-left-label');
    $rightCol2TopLeftIndicator = $rightCol2TopLeft.find('.label-indicator-line-top > div');
    
	// we currently don't have a use case where right columns have left labels so check right labels only
	if ($leftColLabelRight.length > 0 && $rightColLabelRight.length > 0) {
		leftColLabelHeight = $leftColLabelRightIndicator.outerHeight();
		rightColLabelHeight = $rightColLabelRightIndicator.outerHeight();

		barCountLeft = $leftCol.find('.bars-container').hasClass('single-bar') ? 1 : 2;
		barCountRight = $rightCol.find('.bars-container').hasClass('single-bar') ? 1 : 2;

		// currently we don't have a use case for barCountRight > barCountLeft so else part is really just for when barCountRight === barCountLeft
		if (barCountLeft > barCountRight) {
			if ($leftColLabelLeft.length > 0) {
				// align right col right label to left col left label
				offsetDiff = $leftColLabelLeft.find('> div:not(.label-indicator-line-origin)').offset().top - $rightColLabelRight.find('> div:not(.label-indicator-line-bottom)').offset().top;

				if (offsetDiff > 0) {
					$rightColLabelRightIndicator.css('min-height', $rightColLabelRightIndicator.outerHeight() + offsetDiff + 'px');
				}
			} else if (leftColLabelHeight != rightColLabelHeight) {
				tempMax = Math.max(leftColLabelHeight, rightColLabelHeight);
				$leftColLabelRightIndicator.css('min-height', tempMax + 'px');
				$rightColLabelRightIndicator.css('min-height', tempMax + 'px');
			}
		} else if (leftColLabelHeight != rightColLabelHeight) {
			tempMax = Math.max(leftColLabelHeight, rightColLabelHeight);
			$leftColLabelRightIndicator.css('min-height', tempMax + 'px');
			$rightColLabelRightIndicator.css('min-height', tempMax + 'px');
		}
    }
    if ($leftColTopDivider.length > 0 && $rightColTopRight.length > 0) {
        heightDivider = $leftColTopDivider.toArray().reduce(function (minHeight, currLabel) {
            return Math.min(minHeight, $(currLabel).outerHeight());
        }, Number.MAX_SAFE_INTEGER);
        tempMax = Math.max(heightDivider, $rightColTopRightIndicator.outerHeight());
        $leftColTopDivider.css('min-height', tempMax + 'px');
        $rightColTopRightIndicator.css('min-height', tempMax + 'px');
    }
    if ($wrapper.closest('.ds-pbe-modal').length > 0 && $leftColLabelTopRight.length > 0 && $rightCol2TopLeft.length > 0) {
        tempMax = Math.max($leftColLabelTopRightIndicator.outerHeight(), $rightCol2TopLeftIndicator.outerHeight());
        $leftColLabelTopRightIndicator.css('min-height', tempMax + 'px');
        $rightCol2TopLeftIndicator.css('min-height', tempMax + 'px');
    }
}

function injectExpandForDetails($this) {
	var serviceSelector = '.billing-treegrid-services-title',
		injectibleSelector = '#injectible-expand-text',
		$service;

	$this = $($this);

	if ($this.hasClass('billing-treegrid-row-user')) {
		serviceSelector = '.billing-treegrid-row-user-desc';
		injectibleSelector = '#injectible-expand-text-2';
	}

	$service = $this.find(serviceSelector);

	if ($this.find('.injectible-expand-text-clone').length === 0) {
		$(injectibleSelector).clone().removeAttr('id').removeClass('d-none').addClass('injectible-expand-text-clone').insertAfter($service);
	}

	$service.closest('[role=row]').attr('aria-describedby', 'sr-only-expand-text');
}

function removeExpandForDetails($this) {
	var serviceSelector = '.billing-treegrid-services-title';

	$this = $($this);

	if ($this.hasClass('billing-treegrid-row-user')) {
		serviceSelector = '.billing-treegrid-row-user-desc';
	}

	$this.find('.injectible-expand-text-clone').remove();
	$this.find(serviceSelector).closest('[role=row]').removeAttr('aria-describedby');
}

// supports dismissing of tooltip when ESC is pressed while trigger button is focused. 
// also supports showing of tooltip upon pressing SPACE / ENTER on the button trigger(dismisses on blur or ESC)
function onKeydownTooltip(e) {
    var keyCode = e.keyCode, $el;

    if (27 === keyCode) {
        $(this).tooltip('hide');
    }
    else if (32 === keyCode || 13 === keyCode) {
        e.preventDefault();
        e.stopPropagation();
        $el = $(this);
        $el.tooltip('show');
    }
}

// Tooltip modal keypress on mobile view
function tooltipKeypress() {
    $('.tooltip-interactive').on('keypress', function (e) {
        if (e.keyCode == 13 || e.keyCode == 32) {
            $(this).trigger('click');
        }
    });
}

// Close tooltip when escape key is pressed
$(document).on('keydown','[data-toggle="tooltip"], .grid-pbe-tooltip', function (event) {
    if (event.key === "Escape") {
        $(this).tooltip('hide');
    }
});

$(document).on('keydown', function (event) {
	if (event.key === "Escape") {
		$('.bill-redesign .tooltip:visible').each(function () {
			var bsTooltipData = $(this).data('bs.tooltip');

			if (bsTooltipData) {
				$(bsTooltipData.tip).tooltip('hide');
			}
		});
	}
});

var KEYS = {
    space: 32,
    enter: 13,
    left: 37,
    right: 39,
    up: 38,
    down: 40,
    home: 36,
    end: 35,
    esc: 27
}, ActualTabs;


// START Tab Control (tabs DO NOT cause page redirect)
ActualTabs = {
    options: {
        tabSelector: '.actual-tabs-controller-js[role=tablist] [role=tab]'
    },
    init: function (config) {
        var extendedOptions = $.extend(this.options, config),
            $tabs = $(extendedOptions.tabSelector),
            $tabList = $tabs.first().parent().closest('[role=tablist]');

        $tabList.data('actualtabs-options', JSON.stringify(extendedOptions));

        this.initTabEvents($tabs);
    }, initTabEvents: function (tabs) {
        var $tabs = tabs ? $(tabs) : $(tabs.tabSelector);

        // toggle attributes and class when a tab is clicked
        $tabs.on('click', this._tabClickListener);

        // automatic tabs automatically change tab when arrow keys are pressed. consider supporting manual tabs in the future if necessary
        $tabs.on('keydown', this._tabKeydownListener);
    }, cleanTabEvents: function (tabs) {
        var $tabs = tabs ? $(tabs) : $(tabs.tabSelector);

        $tabs.off('click', this._tabClickListener);
        $tabs.off('keydown', this._tabKeydownListener);
    }, reinit: function (tabs) {
        var $tabs = $(tabs);

        this.cleanTabEvents($tabs);
        this.initTabEvents($tabs);
    }, _tabClickListener: function () {
        var clickedTab = $(this),
            tabList = clickedTab.parent().closest('.actual-tabs-controller-js'),
            tabs,
            scrollTop,
            tabPanelContainer,
            tabPanels,
            i,
            len;

        if (tabList.hasClass('manual-tabs-js')) {
            // support manual activation in the future if necessary
        } else {
            // toggle attribute and class
            tabs = tabList.find('[role=tab]');
            tabs.attr({
                'aria-selected': 'false',
                'tabindex': '-1'
            }).removeClass('active');
            clickedTab.attr({
                'aria-selected': 'true',
				'tabindex': clickedTab.is('a[href]') ? null : 0
            }).addClass('active').filter('a').removeAttr('tabindex');

            // scroll into view horizontally
            scrollTop = $(window).scrollTop();
            // Remove the line of code below as it causes flickering issue on IE
            //clickedTab[0].scrollIntoView();
            $(window).scrollTop(scrollTop);

            // set focus if necessary. this is the case if active tab is changed using left/right/home/<USER>
            if (document.activeElement === this || $(document.activeElement).closest('.actual-tabs-controller-js')[0] === tabList[0]) {
                clickedTab.focus();
            }

            // control tab panel switching if necessary. don't do this for carousels by setting data-carousel-tablist=true
            if (tabList.data('carousel-tablist') !== true) {
                tabPanelContainer = $(tabList.data('tab-panels-container'));
                if (tabPanelContainer.length > 0) {
                    tabPanels = tabPanelContainer.find('[role=tabpanel]').filter(function () { return $(this).parent().closest('[role=tabpanel]', tabPanelContainer[0]).length === 0; });

                    for (i = 0, len = tabs.length; i < len; i++) {
                        if (tabs[i] === this) {
                            tabPanels.eq(i).attr({
                                'tabpanel-selected': 'true',
								'tabindex': tabPanels.eq(i).find('a, button, [tabindex=0], input, select, textarea').length > 0 ? -1 : 0
                            });
                        } else {
                            tabPanels.eq(i).attr({
                                'tabpanel-selected': 'false',
                                'tabindex': -1
                            });
                        }
                    }
                }
            }
        }
    }, _tabKeydownListener: function (e) {
        var key = e.which || e.keyCode || 0,
            tabList = $(this).parent().closest('.actual-tabs-controller-js'),
            isVertical = tabList.attr('aria-orientation') === 'vertical', // if tabs are in vertical arrangement, aria-orientation=vertical must be set
            tabs = tabList.find('[role=tab]'),
            index = 0,
            len = tabs.length;

        for (; index < len; index++) {
            if (this === tabs[index]) {
                break;
            }
        }

        if (key === KEYS.home) {
            index = 0;
        } else if (key === KEYS.end) {
            index = len - 1;
        } else {
            // left & right is for horizontal tabs. up & down is for vertical tabs
            if (!isVertical && key === KEYS.left || isVertical && key === KEYS.up) {
                if (index === 0) {
                    index = len - 1;
                } else {
                    index--;
                }
            } else if (!isVertical && key === KEYS.right || isVertical && key === KEYS.down) {
                if (index === len - 1) {
                    index = 0;
                } else {
                    index++;
                }
            } else {
                return;
            }
        }

        e.preventDefault();
        e.stopPropagation();
        tabs.eq(index).trigger('click');
    }
};
// END Tab Control (tabs DO NOT cause page redirect)

// END global and constants
// START document-ready
(function ($) {
    // initialize actual tabs
    ActualTabs.init();

    // header-tab-control is only for group of links that looks like tabs but DO NOT function as tabs
    // set scrollLeft of header-tab-control to make sure the active item is visible in case there's overflow, we'll center it if possible to make it easier to see that the area is scrollable
    $('.header-tab-control').each(function () {
        var scrollableEl = $(this),
            activeEl = scrollableEl.find('li a.active, li a[aria-current]:not([aria-current=false])').first(),
            listEl = activeEl.parent().closest('ul');

        if (activeEl.is(':not([aria-current])')) {
            activeEl.attr('aria-current', 'page');
        }

        scrollableEl.scrollLeft(activeEl.offset().left - listEl.offset().left - listEl.outerWidth() / 2 + activeEl.outerWidth() / 2);
    });

    // select elements now use bell-blue color if it has a selected value and the selected option is also in that color    
    $('select.colored-selected').each(function () {
        // if the colored-selected class is found, automatically set the other class and attributes
        var el = $(this),
            options = el.find('option'),
            selected = options.eq(this.selectedIndex);

        options.not('[selected]').removeClass('selected');
        selected.addClass('selected');

        options.filter('[value=""]').addClass('no-value');

        if (selected.hasClass('no-value')) {
            el.removeClass('has-selected');
        } else {
            el.addClass('has-selected');
        }
    }).on('change', function () {
        // add a change event listener to toggle the classes and attributes accordingly
        var el = $(this),
            selected = el.find('option').eq(this.selectedIndex);

        el.find('option.selected').removeClass('selected').removeAttr('selected');
        if (selected.hasClass('no-value')) {
            el.removeClass('has-selected');
        } else {
            el.addClass('has-selected');
        }
        selected.addClass('selected').attr('selected', 'selected');
    });

    // modal accessibility fix
    $('.modal:not(.nested-dialog)').on('shown.bs.modal', function () {
        overwriteTabIndexAndAriaHiddenDifferentHierarchy($(this));
    }).on('hidden.bs.modal', function () {
        var el = $(this),
            dismissFocusElSelector = el.data('dismiss-focus-target');
        revertTabIndexAndAriaHiddenDifferentHierarchy(el);
        // if data-dimiss-focus-target is set, we'll set the focus to it once the modal has been closed
        if (undefined !== dismissFocusElSelector) {
            $(dismissFocusElSelector).focus();
        }
     });

    $('.nested-dialog').on('show.bs.modal', function () {
        $(this).find('[tabindex="-1"]').addClass('nested-focusable').removeAttr('tabindex');
    }).on('hidden.bs.modal', function () {
        $(this).find('.nested-focusable').removeClass('nested-focusable').attr('tabindex', '-1');
    });

    //Check if element has sroll data-toggle="modal"
    $('[data-toggle="modal"]').on('click', function () {
        if ($('.table-scrollable-wrapper').length > 0) checkScrollableTable();
    });

	$('.history-disabled-tooltip, .payment-tooltip').each(function () {
        var $this = $(this);

        $this.tooltip({
            trigger: 'manual',
            container: $this
        });
    
        $this.on('mouseenter', function () {
            $this.tooltip('show');
        });
    
        $this.on('mouseleave', function () {
            $this.tooltip('hide');
        });
	});

	$('.bar-tooltip').tooltip();

    $('.bar-tooltip').hover(function () {
        $(this).addClass('hovered');
    }, function () {
        $(this).removeClass('hovered');
    });

    $('.bar-tooltip').on('inserted.bs.tooltip', function (e) {
        var $trigger = $(this),
            tooltipData = $trigger.data('bs.tooltip'),
            $tooltip = tooltipData ? $(tooltipData.tip) : $('[role="tooltip"]'),
			delay = tooltipData ? tooltipData.config.delay.hide : 10;

		$tooltip.addClass('bar-tooltip-div');

        $tooltip.hover(function () {
            $(this).addClass('hovered');
        }, function () {
            $(this).removeClass('hovered');
        });

        $trigger.on('hide.bs.tooltip', function (e) {
            if ($tooltip.hasClass('hovered')) {
                $tooltip.on('mouseleave', function () {
                    setTimeout(function () {
                        if (!$trigger.hasClass('hovered')) {
                            $trigger.tooltip('hide');
                        }
                    }, delay);
                });
                e.preventDefault();
                return;
            }
        });
    });

    $('.bar-tooltip, .payment-tooltip').on('show.bs.tooltip', function (e) {
        $('tooltip.in, .tooltip.show').tooltip('hide');
    });


})(jQuery);
// END document-ready

//Pagination Count
//$(document).ready(function () {
//    var CLASS_DISABLED = "disabled",
//        CLASS_ACTIVE = "active",
//        CLASS_SIBLING_ACTIVE = "active-sibling",
//        DATA_KEY = "pagination";
//    //Count  OL items 

//    $(".pagination").each(initPagination);

//    function initPagination() {
//        var $this = $(this);

//        $this.find(".bill-prev").on("click", navigateSinglePage);
//        $this.find(".bill-next").on("click", navigateSinglePage);
//        $this.find("li").on("click", function () {
//            var $parent = $(this).closest(".pagination");
//            $parent.data(DATA_KEY, $parent.find("li").index(this));
//            changePage.apply($parent);
//        });

//    }

//    function navigateSinglePage() {
//        if (!$(this).hasClass(CLASS_DISABLED)) {
//            return
//        }
//        var $parent = $(this).closest(".pagination")
//        var activePageElement = $parent.find("li.active");
//        var currActive = $parent.find("li").index(activePageElement);
//            currActive += $(this).hasClass("bill-prev") ? -1 : 1;
//            changePage.apply($parent);

//        }

//    function changePage(currActive) {
//        currActive = parseInt($(this).data(DATA_KEY), 10);
//        var $list = $(this).find("li");
           
//        $list.filter("." + CLASS_ACTIVE).removeClass(CLASS_ACTIVE);
//        $list.filter("." + CLASS_SIBLING_ACTIVE).removeClass(CLASS_SIBLING_ACTIVE);

//        $list.eq(currActive).addClass(CLASS_ACTIVE);

//        if (currActive === 0) {
//            $(this).find(".bill-prev").addClass(CLASS_DISABLED);
//        } else {
//            $list.eq(currActive - 1).addClass(CLASS_SIBLING_ACTIVE);
//            $(this).find(".bill-prev").removeClass(CLASS_DISABLED);
//        }

//        if (currActive == ($list.length - 1)) {
//            $(this).find(".bill-next").addClass(CLASS_DISABLED);
//        } else {
//            $(this).find(".bill-next").removeClass(CLASS_DISABLED);
//        }
//    }

//});

$('.barcontainer').each(function () {
    var $this = $(this);
    var $barhistory = $this.find('.bar-history');
    if ($barhistory.length >= 7) {
        $this.removeClass('justify-content-center');
    }
    else {
        $this.addClass('justify-content-center');
    }
});

function convertTableToDiv(e) {
	return e.replace(/(<th)/igm, '<div').replace(/<\/th>/igm, '</div>')
		.replace(/(<td)/igm, '<div').replace(/<\/td>/igm, '</div>');
}

function updateBillingGridStickyNav() {
	if ($(".billing-treegrid").length > 0) {
		var $billingGridBoundingClientRect = $(".billing-treegrid")[0].getBoundingClientRect();
		var stickyNav = $('#billing-grid-sticky-nav');

		if (stickyNav.length > 0) {
			var stickyNavService = $(".billing-grid-sticky-nav-service")[0];
			var stickyNavUserService = $(".billing-grid-sticky-nav-user-service")[0];
			var currentNavService;

			if ($billingGridBoundingClientRect.top < 50) {
				stickyNav.removeClass("d-none");
			}
			else {
				stickyNav.addClass("d-none");
			}

			if ($billingGridBoundingClientRect.bottom < 180) {
				stickyNav.addClass("d-none");
			}

			var rowHeader = document.querySelector('.billing-treegrid > thead > tr > th:first-child');

			if (rowHeader.getBoundingClientRect().top < 50) {
				stickyNavService.innerHTML = "";
			}

            var rowServices = $('.billing-treegrid-row-services:not(.hidden) td:first-child, .billing-treegrid-row-user:not(.hidden) td:first-child, .bell-treegrid-row-services:not(hidden) th:first-child');

			rowServices.each(function () {
				var $row = this;

				if ($row.getBoundingClientRect().top < 50) {
					stickyNavService.innerHTML = convertTableToDiv($row.outerHTML);

					if ($row.querySelector('.billing-treegrid-services-title')) {
						if ($row.querySelector('.billing-treegrid-services-title').innerHTML.indexOf("Other") != -1) {
							currentNavService = "Other";
						}
						else {
							currentNavService = $row.querySelector('.billing-treegrid-services-title').innerHTML;
						}
						stickyNavUserService.innerHTML = "";
					}

					if ($row.querySelector('.billing-treegrid-row-user-desc')) {
						stickyNavUserService.innerHTML = currentNavService;
					}
				}
			});
		}
	}
}

function updateBillingGridStickyNavDeferred(delay) {
	setTimeout(updateBillingGridStickyNav, delay === 'number' ? delay : 200);
}

$(document).ready(function () {
    $(document).on('click', '.js-disc-button', function (e) {
        var $this = $(this),
            expandedState = $this.attr('aria-expanded'),
            disclosureContainerID = $this.attr('aria-controls'),
            discButtonActive = $('.js-disc-button[aria-expanded="true"]');

        discButtonActive.attr('aria-expanded', 'false');
        discButtonActive.find('[class*=" icon-"]').removeClass('icon-chevron-up').addClass('icon-chevron-down');
        $(discButtonActive.attr('aria-controls')).addClass('d-none');

        $('.js-remove-selected-filter:not(.d-none)').each(function () {
            $('[data-target-services="#' + $(this).attr('id') + '"]').prop('checked', true);
        });

        if (expandedState === 'true') {
            $this.attr('aria-expanded', 'false');
            $(disclosureContainerID).addClass('d-none');
            $this.find('[class*=" icon-"]').removeClass('icon-chevron-up').addClass('icon-chevron-down');
        } else {
            $this.attr('aria-expanded', 'true');
            $(disclosureContainerID).removeClass('d-none');
            $this.find('[class*=" icon-"]').removeClass('icon-chevron-down').addClass('icon-chevron-up');
            setDisclosureOnKeydown($(disclosureContainerID));
        }

        e.stopPropagation();
    }).on('keydown', '.js-disc-button', function (e) {
        var key = e.which || e.keyCode || 0;

        if (27 === key) {
            e.preventDefault();
            e.stopPropagation();
            $('.container-disclosure').addClass('d-none');
            $(this).attr('aria-expanded', false);
            $(this).find('[class*=" icon-"]').removeClass('icon-chevron-up').addClass('icon-chevron-down');
        }

    });

    $(document).on('click', '.js-apply-filter', function (e) {
        var $this = $(this),
            //$Checkboxes = $this.parent().siblings('.select-filter').find('.checkbox-filter'),
            $Checkboxes = $this.parent().siblings('.select-filter'),
            selector,
            $disclosureTrigger = $this.parent().parent().siblings('.js-disc-button');

        //Display selected filter
        selector = window.matchMedia('(max-width: 767px)').matches ? $('.filter-dialog') : $Checkboxes;

        selector.each(function () {
            var $checkboxes = $(this).find('.checkbox-filter'),
                selectorCategory,
                noCheckedState = true;

            $checkboxes.each(function () {
                var $chkbox = $(this),
                    targetID = $chkbox.data('target-services'),
                    triggerCategory = selectorCategory = $chkbox.data('class-category');

                if ($chkbox.is(':checked')) {
                    $(targetID).removeClass('d-none');
                    $('.' + triggerCategory).addClass('selected');
                    noCheckedState = false;
                } else {
                    $(targetID).addClass('d-none');
                }
            });

            if (noCheckedState) {
                $('.' + selectorCategory).removeClass('selected');
            } else {
                noCheckedState = true;
            }
        });

        e.stopPropagation();

        clearAllAppliedFilters();

        AriaLiveFilter();

        if ($disclosureTrigger.length > 0) {
            $disclosureTrigger.trigger('click');
        }
    });

    $(document).on('click', '.js-remove-selected-filter button', function (e) {
        var $this = $(this),
            $Container = $this.closest('.js-remove-selected-filter'),
            $TargetCheckbox = $('[data-target-services="#' + $Container.attr('id') + '"]'),
            ClassCategory = $TargetCheckbox.data('class-category');


        $TargetCheckbox.prop('checked', false);
        $Container.addClass('d-none');


        if ($TargetCheckbox.closest('.select-filter').find('.checkbox-filter:checked').length == 0) {
            $('.' + ClassCategory).removeClass('selected');
        }


        if ($('.js-remove-selected-filter:not(.d-none)').length == 0) {
            $('.clear-applied-filter').addClass('d-none');
        }

        e.stopPropagation();
    });

    $(document).on('click', '.clear-applied-filter a', function (e) {
        var $this = $(this),
            $targetSelectedFilters = $('.js-remove-selected-filter:not(.d-none)');

        $targetSelectedFilters.each(function () {
            $(this).addClass('d-none');
            $('[data-target-services="#' + $(this).attr('id') + '"]').prop('checked', false);
        });

        $this.parent().addClass('d-none');
        $('.filter-category').removeClass('selected');
        e.stopPropagation();
    });

    $(document).on('click', '.clear-selected-filter', function (e) {
        var $this = $(this),
            $targetAppliedFilters = $this.parent().siblings('.select-filter').find('.checkbox-filter');

        if ($targetAppliedFilters.length == 0) {
            $targetAppliedFilters = $this.parent().siblings('.accordion-wrap').find('.select-filter .checkbox-filter');
        }

        $targetAppliedFilters.each(function () {
            $(this).prop('checked', false);
        });
        e.stopPropagation();
    });

    $(document).on('change', '.checkbox-filter', function (e) {
        var $addressContainer = $(this).closest('.address-disclosure');

        if ($addressContainer.length > 0) {
            $addressContainer.find('.checkbox-filter').prop('checked', false);
            $(this).prop('checked', true);
        }
    });

    $('body').on('mouseup', function (e) {
        var $flyout = $(".container-disclosure:visible");

        if (!$flyout.is(e.target) && $flyout.has(e.target).length === 0 && $flyout.is(':visible') && !$(e.target).hasClass('js-disc-button')) {
            $('[aria-controls="#' + $flyout.attr('id') + '"]').trigger('click');
        }
    });

    var testResize;

    updateComponentsOrder();
    $(window).resize(function () {
        clearTimeout(testResize);
        testResize = setTimeout(updateComponentsOrder, 100);
    });

});

function updateComponentsOrder() {
    if (window.matchMedia('(max-width: 767px)').matches) {
        $('.container-disclosure:visible').siblings('.js-disc-button').trigger('click');
    } else {
        if ($('.filter-dialog.show').length > 0) {
            $('.filter-dialog.show').modal('hide');
        }
    }
}

function setDisclosureOnKeydown($el) {
    //hide content when focus is inside the containers focusable element
    if (!$el.hasClass('js-disclosure-on-keydown-init')) {
        $el.on('keydown', function (e) {
            var $this = $(this),
                key = e.which || e.keyCode;

            $this.addClass('js-disclosure-on-keydown-init');

            if (key === 27) {
                var $containerDisclosure = $this.closest('.container-disclosure'),
                    $btnDisclosureTrigger = $('[aria-controls="#' + $containerDisclosure.attr('id') + '"]');

                $containerDisclosure.addClass('d-none');
                $btnDisclosureTrigger.find('[class*=" icon-"]').removeClass('icon-chevron-up').addClass('icon-chevron-down');
                $btnDisclosureTrigger.attr('aria-expanded', 'false').focus();
            }
        });
    }
}

function clearAllAppliedFilters() {
    // clears all previous applied options when there is no selected checkbox
    if ($('.js-remove-selected-filter:not(.d-none)').length > 0) {
        $('.clear-applied-filter').removeClass('d-none');
    } else {
        $('.clear-applied-filter').addClass('d-none');
        $('.filter-category').removeClass('selected');
    }
}

function AriaLiveFilter() {
    var $currentlyOpenedContainer = $('.container-disclosure:visible').length > 0 ? $('.container-disclosure:visible') : $('.filter-dialog'),
        TotalFilterCount = $currentlyOpenedContainer.find('.checkbox-filter').length,
        CheckedFilterCount = $currentlyOpenedContainer.find('.checkbox-filter:checked').length;

    $('.announce-filter-count').text("Selected filter " + CheckedFilterCount + " of " + TotalFilterCount);
};

function setPaymentListVisibility() {
    var $paymentList = $('.payment-list-due-date');

    $paymentList.each(function () {
        var $this = $(this),
            $totalNumtoShow = $this.data('payment-display'),
            $listItem = $this.find('li');

        $listItem.attr('aria-setsize', $listItem.length);

        if ($listItem.length > $totalNumtoShow) {
            $this.siblings('.payment-list-trigger-container').removeClass('s-none').addClass('d-inline-block');
            hidePaymentListDisplay($this);

        } else {
            $this.siblings('.payment-list-trigger-container').addClass('d-none').removeClass('d-inline-block');
        }
        
    });
};

function hidePaymentListDisplay($el) {
    var listCountToShow = $el.data('payment-display'),
        count = 1;
   
    $el.find('.payment-list-due-date-item').each(function () {
        var $this = $(this);

        if (count > listCountToShow) {
            $this.hide();
        }

        count += 1;
    });
}

$(document).on('click', '.had-data-toggle-modal', function () {
    $($(this).attr('data-target')).modal('show');
});

$(document).on('shown.bs.tooltip', '.bill-tour-tooltip', function (e) {
    $('.ds-bill-tour-tooltip-container [data-toggle="modal"]').removeAttr('data-toggle').addClass('had-data-toggle-modal');

    $('.btn-tour-later').on('click', function (e) {
        var $this = $(this);

        $this.closest('.ds-bill-tour-tooltip-content').hide();
        $this.closest('.ds-bill-tour-tooltip-container').find('.ds-bill-tour-later-content').removeClass('d-none');
        $('.bill-tour-tooltip').focus();
        $('.bill-tour-tooltip').tooltip('update');
    });
});

$(document).on('keydown', '.bill-tour-tooltip', function (e) {
        var $tooltipTrigger = $('.bill-tour-tooltip'),
        key = e.which || e.keyCode || 0;

    if (e.target != this) {
        if (e.which === 27) {
            $tooltipTrigger.focus();
        }
    }

    if (key === 13 || key === 32) {
        if ($(e.target).is('button')) {
            $(e.target).click();
        }
        else if ($(e.target).hasClass('bill-tour-tooltip')) {

            $(e.target).tooltip('show');
        }
    }
});

function scrollToSummary() {
    $('html,body').animate({
        scrollTop: $("#js-summary-changes-container").offset().top
    }, 1500);
};

function scrollToElement(elem, speed, offsetTop) {
    var defaultSpeed = 1500,
        defaultOffsetTop = !isNaN(offsetTop) ? offsetTop : 55,
        $elem = $(elem);

    $('html, body').animate({
        scrollTop: $(elem).offset().top - defaultOffsetTop
    }, !isNaN(speed) ? speed : defaultSpeed);

    if (!$elem.attr('tabindex')) {
        $elem.attr('tabindex', '-1');
    }

    $elem.focus();
};

function initTileCarousel() {
    if (typeof $.fn.slick !== 'function') {
        return;
    }

    $('.tile-carousel').on('beforeChange', function (event, slick, currentSlide, nextSlide) {
        if (window.matchMedia('(max-width: 767px)').matches) {
            var $slider = $(this);
            var slickSlideCount = $slider.find('.slick-slide').length;

            if (nextSlide > 0 && nextSlide < (slickSlideCount - 1)) {
                $slider.find('.slick-track').css("margin-left", "0");
            } else if (nextSlide == (slickSlideCount - 1)) {
                if ($slider.hasClass('slick-plan-js')) {
                    $slider.find('.slick-track').css("margin-left", "-15px");
                } else {
                    $slider.find('.slick-track').css("margin-left", "15px");
                }
            } else {
                $slider.find('.slick-track').css("margin-left", "");
            }
        }
    });
}

function initSliderCarousel() {
    if (typeof $.fn.slick !== 'function') {
        return;
    }

    var isMobileUA = false;

    if (navigator.userAgent.match(/Android/i)
        || navigator.userAgent.match(/webOS/i)
        || navigator.userAgent.match(/iPhone/i)
        || navigator.userAgent.match(/iPad/i)
        || navigator.userAgent.match(/iPod/i)
        || navigator.userAgent.match(/BlackBerry/i)
        || navigator.userAgent.match(/Windows Phone/i)) {
        isMobileUA = true;
    }

    $('.slider-rotating-carousel').each(function () {
        var $sliderCarousel = $(this),
            $sliderCarouselContainer = $sliderCarousel.closest('.slider-rotating-carousel-component'),
            $carouselPauseBtnCircle = $sliderCarouselContainer.find('.slider-rotating-carousel-pause circle'),
            autoplaySpeed,
            progressIndicatorLength,
            progressIndicatorUnit,
            slideTabLabelPrefix = $sliderCarouselContainer.data('slide-tab-label-prefix'),
            slideTabLabelSuffix = $sliderCarouselContainer.data('slide-tab-label-suffix'),
            slideTabLabelSuffixObject = slideTabLabelSuffix ? JSON.parse(slideTabLabelSuffix.replace(/'/g, '"')) : null;

        if (isMobileUA) {
            autoplaySpeed = $sliderCarousel.data('autoplay-speed-mobile');
        }

        if (autoplaySpeed == null) {
            autoplaySpeed = $sliderCarousel.data('autoplay-speed');

            if (autoplaySpeed == null) {
                autoplaySpeed = 6000;
            }
        }

        $sliderCarousel.data('autoplay-speed', autoplaySpeed);

        progressIndicatorLength = typeof SVGElement.prototype.getTotalLength !== "undefined" ? Math.round($carouselPauseBtnCircle.get(0).getTotalLength()) : 125;
        progressIndicatorUnit = progressIndicatorLength / 100;

        $sliderCarouselContainer.data({
            'autoplay-interval-values': {
                'progressIndicatorLength': progressIndicatorLength,
                'progressIndicatorUnit': progressIndicatorUnit,
                'percentComplete': 0
            }
        });

        $sliderCarousel.slick({
            pauseOnFocus: true,
            pauseOnHover: true,
            infinite: false,
            autoplay: false,
            dots: true,
            arrows: false,
            accessibility: false,
            waitForAnimate: false,
            customPaging: function (slider, i) {
                var paginationLabelIndex = 'page' + (i + 1),
                    paginationAddedLabel = slideTabLabelSuffixObject ? slideTabLabelSuffixObject[paginationLabelIndex] : null;

                return '<button class="slider-rotating-carousel-button" type="button" role="tab" '
                    + 'id="slick-slide-control-' + i
                    + '" aria-label="' + slideTabLabelPrefix + ' ' + (i + 1) + (paginationAddedLabel ? ', ' + paginationAddedLabel : '')
                    + '" aria-controls="slick-slide-' + i
                    + '">' + (i + 1) + '</button>';
            },
            dotsClass: 'slider-rotating-carousel-buttons',
            initialSlide: 0
        }).on("beforeChange", function (e, slickInstance) {
            var $sliderCarouselContainer = slickInstance.$slider.closest('.slider-rotating-carousel-component');
            resetAutoPlay($sliderCarouselContainer, true);
            checkAutoPlay($sliderCarouselContainer);
        }).on("afterChange", function (e, slickInstance) {
            var $sliderCarouselContainer = slickInstance.$slider.closest('.slider-rotating-carousel-component');
            fixSliderAccessibilityAttributes($sliderCarouselContainer),
			$videos = $sliderCarouselContainer.find('video');
			
			$videos.each(function() {
				var $video = $(this),
					$parentSlide = $video.closest('.slick-slide');
					
				if ($parentSlide.hasClass('slick-active')) {
					$video.removeAttr('tabindex');
				} else {
					$video.attr('tabindex', '-1');
				}
			});
        });
    });
}

function initProactiveTourDialog() {
    if (typeof $.fn.slick !== 'function') {
        return;
    }
    /* START - Proactive tour dialog */
    $('#proactive-tour-dialog').on('show.bs.modal', function () {
        var $sliderCarouselContainer = $(this).find('.slider-rotating-carousel-component'),
            $sliderCarousel = $sliderCarouselContainer.find('.slider-rotating-carousel');

        $sliderCarousel.slick('slickGoTo', 0);
        $sliderCarouselContainer.removeData('force-autoplay');
        resetAutoPlay($sliderCarouselContainer, true);
    }).on('shown.bs.modal', function () {
        var $sliderCarouselContainer = $(this).find('.slider-rotating-carousel-component'),
            $sliderCarousel = $sliderCarouselContainer.find('.slider-rotating-carousel'),
            $carouselPauseBtn = $sliderCarouselContainer.find('.slider-rotating-carousel-pause');

        $sliderCarousel.slick('setPosition');
        $sliderCarousel.find('.slick-list').attr('aria-live', 'off');

        initSliderAccessibilityAttributes($sliderCarouselContainer);

        updatePauseBtnLeftStyle();

        $carouselPauseBtn.attr('aria-label', $(this).attr('data-play-label')).removeClass('state-paused');

        setTimeout(function () {
            startAutoPlay($sliderCarouselContainer);
        }, 400);
    });

    $(window).on('resize', function () {
        updatePauseBtnLeftStyle();
    });

    $(document).on('keydown focus', '.slider-rotating-carousel-buttons', function (e) {
        var $sliderCarouselContainer = $(this).closest('.slider-rotating-carousel-component'),
            $sliderCarousel = $sliderCarouselContainer.find('.slider-rotating-carousel'),
            slidesCount = $sliderCarousel.find('.slick-slide:not(.slick-cloned)').length,
            $currentSlide = $sliderCarousel.find('.slick-slide.slick-current:not(.slick-cloned)'),
            $this = $(this),
            key = e.which || e.keyCode || 0;


        if (key === 35) {
            $sliderCarousel.slick('slickGoTo', slidesCount - 1);
        }
        else if (key === 36) {
            $sliderCarousel.slick('slickGoTo', 0);
        }
        else if (key === 37) {
            if ($currentSlide.data('slick-index') === 0) {
                $sliderCarousel.slick('slickGoTo', slidesCount - 1);
            }
            else {
                $sliderCarousel.slick('slickPrev');
            }
        }
        else if (key === 39) {
            if (($currentSlide.data('slick-index') + 1) === slidesCount) {
                $sliderCarousel.slick('slickGoTo', 0);
            }
            else {
                $sliderCarousel.slick('slickNext');
            }
        }

        if (key === 35 || key === 36 || key === 37 || key === 39) {
            $this.find('.slick-active .slider-rotating-carousel-button').focus();
        }

        $sliderCarousel.find('.slick-list').attr('aria-live', 'polite');
    }).on('blur', '.slider-rotating-carousel-button', function (e) {
        var $sliderCarouselContainer = $(this).closest('.slider-rotating-carousel-component'),
            $sliderCarousel = $sliderCarouselContainer.find('.slider-rotating-carousel');

        $sliderCarousel.find('.slick-list').attr('aria-live', 'off');
    }).on('focus mouseenter', '.slider-rotating-carousel', function (e) {
        var $sliderCarouselContainer = $(this).closest('.slider-rotating-carousel-component');

        if (e.type === 'mouseenter') {
            $sliderCarouselContainer.addClass('mouse-within');
        }

        checkAutoPlay($sliderCarouselContainer);
    }).on('blur mouseleave', '.slider-rotating-carousel', function (e) {
        var $sliderCarouselContainer = $(this).closest('.slider-rotating-carousel-component');

        if (e.type === 'mouseleave') {
            $sliderCarouselContainer.removeClass('mouse-within');
        }

        checkAutoPlay($sliderCarouselContainer);
    }).on('mouseenter', '.slider-rotating-carousel-pause', function (e) {
        var $carouselPauseBtn = $(this),
            $sliderCarouselContainer = $(this).closest('.slider-rotating-carousel-component');

        $carouselPauseBtn.addClass('mouse-within');

        checkAutoPlay($sliderCarouselContainer);
    }).on('mouseleave', '.slider-rotating-carousel-pause', function () {
        var $carouselPauseBtn = $(this),
            $sliderCarouselContainer = $(this).closest('.slider-rotating-carousel-component');

        $carouselPauseBtn.removeClass('mouse-within');

        checkAutoPlay($sliderCarouselContainer);
    }).on('click', '.slider-rotating-carousel-pause', function () {
        var $carouselPauseBtn = $(this),
            $sliderCarouselContainer = $carouselPauseBtn.closest('.slider-rotating-carousel-component'),
            $sliderCarousel = $sliderCarouselContainer.find('.slider-rotating-carousel');

        if ($sliderCarouselContainer.data('force-autoplay') === false) {
            $sliderCarouselContainer.data('force-autoplay', true);
            startAutoPlay($sliderCarouselContainer);
            $carouselPauseBtn.removeClass('state-paused').attr('aria-label', $carouselPauseBtn.attr('data-pause-label'));
            $sliderCarousel.find('.slick-list').attr('aria-live', 'off');
        } else {
            $sliderCarouselContainer.data('force-autoplay', false);
            stopAutoPlay($sliderCarouselContainer);
            $carouselPauseBtn.addClass('state-paused').attr('aria-label', $carouselPauseBtn.attr('data-play-label'));
            $sliderCarousel.find('.slick-list').attr('aria-live', 'polite');
        }
    }).on('visibilitychange', function () {
        $('.slider-rotating-carousel-component').each(function () {
            var $sliderCarouselContainer = $(this);

            if (document.visibilityState === 'hidden') {
                stopAutoPlay($sliderCarouselContainer);
            } else {
                startAutoPlay($sliderCarouselContainer);
            }
        });
    });

    /* END - Proactive tour dialog */
}

function setupLegalDisclosure() {
    $(document).on('click', '.disclosure-toggle', function () {
        var $disclosureButton = $(this),
            $disclosure = $disclosureButton.closest('.disclosure'),
            $disclosureContent = $disclosure.find('.disclosure-content');

        toggleDisclosure($disclosureButton, $disclosureContent);
    }).on('keydown', '.disclosure-toggle', function (e) {
        var key = e.which || e.keyCode || 0;

        if (13 === key || 32 === key) {
            e.preventDefault();
            e.stopPropagation();
            $(this).trigger('click');
        }
    });

    function toggleDisclosure($disclosureButton, $disclosureContent) {
        var isExpanded = $disclosureButton.attr('aria-expanded') === 'true',
			isExpanding = !isExpanded,
            base64Src;

        $disclosureButton.attr('aria-expanded', isExpanding);

        if (isExpanding) {
            $disclosureContent.addClass('show');
            $disclosureButton.removeClass('collapsed');
            base64Src = 'data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMyAyMyI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiMwMDU0OWE7ZmlsbC1ydWxlOmV2ZW5vZGQ7fTwvc3R5bGU+PC9kZWZzPjxnIGlkPSJCaWxsLWdyaWRpbmciPjxwYXRoIGNsYXNzPSJjbHMtMSIgZD0iTTE3LDExLjVhMSwxLDAsMCwxLTEsMUg3YTEsMSwwLDAsMSwwLTJIMTZBMSwxLDAsMCwxLDE3LDExLjVabTYsMEExMS41LDExLjUsMCwxLDEsMTEuNSwwLDExLjUsMTEuNSwwLDAsMSwyMywxMS41Wm0tMiwwQTkuNSw5LjUsMCwxLDAsMTEuNSwyMSw5LjUsOS41LDAsMCwwLDIxLDExLjVaIi8+PC9nPjwvc3ZnPg==';
        } else {
            $disclosureButton.addClass('collapsed');
            $disclosureContent.removeClass('show');
            base64Src = 'data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMyAyMyI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiMwMDU0OWE7fTwvc3R5bGU+PC9kZWZzPjxwYXRoIGNsYXNzPSJjbHMtMSIgZD0iTTExLjUsMEExMS41LDExLjUsMCwxLDAsMjMsMTEuNSwxMS41LDExLjUsMCwwLDAsMTEuNSwwWm0wLDIxQTkuNSw5LjUsMCwxLDEsMjEsMTEuNSw5LjUsOS41LDAsMCwxLDExLjUsMjFaTTE3LDExLjM4di4xMmExLDEsMCwwLDEtMSwxSDEyLjVWMTZhMSwxLDAsMCwxLS44OCwxSDExLjVhMSwxLDAsMCwxLTEtMVYxMi41SDdhMSwxLDAsMCwxLTEtLjg4VjExLjVhMSwxLDAsMCwxLDEtMUgxMC41VjdhMSwxLDAsMCwxLC44OC0xaC4xMmExLDEsMCwwLDEsMSwxVjEwLjVIMTZBMSwxLDAsMCwxLDE3LDExLjM4WiIvPjwvc3ZnPg=='; 
        }

        $disclosureButton.find('img').attr('src', base64Src);
    }
}

function removeEmptyServiceFooterContainer() {
    var $emptyServiceFooterContainer = $('.bell-services-mobility-modal .PersonalizationTilesContainer:empty');

    if ($emptyServiceFooterContainer.length === 0) {
        return;
    }

    $emptyServiceFooterContainer.parent('div').css({ 'padding-top': '0', 'padding-bottom': '0' });
}

function handleNestedDialogClose() {
    if (typeof iConPjQuery !== 'undefined' && typeof iConPjQuery.scope == 'function') {
        iConPjQuery.scope(document).on('click', '.nested-dialog .close', function () {
            iConPjQuery.scope(this).closest('.modal').modal('hide');
        });
    }
}

function processSameWidth(selector) {
    var $containers = $('.js-same-width-delegate');

    $containers.each(function () {
        var $container = $(this),
            $elements = $container.find(selector),
            maxWidth = 0;

            $elements.css('min-width', '');
   
        $elements.each(function () {
            maxWidth = Math.max(maxWidth, $(this).width());
        });

        $elements.css('min-width', maxWidth + 'px');
    });
}

function processLastPaymentLabels() {
    var $containers = $('.pbe-last-payment-charge-container');

    $containers.each(function () {
        var $container = $(this),
            $unpaidLabelIndicator = $container.find('.unpaid-balance-col .label-indicator-line-top'),
            $currentLabel = $container.find('.current-late-payment-charge-label'),
            $previousCol = $container.find(".previous-bill-col"),
            $lastChargeText = $container.find(".pbe-last-charge-label-inner-text"),
            $unpaidBalanceCol = $container.find('.unpaid-balance-col'),
            distanceFromLeft = $unpaidLabelIndicator.offset().left - $unpaidLabelIndicator.closest('.labels-container').offset().left,
            distanceFromRight = $currentLabel.closest('.labels-container').width() - ($currentLabel.offset().left - $currentLabel.closest('.labels-container').offset().left),
            unpaidDistanceFromRight = $unpaidLabelIndicator.closest('.labels-container').offset().left + $unpaidLabelIndicator.closest('.labels-container').outerWidth() - $unpaidLabelIndicator.offset().left - $unpaidLabelIndicator.outerWidth(),
            columnSpacing = parseFloat($unpaidLabelIndicator.closest('.pbe-col').css('margin-right')),
            leftMargin = (($previousCol.width() - $lastChargeText.outerWidth()) / 2) + (unpaidDistanceFromRight + columnSpacing),
            indicatorToBottomDistance = $unpaidBalanceCol.offset().top + $unpaidBalanceCol.outerHeight() - $unpaidLabelIndicator.offset().top - $unpaidLabelIndicator.outerHeight(),
            indicatorLeftHeight = indicatorToBottomDistance - ($lastChargeText.outerHeight() / 2);

        $container.find('.pbe-last-charge-label-inner').css({
            'padding-left': distanceFromLeft + 'px',
            'padding-right': distanceFromRight + 'px'
        });

        $container.find(".dashed-line-indicator-left").width(leftMargin);
        $container.find(".dashed-line-indicator-left-h").height(indicatorLeftHeight);
    });
}
function bsmodalBlockPaddingRight() {
    var b = document.body;
    const t = new MutationObserver(() => {
        const n = $(window).width();
        $("body.modal-open").css({
            "padding-right": "0px",
            "max-width": n + "px"
        });
        $("body.modal-open .g-header").css("max-width", n + "px");
        $("body.modal-open #billing-grid-sticky-nav").css({
            "padding-right": "0px",
            "max-width": n + "px"
        });
        $("body:not(.modal-open) ,body:not(.modal-open) .g-header, body:not(.modal-open) #billing-grid-sticky-nav ").css({
            "padding-right": "",
            "max-width": ""
        });
    }
    );
    t.observe(b, { attributes: true, attributeFilter: ["style"] });
}

$(document).ready(function () {
    handleNestedDialogClose();
    setupLegalDisclosure();
    bsmodalBlockPaddingRight();
});

//bpe Remade
function pbeSplitChartRemade() {
    var $jsCharts = $('.pbe-js-chart'),
        $pbeChart = $('.pbe-chart');
   

    $.each($pbeChart, function () {
        $(this).find('.pbe-js-chart').first().find('.pbe-chart-container').addClass('pbe-chart-prev');
    });

    $.each($jsCharts, function () {
        
        var $jsChart = $(this),
            $pbeChartContainer = $jsChart.find('.pbe-chart-container'),
            splitChart = $jsChart.data('split-chart'),
            startDate = new Date(splitChart.startDate),
            endDate = new Date(splitChart.endDate),
            transactions = splitChart.transactions,
            transcount = (transactions !== undefined) ? transactions.length : 0,
            transMaxHeight,
            chargeItems = splitChart.chargeItems,
            itemcount = (chargeItems !== undefined) ? chargeItems.length - 1 : 0,
            oneDay = 24 * 60 * 60 * 1000,
            totalDays = Math.round(Math.abs((startDate - endDate) / oneDay)) + 1,
            useLegends = splitChart.UseLegendsForDiagram,
            isUpperExist = chargeItems.find(x => x.position === 'upper') !== undefined,
            isBelowExist = chargeItems.find(x => x.position === 'below') !== undefined || chargeItems.find(x => x.position === 'CURRENT') !== undefined,
            isSingleBar = isBelowExist && !isUpperExist;

        //START: PBE labels
        var $pbeLabelsContainer = $jsChart.find('.pbe-labels-container'),
            $srLabelsContainer;

        $jsChart.append('<div class="pbe-sr-labels sr-only"></div>');
        $srLabelsContainer = $jsChart.find('.pbe-sr-labels');
        
        if (isUpperExist) {
            $pbeChartContainer.append("<div class='pbe-chart-bars-upper'></div>");
            $pbeLabelsContainer.append("<div class='pbe-label-top-group relative bgGray19'></div>");
        }

        if (isBelowExist) {
            $pbeChartContainer.append("<div class='pbe-chart-bars-below'></div>");
            $pbeLabelsContainer.append("<div class='pbe-label-bottom-group relative bgGray19'></div>");
        }

        if (transactions !== undefined && transactions.length > 0) {
            $pbeChartContainer.append("<div class='pbe-chart-transacs'></div>");
        }

        if (isSingleBar) {
            $pbeChartContainer.addClass('single-bar');
        }

        //START chargeItems/bars
        $.each(chargeItems, function () {
            var $this = this,
                $barContainer = ($this.position === "upper") ? $pbeChartContainer.find('.pbe-chart-bars-upper') : $pbeChartContainer.find('.pbe-chart-bars-below'),
                $bar,
                barDays,
                barLabel = $this.label,
                barSubLabel = $this.sub_label,
                itemLegend = $this.itemLegend,
                posDays,
                pOfBars,
                pPosDays,
                $legend = ($this.legend !== undefined) ? "<div class='pbe-legend'>" + $this.legend + "</div>" : '',
                legendOnly = '',
                horizontalPosition = 'middle-position';
            
            $this.startDate = new Date($this.startDate);
            $this.endDate = new Date($this.endDate);
            
            //width computation
            barDays = Math.round(Math.abs(($this.startDate - $this.endDate) / oneDay)) + 1;
            posDays = Math.round(Math.abs(($this.startDate - startDate) / oneDay));

            pOfBars = (barDays * 100) / totalDays;
            pPosDays = (posDays * 100) / totalDays;
            
            if (useLegends) {
                legendOnly = ($this.alwaysLabel !== true) ? 'legend-only' : '';
            }

            $bar = $("<div>", {
                "class": "pbe-chart-bar txtCenter txtWhite h-100 " + itemLegend + " " + legendOnly,
                "style": "width:" + pOfBars + "%; left:" + pPosDays + "%",
                "data-bar-id": itemcount
            });
            $bar.append($legend);
            $barContainer.append($bar);

            // START: PBE labels for bars
            var $labelContainer = ($this.position === "upper") ? $pbeLabelsContainer.find('.pbe-label-top-group') : $pbeLabelsContainer.find('.pbe-label-bottom-group'),
                labelWidth = pOfBars + "%", //width used is the same with its corresponding bar
                labelPosition = pPosDays + "%", //position used is the same with its corresponding bar
                $labelWrapper,
                $labelLineIndicator = "<div class='pbe-line-indicator'><div></div></div>",
                $labelDescription = "<div class='surtitle-black'>" +  barLabel + "</div>",
                $labelDescriptionSub = (barSubLabel !== undefined) ? "<div class='small-text margin-t-4'>" + barSubLabel + "</div>" : '',
                $labelStructure,
                lastLabel,
                $srDateRange = legendOnly === "" ? "<div class='sr-only'> From " + $this.startDate.toDateString() + " - " + $this.endDate.toDateString() + "</div>" : '';


            lastLabel = (itemcount == 0) ? 'label-wrapper-last' : '';
            
            // position line indicator depending on the label position
            if ($this.position === "upper") {
                $labelStructure = "<div class='pbe-label'>" + $labelDescription + $labelDescriptionSub + $srDateRange + "</div>" + $labelLineIndicator;
            } else {
                $labelStructure = $labelLineIndicator + "<div class='pbe-label'>" + $labelDescription + $labelDescriptionSub + $srDateRange + "</div>";
            }

            if ($this.startDate.getTime() <= startDate.getTime() && $this.endDate.getTime() < endDate.getTime()) {
                horizontalPosition = 'left-position';
            } else if ($this.startDate.getTime() > startDate.getTime() && $this.endDate.getTime() >= endDate.getTime()) {
                horizontalPosition = 'right-position';
            }
            
            if (legendOnly !== '') {
                $labelWrapper = $("<div>", {
                    "class": "pbe-label-wrapper " + itemLegend + " " + legendOnly + " " + lastLabel,
                    "style": "width:" + labelWidth + "; left:" + labelPosition,
                    "data-label-bar-id": itemcount
                });
                $labelWrapper.append($labelStructure);
            } else {
                $labelWrapper = $("<div>", {
                    "class": "pbe-label-wrapper " + legendOnly + " " + lastLabel,
                    "style": "left:" + labelPosition,
                    "data-label-bar-id": itemcount
                });
                $labelWrapper.append($labelStructure);
            }

            $labelWrapper.addClass(horizontalPosition);
            $labelWrapper.data('horizontal-position',horizontalPosition);
            $labelContainer.append($labelWrapper);


            // START: label position add class
            var label_location = ($this.label_position !== undefined && $this.label_position !== '') ? $this.label_position : $this.position;

            //if (label_location !== "") {
            $labelWrapper = $labelContainer.find('.pbe-label-wrapper[data-label-bar-id="' + itemcount + '"]');
            $labelWrapper.addClass(label_location);
            //}
            // END: label position add class
            
            if (((useLegends && $this.alwaysLabel) || !useLegends) && label_location === "upper") {
                var $line = $labelWrapper.find('.pbe-line-indicator > div'),
                    lineTopCount;

                if (isSingleBar) {
                    lineTopCount = $jsChart.find('.pbe-label-bottom-group .pbe-label-wrapper.upper:not(.legend-only)').length + transactions.length;
                } else if (label_location === "upper") {
                    lineTopCount = $jsChart.find('.pbe-label-top-group .pbe-label-wrapper:not(.legend-only), .pbe-chart-transac').length + transactions.length;  
                }

                $line.css('min-height', $line.height() * lineTopCount);
            }

            itemcount--;
            
            // adding sr-only for chart
            $.each(transactions, function () {
                var chargeItemsStartDate = new Date($this.startDate),
                    $transaction = this,
                    transactionDate = new Date($transaction.date);
                
                if (chargeItemsStartDate.toDateString() === transactionDate.toDateString()) {
                    $srLabelsContainer.append('<div>' + $transaction.label + " " + $transaction.date_label + '</div>');
                    return;
                }
            });

            var srLegend = "";
            if (legendOnly !== '') {
                srLegend = "Legend: " + $this.legend + " - ";
            }
            
            barSubLabel = (barSubLabel === undefined) ? "" : " - " + barSubLabel;
            $srLabelsContainer.append('<div>' + srLegend + barLabel + barSubLabel + $srDateRange + '</div>');
        });
        
        //START transactions/divider
        $.each(transactions, function (index) {
            var $this = this,
                $transContainer = $pbeChartContainer.find('.pbe-chart-transacs'),
                $trans,
                transLabel = $this.label,
                transDate,
                transDateLabel = $this.date_label,
                posDays,
                pPosDays,
                barHeight = $pbeChartContainer.find('.pbe-chart-bars-below').height(),
                heightOffset = 14,
                height = barHeight + heightOffset,
                marginBottom = parseInt($pbeChartContainer.find('.pbe-chart-bars-upper').css('marginBottom')),
                bottom = 0,
                isTop = false,
                isBot = false,
                grey = '',
                isPrev = $pbeChartContainer.hasClass('pbe-chart-prev');

            $this.date = new Date($this.date);
            transDate = new Date($this.date);
            transDate = transDate.toDateString();
            
            isTop = chargeItems.find(x => x.startDate.getTime() == $this.date.getTime() && x.position === "upper") !== undefined ? true : isTop;
            isBot = chargeItems.find(x => x.startDate.getTime() == $this.date.getTime() && (x.position === "below" || x.position === "current")) !== undefined ? true : isBot;

            //height computation
            height = (isTop && isBot) ? (transcount > 1) ? (height + heightOffset + marginBottom) * transcount : (height + heightOffset + marginBottom + (barHeight / 2)) : height * transcount;
            bottom = (isTop && !isBot) ? barHeight + marginBottom : bottom;

            posDays = Math.round(Math.abs(($this.date - startDate) / oneDay));
            pPosDays = (posDays * 100) / totalDays;

            $trans = $("<div>", { "class": "pbe-chart-transac" + grey, "style": "height: " + height + "px;left:" + pPosDays + "%;bottom: " + bottom + "px;", "data-transId": "trans-id-" + transcount});
            $trans.data('is-both', isTop && isBot);
            $transContainer.append($trans);
            
            // START: PBE labels for transaction (divider)
            var $labelContainer = ($pbeLabelsContainer.find('.pbe-label-top-group').length > 0) ? $pbeLabelsContainer.find('.pbe-label-top-group') : $pbeLabelsContainer.find('.pbe-label-bottom-group'),
                $transDivider,
                $transDescription = "<div class='surtitle-black'>" + transLabel + "</div>",
                $transDate = "<div class='small-text margin-t-4'>" + transDateLabel  + "</div>";


            $transDivider = $("<div>", {
                "class": "pbe-label-divider middle-position",
                "style": "left:" + pPosDays + "%",
                "data-label-transid": "trans-id-" + transcount
            });
            $transDivider.append($("<div class='pbe-label'>" + $transDescription + $transDate + "</div>"));
            $transDivider.data('horizontal-position','middle-position');
            $labelContainer.append($transDivider);
            transcount--;
        });
        
        getPbePosition($jsChart);
        computeLineIndicatorHeight($jsChart);
        iterateDividerLabels($jsChart);
    });
}

//reposition the bar location depending on the top or bottom label height
function getPbePosition($chart) {
    var $barUpper = $chart.find('.pbe-chart-bars-upper'),
        $barBelow = $chart.find('.pbe-chart-bars-below'),
        $pbeContainer = $chart.find('.pbe-chart-container'),
        isSingleBar = $pbeContainer.hasClass('single-bar'),
        isPrev = $pbeContainer.hasClass('pbe-chart-prev'),
        barUpperMarginBottom;
    
    //START compute transaction max height and set it as min-height for pbe label group
    var $labelContainer = ($barUpper.length > 0) ? $chart.find('.pbe-label-top-group') : $chart.find('.pbe-label-bottom-group'),
        barHeight = ($barUpper.length > 0) ? $barUpper.height() : $barBelow.height(),
        $trans = $pbeContainer.find('.pbe-chart-transacs .pbe-chart-transac'),
        //splitChart = $labelBotContainer.closest('.pbe-js-chart').data('split-chart'),
        //chargeItems = splitChart.chargeItems,
        transMaxHeight = 0;
    
    $.each($trans, function () {
        var $this = $(this),
            height = $this.height(),
            label = $labelContainer.find('.pbe-label-divider[data-label-transid="' + $this.data('transid') + '"]').height(),
            spaceBetween = 15,
            isBoth = $this.data('is-both'),
            currentTransHeight = (isBoth) ? (height - barHeight) + label + spaceBetween : height + label + spaceBetween;

        transMaxHeight = (transMaxHeight > currentTransHeight) ? transMaxHeight : currentTransHeight;
    });

    if ($barUpper.length > 0 || (isSingleBar && $barBelow.length > 0)) {
        labelGroupSelector = $barUpper.length > 0 ? '.pbe-label-top-group .pbe-label-wrapper' : '.pbe-label-bottom-group .pbe-label-wrapper.upper';
        $.each($chart.find(labelGroupSelector), function () {
            var $this = $(this),
                height = $this.height(),
                currentLabelHeight = height + 15;
            
            transMaxHeight = (transMaxHeight > currentLabelHeight) ? transMaxHeight : currentLabelHeight;
        });
    }
    
    $labelContainer.css('min-height', transMaxHeight);
    //END compute transaction max height and set it as min-height for pbe label group

    //bar location - top
    if ($barUpper.length > 0) {
        positionPbeBar($barUpper, $chart.find('.pbe-label-top-group'), 'upper');
        barUpperMarginBottom = parseInt($chart.find('.pbe-chart-bars-upper').css('margin-bottom'));
    }

    //bar location - bottom
    if ($barBelow.length > 0) {
        var $siblingChartContainer,
            $labelBotContainer = $chart.find('.pbe-label-bottom-group');


        if (isSingleBar && !isPrev) { 
            $singleChartContainer = $barBelow.closest('.pbe-chart-container');
            $siblingChartContainer = $chart.closest('.pbe-chart').find('.pbe-chart-container.pbe-chart-prev');
            $legendOnly = $labelBotContainer.find('.pbe-label-wrapper.legend-only');

            if ($siblingChartContainer.length > 0) {

                if ($siblingChartContainer.hasClass('single-bar')) {
                    $labelBotContainer.css({
                        'min-height': $siblingChartContainer.siblings('.pbe-labels-container').find('.pbe-label-bottom-group').height()
                    });
                } else {
                    $labelBotContainer.css({
                        'min-height': $siblingChartContainer.siblings('.pbe-labels-container').find('.pbe-label-top-group').height()
                    });
                }
                
            }
            
            $barBelow.css('top', $siblingChartContainer.find('.pbe-chart-bars-below').css('top'));
        }
        
        $labelBotContainer.find('.pbe-label-wrapper.below').css("display", "none");
        if (isSingleBar) {
            $labelBotContainer.find('.pbe-label-wrapper.upper').css("display", "none");
        }
        
        positionPbeBar($barBelow, $labelBotContainer, 'below', barUpperMarginBottom);
    }

    function positionPbeBar($bar, $labelWrapper, position, margin_bottom) {
        var barHeight = $bar.height(),
            labelHeight = $labelWrapper.height(),
            $pbeLabelsContainer = $labelWrapper.closest('.pbe-labels-container'),
            $graphContainer = $pbeLabelsContainer.siblings('.pbe-chart-container'),
            isSingleBar = $graphContainer.hasClass('single-bar'),
            isPrev = $graphContainer.hasClass('pbe-chart-prev'),
            //topPosition = (isSingleBar && isPrev) ? labelHeight - (barHeight * 2) : labelHeight - barHeight;
            topPosition = labelHeight - barHeight;

        // additional computation for bottom label container if the upper bar is present
        if (margin_bottom !== undefined) {
            inlineStyle = { 
                'min-height': barHeight + margin_bottom,
                'top': margin_bottom + "px"
            };
            $labelWrapper.css(inlineStyle);
            
            topPosition = $labelWrapper.offset().top - $bar.offset().top; // computation for pbe-chart-bars-below
            barBottomPosition = topPosition;
        }
        
        if (topPosition <= 0 || labelHeight === undefined) { return; }
        if (isSingleBar && !isPrev) { return; }
        $bar.css('top', topPosition);
    }

    //transaction - recompute its location
    if ($chart.find('.pbe-chart-transacs').length > 0) {
        var $trans = $chart.find('.pbe-chart-transacs'),
            transOffset = $trans.offset().top,
            barBelowOffset = $barBelow.offset().top,
            barBelowHeight = $barBelow.height();
        
        $trans.css('bottom', (transOffset - barBelowOffset - barBelowHeight));
    }
    
    $chart.find('.pbe-label-wrapper').css("display", "");

    //transaction label mapping
    if ($chart.find('.pbe-chart-transac').length > 0) {
        var $chargeLabel,
            totalChargeLabelHeight = 0;
        
        $.each($chart.find('.pbe-chart-transac'), function() {
            var $this = $(this),
                dataTransID = $this.data('transid'),
                $divider = $chart.find('.pbe-label-divider[data-label-transid=' + dataTransID + ']'),
                divStyle;
            
            //NOTE: if charge label is present need to compute total height of all charge labels and deduct to transaction label offsets
            if($chargeLabel === undefined) {
                $chargeLabel = $divider.prevAll('.pbe-label-wrapper:not(.legend-only)');
                $.each($chargeLabel, function() {
                    totalChargeLabelHeight += $(this).height();
                });
            }

            var $parent = $this.parent(),
                parentRightOffset = $parent.offset().left + $parent.width(),
                dividerLabelWidth = $divider.find('.surtitle-black').width(),
                dividerRightOffset = $divider.offset().left + dividerLabelWidth,
                isTextAlignRight = (parentRightOffset <= dividerRightOffset) ? true : false,
                divChangeLeftVal;

            $divider.data({
                'parent-offset-right': parentRightOffset,
                'parent-offset-left': $parent.offset().left,
                'trans-offset-left': $divider.offset().left,
                'trans-offset-right': dividerRightOffset
            });
            
            // condition for checking if text or label on transaction will be aligned on right side when the available space is not enough
            if (isTextAlignRight) {
                if (divChangeLeftVal === undefined) {
                    divChangeLeftVal = parseFloat($divider.css('left')) - dividerLabelWidth;
                    divChangeLeftVal = ((divChangeLeftVal / $parent.width()) * 100) + "%";
                }

                divStyle = {
                    "position": "absolute",
                    "top": mapLabelTransaction($this, $divider, totalChargeLabelHeight) - 10,
                    "left": divChangeLeftVal,
                    "text-align": "right"
                }
            } else {
                divStyle = {
                    "position": "absolute",
                    "top": mapLabelTransaction($this, $divider, totalChargeLabelHeight) - 10
                }
            }
            
            $divider.css(divStyle);
        });
    }
}

//compute the line indicators height per label (legends excluded)
function computeLineIndicatorHeight($chart) {
    var $lineIndicator = $chart.find('.pbe-line-indicator:visible'),
        lineTopCount = $chart.find('.pbe-label-top-group .pbe-label-wrapper:not(.legend-only), .pbe-chart-transac').length,
        lineBotCount = $chart.find('.pbe-label-bottom-group .pbe-label-wrapper:not(.legend-only)').length,
        prevMaxHeight;
    
    $.each($lineIndicator, function () {
        var $this = $(this),
            $line = $this.find('div'),
            $chartContainer = $this.closest('.pbe-js-chart'),
            $barChart,
            $labelWrapper = $this.closest('.pbe-label-wrapper'),
            parentLabelWrapperPosition,
            posVal = 0,
            isSingleBar = $chart.find('.pbe-chart-container').hasClass('single-bar');
        
        if ($line.closest('.pbe-label-top-group').length > 0) {
            lineTopCount--;
        } else {
            if (!isSingleBar || $labelWrapper.hasClass('below')) {
                $line.css('min-height', $line.height() * lineBotCount);
            } 
            lineBotCount--;
        }

        $barChart = $chartContainer.find('.pbe-chart-container');
        parentLabelWrapperPosition = ($this.closest('.pbe-label-top-group').length > 0) ? "top" : "bottom";
        labelOffsetTop = $labelWrapper.offset().top;
        isSingleBar = ($this.closest('.pbe-labels-container').prev('.pbe-chart-container.single-bar').length > 0) ? true : false;

        posVal = positionLineIndicator($labelWrapper, $barChart, parentLabelWrapperPosition, isSingleBar);
        $labelWrapper.css('top', posVal);

        if (parentLabelWrapperPosition === 'bottom' && isSingleBar === false) {
            prevMaxHeight = getMaxHeight(prevMaxHeight, $labelWrapper.height(), ($barChart.height() / 2));
            $this.closest('.pbe-label-bottom-group').css('min-height', prevMaxHeight);
        }

        checkOverlapText($labelWrapper);
    });
}

function iterateDividerLabels($chart) {
    var labelsTop = $chart.find('.pbe-label-top-group .pbe-label-divider'),
        $labelsTopFirst = labelsTop.first(),
        $chargeLabelRight = $chart.find('.pbe-label-top-group .pbe-label-wrapper.right-position:not(.legend-only)'),
        firstOffsetRight,
        labelsBot = $chart.find('.pbe-label-bottom-group .pbe-label-divider'),
        isOverlapping = false,
        isCropped = false,
        isOverlapChargeLabel = false;

    if (labelsTop.length > 0) {
        if ($labelsTopFirst.length > 0) {
            firstOffsetRight = $labelsTopFirst.offset().left + $labelsTopFirst.width();
            $parent = $labelsTopFirst.closest('.pbe-label-top-group');
        }
        
        $.each(labelsTop, function () {
            var $this = $(this),
                $trans,
                $transLabel,
                transHeight,
                $transPrev,
                $transLabelPrev;
            
            checkOverlapText($this);
            $this.addClass('text-wrap');

            // START: additional checking, if transaction labels overlaps with charge item label on the right side change inline style
            if ($chargeLabelRight.length > 0) {
                isOverlapChargeLabel = ($chargeLabelRight.find('.pbe-line-indicator').offset().left <= ($this.offset().left + $this.width())) ? true : false;
                if (isOverlapChargeLabel) {
                    $this.css({
                        'left': parseInt($this.css('left')) - $this.width(),
                        'text-align': "right"
                    })
                }
            }
            // END: additional checking, if transaction labels overlaps with charge item label on the right side change inline style

            if (!$labelsTopFirst.is($this)) {
                isOverlapping = (firstOffsetRight >= $this.offset().left) ? true : false;
                isCropped = ($this.data('parent-offset-right') <= $this.data('trans-offset-right')) ? true : false;


                if (isOverlapping && isCropped) {
                    $trans = $chart.find('.pbe-chart-transac[data-transid="' + $this.data('label-transid') + '"]');
                    $transPrev = $trans.prev();
                    transHeight = $trans.height();

                    if ($transPrev.length > 0) {
                        // change height of previous transaction blue bar
                        $transPrev.css({
                            'height': ($transPrev.height() - transHeight)
                        });

                        // adjust transaction label based on the reduce height of the previous transaction bar height
                        $transLabel = $chart.find('.pbe-label-divider[data-label-transid="' + $trans.data('transid') + '"]');
                        $transLabelPrev = $chart.find('.pbe-label-divider[data-label-transid="' + $transPrev.data('transid') + '"]');
                        $transLabelPrev.css('top', parseFloat($transLabelPrev.css('top')) + transHeight);

                        if (($transLabel.data('trans-offset-left') <= $transLabelPrev.data('trans-offset-left'))) {
                            if ($transLabelPrev.data('location-changed') === undefined) {
                                $transLabelPrev.css({
                                    'left': parseInt($transLabelPrev.css('left')) - $transLabelPrev.width(),
                                    'text-align': "right"
                                });

                                checkTransactionLeftEdgeCropText($transLabelPrev);
                            }
                        } else if (($transLabel.offset().left <= $transLabelPrev.data('trans-offset-left')) && ($transPrev.offset().left <= $transLabelPrev.offset().left)) {
                            if ($transLabelPrev.data('location-changed') === undefined) {
                                $transLabelPrev.css({
                                    'left': parseInt($transLabelPrev.css('left')) - $transLabelPrev.width(),
                                    'text-align': "right"
                                });

                                checkTransactionLeftEdgeCropText($transLabelPrev);
                            }
                        }
                    }

                    //transaction label that overlaps or being cropped
                    $trans.css(
                        { "height": (transHeight * 2) }
                    );

                    $this.css({
                        "top": parseFloat($this.css('top')) - transHeight
                    });
                }
            } else if ($this.data('parent-offset-left') >= $this.offset().left) {
                checkTransactionLeftEdgeCropText($this);
            }
        });

        function checkTransactionLeftEdgeCropText($el) {
            var excess = ($el.data('parent-offset-left') - $el.offset().left);

            if (excess > 0) {
                $el.css({
                    'background-color': "#F4F4F4",
                    'width': ($el.width() - excess),
                    'left': parseInt($el.css('left')) + excess + 2
                }).addClass('is-cropped');

            }

            $el.data('location-changed', true);
        }
    }

    if (labelsBot.length > 0) {
        $.each(labelsBot, function () {
            checkOverlapText($(this));
        });
    }
}

function checkOverlapText($curLabel) {
    var $parent = $curLabel.parent(),
        bartype = ($curLabel.hasClass('pbe-label-wrapper')) ? 'data-bar-id' : 'data-transid',
        curDataLabelID = ($curLabel.hasClass('pbe-label-wrapper')) ? $curLabel.data('label-bar-id') : $curLabel.data('label-transid'),
        curHeight = $curLabel.height(),
        curOffsetLeft = $curLabel.offset().left,
        curOffsetTop = $curLabel.offset().top;
    
    // START: get current label width
    getLabelWidth($curLabel);
    curOffsetRight = curOffsetLeft + $curLabel.data('label-width');
    curOffsetBottom = curOffsetTop + curHeight;
    // END: get current label width
    
    // START: check if label horizontal (left / middle / right) position
    var $correspondingBar = $curLabel.closest('.pbe-js-chart').find("[" + bartype + "='" + curDataLabelID + "']"),
        $parentBar = $correspondingBar.parent(),
        isSingleBar = $parentBar.parent().hasClass('single-bar'),
        labelparentcount,
        curclassname = $curLabel.data('horizontal-position');
    
    // get label parent count and add to condition in determining the right position when top bar has only one charge item
    labelparentcount = $curLabel.closest('.pbe-labels-container').children().length;
    
    // END: check if label horizontal (left / middle / right) position

    $.each($parent.children(':not(.legend-only)'), function (e) {
        var $this = $(this),
            dataLabelID = ($this.hasClass('pbe-label-wrapper')) ? $this.data('label-bar-id') : $this.data('label-transid'),
            labelheight = $this.height(),
            labelwidth,
            offsetLeft = $this.offset().left,
            offsetTop = $this.offset().top,
            offsetRight,
            offsetBottom,
            leftPos = 0,
            rightPos = "auto",
            rightLabelSpacing = 30,
            parentOffsetRight = $parent.offset().left + $parent.width(),
            barWidth = $correspondingBar.width() - ($curLabel.closest('.pbe-js-chart').find('.pbe-chart-transac').width() / 2);
        
        if (barWidth <= 30) {
            if (curclassname === "left-position") {
                $curLabel.find('.pbe-line-indicator, .pbe-label').css('margin-left', (barWidth / 2) - 1);
            } else if (curclassname === "right-position") {
                rightLabelSpacing = (barWidth / 2) + parseInt($curLabel.find('.pbe-line-indicator, .pbe-label').css('margin-left'));
            }
        }

        if (curDataLabelID !== dataLabelID) {
            getLabelWidth($this);
            labelwidth = $this.data('label-width');
            offsetRight = offsetLeft + labelwidth;
            offsetBottom = offsetTop + labelheight;
            
            if (curOffsetLeft <= offsetRight && curOffsetTop <= offsetBottom) {
                if (isSingleBar) { return; } // container for bars - if single bar does not continue right position computation
                
                leftPos = (parseFloat($curLabel.css('left')) + labelwidth);
            }
        } else {
            // check if current label is cropped
            var actualLabelOffsetRight = parseInt($curLabel.find('.pbe-label').offset().left + $curLabel.data('label-width'));
            
            if (parentOffsetRight < actualLabelOffsetRight && curclassname === "middle-position" && isSingleBar) {
                rightPos = $curLabel.data('label-width');
                $curLabel.find('.pbe-label').css({ "position": "relative", "right": rightPos }); // for label to appear as text-align: right
                $curLabel.css("text-align", "right");
            } else if (parentOffsetRight < actualLabelOffsetRight || (curclassname === "right-position" && !isSingleBar)) {

                leftPos = ($parent.width() - rightLabelSpacing);
                rightPos = $curLabel.data('label-width');
                $curLabel.find('.pbe-label').css({ "position": "relative", "right": rightPos }); // for label to appear as text-align: right
                $curLabel.css({ 'left': leftPos });
            } else if (curclassname === "middle-position" && !isSingleBar) {
                leftPos = $correspondingBar.width() / 2;

                $curLabel.css('left', parseInt($curLabel.css('left')) + leftPos);
            } 
        }
    });
}

function mapLabelTransaction($transaction, $transLabel, totalChargeLabelHeight) {
    var transactionOffset,
        transLabelOffset,
        transLabelHeight;
    if (totalChargeLabelHeight === undefined) { totalChargeLabelHeight = 0; }
    if ($transaction.length === 0 || $transLabel.length === 0) { return; }
    transactionOffset = $transaction.offset().top;
    transLabelOffset = $transLabel.offset().top;
    transLabelHeight = $transLabel.height();

    return transactionOffset - transLabelOffset - transLabelHeight + totalChargeLabelHeight;
}

function getMaxHeight(prev, current, elemheight) {
    var height;
    if (elemheight === undefined) { elemheight = 0 }
    if (prev === undefined) { height = current }
    if (prev >= current) { height = prev }
    if (prev < current) { height = current}
    return height + elemheight; //additional 5px as allowance to prevent cropping of text or label
}

function getLabelWidth($el) {
    var dataLabelWidth = $el.data('label-width');

    if (dataLabelWidth === undefined) {
        dataLabelWidth = 0
    }

    $.each($el.find('.pbe-label').children(), function () {
        var $this = $(this);
        $this.addClass('d-inline-flex');
        if (dataLabelWidth <= $this.width()) {
            dataLabelWidth = $this.width();
        }
        $this.removeClass('d-inline-flex');
    })
    $el.data('label-width', dataLabelWidth);
}

function positionLineIndicator($labelWrapper, $chartContainer, labelPosition, isSingleBar) {
    var $bar,
        $dataBarId,
        $parentLabelWrapper,
        barOffsetBottom,
        labelOffsetBottom,
        positionValue = 0,
        isLabelLocationPresent = (!$labelWrapper.hasClass('upper') && !$labelWrapper.hasClass('below')) ? false : true,
        labelLocation,
        labelDataBarId = $labelWrapper.data('label-bar-id'),
        currentContainerHeight,
        finalContainerHeight,
        isPrev = $chartContainer.hasClass('pbe-chart-prev');
    
    $bar = (labelPosition === 'top') ? $chartContainer.find('.pbe-chart-bars-upper') : $chartContainer.find('.pbe-chart-bars-below');
    barOffsetBottom = $bar.offset().top + $bar.height();
    labelOffsetBottom = $labelWrapper.offset().top + $labelWrapper.height();
    isSingleBar = (isSingleBar === undefined) ? false : isSingleBar;
    isPrev = $chartContainer.hasClass('pbe-chart-prev');
    labelLocation = ($labelWrapper.hasClass('upper')) ? "top" : "bottom";
    
    if((isLabelLocationPresent && labelPosition !== labelLocation) || (isLabelLocationPresent === true && isSingleBar === true)) {
        if (labelPosition === "bottom" && labelLocation === "top") {
            positionValue = barOffsetBottom - labelOffsetBottom - ($bar.height()/2);
        } else if ((labelPosition === "top" && labelLocation === "bottom") || (isSingleBar === true && labelPosition ===  labelLocation)) {
            positionValue = $bar.offset().top - $labelWrapper.offset().top + ($bar.height()/2);
        }

        $parentLabelWrapper = $labelWrapper.parent();
        $dataBarId = $bar.find('.pbe-chart-bar[data-bar-id="' + labelDataBarId + '"]');
        $pbeLabelContainer = $parentLabelWrapper.closest('.pbe-labels-container');
        
        // adjust height of label container when condition below is met
        if (isSingleBar === true && labelPosition === labelLocation) {

            if ($labelWrapper.find('.pbe-label').width() < $dataBarId.width()) {
                $labelWrapper.css('width', "calc(100% - " + parseFloat($labelWrapper.css('left')) + "px)");
            } else if (!$dataBarId.is(':first-child') && !$dataBarId.is(':last-child')) {
                var labelActualWidth = 0,
                    getLabelActualWidth = function ($el) {
                        $el.addClass('d-inline-flex');
                        $.each($el.children(), function() {
                            labelActualWidth = (labelActualWidth > $(this).width()) ? labelActualWidth : $(this).width();
                        });
                        $el.removeClass('d-inline-flex').css('width', labelActualWidth);
                    }
                getLabelActualWidth($labelWrapper.find('.pbe-label'));

                $labelWrapper.css({
                    "width": $dataBarId.css('width'),
                    'left': parseFloat($dataBarId.css('left')) + ($dataBarId.width() / 2) - parseInt($labelWrapper.find('.pbe-line-indicator').css('margin-left'))
                });
            } else {
                $labelWrapper.css({
                    'align-items': "flex-start",
                    "width": "auto"
                });
            }
            
            pbeLabelContainerOffsetBottom = $pbeLabelContainer.offset().top + $pbeLabelContainer.outerHeight();

            $upperLabelWrapper = $parentLabelWrapper.find('.pbe-label-wrapper.upper');

            
            if ($parentLabelWrapper.data('original-height') === undefined) {
                $parentLabelWrapper.data('original-height', parseInt($parentLabelWrapper.css('min-height')));
            }
            
            currentContainerHeight =  $labelWrapper.height();
            
            if ($parentLabelWrapper.data('label-max-height') === undefined) {
                $parentLabelWrapper.data('label-max-height', currentContainerHeight);
                finalContainerHeight = currentContainerHeight;
            } else {
                dataMaxHeight = $parentLabelWrapper.data('label-max-height');
                finalContainerHeight = (dataMaxHeight > currentContainerHeight ? dataMaxHeight : currentContainerHeight);
                $parentLabelWrapper.data('label-max-height', finalContainerHeight);
            }

            $whiteBg = $pbeLabelContainer.find('.pbe-labels-white-bg').length > 0 ? $pbeLabelContainer.find('.pbe-labels-white-bg') : $("<div>", { "class": "bgWhite pbe-labels-white-bg" });
            
            if (isPrev) {
                $parentLabelWrapper.css('height', finalContainerHeight + $parentLabelWrapper.data('original-height'));
            }
            
            $whiteBg.css({
                "height": (($pbeLabelContainer.offset().top + $pbeLabelContainer.height()) - barOffsetBottom)
            });

            $pbeLabelContainer.prepend($whiteBg);
        } else if (isSingleBar === true && labelPosition !== labelLocation) {
            $labelWrapper.css({
                "width": $dataBarId.css('width'),
                "left": $dataBarId.css('left')
            });
        }

        return positionValue;
    }

    // retaining original computation for label positioning before introducing additional parameter to base labels upper/below location
    if (!isLabelLocationPresent || (labelPosition === labelLocation)) {
        if (labelPosition === 'bottom' && isSingleBar === false) {
            positionValue = barOffsetBottom - $labelWrapper.offset().top - ($bar.height()/2);
        } else {
            positionValue = barOffsetBottom - labelOffsetBottom - ($bar.height()/2);
        }

        return positionValue;
    }
}

$(document).ready(function () {
    var rsztimeout;

    pbeSplitChartRemade();

    $(window).resize(function() {
        var $jsCharts = $('.pbe-js-chart');

        $jsCharts.find('.pbe-chart-container > div').remove();
        $jsCharts.find('.pbe-labels-container > div').remove();
        clearTimeout(rsztimeout);

        rsztimeout = setTimeout(function() {
            pbeSplitChartRemade();
        }, 200);
    });
});