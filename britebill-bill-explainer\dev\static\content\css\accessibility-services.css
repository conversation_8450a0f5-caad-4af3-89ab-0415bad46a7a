﻿/* MOBILE FIRST */

/* START Helper Class */
.min-width-280 {
    min-width: 280px
}
.dimension-60 {
    height: 60px;
    width: 60px
}
.borderRadiusAll4 {
    border-radius: 4px;
}
.link-text:hover{text-decoration:underline;}
.txtSize36{font-size: 36px;}

.list-style-inside {
    list-style-position: inside;
}

ul.list-type-dash {
    padding-left: 20px;
    list-style: none; /* Remove list bullets */
}

ul.list-type-dash li::before {
    content: "-";
    position: absolute;
    font-weight: bold;
    margin-left: -20px;
}
/* END Helper Class */

/* START Custom Class */

/*START override headter tab control*/
.tab-control.tab-control-accessibility-services .header-tab-control ul li:not(:last-child), .tablist-underlined [role=tab]:not(:last-child) {
    margin-right: 15px;
}
/*END override headter tab control*/

/*START Override Tab Vertical */

.side-tab-control ul.tabs {
    border-top: 1px solid #e1e1e1;
    border-bottom: 1px solid #e1e1e1;
}
.side-tab-control ul.tabs.tabs_vertical li:before{
    color: #00549A;
    font-size: 13px;
}
.side-tab-control ul.tabs.tabs_vertical li.active_tabs:before {
    display: none;
}
.side-tab-control ul.tabs li {
    border: none;
    padding: 0;
    background: #F4F4F4;
}
.side-tab-control ul.tabs li.active_tabs {
    border-left: 1px solid #e1e1e1;
    box-sizing: border-box;
    background: #FFFFFF;
    box-shadow: 0 6px 25px 0 rgba(0,0,0,0.12)
}
.side-tab-control ul.tabs.tabs_vertical li.active_tabs a {
    color: #111111;
}
.side-tab-control ul.tabs.tabs_vertical li {
    color: #00549A;
    font-family: Arial;
    font-size: 16px;
    letter-spacing: 0;
    line-height: 18px;
    border-bottom: 1px solid #e1e1e1;
    padding: 5px;
}
.side-tab-control ul.tabs.tabs_vertical li:last-child{
    border-bottom: none;
}

.side-tab-control ul.tabs.tabs_vertical li a {
    color: #00549A;
    text-decoration: none;
    padding: 15px 35px 15px 15px;
    display: block;
}

.side-tab-control ul.tabs.tabs_vertical li:hover {
    background: #fff;
}

.side-tab-control select.custom-selection {
    background: #f4f4f4;
    color: #00549A;
    font-size: 14px;
    height: 44px;
}

.side-tab-control .card-body {
    padding: 0px;
}

/* ie overrides */
.side-tab-control select::-ms-value{
    background: #f4f4f4;
    color: #00549A;
}

.side-tab-control option.tab_selection{
    background: #f4f4f4;
    color: #111;
}

/*END Override Tab Vertical */

/* START more ways override */
    .more-ways-to-shop h2 {
        margin-bottom: 15px;
    }
    .more-ways-to-shop .container {
        padding-top: 45px;
        padding-bottom: 45px;
    }
    .more-ways-to-shop .content-ways-to-shop > li > div .anchor-icon {
        flex-shrink: 0;
        height: 60px;
        width: 60px;
    }
    .more-ways-to-shop .content-ways-to-shop > li > div .anchor-icon:before {
        font-size: 60px;
    }
/* END more ways override */

/* START OF Nav Breadcrumbs Component*/
.nav-breadcrumbs:not(.dots) li a span:not(:first-child):last-child{
    display: none;
}

.nav-breadcrumbs.dots li a span:not(:first-child):last-child {
    display: inline;
}
.nav-breadcrumbs.dots li a span:not(:last-child):first-child {
    display: none;
}

.nav-breadcrumbs li {
    display: inline;
}

    .nav-breadcrumbs li a.breadcrumbs-active {
        pointer-events: none;
        cursor: default;
    }

        .nav-breadcrumbs li a.breadcrumbs-active:hover{
            text-decoration: none;
        }

        .nav-breadcrumbs li {
            display: none;
        }

    .nav-breadcrumbs li:nth-last-child(2) {
        display: inline-block;
    }
/* END OF Nav Breadcrumbs Component*/

/*START bold numbering on ordered list*/
ol.ol-font-weight {
    counter-reset: item;
    padding-left: 0px;
}
ol.ol-font-weight > li {
    list-style-type: none;
    counter-increment: item;
}
ol.ol-font-weight > li.list-item-bold:before {
    font-weight: bold;
    content: counter(item) ".";
}
ol.ol-font-weight > li:before {
    font-weight: normal;
    content: counter(item) ".";
}
/*END bold numbering on ordered list*/

/*START of FOCUS OUTLINE*/
.focus_outline_gray .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element,
body.is_tabbing .focus_outline_gray *:focus {
    outline: none !important;
    box-shadow: 0 0 0px 3px #f4f4f4, 0 0 2px 3px #f4f4f4, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}
/*END of FOCUS OUTLINE*/

/*START of FOCUS ANCHOR TAG in ACCORDION*/
.accordionContainer a[aria-expanded=true] span:not(.icon) {
    font-size: 14px;
    color: #111111;
}
/*END of FOCUS ANCHOR TAG in ACCORDION*/

/* END Custom Class */
@media (min-width: 768px) {
    /* START Helper Class */
    .txtSize36-sm{font-size: 36px}
    .text-sm-normal-2{font-weight: normal}
    /* END Helper Class */

    /* START Custom Class */
    /*START override headter tab control*/
    .tab-control.tab-control-accessibility-services .header-tab-control ul li:not(:last-child), .tablist-underlined [role=tab]:not(:last-child) {
        margin-right: 20px;
    }
    /*END override headter tab control*/
    .nav-breadcrumbs li {
        display: inline-block;
    }
    /* START more ways override */
    .more-ways-to-shop .content-ways-to-shop > li {
        max-width: 218px;
    }
    .more-ways-to-shop .content-ways-to-shop > li > div .anchor-icon {
        height: 74px;
        width: 74px;
        margin-bottom: 15px;
    }
    /* END more ways override */

    /* START Button-link-2 Class */
    .button-link-2 {
        border-radius: 20px;
        font-size: 15px;
        height: 35px;
        line-height: 17px;
        text-align: center;
        cursor: pointer;
        padding: 7px 28px;
        white-space: nowrap;
        color: #fff;
        background-color: #003778;
        border: 2px solid #003778;
        margin-left: auto;
    }

        .button-link-2:hover, .button-link-2:focus {
            color: #fff;
            background-color: #00549a;
            border-color: #00549a;
        }

        .button-link-2:focus {
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        a:hover.button-link-2 > .anchor-text,
        a:focus.button-link-2 > .anchor-text{
            text-decoration: none;
        }
    /* END Button-link-2 Class */

    /* END Custom Class */
}

@media (min-width: 992px) {
    /* START Helper Class */
    /* END Helper Class */

    /* START Custom Class */
    /* START more ways override */
    .more-ways-to-shop .small-text {
        font-size: 14px;
        line-height: 18px;
    }
    /* END more ways override */
    /* END Custom Class */
}

@media (min-width: 1240px) {
    /* START Helper Class */
    /* END Helper Class */

    /* START Custom Class */
    /* END Custom Class */
}

@media (min-width: 1440px) {
    /* START Helper Class */
    /* END Helper Class */

    /* START Custom Class */
    /* END Custom Class */
}