// This is a custom JS file exclusively for iPhone pages

//Start iphone compare selector functions
$(function () {
    var previousIphone;
    $('.iphone-selector').on('focus', function () {
        previousIphone = $(this).val();
    })
    function selectIphone(selector) {
        var column = $('.' + selector.data('column'));
        var selectedIphone = selector.val();

        Object.keys(iphones).forEach(function (key) {
            var iphone = iphones[key];
            if (key === selectedIphone) {
                column.find('.iphone-image').html(iphone['iphone-image']); // set image
                column.find('.iphone-size').html(iphone['iphone-size']); //set size
                column.find('.iphone-display').html(iphone['iphone-display']); //set display
                column.find('.iphone-network-icon').html(iphone['iphone-network-icon']); //set network icon
                column.find('.iphone-network-info').html(iphone['iphone-network-info']); //set network info
                column.find('.iphone-camera-icon').html(iphone['iphone-camera-icon']); //set camera icon
                column.find('.iphone-battery-info').html(iphone['iphone-battery-info']); //set battery info

                column.find('.iphone-colors').html(iphone['iphone-colors'].map(function (color) {
                    return '<li class="apple-swatch" style="background-color:' + color.colorvalue + '"><span class="sr-only">' + color.colorname + '</span></li>'; // set swatches
                }));

                column.find('.iphone-camera-features').html(iphone['iphone-camera-features'].map(function (feature) {
                    if(feature == '<span class="text-bold">—</span>'){
                        return '<li class="margin-b-15 margin-b-lg-20" aria-hidden="true">' + feature + '</li>'; // set camera features with aria-hidden
                    }else{
                        return '<li class="margin-b-15 margin-b-lg-20">' + feature + '</li>'; // set camera features
                    }
                }));

                // show or hide buy link
                if (iphone['iphone-link']) {
                    column.find('.iphone-link').removeClass('d-none').attr('href', iphone['iphone-link']).attr('aria-label', 'Buy ' + key + ' now'); // set link and aria-label
                }
                else {
                    column.find('.iphone-link').addClass('d-none').attr('href', '#'); // set link
                }

                // show or hide zoom section and set zoom text
                if (iphone['iphone-zoom']) {
                    column.find('.iphone-zoom-container').addClass('d-block').removeClass('d-none'); // show zoom section
                    column.find('.iphone-zoom-none').addClass('d-none').removeClass('d-block'); // hide zoom none
                    column.find('.iphone-zoom').html(iphone['iphone-zoom']); //set zoom
                }
                else {
                    column.find('.iphone-zoom-container').addClass('d-none').removeClass('d-block'); // show zoom section
                    column.find('.iphone-zoom-none').addClass('d-block').removeClass('d-none'); // hide zoom none
                }

                // show or hide lidar section
                if (iphone['iphone-lidar']) {
                    column.find('.iphone-lidar-container').addClass('d-block').removeClass('d-none');
                    column.find('.iphone-lidar-none').addClass('d-none').removeClass('d-block');
                }
                else {
                    column.find('.iphone-lidar-container').addClass('d-none').removeClass('d-block');
                    column.find('.iphone-lidar-none').addClass('d-block').removeClass('d-none');
                }
            }
        });
    }
    $('.iphone-selector').on('change', function () {
        var selectedIphone = $(this).val();
        selectIphone($(this));
        var iphoneSelectors = $('.iphone-selector').not($(this));
        iphoneSelectors.each(function () {
            if ($(this).val() == selectedIphone) {
                $(this).val(previousIphone);
                selectIphone($(this));
                previousIphone = selectedIphone;
            }
        })
    });
});
// End iphone compare selector functions