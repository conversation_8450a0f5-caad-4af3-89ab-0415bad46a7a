/*START BELL ICON FONTS*/
@font-face {
    font-family: 'temporary-suspend-restore-icons';
    src: url(../fonts/temporary-suspend-restore-icons.eot?#iefix) format("embedded-opentype"),url(../fonts/temporary-suspend-restore-icons.woff) format("woff"),url(../fonts/temporary-suspend-restore-icons.ttf) format("truetype"),url(../fonts/temporary-suspend-restore-icons.svg) format("svg");
    font-weight: 400;
    font-style: normal
}

.icon-tsr {
    font-family: 'temporary-suspend-restore-icons';
    font-style: normal;
    speak: none;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

    .icon-tsr:before {
        font-family: 'temporary-suspend-restore-icons';
        position: static;
    }

.icon-tsr.icon-bell-logo:before {content: "\e600";}
.icon-tsr.icon-small_icon_checkmark_outline:before {content: "\e90a";}
.icon-tsr.icon-small_icon_select_trigger_half:before {content: "\e920";}
.icon-tsr.icon-checkmark_circled:before {content: "\e921";}
.icon-tsr.icon-external-link:before {content: "\eae1";}
.icon-tsr.icon-mobile:before {content: "\e9bf";}
.icon-tsr.icon-header-back:before {content: "\e900";}
.icon-tsr.icon-lost-phone:before {content: "\e901";}
.icon-tsr.icon-small_icon_collapse:before {content: "\e90b";}
.icon-tsr.icon-small_icon_expand:before {content: "\e90c";}
.icon-tsr.icon-close:before {content: "\eaa2";}
.icon-tsr.icon-exclamation-circled:before {content: "\e922";}
.icon-tsr.icon-Lost_stolen_phone:before {content: "\e902";}
.icon-tsr.icon-i:before {content: "\e60a";}
.icon-tsr.icon-Big_info:before {content: "\e96b";}
.icon-tsr.icon-small_icon_arrow_pill:before {content: "\e908";}