@font-face {
  font-family: 'bell-icon';
  src: url(../fonts/shop-icons.eot?#iefix) format('embedded-opentype'),
    url(../fonts/shop-icons.woff) format('woff'),
    url(../fonts/shop-icons.ttf) format('truetype'),
    url(../fonts/shop-icons.svg) format('svg');
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'bell-icon-outline';
  src: url(../fonts/shop-icons.eot?iw8dli);
  src: url(../fonts/shop-icons.eot?#iefixiw8dli) format('embedded-opentype'),
    url(../fonts/shop-icons.ttf?iw8dli) format('truetype'),
    url(../fonts/shop-icons.woff?iw8dli) format('woff'),
    url(../fonts/shop-icons.svg?iw8dli/shop-icons) format('svg');
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'bell-icon2';
  src: url(../fonts/shop-icons.eot?#iefix) format('embedded-opentype'),
    url(../fonts/shop-icons.woff) format('woff'),
    url(../fonts/shop-icons.ttf) format('truetype'),
    url(../fonts/shop-icons.svg) format('svg');
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'bell-icon3';
  src: url(../fonts/shop-icons.eot?#iefix) format('embedded-opentype'),
    url(../fonts/shop-icons.woff) format('woff'),
    url(../fonts/shop-icons.ttf) format('truetype'),
    url(../fonts/shop-icons.svg) format('svg');
  font-weight: 400;
  font-style: normal;
  font-display: block;
}
.icon,
.icon2,
.icon3 {
  font-family: 'bell-icon';
  font-style: normal;
  speak: none;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-o {
  font-style: normal;
  speak: none;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon:before,
.icon2:before {
  font-family: 'bell-icon';
  position: relative;
  top: 0px;
}
.icon3,
.icon3:before {
  font-family: 'bell-icon3';
  position: static;
}
.icon-o:before {
  font-family: 'bell-icon-outline';
}

/* START Global header and footer icons */
.icon-chevron:before,
.icon-chevron-up:before,
.icon-chevron-right:before,
.icon-chevron-down:before,
.icon-chevron-left:before {
  content: '\e012';
  display: inline-block;
}
.icon-chevron-up:before {
  -webkit-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  transform: rotate(-90deg);
  -webkit-transform-origin: 45% 40%;
  -ms-transform-origin: 45% 40%;
  transform-origin: 45% 40%;
}
.icon-chevron-down:before {
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}
.icon-chevron-left:before {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}
.icon-plus:before {
  content: '\e007';
  /* top: 3px !important; */
}
.icon-bell-logo:before {
  content: '\e600';
}
.icon-cart:before {
  content: '\e617';
  top: 3px !important;
}
.icon-silhouette:before {
  content: '\e616';
  top: 3px !important;
}
.icon-voice-search:before {
  content: '\e91f';
}
.icon-magnifying-glass:before {
  content: '\e615';
  top: 0 !important;
}
.icon-mobile-menu:before {
  content: '\e618';
}
.icon-close1:before {
  content: '\e624';
}
.icon-home:before {
  content: '\e61c';
}
.icon-chevron-bold:before {
  content: '\e61d';
}
.icon-o-handset:before {
  content: '\e610';
}
.icon-o-location:before {
  content: '\e599';
}
.icon-location-pin:before {
  content: '\e620';
}
.icon-search:before {
  content: '\e919';
}
.icon-facebook:before {
  content: '\e619';
}
.icon-twitter:before {
  content: '\e612';
}
.icon-blog-en:before {
  content: '\e90e';
}
.icon-linked-in-logo:before {
  content: '\e929';
}
.icon-youtube:before {
  content: '\e928';
}
.icon-back-to-top:before {
  content: '\e925';
}
/* END Global header and footer icons */
.icon-smart_touch_guide:before {
  content: '\e96e';
}
.icon-small_icon_arrow_pill:before {
  content: '\e908';
}
.icon-Android:before {
  content: '\e93c';
}
.icon-Apple:before {
  content: '\e939';
}
.icon-Chromecast:before {
  content: '\e937';
}
.icon-FireTV .path1:before {
  content: '\e935';
  color: #999;
}
.icon-FireTV .path2:before {
  content: '\e936';
  color: #231f20;
  margin-left: -1.861328125em;
}
.icon-android-TV .path1:before {
  content: '\e93a';
  color: #231f20;
}
.icon-android-TV .path2:before {
  content: '\e93b';
  color: #8a8b8a;
  margin-left: -6.375em;
}
.icon-AppleTV:before {
  content: '\e938';
}
.icon-12_search:before {
  content: '\e974';
}
.icon-close:before {
  content: '\eaa2';
}
.icon-wi_fi:before {
  content: '\e973';
}
.icon-data_descending:before {
  content: '\e972';
}
.icon-small_icon_expand:before {
  content: '\e90c';
}
.icon-small_icon_collapse:before {
  content: '\e90b';
}
.icon-o-locationNoPad:before {
  content: '\e934';
}
.icon-Big-expand:before {
  content: '\e949';
}
.icon-Big-collapse:before {
  content: '\e94a';
}
.icon-tvpay_perview:before {
  content: '\e947';
}
.icon-small_icon_select_trigger_half:before {
  content: '\e920';
}
.icon-small_icon_checkmark_outline:before {
  content: '\e90a';
}
.icon-Request_a_callback:before {
  content: '\e948';
}
.icon-small_log_in:before {
  content: '\e944';
}
.icon-tvpay_perview_bl:before {
  content: '\e951';
}
.icon-check-light:before {
  content: '\e603';
}
.icon-o-chat-bubble:before {
  content: '\e604';
}
.icon-play_hover_multi .path1:before {
  content: '\e97f';
  color: #000;
  opacity: 0.5978;
}
.icon-play_hover_multi .path2:before {
  content: '\e980';
  color: #fff;
  margin-left: -1em;
}
.icon-4k_tv:before {
  content: '\e945';
}
.icon-01-13_user_profile_bg:before {
  content: '\e91d';
}
.icon-payper_viewbl_bg:before {
  content: '\e977';
}
.icon-whole_home_pvr:before {
  content: '\e985';
}
.icon-restart:before {
  content: '\e988';
}
.icon-speed:before {
  content: '\e989';
}
.icon-upload:before {
  content: '\e98a';
}
.icon-07-07_unlimited_wot:before {
  content: '\e902';
}
.icon-icon-download-speed-7:before {
  content: '\e994';
}
.icon-dnld-speed-8:before {
  content: '\e9aa';
}
.icon-download:before {
  content: '\e987';
}
.icon-o-headphones:before {
  content: '\e611';
}
.icon-o-clock:before {
  content: '\e606';
}
.icon-small_icon_call:before {
  content: '\e909';
}
.icon-4k_whole_home_pvr_big:before {
  content: '\e986';
}
.icon-satellite:before {
  content: '\e976';
}
.icon-find_channel:before {
  content: '\e952';
}
.icon-tablet_mobile_play:before {
  content: '\e958';
}
.icon-mobile_tv:before {
  content: '\e95f';
}
.icon-calendar:before {
  content: '\e961';
}
.icon-tv_package_or_guide:before {
  content: '\e95e';
}
.icon-Small_Cart_with_arrow:before {
  content: '\e933';
}
.icon-exclamation-circled:before {
  content: '\e922';
}
.icon-Small_Location:before {
  content: '\e96a';
}
.icon-icn_OneApp:before {
  content: '\e93d';
}
.icon-icn_AnyDevice:before {
  content: '\e93f';
}
.icon-icn_AllYourTV:before {
  content: '\e940';
}
.icon-icn_GreatValue:before {
  content: '\e93e';
}
.icon-download_bl_bg:before {
  content: '\e953';
}
.icon-remote:before {
  content: '\e970';
}
.icon-i:before {
  content: '\e60a';
}
.icon-favourites-channels-7:before {
  content: '\e9ac';
}
.icon-favourites-channels-10:before {
  content: '\e981';
}
.icon-favourites-channels-20:before {
  content: '\e982';
}
.icon-favourites-channels-30:before {
  content: '\e983';
}
.icon-4k_tv_bl_bg .path1:before {
  content: '\e968';
  color: #231f20;
}
.icon-4k_tv_bl_bg .path2:before {
  content: '\e969';
  color: #fff;
  margin-left: -1em;
}
.icon-movies_bl .path1:before {
  content: '\e966';
  color: #231f20;
}
.icon-movies_bl .path2:before {
  content: '\e967';
  color: #fff;
  margin-left: -1em;
}
.icon-tomorrow_tect .path1:before {
  content: '\e964';
  color: #231f20;
}
.icon-tomorrow_tect .path2:before {
  content: '\e965';
  color: #fff;
  margin-left: -1em;
}
.icon-quality_tv_wot:before {
  content: '\e971';
}
.icon-tablet:before {
  content: '\e9ad';
}
.icon-record_unit:before {
  content: '\e96f';
}
.icon-quality_tv:before {
  content: '\e960';
}
.icon-4k_whole_home_bg:before {
  content: '\e962';
}
.icon-tv_app_icon:before {
  content: '\e955';
}
.icon-wireless_tv:before {
  content: '\e95d';
}
.icon-TUTORIAL:before {
  content: '\e957';
}
.icon-choice_of_4_packages:before {
  content: '\e959';
}
.icon-checkmark:before {
  content: '\e943';
}
.icon-wifi_bl_wot:before {
  content: '\e97c';
}
.icon-Big_info:before {
  content: '\e96b';
}
.icon-checkmark-circled:before {
  content: '\e921';
}
.icon-Small_Chat_now:before {
  content: '\e9ab';
}
