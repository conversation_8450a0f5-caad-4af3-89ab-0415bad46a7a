import * as React from "react";
import { <PERSON><PERSON><PERSON>ead<PERSON>, P<PERSON>Footer } from "singleban-components";
import { InjectedIntlProps, injectIntl } from "react-intl";
import { IPBE } from "../../models";
import { modalOpenedOmniture } from "../../utils/Utility";

interface Props {
    pbe: IPBE;
    isPBEModalLinkDisabled: boolean;
}

const PBEEquipmentNonReturn = (props: Props & InjectedIntlProps) => {
    const { intl: { formatMessage }, pbe } = props;

    const title = formatMessage({ id: pbe?.titleKey ?? "" });
    const description =  (pbe?.pbeDataBag?.equipmentName) ? formatMessage({ id: "EQUIPMENT_NON_RETURN_FEE_WITH_NAME" }, { equipmentName: pbe?.pbeDataBag?.equipmentName })
                            :
                         formatMessage({ id: "EQUIPMENT_NON_RETURN_FEE_WITHOUT_NAME" });

    const description2 = formatMessage({ id: "EQUIPMENT_NON_RETURN_FEE_NOTE" });

    const imageClassName = pbe?.pbeIconName;
    const footerItems = [{
        ctaLink: formatMessage({ id: "PBE_EQUIPMENT_NON_RETURN_FEE_LINK" }),
        iconClassName: "icon-10_move",
        titleKey: formatMessage({ id: "PBE_EQUIPMENT_NON_RETURN_FEE_FOOTER_TITLE" }),
        ctaTitleKey: formatMessage({ id: "PBE_EQUIPMENT_NON_RETURN_FEE_FOOTER_SUBTITLE" }),
        isFirstRow: true,
        id: "pbe-Equipment-non-return-fee-return-equipment"
    }];

    React.useEffect(() => {
        modalOpenedOmniture(props?.pbe?.triggerPbeOmniture, title, description);
    },[title, description]);
    return (
        <>
            <PBEHeader descriptionKey={description} iconClassName={imageClassName} titleKey={title} showUsageLink={false} anchorTag={null} isHTMLDescription={false} descriptionKey2={description2} />
            <PBEFooter footerItems={footerItems} isPBEModalLinkDisabled={props.isPBEModalLinkDisabled}/>
        </>
    );
};


export default (injectIntl(PBEEquipmentNonReturn));