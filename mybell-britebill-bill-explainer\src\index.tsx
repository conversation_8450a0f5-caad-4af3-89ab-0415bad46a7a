import { Init, RenderWidget } from "bwtk";
export function initialize(config: any, root: string, loader: any, debug) {

  config = Object.assign({}, config, {
    "loader.staticWidgetMappings": {
      "britebill-bill-explainer": {
        "factory": () => { return require("britebill-bill-explainer"); },
        "namespace": "singleban/billing"
      }
    }
  });
  Init(config);
  let bwtk = require("bwtk");
  let loc = bwtk.ServiceLocator.instance.getService(bwtk.CommonServices.Localization);

  loc.preloadLocaleData({
    "britebill-bill-explainer": "SingleBAN/Billing/britebill-bill-explainer"
  });

  RenderWidget("britebill-bill-explainer", _(root) as any);

  function _(root) {
    return document.getElementById(root);
  }
}