var TSRS_LM = window.TSRS_LM || {
	init: function () {
		// global/core events (only scripts that could possibly be moved to core should be added here)
		this.initEvents();

		// components (only scripts for components that could possibly be moved to core should be added here)
		this.initComponents();

		// custom post-init scripts
		this.postInit();
	},
	initEvents: function () { // only scripts that could possibly be moved to core should be added here
		var $body = $('body'),
			resizeTimeoutFn;

		if ($body.data('js-click-on-space-delegate-initialized') !== true) {
			$body.data('js-click-on-space-delegate-initialized', true).on('keydown', '.js-click-on-space-delegate', function (e) {
				var $this;

				if (32 === (e.which || e.keyCode || 0)) {
					e.preventDefault();
					e.stopPropagation();

					$this = $(this);
					if ($this.is('a')) {
						this.click();
					} else {
						$this.click();
					}
				}
			});
		}

		if ($body.data('js-same-height-resize-initialized') !== true) {
			$body.data('js-same-height-resize-initialized', true);

			$(window).on('resize', function () {
				clearTimeout(resizeTimeoutFn);
				resizeTimeoutFn = setTimeout(function () {
					// for 'same height elements', we first need to remove any inline height setting before we recompute and set the new heights
					resetSameHeightElements();
					processSameHeightElements();
				}, 200);
			});
		}
	},
	initComponents: function () { // only scripts for components that could possibly be moved to core should be added here
		$('.js-accordion-heading:not(.js-accordion-heading-initialized)').addClass('js-accordion-heading-initialized').each(function () {
			var $this = $(this);

			$this.on('click', function () {
				var isExpanded = $this.attr('aria-expanded') === 'true',
					$panel = $('#' + $this.attr('aria-controls'));

				if (isExpanded) {
					$this.attr('aria-expanded', 'false');
					$this.find('.accordion-toggle-icon').removeClass('icon-tsr-small_minus').addClass('icon-tsr-small_plus');
					$panel.addClass('collapse d-none');
				} else {
					$this.attr('aria-expanded', 'true');
					$this.find('.accordion-toggle-icon').removeClass('icon-tsr-small_plus').addClass('icon-tsr-small_minus');
					$panel.removeClass('collapse d-none');

					// collapse all panel that not match on current selected elemet
					$this.closest('.accordion-wrapper').find('.js-accordion-heading').not($this).each(function () {
						var $otherTrigger = $(this);
						var $otherPanel = $('#' + $otherTrigger.attr('aria-controls'));

						$otherTrigger.attr('aria-expanded', 'false');
						$otherTrigger.find('.accordion-toggle-icon').removeClass('icon-tsr-small_minus').addClass('icon-tsr-small_plus');
						$otherPanel.addClass('collapse d-none');
					});
				}
			});
		});

		$('.js-select-detail-panel').on('change', function (e, data) {
			var $target = $('#' + data.selectedOption.data('target-detail-panel'));

			$target.closest('.select-detail-panel-wrapper').find('.select-detail-panel').addClass('hide d-none').removeClass('d-flex');
			$target.removeClass('hide d-none').addClass('d-flex');
		});

		$('.js-aria-combobox-select-wrapper:not(.js-aria-combobox-select-wrapper-initialized)').addClass('js-aria-combobox-select-wrapper-initialized').each(function () {
			var $wrapper = $(this),
				$trigger = $wrapper.find('.aria-combobox-select-trigger'),
				$list = $('#' + $trigger.attr('aria-controls')),
				fnHideListbox = function (doFocusTrigger) {
					$trigger.attr('aria-expanded', 'false');
					$list.addClass('hide d-none');

					if (doFocusTrigger) {
						$trigger.focus();
					}
				}, fnShowListbox = function () {
					$trigger.attr('aria-expanded', 'true');
					$list.removeClass('hide d-none');
					$list.find('li.custom-highlight').removeClass('custom-highlight');
					$list.find('#' + $list.attr('aria-activedescendant')).addClass('custom-highlight');
					$list.attr('tabindex', '-1').focus();
				}, fnSetValueFromOption = function ($selOption) {
					var oldValue = $wrapper.find('.aria-combobox-select-trigger-value').text(),
						newValue = $selOption.text();

					if (oldValue !== newValue) {
						$wrapper.find('.aria-combobox-select-trigger-value').text(newValue);

						// fire custom change event on the wrapper in case something wants to listen to it
						$wrapper.trigger('change', {
							oldValue: oldValue,
							newValue: newValue,
							selectedOption: $selOption
						});
					}
				};

			$list.find('li').each(function () {
				var $option = $(this),
					id = $option.attr('id'),
					isSelected;

				if (!id) {
					id = uuidv4();
					$option.attr('id', id);
				}

				isSelected = $option.hasClass('custom-selected');
				if (isSelected) {
					$list.data('cached-selected', id);
					$list.find('li').not($option).removeClass('custom-selected');
					fnSetValueFromOption($option);
				}

				$option.on('click', function () {
					var selectedId = $option.attr('id');

					$list.find('li.custom-selected').removeClass('custom-selected');
					$option.addClass('custom-selected');
					$list.data('cached-selected', selectedId).attr('aria-activedescendant', selectedId);
					fnSetValueFromOption($option);
					fnHideListbox(true);
				});
			});

			$trigger.on('click', function () {
				var isExpanded = $trigger.attr('aria-expanded') === 'true',
					selectedId;

				if (isExpanded) {
					fnHideListbox();
				} else {
					selectedId = $list.data('cached-selected');
					$list.attr('aria-activedescendant', selectedId || $list.find('li').first().attr('id'));
					fnShowListbox();
				}
			});

			$trigger.on('keydown', function (e) {
				var key = e.which || e.keyCode || 0,
					isExpanded = $trigger.attr('aria-expanded') === 'true',
					selectedId,
					$option,
					KEYS = {
						'DOWN': 40,
						'UP': 38
					};

				if (!isExpanded) {
					if (KEYS.DOWN === key) {
						e.preventDefault();
						e.stopPropagation();
						selectedId = $list.data('cached-selected');
						if (selectedId) {
							$option = $list.find('#' + selectedId).next();
							if ($option.length === 0) {
								$option = $list.find('#' + selectedId);
							}
						} else {
							$option = $list.find('li').first();
						}
						$list.attr('aria-activedescendant', $option.attr('id'));
						fnShowListbox();
					} else if (KEYS.UP === key) {
						e.preventDefault();
						e.stopPropagation();
						selectedId = $list.data('cached-selected');
						if (selectedId) {
							$option = $list.find('#' + selectedId).prev();
							if ($option.length === 0) {
								$option = $list.find('#' + selectedId);
							}
						} else {
							$option = $list.find('li').first();
						}
						$list.attr('aria-activedescendant', $option.attr('id'));
						fnShowListbox();
					}
				}
			});

			$list.on('keydown', function (e) {
				var key = e.which || e.keyCode || 0,
					isExpanded = $trigger.attr('aria-expanded') === 'true',
					selectedId,
					$option,
					KEYS = {
						'ESCAPE': 27,
						'ENTER': 13,
						'SPACE': 32,
						'DOWN': 40,
						'UP': 38,
						'END': 35,
						'HOME': 36,
						'TAB': 9
					};

				if (isExpanded) {
					if (KEYS.ESCAPE === key) {
						e.preventDefault();
						e.stopPropagation();
						fnHideListbox(true);
						$trigger.focus();
					} else if (KEYS.ENTER === key || KEYS.SPACE === key) {
						e.preventDefault();
						e.stopPropagation();
						$option = $list.find('li.custom-highlight');
						selectedId = $option.attr('id');
						$list.data('cached-selected', selectedId).attr('aria-activedescendant', selectedId);
						fnSetValueFromOption($option);
						fnHideListbox(true);
					} else if (KEYS.DOWN === key) {
						e.preventDefault();
						e.stopPropagation();
						$option = $list.find('li.custom-highlight').removeClass('custom-highlight').next();
						if ($option.length === 0) {
							$option = $list.find('li').last();
						}
						$list.attr('aria-activedescendant', $option.addClass('custom-highlight').attr('id'));
					} else if (KEYS.UP === key) {
						e.preventDefault();
						e.stopPropagation();
						$option = $list.find('li.custom-highlight').removeClass('custom-highlight').prev();
						if ($option.length === 0) {
							$option = $list.find('li').first();
						}
						$list.attr('aria-activedescendant', $option.addClass('custom-highlight').attr('id'));
					} else if (KEYS.END === key) {
						e.preventDefault();
						e.stopPropagation();
						$option = $list.find('li.custom-highlight').removeClass('custom-highlight').siblings().last();
						$list.attr('aria-activedescendant', $option.addClass('custom-highlight').attr('id'));
					} else if (KEYS.HOME === key) {
						e.preventDefault();
						e.stopPropagation();
						$option = $list.find('li.custom-highlight').removeClass('custom-highlight').siblings().last();
						$list.attr('aria-activedescendant', $option.addClass('custom-highlight').attr('id'));
					} else if (KEYS.TAB === key) {
						fnHideListbox();
					}
				}
			});

			$(document).on('click', function (e) {
				var $target = $(e.target);

				if ($target.closest($trigger).length === 0 && $target.closest($list).length === 0) {
					fnHideListbox();
				}
			});
		});
	},
	postInit: function () {
		processSameHeightElements();
		this.checkPageLoadAutoFocus();
	},
	checkPageLoadAutoFocus: function () {
		var $autoFocusElContainer = $('.js-auto-focus-on-load:visible').first(),
			$target = $autoFocusElContainer.data('auto-focus-target-selector');

		// if data-auto-focus-target-selector is set, we only check for it. otherwise, we find the first link, button, or element with tabindex. the container is included in the pool of elements to check
		if ($target) {
			$target = $($target);
			$target.focus();
		} else {
			$autoFocusElContainer.find('a:visible, button:visible, [tabindex]:visible').addBack('a, button, [tabindex]').first().focus();
		}
	}
};

function uuidv4() {
	return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
		var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
		return v.toString(16);
	});
}

function resetSameHeightElements(pSameHeightElement) {
	if (pSameHeightElement !== undefined) {
		pSameHeightElement.closest('.same-height-wrap').find('.same-height[data-same-height-index=' + pSameHeightElement.data('same-height-index') + ']').css('min-height', '');
	} else {
		$('.same-height-wrap .same-height').css('min-height', '');
	}
}

function processSameHeightElements(pSameHeightElement) {
	var mobileMax = 767.98,
		tabletMax = 991.98,
		initializing = true,
		wrapEl,
		specificIndex;

	if (pSameHeightElement !== undefined) {
		initializing = false;
		wrapEl = pSameHeightElement.closest('.same-height-wrap');
		specificIndex = pSameHeightElement.data('same-height-index');
	} else {
		wrapEl = $('.same-height-wrap');
	}

	wrapEl.each(function () {
		var sameHeightWrap = $(this),
			skipBreakpoints = sameHeightWrap.data('same-height-skip'),
			sameHeightElements,
			indexArr;

		// if there are skip breakpoint flags, check if resizing should be skipped (note that resetSameHeightElements still gets fired)
		if (undefined !== skipBreakpoints) {
			skipBreakpoints = $.trim(skipBreakpoints).toLowerCase().split(',');

			if (window.matchMedia('(max-width: ' + mobileMax + 'px)').matches) {
				if (skipBreakpoints.indexOf('m') > -1) {
					return;
				}
			} else if (window.matchMedia('(max-width: ' + tabletMax + 'px)').matches) {
				if (skipBreakpoints.indexOf('t') > -1) {
					return;
				}
			} else if (skipBreakpoints.indexOf('d') > -1) {
				return;
			}
		}


		indexArr = [];
		sameHeightElements = sameHeightWrap.find('.same-height' + (undefined === specificIndex ? '' : '[data-same-height-index=' + specificIndex + ']'));

		sameHeightElements.each(function () {
			var sameHeightEl = $(this),
				index = sameHeightEl.data('same-height-index');

			// check same-height-index groups only once per group
			if (indexArr.indexOf(index) === -1) {
				var maxHeight = 0,
					equalElements = sameHeightElements.filter(function () {
						var tempEl = $(this),
							height = tempEl.outerHeight(),
							ret = tempEl.data('same-height-index') === index;

						if (ret && height > maxHeight) {
							maxHeight = height;
						}

						return ret;
					});

				equalElements.css('min-height', maxHeight);
				indexArr.push(index);
			}
		});

		if (initializing) {
			// listen for an event that can be fired which forces the resizing of all elements in the same group as the scope element. 
			// fire this when element height changes(see image lazyload afterLoad event listener above for example).
			sameHeightElements.on('resize', function () {
				var triggerEl = $(this);

				resetSameHeightElements(triggerEl);
				processSameHeightElements(triggerEl);
			});
		}
	});
}

$(document).ready(function () {
	TSRS_LM.init();
});