import * as React from "react";
import { TITLE_NEW_ACTIVATION, TITLE_PRORATION, TITLE_REMOVAL, DEVICE_RETURN_OPTION_FEE, LONG_DISTANCE_NETWORK_CONNECTION, INTERNET_DATA_OVERAGE, HOME_PHONE_PAY_PER_USAGE, HOME_PHONE_LONG_DISTANCE_USAGE, HOME_PHONE_OTHER_CARRIER_USAGE, MOBILITY_ROAMING_USAGE, EQUIPMENT_NON_RETURN_FEE, SMARTPAY_BEGAN, SMARTPAY_EARLY_TERMINATION, MONTHLY_DEVICE_PAYMENT, PBE_TELE_PREMIUM_SPORTS_INSTALLMENTS, PBE_LATE_PAYMENT_CHARGE, MOB_TEXT_MESSAGES, MOB_LOCAL_CALLS, MOB_LONG_DISTANCE, PBE_MOBILITY_EVENTS, MOB_DATA_OVERAGE, PBE_TEMP_SERVICE_SUSPENSION, RTB_PRORATION } from "../../utils/Constants";
import { getTitle } from "../../utils/Utility";
import { IBillExplainerBody } from "./IBillExplainerBody";
import PBENewActivation from "./PBENewActivation";
import PBEProration from "./PBEProration";
import PBERemoval from "./PBERemoval";
import PBEDeviceReturnOptionFee from "../PBEPopOver/PBEDeviceReturnOptionFee";
import PBELongDistanceNetworkConnection from "../PBEPopOver/PBELongDistanceNetworkConnection";
import PBEInternetDataOverage from "../PBEPopOver/PBEInternetDataOverage";
import PBEGeneric from "../PBEPopOver/PBEGeneric";
import PBEEquipmentNonReturn from "../PBEPopOver/PBEEquipmentNonReturn";
import PBEHomePhonePayPerUse from "../PBEPopOver/PBEHomePhonePayPerUse";
import PBEHomePhoneLongDistance from "../PBEPopOver/PBEHomePhoneLongDistance";
import PBEHomePhoneOtherCarrier from "../PBEPopOver/PBEHomePhoneOtherCarrier";
import PBEMobilityRoamingUsage from "../PBEPopOver/PBEMobilityRoamingUsage";
import PBESmartpayBegan from "../PBEPopOver/PBESmartpayBegan";
import PBESmartPayEarlyTermination from "../PBEPopOver/PBESmartPayEarlyTermination";
import PBEMonthlyDevicePayment from "../PBEPopOver/PBEMonthlyDevicePayment";
import PBETelePremiumSportsInstallments from "../PBEPopOver/PBETelePremiumSportsInstallments";
import PBELatePaymentCharge from "../PBEPopOver/PBELatePaymentCharge";
import PBEMobilityTextMessages from "../PBEPopOver/PBEMobilityTextMessages";
import PBEMobilityLocalCalls from "../PBEPopOver/PBEMobilityLocalCalls";
import PBEMobilityLongDistance from "../PBEPopOver/PBEMobilityLongDistance";
import PBEMobilityEvents from "../PBEPopOver/PBEMobilityEvents";
import PBEMobilityDataOverage from "../PBEPopOver/PBEMobilityDataOverage"
import PBETempServiceSuspension from "../PBEPopOver/PBETempServiceSuspension";
import PBERTBProration from "./PBERTBProration";

const BillExplainerBody = (props: IBillExplainerBody) => {
    const { title } = getTitle(props?.pbe?.pbeId ? props.pbe.pbeId : props.pbeCategory);
    if (title === TITLE_NEW_ACTIVATION) {
        return <PBENewActivation {...props} />;
    } else if (title === TITLE_PRORATION) {
        return <PBEProration {...props} />;
    } else if (title === TITLE_REMOVAL) {
        return <PBERemoval {...props} />;
    } else if (title === DEVICE_RETURN_OPTION_FEE) {
        return <PBEDeviceReturnOptionFee {...props} />
    } else if (title === RTB_PRORATION) {
        return <PBERTBProration {...props} />
    } else if (title === LONG_DISTANCE_NETWORK_CONNECTION) {
        return <PBELongDistanceNetworkConnection {...props} />
    } else if (title === INTERNET_DATA_OVERAGE) {
        return <PBEInternetDataOverage {...props} />
    } else if (title === EQUIPMENT_NON_RETURN_FEE) {
        return <PBEEquipmentNonReturn {...props} />
    } else if (title === HOME_PHONE_PAY_PER_USAGE) {
        return <PBEHomePhonePayPerUse {...props} />
    } else if (title === HOME_PHONE_LONG_DISTANCE_USAGE) {
        return <PBEHomePhoneLongDistance {...props} />
    } else if (title === HOME_PHONE_OTHER_CARRIER_USAGE) {
        return <PBEHomePhoneOtherCarrier {...props} />
    } else if (title === MOBILITY_ROAMING_USAGE) {
        return <PBEMobilityRoamingUsage {...props} />;
    } else if (title === SMARTPAY_BEGAN) {
        return <PBESmartpayBegan {...props} />;
    } else if (title === SMARTPAY_EARLY_TERMINATION) {
        return <PBESmartPayEarlyTermination {...props} />;
    } else if (title === MONTHLY_DEVICE_PAYMENT) {
        return <PBEMonthlyDevicePayment {...props} />;
    } else if (title === PBE_TELE_PREMIUM_SPORTS_INSTALLMENTS) {
        return <PBETelePremiumSportsInstallments {...props} />
    } else if (title === PBE_LATE_PAYMENT_CHARGE) {
        return <PBELatePaymentCharge {...props} />
    } else if (title === MOB_TEXT_MESSAGES) {
        return <PBEMobilityTextMessages {...props} />
    } else if (title === MOB_LOCAL_CALLS) {
        return <PBEMobilityLocalCalls {...props} />
    } else if (title === MOB_LONG_DISTANCE) {
        return <PBEMobilityLongDistance {...props} />
    } else if (title === PBE_MOBILITY_EVENTS) {
        return <PBEMobilityEvents {...props} />
    } else if (title === MOB_DATA_OVERAGE) {
        return <PBEMobilityDataOverage {...props} />
    }else if (title === PBE_TEMP_SERVICE_SUSPENSION) {
        return <PBETempServiceSuspension {...props} /> 
    }else {
        return <PBEGeneric {...props} />;
    }
};

export default BillExplainerBody;