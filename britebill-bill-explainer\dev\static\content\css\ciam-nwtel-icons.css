@font-face {
    font-family: 'ciam-nwtel-icons';
    src: url(../fonts/ciam-nwtel-icons.eot?#iefix) format("embedded-opentype"),url(../fonts/ciam-nwtel-icons.woff) format("woff"),url(../fonts/ciam-nwtel-icons.ttf) format("truetype"),url(../fonts/ciam-nwtel-icons.svg) format("svg");
    font-weight: 400;
    font-style: normal
}

.icon-ciam-nwtel,
.icon-ciam-nwtel [class^="icon-"],
.icon-ciam-nwtel [class*=" icon-"] {
    top: 0;
}

.icon-ciam-nwtel {
    font-family: 'ciam-nwtel-icons' !important;
    font-style: normal;
    speak: none;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

    .icon-ciam-nwtel:before {
        font-family: 'ciam-nwtel-icons' !important;
        position: static;
    }

.icon-ciam-nwtel-logo:before {
    content: "\e902";
    color: #008542;
}

.icon-checkmark-new:before {
    content: "\e901";
}

.icon-info .path1:before {
    content: "\e928";
    color: #333333;
}

.icon-info .path2:before {
    content: "\e929";
    color: #ffffff;
    margin-left: -1em;
}

.icon-info .path3:before {
    content: "\e92a";
    color: #ffffff;
    margin-left: -1em;
}

.icon-error-red .path1:before {
    content: "\e92f";
    color: #BD2025;
}

.icon-error-red .path2:before {
    content: "\e930";
    color: #fff;
    margin-left: -1em;
}

.icon-error-red .path3:before {
    content: "\e931";
    color: #fff;
    margin-left: -1em;
}

.icon-success:before {
    content: "\e92e";
    color: #378E42;
}

.icon-no-success:before {
    content: "\e92e";
    color: #e1e1e1;
}

.icon-eye-open:before {
    content: "\e903";
    color: #707070;
}

.icon-down_arrow:before {
    content: "\e90b";
}

.icon-eye-hide:before {
    content: "\e904";
    color: #707070;
}

/* NOTE: The icons below are still unused at time of writing. If you use any of them, move it above this comment. TODO: remove unused icons prior to deployment */

.icon-checkmark:before {
    content: "\e906";
}

/*Warning icon paths*/
.icon-warning-yellow .path1:before {
    content: "\e92f";
    color: #ffd669;
}

.icon-warning-yellow .path2:before {
    content: "\e930";
    color: #002e73;
    margin-left: -1em;
}

.icon-warning-yellow .path3:before {
    content: "\e931";
    color: #002e73;
    margin-left: -1em;
}


.icon-Bottom_arrow-small:before {
    content: "\e933";
}

.icon-close-big:before {
    content: "\e900";
}
