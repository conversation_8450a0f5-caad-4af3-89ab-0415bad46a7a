import { IPBE } from "./App";

export interface IFetchBillsReqPayload {

}

export interface IFetchBillsResPayload {
    pbeCategory: string;
    description: string;
    currentBalance: number;
    billCloseDate: string;
    downloadPDFLink: any;
    startDate: string;
    endDate: string;
    subscriberDetails: ISubscriberDetails;
    chargeItems: IChargeItem[];
    displayGraph: boolean;
    activityGraph: any;
    titleKey: string;
    descriptionKey: string;
    detailedDescKey: string;
    previousPeriodStartDate: string;
    previousPeriodEndDate: string;
    currentPeriodStartDate: string;
    currentPeriodEndDate: string;
    transactions: ICSMTransaction[];
    useLegendsForDiagram: boolean;
}

export interface IFetchBillsErrPayload { }


export interface IFetchBillsState {
    pbeCategory: string;
    description: string;
    billCloseDate: string;
    startDate: string;
    endDate: string;
    subscriberDetails: any;
    chargeItems: any;
    titleKey: string;
    descriptionKey: string;
    detailedDescKey: string;
    previousPeriodStartDate: string;
    previousPeriodEndDate: string;
    currentPeriodStartDate: string;
    currentPeriodEndDate: string;
    transactions: ICSMTransaction[];
    useLegendsForDiagram: boolean;
}

export type PBEPosition = "below" | "upper" | "";

export interface ISubscriberDetails {
    nickName: string;
    phoneNumber: string;
    subscriberNo: string;
    subscriberType: string;
    subAlignment: string;
}

export interface IChargeItem {
    chargeType: string;
    amount: number;
    startDate: string;
    endDate: string;
    name: string;
    description: string;
    chargeIdentifier: string;
    prorationDays: string;
    position: string;
    itemDescKey: string;
    itemDescSubKey: string;
    itemDetailedDescKey: string;
    itemLegend: string;
    legend: string;
    alwaysLabel: boolean;
    labelPosition: PBEPosition;
}

export interface ICSMTransaction {
    trxId: string;
    trxDescKey: string;
    trxEffDate: string;
}

export interface IBillExplainerContainer {
    configLinks: any;
    pbe: IPBE;
    pbeCategory: string;
    description: string;
    billCloseDate: string;
    cycleStartDate: string;
    cycleEndDate: string;
    subscriberDetails: ISubscriberDetails;
    chargeItems: IChargeItem[];
    isUXPMode: boolean;
    titleKey: string;
    descriptionKey: string;
    detailedDescKey: string;
    previousPeriodStartDate: string;
    previousPeriodEndDate: string;
    currentPeriodStartDate: string;
    currentPeriodEndDate: string;
    transactions: ICSMTransaction[];
    useLegendsForDiagram: boolean;
    isPBEModalLinkDisabled: boolean;
}