import * as React from "react";
import { InjectedIntlProps, injectIntl } from "react-intl";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer, PBEDeviceInstallmentsDiagram } from "singleban-components";
import { IPBE, IStoreState } from "../../models";
import { CURRENCY_OPTIONS, DATE_OPTIONS, getPBECacheKey, modalOpenedOmniture } from "../../utils/Utility";
import { connect } from "react-redux";

interface Props {
    pbe: IPBE;
    isPBEModalLinkDisabled: boolean;
}

interface IDataFromActionURL {
    agreementCredit?: number;
    droDeviceReturnDate?: string;
    droDeferredAmount?: number;
    isEligibleForDeviceUpgrade?: boolean;
}

interface MapStateToProps {
    dataFromActionURL: IDataFromActionURL;
}

const PBEMonthlyDevicePayment = (props: Props & InjectedIntlProps & MapStateToProps) => {
    const { intl: { formatMessage, formatNumber, formatDate, formatHTMLMessage }, pbe, dataFromActionURL } = props;
    const title = formatMessage({ id: "MONTHLY_DEVICE_PAYMENT_TITLE" });
    const currentInstallment = pbe?.pbeDataBag?.currentInstallmentNo;
    const totalInstallment = pbe?.pbeDataBag?.totalInstallmentNo;
    const description = formatHTMLMessage({ id: "MONTHLY_DEVICE_PAYMENT_DESC" }, {
        currentInstallment: formatNumber(currentInstallment),
        totalInstallment: formatNumber(totalInstallment),
        deviceName: pbe?.pbeDataBag?.deviceName
    });
    const droAmountGreaterThanZero = (dataFromActionURL?.droDeferredAmount ?? 0) > 0;
    const PBEFooterItems = [droAmountGreaterThanZero ? {
        ctaLink: formatMessage({ id: "MONTHLY_DEVICE_PAYMENT_DRO_LINK" }, {
            acctNo: pbe?.pbeDataBag?.encryptAcctNo,
            subNo: pbe?.pbeDataBag?.subNo,
        }),
        iconClassName: "icon-07_mobile_phone",
        titleKey: formatMessage({ id: "MONTHLY_DEVICE_PAYMENT_DRO_TITLE" }),
        ctaTitleKey: formatMessage({ id: "MONTHLY_DEVICE_PAYMENT_DRO_SUBTITLE" }),
        isFirstRow: true,
        id: "pbe-monthly-device-payment-dro"
    } : {
        ctaLink: formatMessage({ id: "MONTHLY_DEVICE_PAYMENT_DEVICE_DETAILS_LINK" }, {
            acctNo: pbe?.pbeDataBag?.encryptAcctNo,
            subNo: pbe?.pbeDataBag?.subNo,
        }),
        iconClassName: "icon-07_mobile_phone",
        titleKey: formatMessage({ id: "MONTHLY_DEVICE_PAYMENT_DEVICE_DETAILS_TITLE" }),
        ctaTitleKey: formatMessage({ id: "MONTHLY_DEVICE_PAYMENT_DEVICE_DETAILS_SUBTITLE" }),
        isFirstRow: true,
        id: "pbe-monthly-device-payment-device-details"
    }, dataFromActionURL?.isEligibleForDeviceUpgrade ? {
        ctaLink: formatMessage({ id: "MONTHLY_DEVICE_PAYMENT_UPGRADE_LINK" }, {
            acctNo: pbe?.pbeDataBag?.encryptAcctNo,
            subNo: pbe?.pbeDataBag?.subNo,
        }),
        iconClassName: "icon-19_mobile_update",
        titleKey: formatMessage({ id: "MONTHLY_DEVICE_PAYMENT_UPGRADE_TITLE" }),
        ctaTitleKey: formatMessage({ id: "MONTHLY_DEVICE_PAYMENT_UPGRADE_SUBTITLE" }),
        isFirstRow: false,
        id: "pbe-monthly-device-payment-upgrade"
    } : null];

    React.useEffect(() => {
        modalOpenedOmniture(props?.pbe?.triggerPbeOmniture, title, description);
    },[title, description]);
    return (
        <>
            <PBEHeader descriptionKey={description} titleKey={title} isHTMLDescription />
            <PBEDeviceInstallmentsDiagram currentInstallment={currentInstallment} totalInstallment={totalInstallment} ariaLabel={formatMessage({ id: "MONTHLY_DEVICE_PAYMENT_DIAGRAM_SR" }, {
                currentInstallment: formatNumber(currentInstallment),
                totalInstallment: formatNumber(totalInstallment),
            })} />
            <div className="margin-b-30 margin-h-xs-15 margin-h-30">
                <div className="box-round-grey pad-xs-15 pad-20">
                    <div className="d-block relative">
                        <div className="d-flex">
                            <span className="margin-r-xs-15 margin-r-20 d-flex flex-column">
                                <span className="surtitle-black">{formatMessage({ id: "MONTHLY_DEVICE_PAYMENT_REMAINING_BALANCE" })}</span>
                                <span className="sr-only">&nbsp;</span>
                                <span>({formatMessage({ id: "AS_OF" })} <span className="text-nowrap">{formatDate(new Date(pbe?.pbeDataBag?.remainingBalanceDate), DATE_OPTIONS)}</span>)</span>
                            </span>
                            <span className="d-flex container-flex-grow-fill justify-end">
                                <span className="d-inline noTxt txtRight">
                                    <span className="text-nowrap txtSize14 line-height-18" aria-hidden="true"><strong>{formatNumber(pbe?.pbeDataBag?.remainingBalance, CURRENCY_OPTIONS)}</strong> {formatMessage({ id: "OF" })} </span>
                                    <span className="sr-only">{formatNumber(pbe?.pbeDataBag?.remainingBalance, CURRENCY_OPTIONS)}</span>
                                    <span className="text-nowrap txtSize14 line-height-18" aria-hidden="true">{formatNumber(pbe?.pbeDataBag?.totalBalance, CURRENCY_OPTIONS)}</span>
                                    <span className="sr-only">{formatNumber(pbe?.pbeDataBag?.totalBalance, CURRENCY_OPTIONS)}</span>
                                </span>
                            </span>
                        </div>
                    </div>
                    {(dataFromActionURL?.agreementCredit !== null && dataFromActionURL?.agreementCredit !== undefined) ? <><hr className="margin-v-15 border-lightGray" aria-hidden="true" />
                        <div className="d-block relative">
                            <div className="d-flex">
                                <span className="margin-r-xs-15 margin-r-20">
                                    <span className="surtitle-black">{formatMessage({ id: "MONTHLY_DEVICE_PAYMENT_REMAINING_AGREEMENT_CREDIT" })}</span>
                                    <span className="sr-only">&nbsp;</span>
                                </span>
                                <span className="d-flex container-flex-grow-fill justify-end">
                                    <span className="d-inline noTxt txtRight">
                                        <span className="text-nowrap txtSize14 line-height-18" aria-hidden="true">{formatNumber(dataFromActionURL?.agreementCredit, CURRENCY_OPTIONS)}</span>
                                        <span className="sr-only">{formatNumber(dataFromActionURL?.agreementCredit, CURRENCY_OPTIONS)}</span>
                                    </span>
                                </span>
                            </div>
                        </div></> : null}
                    <hr className="margin-v-15 border-lightGray" aria-hidden="true" />
                    <div className="d-block relative">
                        <div className="d-flex margin-t-10">
                            <span className="margin-r-xs-15 margin-r-20">
                                <span className="surtitle-black">{formatMessage({ id: "MONTHLY_DEVICE_PAYMENT_MONTHLY_PAYMENT" })}</span>
                                <span className="sr-only">&nbsp;</span>
                            </span>
                            <span className="d-flex container-flex-grow-fill justify-end">
                                <span className="d-inline noTxt txtRight">
                                    <span className="text-nowrap txtSize14 line-height-18" aria-hidden="true">{formatNumber(pbe?.pbeDataBag?.monthlyPayment, CURRENCY_OPTIONS)}</span>
                                    <span className="sr-only">{formatNumber(pbe?.pbeDataBag?.monthlyPayment, CURRENCY_OPTIONS)}</span>
                                </span>
                            </span>
                        </div>
                    </div>
                    <hr className="margin-v-15 border-lightGray" aria-hidden="true" />
                    <div className="d-block relative">
                        <div className="d-flex">
                            <span className="margin-r-xs-15 margin-r-20">
                                <span className="surtitle-black">{formatMessage({ id: "MONTHLY_DEVICE_PAYMENT_FIRST_LAST_PAYMENT" })}</span>
                                <span className="sr-only">&nbsp;</span>
                            </span>
                            <span className="d-flex container-flex-grow-fill justify-end">
                                <span className="d-inline txtRight">
                                    <span className="text-nowrap txtSize14 line-height-18">{formatDate(new Date(pbe?.pbeDataBag?.firstPaymentDate), DATE_OPTIONS)} </span>
                                    <span className="sr-only">{formatMessage({ id: "ARIA_ONLY_TO" })}</span>
                                    <span className="txtSize14" aria-hidden="true">&nbsp;{formatMessage({ id: "PBE_HYPHEN" })}&nbsp;</span>
                                    <span className="text-nowrap txtSize14 line-height-18">{formatDate(new Date(pbe?.pbeDataBag?.lastPaymentDate), DATE_OPTIONS)}</span>
                                </span>
                            </span>
                        </div>
                    </div>
                    {droAmountGreaterThanZero ? <><hr className="margin-v-15 border-lightGray" aria-hidden="true" />
                        <div className="d-block relative">
                            <div className="d-flex">
                                <span className="margin-r-xs-15 margin-r-20">
                                    <span className="surtitle-black">{formatMessage({ id: "MONTHLY_DEVICE_PAYMENT_DEVICE_RETURN_OPTION" })}</span>
                                    <span className="sr-only">&nbsp;</span>
                                </span>
                            </div>
                            <div className="d-flex margin-t-10">
                                <span className="margin-r-xs-0 margin-r-20">
                                    <span className="">{formatMessage({ id: "MONTHLY_DEVICE_PAYMENT_UPGRADE_RETURN_BY" })}</span>
                                    <span className="sr-only">&nbsp;</span>
                                </span>
                                <span className="d-flex container-flex-grow-fill justify-end">
                                    <span className="d-inline noTxt txtRight">
                                        <span className="text-nowrap txtSize14 line-height-18">{formatDate(new Date(dataFromActionURL?.droDeviceReturnDate!), DATE_OPTIONS)}</span>
                                    </span>
                                </span>
                            </div>
                            <div className="d-flex margin-t-10">
                                <span className="margin-r-xs-0 margin-r-20">
                                    <span className="">{formatMessage({ id: "MONTHLY_DEVICE_PAYMENT_PAY_DEFERRED_AMOUNT" })}</span>
                                    <span className="sr-only">&nbsp;</span>
                                </span>
                                <span className="d-flex container-flex-grow-fill justify-end">
                                    <span className="d-inline noTxt txtRight">
                                        <span className="text-nowrap txtSize14 line-height-18" aria-hidden="true">{formatNumber(dataFromActionURL?.droDeferredAmount!, CURRENCY_OPTIONS)}</span>
                                        <span className="sr-only">{formatNumber(dataFromActionURL?.droDeferredAmount!, CURRENCY_OPTIONS)}</span>
                                    </span>
                                </span>
                            </div>
                        </div></> : null}
                </div>
            </div>
            {pbe?.pbeDataBag?.isSubscriberCancelled ? null : <PBEFooter footerItems={PBEFooterItems} isPBEModalLinkDisabled={props.isPBEModalLinkDisabled} />}
        </>
    );
};

const mapStateToProps = (state: IStoreState, ownProps: Props): MapStateToProps => {
    return (
        {
            dataFromActionURL: state.pbeDataCache[getPBECacheKey(ownProps.pbe)]
        }
    );
};


export default injectIntl(connect(mapStateToProps)(PBEMonthlyDevicePayment));