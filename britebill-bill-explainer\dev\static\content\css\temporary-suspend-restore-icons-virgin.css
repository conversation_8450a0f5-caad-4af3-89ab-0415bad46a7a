/*START VIRGIN ICON FONTS*/
@font-face {
    font-family: 'tsr-virgin-icons';
    src: url(../fonts/temporary-suspend-restore-icons-virgin.eot?#iefix) format("embedded-opentype"),
        url(../fonts/temporary-suspend-restore-icons-virgin.woff) format("woff"),
        url(../fonts/temporary-suspend-restore-icons-virgin.ttf) format("truetype"),
        url(../fonts/temporary-suspend-restore-icons-virgin.svg) format("svg");
    font-weight: 400;
    font-style: normal
}

.icon-tsr-virgin {
    font-style: normal;
    speak: none;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

    .icon-tsr-virgin, .icon-tsr-virgin:before {
        font-family: 'tsr-virgin-icons';
        position: static
    }

.icon-small-warning:before {
    content: "\e900";
}

.icon-BIG_WARNING .path1:before {
    content: "\e902";
    color: #e99e00;
}

.icon-BIG_WARNING .path2:before {
    content: "\e903";
    color: #fff;
    margin-left: -1em;
}

.icon-Big_check_confirm .path1:before {
    content: "\e9b8";
    color: #2c9e25;
}

.icon-Big_check_confirm .path2:before {
    content: "\e9b9";
    color: #fff;
    margin-left: -1em;
}

.icon-Big_info_bg1:before {
    content: "\e9d2";
}

.icon-close:before {
    content: "\e911";
}

.icon-Close2x:before {
    content: "\e963";
}

.icon-triangle-down:before {
    content: "\e9da";
}

.icon-arrow_left:before {
    content: "\e013";
}

.icon-upgrade-phone:before {
    content: "\e93f";
}

.icon-collapse-bold:before {
    content: "\e907";
}

.icon-expand-bold:before {
    content: "\e906";
}