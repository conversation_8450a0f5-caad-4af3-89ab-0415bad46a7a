import * as React from "react";
import { FormattedMessage } from "react-intl";
import { ExtractedFormattedMessage } from "singleban-components";
import { getLobData, getTitle } from "../../utils/Utility";
import { IBillExplainerTop } from "./IBillExplainerTop";
import { RTB_PRORATION } from "../../utils/Constants";

const getSubtitleTextKey = (nickname: string, serviceId: string) => {
    if (serviceId && /\s/g.test(serviceId)) {
        serviceId = serviceId.replace(/\s/g, "");
    }
    if (serviceId && nickname && ((serviceId === nickname) || (serviceId.toLowerCase() === nickname.toLowerCase()))) {
        return "PAGE_SERVICE_INFO_NO_DUPLICATE";
    }
    else {
        return "PAGE_SERVICE_INFO";
    };
}

const BillExplainerTop = (props: IBillExplainerTop) => {

    let { title } = getTitle(props.pbeCategory);
    const { lobName } = getLobData(props.service);
    const service = lobName;
    const subtitleTextKey = "PAGE_SERVICE_INFO";

    if (props.pbeCategory === RTB_PRORATION) {
        title = props.titleKey;
    }

    return (
        <div className="container pad-v-45 pad-v-xs-30 txtCenter d-flex flex-column align-items-center">
            <div className="dimension-72 d-flex align-items-center justify-center dimension-60-xs bgBlue border border-allRound margin-b-30 margin-b-xs-15" aria-hidden="true">
                <span className="icon-bill-redesign icon-ask-question txtWhite txtSize34 txtSize28-xs"></span>
            </div>
            <h2 className="title margin-b-5 margin-b-xs-15">
                {title && <ExtractedFormattedMessage id={title} />}
            </h2>
            <div className="long-text margin-b-0">
                <FormattedMessage id={service}>
                    {(serviceName: string) => <ExtractedFormattedMessage id={subtitleTextKey} values={{ service: serviceName, UserID: props.serviceID }} />}
                </FormattedMessage>
            </div>
        </div>
    );
};

export default BillExplainerTop;