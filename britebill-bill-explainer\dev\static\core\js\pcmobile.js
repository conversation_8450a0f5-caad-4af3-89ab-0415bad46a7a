
//Popup tooltips/menus
$('.trigger-popup').on('click touch', function () {
    $('.trigger-popup').next().hide();
    $(this).next().show();
});

$(document).ready(function () {
    $('.tooltip-interactive').each(function () {
        var $this = jQuery(this);
        $this.tooltip({
            container: $this
        })
    });
});


$(document).ready(function () {
    // this intialization adds the tooltip to the body and should be used for static-text tooltips that doesn't need to receive focus (read through aria-describedby)
    //$('.tooltip-static').tooltip();

    // START focusable tooltips for screen reader compatibility
    $('[data-toggle="tooltip"][data-tooltipnofocus!=true]:not(.tooltip-static)').on('shown.bs.tooltip', function () {
        $(this).find('.tooltip').attr('tabindex', 0);
    });
    // END focusable tooltips for screen reader compatibility


    // START Tooltip Auto Placement
    function fnTooltipPreAdjustment() {
        $(this).find('.tooltip.top, .tooltip.bottom').css('opacity', 0);
    }

    /* modified code to handle deeper nested elements. note that this supports absolute positioned tooltips only which is the default */
    function fnTooltipAdjustment() {
        var tooltip, parent, height, scrollTop, elementOffset, distance, tailHeight, marginTopDiff;

        tooltip = $(this).find('.tooltip.top');
        if (tooltip.length > 0) {
            // check top overflow
            parent = tooltip.parent();
            // tooltip height doesn't include the arrow and the arrow sometimes overlap the parent, so let's compute the height manually
            height = tooltip.height() + tooltip.find('.tooltip-arrow')[0].getBoundingClientRect().bottom - tooltip[0].getBoundingClientRect().bottom;
            scrollTop = $(window).scrollTop();
            elementOffset = parent.offset().top;
            distance = elementOffset - scrollTop;

            if (height > distance) {
                setTimeout(function () {
                    if (tooltip.hasClass('top')) {
                        marginTopDiff = parseFloat(tooltip.css('margin-top'));
                        tooltip.removeClass('top').addClass('bottom');
                        marginTopDiff -= parseFloat(tooltip.css('margin-top'));
                        tailHeight = tooltip[0].getBoundingClientRect().top - tooltip.find('.tooltip-arrow')[0].getBoundingClientRect().top;
                        tooltip.css('top', tooltip.position().top + height + parent.height() + tailHeight + marginTopDiff + "px");
                    }
                    tooltip.css('opacity', 1);
                }, 0);
            } else {
                tooltip.css('opacity', 1);
            }
            return;
        }

        tooltip = $(this).find('.tooltip.bottom');
        if (tooltip.length > 0) {
            // check bottom overflow
            parent = tooltip.parent();
            // tooltip height doesn't include the arrow and the arrow sometimes overlap the parent, so let's compute the height manually
            height = tooltip.height() + tooltip[0].getBoundingClientRect().top - tooltip.find('.tooltip-arrow')[0].getBoundingClientRect().top;
            distance = $(window).height() - parent[0].getBoundingClientRect().bottom;

            if (height > distance) {
                setTimeout(function () {
                    if (tooltip.hasClass('bottom')) {
                        marginTopDiff = parseFloat(tooltip.css('margin-top'));
                        tooltip.removeClass('bottom').addClass('top');
                        marginTopDiff -= parseFloat(tooltip.css('margin-top'));
                        tailHeight = tooltip.find('.tooltip-arrow')[0].getBoundingClientRect().bottom - tooltip[0].getBoundingClientRect().bottom;
                        tooltip.css('top', tooltip.position().top - height - parent.height() - tailHeight + marginTopDiff + "px");
                    }
                    tooltip.css('opacity', 1);
                }, 0);
            } else {
                tooltip.css('opacity', 1);
            }
            return;
        }

        tooltip.css('opacity', 1);
    }

    $('[data-toggle="tooltip"]').on('inserted.bs.tooltip', function () {
        fnTooltipPreAdjustment.apply(this);
    });

    $('[data-toggle="tooltip"]').on('shown.bs.tooltip', function () {
        fnTooltipAdjustment.apply(this);
    });

    // handle onscroll adjustment
    $(window).scroll(function () {
        var timeout;

        this.clearTimeout(timeout);
        timeout = setTimeout(function () {
            $('.tooltip.in').each(function () {
                var triggerEl = $(this).parent().first();
                fnTooltipPreAdjustment.apply(triggerEl);
                fnTooltipAdjustment.apply(triggerEl);
            });
        }, 100);
    });
    // END Tooltip Auto Placement

});

/* 
 * This function sets the other sections' tabindex to -1 and aria-hidden to true when the passed element is shown to trap keyboard and screenreader focus.
 */
function overwriteTabIndexAndAriaHiddenDifferentHierarchy(currEl, tempMoveToProperPos) {
    var parent, index, originClass;

    // if tempMoveToProperPos === true, the element will temporarily be moved and become an immediate child of the document body
    if (tempMoveToProperPos === true) {
        index = currEl.index();
        if (index > 0) { // has previous sibling, mark it
            originClass = 'origin-' + (new Date()).getTime();
            currEl.prev().addClass(originClass);
            currEl.data('originprev', originClass).appendTo('body');
        } else { // remember parent
            originClass = 'origin-' + (new Date()).getTime();
            currEl.parent().addClass(originClass);
            currEl.data('originparent', originClass).appendTo('body');
        }
    }

    // process the current element's siblings
    currEl.siblings().each(function () {
        var el = $(this), tabindex = el.attr('tabindex'), ariaHidden = el.attr('aria-hidden');

        if (undefined !== tabindex) {
            el.data('oldtabindex', tabindex);
        }
        el.attr('tabindex', -1);

        if (undefined !== ariaHidden && "" !== ariaHidden) {
            el.data('oldariahidden', ariaHidden);
        }
        el.attr('aria-hidden', true);

        el.find('a,area,input,select,textarea,button,iframe,[tabindex],[contentEditable=true]').each(function () {
            el = $(this), tabindex = el.attr('tabindex');

            if (undefined !== tabindex && "" !== tabindex) {
                el.data('oldtabindex', tabindex);
            }
            el.attr('tabindex', -1);
        });
    });

    // use recursion to process each ancestor until the body root is reached
    parent = currEl.parent();
    if (parent.length > 0 && !parent.is('body')) {
        overwriteTabIndexAndAriaHiddenDifferentHierarchy(currEl.parent());
    }
}

/* 
 * This function reverts the other sections' tabindex and aria-hidden to their original values when the passed element is hidden to remove keyboard and screenreader focus trapping.
 */
function revertTabIndexAndAriaHiddenDifferentHierarchy(currEl) {
    var parent, origParentClass, origParent, origPrevSibClass, origPrevSib;

    // process the current element's siblings
    currEl.siblings().each(function () {
        var el = $(this), tabindex = el.data('oldtabindex'), ariaHidden = el.attr('oldariahidden');

        if (undefined !== tabindex) {
            el.attr('tabindex', tabindex);
            el.removeData('oldtabindex');
        } else {
            el.removeAttr('tabindex');
        }

        if (undefined !== ariaHidden) {
            el.attr('aria-hidden', ariaHidden);
            el.removeData('oldariahidden');
        } else {
            el.removeAttr('aria-hidden');
        }

        el.find('a,area,input,select,textarea,button,iframe,[tabindex],[contentEditable=true]').each(function () {
            el = $(this), tabindex = el.data('oldtabindex');

            if (undefined !== tabindex) {
                el.attr('tabindex', tabindex);
                el.removeData('oldtabindex');
            } else {
                el.removeAttr('tabindex');
            }
        });
    });

    // use recursion to process each ancestor until the body root is reached
    parent = currEl.parent();
    if (parent.length > 0 && !parent.is('body')) {
        revertTabIndexAndAriaHiddenDifferentHierarchy(currEl.parent());
    }

    // this returns the element to its original position if it was temporarily moved by overwriteTabIndexAndAriaHiddenDifferentHierarchy
    origParentClass = currEl.data('originparent');
    if (origParentClass) {
        origParent = $('.' + origParentClass);
        currEl.prependTo(origParent);
        origParent.removeClass(origParentClass);
        currEl.removeData('originparent');
    } else {
        origPrevSibClass = currEl.data('originprev');
        if (origPrevSibClass) {
            origPrevSib = $('.' + origPrevSibClass);
            currEl.insertAfter(origPrevSib);
            origPrevSib.removeClass(origPrevSibClass);
            currEl.removeData('originprev');
        }
    }
}

//Modal js and focus .close
$('.modal').on('shown.bs.modal', function () {
    $('.close').focus();
    overwriteTabIndexAndAriaHiddenDifferentHierarchy($(this));
}).on('hidden.bs.modal', function () {
    var el = $(this),
        dismissFocusElSelector = el.data('dismiss-focus-target');
    revertTabIndexAndAriaHiddenDifferentHierarchy(el);
    if (undefined !== dismissFocusElSelector) {
        $(dismissFocusElSelector).focus();
    }
});

