﻿/* @import 'disable-hover.css'; */

/* RSX/BRF patch */
@media (min-width: 1000px) {
  .rsx-connector-area,
  .connector-area {
    position: relative !important;
  }

  .rsx-connector-area > a[class*="hidden-"]:after,
  .connector-area > a[class*="hidden-"]:after {  
    content: none !important;
  }  

  .rsx-connector-area > a[class*="hidden-"]:before,
  .connector-area > a[class*="hidden-"]:before {
    border-top-color: transparent;
  }

  .connector-area.active .connector-lob-flyout {
    display: none !important;
  }

  .connector-area.active > a,
  .connector-area.connector-area_current > a,
  .rsx-connector-area.rsx-active > a {
    color: #fff;
  }
}

/* Menu flyout styles */
.menu-flyout {
  overflow: hidden;
  position: absolute;
  -webkit-transition: opacity .25s;
  -o-transition: opacity .25s;
  transition: opacity .25s;
}

@media(min-width: 1000px) {
  .menu-flyout.menu-flyout-visible {
    position: absolute;
    background-color: #f0f0f0;
    width: 214px;
    padding-top: 0;
    padding-bottom: 0;
    -webkit-box-shadow: 0px 3px 16px 2px rgba(0,0,0,0.23);
            box-shadow: 0px 3px 16px 2px rgba(0,0,0,0.23);
    left: 50%;
    margin-left: -107px;
    top: 55px;
    height: auto;
    max-height: none;
    overflow: visible; 
  }

  .rsx-mode-aliant .menu-flyout,
  .mode-aliant .menu-flyout {
    top: 55px;
  }

  .menu-flyout ~ .connector-lob-flyout,
  .menu-flyout ~ .rsx-connector-lob-flyout {
    display: none !important;
  }

  .menu-flyout-visible .sub-nav-header {
    padding: 30px 30px 0 30px;
    margin: 0;
    font-weight: bold;
    font-size: 1.2rem;
    color: #111;
  }
  
  .menu-flyout-visible  ul {
    list-style: none;
    margin-top: 0;  
    padding-left: 0;
  }
  
  .menu-flyout-visible .menu-flyout-root {
    position: relative;
    min-height: 300px;
  }
  
  .menu-flyout-visible .menu-flyout-root:after {
    bottom: 100%;
    left: 50%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;  
    border-color: rgba(255, 255, 255, 0);
    border-bottom-color: #f0f0f0;
    border-width: 14px;
    margin-left: -14px;
  }
  
  .menu-flyout-visible .sub-nav-root {
    padding-top: 20px;
  }

  .menu-flyout-visible .sub-nav-root > li:last-child {
    padding-bottom: 40px;
  }
  
  .menu-flyout-visible .sub-nav-level-1 {
    padding: 10px 18px; 
    display: block;
    border-left: 4px solid #f0f0f0;
    font-size: 1.5rem;
    position: relative;
    line-height: normal;
  }
  
  .menu-flyout-visible .sub-nav-level-1:after {
    content: url('image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAANCAYAAACUwi84AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyRpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoTWFjaW50b3NoKSIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDozQTdGOTE3RDZFMDkxMUU4ODU1MkE2RDY0QTMwNUEyMyIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDozQTdGOTE3RTZFMDkxMUU4ODU1MkE2RDY0QTMwNUEyMyI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjNBN0Y5MTdCNkUwOTExRTg4NTUyQTZENjRBMzA1QTIzIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjNBN0Y5MTdDNkUwOTExRTg4NTUyQTZENjRBMzA1QTIzIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Vo8LxgAAAZJJREFUeNpMUEsvA2EUvfP55q1GR6tECYJ6rTyasJCQSCxEIiQeYWHDmv9gKSzEokIkrCQWVlJBjNZKKhYI2oSUeCeD6mg7046vXnGX955z7jmHmlw7hFdNbwYKkCwyfi2eBIqCv0HRuOGe2zzdn/ee+qLxZEMWT4Np/gOkTJPhGQxqWIXl3YuNSEyvEjlMQN8oZBVZf3djsTu/LPft6vzetrB95ovpqRoLz3wpoWTKBC1hHNSX2buKyh3GTfApZ0W58H4kjBKBxYDSMnoyBWShuF25rc4Kx2Pw/KFgSQkqxF91RkvvKPA0BtnCQqFNDFcVyMdHty9Dj6FnKYKgAxnkBYMR2AggW2BR4PJpOKpqINozobbQOoXTkdO5WToD1gPhVcUX6uFkAfraXGN2C+dBCFEgCQzsnNwtp4+0hYOBVtdYnsR7Ih8JQMSl5D26XvTuhYZwFg+D7ZXjDonzqNE4USb1vsf0uq1AeIRUDf1tFRNOWZh50wjzp2/M0tjf2VQ6y3NYc1qFafU98cX8nU8BBgAJEJrPXAOh1QAAAABJRU5ErkJggg==');
    color: #00549a;
    position: absolute;
    top: 50%;
    margin-top: -6px;
    right: 15px;
  }
  
  .menu-flyout-visible .sub-nav-level-1:hover {
    border-left-color: #00549a;
    background-color: #e1e1e1;
    text-decoration: none;
  }
  
  .menu-flyout-visible .no-sub-nav .sub-nav-level-1:after {
    content: none;
  }

  .menu-flyout-visible .no-sub-nav .sub-nav-level-1 {
    color: #00549a;
  }
  
  .menu-flyout.menu-flyout-visible li.menu-flyout-item-active > .sub-nav-level-1 {
    border-left-color: #00549a;
    background-color: white;
    font-weight: bold;
    -webkit-box-shadow: -1px 4px 8px -4px rgba(0,0,0,0.1);
            box-shadow: -1px 4px 8px -4px rgba(0,0,0,0.1);
  }
  
  .menu-flyout.menu-flyout-visible li.menu-flyout-item-active > .sub-nav-level-1:after {
    content: "";
  }
  
  .menu-flyout-visible .sub-nav-level-1 span {
    display: block;
    font-size: 1.2rem;
  }

  .menu-flyout-visible .sub-nav-group {
    width: 180%;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 0;
    background-color: white;
    z-index: -1;
    -webkit-box-shadow: 0px 3px 16px 2px rgba(0,0,0,0.23);
            box-shadow: 0px 3px 16px 2px rgba(0,0,0,0.23);
    opacity: 0;
    -webkit-transition: opacity .225s;
    -o-transition: opacity .225s;
    transition: opacity .225s;
  }
  
  .menu-flyout-visible .enter-animation .sub-nav-group,
  .menu-flyout-visible .enter-animation .sub-nav-group a,
  .menu-flyout-visible .enter-animation .sub-nav-item {
    color: white;
  }
  
  .menu-flyout-visible .enter-offset .sub-nav-item {
    opacity: 0;
    -webkit-transform: translateX(20px);
        -ms-transform: translateX(20px);
            transform: translateX(20px);
    overflow: hidden;
  }
  
  .menu-flyout-visible .pre-enter-fadein .sub-nav-item,
  .menu-flyout-visible .enter-fadein .sub-nav-item {
    opacity: 0;
  }
  
  .menu-flyout-has-been-expanded .sub-nav-group {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
  
  .menu-flyout-visible .sub-nav-large {
    width: 230%;
  }
  
  .menu-flyout.menu-flyout-visible li.menu-flyout-item-active .sub-nav-group {
    position: absolute;
    left: 214px;
    top: 0;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    opacity: 1;
  }
  
  .menu-flyout-visible .sub-nav-item {
    opacity: 1;
    -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
            transform: translateX(0);
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
    width: 0;
    overflow-y: hidden;
    -webkit-transition: opacity .225s,  -webkit-transform .225s;
    transition: opacity .225s,  -webkit-transform .225s;
    -o-transition: opacity .225s,  transform .225s;
    transition: opacity .225s,  transform .225s;
    transition: opacity .225s,  transform .225s,  -webkit-transform .225s;
  }
  
  .menu-flyout-visible .sub-nav-item ul {
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
    padding-left: 30px;
    padding-right: 30px;
    height: 100%;
    padding-top: 30px;
  }
  .menu-flyout-visible .sub-nav-item .sub-nav-level4 {
    padding-top: 20px;
  }
  .menu-flyout-visible .sub-nav-item:not(:last-child) ul {
    border-right: 1px solid #e2e2e2;  
  }
  
  .menu-flyout-visible .sub-nav-item ul > li {
    margin-bottom: 1.5rem;
  }
  
  .menu-flyout-visible .sub-nav-item ul > li a {
    display: block;
    color: #555;
    font-size: 1.3rem;
    line-height: normal;
  }
  
  .menu-flyout-visible .sub-nav-item ul > li a:hover {
    color: #00549a;
    text-decoration: underline;
  }
  
  .menu-flyout-visible .sub-nav-item .sub-nav-links-two-columns,
  .menu-flyout-visible .sub-nav-item .sub-nav-links-three-columns {
    -webkit-column-gap: 30px;
            column-gap: 30px;
    -webkit-column-fill: auto;
            column-fill: auto;
            height: 280px;          
  }

  .menu-flyout-visible .sub-nav-links-two-columns {
    columns: 2;
    -webkit-columns: 2;
    -moz-columns: 2;
    list-style-position: inside;
  }
  
  .menu-flyout-visible .sub-nav-links-three-columns {
    columns: 3;
    -webkit-columns: 3;
    -moz-columns: 3;
    list-style-position: inside;
  }  
}

/* To be clean by Alex*/

.global-navigation #connector-search [type="search"]::-ms-clear {
    display: none;
}

.global-navigation ::-webkit-search-cancel-button {
    -webkit-appearance: none;
}

.global-navigation .container {
    width: 960px;
}

.global-navigation a:focus, .global-navigation .btn:focus, .global-navigation li.ui-menu-item:focus {
    outline: -webkit-focus-ring-color auto 5px;
}

.global-navigation .caret {
    border-top: medium none;
}

/* Star skip to main content*/

.global-navigation .skip-to-main-link:focus {
    top: 0;
}

.global-navigation a.skip-to-main-link {
    color: #ffffff;
}

.global-navigation .skip-to-main-link {
    display: inline-block;
    padding: 9px 12px;
    position: absolute;
    top: -50px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    text-decoration: none;
    border-bottom-right-radius: 8px;
    transition: top .3s ease-out;
    z-index: 3000;
    color: #fff;
    text-transform: uppercase;
    font-size: 11px;
    background: #2d2e33;
}

/* End skip to main content*/

/* Start Federal Bar Area */
.global-navigation .federal-bar {
    background: #2d2e33;
    height: 33px;
    padding: 9px 0;
    display: block;
}

.global-navigation .federal-bar-links a, .global-navigation .federal-bar-links a:link, .global-navigation .federal-bar-links a:visited {
    color: #babec2;
    text-decoration: none;
}

.global-navigation .federal-bar-store-locator {
    display: inline-block;
    position: relative;
}

.global-navigation .store-locator-section-links li .label-text {
    font-size: 13px;
    color: #555555;
    text-decoration: none;
    white-space: nowrap;
}

    .global-navigation .store-locator-section-links li .label-text:hover {
        color: #555555;
    }

.global-navigation .store-locator-section-links li:not(:first-child) {
    padding-top: 10px;
}

.global-navigation .federal-bar-store-locator-popup.federal-bar-links {
    width: 260px;
}

.global-navigation .federal-bar-store-locator-popup {
    display: none;
    position: absolute;
    right: -65px;
    background: #fff;
    z-index: 100;
    padding: 20px;
    box-shadow: 0 0 40px rgba(0,0,0, .5);
    top: 30px;
    width: 360px;
    text-transform: none;
}

.global-navigation .federal-bar-store-locator.active .federal-bar-store-locator-popup {
    display: block;
}

.global-navigation .federal-bar-store-locator .federal-bar-store-locator-popup.federal-bar-links.caret::after {
    left: calc(50% + 30px);
}

.global-navigation .federal-bar-store-locator-popup.federal-bar-links .store-locator-section {
    display: none;
}

.global-navigation .federal-bar-store-locator-popup .store-locator-section {
    display: block;
}

.global-navigation .federal-bar-store-locator-popup.federal-bar-links .store-locator-section-links {
    display: block;
}

.global-navigation .federal-bar-store-locator-popup .store-locator-section-links {
    display: none;
}

.global-navigation .federal-bar-mobile .custom-select-trigger > .icon {
    -webkit-transform: translateY(-55%) rotate(90deg);
    -ms-transform: translateY(-55%) rotate(90deg);
    transform: translateY(-55%) rotate(90deg);
    color: #97989c;
}

.global-navigation .checkboxes_absolute .checkbox {
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 3px;
    box-shadow: inset 1px 1px 0 0 rgba(0,0,0,.1);
    display: inline-block;
    width: 22px;
    height: 22px;
    border: 1px solid #ccc;
    background-color: #fff;
    transition: background-color 10ms cubic-bezier(.17, .67, .83, .67);
}

.global-navigation .radios .label, .checkboxes .label {
    margin-left: 0;
    position: relative;
    color: #212121;
    font-weight: normal;
    vertical-align: top;
    cursor: default;
}

.global-navigation .radios_absolute.radios .label-text, .checkboxes_absolute.checkboxes .label-text {
    padding-left: 35px;
    -webkit-transform: translateY(2px);
    -ms-transform: translateY(2px);
    transform: translateY(2px);
}

.global-navigation div.checkboxes label.active .checkbox {
    background-color: #003778;
    border-color: #003778;
}

.global-navigation .radios .label.focused .radio, .global-navigation .checkboxes .label.focused .checkbox {
    outline: 0;
    box-shadow: 0 0 3px 2px rgba(178,209,228,1);
}

.global-navigation .checkboxes .label:not(.disabled) {
    cursor: pointer;
}

.global-navigation .federal-bar-store-locator-popup a.button {
    box-sizing: border-box;
    display: inline-block;
    position: relative;
    margin: 15px 0;
    padding: 10px 36px;
    vertical-align: middle;
    background-color: #003778;
    font-size: 16px;
    line-height: 1;
    text-align: center;
    text-decoration: none !important;
    color: #fff;
    border: 2px solid #003778;
    border-radius: 24px;
    cursor: pointer;
    transition: all .25s cubic-bezier(.55,0,.1,1);
    width: 100%;
}

    .global-navigation .federal-bar-store-locator-popup a.button:hover, .federal-bar-store-locator-popup a.button:focus {
        background-color: #00549a;
        border-color: #00549a;
    }

.global-navigation .federal-bar-link-provinces {
    position: absolute;
    top: 30px;
    right: -17px;
    z-index: 100;
    width: 250px;
    display: none;
    background-color: white;
    padding: 15px 10px;
    box-shadow: 0 0 40px rgba(0,0,0, .3);
}

    .global-navigation .federal-bar-link-provinces.caret:after {
        transform: translateX(53px) translateY(-100%);
        -webkit-transform: translateX(53px) translateY(-100%);
        -ms-transform: translateX(53px) translateY(-100%);
    }

.global-navigation #connector-search {
    width: 210px;
}

    .global-navigation #connector-search [type="reset"], #connector-search [type="submit"], #connector-search #voice_search {
        position: absolute;
        right: 5px;
        left: auto;
        top: 0;
        padding: 0;
        border: 0;
        background: none;
    }

    .global-navigation #connector-search [type="reset"] {
        right: 30px;
        width: 30px;
        display: none;
    }

        .global-navigation #connector-search [type="reset"].active {
            display: block;
        }

.global-navigation .header-preferences {
    display: inline-block;
    margin-left: 15px;
}



/* End of Federal Bar Area */

/* Start of Connector */

.global-navigation .connector {
    position: relative;
    background: #00549a;
}

    /* End of Connector */



    /* Start Right Connecter Area */
    .global-navigation .connector .connector-brand a {
        color: #fff;
        top: 4px;
    }

.global-navigation .connector-area.active div > a {
    color: #fff;
}

.global-navigation .connector-search-wrap {
    float: left;
    margin-top: 0px;
    margin-right: 10px;
    position: relative;
}

.global-navigation .hideAutocomplete.active:focus {
    opacity: .5;
}

.global-navigation .connector-cart-button {
    padding: 0;
    margin: 0;
    font-size: 27px;
    bottom: 0;
    color: #ffffff;
}

    .global-navigation .connector-cart-button:hover {
        color: #ffffff;
    }

.global-navigation #connector-search [type="submit"]:after {
    content: '\e615';
    -webkit-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
    font-size: 18px;
    color: #003778;
}

.global-navigation #connector-search [type="reset"]:after, #connector-search [type="submit"]:after {
    font-family: 'bell-icon';
    line-height: 1;
}

.global-navigation #connector-search [type="reset"]:after, #connector-search [type="reset"]:before, #connector-search [type="submit"]:after {
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
}

/* End Right Connecter Area */

.global-navigation #connector-search [type="search"] {
    display: inline-block;
    border-radius: 18px;
    box-shadow: inset 2px 0 3px -1px rgba(0,0,0,.46);
}

.global-navigation input[type="search"]::-webkit-input-placeholder {
    color: #999999;
}

.global-navigation input[type="search"]::-moz-placeholder {
    color: #999999;
}

.global-navigation input[type="search"]:-ms-input-placeholder {
    color: #999999;
}

.global-navigation input[type="search"]:-moz-placeholder {
    color: #999999;
}

.global-navigation .connector-area div > a {
    color: #c2cedf;
    display: inline-block;
    margin-right: 15px;
    margin-left: 10px;
    top: -1px;
    position: relative;
}

    .global-navigation .connector-area div > a:hover {
        color: #ffffff;
    }

    .global-navigation .connector-area div > a span {
        font-size: 26px;
        letter-spacing: -1px;
    }

.global-navigation a, .global-navigation a:hover {
    color: #00549a;
}

body.voice-search-enabled #connector-search #voice_search {
    display: block;
}

.global-navigation #connector-search [type="submit"] {
    width: 30px;
}

.global-navigation #connector-search #voice_search {
    right: 30px;
}

    .global-navigation #connector-search #voice_search:after {
        content: '';
        -webkit-transform: translateX(-50%) translateY(-50%);
        -ms-transform: translateX(-50%) translateY(-50%);
        transform: translateX(-50%) translateY(-50%);
        font-size: 18px;
        color: #003778;
    }

.global-navigation .menu-flyout-visible .sub-nav-level-1 {
    font-size: 15px;
}

.global-navigation .menu-flyout-visible .sub-nav-item ul > li a {
    font-size: 13px;
}

.global-navigation .menu-flyout-visible .sub-nav-level-1:after {
    content: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAANCAYAAACUwi84AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyRpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoTWFjaW50b3NoKSIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDozQTdGOTE3RDZFMDkxMUU4ODU1MkE2RDY0QTMwNUEyMyIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDozQTdGOTE3RTZFMDkxMUU4ODU1MkE2RDY0QTMwNUEyMyI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjNBN0Y5MTdCNkUwOTExRTg4NTUyQTZENjRBMzA1QTIzIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjNBN0Y5MTdDNkUwOTExRTg4NTUyQTZENjRBMzA1QTIzIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Vo8LxgAAAZJJREFUeNpMUEsvA2EUvfP55q1GR6tECYJ6rTyasJCQSCxEIiQeYWHDmv9gKSzEokIkrCQWVlJBjNZKKhYI2oSUeCeD6mg7046vXnGX955z7jmHmlw7hFdNbwYKkCwyfi2eBIqCv0HRuOGe2zzdn/ee+qLxZEMWT4Np/gOkTJPhGQxqWIXl3YuNSEyvEjlMQN8oZBVZf3djsTu/LPft6vzetrB95ovpqRoLz3wpoWTKBC1hHNSX2buKyh3GTfApZ0W58H4kjBKBxYDSMnoyBWShuF25rc4Kx2Pw/KFgSQkqxF91RkvvKPA0BtnCQqFNDFcVyMdHty9Dj6FnKYKgAxnkBYMR2AggW2BR4PJpOKpqINozobbQOoXTkdO5WToD1gPhVcUX6uFkAfraXGN2C+dBCFEgCQzsnNwtp4+0hYOBVtdYnsR7Ih8JQMSl5D26XvTuhYZwFg+D7ZXjDonzqNE4USb1vsf0uq1AeIRUDf1tFRNOWZh50wjzp2/M0tjf2VQ6y3NYc1qFafU98cX8nU8BBgAJEJrPXAOh1QAAAABJRU5ErkJggg==);
}


.global-navigation .menu-flyout {
    display: none;
}

    .global-navigation .menu-flyout.menu-flyout-visible {
        display: block;
    }

.global-navigation .menu-flyout-visible .sub-nav-root ul.sub-nav-group {
    display: none;
}

.global-navigation .bellSlimSemibold-Nav {
    border-right: solid 1px #004978;
    border-left: solid 1px #004978;
    margin-left: 4px;
    margin-top: 3px;
    padding-left: 9px;
    letter-spacing: -1.0px;
}

.global-navigation .sub-nav-why-bell-desktop {
    display: block;
}

.global-navigation .sub-nav-why-bell-mobile {
    display: none;
}



.global-navigation .connector-settings-mobile > li > div > .icon {
    position: absolute;
    color: #fff;
    font-size: 22px;
    top: 9px;
    left: 18px;
}

.global-navigation .connector-settings-mobile > li > div > a {
    display: block;
    padding: 10px 20px 11px 49px;
    font-size: 17px;
}

@media(min-width: 1200px) {
    .global-navigation .container

{
    width: 1200px;
}

.global-navigation .connector-area div > a {
    margin: 0 20px;
}

.global-navigation #connector-search {
    width: 300px;
}

    .global-navigation #connector-search [type="submit"] {
        width: 35px;
    }

}

@media(min-width: 992px) {
    /* Start of mobile setting */
    .global-navigation .connector-mobile-bar, .global-navigation .connector-settings-mobile, .global-navigation .connector-nav-close-button, .global-navigation .federal-bar-link-provinces

{
    display: none;
}

.global-navigation .federal-bar-link-provinces.active {
    display: block;
}

.global-navigation .connector-mobile-bar {
    border: none !important;
    position: relative;
    height: 55px;
}

/* Start of mobile setting */

.global-navigation .connector-nav {
    width: auto;
    position: static;
    float: left;
    margin-top: 13px;
    background: none;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
    overflow: visible;
}

.global-navigation .menu-flyout-visible .sub-nav-item ul > li {
    margin-bottom: 15px;
}

.global-navigation .menu-flyout {
    opacity: 0;
    height: 0;
    max-height: 0;
    overflow: hidden;
    position: absolute;
    z-index: -1;
    -webkit-transition: opacity .25s;
    -o-transition: opacity .25s;
    transition: opacity .25s;
}

    .global-navigation .menu-flyout.menu-flyout-visible {
        opacity: 1;
        position: absolute;
        z-index: 20;
        background-color: #f0f0f0;
        width: 214px;
        padding-top: 0;
        padding-bottom: 0;
        -webkit-box-shadow: 0 3px 16px 2px rgba(0,0,0,.23);
        box-shadow: 0 3px 16px 2px rgba(0,0,0,.23);
        left: 50%;
        margin-left: -107px;
        top: 55px;
        height: auto;
        max-height: none;
        overflow: visible;
    }

.global-navigation .menu-flyout-visible .sub-nav-header {
    padding: 29px 30px 0 30px;
    font-weight: bold;
    color: #111;
    display: flex;
    font-size: 12px;
}

.global-navigation .menu-flyout.menu-flyout-visible li.menu-flyout-item-active .sub-nav-group {
    position: absolute;
    left: 214px;
    top: 0;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    opacity: 1;
}

.global-navigation .menu-flyout-visible .no-sub-nav .sub-nav-level-1::after {
    content: none;
}

.global-navigation .shopping-cart-button {
    padding-right: 0;
    padding-left: 10px;
    margin-top: -2px;
    margin-left: 6px;
    font-size: 27px;
    bottom: -3px;
}

}

@media(max-width: 991.98px) {
    .global-navigation a, .global-navigation a:hover

{
    color: #ffffff;
}

.global-navigation .container {
    width: 100%;
}

.global-navigation .federal-bar {
    display: none;
}

.global-navigation .connector-mobile-bar {
    margin: 0;
    position: relative;
    border-bottom: 1px solid #003778 !important;
}

.global-navigation #connector-search {
    position: relative;
    width: 100%;
    display: block;
}

    .global-navigation #connector-search [type="search"] {
        border-radius: 0;
        box-shadow: none;
    }

/* Start Shadow */
.global-navigation .connector-area.connector-area_first > div:first-child {
    -webkit-box-shadow: inset 0px 11px 17px 0px rgba(0,0,0,0.25);
    -moz-box-shadow: inset 0px 11px 17px 0px rgba(0,0,0,0.25);
    box-shadow: inset 0px 11px 17px 0px rgba(0,0,0,0.25);
}
/* End Shadow */

.global-navigation #connector-search [type="reset"], .global-navigation #connector-search [type="submit"], .global-navigation #connector-search #voice_search {
    right: 8px;
}

    .global-navigation #connector-search [type="submit"]:after {
        color: #00549a;
        font-size: 16px;
    }

.global-navigation .connector-search-wrap {
    position: absolute;
    width: 100%;
    z-index: 55;
    top: 55px;
    left: 0;
    display: none;
}

    .global-navigation .connector-search-wrap.active {
        display: block;
    }

.global-navigation .connector-areas li > div, .global-navigation .connector-settings-mobile li > div, .global-navigation .federal-bar-mobile li > div {
    padding: 1px;
    width: 100%;
}

.global-navigation .connector.connector-search-active .connector-nav {
    top: 111px;
}

.global-navigation .connector-area div > a {
    margin: 0;
}

.global-navigation .connector-area.active div > a::after, .global-navigation .connector-area.active .menu-flyout-visible .menu-flyout-item-active .sub-nav-level-1:after {
    top: 48px;
    left: 28px;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    z-index: 11;
    pointer-events: none;
    border-left: 13px solid transparent;
    border-right: 13px solid transparent;
    border-top: 11px solid #00549a;
}

.global-navigation .connector-area.active .menu-flyout-visible .menu-flyout-item-active .sub-nav-level-1:after {
    transform: none;
    border-top: 11px solid #003778;
}

.global-navigation .connector-area div > a span {
    font-size: 20px;
}

.global-navigation .connector-single-link a > h3 {
    font-size: 18px;
    font-family: sans-serif;
    font-weight: normal;
    letter-spacing: normal;
    line-height: 1.1
}

.global-navigation .connector-single-link a {
    padding: 17px 40px 6px 24px;
    display: block;
    position: relative;
}

.global-navigation .connector-area div > a {
    position: relative;
    font-family: 'bell-slim';
    letter-spacing: .4px;
    display: block;
    padding: 11px 35px 6px 14px;
    font-size: 20px;
    margin-bottom: 1px;
}

.global-navigation .connector-area_find-store:after {
    font-family: 'bell-icon';
    content: "\e620";
    color: #fff;
    font-size: 18px;
    font-style: normal;
    speak: none;
    font-weight: normal;
    font-variant: normal;
    position: absolute;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    top: 25px;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    right: 11px;
    opacity: 1;
    transition: opacity .3s cubic-bezier(.55,0,.1,1), transform .2s cubic-bezier(.55,0,.1,1);
}

.global-navigation .sub-nav-why-bell-desktop {
    display: none;
}

.global-navigation .sub-nav-why-bell-mobile {
    display: block;
}

.global-navigation .menu-flyout.menu-flyout-visible {
    display: block;
    position: relative;
    background-color: #003778;
}

.global-navigation .menu-flyout-visible .sub-nav-level-1 {
    font-size: 18px;
    padding: 14px 40px 9px 24px;
    display: block;
    position: relative;
}

    .global-navigation .menu-flyout-visible .sub-nav-level-1:after, .sub-nav-root .sub-nav-group .sub-nav-item li a::after {
        font-family: 'bell-icon';
        content: "\e012";
        color: #fff;
        font-size: 13px;
        font-style: normal;
        speak: none;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        line-height: 1;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: 14px;
    }

.global-navigation .menu-flyout-visible li:not(.no-sub-nav) .sub-nav-level-1:after {
    transform: translateY(-50%) rotate(90deg);
}

.global-navigation .menu-flyout-visible .menu-flyout-item-active .sub-nav-level-1:after {
    content: " ";
}

.global-navigation .menu-flyout-visible .menu-flyout-item-active ul.sub-nav-group {
    display: block;
    background: #002c6b;
}

.global-navigation .connector-areas > li:first-child, .menu-flyout-root > ul > li:first-child, .menu-flyout-root > ul > li > ul > li:first-child {
    -webkit-box-shadow: inset 0 11px 17px 0 rgba(0, 0, 0, 0);
    -moz-box-shadow: inset 0 11px 17px 0 rgba(0, 0, 0, 0);
    box-shadow: inset 0 11px 17px 0 rgba(0, 0, 0, 0);
}

.global-navigation .sub-nav-root li:first-child > a {
    -webkit-box-shadow: inset 0 11px 17px 0 rgba(0,0,0,.11);
    -moz-box-shadow: inset 0 11px 17px 0 rgba(0,0,0,.11);
    box-shadow: inset 0 11px 17px 0 rgba(0,0,0,.11);
}

.global-navigation .menu-flyout .sub-nav-root > li > ul > li > ul > li > a {
    box-shadow: none;
}

.global-navigation .sub-nav-group > li, .global-navigation .sub-nav-group > li > a, .global-navigation .sub-nav-group > li > ul > li {
    border-bottom: 1px solid #002b65;
}

.global-navigation .sub-nav-root .sub-nav-group .sub-nav-item ul a {
    color: #c2cedf;
    text-decoration: none;
}

.global-navigation .sub-nav-root .sub-nav-group .sub-nav-item li a:hover {
    color: #ffffff;
}

.global-navigation .sub-nav-root .sub-nav-group .sub-nav-item a, .sub-nav-root .sub-nav-group a {
    font-size: 18px;
    padding: 14px 40px 9px 34px;
    position: relative;
    display: block;
}

.global-navigation .connector-area:not(.connector-area_first).active:after {
    opacity: 0;
}

.global-navigation .connector-area.connector-area_first.active:after {
    opacity: 1;
    transform: rotate(90deg);
}

.global-navigation .connector-area:after {
    top: 19px;
    transform: rotate(90deg);
}

.global-navigation ul.federal-bar-mobile {
    background-color: #2d2e33;
    padding-bottom: 60px;
}

.global-navigation .federal-bar-mobile > li a:link, .global-navigation .federal-bar-mobile > li a:visited, .global-navigation .federal-bar-mobile > li a:hover, .global-navigation .federal-bar-mobile > li a:active {
    display: block;
    padding: 13px 15px 14px 14px;
    font-size: 12px;
    text-transform: uppercase;
    color: #97989c;
    position: relative;
}

.global-navigation .federal-bar-mobile .custom-select-trigger {
    border: none;
    padding: 13px 15px 14px 14px;
    color: #97989c;
}

.global-navigation .custom-select-trigger > .icon {
    position: absolute;
    top: 50%;
    right: 16px;
}

.global-navigation select:focus ~ .custom-select-trigger.bg-transparent {
    outline: -webkit-focus-ring-color auto 5px;
}

}
/* To be clean by Alex*/