!function(e,r){"object"==typeof exports&&"object"==typeof module?module.exports=r(require("rxjs/Observable"),require("rxjs/Subject"),require("rxjs/operator/filter"),require("rxjs/operator/map"),require("rxjs/operator/switchMap"),require("rxjs/observable/from"),require("rxjs/observable/merge"),require("rxjs/observable/of")):"function"==typeof define&&define.amd?define(["rxjs/Observable","rxjs/Subject","rxjs/operator/filter","rxjs/operator/map","rxjs/operator/switchMap","rxjs/observable/from","rxjs/observable/merge","rxjs/observable/of"],r):"object"==typeof exports?exports.ReduxObservable=r(require("rxjs/Observable"),require("rxjs/Subject"),require("rxjs/operator/filter"),require("rxjs/operator/map"),require("rxjs/operator/switchMap"),require("rxjs/observable/from"),require("rxjs/observable/merge"),require("rxjs/observable/of")):e.ReduxObservable=r(e.Rx,e.Rx,e.Rx.Observable.prototype,e.Rx.Observable.prototype,e.Rx.Observable.prototype,e.Rx.Observable,e.Rx.Observable,e.Rx.Observable)}(this,function(e,r,t,n,o,u,i,a){return function(e){function r(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}var t={};return r.m=e,r.c=t,r.i=function(e){return e},r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:n})},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,r){return Object.prototype.hasOwnProperty.call(e,r)},r.p="",r(r.s=5)}([function(e,r,t){"use strict";function n(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function o(e,r){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!r||"object"!=typeof r&&"function"!=typeof r?e:r}function u(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function, not "+typeof r);e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(e,r):e.__proto__=r)}Object.defineProperty(r,"__esModule",{value:!0}),r.ActionsObservable=void 0;var i=function(){function e(e,r){for(var t=0;r.length>t;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(r,t,n){return t&&e(r.prototype,t),n&&e(r,n),r}}(),a=t(6),c=t(13),s=t(11),f=t(2);r.ActionsObservable=function(e){function r(e){n(this,r);var t=o(this,(r.__proto__||Object.getPrototypeOf(r)).call(this));return t.source=e,t}return u(r,e),i(r,null,[{key:"of",value:function(){return new this(c.of.apply(void 0,arguments))}},{key:"from",value:function(e,r){return new this((0,s.from)(e,r))}}]),i(r,[{key:"lift",value:function(e){var t=new r(this);return t.operator=e,t}},{key:"ofType",value:function(){return f.ofType.apply(void 0,arguments)(this)}}]),r}(a.Observable)},function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0});r.EPIC_END="@@redux-observable/EPIC_END"},function(e,r,t){"use strict";function n(){for(var e=arguments.length,r=Array(e),t=0;e>t;t++)r[t]=arguments[t];return function(e){return o.filter.call(e,function(e){var t=e.type,n=r.length;if(1===n)return u(t,r[0]);for(var o=0;n>o;o++)if(u(t,r[o]))return!0;return!1})}}Object.defineProperty(r,"__esModule",{value:!0}),r.ofType=n;var o=t(8),u=function(e,r){return e===r||"function"==typeof r&&e===""+r}},function(e,r,t){"use strict";function n(e){if(Array.isArray(e)){for(var r=0,t=Array(e.length);e.length>r;r++)t[r]=e[r];return t}return Array.from(e)}Object.defineProperty(r,"__esModule",{value:!0}),r.combineEpics=void 0;var o=t(12);r.combineEpics=function(){for(var e=arguments.length,r=Array(e),t=0;e>t;t++)r[t]=arguments[t];return function(){for(var e=arguments.length,t=Array(e),u=0;e>u;u++)t[u]=arguments[u];return o.merge.apply(void 0,n(r.map(function(e){var r=e.apply(void 0,t);if(!r)throw new TypeError('combineEpics: one of the provided Epics "'+(e.name||"<anonymous>")+"\" does not return a stream. Double check you're not missing a return statement!");return r})))}}},function(e,r,t){"use strict";function n(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p;if("function"!=typeof e)throw new TypeError("You must provide a root Epic to createEpicMiddleware");r=o({},p,r);var t=new u.Subject,n=r.adapter.input(new c.ActionsObservable(t)),f=new u.Subject,l=void 0,b=function(o){return l=o,function(o){var u;return(u=i.map.call(f,function(e){var t=l,o="dependencies"in r?e(n,t,r.dependencies):e(n,t);if(!o)throw new TypeError('Your root Epic "'+(e.name||"<anonymous>")+"\" does not return a stream. Double check you're not missing a return statement!");return o}),a.switchMap).call(u,function(e){return r.adapter.output(e)}).subscribe(function(e){try{l.dispatch(e)}catch(e){console.error(e)}}),f.next(e),function(e){var r=o(e);return t.next(e),r}}};return b.replaceEpic=function(e){l.dispatch({type:s.EPIC_END}),f.next(e)},b}Object.defineProperty(r,"__esModule",{value:!0});var o=Object.assign||function(e){for(var r=1;arguments.length>r;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e};r.createEpicMiddleware=n;var u=t(7),i=t(9),a=t(10),c=t(0),s=t(1),f={input:function(e){return e},output:function(e){return e}},p={adapter:f}},function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=t(4);Object.defineProperty(r,"createEpicMiddleware",{enumerable:!0,get:function(){return n.createEpicMiddleware}});var o=t(0);Object.defineProperty(r,"ActionsObservable",{enumerable:!0,get:function(){return o.ActionsObservable}});var u=t(3);Object.defineProperty(r,"combineEpics",{enumerable:!0,get:function(){return u.combineEpics}});var i=t(1);Object.defineProperty(r,"EPIC_END",{enumerable:!0,get:function(){return i.EPIC_END}});var a=t(2);Object.defineProperty(r,"ofType",{enumerable:!0,get:function(){return a.ofType}})},function(r,t){r.exports=e},function(e,t){e.exports=r},function(e,r){e.exports=t},function(e,r){e.exports=n},function(e,r){e.exports=o},function(e,r){e.exports=u},function(e,r){e.exports=i},function(e,r){e.exports=a}])});