import * as React from "react";
import BillExplainerBody from "../BillExplainerBody/BillExplainerBody";
import { PBELoader } from 'singleban-components';
import { IPBE, IPBEDataCache, IStoreState } from "../../models";
import { connect } from "react-redux";
import { getPBEDataFromActionURL, setIsLoadingPBE } from "../../store";
import { getPBECacheKey } from "../../utils/Utility";

interface Props {
    pbe: IPBE;
    locale: string; //triggers re-render when locale changed
    isPBEModalLinkDisabled: boolean;
}

interface MapStateToProps {
    isLoadingPBE: boolean;
    pbeDataCache: IPBEDataCache;
}

interface MapDispatchToProps {
    fetchAdditionalPBEData: (actionUrl: string, cacheKey: string) => void;
    resetIsLoadingPBE: () => void;
}

const PBEModalExplainer = (props: Props & MapStateToProps & MapDispatchToProps) => {
    const { isLoadingPBE, pbe, fetchAdditionalPBEData, pbeDataCache, resetIsLoadingPBE, isPBEModalLinkDisabled } = props;

    React.useEffect(() => {
        if (isLoadingPBE) {
            resetIsLoadingPBE();
        }
        const hasActionUrl = pbe?.actionUrl !== "" && pbe?.actionUrl !== null && pbe?.actionUrl !== undefined;
        const cacheKey = getPBECacheKey(pbe);
        const existsInCache = pbeDataCache[cacheKey] !== undefined && pbeDataCache[cacheKey] !== null;
        if (hasActionUrl && !existsInCache) {
            fetchAdditionalPBEData(pbe.actionUrl, cacheKey);
        }
    }, [pbe])
    
    return isLoadingPBE ? <PBELoader /> : <BillExplainerBody {...props as any} />;
};

const mapStateToProps = (state: IStoreState): MapStateToProps => {
    return (
        {
            isLoadingPBE: state.isLoadingPBE,
            pbeDataCache: state.pbeDataCache
        }
    );
};

const mapDispatchToProps = (dispatch: any): MapDispatchToProps => {
    return {
        fetchAdditionalPBEData: (actionUrl: string, cacheKey: string) => dispatch(getPBEDataFromActionURL({ actionUrl, cacheKey })),
        resetIsLoadingPBE: () => dispatch((setIsLoadingPBE(false)))
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(PBEModalExplainer);