var t,e;t=window,e=function(){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var i=e[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:r})},n.r=function(t){Object.defineProperty(t,"__esModule",{value:!0})},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=24)}([function(t,e,n){"use strict";(function(e){function r(t,e){if(t===e)return 0;for(var n=t.length,r=e.length,i=0,o=Math.min(n,r);i<o;++i)if(t[i]!==e[i]){n=t[i],r=e[i];break}return n<r?-1:r<n?1:0}function i(t){return e.Buffer&&"function"==typeof e.Buffer.isBuffer?e.Buffer.isBuffer(t):!(null==t||!t._isBuffer)}var o=n(3),s=Object.prototype.hasOwnProperty,u=Array.prototype.slice,c="foo"===function(){}.name;function a(t){return Object.prototype.toString.call(t)}function f(t){return!i(t)&&"function"==typeof e.ArrayBuffer&&("function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(t):!!t&&(t instanceof DataView||!!(t.buffer&&t.buffer instanceof ArrayBuffer)))}var l=t.exports=d,p=/\s*function\s+([^\(\s]*)\s*/;function h(t){if(o.isFunction(t)){if(c)return t.name;var e=t.toString().match(p);return e&&e[1]}}function v(t,e){return"string"==typeof t?t.length<e?t:t.slice(0,e):t}function y(t){if(c||!o.isFunction(t))return o.inspect(t);var e=h(t);return"[Function"+(e?": "+e:"")+"]"}function g(t,e,n,r,i){throw new l.AssertionError({message:n,actual:t,expected:e,operator:r,stackStartFunction:i})}function d(t,e){t||g(t,!0,e,"==",l.ok)}function m(t,e,n,s){if(t===e)return!0;if(i(t)&&i(e))return 0===r(t,e);if(o.isDate(t)&&o.isDate(e))return t.getTime()===e.getTime();if(o.isRegExp(t)&&o.isRegExp(e))return t.source===e.source&&t.global===e.global&&t.multiline===e.multiline&&t.lastIndex===e.lastIndex&&t.ignoreCase===e.ignoreCase;if(null!==t&&"object"==typeof t||null!==e&&"object"==typeof e){if(f(t)&&f(e)&&a(t)===a(e)&&!(t instanceof Float32Array||t instanceof Float64Array))return 0===r(new Uint8Array(t.buffer),new Uint8Array(e.buffer));if(i(t)!==i(e))return!1;var c=(s=s||{actual:[],expected:[]}).actual.indexOf(t);return-1!==c&&c===s.expected.indexOf(e)||(s.actual.push(t),s.expected.push(e),function(t,e,n,r){if(null===t||void 0===t||null===e||void 0===e)return!1;if(o.isPrimitive(t)||o.isPrimitive(e))return t===e;if(n&&Object.getPrototypeOf(t)!==Object.getPrototypeOf(e))return!1;var i=b(t),s=b(e);if(i&&!s||!i&&s)return!1;if(i)return m(t=u.call(t),e=u.call(e),n);var c,a,f=x(t),l=x(e);if(f.length!==l.length)return!1;for(f.sort(),l.sort(),a=f.length-1;a>=0;a--)if(f[a]!==l[a])return!1;for(a=f.length-1;a>=0;a--)if(!m(t[c=f[a]],e[c],n,r))return!1;return!0}(t,e,n,s))}return n?t===e:t==e}function b(t){return"[object Arguments]"==Object.prototype.toString.call(t)}function w(t,e){if(!t||!e)return!1;if("[object RegExp]"==Object.prototype.toString.call(e))return e.test(t);try{if(t instanceof e)return!0}catch(t){}return!Error.isPrototypeOf(e)&&!0===e.call({},t)}function _(t,e,n,r){var i;if("function"!=typeof e)throw new TypeError('"block" argument must be a function');"string"==typeof n&&(r=n,n=null),i=function(t){var e;try{t()}catch(t){e=t}return e}(e),r=(n&&n.name?" ("+n.name+").":".")+(r?" "+r:"."),t&&!i&&g(i,n,"Missing expected exception"+r);var s="string"==typeof r,u=!t&&o.isError(i),c=!t&&i&&!n;if((u&&s&&w(i,n)||c)&&g(i,n,"Got unwanted exception"+r),t&&i&&n&&!w(i,n)||!t&&i)throw i}l.AssertionError=function(t){var e;this.name="AssertionError",this.actual=t.actual,this.expected=t.expected,this.operator=t.operator,t.message?(this.message=t.message,this.generatedMessage=!1):(this.message=v(y((e=this).actual),128)+" "+e.operator+" "+v(y(e.expected),128),this.generatedMessage=!0);var n=t.stackStartFunction||g;if(Error.captureStackTrace)Error.captureStackTrace(this,n);else{var r=new Error;if(r.stack){var i=r.stack,o=h(n),s=i.indexOf("\n"+o);if(s>=0){var u=i.indexOf("\n",s+1);i=i.substring(u+1)}this.stack=i}}},o.inherits(l.AssertionError,Error),l.fail=g,l.ok=d,l.equal=function(t,e,n){t!=e&&g(t,e,n,"==",l.equal)},l.notEqual=function(t,e,n){t==e&&g(t,e,n,"!=",l.notEqual)},l.deepEqual=function(t,e,n){m(t,e,!1)||g(t,e,n,"deepEqual",l.deepEqual)},l.deepStrictEqual=function(t,e,n){m(t,e,!0)||g(t,e,n,"deepStrictEqual",l.deepStrictEqual)},l.notDeepEqual=function(t,e,n){m(t,e,!1)&&g(t,e,n,"notDeepEqual",l.notDeepEqual)},l.notDeepStrictEqual=function t(e,n,r){m(e,n,!0)&&g(e,n,r,"notDeepStrictEqual",t)},l.strictEqual=function(t,e,n){t!==e&&g(t,e,n,"===",l.strictEqual)},l.notStrictEqual=function(t,e,n){t===e&&g(t,e,n,"!==",l.notStrictEqual)},l.throws=function(t,e,n){_(!0,t,e,n)},l.doesNotThrow=function(t,e,n){_(!1,t,e,n)},l.ifError=function(t){if(t)throw t};var x=Object.keys||function(t){var e=[];for(var n in t)s.call(t,n)&&e.push(n);return e}}).call(this,n(11))},function(t,e,n){var r=n(0);t.exports=new function(){"use strict";var t=this;t.__typename__="scarlet.lib.extensions.Enumerable",t.arrayFor=function(t,e){for(var n=0;n<t.length;n++)e(t[n],n,t)},t.funcFor=function(t,e){for(var n in t)e(t[n],n,t)},t.stringFor=function(e,n){t.arrayFor(e.split(""),function(t,r){n(t,r,e)})},t.allEach=function(t,e){Object.getOwnPropertyNames(t).forEach(function(n){e(t[n],n,t)})},t.forEach=function(e,n){if(e){var r=t.funcFor;e instanceof Function?r=t.funcFor:"string"==typeof e?r=t.stringFor:"number"==typeof e.length&&(r=t.arrayFor),r(e,n)}},t.any=function(e,n){var r=!1;return t.forEach(e,function(t,e){n(t,e)&&(r=!0)}),r},t.where=function(e,n){var r=[];return t.forEach(e,function(t){n(t)&&r.push(t)}),r},t.first=function(e,n){if(void 0===n&&void 0!==e&&e.length>0)return e[0];var r=t.where(e,n);return r.length>0?r[0]:null},t.mapSeries=function(e,n,i){if(r("number"==typeof e.length,"Object to map must be an array"),!this)return new t.mapSeries(e,n,i);var o=[],s=this,u=0,c=null;s._proceed=function(t,r){void 0!==r&&o.push(r);var a=function(){var t=e[u];return u++,t}();return a?n(t,a,s._proceed):i&&(c=i(t,o)),c},s._proceed()}}},function(t,e,n){t.exports=new function(){"use strict";n(1),this.__typename__="scarlet.lib.extensions.Object",this.has=function(t,e){return hasOwnProperty.call(t,e)},this.isObject=function(t){return t instanceof Object},this.isFunction=function(t){return t instanceof Function},this.isNull=function(t){return"object"==typeof t?null===t:void 0===t},this.inherit=function(t,e){t.__super__=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,writable:!0,enumerable:!1,configurable:!0}})},this.name=function(t){if(!t)return"undefined";if(t.name)return t.name;if(t.constructor&&t.constructor.name)return t.constructor.name;var e=/function\s([^(]{1,})\(/.exec(t.toString());return e&&e.length>1?e[1].trim():t instanceof Function?"Function":"Object"},this.objectHasFunction=function(t,e){for(var n in t)if(t[n]==e)return!0;return!1},this.extend=function(t,e){for(var n in t)e[n]=t[n]}}},function(t,e,n){(function(t,r){var i=/%[sdj%]/g;e.format=function(t){if(!d(t)){for(var e=[],n=0;n<arguments.length;n++)e.push(u(arguments[n]));return e.join(" ")}n=1;for(var r=arguments,o=r.length,s=String(t).replace(i,function(t){if("%%"===t)return"%";if(n>=o)return t;switch(t){case"%s":return String(r[n++]);case"%d":return Number(r[n++]);case"%j":try{return JSON.stringify(r[n++])}catch(t){return"[Circular]"}default:return t}}),c=r[n];n<o;c=r[++n])y(c)||!w(c)?s+=" "+c:s+=" "+u(c);return s},e.deprecate=function(n,i){if(m(t.process))return function(){return e.deprecate(n,i).apply(this,arguments)};if(!0===r.noDeprecation)return n;var o=!1;return function(){if(!o){if(r.throwDeprecation)throw new Error(i);r.traceDeprecation,o=!0}return n.apply(this,arguments)}};var o,s={};function u(t,n){var r={seen:[],stylize:a};return arguments.length>=3&&(r.depth=arguments[2]),arguments.length>=4&&(r.colors=arguments[3]),v(n)?r.showHidden=n:n&&e._extend(r,n),m(r.showHidden)&&(r.showHidden=!1),m(r.depth)&&(r.depth=2),m(r.colors)&&(r.colors=!1),m(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=c),f(r,t,r.depth)}function c(t,e){var n=u.styles[e];return n?"["+u.colors[n][0]+"m"+t+"["+u.colors[n][1]+"m":t}function a(t,e){return t}function f(t,n,r){if(t.customInspect&&n&&E(n.inspect)&&n.inspect!==e.inspect&&(!n.constructor||n.constructor.prototype!==n)){var i=n.inspect(r,t);return d(i)||(i=f(t,i,r)),i}var o=function(t,e){if(m(e))return t.stylize("undefined","undefined");if(d(e)){var n="'"+JSON.stringify(e).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return t.stylize(n,"string")}return g(e)?t.stylize(""+e,"number"):v(e)?t.stylize(""+e,"boolean"):y(e)?t.stylize("null","null"):void 0}(t,n);if(o)return o;var s=Object.keys(n),u=function(t){var e={};return s.forEach(function(t,n){e[t]=!0}),e}();if(t.showHidden&&(s=Object.getOwnPropertyNames(n)),x(n)&&(s.indexOf("message")>=0||s.indexOf("description")>=0))return l(n);if(0===s.length){if(E(n)){var c=n.name?": "+n.name:"";return t.stylize("[Function"+c+"]","special")}if(b(n))return t.stylize(RegExp.prototype.toString.call(n),"regexp");if(_(n))return t.stylize(Date.prototype.toString.call(n),"date");if(x(n))return l(n)}var a,w="",O=!1,S=["{","}"];return h(n)&&(O=!0,S=["[","]"]),E(n)&&(w=" [Function"+(n.name?": "+n.name:"")+"]"),b(n)&&(w=" "+RegExp.prototype.toString.call(n)),_(n)&&(w=" "+Date.prototype.toUTCString.call(n)),x(n)&&(w=" "+l(n)),0!==s.length||O&&0!=n.length?r<0?b(n)?t.stylize(RegExp.prototype.toString.call(n),"regexp"):t.stylize("[Object]","special"):(t.seen.push(n),a=O?function(t,e,n,r,i){for(var o=[],s=0,u=e.length;s<u;++s)j(e,String(s))?o.push(p(t,e,n,r,String(s),!0)):o.push("");return i.forEach(function(i){i.match(/^\d+$/)||o.push(p(t,e,n,r,i,!0))}),o}(t,n,r,u,s):s.map(function(e){return p(t,n,r,u,e,O)}),t.seen.pop(),function(t,e,n){return t.reduce(function(t,e){return e.indexOf("\n"),t+e.replace(/\u001b\[\d\d?m/g,"").length+1},0)>60?n[0]+(""===e?"":e+"\n ")+" "+t.join(",\n  ")+" "+n[1]:n[0]+e+" "+t.join(", ")+" "+n[1]}(a,w,S)):S[0]+w+S[1]}function l(t){return"["+Error.prototype.toString.call(t)+"]"}function p(t,e,n,r,i,o){var s,u,c;if((c=Object.getOwnPropertyDescriptor(e,i)||{value:e[i]}).get?u=c.set?t.stylize("[Getter/Setter]","special"):t.stylize("[Getter]","special"):c.set&&(u=t.stylize("[Setter]","special")),j(r,i)||(s="["+i+"]"),u||(t.seen.indexOf(c.value)<0?(u=y(n)?f(t,c.value,null):f(t,c.value,n-1)).indexOf("\n")>-1&&(u=o?u.split("\n").map(function(t){return"  "+t}).join("\n").substr(2):"\n"+u.split("\n").map(function(t){return"   "+t}).join("\n")):u=t.stylize("[Circular]","special")),m(s)){if(o&&i.match(/^\d+$/))return u;(s=JSON.stringify(""+i)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(s=s.substr(1,s.length-2),s=t.stylize(s,"name")):(s=s.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),s=t.stylize(s,"string"))}return s+": "+u}function h(t){return Array.isArray(t)}function v(t){return"boolean"==typeof t}function y(t){return null===t}function g(t){return"number"==typeof t}function d(t){return"string"==typeof t}function m(t){return void 0===t}function b(t){return w(t)&&"[object RegExp]"===O(t)}function w(t){return"object"==typeof t&&null!==t}function _(t){return w(t)&&"[object Date]"===O(t)}function x(t){return w(t)&&("[object Error]"===O(t)||t instanceof Error)}function E(t){return"function"==typeof t}function O(t){return Object.prototype.toString.call(t)}function j(t,e){return Object.prototype.hasOwnProperty.call(t,e)}e.debuglog=function(t){return m(o)&&(o=r.env.NODE_DEBUG||""),t=t.toUpperCase(),s[t]||(new RegExp("\\b"+t+"\\b","i").test(o)?(r.pid,s[t]=function(){e.format.apply(e,arguments)}):s[t]=function(){}),s[t]},e.inspect=u,u.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},u.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},e.isArray=h,e.isBoolean=v,e.isNull=y,e.isNullOrUndefined=function(t){return null==t},e.isNumber=g,e.isString=d,e.isSymbol=function(t){return"symbol"==typeof t},e.isUndefined=m,e.isRegExp=b,e.isObject=w,e.isDate=_,e.isError=x,e.isFunction=E,e.isPrimitive=function(t){return null===t||"boolean"==typeof t||"number"==typeof t||"string"==typeof t||"symbol"==typeof t||void 0===t},e.isBuffer=n(22),e.log=function(){},e.inherits=n(21),e._extend=function(t,e){if(!e||!w(e))return t;for(var n=Object.keys(e),r=n.length;r--;)t[n[r]]=e[n[r]];return t}}).call(this,n(11),n(10))},function(t,e,n){(function(e){var r=n(14),i=n(0);t.exports=function(){"use strict";var t=this;t.directoryPath=e+"/../../../",t.setDirectory=function(e){t.directoryPath=e},t.load=function(e,o){i(e);var s=r.normalize(t.directoryPath+o),u=new(n(13)(s+""))(e);return u.initialize(),u}}}).call(this,"/")},function(t,e,n){var r=n(0),i=n(2),o=n(9);t.exports=function t(e,n){"use strict";return this instanceof t?(r(e),this.__typename__="scarlet.lib.proxies.ProxyMetadata",this.reflection={},this.hasShadow=function(){return i.has(e,"__scarlet__")},this.ensureShadow=function(){return this.hasShadow()||(e.__scarlet__={}),o.debug(t,"ensureShadow","Shadow Object Created",[e]),this},this.reflection.isAllowed=function(){var e="__scarlet__"!=n;return o.debug(t,"isAllowed","Is Allowed For Proxy?",[e]),e},this.reflection.isMethod=function(){var r=!i.isNull(e)&&!i.isNull(n)&&!i.isNull(e[n])&&i.isFunction(e[n]);return o.info(t,"isMethod","Is Method?",[r]),r},this.reflection.isFunction=function(){var r=i.isNull(n)&&i.isFunction(e);return o.info(t,"isFunction","Is Function?",[r]),r},this.reflection.isProperty=function(){var r=!i.isFunction(e[n]);return o.info(t,"isProperty","Is Property?",[r]),r},this.reflection.isInstance=function(){this.isFunction();var r=i.isNull(n)&&i.isObject(e);return o.info(t,"isInstance","Is Instance?",[r]),r},this.reflection.isPrototype=function(){var r=i.isNull(n)&&i.isFunction(e)&&!i.isNull(e.prototype);return o.info(t,"isPrototype","Is Prototype?",[r]),r},this.reflection.hasMember=function(){return!i.isNull(n)},this):new t(e,n)}},function(t,e,n){var r=n(0),i=n(8),o=n(5),s=n(7),u=n(1);t.exports=function t(e){"use strict";if(!(this instanceof t))return new t(e);r(e);var n=[];this.__typename__="scarlet.lib.proxies.ProxyInstance",this.wrap=function(t){return r(t),c(e,function(e){e.wrap(t),n.push(e)}),this},this.unwrap=function(){return u.forEach(n,function(t){t.unwrap()}),this};var c=function(t,e){e&&u.forEach(t,function(n,u){r(u);var c=new o(t,u).ensureShadow();if(c.reflection.isAllowed())return c.reflection.isMethod()?e(new i(t,u)):c.reflection.isProperty()?e(new s(t,u)):void 0})}}},function(t,e,n){var r=n(0);t.exports=function(t,e){"use strict";r(e),r(t);var n=t[e];this.__typename__="scarlet.lib.proxies.ProxyProperty",this.wrap=function(n){return r(n),Object.defineProperty(t,e,{get:function(){return i(n)},set:function(t){o(n,t)}}),this};var i=function(r){return r.call(t,e,function(){return n})},o=function(r,i){return r.call(t,e,function(){n=i})};this.unwrap=function(){return delete t[e],t[e]=n,this}}},function(t,e,n){var r=n(0);t.exports=function(t,e){"use strict";r(e),r(t);var n=t[e];this.__typename__="scarlet.lib.proxies.ProxyMethod",this.wrap=function(i){return r(i),t[e]=function(){var r=Array.prototype.slice.call(arguments);return i.call(t,e,n,r)},this},this.unwrap=function(){return t[e]=n,this}}},function(t,e,n){var r=n(3).inspect,i=(n(0),n(2));t.exports=new function(){"use strict";var t=this;t.DEBUG=4,t.INFO=3,t.WARN=2,t.ERROR=1,t.NONE=0,t.logLevel=t.NONE,t.log=console.log;var e=function(t){if("string"==typeof t)return t;var e=t.toString();return""!==(e=(e=e.substr("function ".length)).substr(0,e.indexOf("(")))&&null!==e&&void 0!==e||(e="function<anonymous>"),e},n=function(t){var e="";"string"==typeof t?e+=t:"string"!=typeof t&&(e=t.toString()),"[object Object]"==e&&(e+="");var n=e.indexOf("("),r=e.indexOf(")");return-1===n||-1===r?[]:(e=e.slice(e.indexOf("(")+1,e.indexOf(")"))).split(",")};t.print=function(o,s,u,c,a){if(a=null===a?"":"\n"+r(a).replace(/\n/g,"\n"),"object"!=typeof c&&"function"!=typeof c||(c=r(c,{depth:10,showHidden:!0})),"string"!=typeof u||i.isNull(s)||i.isNull(s[u]))i.isNull(s)||!i.isNull(u)?!i.isNull(s)||i.isNull(u)?i.isNull(s)||i.isNull(u)||t.log(o+e(s)+"::"+e(u)+"("+n(u).join(",")+") - "+c+a+"\n"):t.log(o+e(u)+"("+n(u).join(",")+") - "+c+a+"\n"):t.log(o+e(u)+"("+n(s).join(",")+") - "+c+a+"\n");else{var f=u,l=s[u];t.log(o+e(s)+"::"+f+"("+n(l).join(",")+") - "+c+a+"\n")}},t.debug=function(e,n,r,i){t.logLevel==t.DEBUG&&t.print("DEBUG @ ["+(new Date).toString()+"] -> ",e,n,r,i)},t.info=function(e,n,r,i){t.logLevel>=t.INFO&&t.print("INFO  @ ["+(new Date).toString()+"] -> ",e,n,r,i)},t.warn=function(e,n,r,i){t.logLevel>=t.WARN&&t.print("WARN  @ ["+(new Date).toString()+"] -> ",e,n,r,i)},t.error=function(e,n,r,i){t.logLevel>=t.ERROR&&t.print("ERROR @ ["+(new Date).toString()+"] -> ",e,n,r,i)}}},function(t,e){var n,r,i=t.exports={};function o(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function u(t){if(n===setTimeout)return setTimeout(t,0);if((n===o||!n)&&setTimeout)return n=setTimeout,setTimeout(t,0);try{return n(t,0)}catch(e){try{return n.call(null,t,0)}catch(e){return n.call(this,t,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:o}catch(t){n=o}try{r="function"==typeof clearTimeout?clearTimeout:s}catch(t){r=s}}();var c,a=[],f=!1,l=-1;function p(){f&&c&&(f=!1,c.length?a=c.concat(a):l=-1,a.length&&h())}function h(){if(!f){var t=u(p);f=!0;for(var e=a.length;e;){for(c=a,a=[];++l<e;)c&&c[l].run();l=-1,e=a.length}c=null,f=!1,function(t){if(r===clearTimeout)return clearTimeout(t);if((r===s||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(t);try{r(t)}catch(e){try{return r.call(null,t)}catch(e){return r.call(this,t)}}}(t)}}function v(t,e){this.fun=t,this.array=e}function y(){}i.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];a.push(new v(t,e)),1!==a.length||f||u(h)},v.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=y,i.addListener=y,i.once=y,i.off=y,i.removeListener=y,i.removeAllListeners=y,i.emit=y,i.prependListener=y,i.prependOnceListener=y,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(t,e){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(t){"object"==typeof window&&(n=window)}t.exports=n},function(t,e,n){var r=n(0);t.exports=function(t,e){"use strict";r(e,"info=null"),r(t,"proceed=null");var n=this;n.args=e.args,n.result=e.result,n.memberName=e.memberName,n.hasResult=void 0!==e.result,n.hasArgs=void 0!==e.args,n.argsEmpty=null!==e.args&&void 0!==e.args&&0===e.args.length,n.traceTo=function(t){var r=n.hasResult?e.result:"void";t((e.memberName?e.memberName:"")+"("+(!n.hasArgs||n.argsEmpty?"":JSON.stringify(e.args))+"):"+r)}}},function(t,e,n){var r={"./plugin-manager":4,"./plugin-manager.js":4};function i(t){var e=o(t);return n(e)}function o(t){var e=r[t];if(!(e+1)){var n=new Error('Cannot find module "'+t+'".');throw n.code="MODULE_NOT_FOUND",n}return e}i.keys=function(){return Object.keys(r)},i.resolve=o,t.exports=i,i.id=13},function(t,e,n){(function(t){function n(t,e){for(var n=0,r=t.length-1;r>=0;r--){var i=t[r];"."===i?t.splice(r,1):".."===i?(t.splice(r,1),n++):n&&(t.splice(r,1),n--)}if(e)for(;n--;n)t.unshift("..");return t}var r=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/,i=function(t){return r.exec(t).slice(1)};function o(t,e){if(t.filter)return t.filter(e);for(var n=[],r=0;r<t.length;r++)e(t[r],r,t)&&n.push(t[r]);return n}e.resolve=function(){for(var e="",r=!1,i=arguments.length-1;i>=-1&&!r;i--){var s=i>=0?arguments[i]:t.cwd();if("string"!=typeof s)throw new TypeError("Arguments to path.resolve must be strings");s&&(e=s+"/"+e,r="/"===s.charAt(0))}return e=n(o(e.split("/"),function(t){return!!t}),!r).join("/"),(r?"/":"")+e||"."},e.normalize=function(t){var r=e.isAbsolute(t),i="/"===s(t,-1);return(t=n(o(t.split("/"),function(t){return!!t}),!r).join("/"))||r||(t="."),t&&i&&(t+="/"),(r?"/":"")+t},e.isAbsolute=function(t){return"/"===t.charAt(0)},e.join=function(){var t=Array.prototype.slice.call(arguments,0);return e.normalize(o(t,function(t,e){if("string"!=typeof t)throw new TypeError("Arguments to path.join must be strings");return t}).join("/"))},e.relative=function(t,n){function r(t){for(var e=0;e<t.length&&""===t[e];e++);for(var n=t.length-1;n>=0&&""===t[n];n--);return e>n?[]:t.slice(e,n-e+1)}t=e.resolve(t).substr(1),n=e.resolve(n).substr(1);for(var i=r(t.split("/")),o=r(n.split("/")),s=Math.min(i.length,o.length),u=s,c=0;c<s;c++)if(i[c]!==o[c]){u=c;break}var a=[];for(c=u;c<i.length;c++)a.push("..");return(a=a.concat(o.slice(u))).join("/")},e.sep="/",e.delimiter=":",e.dirname=function(t){var e=i(t),n=e[0],r=e[1];return n||r?(r&&(r=r.substr(0,r.length-1)),n+r):"."},e.basename=function(t,e){var n=i(t)[2];return e&&n.substr(-1*e.length)===e&&(n=n.substr(0,n.length-e.length)),n},e.extname=function(t){return i(t)[3]};var s="b"==="ab".substr(-1)?function(t,e,n){return t.substr(e,n)}:function(t,e,n){return e<0&&(e=t.length+e),t.substr(e,n)}}).call(this,n(10))},function(t,e,n){var r=n(3),i=n(0),o=n(2),s=n(5),u=n(6);n(1),t.exports=function(t){"use strict";i(t),i(t.prototype);var e=this;e.whenCalled=null,e.__typename__="scarlet.lib.proxies.ProxyPrototype",e.wrap=function(o,s){return i(o),e.whenCalled=o,r.inherits(n,t),s&&s(n),e},e.unwrap=function(){return t};var n=function(){var n=this||{},r=Array.prototype.slice.call(arguments),i=c(n);return e.whenCalled.call(t.name,n,i,r)},c=function(e){return function(){var n=Array.prototype.slice.call(arguments),r=t.apply(e,n);return a(e),r}},a=function(t){s(t).hasShadow()||(s(t).ensureShadow(),o.objectHasFunction(t,n)||u(t).wrap(e.whenCalled))}}},function(t,e,n){var r=n(0);t.exports=function(t){"use strict";r(t),this.__typename__="scarlet.lib.proxies.ProxyFunction",this.wrap=function(e,n){return r(e),n&&n(function(){var n=Array.prototype.slice.call(arguments);return e.call(this,t.name,t,n)}),this},this.unwrap=function(){return t}}},function(t,e,n){var r=n(0),i=n(8),o=n(5),s=n(7),u=n(6),c=n(16),a=n(15);t.exports=function(t,e){"use strict";r(t),this.proxy=null,this.__typename__="scarlet.lib.proxies.ProxyInterceptor",this.intercept=function(i,s){r(i),r(s),o(t,e).ensureShadow(),this.proxy=n().wrap(i,s)},this.release=function(){this.proxy&&this.proxy.unwrap()};var n=function(){return e?f():t.prototype?new a(t):"function"==typeof t?new c(t):new u(t)},f=function(){if(e)return"function"==typeof t[e]?new i(t,e):new s(t,e)}}},function(t,e,n){var r=n(0),i=n(2);t.exports=function(t,e,n,o){"use strict";r(n,"invocationMethod == null");var s=this;o||(o=[]),s.args=o,s.context=t,s.result=null,s.method=n,s.contextName=function(){return i.name(s.context)},s.memberName=function(){return e||i.name(s.method)},s.executionStartDate=null,s.executionEndDate=null,s.proceed=function(){var t=Array.prototype.slice.call(s.args);return s.executionStartDate=new Date,s.result=s.method.apply(s.context,t),s.executionEndDate=new Date,s.result}}},function(t,e,n){var r=n(0),i=n(18),o=n(1),s=n(17);t.exports=function(){"use strict";var t=this;t.proxy=null,t.interceptors=[],t.__typename__="scarlet.lib.interceptors.Interceptor",t.intercept=function(n,i,o){return r(n),r(o),t.proxy=new s(n,i),t.proxy.intercept(e,o),t},t.using=function(e){return r(t.proxy,"Please make sure you are intercepting something first"),t.interceptors.push(e),t};var e=function(e,o,s){r(t.interceptors.length>0,"Please make sure you add an interceptor");var u=new i(this,e,o,s);return n(this,u,function(t,e){if(t)throw t;return u.result=u.proceed(),e.length>0&&(u.result=e[e.length-1]),u.result}),u.result},n=function(e,n,r){o.mapSeries(t.interceptors,function(t,r,i){return t&&u(r)<3?i(t):1===u(r)?r.call(e,i):2===u(r)?r.call(e,n,i):r.call(e,t,n,i)},r)}};var u=function(t){return"function"!=typeof t?0:t.length}},function(t,e){function n(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}function r(t){return"function"==typeof t}function i(t){return"object"==typeof t&&null!==t}function o(t){return void 0===t}t.exports=n,n.EventEmitter=n,n.prototype._events=void 0,n.prototype._maxListeners=void 0,n.defaultMaxListeners=10,n.prototype.setMaxListeners=function(t){if("number"!=typeof t||t<0||isNaN(t))throw TypeError("n must be a positive number");return this._maxListeners=t,this},n.prototype.emit=function(t){var e,n,s,u,c,a;if(this._events||(this._events={}),"error"===t&&(!this._events.error||i(this._events.error)&&!this._events.error.length)){if((e=arguments[1])instanceof Error)throw e;var f=new Error('Uncaught, unspecified "error" event. ('+e+")");throw f.context=e,f}if(o(n=this._events[t]))return!1;if(r(n))switch(arguments.length){case 1:n.call(this);break;case 2:n.call(this,arguments[1]);break;case 3:n.call(this,arguments[1],arguments[2]);break;default:u=Array.prototype.slice.call(arguments,1),n.apply(this,u)}else if(i(n))for(u=Array.prototype.slice.call(arguments,1),s=(a=n.slice()).length,c=0;c<s;c++)a[c].apply(this,u);return!0},n.prototype.addListener=function(t,e){var s;if(!r(e))throw TypeError("listener must be a function");return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",t,r(e.listener)?e.listener:e),this._events[t]?i(this._events[t])?this._events[t].push(e):this._events[t]=[this._events[t],e]:this._events[t]=e,i(this._events[t])&&!this._events[t].warned&&(s=o(this._maxListeners)?n.defaultMaxListeners:this._maxListeners)&&s>0&&this._events[t].length>s&&(this._events[t].warned=!0,console.trace),this},n.prototype.on=n.prototype.addListener,n.prototype.once=function(t,e){if(!r(e))throw TypeError("listener must be a function");var n=!1;function i(){this.removeListener(t,i),n||(n=!0,e.apply(this,arguments))}return i.listener=e,this.on(t,i),this},n.prototype.removeListener=function(t,e){var n,o,s,u;if(!r(e))throw TypeError("listener must be a function");if(!this._events||!this._events[t])return this;if(s=(n=this._events[t]).length,o=-1,n===e||r(n.listener)&&n.listener===e)delete this._events[t],this._events.removeListener&&this.emit("removeListener",t,e);else if(i(n)){for(u=s;u-- >0;)if(n[u]===e||n[u].listener&&n[u].listener===e){o=u;break}if(o<0)return this;1===n.length?(n.length=0,delete this._events[t]):n.splice(o,1),this._events.removeListener&&this.emit("removeListener",t,e)}return this},n.prototype.removeAllListeners=function(t){var e,n;if(!this._events)return this;if(!this._events.removeListener)return 0===arguments.length?this._events={}:this._events[t]&&delete this._events[t],this;if(0===arguments.length){for(e in this._events)"removeListener"!==e&&this.removeAllListeners(e);return this.removeAllListeners("removeListener"),this._events={},this}if(r(n=this._events[t]))this.removeListener(t,n);else if(n)for(;n.length;)this.removeListener(t,n[n.length-1]);return delete this._events[t],this},n.prototype.listeners=function(t){return this._events&&this._events[t]?r(this._events[t])?[this._events[t]]:this._events[t].slice():[]},n.prototype.listenerCount=function(t){if(this._events){var e=this._events[t];if(r(e))return 1;if(e)return e.length}return 0},n.listenerCount=function(t,e){return t.listenerCount(e)}},function(t,e){"function"==typeof Object.create?t.exports=function(t,e){t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}})}:t.exports=function(t,e){t.super_=e;var n=function(){};n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t}},function(t,e){t.exports=function(t){return t&&"object"==typeof t&&"function"==typeof t.copy&&"function"==typeof t.fill&&"function"==typeof t.readUInt8}},function(t,e,n){var r=n(3),i=n(20),o=n(0),s=n(9),u=n(19),c=n(4),a=n(12);function f(t){"use strict";if(!(this instanceof f))return new f(t);var e=null,n=new c,r=this;r.plugins={},r.__typename__="scarlet.lib.Scarlet",r.intercept=function(t,n){return o(t,"Please make sure you supply a typeOrInstance parameter. eg. scarlet.intercept(MyFunc, scarlet.type.asInstance());"),s.info(f,"intercept","For Type Or Instance",[t]),(e=new u).observable=t,e.intercept(t,n,function(t){e.observable=t}),e.using(r.beforeEventEmitterInterceptor),r},r.using=function(t){return o(t),o(e),s.info(f,"using","Using Interceptor",[t]),e.using(t),r},r.beforeEventEmitterInterceptor=function(t,n){o(e),r.emit("before",t),n()},r.afterEventEmitterInterceptor=function(t,n,i){if(o(e),void 0!==t&&null!==t)throw n.error=t,r.emit("error",n),t;var s=null;try{s=i(),r.emit("after",n)}catch(t){throw n.error=t,r.emit("error",n),t}return r.emit("done",n),s},r.proxy=function(){return o(e),o(e.observable),s.info(f,"proxy","As Proxied Type Or Instance",[e.observable]),e.using(r.afterEventEmitterInterceptor),e.observable},r.load=function(t){return o(t),n.load(r,t),r},r.interceptQuery=function(t,e){return new a(t,e)},r.on("error",function(t){s.error(f,"Event","error event",[t])}),"string"==typeof t&&(t=[t]),t&&t.length&&t.forEach(function(t){r.load(t)}),i.EventEmitter.call(r)}r.inherits(f,i.EventEmitter),t.exports=f},function(t,e,n){t.exports=n(23)}])},"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.Scarlet=e():t.Scarlet=e();