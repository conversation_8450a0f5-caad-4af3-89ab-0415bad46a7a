function SwapContentPosition() {
    if ($('#elementContainer2').length > 0 && $('#elementContainer3').length > 0) {
        if (window.matchMedia('(min-width: 992px)').matches) {
            $('#elementContainer2').before($('#elementContainer3'));
        }


        $(window).resize(function () {
            if (window.matchMedia('(max-width: 991px)').matches) {
                if ($('#elementContainer2').prev().attr('id') != undefined) {
                    $('#elementContainer2').after($('#elementContainer3'));
                }
            }
            else {
                if ($('#elementContainer2').next().attr('id') != undefined) {
                    $('#elementContainer2').before($('#elementContainer3'));
                }
            }
        });
    }

}

function AddSurveyButtonEventListener() {
    if ($('a.rating-icon-button').length > 0) {
        $('a.rating-icon-button').click(function (e) {
            if ($(this).parent().attr('rating-selected') == undefined) {
                $('.rating-buttons-cont div[rating-selected]').removeAttr('rating-selected');
                $(this).parent().attr('rating-selected', $(this).attr('aria-controls'));
                $('.survey-content-cont textarea').val('');
                //$(this).focus();
            }

        });
    }
   
}

function cssScrollCustom() {
    var isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);
    var isSafari = /Safari/.test(navigator.userAgent) && /Apple Computer/.test(navigator.vendor);
    if (isChrome || isSafari) {
        if ($('div').hasClass('scroll-wrap')) {
            $('.scroll-wrap').removeClass('scrollbar-inner').addClass('scrollAdjust');
            $('.scrollbar-content').removeClass('scrollbar-content vPadding20-left-xs').addClass('vPadding20');



            if (window.matchMedia('(max-width: 767px)').matches) {
                $('.modal .scrollbar-area').css("padding", "");
                $('.modal-scroll-area').css("padding-right", "15px");
            } else {
                if ($(window).width() > 767) {
                    $('.modal .scrollbar-area').css("padding", "15px 15px 0 0");
                    $('.modal-scroll-area').css("padding", "0");
                }
            }
        }
    }
    else if ($(window).width() > 999) {
        if (typeof $('.scrollbar-inner').scrollbar !== 'undefined') {
            $(".scrollbar-inner").scrollbar();

        }
    }
}

function setModalMaxHeightInit() {
    var scrollableBodyModal,
        innerFn = function (modalEl) {
            modalEl.on('show.bs.modal', function () {
                $(this).show();
                setModalMaxHeight(this);
            });
            $(window).resize(function () {
                // bootstrap 4 uses 'show' class instead of 'in'
                if ($('.modal.show, .modal.in').length !== 0) {
                    new setModalMaxHeight($('.modal.show, .modal.in'));
                }
            });
        };

    //Vertically center modal window and make content scrollable if modal is too long

    innerFn($('.modal'));
}


function setModalMaxHeight(element) {
    this.$element = $(element);
    this.$content = this.$element.find('.modal-content');
    var borderWidth = this.$content.outerHeight() - this.$content.innerHeight();
    // if the new 'scrollable-body' class is set, we'll use the margins defined in the digital styleguide (60px each for top and bottom for desktop and tablet. nothing is defined for mobile, but based on one mockup, we'll use 45px)
    var dialogMargin = this.$element.hasClass('scrollable-body') ? ($(window).width() < 768 ? 45 : 120) : ($(window).width() < 768 ? 20 : 60);
    var contentHeight = $(window).height() - (dialogMargin + borderWidth);
    var headerHeight = this.$element.find('.modal-header').outerHeight() || 0;
    var subHeight = this.$element.find('.modal-sub-header').outerHeight() + 1 || 0;
    var footerHeight = this.$element.find('.modal-footer').outerHeight() || 0;
    var maxHeight = contentHeight - (headerHeight + subHeight + footerHeight),
        modalBody = this.$element.find('.modal-body'),
        modalBodyVerticalMargin = parseInt(modalBody.css('margin-top'), 10) + parseInt(modalBody.css('margin-bottom'), 10);

    // if the modal-body has any top or bottom margin, we need to deduct it
    this.$element
        .find('.modal-body').css({
            'max-height': maxHeight - modalBodyVerticalMargin - 15,
            'overflow-y': 'auto'
        });
}

$(document).ready(function () {

    SwapContentPosition();
    AddSurveyButtonEventListener();
    setModalMaxHeightInit();
    cssScrollCustom();

    var resizeTimeoutFn;

    $(window).on('resize', function () {
        clearTimeout(resizeTimeoutFn);
        resizeTimeoutFn = setTimeout(function () {
            setModalMaxHeightInit();
            cssScrollCustom();
        }, 300);
    });

    //Add border to checked box container
    $('.checkbox_Check').click(function (e) {
        if ($(e.target).hasClass('checkbox_Check')) {
            $(this).toggleClass('checked-border');
            if ($(this).find('input').is(":checked")) {
                $(this).find('input').prop('checked', false);
            }
            else {
                $(this).find('input').prop('checked', true);
            }
        }
        $(this).find('input').focus();
    });
    //Change checkbox when enter button is pressed
    $('.graphical_ctrl input[type="checkbox"]').keypress(function (e) {
        if (e.keyCode == 13){
            $(this).closest('.checkbox_Check').trigger('click');   
        }
    });


    //Radio Button with border
    $('.graphical_ctrl input').change(function () {
        var $this = $(this);
        if ($this.attr('type') == "radio") {
            //Remove border to radio button visible only on 991px and below
            $this.closest('[role="radiogroup"]').find('.radio-container').removeClass('checked-border');
            //Hide other reason textarea
            $('.other-reason').addClass('d-none');

            if ($this.prop('checked')) {
                //Add border to radio button visible only on 991px and below
                $this.closest('.radio-container').addClass('checked-border');
                //Show other reason textarea if selected
                if ($this.attr('value') == "other") $('.other-reason').removeClass('d-none');
            }
        } else if ($this.attr('type') == "checkbox") {
            //Add/remove border to checkbox container
            $this.closest('.checkbox_Check').toggleClass('checked-border');
        }
    });
    $('.graphical_ctrl input').focus(function () {
        //Add focus class in radio button container
        $(this).closest('.radio-container').addClass('focused-element');
        //Add focus class in checkbox container
        $(this).closest('.checkbox-container').addClass('focused-element');
    });
    $('.graphical_ctrl input').blur(function () {
        //Remove focus class in radio button container
        $(this).closest('.radio-container').removeClass('focused-element');
        //Remove focus class in checkbox container
        $(this).closest('.checkbox-container').removeClass('focused-element');
    });
    $('.radio-container').click(function (e) {
        if (window.matchMedia('(max-width: 991.9px)').matches) {
            var $this = $(this);
            $this.find('.graphical_ctrl input').prop('checked', true);
            $this.find('.graphical_ctrl input').trigger('change');
            $this.find('.graphical_ctrl input').focus();
        }
    });


    $('.booking-calendar-box').click(function (e) {
        $('.booking-calendar-box').attr('tabindex', '-1').removeAttr('aria-selected');
        $(this).attr('aria-selected', 'true').attr('tabindex', '0').focus();
        $('#booking-calendar-submit').attr("disabled", false);	
    });

    $('.booking-calendar-box').keyup(function (e) {
        if (e.which == 37 || e.which == 39) {
            var rowIndex = $(this).index() + 1;
            var columnIndex = e.which == 37 ? $(this).parent().index() : ($(this).parent().index() + 2);
            var targetRow = $(this).is(':last-child') ? $('.booking-calendar-rows > div:nth-child(' + columnIndex + ') div[role="gridcell"]:last-child') : $('.booking-calendar-rows > div:nth-child(' + columnIndex + ') div[role="gridcell"]:nth-child(' + rowIndex + ')');

            if (targetRow.length > 0 && targetRow.hasClass('booking-calendar-box')) {
                $(this).attr('tabindex', '-1').removeAttr('aria-selected');
                targetRow.attr('aria-selected', 'true').attr('tabindex', '0').focus();
                $('#booking-calendar-submit').attr("disabled", false);
            }
            else if (targetRow.length > 0 && targetRow.hasClass('booking-calendar-disabled-box')) {
                targetRow = $(this).is(':last-child') ? targetRow.prev() : targetRow.next();
                $(this).attr('tabindex', '-1').removeAttr('aria-selected');
                targetRow.attr('aria-selected', 'true').attr('tabindex', '0').focus();
                $('#booking-calendar-submit').attr("disabled", false);
            }
        }
        else if (e.which == 40 || e.which == 38) {
            var targetRow = e.which == 40 ? $(this).next() : $(this).prev();
            if (targetRow.length > 0 && targetRow.hasClass('booking-calendar-box')) {
                $(this).attr('tabindex', '-1').removeAttr('aria-selected');
                targetRow.attr('aria-selected', 'true').attr('tabindex', '0').focus();
                $('#booking-calendar-submit').attr("disabled", false);
            }
            else if (targetRow.length > 0 && targetRow.hasClass('booking-calendar-disabled-box') && !targetRow.is(':last-child') && !targetRow.is(':first-child')) {
                targetRow = e.which == 40 ? targetRow.next() : targetRow.prev();
                $(this).attr('tabindex', '-1').removeAttr('aria-selected');
                targetRow.attr('aria-selected', 'true').attr('tabindex', '0').focus();
                $('#booking-calendar-submit').attr("disabled", false);
            }
        }
        else if (e.which == 9) {
            if ($(this).attr('aria-selected') == undefined) {
                $(this).attr('aria-selected', 'true');
                $('#booking-calendar-submit').attr("disabled", false);
            }
        }
    });

    

    $('input[name="timeperiod"]').change(function (e) {
        if ($('input[name="timeperiod"]:checked').length > 0) {
            $(this).closest('.calendar-modal').find('.btn-primary.disable-gray').removeAttr('disabled');
        }
        else {
            $(this).closest('.calendar-modal').find('.btn-primary.disable-gray').attr('disabled','true');
        }
    });

});



