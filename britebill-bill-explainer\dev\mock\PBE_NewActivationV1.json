{"title": "New Activation", "description": "\n    <div class=\"txtSize14-xs txtBold txtBlack\">Your rate plan was changed on February 5</div>\n    <div class=\"spacer3\"></div>\n    <div class=\"txtSize14-xs\"><span class=\"txtBold-xs\">Old plan:</span> <span >SmartPay CE 20</span></div>\n    <div class=\"txtSize14-xs\"><span class=\"txtBold-xs\">New plan:</span> <span >SmartPay CE 20 <span class=\"txtBlue\">(<span class=\"color2\">$90.00/mo.</span>)</span></span></div>\n    ", "currentBalance": 0.0, "billCloseDate": "2021-02-08T00:00:00-05:00", "startDate": "2021-01-08T00:00:00-05:00", "endDate": "2021-02-07T00:00:00-05:00", "subscriberDetails": {"nickName": null, "phoneNumber": "4167354284", "subscriberNo": "16893809", "subscriberType": "Mobile"}, "chargeItems": [{"chargeType": "Monthly", "amount": 90.0, "startDate": "2021-02-08T00:00:00-05:00", "endDate": "2021-03-07T00:00:00-05:00", "name": "SmartPay CE 20", "description": "A new rate plan, SmartPay CE 20, was added and is billed one month in advance from Feb. 08 to Mar. 07", "chargeIdentifier": "Monthly.9573906087"}, {"chargeType": "Partial", "amount": 9.0, "startDate": "2021-01-09T00:00:00-05:00", "endDate": "2021-02-07T00:00:00-05:00", "name": "SmartPay CE 20", "description": "A new rate plan, SmartPay CE 20, was added in the middle of the billing cycle, resulting in a partial charge billed from Feb. 05 to Feb. 07", "chargeIdentifier": "Partial.9552204763"}, {"chargeType": "Partial", "amount": -9.0, "startDate": "2021-02-05T00:00:00-05:00", "endDate": "2021-02-07T00:00:00-05:00", "name": "Promotional Discount", "description": "A new rate plan was added in the middle of a billing cycle, resulting in a credit for SmartPay CE 20 for the period from Feb. 05 to Feb. 07", "chargeIdentifier": "Partial.9552204763"}], "pbeCategory": "NEW_ACTIVATION"}