/* use mobile-first approach since that's what Bootstrap 4 uses (especially its display utilities) */
/* follow document format and order of shop.css for consistency */

/* START Bootstrap class overrides */

/* END Bootstrap class overrides */

/* START native element defaults */

/* END native element defaults */

/* START generic helpers */

/*START Focus Outline*/
.focus_outline .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element, .radio-container.focused-element {
    outline: none !important;
    box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}

.focus_outline_blue .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element, body.is_tabbing .focus_outline_blue *:focus {
    outline: none !important;
    box-shadow: 0 0 0px 3px #00549a, 0 0 2px 3px #00549a, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}

.focus_outline_gray .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element,
body.is_tabbing .focus_outline_gray *:focus {
    outline: none !important;
    box-shadow: 0 0 0px 3px #f4f4f4, 0 0 2px 3px #f4f4f4, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}

.focus_outline_extra-light-blue .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element,
body.is_tabbing .focus_outline_extra-light-blue *:focus {
    outline: none !important;
    box-shadow: 0 0 0px 3px #c2cedf, 0 0 2px 3px #c2cedf, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}

.outline-circle:focus {
    border-radius: 50%;
}


/*END Focus Outline*/

.line-height-16 {
    line-height: 16px;
}

.line-height-17 {
    line-height: 17px;
}

.line-height-20 {
    line-height: 20px;
}

.line-height-26 {
    line-height: 26px;
}

.txtSize19 {
    font-size: 19px;
}

.txtSize28 {
    font-size: 28px;
}

.txtSize36 {
    font-size: 36px;
}

.max-width-300 {
    max-width: 300px !important;
}

.max-width-610 {
    max-width: 610px;
    flex-basis: 100%;
}

.max-width-620 {
    max-width: 610px;
}

.max-width-415 {
    max-width: 415px;
    flex-basis: 100%;
}

.max-width-770 {
    max-width: 770px;
    flex-basis: 100%;
}

.max-width-50 {
    max-width: 50px;
}

.max-width-630 {
    max-width: 630px;
}

.min-heigth-50 {
    min-height: 50px;
}

.max-height-80 {
    max-height: 80px;
}

.flex-1 {
    flex: 1;
}

.border-left-e1e1e1 {
    border-left: 1px solid #e1e1e1;
}

.cover-content {
    width: 100%;
    height: 100%;
    left: 0;
}

.topCenter {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
}

.topCenter-centered {
    top: 0;
    left: 50%;
    transform: translate(-50%,-50%);
}

.leftCenter-centered {
    top: 50%;
    left: 0;
    transform: translate(-50%,-50%);
}

.z-index-10 {
    z-index: 10;
}

.height-440{
    height: 440px;
}

.height-295 {
    height: 295px;
}

.height-80 {
    height: 80px;
}

.height-55 {
    height: 55px;
}

textarea.height-135 {
    height: 135px;
}

.margin-l-20-percent {
    margin-left: 20%;
}

.width-33 {
    width: 33%;
}

.width-150 {
    width: 150px;
}


main a:focus > [class*="icon"] ~ *, main a:hover > [class*="icon"] ~ * {
    text-decoration: underline !important;
}

main a > [class*="icon"] ~ * {
    text-decoration: none !important;
}

.text-after::after, .text-after-top-center::after {
    content: attr(text-after);
    padding: 5px;
    position: absolute;
    font-weight: bold;
    transform: translateX(-50%);
    background-color: #f4f4f4;
}

.text-after-top-center::after {
    top: 50%;
    left: 50%;
    font-weight: normal;
    transform: translate(-50%, -50%);
    background-color: #fff;
}

.txtBlack2-after::after {
    color: #111;
}

.txtBlue-after::after {
    color: #00549A;
}

@keyframes bg-animation-secFadeInOut {
    0% {
        opacity: 0;
    }

    15% {
        opacity: 1;
    }

    55% {
        opacity: 0;
    }

    100% {
        opacity: 0;
    }
}

.bg-animation img {
    animation: bg-animation-secFadeInOut 9s ease-in-out infinite 0s;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
}

    .bg-animation img:first-child {
        position: relative;
    }

    .bg-animation img:nth-of-type(2) {
        animation-delay: 3s;
    }

    .bg-animation img:nth-of-type(3) {
        animation-delay: 6s;
    }


@media (max-width:991.98px) {
    .txtCenter-sm{
        text-align: center;
    }

    .home-phone-package-4 .title-cont {
        max-width: 350px;
    }

    .offer-hires-img {
        width: 55px;
    }

    .three-column-icons {
        margin-top: -45px;
    }

    .banner-image{
        width: 420px;
    }

    .max-width-sm-480 {
        max-width: 480px;
    }

    .banner-hi-res {
        z-index: 1;
    }

    .tablist-underlined.stream-tab {
        overflow-x: visible;
        white-space: pre-line;
    }
}

@media (max-width:767.98px) {
    .margin-xs-auto {
        margin: auto
    }

    .max-height-160-xs {
        max-height: 160px;
    }

    .width-80-xs {
        width: 80px;
    }

    .width-225-xs {
        width: 225px;
    }

    .topCenter-centered-xs {
        top: 0;
        left: 50%;
        transform: translate(-50%,-50%);
    }

    #custom-stream-tab-control-1.stream-tab a, #custom-stream-tab-control-2.stream-tab a {
        margin-right: 20px;
    }

    .txtLeft-xs {
        text-align: left;
    }
    .three-card-margin-top > div:not(:first-child) {
        margin-top: 25%
    }
    .height-xs-220 {
        height: 220px;
    }

}

.height-6 {
    height: 6px;
}

.width-6 {
    width:6px;
}
/* END generic helpers */
.modal.scrollable-body.show {
    z-index: 2003;
}
.modal.scrollable-body.dual-modal.show {
    z-index: 2001;
}
.modal-backdrop:nth-child(odd) {
    z-index: 2000;
}


/* START component styles and overrides */
/* START Custom Dropdown - Wrapped Text*/
.custom-selected > select {
    display: block;
    position: absolute;
    z-index: 3;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    -webkit-appearance: none;
}

.custom-selected > select {
    display: block;
    position: absolute;
    z-index: 3;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    -webkit-appearance: none;
}

.custom-selected select:disabled {
    opacity: 0;
}

    .custom-selected select:disabled ~ button {
        background-color: #f4f4f4;
    }

.custom-selected select:focus ~ button {
    outline: 2px auto -webkit-focus-ring-color !important
}
/* END Custom Dropdown - Wrapped Text*/


.pos-icon-n5.icon:before {
    top: -5px;
}


.scroll-style::-webkit-scrollbar {
    width: 8px
}

.scroll-style::-webkit-scrollbar-track {
    -webkit-border-radius: 8px;
    border-radius: 8px;
    background: #e1e1e1;
    margin-bottom: 5px !important;
}

.scroll-style::-webkit-scrollbar-thumb {
    height: 40px;
    -webkit-border-radius: 8px;
    border-radius: 8px;
    background: #003778
}

.scroll-style {
    overflow: auto;
    overflow-x: hidden
}

.product-card.channel-card .card-icon {
    align-items: center;
    display: flex;
    justify-content: center;
}

    .product-card.channel-card .card-icon img,
    .product-card.hardware-card .card-icon img {
        width: auto;
    }

.product-card .card-cta-block.card-cta-nospace {
    justify-content: flex-start;
}

.product-card.channel-card .card-cta-block {
    justify-content: flex-start;
}

    .product-card.channel-card .card-cta-block ul {
        margin-top: 15px;
    }

        .product-card.channel-card .card-cta-block ul > li {
            justify-content: space-between;
        }

            .product-card.channel-card .card-cta-block ul > li:not(:first-child) {
                border-top: 1px solid #e1e1e1;
                padding-top: 15px;
            }

            .product-card.channel-card .card-cta-block ul > li:not(:last-child) {
                padding-bottom: 15px;
            }

.product-card.hardware-card .card-icon {
    align-items: flex-start;
    display: flex;
    justify-content: center;
    margin-right: 15px;
    min-width: 73px;
    width: 73px;
}

    .product-card.hardware-card .card-icon img {
        max-width: 100%;
        max-height: 200px;
    }

.product-card.hardware-card ul {
    margin-top: 0;
}

.product-card.hardware-card .form-control-select-box {
    width: 259px;
}

.panel-img {
    min-height: 350px;
    width: auto;
    height: 100%;
}

.tabpanel-container div[role="tabpanel"][aria-hidden="true"]:not(.slick-slide), .events-panel div[role="tabpanel"][aria-hidden="true"] {
    display: none
}

.device-details .device-logos > div {
    min-height: 40px;
}

.banner-icon-wrapper {
    background: #00549a;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 15px;
}

    .banner-icon-wrapper .icon-i {
        font-size: 50px;
        color: #fff;
        top: -5px;
        left: 0;
    }

.sub-banner .banner-text .banner-logos div:not(:last-child) {
    margin-right: 10px;
}

.sub-banner .banner-text .banner-logos > div {
    margin-top: 10px;
    display: inline-block;
    width: 65px;
    height: 65px;
}


.sub-banner-large .banner-image > img, .primary-offers .graphic-product-card .offer-image > img, .huge-offer .image-banner > img, .banner-image.banner-image-2 > img, .img-responsive {
    max-width: 100%;
    flex-shrink: 0;
}

.graphic-product-card .box-shadow-round > div {
    padding: 0 15px;
}

.graphic-product-card .primary-offer-details {
    background-color: #f4f4f4;
    border-top: 1px solid #e1e1e1;
}

.graphic-product-card .card-heading img.normal-pos-img {
    position: relative;
    top: 0;
    left: 0;
    transform: translateX(0);
    height: auto;
    max-width: 100%;
}

.offer-channels {
    max-width: 375px;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

.graphic-product-card .offer-channels-cont {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.offer-channels > div {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 1px solid #e1e1e1;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

    .offer-channels > div > img {
        max-width: 55px;
    }

.primary-offers .primary-offer-cont {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    flex-wrap: wrap;
}

.primary-offers .graphic-product-card > div {
    border: 1px solid #e1e1e1;
}


.graphic-product-cards .graphic-product-card .offer-inclusion-img {
    width: 75px;
    height: 75px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

    .graphic-product-cards .graphic-product-card .offer-inclusion-img > img {
        max-width: 60px;
        max-height: 60px;
    }

/*Start custom style for product card img*/
.offer-inclusion-img {
    width: 75px;
    height: 75px;
}

    .offer-inclusion-img > img {
        max-width: 60px;
        max-height: 60px;
    }
/* End custom style for product card img*/

.graphic-product-card .offer-inclusion {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    justify-content: center;
    align-items: center;
}

.graphic-product-card .primary-offer-selection > div:first-child {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: flex-end;
    flex-wrap: wrap;
}

.graphic-product-card .offer-inclusion > .offer-inclusion-details {
    width: calc(100% - 75px)
}

.graphic-product-card .primary-offer-details {
    border-radius: 0 0 10px 10px;
}

.secondary-offer .offer-image-2 {
    max-height: 100%;
    height: auto;
}

.secondary-offer:not(.no-box) .offer-details.offer-details-2 {
    padding-top: 30px;
    padding-bottom: 30px;
}

.secondary-offer .offer-image-2 > img {
    max-height: 100%;
    max-width: 270px;
}

.secondary-offer.secondary-offer-2:not(.no-box) a {
    margin-bottom: 30px;
}

.huge-offer .offer-details-col > div:not(:first-child) {
    margin-top: 30px;
}

.huge-offer .offer-price-col .small-text {
    margin-top: 15px;
}

.huge-offer .offer-price-col .details-trigger-block {
    margin-top: 30px;
    padding-bottom: 30px;
}

.huge-offer .offer-price-col .details-trigger-block {
    margin-top: 15px;
}

.huge-offer .title-cont {
    padding-bottom: 30px;
    padding-top: 30px;
}

.huge-offer .order-details, .primary-offers .order-details {
    border-top: 1px solid #e1e1e1;
    padding: 30px 0px;
}

.huge-offer .checked-list li:not(:first-child),
section[class*="home-phone-package"] .checked-list li:not(:first-child) {
    margin-top: 5px;
}

.content-ways-to-shop-2 .icon-circle-large:before {
    font-size: 70px;
}

/* TV HD Receivers components */
.two-column-card, .trailer-card {
    border: 1px solid #E1E1E1;
    border-radius: 10px;
    background-color: #FFFFFF;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
}

    .two-column-card > div {
        min-height: 145px
    }

    .two-column-card:not(:last-child) {
        margin-bottom: 30px;
    }

.responsive-border-light-grey {
    box-sizing: border-box;
    border-top: 1px solid #E1E1E1;
    background-color: #F4F4F4;
    padding: 30px 15px;
}

.box-shadow-gray {
    box-shadow: 0 6px 25px 0 rgba(0,0,0,0.12)
}

.border-gray {
    border-bottom: 1px solid #E1E1E1;
    flex: 1;
}

.two-columns {
    padding: 30px 0;
    display: flex;
    flex-direction: column;
    color: #FFF;
}

.column-divider {
    border-bottom: 1px solid #003778;
    margin: 30px 0;
}

.heading-label, .number-icon, .fs18fwBoldlh22 {
    font-size: 18px;
    font-weight: bold;
    line-height: 22px;
}

.list-with-icon {
    list-style: none;
    margin: 0;
    padding: 0;
}

    .list-with-icon li {
        display: flex;
        align-items: center;
    }

        .list-with-icon li:not(:last-child) {
            margin-bottom: 10px;
        }

.list-item-text {
    font-size: 14px;
    font-weight: bold;
    padding-left: 15px;
}

.img-min105x90 {
    min-height: 90px;
    min-width: 105px;
}

.product-hardware .img-container {
    align-items: flex-start;
    display: flex;
    justify-content: center;
    margin-right: 15px;
    min-width: 80px;
    width: 80px;
}

.img-145x135 {
    height: 135px;
    width: 145px;
}

.panel-with-arrow:after {
    content: "";
    border: 15px solid transparent;
    border-bottom-color: #F4F4F4;
    top: -30px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.two-column-arrow-divider {
    display: flex;
    flex-wrap: wrap;
}

    .two-column-arrow-divider > div:first-child {
        border-bottom: 1px solid #e1e1e1;
        padding: 0 0 20px 0;
        display: flex;
        justify-content: center;
    }

        .two-column-arrow-divider > div:first-child:before {
            content: "";
            width: 20px;
            height: 20px;
            position: absolute;
            border: 1px solid #e1e1e1;
            right: 50%;
            transform: rotate(135deg) translateX(-50%);
            border-bottom: none;
            border-left: none;
            background-color: #fff;
            top: 99%;
        }

    .two-column-arrow-divider > div:last-child {
        display: flex;
        justify-content: center;
        padding-top: 35px;
    }

.btn-tv {
    font-size: 36px;
    background: none;
    border: 0;
    margin-top: 10px;
    cursor: pointer;
    color: #00549A;
}

    .btn-tv[disabled] {
        color: #BABEC2;
        opacity: 1;
        cursor: default;
    }

.textInIcon-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
}

.heading-card, .number-in-icon {
    font-size: 32px;
    font-weight: 900;
    letter-spacing: -0.5px;
    line-height: 38px;
}

.card-image-container {
    padding: 30px 0 15px 0;
}

.responsive-hd-divider {
    border-top: 1px solid #003778;
}

.slick-container .slide-top-content {
    display: flex;
    flex-wrap: wrap;
    padding: 30px;
    overflow: hidden;
    white-space: pre-wrap;
    word-break: break-all;
}

.slick-container .slide-top-content-info {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    flex-direction: column;
    padding-left: 30px;
}

.panel-img {
    padding: 30px;
    display: flex;
    flex-wrap: wrap;
}

.labelled-image-container {
    width: 100%;
    margin-top: 15px;
}

.label-container {
    flex-basis: 100%;
    flex-wrap: wrap;
    flex: 1;
}

    .label-container > div {
        padding: 0 10px 0 35px;
        flex: 1;
    }

        .label-container > div > div {
            padding-left: 10px;
            border-left: 1px solid #E1E1E1;
        }

            .label-container > div > div > span {
                display: block;
            }

    .label-container:first-child > div {
        align-self: flex-end;
    }

.welcome-note-container {
    padding: 45px 0;
}

.panel-with-arrow {
    margin-top: 30px;
}

.hardware-label > span:first-child {
    margin-bottom: 15px;
}

.icon-tvpay_perview:before {
    color: #BABEC2;
}

/* Start - Set channel icon to 40px */
.icon-circle-medium img, .icon-circle-medium .channel-minicard-img > img {
    max-height: 40px;
    max-width: 40px;
}
/* End - Set channel icon to 40px */

/* START whats on New Component Class */
.trailer-card {
    margin: 30px 0 30px 0;
    padding: 30px;
}

.trailer-card-details-column {
    padding: 0 5px 0 30px;
}

.trailer-card-logo{
    min-height: 35px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.trailer-card-title, .three-column-movies-title {
    font-size: 24px;
    letter-spacing: -0.4px;
    line-height: 26px;
    margin-bottom: 10px;
}

.three-column-movies-container {
    margin: 0 -30px;
}

    .three-column-movies-container > div {
        padding: 0 30px;
    }

        .three-column-movies-container > div:not(:first-child) {
            border-left: 1px solid #E1E1E1;
        }

.three-column-movies-image {
    min-height: 100px;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 25px;
}

    .three-column-movies-image > img{
        width: 100%;
    }

.three-column-movies-logo{
    margin-bottom: 15px;
    min-height:30px;
}
/* END whats on New Component Class */

/* Responsive for IE */
.icon-text-list {
    display: flex;
    display: -webkit-flex;
    flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    max-width: 720px;
    width: 100%;
    text-align: center;
}

.modal-dialog.see-all-devices .compatible-device:not(:last-child) {
    border-bottom: 1px solid #e1e1e1;
}

.modal-dialog.see-all-devices .compatible-device {
    padding: 30px 0;
    display: flex;
    display: -webkit-flex;
    flex-wrap: nowrap;
    -webkit-flex-wrap: nowrap;
    width: 100%;
}

.modal-dialog.see-all-devices .device-logo-cont > img {
    max-height: 100px;
}

.modal-dialog.compare-services table tr > td:not(:first-child) {
    text-align: center;
}

.modal-dialog .add-on-card-details, .modal-dialog .card-price-details, .channel-lineup-selection .card-price-details {
    display: flex;
    align-items: center;
}

.modal-dialog .product-card .card-icon img.add-on-img {
    width: 100%;
}

.modal-dialog .addons-modal {
    width: 100%;
}

    .modal-dialog.addons-modal .accordion-body ol > li {
        padding-left: 10px;
        padding-bottom: 15px;
    }

    .modal-dialog.addons-modal .accordion-body {
        padding-top: 15px;
        padding-left: 10px;
    }

.image-with-desc-card .card-image {
    overflow: hidden;
}

.image-with-desc-card img {
    max-width: 100%;
    width: 100%;
}

.image-with-desc-card .card-desc {
    padding: 30px 15px;
}

.image-with-desc-card .card-price {
    display: inline-block;
    margin-bottom: 15px;
    width: auto;
}

    .image-with-desc-card .card-price:not(:last-child) {
        padding-right: 30px;
    }

/*.featured-channels .featured-channel:not(:first-child) {
    margin-top: 15px;
}

.featured-channel {
    display: flex;
    border: 1px solid #e1e1e1;
    border-radius: 10px;
    padding: 25px 15px 30px 15px;
    flex-wrap: wrap;
}*/

.movieseries-pricelist .card-price {
    max-width: 160px;
    display: inline-block;
}

    .movieseries-pricelist .card-price:last-child {
        padding-left: 10px;
    }

    .movieseries-pricelist .card-price:first-child {
        padding-right: 10px;
    }

/*.featured-channel .featured-channel-logos {
    margin-left: -15px;
    margin-top: -20px;
}

        .featured-channel .featured-channel-logos > img {
            margin-left: 15px;
            margin-top: 20px;
        }*/


.modal-dialog.addons-modal .tab-panels-container {
    margin-bottom: 30px;
}

.language-selection {
    display: flex;
    flex-wrap: wrap;
    border-radius: 10px;
    background-color: #f0f0f0;
    padding: 15px;
    align-items: center;
}

.modal-dialog.addons-modal .language-content-details > div, .channel-lineup-selection .language-content-details > div, div[role="tabpanel"] .language-content-details > div {
    display: none;
}

.modal-tooltip.modal-tooltip-channel .modal-body {
    padding: 0 25px;
    margin-bottom: 25px;
}

.language-selection .form-control-select-box {
    width: 100%;
    display: flex;
    justify-content: flex-end;
}

.channel-promotion:after {
    content: '';
    display: block;
    position: absolute;
    z-index: 2;
    width: 0;
    height: 0;
    border-style: solid;
    border-color: transparent;
    border-top-color: #00549a;
    border-width: 12px;
    left: 50%;
    transform: translateX(-50%) translateY(8px);
}

.channel-promotion {
    background-color: #00549a;
    padding: 10px;
    border-radius: 10px;
    text-align: center;
}

.additional-details-2 .accordion-body ol > li {
    padding-left: 10px;
    padding-bottom: 15px;
}

.additional-details-2 .accordion-body ol {
    padding-left: 0;
}

.additional-details-2 .accordion-body {
    padding-top: 14px;
    padding-left: 25px;
}

.slick-dots li button:before {
    opacity: 1;
}

.slick-prev,
.slick-next {
    background: #fff;
    border: 1px solid #E1E1E1;
    box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
    margin: 1px;
    margin-top: -16px;
    opacity: 1;
    transform: translateY(-50%);
}

.slick-prev {
    left: -15px;
}

.slick-next {
    right: -15px;
}

    .slick-prev:before,
    .slick-next:before {
        opacity: 1;
    }

    .slick-next:before {
        top: 12px;
    }

    .slick-prev:hover,
    .slick-prev:focus,
    .slick-next:hover,
    .slick-next:focus {
        background: #fff;
        border: 2px solid #00549A;
        box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
        color: #00549a;
        margin: 0;
        margin-top: -17px;
    }

        .slick-prev:hover:before,
        .slick-prev:focus:before,
        .slick-next:hover:before,
        .slick-next:focus:before {
            color: #00549a;
            opacity: 1;
        }

.resizing-slider .slick-dots {
    padding-top: 15px;
}

.resizing-slider .slider-image {
    border-radius: 10px;
}

.resizing-slider-2 .slick-dots {
    float: right;
    margin-top: 20px;
}

    .resizing-slider-2 .slick-dots li button {
        background: rgba(0, 0, 0, 0);
        border: 1px solid #555;
        opacity: 1;
    }

    .resizing-slider-2 .slick-dots li.slick-active button {
        background: #555;
    }

.choice-link{
    max-width: 120px;
    margin: 0 7.5px;
}

.choice-link > img{
    max-height: 30px
}
/* ON DEMAND */

/* START MOVIES */
.movies-container {
    border: 1px solid #E1E1E1;
    border-radius: 10px;
    padding: 30px;
    margin-bottom: 30px;
    min-width: 0;
    min-height: 0;
}

.movie-slider .slick-dots li.slick-active button {
    background-color: #555555;
}

.movie-slider .slick-dots li button {
    border: 1px solid #555555;
    opacity: 1;
}

.movie-slider .slick-dots button {
    background-color: #ffffff;
}

/* Slick Arrows */
.movie-slider .slick-prev, .movie-slider .slick-next {
    background: #fff;
    border: 1px solid #E1E1E1;
    box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
    margin: 0px 15px;
    margin-top: -15px;
    opacity: 1;
    transform: translateY(-100%);
}

    .movie-slider .slick-next:before {
        top: 12px;
    }

    .movie-slider .slick-prev:hover,
    .movie-slider .slick-prev:focus,
    .movie-slider .slick-next:hover,
    .movie-slider .slick-next:focus {
        background: #fff;
        border: 1px solid #00549A;
        box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
        color: #00549a;
    }

        .movie-slider .slick-prev:hover:before,
        .movie-slider .slick-prev:focus:before,
        .movie-slider .slick-next:hover:before,
        .movie-slider .slick-next:focus:before {
            color: #00549a;
            opacity: 1;
        }

.movie-slider .slick-arrow.slick-disabled {
    display: none;
}

.movie-slider .slick-prev:focus, .movie-slider .slick-next:focus, .movie-slider .slick-next:focus:before, .movie-slider .slick-prev:focus:before {
    background: #fff;
    color: #00549a;
}

.movies-container .movies-wrapper .movie-template {
    margin-top: 15px;
    height: auto;
    display: flex;
    flex-flow: column nowrap;
}

.movie-slider .slick-slide {
    /* Override for SLICK inline css */
    width: 138px !important;
    margin-right: 15px;
}

.movies-container .movies-wrapper .movie-template .img-wrapper {
    max-height: 205px;
    max-width: 138px;
    display: flex;
    position: relative;
}

    .movies-container .movies-wrapper .movie-template .img-wrapper .movie-img {
        max-width: 138px;
        max-height: 206px;
        align-self: center;
        flex-shrink: 0;
    }

.movies-container .movies-wrapper .movie-template .movie-status {
    margin-top: 10px;
    color: #555555;
    font-size: 12px;
    max-width: 130px;
    padding-bottom: 15px;
    line-height: 14px;
}

.movies-container .movies-wrapper .movie-template .movie-title {
    color: #111111;
}
/* END MOVIES */

.container4k {
    border-radius: 10px;
    background-color: #FFFFFF;
    box-shadow: inset 0 0 80px 30px rgba(0,0,0,0.05);
    padding: 40px 30px;
    margin-bottom: 10px;
    display: flex;
}

    .container4k .select-icon {
        font-size: 72px;
        color: #00549a;
    }

    .container4k .select-title {
        font-size: 24px;
        padding-top: 10px;
        padding-bottom: 10px;
        width: 100%;
    }

/* START On Demand Tablist */

.blue-bottom-tab ul {
    display: flex;
    justify-content: center;
    padding-top: 30px;

}
    .blue-bottom-tab ul li {
        display: inline-block;
        margin: 0;
        margin: auto 15px;
        padding-bottom: 15px;
    }

        .blue-bottom-tab ul li span {
            color: #00549a;
            cursor: pointer;
            font-size: 18px;
            line-height: 18px;
        }

        .blue-bottom-tab ul li[aria-selected="true"] {
            border-bottom: 4px solid #00549a;
            text-align: center;
            padding-bottom: 11px;
        }

            .blue-bottom-tab ul li[aria-selected="true"] span {
                color: #111;
            }

.scrollable-img-contents {
    padding-right: 60px;
    height: 458px;
    position: relative;
    display: flex;
    flex-shrink: 0;
    justify-content: center;
    width: 100%;
}

    .scrollable-img-contents .scrollable-img {
        height: 458px;
        width: 227px;
        position: absolute;

    }

        .scrollable-img-contents .scrollable-img > img {
            /*visibility: hidden;*/
            max-width: 100%;
            max-height: 100%;
        }

        .scrollable-img-contents .scrollable-img.active > img {
            /*visibility: visible;*/
            /* add transition */
        }

.step-container {
    /* Based on mockups */
    width: 645px;
    margin: auto;
}

    .step-container .step-scroll {
        border-left: 1px solid #e1e1e1;
        position: relative;
    }

        .step-container .step-scroll .step-group {
            padding-left: 45px;
            padding-right: 15px;
            margin-bottom: 0;
        }

                .step-container .step-scroll .step-group .step-list .step-title {
                    color: #00549A;
                    font-size: 18px;
                    line-height: 22px;
                }

                .step-container .step-scroll .step-group .step-list .step-desc {
                    color: #555;
                }

                .step-container .step-scroll .step-group .step-list.active .step-title {
                    color: #111;
                }

        .step-container .step-scroll .scroll-line {
            position: absolute;
            width: 4px;
            top: 0;
            background-color: #00549A;
            list-style: none;
        }

.scrollable-steps {
    display: flex;
    flex-shrink: 0;
}
    .scrollable-steps > div:first-child{
        width: 305px;
    }
    .scrollable-steps > div:last-child {
        width: calc(100% - 305px);
    }
/* END of On Demand Tab List */

.button-align-bottom {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.choice-card {
    padding: 0 35px;
    border-right: 1px solid #e1e1e1;
    border-bottom: none;
    text-align: center;
    color: #555555;
}

    .choice-card:last-child {
        border-right: none;
    }

    .choice-card .choice-img-wrapper {
        height: 109px;
        width: 183px;
        display: flex;
        margin: auto;
        justify-content: center;
    }

        .choice-card .choice-img-wrapper img {
            max-width: 100%;
            max-height: 100%;
            align-self: center;
        }

    .choice-card .choice-card-title {
        margin: 30px 0 15px 0;
        color: #111111;
        font-weight: bold;
        font-size: 18px;
        line-height: 22px;
    }

    .choice-card .choice-bottom {
        padding-top: 15px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

.secondary-offers .button-wrapper a {
    margin-bottom: 15px;
}
/* ON DEMAND */

/* START Fibe TV Page component class */
div[class*="label-"]{
    min-width: 120px;
}

.device-list {
    display: flex;
    flex-wrap: wrap;
}
    .device-list > li {
        width: 135px;
        margin: 0 auto;
    }

        .device-list > li > div {
            display: flex;
            flex-direction: column;
            height: 100%;
            width: 100%;
            margin: auto;
        }

            .device-list > li > div > div:first-child {
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                max-width: 100%;
                align-items: center;
            }

            .device-list > li > div > div:not(:first-child) {
                display: flex;
                justify-content: center;
                align-items: center;
            }

.background-img-responsive {
    margin-top: -12%;
}

/* END Fibe TV Page component class */

/*.modal-dialog.addons-modal .tab-panels-container > div:not(:first-child) {
    display: none;
}*/

.modal-dialog.addons-modal .tab-panels-container .offer-channels, .channel-lineup-selection .offer-channels {
    max-width: 100%;
    margin-left: -15px;
    margin-top: -20px;
    overflow: hidden;
}

    .modal-dialog.addons-modal .tab-panels-container .offer-channels > div, .channel-lineup-selection .offer-channels > div {
        margin-left: 15px;
        margin-top: 20px;
    }

.channel-minicard {
    height: 145px;
    padding: 25px 0 20px 0;
    width: 137px;
}

.channel-minicard p {
    text-align: center;
}

    .channel-minicard .tooltip * {
        text-align: left;
    }

.channel-minicard-img {
    height: 65px;
    width: 65px;
    margin-left: auto;
    margin-right: auto;
}

    .channel-minicard-img > img {
        max-width: 52px;
        max-height: 52px;
    }


.postalcode-caret:before {
    content: "";
    border: 15px solid transparent;
    border-bottom-color: #F4F4F4;
    top: -30px;
    left: 30px;
    position: absolute;
}

.form-group.error .error-description {
    display: flex;
}

.form-group .error-description {
    display: none;
}

.postalcode-details {
    padding: 30px 15px 15px 15px;
    margin-top: -15px;
    display: none;
}

.package-radiogroup .ctrl_radioBtn .ctrl_element {
    top: -3px;
    left: 0;
}

.package-radiogroup .ctrl_radioBtn{
    height: 26px;
}

/*.package-radiogroup, .print-packages .modal-body .form-group {
    padding: 0 15px;
}*/

.print-packages .modal-body {
    padding: 0;
}



/* Step */
.step--card {
    display: inline-block;
}

    .step--card > div {
        display: flex;
        flex-direction: column;
        height: 100%;
        justify-content: space-between;
    }

.bg-blue-spotlight-radial-gradiant {
    background: radial-gradient(circle, #1CBCF4 0%, #0E75CD 45.47%, #024791 69.57%, #012F6A 100%);
}

.hero-img-container img {
    max-width: 100%;
    height: auto;
}

.hero-banner.hero-banner-heightauto:not(.even-split) .banner-image > img:not(.lazy-loading) {
    width:auto;
}

.hero-banner.hero-banner-heightauto > div {
    height: auto;
}

.hero-banner.hero-banner-heightauto .banner-reverse .banner-text {
    width: 100%;
    padding: 0;
    align-items: center;
    height: auto;
}

.hero-banner.hero-banner-heightauto .banner-reverse .banner-image.banner-image-widthauto {
    display: flex;
    justify-content: center;
    padding: 40px 0 0 0;
    height: auto;
    width: 100%;
    flex: auto;
}

.hero-banner.hero-banner-heightauto .banner-reverse {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
}

.message--textarea textarea {
    height: 134px;
    margin-bottom: 10px;
}

.email--input, .message--textarea {
    margin-bottom: 15px;
}

    .email--input p {
        font-size: 12px;
        line-height: 12px;
        margin-bottom: 10px;
    }

.resizing-slider .slick-slide {
    overflow: hidden;
    border-radius: 10px;
}

.hero-banner-video {
    height: 395px;
}
/*Start custom styles for 4k component*/
.hi-res-tab-panel {
    /*bottom: 226px;*/
    bottom: -18%;
    width: 46.97%;
    right: 45px;
}

.tablist-pills-container.hi-res-tab {
    left: 33px;
    bottom: 15px;
}

    .tablist-pills-container.hi-res-tab ul li {
        padding: 5px 30px;
    }


/*End custom styles for 4k component*/
/* Start Promotion 4K */
.hires-text {
    z-index: 1;
    height: 100%;
    /*max-width: 412px;*/
    color: #FFFFFF;
    font-size: 40px;
    font-weight: 900;
    letter-spacing: -0.7px;
    line-height: 46px;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}


.video-wrapper {
    z-index: 0;
    top: 0px;
    left: 0px;
    bottom: 0px;
    right: 0px;
    overflow: hidden;
}

.banner-video-play {
    margin: auto;
    position: absolute;
    z-index: 1;
    top: 0%;
    left: 50%;
    transform: translate(-50%, 0%);
    visibility: visible;
    opacity: 1;
    width: 100%;
    height: auto;
}

.hero-banner-video .banner-gradient-overlay {
    height: 225px;
    background: linear-gradient(180deg, rgba(255,255,255,0) 0%, #FFFFFF 100%);
    bottom: -2px;
    left: 0;
    position: absolute;
    width: 100%;
}

.three-column-icons .icon-4k_tv_bl_bg .path1:before,
.three-column-icons .icon-movies_bl .path1:before,
.three-column-icons .icon-tomorrow_tect .path1:before {
    color: #00549a;
}    

.hires-tablist.tablist-pills-container {
    position: absolute;
    left: 18px;
    bottom: 15px;
}

.sub-banner .banner-text.text-hires {
    order: 1;
    max-width: 300px;
}

.sub-banner .international-banner-image > img {
    position: relative;
    height: auto;
    transform: none;
    max-height: 300px;
    left: 0;
}

.sub-banner .banner-image.banner-hires {
    order: 2;
}

    .sub-banner .banner-image.banner-hires > img {
        position: unset;
        bottom: 0;
        left: 50%;
        transform: none;
        width: 100%;
        height: 100%;
        /*max-width: 634px;*/
        max-height: 486px;
    }

/* Parallax Screens */
.parallax-wrapper {
    height: 400px;
    -webkit-perspective: 80px;
    perspective: 80px;
    position: relative;
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

.banner-text .parallax-title {
    height: 52px;
    /*width: 330px;*/
    color: #111111;
    font-family: "bellslim_font_black", Helvetica, Arial, sans-serif;
    font-size: 24px;
    font-weight: 900;
    letter-spacing: -0.4px;
    line-height: 26px;
}

.parallax-screens-js {
    width: 100%;
    /*position: absolute;*/
    -webkit-transform: rotateX(0deg) rotateY(2deg) scaleX(1.15) scaleY(1.3);
    transform: rotateX(0deg) rotateY(2deg) scaleX(1.15) scaleY(1.3);
    -webkit-transform-origin: left center;
    -ms-transform-origin: left center;
    transform-origin: left center;
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
}


    .parallax-screens-js .screen-1 {
        transform: translateY(3.56498px);
        left: 24px;
        top: 45px;
        z-index: 9;
    }

    .parallax-screens-js .screen-2 {
        transform: translateY(4.44043px);
        left: 90px;
        top: 64px;
        z-index: 5;
    }

    .parallax-screens-js .screen-3 {
        transform: translateY(19.2058px);
        left: 0px;
        top: 192px;
        z-index: 3;
    }

    .parallax-screens-js .screen-4 {
        transform: translateY(30.8664px);
        left: 325px;
        top: 16px;
        z-index: 7;
    }

    .parallax-screens-js .screen-5 {
        transform: translateY(3.83574px);
        left: 83px;
        top: 271px;
        z-index: 1;
    }

    .parallax-screens-js .screen-6 {
        transform: translateY(-3.61011px);
        left: 286px;
        top: 217px;
        z-index: 7;
    }

    .parallax-screens-js .screen-1 > img {
        width: 138px;
    }

    .parallax-screens-js .screen-2 > img {
        width: 301px;
    }

    .parallax-screens-js .screen-3 > img {
        width: 166px;
    }

    .parallax-screens-js .screen-4 > img {
        width: 119px;
    }

    .parallax-screens-js .screen-5 > img {
        width: 133px;
    }

    .parallax-screens-js .screen-6 > img {
        width: 176px;
    }

.text-hires .secondary-offer:not(.no-box) > div {
    padding: 0 40px;
}

.offer-details .hires-offer {
    display: flex;
    flex-flow: column;
    padding: 60px 10px;
}

.banner-hires .wrapper-hires {
    position: relative;
    padding-bottom: 82px;
}

.sub-banner.banner-hires > div {
    padding-left: 0px;
}


.res-hires-view > div {
    position: absolute;
}

.res-hires-view.tabpanel-container {
    z-index: 1;
}

.res-hires-view > div img {
    max-width: 310px;
}

.movies-container.four-column {
    border: none;
    padding: 0;
}

    .movies-container.four-column .four-column-wrapper .movie-template {
        height: 100%;
    }

        .movies-container.four-column .four-column-wrapper .movie-template .img-wrapper img {
            width: 100%;
            border-radius: 10px;
        }

.four-column .movie-slider .slick-slide {
    width: 240px !important;
}

.four-column .four-column-wrapper .slick-list,
.four-column .four-column-wrapper .slick-list .slick-track {
    display: flex;
}

    .four-column .four-column-wrapper .slick-list .slick-track .slick-slide > div {
        height: 100%;
    }

.four-column-wrapper {
    margin: 0 -15px 0 -7.5px;
}

    .four-column-wrapper .movie-slider .slick-slide {
        padding: 0 7.5px;
        margin: 0;
    }

/* needs section */
.needs-container {
    display: flex;
    flex-direction: column;
}

.needs-card {
    padding: 25px;
    background: #00549a;
    border-radius: 10px;
    /* min-width: 300px;
    min-height: 235px; */
}

.hover-blue:hover {
    background: #003778;
}


.needs-card.button-align-bottom {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.needs-card .needs-img img {
    display: block;
    max-width: 100%;
    height: auto;
    width: 100%;
}

.needs-card .needs-wrapper .needs-text {
    color: #fff;
    text-align: center;
}

/* .needs-card .needs-wrapper {
    transform: translateY(-16%);
} */

.remote-container {
    position: relative;
}

.remote-img {
    max-width: 615px;
    margin: 0 auto;
    position: relative;
    z-index: 10;
    top: -20px;
}

    .remote-img img {
        width: 100%;
    }

/* .remote-waves > div {
    max-width: 992px;
} */

.remote-waves > div img {
    width: 100%;
}

.remote-waves div[class^="wave"] {
    position: absolute;
    left: -5px;
}

.remote-waves > div.wave-1 {
    z-index: 5;
    transition: all .75s;
    opacity: 1;
    transform: scale(0);
}
.remote-waves > div.wave-2 {
    z-index: 4;
    transition: all .85s;
    opacity: 1;
    transform: scale(0);
}
.remote-waves > div.wave-3 {
    z-index: 3;
    transition: all .95s;
    opacity: 1;
    transform: scale(0);
}
.remote-waves > div.wave-4 {
    z-index: 2;
    transition: all 1.05s;
    opacity: 1;
    transform: scale(0);
}
.remote-waves > div.wave-5 {
    z-index: 1;
    transition: all 1.15s;
    opacity: 1;
    transform: scale(0);
}

.hero-banner-3 {
    height: 420px;
}

    .hero-banner-3 img {
        width: 300px;
    }

.image-list > div{
    width: 148px;
}

    .image-list > div > img {
        height: 198px;
    }

.image-overlapping {
    position: absolute;
    max-width: 80%;
    bottom: 0;
    right: 15px;
}

.three-column-info-block .image-container {
    height: 109px;
    width: 183px;
}

    .three-column-info-block .image-container img {
        max-width: 100%;
        max-height: 100%;
    }

/* End Promotion 4K */

.sticky-first-column-cont {
    width: 135px;
    min-width: 135px;
}

.sticky-first-column {
    padding: 15px 20px;
}

.table-scrollable-wrapper tbody td div.inner-content {
    padding: 12px 20px;
}

.table-scrollable-wrapper tbody td {
    padding: 0;
}

.sticky-first-column-cont > div:not(:first-child) {
    border-top: 1px solid #d4d4d4;
}

.sticky-first-column-cont > div:nth-child(odd) .sticky-first-column {
    background-color: #fff;
}

.sticky-first-column-cont > div:nth-child(even) .sticky-first-column {
    background-color: #f4f4f4;
}

.text-tag-positioned-left{
    position: absolute;
    top: -10px;
}

@supports (-moz-appearance:none) {
    .sticky-first-column-cont > div:first-child div.same-height {
        margin-top: -1px;
    }
}

/*START slider rotating carousel pause bg*/
.slider-rotating-carousel-pause[data-pressed="true"] .slider-rotating-carousel-pause-bg:before {
    content: "";
    display: block;
    position: absolute;
    top: 50%;
    left: calc(50% + 1px);
    transform: translate(-50%,-50%);
    width: 0;
    height: 0;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 11px solid #003777;
    z-index: 1;
}
    .slider-rotating-carousel-pause {
    height: 38px;
    width: 38px;
    border-radius: 50%;
    border: none;
    background-color: transparent;
    position: absolute;
    bottom: 40px;
    left: 20px;
    z-index: 10;
    padding: 0;
    cursor: pointer;
}

.slider-rotating-carousel-pause-bg {
    height: 34px;
    width: 34px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    border-radius: 50%;
    background-color: #FFF;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.2);
    pointer-events: none;
}

.slider-rotating-carousel-pause[data-pressed="false"] .slider-rotating-carousel-pause-bg:before, .slider-rotating-carousel-pause[data-pressed="false"] .slider-rotating-carousel-pause-bg:after {
    content: "";
    display: block;
    position: absolute;
    top: 50%;
    left: calc(50% - 3px);
    transform: translate(-50%,-50%);
    height: 10px;
    width: 0;
    border: 1px solid #003778;
    z-index: 1;
}

.slider-rotating-carousel-pause[data-pressed="false"] .slider-rotating-carousel-pause-bg:before {
    left: calc(50% + 3px);
}

svg.slider-rotating-carousel-progress {
    overflow: visible;
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
    transform: rotate(-90deg);
}

svg.slider-rotating-carousel-progress circle.slider-rotating-carousel-progress_initial {
    display: none;
}

svg.slider-rotating-carousel-progress circle {
    stroke: #0075FF;
    stroke-width: 3px;
    stroke-dasharray: 125;
    stroke-dashoffset: 0;
    fill: rgba(225,255,255,0);
}

/*END slider rotating carousel pause bg*/
/* mobile only */
@media (max-width: 767.98px) {
    .width-100-xs {
        width: 100%;
    }

    .margin-neg-10-l-xs {
        margin-left: -10px;
    }

    .margin-neg-15-l-xs {
        margin-left: -15px;
    }

    .small-title-2 {
        font-family: Helvetica, Arial, sans-serif;
        line-height: 22px;
        font-size: 18px;
        font-weight: bold;
    }

    .hero-banner.hero-banner-heightauto:not(.even-split) .banner-image > img:not(.lazy-loading) {
        flex-shrink: 0;
    }

    .hero-banner.hero-banner-heightauto:not(.even-split) .banner-reverse .banner-image > img:not(.lazy-loading) {
        max-width: 100%;
    }

    .hero-banner.hero-banner-heightauto:not(.even-split) .banner-image {
        display: flex;
        justify-content: center;
    }

    .hero-banner.hero-banner-heightauto:not(.even-split) .banner-text {
        max-width: none;
    }

    .hero-banner.hero-banner-heightauto .banner-reverse .banner-text {
        padding: 30px 15px 0 15px;
    }

    /*.featured-channel.featured-event {
        padding: 0;
    }*/

    .postalcode-caret:before {
        left: 15px;
    }

    .expander-description {
        max-height: 54px;
        overflow: hidden;
        position: relative;
        transition: max-height 0.3s ease-out;
    }

        .expander-description:after {
            background: linear-gradient(180deg, rgba(255,255,255,0) 0%, #FFFFFF 100%);
            content: '';
            top: 36px;
            height: 36px;
            left: 0;
            pointer-events: none;
            position: absolute;
            width: 100%;
            display: block;
        }

        .expander-description[style]:after {
            display: none;
        }

    .expander-description-control[aria-expanded="true"] .collapse-text, .expander-description-control[aria-expanded="false"] .expanded-text {
        display: none;
    }

    .expander-description-control[aria-expanded="false"] .collapse-text, .expander-description-control[aria-expanded="true"] .expanded-text {
        display: inline;
    }

    .product-card.channel-card .card-details {
        display: block;
        padding-top: 15px;
    }

    .product-card.channel-card .card-icon {
        display: flex;
        float: right;
        width: auto;
        margin: 0;
    }

        .product-card.channel-card .card-icon img {
            padding-left: 15px;
        }

    .product-card.channel-card .card-heading {
        margin-top: 20px;
    }

    .product-card.hardware-card .card-icon {
        display: block;
    }


    .device-details .device-logos > div:nth-child(odd) {
        border-right: 1px solid #e1e1e1;
    }

    .device-details .device-logos > div:nth-child(n+3) {
        margin-top: 30px;
    }

    .huge-offer .offer-price-col {
        padding-top: 30px;
    }

    .sub-banner .banner-image.banner-image-2 {
        padding-top: 30px;
        display: flex;
        justify-content: center;
    }

        .sub-banner .banner-image.banner-image-2 > img {
            height: auto;
            position: static;
            left: 0;
            transform: translateX(0%)
        }

    .sub-banner .banner-logos {
        margin-bottom: 20px;
    }

    .huge-offer .offer-details-col {
        padding-bottom: 30px;
    }

    .huge-offer .image-banner {
        padding-top: 30px;
    }

    .secondary-offer.secondary-offer-2:not(.no-box) > div {
        padding: 0 15px;
    }

    .huge-offer > div {
        border-radius: 0;
    }

    .huge-offer .order-details {
        text-align: left;
    }

        .huge-offer .order-details > a, .primary-offers .order-details > a:not(:first-child) {
            margin-top: 15px;
            padding: 0;
            display: -webkit-box;
            display: -moz-box;
            display: -ms-flexbox;
            display: -webkit-flex;
            display: flex;
        }

    .container.huge-offer {
        padding: 0;
    }

    .graphic-product-card .offer-channels {
        max-width: 225px;
    }

        .graphic-product-card .offer-channels > div:nth-child(n+4) {
            margin-top: 15px;
        }

        .graphic-product-card .offer-channels > div:not(:nth-child(3n)) {
            margin-right: 15px;
        }

    .primary-offers .primary-offer-cont .primary-offer:not(:last-child) {
        margin-bottom: 30px;
    }

    .icon-text-list > li:nth-child(n+3) {
        margin-top: 30px;
    }

    .modal .channel-minicard {
        width: 135px;
    }

    .modal-dialog .offer-channels {
        max-width: 680px;
        justify-content: flex-start;
        align-items: flex-start;
        margin-left: -15px;
    }

    .modal-dialog.addons-modal .tab-panels-container .offer-channels {
        max-width: 100%;
        justify-content: center;
        align-items: center;
        max-height: 160px;
    }

    .modal-dialog .offer-channels > div {
        margin-top: 15px;
        margin-left: 15px;
    }

    .product-card .card-details.hardware--xs {
        flex-direction: column;
        align-items: center;
    }

    .modal-dialog.compare-services table tr > th {
        padding: 10px 8px;
    }

    .modal-dialog.compare-services table tr > td:first-child {
        padding: 20px 15px 20px 20px;
    }

    .modal-dialog .add-on-card-details, .modal-dialog .card-price-details, .channel-lineup-selection .card-price-details {
        flex-direction: column;
        text-align: center;
    }

        .modal-dialog .card-price-details > div {
            border-top: 0;
        }

    .modal-dialog .add-on-card-details, .channel-lineup-selection .add-on-card-details {
        padding-bottom: 30px;
    }

    .product-card .card-details.hardware--xs img {
        margin-bottom: 30px;
    }

    .card-details.add-on-card-details .card-icon {
        display: block;
        margin-bottom: 15px;
        margin-right: 0;
        max-width: 80px;
        width: 80px;
    }

    .product-card .card-details.img-position {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

        .product-card .card-details.img-position .card-icon {
            width: 100%;
            margin-bottom: 30px;
            margin-right: 0;
            display: block;
        }

    .btn-right {
        margin-left: 0;
        margin-top: 10px
    }

    .header-text-button {
        flex-wrap: wrap
    }

    .header-text {
        width: 100%
    }

    .hardware-panel .arrwpanel-card {
        flex-direction: column;
    }

    .hd-option-detail {
        padding-bottom: 30px;
    }

    .hd-option-info {
        padding-top: 30px;
    }

    .img-container {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        margin: 0 0 30px 0
    }

    .card-image-container {
        padding: 20px 0 10px 0;
    }

    /* Mobile Slider */
    .slick-slider-mobile-js.slick-initialized {
        width: 100%;
        margin: 0 auto;
    }

        .slick-slider-mobile-js.slick-initialized .slick-list {
            margin: 0 -30px;
            padding: 0 30px;
        }

        .slick-slider-mobile-js.slick-initialized .slick-track {
            display: flex;
            margin-left: -22.5px;
            padding: 30px;
            width: 100%;
        }

            .slick-slider-mobile-js.slick-initialized .slick-track.margin-n30-left {
                margin-left: -30px;
            }

            .slick-slider-mobile-js.slick-initialized .slick-track.margin-n15-left {
                margin-left: -15px;
            }

        .slick-slider-mobile-js.slick-initialized .slick-slide {
            margin-right: 15px;
            height: auto;
        }

            .slick-slider-mobile-js.slick-initialized .slick-slide > div {
                height: 100%;
            }

            .slick-slider-mobile-js.slick-initialized .slick-slide .slickSlide {
                height: 100%;
            }

                .slick-slider-mobile-js.slick-initialized .slick-slide .slickSlide .two-column-card {
                    height: 100%;
                }

        .slick-slider-mobile-js.slick-initialized .slick-dots {
            margin-top: -5px;
        }



    .slick-container .slide-top-content {
        justify-content: center;
    }

    .slick-container .slide-top-content-info {
        padding-left: 10px;
    }

    .panel-img {
        padding: 30px 15px;
    }

    .slick-slider-mobile-js .slick-dots li button {
        border: 1px solid #555555;
        opacity: 1;
        background-color: #fff;
    }

    .slick-slider-mobile-js .slick-dots li.slick-active button {
        background-color: #555555;
    }

    .panel-with-arrow {
        margin-top: 45px;
    }

    /*End of Mobile Slider*/
    .product-hardware .img-container img {
        max-width: 100%;
        flex-shrink: 0;
    }

    .product-hardware .hardware-label {
        width: 100%;
        display: flex;
    }

    .flex-1 {
        flex-basis: 100%
    }

    .btn-tv {
        margin-top: 0;
    }

    .heading-label {
        font-size: 14px;
    }

    .heading-card {
        font-size: 26px;
        line-height: 28px;
        line-height: -0.4px;
    }

    .label-container > div {
        padding: 0;
    }

    .labelled-image-container {
        padding: 0 15px;
        margin: 0;
    }

    .panel-img, .two-column-card > div {
        min-height: 0;
    }

    .label-container > div:first-child > div {
        padding-right: 10px;
    }

    .label-container > div:only-child > div {
        padding-right: 0;
    }

    .welcome-note-container {
        padding: 45px 0 25px 0;
    }

    .fullHeight-xs {
        height: 100%;
    }

    .hardware-label > span:first-child {
        margin-bottom: 5px;
    }

    .card-image-container {
        padding: 20px 5% 10px 5%;
    }

    /* 767px - START whats on New Component Class */
    .trailer-card {
        padding: 0;
    }

    .trailer-card-details-column {
        padding: 30px 15px;
    }

    .trailer-card-logo {
        margin-bottom: 20px;
    }

    .movie-details-container {
        padding-bottom: 15px;
    }

    .three-column-movies-container > div:not(:first-child) > .movie-details-container {
        padding-top: 30px;
    }

    .three-column-movies-container > div:not(:last-child) > .movie-details-container {
        border-bottom: 1px solid #E1E1E1;
    }

    .three-column-movies-image > img {
        max-width: 400px;
    }
    /* 767px - END whats on New Component Class */

    .secondary-offers .secondary-offer .pad-15-left-right-xs,
    .pad-15-left-right-xs {
        padding-left: 15px;
        padding-right: 15px;
    }

    .container4k {
        padding: 30px 15px;
        margin-top: 30px;
        margin-bottom: 0px;
        text-align: center;
        display: block;
    }

        .container4k .select-title {
            font-size: 22px;
            padding-top: 0;
            padding-bottom: 15px;
        }

    .choice-card {
        border-right: none;
        border-bottom: 1px solid #e1e1e1;
        padding: 30px 0;
        margin: auto 15px;
    }

    .video-hires {
        border-bottom: none;
    }

    .choice-card:first-child {
        padding-top: 0;
    }

    .choice-card:last-child {
        border-bottom: none;
    }


    .movies-container {
        border-radius: 0px;
        padding: 30px 15px;
        margin-bottom: -1px;
    }

    .movie-slider .slick-prev, .movie-slider .slick-next {
        opacity: 0;
    }

    .movies-container .movies-wrapper .movie-template {
        padding-bottom: 5px;
    }

    .button-wrapper {
        margin: 0 auto;
        max-width: 320px;
    }

    /* START On Demand */

    .scrollable-img-contents {
        height: 325px;
    }

        .scrollable-img-contents .scrollable-img {
            height: 325px;
            width: 163px;
        }

    .step-container {
        width: 100%;
    }

        .step-container .scrollable-steps .scrollable-img-contents {
            justify-content: center;
            padding-right: 0;
        }

        .step-container .step-scroll {
            border-left: none;
            padding-right: 0;
        }

            .step-container .step-scroll .step-group {
                padding-left: 0;
                margin-bottom: 30px;
            }

                .step-container .step-scroll .step-group .slide-step-img {
                    justify-content: center;
                    padding-right: 0;
                    display: flex;
                    margin-bottom: 15px;
                }

                    .step-container .step-scroll .step-group .slide-step-img img {
                        height: 325px;
                        width: 163px;
                    }

                .step-container .step-scroll .step-group .step-list {
                    padding-top: 0;
                    padding-bottom: 0;
                    text-align: center;
                }

                    .step-container .step-scroll .step-group .step-list.active .step-title,
                    .step-container .step-scroll .step-group .step-title {
                        color: #00549A;
                    }

                .step-container .step-scroll .step-group .step-desc {
                    padding: 0 30px;
                }

            .step-container .step-scroll .full-width-slide {
                padding: 0;
            }

        .step-container .slick-slider-mobile-js.slick-initialized .slick-list {
            margin: 0 auto;
            padding: 0;
        }

        .step-container .slick-slider-mobile-js.slick-initialized .slick-slide {
            margin-right: 0;
        }

        .step-container .slick-slider-mobile-js.slick-initialized .slick-track {
            padding: 0;
            display: block;
            margin-left: auto;
        }

        .step-container .scrollable-steps .scroll-line {
            display: none;
        }

        .step-container .slick-slider-mobile-js.slick-initialized .slick-dots {
            margin-top: -10px;
            clear: both;
        }
    /* .demand-tab .tablist.receiver-tablist ul li{
        padding: 10px 13px;
        font-size: 14px;
    }
    .demand-tablist.tab-control .header-tab-control ul li {
        margin: auto 7.5px;
    } */
    .blue-bottom-tab ul li {
        margin: auto 7.5px;
    }

        .blue-bottom-tab ul li span {
            font-size: 14px;
        }

    .scrollable-steps > div:last-child {
        width: 100%;
    }

    .choice-link {
        max-width: 100px;
    }

    /* END On Demand */

    .hero-text-container {
        margin-top: 30px;
        margin-bottom: 40px;
        max-width: 260px;
    }

    .hero-banner div.hero-container {
        flex-direction: column;
        overflow: visible;
        height: 210px;
    }

    .hero-img-container {
        max-width: 275px;
        max-height: 155px;
    }

    .hero-banner-gradient {
        margin-bottom: 100px;
    }

    .step--card:first-child {
        margin-bottom: 15px
    }

    .movies-container .movie-slider {
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease;
        -webkit-transition: opacity 0.3s ease;
        max-height: 435px;
    }

        .movies-container .movie-slider.slick-initialized {
            visibility: visible;
            opacity: 1;
            max-height: initial;
        }

    .video-hires {
        border-right: none;
        padding: 0px;
    }

        .video-hires:first-child {
            padding-top: 0;
        }

        .video-hires:last-child {
            border-bottom: none;
        }


    /* START Fibe TV Page component class 767 */
    .button-link {
        color: #00549a;
        text-decoration: underline !important;
    }

        .button-link:hover, .button-link:focus {
            text-decoration: none !important;
        }

    .device-list {
        max-width: 270px;
        margin: 0 auto !important;
    }

        .device-list > li {
            margin: 0 0 10px 0;
        }

            .device-list > li > div:first-child {
                justify-content: center;
            }

            .device-list > li > div > div:first-child {
                justify-content: center;
                padding-left: 30px;
                padding-right: 30px;
            }

    .img-responsive.centerView-xs {
        margin-left: 50%;
        transform: translateX(-50%);
        width: auto !important;
        max-width: none;
    }

    .background-img-responsive .img-responsive {
        height: 345px;
    }

    .background-img-responsive-2 .img-responsive {
        height: 200px;
    }

    .background-img-responsive {
        margin-top: -90px;
    }
    /* END Fibe TV Page component class */
    .needs-container > div:not(:last-child) {
        padding-bottom: 75px;
    }

    .needs-container {
        display: block;
    }

    .needs-card.button-align-bottom {
        flex-direction: row;
        justify-content: center;
    }

    .needs-card .needs-wrapper .needs-img {
        margin: -45px auto;
        max-width: 240px;
    }

    .three-column-img-container {
        margin: -45px auto;
        /*max-width: 240px;*/
    }

    .four-column .four-column-wrapper .slick-list {
        padding-bottom: 25px;
        display: block;
    }

    .hero-banner-video .banner-gradient-overlay {
        height: 115px;
    }

    /*.banner-video-play {
        top: 60%;
        width: 543px;
        height: 306px;
        bottom: 30px;
    }*/

    .hires-hero-banner.hero-banner-gradient {
        margin-bottom: -50px;
    }

    .hires-text .big-title {
        color: #FFFFFF;
        font-size: 22px;
        line-height: 24px;
        text-align: center;
    }

    .video-hires .icon-wrapper {
        max-width: 72px;
    }

    .three-column-icons .icon-wrapper span {
        font-size: 72px;
        color: #00549a;
    }

    .video-hires .icon-wrapper {
        margin: 0px;
    }


    .three-column-icons .hires-video-card-wrapper {
        display: flex;
    }

    .choice-card.video-hires .hires-detail-wrapper {
        text-align: left;
        padding-left: 15px;
        /*max-width: 190px;*/
    }

    .sub-banner.banner-hires > div {
        padding: 0;
    }

    .sub-banner .banner-image.banner-hires-image > img {
        width: 100%;
        height: auto;
    }

    .three-column-icons > div {
        max-width: 500px;
        margin: auto;
    }

    .res-hires-view > div img {
        max-width: 142px;
    }

    .res-hires-view > div {
        right: 15px;
        bottom: -38px;
    }

    .text-hires.secondary-offer:not(.no-box) > div {
        padding: 0px 15px;
    }

    hires-tablist.tablist-pills-container ul li {
        padding: 10px 15px;
    }

    .hero-banner .banner-reverse.hires-parallax {
        display: table;
    }

    .parallax-screens-js {
        top: -125px;
    }

        .parallax-screens-js > div > img {
            max-width: 100%;
        }

    .parallax-wrapper {
        margin-top: 10%;
        margin-bottom: 5%;
        height: 280px;
    }

    .parallax-screens-js .screen-1 {
        left: -4%;
        transform: translate(29%, 105.56498px);
        width: 29%;
        z-index: 9;
    }

    .parallax-screens-js .screen-2 {
        left: 56%;
        z-index: 5;
        transform: translate(-60%, 98.44043px);
        width: 60%;
    }

    .parallax-screens-js .screen-3 {
        left: -12%;
        z-index: 3;
        transform: translate(35%, 52.2058px);
        width: 35%;
    }

    .parallax-screens-js .screen-4 {
        left: 60%;
        z-index: 7;
        transform: translate(26%, 131.8664px);
        width: 26%;
    }

    .parallax-screens-js .screen-5 {
        left: 10%;
        z-index: 1;
        transform: translate(29%, 11.83574px);
        width: 29%;
    }

    .parallax-screens-js .screen-6 {
        left: 47%;
        z-index: 7;
        transform: translate(35%, 28.38989px);
        width: 35%;
    }

    .hero-banner .banner-text.parallax-banner-text {
        text-align: left;
        padding-top: 0;
    }

    .hero-banner:not(.even-split) .banner-text.parallax-banner-text {
        max-width: 100%;
    }

    .banner-video-play {
        width: 130%;
    }

    .hires-tablist.tablist-pills-container {
        left: 12px;
        bottom: 12px;
    }

        .hires-tablist.tablist-pills-container ul {
            padding: 1px;
        }

            .hires-tablist.tablist-pills-container ul li {
                padding: 0px 15px;
            }

    .hero-banner-video .banner-gradient-overlay {
        height: 225px;
    }

    .offer-hires > div > img {
        max-width: 55px;
    }

    .remote-waves {
        position: absolute;
        z-index: 5;
        top: 55%;
        left: 5%;
        width: 120%;
    }

    .image-list {
        width: 296px;
    }
    /*Start custom styles for 4k promotions*/
    .tablist-pills-container.hi-res-tab ul {
        padding: 1px;
    }

        .tablist-pills-container.hi-res-tab ul li {
            padding: 0px 15px;
        }

    .hi-res-tab-panel {
        width: 44%;
        right: 30px;
    }
    /*End custom styles for 4k promotions*/

    .hero-banner-video {
        height: 235px;
    }

    .text-after:after {
        top: 0;
        left: 50%;
        transform: translate(-50%,-50%);
    }

    .banner-image {
        width: 290px;
    }

    .slider-rotating-carousel-pause {
        bottom: 45px;
        left: 25px;
    }
}

/* tablet only */
@media (min-width: 768px) and (max-width: 991.98px) {
    .max-height-135-sm {
        max-height: 135px;
    }

    .border-left-sm-0 {
        border-left: 0;
    }

    .hero-banner.hero-banner-heightauto:not(.even-split) .banner-image, .hero-banner-10 .image-banner {
        margin-right: -30px;
    }

    .hero-banner.hero-banner-heightauto:not(.even-split) .banner-reverse .banner-image {
        margin-right: 0;
        align-self: flex-start;
    }

    .hero-banner.hero-banner-heightauto .banner-reverse .banner-text {
        padding: 45px 60px 0 60px;
    }

    .huge-offer .title-cont {
        padding-bottom: 35px;
        padding-top: 30px;
    }

    .huge-offer .offer-details-col {
        padding-top: 10px;
    }

    .huge-offer .image-banner {
        padding-top: 40px;
    }

    .graphic-product-card .offer-channels {
        max-width: 300px;
    }

        .graphic-product-card .offer-channels > div {
            margin-top: 15px;
            margin-left: 15px;
        }

    .graphic-product-card .offer-channels {
        margin-top: -15px;
        margin-left: -15px;
    }

    .product-card.addon-product-card {
        flex-direction: row;
    }

    .welcome-note-container {
        padding: 45px 0 40px 0;
    }

    .label-container > div {
        padding: 0 10px 0 10px;
    }

    .card-image-container {
        padding: 30px 10% 15px 10%;
    }

    /* 991px - START whats on New Component Class */
    .trailer-card-details-column {
        padding: 0 0 0 45px;
    }
    /* 991px - END whats on New Component Class */

    .choice-card {
        padding: 0 15px;
    }

    .labelled-image-container > div {
        padding: 0 30px;
    }

    /*fix for the  slider showing multiple images*/
    .movies-container .movie-slider {
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease;
        -webkit-transition: opacity 0.3s ease;
        max-height: 435px;
    }

        .movies-container .movie-slider.slick-initialized {
            visibility: visible;
            opacity: 1;
            max-height: initial;
        }

 
    .sub-banner .banner-text.text-hires {
        order: 1;
    }

    .sub-banner .banner-image.banner-hires {
        order: 2;
        min-height: 351px;
    }

    .sub-banner banner-hires > div {
        padding: 15px;
    }

    .hires-hero-banner.hero-banner-gradient {
        margin-bottom: -50px;
    }

    /* START Fibe TV Components 768-991 */
    .devices-list-content {
        max-width: 650px;
    }

    .background-img-responsive {
        margin-top: -75px;
    }
    /* END Fibe TV Components 768-991 */
    .four-column .four-column-wrapper .slick-list {
        padding-bottom: 25px;
        display: block;
    }

    .offer-hires > div > img {
        max-width: 55px;
    }

    .banner-image.banner-hires-image > img {
        height: 100%;
    }

    .res-hires-view > div img {
        max-width: 226px;
    }

    .res-hires-view > div {
        position: absolute;
        bottom: 26px;
        right: 25px;
    }

    .res-hires-view {
        right: 335px;
        bottom: 304px;
    }

    .sub-banner.banner-hires > div {
        max-height: 435px;
    }

    .sub-banner .banner-image.banner-hires-image > img {
        position: relative;
        height: 100%;
        width: 100%;
        bottom: 0;
        left: 0;
        transform: none;
    }

    .secondary-offer .offer-details.offer-hires .small-title {
        margin-bottom: 0;
    }

    .parallax-screens-js {
        top: -130px;
    }

        .parallax-screens-js > div > img {
            max-width: 100%;
        }

    .parallax-screens-js .screen-1 {
        left: -5%;
        transform: translate(29%, 105.56498px);
        width: 29%;
        z-index: 9;
    }

    .parallax-screens-js .screen-2 {
        left: 54%;
        z-index: 5;
        transform: translate(-60%, 98.44043px);
        width: 60%;
    }

    .parallax-screens-js .screen-3 {
        left: -14%;
        z-index: 3;
        transform: translate(35%, 60.2058px);
        width: 35%;
    }

    .parallax-screens-js .screen-4 {
        left: 59%;
        z-index: 7;
        transform: translate(26%, 135.8664px);
        width: 26%;
    }

    .parallax-screens-js .screen-5 {
        left: 10%;
        z-index: 1;
        transform: translate(29%, 19.83574px);
        width: 29%;
    }

    .parallax-screens-js .screen-6 {
        left: 43%;
        z-index: 7;
        transform: translate(35%, 36.38989px);
        width: 35%;
    }

    .hero-banner:not(.even-split) .hires-parallax {
        height: 300px;
    }

    .hires-tablist ul li {
        padding: 5px 28px;
    }

    .banner-hires .wrapper-hires {
        /*min-height: 486px;*/
        position: relative;
        /*height: 486px;*/
    }

    .tablist-pills-container.hi-res-tab ul li {
        padding: 5px 28px;
    }

    .hero-banner-video {
        height: 300px;
    }

    .subtitle {
        font-size: 18px;
        line-height: 1.38105;
        font-weight: 400;
        letter-spacing: .011em;
        color: #ccc;
    }

    .parallax-wrapper {
        height: 280px;
    }

    .device-list.six-devices > li {
        width: 115px;
    }

    .tablist-underlined.stream-tab a {
        font-size: 14px;
        line-height: 18px;
    }

    .modal .channel-minicard {
        width: 126px;
    }

}

@media (max-width: 991.98px) {
    .table-options {
        width: calc(991px - 135px);
    }

    .table-scrollable-wrapper::-webkit-scrollbar {
        height: 8px;
    }

    .table-scrollable-wrapper::-webkit-scrollbar-track {
        background: #e1e1e1;
        height: 8px;
    }

    .table-scrollable-wrapper::-webkit-scrollbar-thumb {
        height: 8px;
        background: #003778
    }
}

/* tablet and larger */
@media (min-width: 768px) {
    .min-heigth-145-sm {
        min-height: 145px
    }

    .min-heigth-350-sm {
        min-height: 350px
    }

    .max-width-260-sm {
        max-width: 260px;
    }

    .slick-prev {
        left: -25px;
    }

    .slick-next {
        right: -25px;
    }


    .hero-banner.hero-banner-heightauto:not(.even-split) .banner-image {
        padding-top: 45px;
    }

        .hero-banner.hero-banner-heightauto:not(.even-split) .banner-image > img {
            position: static;
            top: 0;
        }

    .product-card.channel-card .card-details {
        width: 70%;
    }

    .product-card.channel-card .card-icon {
        min-width: 108px;
        max-width: 108px;
    }

    .product-card.channel-card .card-cta-block {
        width: 30%;
    }

        .product-card.channel-card .card-cta-block ul {
            margin-top: 30px;
        }

    .product-card.hardware-card .card-details {
        width: 67%;
    }

    .product-card.hardware-card .card-cta-block {
        width: 33%;
    }

    .product-card.hardware-card .card-icon {
        margin-right: 30px;
        min-width: 64px;
        width: 64px;
    }

    .product-card.hardware-card .form-control-select-box {
        width: 168px;
    }

    .device-details .device-logos > div:not(:last-child):not(:nth-child(6n)) {
        border-right: 1px solid #e1e1e1;
    }

    .device-details .device-logos > div:nth-child(n+7) {
        margin-top: 30px;
    }

    .sub-banner .banner-image.banner-image-2 > img {
        max-height: 155px;
        transform: translate(-50%,-50%);
        top: 50%;
        height: auto;
    }

    .sub-banner .banner-text-no-vpad {
        padding-top: 0;
        padding-bottom: 0;
    }

    .sub-banner-2 .banner-text {
        padding-left: 30px;
    }


    .huge-offer .offer-details-col {
        padding-right: 30px;
    }

    .primary-offers .order-details {
        display: flex;
        flex-wrap: wrap;
    }

    .primary-offers .order-details {
        padding: 30px 0px 15px 0;
    }

    .huge-offer .order-details {
        padding: 30px 0;
    }

    .primary-offers .order-details > a {
        margin-bottom: 15px;
    }

    .huge-offer .image-banner > img {
        max-width: 300px;
    }

    .huge-offer .offer-price-col {
        padding-left: 30px;
    }

    .huge-offer .order-details {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .huge-offer .offer-price-col .small-text, .huge-offer .offer-price-col .details-trigger-block {
        margin-top: 20px;
    }

    .graphic-product-card .box-shadow-round > div {
        padding: 0 30px;
    }

    .primary-offers .primary-offer-cont .primary-offer:nth-child(odd) {
        padding-right: 15px;
    }

    .primary-offers .primary-offer-cont .primary-offer:nth-child(even) {
        padding-left: 15px;
    }

    .primary-offers .primary-offer-cont .primary-offer:nth-child(n+3) {
        margin-top: 30px;
    }

    .resizing-slider:after {
        content: '';
        display: block;
        /* subtract double the value of the box-shadow spread from the intended width */
        width: 610px;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        top: -30px;
        box-shadow: 0 5px 20px 20px rgba(0,0,0,0.25);
    }

    .resizing-slider .slider-image {
        /* july 01 2020 - milko due distorted image */
        /* height: 334px; */
        position: relative;
        left: 50%;
        transform: translate(-50%, 0);
        width: inherit ;
    }

    .icon-text-list > li:nth-child(n+5) {
        margin-top: 30px;
    }

    .modal-dialog.see-all-devices .compatible-device {
        padding: 30px;
        align-items: center;
    }

    .modal-dialog.alt-premium .offer-channels > div, .modal-dialog.alt-tv .offer-channels > div, .modal-dialog.alt-modal .offer-channels > div:not(:nth-child(8n)) {
        margin-right: 15px;
    }

        .modal-dialog.alt-premium .offer-channels > div:nth-child(n+10), .modal-dialog.alt-tv .offer-channels > div:nth-child(n+10), .modal-dialog.alt-modal .offer-channels > div:nth-child(n+9) {
            margin-top: 15px;
        }

    .modal-dialog.alt-premium .offer-channels, .modal-dialog.alt-tv .offer-channels {
        max-width: 680px;
        justify-content: flex-start;
        align-items: flex-start;
    }

    .modal-dialog.see-all-devices, .modal-dialog.alt-premium, .modal-dialog.alt-tv, .modal-dialog-md {
        width: 740px;
    }

    .modal-dialog.alt-modal .offer-channels {
        max-width: 590px;
        justify-content: flex-start;
        align-items: flex-start;
    }

    .modal-dialog.addons-modal .tab-panels-container .offer-channels, .channel-lineup-selection .offer-channels {
        align-items: flex-start;
        justify-content: flex-start;
        max-height: 80px;
    }

    .modal-dialog.addons-modal .card-price-details .addon-pack-price {
        padding-bottom: 15px;
        padding-left: 30px;
        padding-top: 10px;
        border-left: 2px solid #e1e1e1;
    }

        .modal-dialog.addons-modal .card-price-details .addon-pack-price.international-combos, .channel-lineup-selection .card-price-details .addon-pack-price.international-combos {
            border-left: 1px solid #e1e1e1;
        }

    .image-with-desc-card {
        display: flex;
        padding: 30px;
    }

        .image-with-desc-card .card-image {
            padding-right: 20px;
        }

        .image-with-desc-card .card-desc {
            padding: 0;
            padding-left: 10px;
        }

        .image-with-desc-card .card-price:not(:last-child) {
            padding-right: 20px;
        }

    .postalcode-details {
        padding: 30px 30px 15px 30px;
    }

    /*.package-radiogroup, .print-packages .modal-body .form-group {
        padding: 0 30px;
    }*/

    /*.featured-channel {
        padding: 30px;
    }*/

    .movieseries-pricelist .card-price {
        max-width: 200px;
    }

    .width-196-sm, .print-packages .modal-body .form-group select {
        width: 196px;
    }

    .language-selection select {
        min-width: 196px;
    }

    .postalcode-details input, .width-280-sm {
        width: 280px;
    }

    .channel-minicard {
        padding: 25px 0 20px 0;
    }

    .channel-lineup-selection .channel-minicard {
        padding: 25px 0 20px 0;
    }

    .modal-dialog.addons-modal {
        width: 764px;
    }

    .product-card.channel-card .card-details.option--details {
        width: 73%;
    }

    .product-card.channel-card .card-cta-block.option--cta-block {
        width: 27%;
        justify-content: center;
        align-items: center;
    }

    .product-card .card-details.img-position .card-icon {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .responsive-border-light-grey {
        border: none;
        border-left: 1px solid #E1E1E1;
        padding: 30px
    }

    .header-text {
        padding: 0 10px 0 0
    }

    .two-columns {
        padding: 45px 0;
        flex-direction: row
    }

    .column-list-with-icon {
        flex: 1
    }

    .column-divider {
        border-left: 1px solid #003778;
        margin: 0 30px;
        width: 2px
    }

    .tablist ul li {
        white-space: nowrap
    }

    .hardware-panel .arrwpanel-cards, .arrwpanel-cards {
        display: flex;
    }

    .responsive-hd-divider {
        border-top: 0;
        border-bottom: 0;
        border-left: 1px solid #003778
    }

    .hd-option-info {
        padding-left: 30px;
        flex-basis: 100%;
    }

    .hd-option-detail {
        padding-right: 40px;
        flex-basis: 100%;
    }

    /*  */
    .two-column-arrow-divider > div:first-child {
        border: none;
        border-right: 1px solid #e1e1e1;
        padding: 0 40px 0 0;
        display: flex;
    }

        .two-column-arrow-divider > div:first-child:before {
            content: "";
            width: 20px;
            height: 20px;
            position: absolute;
            border: 1px solid #e1e1e1;
            right: 4px;
            transform: rotate(45deg) translateY(-100%);
            border-bottom: none;
            border-left: none;
            background-color: #fff;
            top: 50%;
        }

    .two-column-arrow-divider > div:last-child {
        padding: 0 0 0 40px;
    }

    .icon-text-container {
        min-width: 260px
    }

        .icon-text-container > div > span.icon {
            margin-top: 10px
        }

    .product-hardware .slickSlide:not(:last-child) {
        margin-bottom: 15px;
    }

    .product-hardware .img-container {
        min-height: 58px;
        min-width: 122px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 30px;
        /* border: 1px solid blue; */
    }

    .product-hardware .hardware-label {
        justify-content: center;
        width: 100%;
        display: flex;
    }

    .secondary-offers .secondary-offer.space-between-15:first-child {
        padding-right: 7.5px
    }

    .secondary-offers .secondary-offer.space-between-15:last-child {
        padding-left: 7.5px
    }

    /* Step class */
    .step--card:first-child {
        padding-right: 15px;
    }

    .step--card:last-child:not(:first-child) {
        padding-left: 15px;
    }


    /*.label-container>div:only-child{margin-left:30px}*/ /* Might be used during once final design is approved*/

    .hero-banner div.hero-container {
        flex-direction: column;
        overflow: visible;
        height: 300px;
    }

    .hero-img-container {
        max-width: 600px;
        max-height: 325px;
    }

    .hero-banner-gradient {
        margin-bottom: 175px;
    }

    .hero-text-container {
        text-align: center;
        margin-top: 45px;
        margin-bottom: 40px;
    }

    .email--input {
        width: 260px;
    }

    .name-input, .phone-number-input, .width-240-sm {
        width: 240px;
    }

    .movie-slider .slick-prev {
        left: -32px;
    }

    .movie-slider .slick-next {
        right: -32px;
    }

    /* START Fibe TV Page component class 768 */
    .button-link {
        border-radius: 20px;
        font-size: 15px;
        height: 35px;
        line-height: 17px;
        text-align: center;
        cursor: pointer;
        padding: 7px 28px;
        white-space: nowrap;
        color: #fff;
        background-color: #003778;
        border: 2px solid #003778;
        margin-left: auto;
    }

        .button-link:hover, .button-link:focus {
            color: #fff;
            background-color: #00549a;
            border-color: #00549a;
        }

        .button-link:focus {
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }

    .device-list > li > div > div:last-child {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .device-list > li > div > div:first-child > div {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
    }

    .device-list > li > div > div:last-child {
        width: 100%;
    }

    .label-compact:before, .label-bluetooth:before, .label-bluetooth-2:before, .label-150hrs:before, .label-150hrs-2:before {
        content: "";
        display: block;
        background: #fff;
        border-radius: 100%;
        width: 10px;
        height: 10px;
        position: absolute;
        -webkit-transform: scale(1);
        -ms-transform: scale(1);
        transform: scale(1);
        -webkit-transition: -webkit-transform .35s;
        transition: -webkit-transform .35s;
        transition: transform .35s;
        transition: transform .35s, -webkit-transform .35s;
        top: 95px;
    }

    .label-compact:before, .label-150hrs:before {
        left: 50%;
    }

    .label-150hrs-2:before {
        left: calc(50% - 20px);
    }

    .label-compact:after, .label-150hrs:after {
        left: calc(50% + 5px);
    }

    .label-150hrs-2:after {
        left: calc(50% - 15px);
    }

    .label-compact:after, .label-bluetooth:after, .label-150hrs:after, .label-150hrs-2:after, .label-bluetooth-2:after {
        content: "";
        opacity: 1;
        display: block;
        background: #fff;
        width: 1px;
        height: 45px;
        bottom: calc(-100% - 3.5px);
        position: absolute;
        margin-top: 5px;
        transition: height 1s;
    }



    .hiddenClass.label-compact:after, .hiddenClass.label-bluetooth:after, .hiddenClass.label-150hrs:after, .hiddenClass.label-150hrs-2:after, .hiddenClass.label-bluetooth-2:after {
        height: 0;
    }

    .label-bluetooth:after, .label-bluetooth-2:after {
        width: calc(50% - 25px);
        right: calc(50% + -15px);
        background: none;
        border-bottom: 1px solid #fff;
        border-right: 1px solid #fff;
    }

    .label-bluetooth-2:after {
        width: calc(50% - 15px);
        right: 50%;
    }
    /* END Fibe TV Page component class */

    .hires-hero-banner .hires-hero-img-container {
        position: static;
    }

    .needs-container > div {
        padding: 0 7.5px;
    }

    .needs-card .needs-wrapper .needs-img {
        margin: -25px -30px;
    }

    .three-column-img-container {
        margin: -25px -30px;
    }

    /* .needs-card .needs-wrapper {
        transform: translateY(-25%);
    } */

    .needs-container {
        padding-top: 100px;
        margin: 0 -15px;
        flex-direction: row;
    }

    .needs-card.button-align-bottom {
        flex-direction: row;
    }

    .four-column-wrapper {
        margin: 0 -30px 0 -7.5px;
    }

    /*.video-hires {
        max-width: 217px;
    }*/

    .three-column-icons .icon-wrapper span {
        font-size: 90px;
    }

    .remote-waves {
        position: absolute;
        z-index: 5;
        top: 50%;
        left: 7%;
        width: 115%;
    }

    .hero-banner-3 img {
        width: 470px;
    }

    .hero-banner-3 {
        height: 395px;
    }

    .table-options {
        width: calc(991px - 165px);
    }

    .sticky-first-column-cont {
        width: 165px;
        min-width: 165px;
    }

    .banner-hi-res {
        padding-bottom: 82px;
    }

    .min-height-50 {
        min-height: 50px;
    }

    .border-left-gray2 {
        padding-bottom: 15px;
        padding-left: 30px;
        padding-top: 10px;
        border-left: 2px solid #e1e1e1;
    }

    .max-width-590 {
        max-width: 590px;
    }

    .max-width-680 {
        max-width: 680px;
    }

    .slider-rotating-carousel-pause {
        bottom: 50px;
        left: 50px;
    }
}

/* desktop and larger */
@media (min-width: 1200px) {
    .infoblock-spacer {
        margin-left: -15px;
        margin-right: -15px;
    }

    .margin-l-lg-auto{margin-left:auto}
    .margin-b-lg-50{margin-bottom:50px}
    .pad-h-lg-10{padding-left:10px;padding-right:10px}
    .pad-h-lg-15{padding-left:15px;padding-right:15px}
    .pad-l-lg-30{padding-left:30px}
    .pad-l-lg-20{padding-left:20px}
    .pad-h-lg-30 {
        padding-left: 30px;
        padding-right: 30px;
    }
    .overflowVisible{
        overflow: visible;
    }
}
@media (max-width: 1199.98px) and (min-width: 992px) {
    .infoblock-spacer {
        margin-left: -7.5px;
        margin-right: -7.5px;
    }

        .infoblock-spacer > div {
            padding-left: 7.5px;
            padding-right: 7.5px;
        }
}

@media (min-width: 992px) {
    .slick-prev {
        left: -15px;
    }

    .slick-next {
        right: -15px;
    }

    .height-md-440 {
        height: 440px;
    }
    .product-card.channel-card .card-details,
    .product-card.hardware-card .card-details {
        width: 76%;
    }

    .product-card.channel-card .card-cta-block,
    .product-card.hardware-card .card-cta-block {
        width: 24%;
    }

    .product-card.hardware-card .card-icon {
        min-width: 108px;
        width: 108px;
    }

    .graphic-product-card .offer-channels > div:nth-child(n+6) {
        margin-top: 15px;
    }

    .graphic-product-card .offer-channels > div:not(:nth-child(5n)) {
        margin-right: 15px;
    }

    .secondary-offer.secondary-offer-2:not(.no-box) a {
        margin-bottom: 40px;
    }

    .secondary-offer .offer-image-2 > img {
        max-width: 365px;
    }

    .additional-details-2 .accordion-wrap {
        padding: 0;
    }

    .huge-offer .image-banner {
        /*display: block;
        margin: 0 auto;*/
        display: flex;
        justify-content: center;
    }

    .huge-offer .title-cont {
        padding-bottom: 30px;
        padding-top: 45px;
    }

    .huge-offer .offer-cont {
        padding-left: 30px;
    }

    .modal-dialog.addons-modal {
        width: 986px;
    }

    .modal-dialog.addons-modal .additional-details .accordion-wrap {
        padding-left: 0;
        padding-right: 0;
    }

    .channel-minicard {
        padding: 25px 0 20px 0;
    }

    .resizing-slider:before {
        /* subtract double the value of the box-shadow spread from the intended width */
        width: 758px;
    }

    .resizing-slider .slider-image {
        max-height: 455px;
    }

    .product-card.channel-card .card-details.option--details {
        width: 80%;
    }

    .product-card.channel-card .card-cta-block.option--cta-block {
        width: 20%;
        justify-content: center;
        align-items: center;
    }

    .image-with-desc-card .card-image {
        padding-right: 0;
    }

    .image-with-desc-card .card-desc {
        padding-left: 30px;
    }

    .image-with-desc-card .card-price:not(:last-child) {
        padding-right: 45px;
    }

    /* START whats on New Component Class */
    .trailer-card {
        margin: 45px 0 30px 0;
    }
    /* END whats on New Component Class */

    .movie-slider .slick-prev, .movie-slider .slick-next {
        opacity: 0;
    }

    .hero-banner div.hero-container {
        height: 440px;
    }

    .hero-img-container {
        max-width: 730px;
        max-height: 395px;
    }

    .hero-banner-gradient {
        margin-bottom: 135px;
    }

    .hero-text-container {
        margin-top: 55px;
        margin-bottom: 50px;
    }

    .hero-banner.hero-banner-heightauto .banner-reverse .banner-text {
        padding-right: 60px;
        order: 2;
        padding-left: 0;
    }

    .hero-banner.hero-banner-heightauto .banner-reverse .banner-image.banner-image-widthauto {
        display: flex;
        justify-content: center;
        padding-left: 45px;
        padding-right: 60px;
        order: 1;
    }

    .hero-banner.hero-banner-heightauto .banner-reverse {
        flex-direction: row;
        flex-wrap: nowrap;
    }

    .movie-slider {
        display: flex;
        width: 100%;
        overflow: hidden;
    }

    .movies-container .movies-wrapper .movie-template {
        width: 138px;
        /*margin-right: 5.5%;*/
        /* margin: 0 auto; */
    }

    .movie-slider .slick-dots {
        opacity: 0;
    }

    .movie-slider > div.movie-template:first-child {
        margin-left: 0;
    }

    .movie-slider > div.movie-template:last-child {
        margin-right: 0;
    }

    .sub-banner .banner-image.banner-hires-image > img {
        position: static;
        bottom: 0;
        left: 50%;
        transform: none;
        width: 100%;
        height: 100%;
        /*max-width: 634px;*/
        max-height: 486px;
    }

    .sub-banner .wrapper-hires {
        padding-left: 0;
    }

    .three-column-icons {
        margin-top: -150px;
    }

    /* START Fibe TV Page component class */
    .device-list > li {
        margin: 0 1%;
    }

        .device-list > li:first-child {
            margin-left: auto;
        }

        .device-list > li:last-child {
            margin-right: auto;
        }
    /* END Fibe TV Page component class */

    .movies-container.four-column {
        padding: 0;
    }

        .movies-container.four-column .four-column-wrapper .movie-template {
            height: auto;
        }

    .needs-container > div {
        padding: 0 15px;
    }

    /* .needs-card .needs-wrapper {
        transform: translateY(-20%);
    } */


    .needs-card .needs-wrapper .needs-img {
        margin: -25px -30px;
    }
    /* .needs-card .needs-wrapper {
        transform: translateY(-25%);
    } */

    .needs-container {
        padding-top: 95px;
    }

    .four-column-wrapper .movie-template {
        padding: 0 7.5px;
    }

    .movies-container.four-column .four-column-wrapper .movie-template {
        max-width: 25%;
    }

    .four-column-wrapper {
        margin: 0 -7.5px;
    }

    .hero-banner .banner-text.parallax-banner-text {
        padding-left: 120px;
    }

    .hero-banner.hires-hero-banner {
        height: 400px;
    }

    .res-hires-view {
        position: absolute;
        right: 335px;
        bottom: 304px;
    }

    .remote-img {
        max-width: 765px;
        top: -20px;
    }

    .remote-waves {
        position: absolute;
        z-index: 5;
        top: 55%;
        left: 12%;
        width: 100%;
    }

    .hires-tablist.tablist-pills-container ul li {
        padding: 6px 30px;
    }

    .hero-banner-3 img {
        width: 575px;
    }

    .hero-banner-3 {
        height: 385px;
    }

    .table-options {
        width: 100%;
    }

    /*.sticky-column {
        position: static;
        width: auto;
        max-width: none;
        z-index: 1;
    }*/

    .bottom-60-neg-md {
        bottom: -60px;
    }

    .column-spacer-30-lg {
        margin: 0 -15px;
    }

    .column-spacer-30-lg > div {
        padding-left: 15px;
        padding-right: 15px;
    }

    .resizing-slider:after {
        content: '';
        display: block;
        /* subtract double the value of the box-shadow spread from the intended width */
        width: 83%;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        top: -30px;
        box-shadow: 0 5px 20px 20px rgba(0,0,0,0.25);
    }
    .modal .channel-minicard {
        width: 139px;
    }

    .slider-rotating-carousel-pause {
        bottom: 55px;
        left: 40px;
    }
}

@media (min-width: 1200px) {
    .slider-rotating-carousel-pause {
        transform: translateX(-570px);
        left: 50%;
    }
}

/* extra-large desktop */
@media (min-width: 1250px) {
    .slick-prev {
        left: -25px;
    }

    .slick-next {
        right: -25px;
    }

    .hero-banner.hires-hero-banner {
        height: 600px;
    }

    .hero-banner-video {
        height: 600px;
    }
}

@media (min-width: 1440px) {
    .pad-t-xl-20 {
        padding-top: 20px;
    }
    .pad-l-xl-20 {
        padding-left: 20px;
    }
    .pad-l-xl-40 {
        padding-left: 40px;
    }
    .pad-r-xl-60 {
        padding-right: 60px;
    }
    .width-100-lg {
        width: 100%;
    }
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
    .modal-open .modal-dialog.compare-services .modal-body.scrollAdjust:not(*:root), .modal-dialog.alt-premium .modal-body.scrollAdjust:not(*:root), .modal-dialog.alt-modal .modal-body.scrollAdjust:not(*:root),
    .modal-dialog.addons-modal .modal-body.scrollAdjust:not(*:root), .modal-open .modal-dialog.available-channel .modal-body.scrollAdjust:not(*:root) {
        margin-right: 0;
        padding-right: 10px;
    }

    .modal-open .modal-dialog .modal-body.scrollAdjust.scroll-end:not(*:root) {
        margin-right: 0;
        padding-right: 15px;
    }

    .modal-dialog.print-packages .modal-body.scrollAdjust:not(*:root),
    .modal-dialog.email-tv-packages .modal-body.scrollAdjust:not(*:root) {
        margin-right: 0;
        padding-right: 0;
    }

    .modal-dialog.modal-dialog-md .modal-body.scrollAdjust:not(*:root) {
        margin-right: 0;
    }
}

/* END component styles and overrides */

/*Tablet*/
@media (min-width: 768px) and (max-width: 991.98px) {
    .infoblock-spacer {
        margin-left: -15px;
        margin-right: -15px;
    }
}

@media all and (-ms-high-contrast: active) and (max-width: 1200px), all and (-ms-high-contrast: none) and (max-width: 1200px) {
    .remote-container.pad-t-60 {
        display: block;
        overflow: hidden;
    }
}

@media all and (-ms-high-contrast: active) and (max-width: 767.98px), all and (-ms-high-contrast: none) and (max-width: 767.98px) {
    .tablist-underlined.stream-tab {
        overflow: hidden;
    }

    #custom-stream-tab-control-1.stream-tab a, #custom-stream-tab-control-2.stream-tab a {
        margin-right: 5px;
    }
}