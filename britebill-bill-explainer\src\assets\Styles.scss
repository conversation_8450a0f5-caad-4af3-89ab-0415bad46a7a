body {
  background:#eee;
}

#greeter-wrap{
  max-width: 500px;
  margin:0 auto;
  padding: 15px;
  border:1px lightblue solid;
  box-shadow: 1px 1px 2px rgba(0,0,0,.15);
  background:#fff;
}

#greeter {
    font-family: "Helvetica Neau", Helvetica, Arial, sans-serif;
    
  h1 {
    color: rgb(36, 104, 250);
    font-family: Impact;
    margin-top:0;
    border-bottom: 1px dotted lightblue;
    display: block;
    width: 100%;
    transition: color .5s ease-out, background-color .25s ease-out, border-radius .25s ease-out;

    &.mouseover {
      background: rgb(36, 104, 250);
      color:#fff;
      border-radius: 30px;
    }
  }

  h1, h2 {
    text-align: center;
  }

  .greeting {
    padding: 1em 0;
    line-height: 1.5em;
    color: rgb(80, 25, 0);
  }
}

#greeter-controls {
  margin-top: 15px;
  text-align: center;
}

.spinner {
  /* Spinner size and color */
  width: 1.5rem;
  height: 1.5rem;
  border-top-color: #444;
  border-left-color: #444;

  /* Additional spinner styles */
  animation: spinner 400ms linear infinite;
  border-bottom-color: transparent;
  border-right-color: transparent;
  border-style: solid;
  border-width: 2px;
  border-radius: 50%;  
  box-sizing: border-box;
  display: inline-block;
  vertical-align: middle;
}
  
/* Animation styles */
@keyframes spinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}