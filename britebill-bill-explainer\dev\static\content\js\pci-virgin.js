$(document).ready(function () {
    $('.tooltip-interactive').each(function () {
        var $this = jQuery(this);
        $this.tooltip({
            container: $this
        })
    });

    $('input[type="radio"][name="identification"]').click(function () {
        // Get value of clicked radio button
        inputValue = $(this).attr('value');

        $(this).prop('checked', true);
        // Get the DOM box class
        let targetBox = $('.' + inputValue);

        // Hide all other box
        $('.box').not(targetBox).addClass('hide');
        // Show targeted box
        $(targetBox).removeClass('hide');
    });
    
});



