@font-face {
	font-family: 'tsr-lm';
	src: url('../fonts/temporary-suspend-restore-icons-lm.eot') format('embedded-opentype'), url('../fonts/temporary-suspend-restore-icons-lm.ttf') format('truetype'), url('../fonts/temporary-suspend-restore-icons-lm.woff') format('woff'), url('../fonts/temporary-suspend-restore-icons-lm.svg') format('svg');
	font-weight: normal;
	font-style: normal;
	font-display: block;
}

.icon-tsr-lm.icon-tsr-lm {
    font-family: 'tsr-lm' !important;
    font-style: normal;
    speak: none;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

	.icon-tsr-lm:before {
		font-family: 'tsr-lm' !important;
		position: static;
	}

.icon-lm-logo:before {
	content: "\e920";
}

.icon-tsr-close-big:before {
	content: "\e900";
}

.icon-tsr-success:before {
	content: "\e92e";
}

.icon-tsr-Bottom_arrow-small:before {
	content: "\e933";
}

.icon-tsr-warning-yellow .path1:before {
	color: #FFD668;
	content: "\e904";
}

.icon-tsr-warning-yellow .path2:before {
	content: "\e905";
	color: #002D72;
	margin-left: -1em;
}

.icon-tsr-warning-yellow .path3:before {
	content: "\e906";
	color: #002D72;
	margin-left: -1em;
}

.icon-tsr-error .path1:before {
	content: "\e904";
}

.icon-tsr-error .path2:before {
	content: "\e905";
	color: #fff;
	margin-left: -1em;
}

.icon-tsr-error .path3:before {
	content: "\e906";
	color: #fff;
	margin-left: -1em;
}

.icon-tsr-info .path1:before {
	content: "\e901";
	color: #41b6e6;
}

.icon-tsr-info .path2:before {
	content: "\e902";
	color: #002d72;
	margin-left: -1em;
}

.icon-tsr-info .path3:before {
	content: "\e903";
	color: #002d72;
	margin-left: -1em;
}

.icon-tsr-external-link:before {
	content: "\e907";
}

.icon-tsr-lost-stolen-phone:before {
	content: "\e908";
}

.icon-tsr-mobile:before {
	content: "\e90b";
}

.icon-tsr-small_minus:before {
	content: "\e909";
}

.icon-tsr-small_plus:before {
	content: "\e90a";
}

.icon-tsr-Right_arrow-small:before {
	content: "\e90c";
}

.icon-tsr-facebook:before {
	content: "\e90d";
}

.icon-tsr-twitter:before {
	content: "\e90e";
}

.icon-tsr-instagram:before {
	content: "\e90f";
}

.icon-tsr-youtube:before {
	content: "\e910";
}
