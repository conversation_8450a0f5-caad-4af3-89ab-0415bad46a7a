var BellSlick = {
    slidersToInit: '.slider-with-data-options',
    slidersToResize: '.slider-resize-refresh',
    resizeTimeoutFn: null,
    init: function(){
        if (undefined === $.fn.slick) return;
        $(BellSlick.slidersToInit).on('init', function () {
            var slider = $(this);
    
            setTimeout(function () {
                var customSliderTabControl, i, len, tabList, tabs, innerBoundFunction = function (pIndex) {
                    if (this.slick('slickCurrentSlide') !== pIndex) {
                        this.slick('slickGoTo', pIndex);
                    }
                };
    
                // call as soon as the slider gets initialized to override the wrong attributes being set by slick.js
                BellSlick.fixSliderAccessibilityAttributes(slider);
                BellSlick.fixSliderEvents(slider);
    
                // code for custom tab control. if slider has data-custom-tab-control is set, we'll attach events to it to allow it to control the slider
                customSliderTabControl = $(slider.data('custom-tab-control'));
                for (i = 0, len = customSliderTabControl.length; i < len; i++) {
                    tabList = customSliderTabControl.eq(i);
                    tabs = tabList.find('[role=tab]');
    
                    tabs.each(function (index) {
                        $(this).on('click', innerBoundFunction.bind(slider, index));
                    });
                }

                // remove inline-style set by slick.js to hide prev arrow when displaying the first slide
                if (slider.hasClass('slider-remove-prev')) {
                    slider.each(function (key, item) {
                        $(this).find('.slick-arrow.slick-disabled').removeAttr('style');
                    });
                }

                // for slider with same-height class 
                if (slider.find('.same-height').length > 0) {
                    slider.find('.same-height').each(function () {
                        resetSameHeightElements($(this));
                        processSameHeightElements($(this));
                    });
                }

            }, 0);
        }).on('afterChange', function () {
            var slider = $(this);
    
            setTimeout(function () {
                var focusedEl = $(document.activeElement), customSliderTabControl, i, len, tabList, currentIndex;
    
                BellSlick.fixSliderAccessibilityAttributes(slider);
    
                // if the previously-focused element was a slick slide or slick dot, transfer the focus to the new active slide or slick dot respectively
                if (focusedEl.hasClass('slick-slide')) {
                    slider.find('.slick-current').focus();
                } else if (focusedEl.is('button') && focusedEl.closest('.slick-dots').length > 0) {
                    slider.find('.slick-dots .slick-active button').focus();
                }
    
                // code for custom tab control. if slider has data-custom-tab-control is set, sync the slide/tabpanel and custom tab
                currentIndex = slider.slick('slickCurrentSlide');
                customSliderTabControl = $(slider.data('custom-tab-control'));
                for (i = 0, len = customSliderTabControl.length; i < len; i++) {
                    customSliderTabControl.eq(i).find('[role=tab]').eq(currentIndex).click();
                }
            }, 0);
        });
        $(BellSlick.slidersToInit).slick();

        $(window).on('resize', function () {
            clearTimeout(BellSlick.resizeTimeoutFn);
            BellSlick.resizeTimeoutFn = setTimeout(function () {
                // for carousels with the 'sliders-with-data-options' class, check if they have been unslicked on the previous breakpoint
    
                // for carousels with the 'slider-resize-refresh' class, call slick setPosition to refresh positioning/sizing            
                $(BellSlick.slidersToResize).slick('setPosition');
            }, 200);
        });
    },
    fixSliderAccessibilityAttributes: function(slider) {
        var isDotted = slider.hasClass('slick-dotted');

        if (isDotted) {
            // if slider has slick-dots, we'll define the attributes as if it is a tab control
            slider.find('.slick-slide:not(.slick-active)').attr('tabindex', -1);
            slider.find('.slick-slide.slick-active').attr('tabindex', 0);
            slider.find('.slick-slide[aria-describedby]').each(function () {
                var slide = $(this);

                slide.attr('aria-labelledby', slide.attr('aria-describedby')).removeAttr('aria-describedby');
            });
        } else {
            // if slider has no dots, we'll simply define it as a list (we can't use toolbar + option because we'be observed issues with it on some mobile devices)
            slider.removeAttr('role');
            slider.find('.slick-track').attr('role', 'list');
            slider.find('.slick-slide').attr({
                'role': 'listitem',
                'tabindex': '-1'
            });
        }
    },
    fixSliderEvents:function(slider){
        var isDotted = slider.hasClass('slick-dotted');

        if (isDotted) {
            // if slider has slick-dots, we'll support home and end keydown events (arrow keys are already supported by slickjs)
            slider.find('.slick-dots').on('keydown', function (e) {
                var key = e.which || e.keyCode || 0;

                if (36 === key) { // home
                    e.preventDefault();
                    e.stopPropagation();
                    slider.slick('slickGoTo', 0);
                } else if (35 === key) { // end
                    e.preventDefault();
                    e.stopPropagation();
                    slider.slick('slickGoTo', slider.find('.slick-slide:not(.slick-clone)').length - 1);
                }
            });
        }
    }
}

$(document).ready(function(){
    BellSlick.init();
    
    // return back focus-able to buttons inside slick cards that was removed by the slick library itself
    $(document).find('.slick-card-button').attr('tabindex', 0);
})