/* eslint-disable no-console */
const express = require("express");
const path = require("path");

const app = express();

const logger = require("morgan");
app.use(logger("dev"));

app.use(express.static("dev"));
app.use("/node_modules", express.static(path.join(__dirname, "../node_modules")));
app.use("/dist", express.static(path.join(__dirname, "../dist")));
app.use("/dist/widget-template/widget.js", function (req, res) {
  res.sendFile(
    path.join(__dirname, "../dist/widget.js"),
    {
      headers: {
        "Content-Type": "application/javascript"
      }
    }
  );
});
app.use("/localization", function (req, res) {
  res.sendFile(
    path.join(__dirname, "../src/default-messages.json"),
    {
      headers: {
        "Content-Type": "application/json"
      }
    }
  );
});

app.use("/getBillExplainer", function (req, res) {
  res.sendFile(
    path.join(__dirname, "./mock/PBE_NewActivationV3.json"),
    {
      headers: {
        "Content-Type": "application/json"
      }
    }
  );
});

app.use("/node_modules/react-slick.js", function (req, res) {
  res.sendFile(
    path.join(__dirname, "./static/core/js/react-slick.js"),
    {
      headers: {
        "Content-Type": "application/javascript"
      }
    }
  );
});

app.listen(8883, "127.0.0.1");


console.log("Server running on \x1b[33mhttp://127.0.0.1:8883\x1b[0m");
console.log("Ctrl + C to stop");
