import { Injectable } from "bwtk";
import { combineEpics } from "redux-observable";

import { IFetchBillsEpic, IFetchPBEActionURLDataEpic } from "../models";
import { Client } from "../Client";
import { Config } from "../Config";

import { Observable, operators } from "rxjs";

import { fetchBillsAction, fetchBillsCompleted, fetchBillsFailed, getPBEDataFromActionURL, getPBEDataFromActionURLFailed, setIsLoadingPBE, updatePBEDataCache } from "./Actions";

import { Logger } from "../Logger";

const { map, catchError } = operators;

@Injectable
export class Epics {
  constructor(private client: Client, private config: Config, private logger: Logger) { }

  combineEpics() {
    return combineEpics(
      this.fetchBillsEpic,
      this.fetchPBEActionURLDataEpic
    );
  }

  private get fetchBillsEpic(): IFetchBillsEpic {
    return action$ => action$.ofType(fetchBillsAction.toString())
      .mergeMap((action: any) => {
        return this.config.mockData ? this.client.getBriteBillBillExplainerApiMockData()
        .pipe(
          map((data) => {
          return fetchBillsCompleted(data);
        }), catchError((error) => {
          this.logger.error(error);
          return Observable.of({...fetchBillsFailed(error), error: true});
        })
        )
        : this.client.getBriteBillBillExplainerApi()
        .pipe(
          map((response) => {
            return fetchBillsCompleted(response.data);
          })
        , catchError(error => {
            this.logger.error(error);
            return Observable.of({...fetchBillsFailed(error), error: true});
          })
        );
      });
  }

  private get fetchPBEActionURLDataEpic(): IFetchPBEActionURLDataEpic {
    return (action$, store) => action$.ofType(getPBEDataFromActionURL.toString())
      .mergeMap(({ payload }: ReduxActions.Action<{ actionUrl: string, cacheKey: string }>) => {
        store.dispatch(setIsLoadingPBE(true));
        const actionUrl = this.config.mockedPBEActionURL ? this.config.mockedPBEActionURL : payload!.actionUrl!;
        return this.client.getPBEActionURLData(actionUrl).pipe(map((response) => {
          store.dispatch(setIsLoadingPBE(false));
          return updatePBEDataCache({ key: payload!.cacheKey, value: response.data });
        }), catchError(error => {
          store.dispatch(setIsLoadingPBE(false));
          this.logger.error(error);
          return Observable.of({ ...getPBEDataFromActionURLFailed(error), error: true });
        }))
      });
  }
}
