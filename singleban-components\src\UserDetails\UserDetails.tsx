import * as React from "react";
import { getRightAppendedClassNames } from "../Utility/Utility";
import { IUserDetailsProps, IUser } from "./IUserDetails";

export const UserDetails: React.FC<IUserDetailsProps> = (props) => {
  const { config, className, elementId } = props;
  const [users, setUsers] = React.useState<IUser[]>([]);
  const [loading, setLoading] = React.useState<boolean>(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const response = await fetch(config.USERS_API);
        if (!response.ok) {
          throw new Error(`Failed to fetch users: ${response.status}`);
        }
        const data = await response.json();
        setUsers(Array.isArray(data) ? data : [data]);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : "An unknown error occurred");
      } finally {
        setLoading(false);
      }
    };

    if (config.USERS_API) {
      fetchUsers();
    } else {
      setError("No API endpoint provided");
      setLoading(false);
    }
  }, [config.USERS_API]);

  const addedClassNames = getRightAppendedClassNames(className);

  if (loading) {
    return <div className={`user-details-loading ${addedClassNames}`} id={elementId}>Loading user data...</div>;
  }

  if (error) {
    return <div className={`user-details-error ${addedClassNames}`} id={elementId}>Error: {error}</div>;
  }

  return (
    <div className={`user-details ${addedClassNames}`} id={elementId}>
      <h2>User Details</h2>
      {users.length === 0 ? (
        <p>No users found</p>
      ) : (
        <div className="user-list">
          {users.map((user) => (
            <div key={user.id} className="user-card">
              <h3>{user.name}</h3>
              <p>Email: {user.email}</p>
              {user.phone && <p>Phone: {user.phone}</p>}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};