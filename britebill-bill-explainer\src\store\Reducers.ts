import { IFetchBillsState, IPBEDataCache, IRequestStatus } from "../models";

export const trueReducer = () =>
  () => true;

export const falseReducer = () =>
  () => false;

export const setFetchBillsStatusReducer = (status: IRequestStatus) => (state: IRequestStatus, { payload: fetchBillsStatus }: ReduxActions.Action<IRequestStatus>) => (status || IRequestStatus.IDLE);
export const setFetchBillsReducer = () => (state: IFetchBillsState, { payload: fetchBills }: ReduxActions.Action<IFetchBillsState>) => ({ ...state, ...fetchBills });
export const setIsLoadingPBEReducer = (state: boolean, action: ReduxActions.Action<boolean>) => action.payload!;
export const setPBEDataCacheReducer = (state: IPBEDataCache, action: ReduxActions.Action<{ key: string, value: any }>) => ({ ...state, [action.payload!.key!]: action.payload!.value });
