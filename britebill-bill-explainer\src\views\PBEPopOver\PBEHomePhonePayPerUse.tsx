import * as React from "react";
import { InjectedIntlProps, injectIntl } from "react-intl";
import { P<PERSON>Header, PBEFooter } from "singleban-components";
import { IPBE } from "../../models";
import { CURRENCY_OPTIONS, modalOpenedOmniture } from "../../utils/Utility";

interface Props {
    pbe: IPBE;
    isPBEModalLinkDisabled: boolean;
}

const PBEHomePhonePayPerUse = (props: Props & InjectedIntlProps) => {
    const { intl: { formatMessage, formatNumber }, pbe } = props;
    const title = formatMessage({ id: "PBE_HOME_PHONE_PAY_PER_USE_TITLE" });
    const description = formatMessage({ id: "PBE_HOME_PHONE_PAY_PER_USE_DESC" }, {
        numCalls: pbe?.pbeDataBag?.totalCalls,
        charge: formatNumber(pbe?.pbeDataBag?.totalCharge, CURRENCY_OPTIONS)
    });
    const imageClassName = "icon-01_phonecall_circle";
    const PBEFooterItems = [pbe?.pbeDataBag?.showUsageLink ? {
        ctaLink: formatMessage({ id: "HP_PAY_PER_USE_USAGE_LINK" }, {
            acctNo: pbe?.pbeDataBag?.encryptedIdentifier,
            subNo: pbe?.pbeDataBag?.encryptedIdentifier,
            ban: pbe?.pbeDataBag?.encryptedBan
        }),
        iconClassName: "icon-10_usage",
        titleKey: formatMessage({ id: "HP_PAY_PER_USE_FOOTER_TITLE1" }),
        ctaTitleKey: formatMessage({ id: "HP_PAY_PER_USE_FOOTER_SUBTITLE1" }),
        isFirstRow: true,
        id: "pbe-hp-pay-per-use-usage"
    } : null, {
        ctaLink: formatMessage({ id: "HP_PAY_PER_USE_UPGRADE_LINK" }, {
            acctNo: pbe?.pbeDataBag?.encryptedIdentifier,
            subNo: pbe?.pbeDataBag?.identifier,
        }),
        iconClassName: "icon-09_unlimited",
        titleKey: formatMessage({ id: "HP_PAY_PER_USE_FOOTER_TITLE2" }),
        ctaTitleKey: formatMessage({ id: "HP_PAY_PER_USE_FOOTER_SUBTITLE2" }),
        isFirstRow: false,
        id: "pbe-hp-pay-per-use-upgrade"
    }];
    React.useEffect(() => {
        modalOpenedOmniture(props?.pbe?.triggerPbeOmniture, title, description);
    },[title, description]);

    return (
        <>
            <PBEHeader descriptionKey={description} titleKey={title} iconClassName={imageClassName} />
            <PBEFooter footerItems={PBEFooterItems} isPBEModalLinkDisabled={props.isPBEModalLinkDisabled}/>
        </>
    );
};


export default (injectIntl(PBEHomePhonePayPerUse));