import * as React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, PBEFooter } from "singleban-components";
import { InjectedIntlProps, injectIntl } from "react-intl";
import { IPBE, IStoreState } from "../../models";
import { DATE_OPTIONS, getPBECacheKey } from "../../utils/Utility";
import { connect } from "react-redux";
import { getLobData } from "../../utils/Utility";
import { modalOpenedOmniture } from "../../utils/Utility";

interface Props {
    pbe: IPBE;
    isPBEModalLinkDisabled: boolean;
}
interface IlistOfsuspensionDetail {
    subNo: number;
    startDate: string;
    endDate: string;
    lobName: string;
    serviceId: string
}

interface IDataFromActionURL {
    suspensionDetails: IlistOfsuspensionDetail[];
    isLatestBill: boolean;
}

interface MapStateToProps {
    dataFromActionURL: IDataFromActionURL;
}

const PBETempServiceSuspension = (props: Props & InjectedIntlProps & MapStateToProps) => {
    const { intl: { formatMessage, formatDate }, pbe, dataFromActionURL} = props;
    const title = formatMessage({ id: "PBE_TEMP_SERVICE_SUSPENSION_TITLE" });
    const description = formatMessage({ id: "PBE_TEMP_SERVICE_SUSPENSION_DESCRIPTION" });
    const iconClassName = "icon-07_temporarly_suspend_service_circle";
    const footerItems = [{
        ctaLink: formatMessage({ id: "PBE_TEMP_SERVICE_SUSPENSION_LINK"}),
        iconClassName:"icon-03_edit",
        titleKey: formatMessage({ id: "PBE_TEMP_SERVICE_SUSPENSION_FOOTER" }),
        ctaTitleKey: formatMessage({ id: "PBE_TEMP_SERVICE_SUSPENSION_FOOTER_SUBTITLE" }),
        isFirstRow: true,
        id: "pbe-Temporary-Service-Suspension"
    }];
    const hyphen = formatMessage({ id : "HYPHEN"});


    React.useEffect(() => {
        modalOpenedOmniture(props?.pbe?.triggerPbeOmniture, title, description);
    }, [title, description]);

    return (
        <>
            <PBEHeader descriptionKey={description} description2Key={pbe?.descriptionKey2} titleKey={title} iconClassName={iconClassName} />
            <div>
                {(dataFromActionURL?.suspensionDetails !== null && dataFromActionURL?.suspensionDetails !== undefined && dataFromActionURL?.suspensionDetails.length > 0) ?
                    <div className="margin-b-30 margin-h-xs-15 margin-h-30">
                        <div className="box-round-grey pad-xs-15 pad-20">
                            <div className="d-block relative">
                                <div className="d-flex">
                                    <div className="margin-r-xs-15 margin-r-20">
                                        <div role="heading" aria-level={3} className="surtitle-black">{formatMessage({ id: "PBE_TEMP_SERVICE_SUSPENSION_TABLE_TITLE" })}</div>
                                    </div>
                                </div>
                                <div role="table" className="bill-redesign-d-table pad-0 margin-t-15 margin-b-0 js-same-width-delegate bill-redesign-w-100">
                                    <div role="rowgroup">
                                        <div role="row">
                                            <div className="sr-only" role="columnheader">{formatMessage({ id: "PBE_TEMP_SERVICE_SUSPENSION_COLUMN_1" })}</div>
                                            <div className="sr-only" role="columnheader">{formatMessage({ id: "PBE_TEMP_SERVICE_SUSPENSION_COLUMN_2" })}</div>
                                            <div className="sr-only" role="columnheader">{formatMessage({ id: "PBE_TEMP_SERVICE_SUSPENSION_COLUMN_3" })}</div>
                                        </div>
                                    </div>
                                    {dataFromActionURL?.suspensionDetails.map((listOfsuspensionDetail, index) =>
                                        <div role="rowgroup">
                                            <div role="row">
                                                <div className="d-flex flex-sm-row flex-column">
                                                    <div>
                                                        <div className="js-same-width-delegate-item" role="cell">
                                                            <strong>{formatMessage({ id: getLobData(listOfsuspensionDetail.lobName).lobName })}</strong>
                                                        </div>
                                                    </div>
                                                    <div className="d-flex container-flex-grow-fill flex-justify-space-between">
                                                        <div className="txtLeft pad-h-xs-0 pad-b-10 text-nowrap pad-l-xs-0 pad-l-20" role="cell">
                                                            <span>{formatDate(new Date(listOfsuspensionDetail.startDate), DATE_OPTIONS)} {hyphen} <br aria-hidden="true" /> {formatDate(new Date(listOfsuspensionDetail.endDate), DATE_OPTIONS)}</span>
                                                        </div>
                                                        <div className="txtRight text-nowrap" role="cell">
                                                            <span>{listOfsuspensionDetail.serviceId}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    : null}
            </div>
            { dataFromActionURL?.isLatestBill && <PBEFooter footerItems={footerItems} isPBEModalLinkDisabled={props.isPBEModalLinkDisabled} />}
        </>
    );
};
const mapStateToProps = (state: IStoreState, ownProps: Props): MapStateToProps => {
    return (
        {
            dataFromActionURL: state.pbeDataCache[getPBECacheKey(ownProps.pbe)]
        }
    );
};


export default injectIntl(connect(mapStateToProps)(PBETempServiceSuspension));