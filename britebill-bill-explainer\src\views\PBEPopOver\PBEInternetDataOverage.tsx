import * as React from "react";
import { PBEHeader, PBEOverageDiagram ,PBEFooter} from "singleban-components";
import { GB, MB } from "../../utils/Constants";
import { InjectedIntlProps, injectIntl } from "react-intl";
import { IPBE } from "../../models";
import { modalOpenedOmniture } from "../../utils/Utility";
interface Props {
    pbe: IPBE;
    isPBEModalLinkDisabled: boolean;
}

const PBEInternetDataOverage = (props: Props & InjectedIntlProps) => {
    const { intl: { formatMessage, formatHTMLMessage }, pbe } = props;
    const title = formatMessage({ id: "PBE_INTERNET_DATA_OVERAGE_TITLE" });
    const overageDiagramPath = "PBE_DATA_OVERAGE_DIAGRAM_PATH"
    const overageValue = pbe?.pbeDataBag?.overageDataValue;
    const showUsageLink = pbe?.pbeDataBag?.showUsageLink;
    const serviceName = pbe?.pbeDataBag?.serviceName;
    const overageBy = pbe?.pbeDataBag?.identifier;
    const nickName = pbe?.pbeDataBag?.nickname;
    // const overageUnit = pbe?.pbeDataBag?.overageDataUnit ?? "";
    const description = formatHTMLMessage({ id: "PBE_INTERNET_DATA_OVERAGE_DESCRIPTION"}, {
        overageValue: pbe?.pbeDataBag?.overageDataValue,
        unit: (pbe?.pbeDataBag?.overageDataUnit === "GB" || pbe?.pbeDataBag?.overageDataUnit === "Go") ? formatMessage({ id: "GB_DATA_UNIT" }) : formatMessage({ id: "MB_DATA_UNIT" }),
        unitName: (pbe?.pbeDataBag?.overageDataUnit === "GB" || pbe?.pbeDataBag?.overageDataUnit === "Go") ? formatMessage({ id: "GB_DATA_UNIT_FULL_FORM" }) : formatMessage({ id: "MB_DATA_UNIT_FULL_FORM" }),
        amount: pbe?.pbeDataBag?.overageDataCharge,
        dollarAmount: Math.trunc(pbe?.pbeDataBag?.overageDataCharge),
        cents: pbe?.pbeDataBag?.overageDataCharge - Math.trunc(pbe?.pbeDataBag?.overageDataCharge)
    })
    const overageDataUnitSpecified = (pbe?.pbeDataBag?.overageDataUnit === "GB" || pbe?.pbeDataBag?.overageDataUnit === "Go") ? GB : MB;
    const anchorTag = formatHTMLMessage({ id: "VIEW_USAGE_DETAILED_LINK" },{
        viewDetailedUsageHref: formatMessage({ id: "PBE_INTERNET_DATA_OVERAGE_LINK" }, {
            acctNo: pbe?.pbeDataBag?.encryptAcctNo
        })
    });
    const overageByLabel = formatMessage({ id: "PBE_DATA_OVERAGE_BY" });
    const footerItems = [{
        ctaLink: formatMessage({ id: "PBE_INTERNET_DATA_OVERAGE_SWITCH_TO_UNLIMITED_PACK" }, {
            encryptedAcctNo: pbe?.pbeDataBag?.encryptedIdentifier,
            subNo: pbe?.pbeDataBag?.identifier
        }),
        iconClassName: "icon-09_unlimited",
        titleKey: formatMessage({ id: "PBE_INTERNET_DATA_OVERAGE_NEVER_WORRY_ABOUT_EXCEEDING" }),
        ctaTitleKey: formatMessage({ id: "PBE_INTERNET_DATA_OVERAGE_NEVER_WORRY_ABOUT_EXCEEDING_SWITCH" }),
        isFirstRow: true,
        id: "PBE_INTERNET_DATA_OVERAGE_SWITCH_TO_UNLIMITED"
    }];
    React.useEffect(() => {
        modalOpenedOmniture(props?.pbe?.triggerPbeOmniture, title, description);
    },[title, description]);
    return (
        <>
            <PBEHeader descriptionKey={description} titleKey={title} showUsageLink={showUsageLink} anchorTag={anchorTag} isHTMLDescription/>
            <PBEOverageDiagram overageDiagramPath={overageDiagramPath} overageValue={overageValue} overageBy={overageBy} serviceName={serviceName} serviceNickname={nickName} overageByLabel={overageByLabel} overageUnit={overageDataUnitSpecified} />
            <PBEFooter footerItems={footerItems} isPBEModalLinkDisabled={props.isPBEModalLinkDisabled}/>
        </>
    );
};


export default (injectIntl(PBEInternetDataOverage));