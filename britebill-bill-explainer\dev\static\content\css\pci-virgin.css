header .skip-to-main-link {
    display: inline-block;
    padding: 9px 12px;
    position: absolute;
    top: -50px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    text-decoration: underline;
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
    transition: top .3s ease-out;
    z-index: 999999999;
    font-size: 13px;
    background-color: #efefef;
    color: #212121;
}

header .skip-to-main-link:focus {
    top: 0;
}

footer .skip-to-main-link {
    display: inline-block;
    padding: 9px 12px;
    position: absolute;
    left: -300px;
    text-decoration: underline;
    border-bottom-right-radius: 5px;
    transition: left .3s ease-out;
    background-color: #efefef;
    z-index: 3000;
    font-size: 13px;
    color: #212121;
}

footer .skip-to-main-link:focus {
    left: 0;
}



.width-80 {
    width: 80px;
}

.width-465 {
    width: 465px;
}

.max-width-100-percent {
    max-width: 100%;
}

.height-20 {
    height: 20px;
}

.height-55 {
    height: 55px;
}

.line-height-18 {
    line-height: 18px;
}

.line-height-22 {
    line-height: 22px;
}

.line-height-24 {
    line-height: 24px;
}

.txtSize36 {
    font-size: 36px;
}

.icon-pci.no-change-color.txtGrey:hover, .icon-pci.no-change-color.txtGrey:focus {
    color: #a1a5a6;
}

.left-0 {
    left: 0;
}

.top-neg-1 {
    top: -1px;
}

.form-control {
    border: 1px solid #D7D7D7;
}

.form-control-select + span.top-neg-3 {
    top: -3px;
}

.tooltip-interactive .tooltip.bs-tooltip-bottom .arrow {
    margin-left: -32px;
}

.form-control-select-box:after {
    font-family: "pci-virgin";
    content: "\e9da";
    font-size: 6px;
    color: #555;
    right: 4px;
    top: 7px;
    padding: 10px;
    height: 30px;
    cursor: pointer;
    background-color: #f4f4f4;
}

.tooltip-inner{
    font-family: Arial, Helvetica, sans-serif;
    padding: 30px;
}

.vm-light-blue, a.vm-light-blue:hover, a.vm-light-blue:focus {
    color: #006FE6
}

.border-black-round {
    border: 1px solid #111111;
    border-radius: 10px;
    padding: 2px 5px;
}

/*Cheque line*/
.cheque-line:after {
    content: "";
    opacity: 1;
    display: block;
    background: #111111;
    width: 1px;
    height: 20px;
    position: absolute;
    left: 50%;
    top: 19px;
}

.cheque-line-left:after {
    content: "";
    opacity: 1;
    display: block;
    width: 8px;
    background: none;
    border-bottom: 1px solid #111111;
    border-right: 1px solid #111111;
    height: 32px;
    position: absolute;
    left: 7px;
    bottom: -33px;
}

.cheque-line-right:after {
    content: "";
    opacity: 1;
    display: block;
    width: 8px;
    background: none;
    border-bottom: 1px solid #111111;
    border-left: 1px solid #111111;
    height: 32px;
    position: absolute;
    left: 20px;
    bottom: -33px;
}

.modal-dialog .modal-content .modal-header .close {
    margin: 0 0 0 15px;
    border: 0;
}

/*radio check focus outline*/
.graphical_ctrl.secondary_checkbox input[type="checkbox"]:focus ~ .ctrl_element, .graphical_ctrl input[type="radio"]:focus ~ .ctrl_element, .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element {
    outline-width: 2px;
    outline-style: solid;
    outline-color: #4d90fe;
    outline-offset: 2px;
}

@media (min-width: 992px) {
    .height-75-md {
        height: 75px;
    }

    .line-height-26-md {
        line-height: 26px;
    }
}
/*Greater than Mobile*/
@media (min-width: 768px) {
    .width-90-sm {
        width: 90px;
    }
    .width-135-sm {
        width: 135px;
    }
    .width-260-sm {
        width: 260px;
    }
    .width-300-sm {
        width: 300px;
    }
    .width-380-sm {
        width: 380px;
    }
    .modal:before {
         height: 100%; 
    }
    .cheque-line:after {
        left: 50%;
        top: 19px;
    }

    .cheque-line-left:after {
        left: 13px;
        bottom: -33px;
    }

    .cheque-line-right:after {
        left: 35px;
        bottom: -33px;
    }
}

@media (max-width: 767.98px) {
    .modal.modal-tooltip.vm-mobile .tooltip-dialog {
        max-width: 100%;
    }
    .modal:before {
        height: 65%;
    }

    .modal.modal-vm {
        bottom: -400px;
        top: unset;
        transition: all 0.15s linear;
    }

    .modal.show {
        bottom: 0;
        max-height: 100%;
        height: auto;
    }

    .ctrl_radioBtn .ctrl_element {
        top: -1px;
        left: 0;
    }

    .form-control-select.txtSize14-xs {
        font-size: 14px;
    }

    .form-control-select.pad-r-xs-35 {
        padding-right: 35px;
    }

    .modal.fade.show .modal-dialog {
        top: unset;
        transform: unset !important;
        transition: unset;
    }
}