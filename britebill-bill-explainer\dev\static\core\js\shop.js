// START global variables and constants

var KEYS = {
    space: 32,
    enter: 13,
    left: 37,
    right: 39,
    up: 38,
    down: 40,
    home: 36,
    end: 35,
    esc: 27
}, ActualTabs;

// START Tab Control (tabs DO NOT cause page redirect)
ActualTabs = {
    options: {
        tabSelector: '.actual-tabs-controller-js[role=tablist] [role=tab]'
    },
    init: function (config) {
        var extendedOptions = $.extend(this.options, config),
            $tabs = $(extendedOptions.tabSelector),
            $tabList = $tabs.first().parent().closest('[role=tablist]');

        $tabList.data('actualtabs-options', JSON.stringify(extendedOptions));

        this.initTabEvents($tabs);
    }, initTabEvents: function (tabs) {
        var $tabs = tabs ? $(tabs) : $(tabs.tabSelector);

        // toggle attributes and class when a tab is clicked
        $tabs.on('click', this._tabClickListener);

        // automatic tabs automatically change tab when arrow keys are pressed. consider supporting manual tabs in the future if necessary
        $tabs.on('keydown', this._tabKeydownListener);
    }, cleanTabEvents: function (tabs) {
        var $tabs = tabs ? $(tabs) : $(tabs.tabSelector);

        $tabs.off('click', this._tabClickListener);
        $tabs.off('keydown', this._tabKeydownListener);
    }, reinit: function (tabs) {
        var $tabs = $(tabs);

        this.cleanTabEvents($tabs);
        this.initTabEvents($tabs);
    }, _tabClickListener: function () {
        var clickedTab = $(this),
            tabList = clickedTab.parent().closest('.actual-tabs-controller-js'),
            tabs,
            scrollTop,
            tabPanelContainer,
            tabPanels,
            i,
            len;

        if (tabList.hasClass('manual-tabs-js')) {
            // support manual activation in the future if necessary
        } else {
            // toggle attribute and class
            tabs = tabList.find('[role=tab]')
            tabs.attr({
                'aria-selected': 'false',
                'tabindex': '-1'
            }).removeClass('active');
            clickedTab.attr({
                'aria-selected': 'true',
                'tabindex': '0'
            }).addClass('active').filter('a').removeAttr('tabindex');

            // scroll into view horizontally
            scrollTop = $(window).scrollTop();
            // Remove the line of code below as it causes flickering issue on IE
            //clickedTab[0].scrollIntoView();
            $(window).scrollTop(scrollTop);

            // set focus if necessary. this is the case if active tab is changed using left/right/home/<USER>
            if (document.activeElement === this || $(document.activeElement).closest('.actual-tabs-controller-js')[0] === tabList[0]) {
                clickedTab.focus();
            }

            // control tab panel switching if necessary. don't do this for carousels by setting data-carousel-tablist=true
            if (tabList.data('carousel-tablist') !== true) {
                tabPanelContainer = $(tabList.data('tab-panels-container'));
                if (tabPanelContainer.length > 0) {
                    tabPanels = tabPanelContainer.find('[role=tabpanel]').filter(function () { return $(this).parent().closest('[role=tabpanel]', tabPanelContainer[0]).length === 0; });

                    for (i = 0, len = tabs.length; i < len; i++) {
                        if (tabs[i] === this) {
                            tabPanels.eq(i).attr({
                                'tabpanel-selected': 'true',
                                'tabindex': 0
                            });
                        } else {
                            tabPanels.eq(i).attr({
                                'tabpanel-selected': 'false',
                                'tabindex': -1
                            });
                        }
                    }
                }
            }
        }
    }, _tabKeydownListener: function (e) {
        var key = e.which || e.keyCode || 0,
            tabList = $(this).parent().closest('.actual-tabs-controller-js'),
            isVertical = tabList.attr('aria-orientation') === 'vertical', // if tabs are in vertical arrangement, aria-orientation=vertical must be set
            tabs = tabList.find('[role=tab]'),
            index = 0,
            len = tabs.length;

        for (; index < len; index++) {
            if (this === tabs[index]) {
                break;
            }
        }

        if (key === KEYS.home) {
            index = 0;
        } else if (key === KEYS.end) {
            index = len - 1;
        } else {
            // left & right is for horizontal tabs. up & down is for vertical tabs
            if (!isVertical && key === KEYS.left || isVertical && key === KEYS.up) {
                if (index === 0) {
                    index = len - 1;
                } else {
                    index--;
                }
            } else if (!isVertical && key === KEYS.right || isVertical && key === KEYS.down) {
                if (index === len - 1) {
                    index = 0;
                } else {
                    index++;
                }
            } else {
                return;
            }
        }

        e.preventDefault();
        e.stopPropagation();
        tabs.eq(index).trigger('click');
    }
};
// END Tab Control (tabs DO NOT cause page redirect)

// END global and constants



// START document-ready
(function ($) {
    // initialize actual tabs
    ActualTabs.init();

    // header-tab-control is only for group of links that looks like tabs but DO NOT function as tabs
    // set scrollLeft of header-tab-control to make sure the active item is visible in case there's overflow, we'll center it if possible to make it easier to see that the area is scrollable
    $('.header-tab-control').each(function () {
        var scrollableEl = $(this),
            activeEl = scrollableEl.find('li a.active, li a[aria-current]:not([aria-current=false])').first(),
            listEl = activeEl.parent().closest('ul');

        if (activeEl.is(':not([aria-current])')) {
            activeEl.attr('aria-current', 'page');
        }

        scrollableEl.scrollLeft(activeEl.offset().left - listEl.offset().left - listEl.outerWidth() / 2 + activeEl.outerWidth() / 2);
    });

    // users should be able to activate "buttons" using the space key. this is for anchor tags so enter key is already supported
    $('a[role=button]:not(.click-on-space)').on('keypress', function (e) {
        if (KEYS.space === (e.which || e.keyCode || 0)) {
            e.preventDefault();
            e.stopPropagation();
            $(this).trigger("click");
        }
    });

    // select elements now use bell-blue color if it has a selected value and the selected option is also in that color    
    $('select.colored-selected').each(function () {
        // if the colored-selected class is found, automatically set the other class and attributes
        var el = $(this),
            options = el.find('option'),
            selected = options.eq(this.selectedIndex);

        options.not('[selected]').removeClass('selected');
        selected.addClass('selected');

        options.filter('[value=""]').addClass('no-value');

        if (selected.hasClass('no-value')) {
            el.removeClass('has-selected');
        } else {
            el.addClass('has-selected');
        }
    }).on('change', function () {
        // add a change event listener to toggle the classes and attributes accordingly
        var el = $(this),
            selected = el.find('option').eq(this.selectedIndex);

        el.find('option.selected').removeClass('selected').removeAttr('selected');
        if (selected.hasClass('no-value')) {
            el.removeClass('has-selected');
        } else {
            el.addClass('has-selected');
        }
        selected.addClass('selected').attr('selected', 'selected');
    });

    // modal accessibility fix
    $('.modal').on('shown.bs.modal', function () {
        overwriteTabIndexAndAriaHiddenDifferentHierarchy($(this));
    }).on('hidden.bs.modal', function () {
        var el = $(this),
            dismissFocusElSelector = el.data('dismiss-focus-target');

        revertTabIndexAndAriaHiddenDifferentHierarchy(el);

        // if data-dimiss-focus-target is set, we'll set the focus to it once the modal has been closed
        if (undefined !== dismissFocusElSelector) {
            $(dismissFocusElSelector).focus();
        }

        
    });


    //Check if element has sroll data-toggle="modal"
    $('[data-toggle="modal"]').on('click', function () {
        if ($('.table-scrollable-wrapper').length > 0) checkScrollableTable();
    })

    checkClearFix();

})(jQuery);
// END document-ready

// for multiple modals - add back modal-open class when there is still open modal
$(document).on('hidden.bs.modal', '.modal', function () {
    if ($('.modal.show').length > 0) {
        $('body').addClass('modal-open');
    }
})

$(".custom-selected select").on('change', function () {
    var $this = $(this)
    $this.closest("div").find(".custom-select-trigger").find(".custom-select-trigger-label").text($this.find('option:selected').text());
});

$("a.collapse-trigger").on('click', function (){
    var dataTarget = $(this).attr('aria-controls');
        ID = '#'+dataTarget;
    
    // It will reset all the other icons except the clicked item icon
    var elementTargetSame = document.querySelectorAll("[data-target='" + ID + "']");
    var elementTargets = $(elementTargetSame).not(this);

    // var otherElementTarget = document.querySelectorAll("[data-toggle='collapse']").not();

    // console.log(elementTargets);

    if (elementTargets.find('span.icon').hasClass('icon-chevron-up')) {

        elementTargets.find('span.icon').removeClass('icon-chevron-up').addClass('icon-chevron-down');

    } else {
        elementTargets.find('span.icon').removeClass('icon-chevron-down').addClass('icon-chevron-up');
    }

    // close other tab
    var otherElementTarget = $("a.collapse-trigger[data-target!='" + ID + "']").not("[data-target='#additional-details-body']");

    if (otherElementTarget.find('span.icon').hasClass('icon-chevron-up')) {

        otherElementTarget.find('span.icon').removeClass('icon-chevron-up').addClass('icon-chevron-down');

    }
});

$('.tooltip-interactive').on('show.bs.tooltip', function () {
    $('.tooltip-interactive').not(this).tooltip('hide');
});

function checkScrollableTable() {
    if ($('#table-scrollable-wrapper').length > 0) {
        setTimeout(function () {
            var el = document.getElementById('table-scrollable-wrapper');

            if (el.scrollWidth > el.clientWidth) {
                $('.sticky-element').addClass("scrollTrue");
            } else {
                $('.sticky-element').removeClass("scrollTrue");
            }
        }, 200);
    }
}
var resizeTimeoutFn = 0;
$(window).on('resize', function () {
    clearTimeout(resizeTimeoutFn);
    resizeTimeoutFn = setTimeout(function () {
        checkScrollableTable();
        checkClearFix();
    }, 200);
});

function checkClearFix() {
    if ($('#fixedHeader').length > 0) {
        if (window.matchMedia("(min-width: 992px)").matches) {
            $('#fixedHeader').removeClass('d-none');
        } else {
            if ($('#fixedHeader').find('.clearfix').length == 0) $('#fixedHeader').addClass('d-none');
        }
    }
}