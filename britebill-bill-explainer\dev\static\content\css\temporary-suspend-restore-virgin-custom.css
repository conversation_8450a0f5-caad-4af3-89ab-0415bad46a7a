﻿/* NOTE: use mobile-first media queries. place your styles under proper categories.
   Custom Styles for Virgin temporary restore
*/

.virgin-footer-links .footer-links li a {
    color:#CBCBCB;
}

.standard-minimal-footer.virgin-footer-links .legal-links ul li {
    display: flex;
    align-items: center;
}

    .standard-minimal-footer.virgin-footer-links .legal-links ul li:not(:first-child) {
        margin-top: 0;
    }

    .standard-minimal-footer.virgin-footer-links .legal-links ul li:not(:last-child):after {
        border-right: 1px solid #D4D4D4;
        content: '';
        display: inline-block;
        height: 12px;
        margin: 0 13px;
    }
 
.standard-minimal-footer.virgin-footer-links .footer-links {
    display: flex;
}

.standard-minimal-footer.virgin-footer-links .skip-to-main-link {
    display:block;
}

/*Header*/
.virgin-header-links .standard-step-flow-header-upper a:focus,
.virgin-header-links .standard-step-flow-header-upper button:focus {
    outline: none !important;
    box-shadow: 0 0 0px 3px #000, 0 0 2px 3px #000, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
}

.txtSize36 {
    font-size: 36px;
}

/*Helpers*/
.txtVirginYellow {
    color: #E99E00;
}
.txtVirginRed{
    color: #CC0000;
}
.txtVirginBlack{
    color: #222;
}
.txtNoUnderlineFocus:focus,.txtNoUnderlineFocus:hover{
    text-decoration: none;
}
.line-height-17{
    line-height: 17px;
}
.line-height-24{
    line-height: 24px;
}
/*Helpers*/
/*Modal*/
/*Modal*/

.modal-suspend-virgin.modal {
    padding-top: 0;
}

.modal-suspend-virgin .small-title {
    color: #333333;
    font-size: 22px;
    letter-spacing: 0;
    line-height: 24px;
    margin-bottom: 0;
}


.modal-suspend-virgin .modal-content {
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
    border-radius: 0;
}


.modal-suspend-virgin .modal-header {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    padding: 25px 30px;
}

    .modal-suspend-virgin .modal-header .close {
        color: #000;
        opacity: 0.75;
        margin-top: 0px;
        margin-right: 0px;
    }

        .modal-suspend-virgin .modal-header .close:hover,
        .modal-suspend-virgin .modal-header .close:focus {
            opacity: 1;
        }

/*Radio button*/
.virgin-step-flow-main-content .ctrl_element {
    outline: none !important;
    top: 50%;
    margin-top: -12px;
}
.virgin-step-flow-main-content .graphical_ctrl > [type="radio"] {
    z-index: 1;
}

.virgin-step-flow-main-content .graphical_ctrl > [type="radio"] .ctrl_element {
    z-index: 0;
}
.virgin-step-flow-main-content .graphical_ctrl label {
    margin-bottom: 0;
}

.virgin-step-flow-main-content .form-group-error {
    color: #BD2025;
    border-color: #BD2025;
    transition: border-color .5s cubic-bezier(.55,0,.1,1),color .5s cubic-bezier(.55,0,.1,1);
}

.virgin-step-flow-main-content .graphical_ctrl > [type="radio"]:focus ~ .ctrl_element:before {
    content: " ";
    display: block;
    position: absolute;
    left: -1px;
    top: -1px;
    width: 24px;
    height: 24px;
    border-radius: 50% !important;
    background-color: transparent;
    box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #257fa3, 0 0 2px 5px #257fa3;
}


.virgin-step-flow-main-content .title {
    color:#333;
}

.tsrs-main.virgin-step-flow-main-content .graphical_ctrl > input[type="radio"]:checked ~ label {
    font-weight: normal;
}

/*Select Box*/
.form-control-select + span.txtVirginGray {
    color: #BDBDBD;
}

/*Utilities*/

/*Text color*/
.txtLightRed {
    color:#CC0000;
}


/*FOCUS BUTTON OVERRIDE FOR FOCUS OUTLINE HOVER*/
/*Bordered White button in white Background*/
.accss-bordrBtn-white-bg:focus,
.accss-bordrBtn-white-bg:active,
.accss-bordrBtn-white-bg:hover,
/*Bordered Dark and Light Gray button in gray Background*/
.accss-bordrBtn-gray-bg:focus,
.accss-bordrBtn-gray-bg:active,
.accss-bordrBtn-gray-bg:hover {
    color: #fff !important;
    background-color: #333 !important;
    border-color: #333 !important;
    text-decoration: none !important;
}
/*Black button in white Background*/
.accss-whiteBtn-black-bg:focus,
.accss-whiteBtn-black-bg:active,
.accss-whiteBtn-black-bg:hover {
    color: #000;
    background-color: #fff;
    border-color: #fff;
    text-decoration: none;
}
/*Red button in red Background*/
.accss-redBtn-red-bg:focus,
.accss-redBtn-red-bg:active,
.accss-redBtn-red-bg:hover {
    color: #000;
    background-color: #eee;
    border-color: #eee;
    text-decoration: none;
}

/*Red button with light red focus and hover in Black or white bacground*/
.accss-redBtn-whte-blck-bg:focus,
.accss-redBtn-whte-blck-bg:active,
.accss-redBtn-whte-blck-bg:hover {
    background-color: #eb0000;
    border-color: #eb0000;
    color: #fff;
    text-decoration: none;
}

.cmp-tsr-info .lnkUnderline {
    text-decoration:underline;
}

.cmp-tsr-info .lnkUnderline:focus {
    text-decoration: none;
}


/*Media queries*/
/*Dektop*/
/*For overriding the container*/
@media (min-width: 1240px) {
    .virgin-step-flow-main-content .max-width-container {
        padding-left: 0px !important;
        padding-right: 0px !important;
        width: 1200px !important;
    }

    .standard-step-flow-header .max-width-container {
        padding: 0px !important;
        width: 1200px !important;
    }
    
}

@media (min-width: 992px) {
    .virgin-step-flow-main-content .max-width-container {
        padding-left: 15px;
        padding-right: 15px;
        width: 100%;
        max-width: 100%;
    }

    .standard-step-flow-header .max-width-container {
        padding: 0 15px;
        width: 100%;
        max-width: 100%;
    }

}


/*Mobile & Tablet*/
@media screen and (max-width: 991.98px) {
    .standard-minimal-footer.virgin-footer-links .footer-links {
        justify-content:center;
    }

    .virgin-step-flow-main-content .max-width-container {
        width: 100%;
        max-width:100%;
    }

    .standard-step-flow-header .max-width-container {
        width: 100%;
        max-width: 100%;
    }
}

/*Mobile*/
@media screen and (max-width:767.98px) {
    .virgin-step-flow-main-content .max-width-container {
        padding: 0;
    }

    .standard-step-flow-header .max-width-container {
        padding: 0;
    }

    .standard-minimal-footer.virgin-footer-links .footer-links {
        margin-bottom: 5px;
        flex-wrap: wrap;
    }

    .standard-minimal-footer.virgin-footer-links .legal-links ul li {
        margin-bottom: 10px;
    }

    .no-pad-xs {
        padding: 0;
    }

    .virgin-step-flow-main-content .virgin-confirm-step {
        display: flex;
    }

        .virgin-step-flow-main-content .virgin-confirm-step .btn-primary {
            margin-left: 0px;
            margin-bottom: 20px;
        }

        .virgin-step-flow-main-content .virgin-confirm-step a.lnkUnderline {
            margin: 0 auto;
            align-self: center;
            display: inline-block;
        }

    .virgin-step-flow-main-content .error-container {
        padding:30px 15px;
    }

    /*Modal*/
    .modal-suspend-virgin .modal-dialog {
        height: auto;
        max-height: calc(100% - 45px);
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
    }

    .modal-suspend-virgin .modal-content {
        border: 0;
    }

    .modal-suspend-virgin.modal.fade .modal-dialog {
        top: unset;
        transform: none !important;
        margin: 0;
        padding: 0;
    }

    .modal-suspend-virgin .modal-body .modal-buttons {
        display: flex;
        flex-direction: column;
        width: 100%;
        margin-bottom:0;
    }

        .modal-suspend-virgin .modal-body .modal-buttons button:first-of-type {
            margin-bottom:10px;
        }

        .modal-suspend-virgin .modal-body .modal-buttons button {
            width: 100%;
        }

    .modal-suspend-virgin .modal-header {
        padding:25px 15px;
        align-items: center;
    }

        .modal-suspend-virgin .modal-header .small-title {
            font-size: 18px;
            padding-right: 19%;
            font-family: "Arial";
            line-height: 21px;
        }


}

@media screen and (max-width: 767.98px) {
    .padding-h-xs-15{
        padding-left: 15px !important;
        padding-right: 15px !important;
    }
}

/*smaller mobile*/
@media (max-width:425px) {
    .standard-minimal-footer.virgin-footer-links .legal-links ul li:not(:last-child):after {
        margin: 0 10px;
    }
    
}

@media (max-width:525px) {
    .standard-minimal-footer.virgin-footer-links .legal-links ul li:nth-child(3):after {
        display: none;
    }

    .standard-minimal-footer.virgin-footer-links .legal-links ul {
        margin-left:20px;
        margin-right:20px;
    }

}

/*Tablet*/
@media (min-width:768px) and (max-width:991.98px) {
    .virgin-footer-links .footer-links {
        margin-bottom: 30px;
    }
    .modal-suspend-virgin .modal-body {
        padding: 16px 30px;
    }

    /*Utilities*/
    .txtBold-sm-md {
        font-weight:bold;
    }

    .virgin-step-flow-main-content .max-width-container.container {
        padding-left:15px;
        padding-right:15px;
    }

    .modal-suspend-virgin.modal:before, .modal-suspend-virgin .modal-dialog {
        vertical-align: middle;
    }

}


/*Desktop*/
@media (min-width:991px) {
    .modal-suspend-virgin .modal-body {
        padding:16px 30px;
    }


    /*Utilities*/
    .txtBold-sm-md {
        font-weight: bold;
    }

}

/*IE Target only*/

/* START IE10+ CSS styles go here */
/*Mobile*/
/*Modal position*/
@media all and (-ms-high-contrast: none) and (min-width:320px) and (max-width:767.68px),
    (-ms-high-contrast: active) and (min-width:320px) and (max-width:767.68px) {
        .modal-suspend-virgin.modal.fade .modal-dialog {
            top: auto;
        }
}

.tsrs-main .accordionContainer > .accordion-wrap > div > a[aria-expanded="false"] > span:not(.icon) {
    color: #007cad;
}