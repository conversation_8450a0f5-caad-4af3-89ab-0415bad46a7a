
import * as React from "react";
import { LOB_MOBILE, SERVICE_TYPE_PACKAGE, SERVICE_TYPE_PLAN } from "../../utils/Constants";
import { FormattedFromToDate, FormattedFromToDateSR, getCharge, getGroupedItems, getLobData, getPreviousData, getBillPeriodByBillCloseDate } from "../../utils/Utility";
import { IBillExplainerBody } from "./IBillExplainerBody";
import { fixPBELayout, refreshStickyNav, ExtractedFormattedMessage, FormattedDollarsAmount, CustomFormattedDateWithoutYearLong } from "singleban-components";
import { FormattedMessage } from "react-intl";

const PBEProration = (props: IBillExplainerBody) => {
    const { currentBillChgItems, partialChgCrdItems, refundItems } = getGroupedItems(props.chargeItems, props.cycleStartDate);

    const partialChgCrdItem = partialChgCrdItems?.length && partialChgCrdItems?.length >= 1 ? partialChgCrdItems[0] : null;
    const service = props.subscriberDetails?.subscriberType;
    const { lobName } = getLobData(service);
    const serviceType = service.toUpperCase().trim() === LOB_MOBILE ? SERVICE_TYPE_PLAN : SERVICE_TYPE_PACKAGE;
    const refund = getCharge(refundItems);

    const { serviceModifyDate, chargedDays, width } = getPreviousData(props.cycleStartDate, props.cycleEndDate, partialChgCrdItem);
    const { currentStartDate, currentEndDate, prevStartDate, prevEndDate } = getBillPeriodByBillCloseDate(props.billCloseDate);
    const newPackageChargePartial = getCharge(partialChgCrdItems);
    const newPackageCharge = getCharge(currentBillChgItems);

    React.useEffect(() => {
        refreshStickyNav();
    });
    React.useEffect(() => {
        fixPBELayout(true);
    });
    return (
        <div className="container pad-b-45">
            <div className="pbe-chart pbe-proration-chart col-sm-10 margin-auto pad-30 pad-xs-0 borderRadiusAll10 border-gray2 no-borders-xs pad-h-xs-0">
                <h3 className="subtitle-2 margin-b-15">
                    <FormattedMessage id={lobName}>
                        {(serviceName: string) => <ExtractedFormattedMessage id={`PAGE_PRORATION_${serviceType}_HEADER3`} values={{ service: serviceName }} />}
                    </FormattedMessage>
                </h3>
                <p className="margin-b-10">
                    {/* Last month you changed your Internet package. Because your services are billed in advance, you had already paid for the full month at your old rate. As a result: */}
                    {/* <FormattedHTMLMessage id={props.description} /> */}
                </p>
                <ul className="margin-b-30 margin-l-15 pad-l-0">
                    {!!refund &&
                        <li>
                            <FormattedDollarsAmount amount={refund} showHyphenIfAmountIsNull={false}>{(amount: string) => <ExtractedFormattedMessage id={`PAGE_PRORATION_${serviceType}_BODY1`} values={{ amount: amount }} />}</FormattedDollarsAmount>
                        </li>}
                    <li>
                        <FormattedDollarsAmount amount={newPackageChargePartial} showHyphenIfAmountIsNull={false}>{(amount: string) => <ExtractedFormattedMessage id={`PAGE_PRORATION_${serviceType}_BODY2`} values={{ amount: amount }} />}</FormattedDollarsAmount>
                    </li>
                    <li>
                        <FormattedDollarsAmount amount={newPackageCharge} showHyphenIfAmountIsNull={false}>{(amount: string) => <ExtractedFormattedMessage id={`PAGE_PRORATION_${serviceType}_BODY3`} values={{ amount: amount }} />}</FormattedDollarsAmount>
                    </li>
                </ul>
                <div className="pbe-wrapper row pad-h-15 same-height-wrap">
                    <div className="pbe-col col-6 pad-4-right">
                        <div className="txtCenter margin-b-15">
                            <h3 className="subtitle-2 d-flex justify-center align-items-end same-height" data-same-height-index="1">
                                <ExtractedFormattedMessage id="PREVIOUS_BILL" />
                            </h3>
                            <div className="txtSize12">
                                <span aria-hidden="true"><FormattedFromToDate startDate={prevStartDate} endDate={prevEndDate} /></span>
                                <span className="sr-only"><FormattedFromToDateSR startDate={prevStartDate} endDate={prevEndDate} /></span>
                            </div>
                        </div>
                        <div className="sr-only">
                            <div>
                                <ExtractedFormattedMessage id={`PAGE_PRORATION_PREVIOUS_${serviceType}`} />
                                <FormattedFromToDate startDate={prevStartDate} endDate={serviceModifyDate} />
                                <ExtractedFormattedMessage id="PAGE_PRORATION_PREVIOUS_PACKAGE_PLAN_BODY" />
                                {/* Old package {Month day} to {Month day} (paid one month in advance) */}
                            </div>
                            <div>
                                <ExtractedFormattedMessage id={`PAGE_PRORATION_${serviceType}_CHANGE`} />
                                <CustomFormattedDateWithoutYearLong date={serviceModifyDate}>{(date: string) => date}</CustomFormattedDateWithoutYearLong>
                                {/* Package change February 10 */}
                            </div>
                            <div>
                                <ExtractedFormattedMessage id={`PAGE_PRORATION_NEW_${serviceType}`} />
                                <ExtractedFormattedMessage id="PAGE_PRORATION_NEW_PACKAGE_PLAN_BODY" values={{ days: chargedDays }} />
                                <FormattedDollarsAmount amount={newPackageChargePartial} showHyphenIfAmountIsNull={false}>{(amount: string) => amount}</FormattedDollarsAmount>
                                {/* New package (charge for 10 days) 18.32 dollars */}
                            </div>
                            {!!refund && <div>
                                <ExtractedFormattedMessage id={`PAGE_PRORATION_PREVIOUS_${serviceType}`} />
                                <ExtractedFormattedMessage id="PAGE_PRORATION_REFUND" />
                                <FormattedDollarsAmount amount={refund} showHyphenIfAmountIsNull={false}>{(amount: string) => amount}</FormattedDollarsAmount>
                                {/* Old package refund -24.32 dollars */}
                            </div>}
                        </div>
                        <div className="js-split-wrapper relative invisible-force" data-split-divider-width="8" data-split-divider-margin="2" data-js-split-percentage={width}>
                            <div className="upper-line-container relative bgGray19 pad-t-20" aria-hidden="true">
                                <div className="pbe-line"></div>
                            </div>
                            <div className="bars-container bgGray19" aria-hidden="true">
                                <div className="d-flex height-31">
                                    <div className="split-bar-left overflowHidden">
                                        <div className="h-100 bgGray19"></div>
                                    </div>
                                    <div className="split-bar-divider"></div>
                                    <div className="split-bar-right overflowHidden">
                                        <div className="h-100 bgBlue"></div>
                                    </div>
                                </div>
                                <div className="spacer3"></div>
                                <div className="d-flex height-31">
                                    <div className="split-bar-left overflowHidden">
                                        <div className="h-100 bgBlueExtraLight border-new-grey"></div>
                                    </div>
                                    <div className="split-bar-divider"></div>
                                    <div className="split-bar-right overflowHidden">
                                        <div className="h-100 bgBlueExtraDark2"></div>
                                    </div>
                                </div>
                                <div className="split-divider-indicator pbe-prev-package-change-blue-line-inner">
                                    <div className="h-100"></div>
                                </div>
                            </div>
                            <div className="labels-container" aria-hidden="true">
                                <div className="label-group-top relative bgGray19">
                                    <div className="split-right-label d-flex flex-column">
                                        <div className="w-100">
                                            <div className="surtitle-black">
                                                {/* New package */}
                                                <ExtractedFormattedMessage id={`PAGE_PRORATION_NEW_${serviceType}`} />
                                            </div>
                                            <div className="small-text margin-3-top">
                                                {/* (charge for 10 days) */}
                                                <ExtractedFormattedMessage id="PAGE_PRORATION_NEW_PACKAGE_PLAN_BODY" values={{ days: chargedDays }} />
                                            </div>
                                            <div className="small-text">
                                                {/* <span>$</span>18.32 */}
                                                <FormattedDollarsAmount amount={newPackageChargePartial} showHyphenIfAmountIsNull={false}>{(amount: string) => amount}</FormattedDollarsAmount>
                                            </div>
                                        </div>
                                        <div className="label-indicator-line-top">
                                            <div></div>
                                        </div>
                                    </div>
                                    <div className="split-divider-label d-flex flex-row">
                                        <div className="w-100">
                                            <div className="surtitle-black">
                                                {/* Package change */}
                                                <ExtractedFormattedMessage id={`PAGE_PRORATION_${serviceType}_CHANGE`} />
                                            </div>
                                            <div>
                                                {/* February 10 */}
                                                <CustomFormattedDateWithoutYearLong date={serviceModifyDate}>{(date: string) => date}</CustomFormattedDateWithoutYearLong>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="label-group-bottom relative">
                                    <div className="split-origin-label d-flex">
                                        <div className="label-indicator-line-origin">
                                        </div>
                                        <div>
                                            <div className="surtitle-black">
                                                {/* Old package */}
                                                <ExtractedFormattedMessage id={`PAGE_PRORATION_PREVIOUS_${serviceType}`} />
                                            </div>
                                            <div className="small-text margin-3-top">
                                                {/* (paid one month in advance) */}
                                                <ExtractedFormattedMessage id="PAGE_PRORATION_PREVIOUS_PACKAGE_PLAN_BODY" />
                                            </div>
                                        </div>
                                    </div>
                                    {!!refund && <div className="split-right-label d-flex flex-column">
                                        <div className="label-indicator-line-bottom">
                                            <div></div>
                                        </div>
                                        <div className="w-100">
                                            <div className="surtitle-black">
                                                {/* Refund */}
                                                <ExtractedFormattedMessage id="PAGE_PRORATION_REFUND" />
                                            </div>
                                            <div className="small-text margin-3-top">
                                                {/* - <span>$</span>24.32 */}
                                                <FormattedDollarsAmount amount={refund} showHyphenIfAmountIsNull={false}>{(amount: string) => amount}</FormattedDollarsAmount>
                                            </div>
                                        </div>
                                    </div>}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="pbe-col col-6 pad-4-left">
                        <div className="txtCenter margin-b-15">
                            <h3 className="subtitle-2 d-flex justify-center align-items-end same-height" data-same-height-index="1">
                                {/* Current bill */}
                                <ExtractedFormattedMessage id="CURRENT_BILL" />
                            </h3>
                            <div className="txtSize12">
                                {/* <span aria-hidden="true">Feb 20 - Mar 19</span> <span className="sr-only">February 20 to March 19</span> */}
                                <span aria-hidden="true"><FormattedFromToDate startDate={currentStartDate} endDate={currentEndDate} /></span>
                                <span className="sr-only"><FormattedFromToDateSR startDate={currentStartDate} endDate={currentEndDate} /></span>
                            </div>
                        </div>
                        <div className="sr-only">
                            <div>
                                <ExtractedFormattedMessage id={`PAGE_PRORATION_NEW_${serviceType}`} />
                                <ExtractedFormattedMessage id="PAGE_PRORATION_NEW_PACKAGE_PLAN_ADVANCE_BODY" values={{ days: chargedDays }} />
                                <FormattedDollarsAmount amount={newPackageCharge} showHyphenIfAmountIsNull={false}>{(amount: string) => amount}</FormattedDollarsAmount>
                                {/* New package (charged one month in advance) 54.95 dollars */}
                            </div>
                        </div>
                        <div className="js-split-wrapper relative invisible-force" data-split-divider-width="8" data-split-divider-margin="2" data-js-split-percentage="null">
                            <div className="upper-line-container relative bgGray19 pad-t-20" aria-hidden="true">
                                <div className="pbe-line"></div>
                            </div>
                            <div className="bars-container single-bar bgGray19" aria-hidden="true">
                                <div className="d-flex height-31">
                                    <div className="split-bar-left overflowHidden">
                                        <div className="h-100 bgGray19"></div>
                                    </div>
                                    <div className="split-bar-divider"></div>
                                    <div className="split-bar-right overflowHidden">
                                        <div className="h-100 bgBlue"></div>
                                    </div>
                                </div>
                            </div>
                            <div className="labels-container" aria-hidden="true">
                                <div className="label-group-top relative bgGray19">
                                </div>
                                <div className="label-group-bottom relative">
                                    <div className="split-right-label d-flex flex-column">
                                        <div className="label-indicator-line-bottom">
                                            <div></div>
                                        </div>
                                        <div className="w-100">
                                            <div className="surtitle-black">
                                                {/* New package */}
                                                <ExtractedFormattedMessage id={`PAGE_PRORATION_NEW_${serviceType}`} />
                                            </div>
                                            <div className="small-text margin-3-top">
                                                {/* (charged one month in advance) */}
                                                <ExtractedFormattedMessage id="PAGE_PRORATION_NEW_PACKAGE_PLAN_ADVANCE_BODY" values={{ days: chargedDays }} />
                                            </div>
                                            <div className="small-text">
                                                {/* <span>$</span>54.95 */}
                                                <FormattedDollarsAmount amount={newPackageCharge} showHyphenIfAmountIsNull={false}>{(amount: string) => amount}</FormattedDollarsAmount>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PBEProration;