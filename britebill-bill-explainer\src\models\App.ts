import { LocalizationState } from "bwtk";
import { ICSMTransaction, IChargeItem, ISubscriberDetails } from "./IBillExplainerBill";

export interface IAppOwnProps {
  configLinks: any;
  isUXPMode: boolean;
  pbeData: IPBE;
  loadedFromOtherWidget: boolean;
  isPbeModalLinkDisabled: boolean;
}

export interface IAppStateToProps {
  localization: LocalizationState;
  pbeCategory: string;
  description: string;
  billCloseDate: string;
  startDate: string;
  endDate: string;
  subscriberDetails: ISubscriberDetails;
  chargeItems: IChargeItem[];
  fetchBillsStatus: IRequestStatus;
  titleKey: string;
  descriptionKey: string;
  detailedDescKey: string;
  previousPeriodStartDate: string;
  previousPeriodEndDate: string;
  currentPeriodStartDate: string;
  currentPeriodEndDate: string;
  transactions: ICSMTransaction[];
  useLegendsForDiagram: boolean;
}

export interface IAppDispatchToProps {

}

export interface IAppMergedProps extends IAppOwnProps, IAppStateToProps, IAppDispatchToProps {

}


export enum IRequestStatus {
  IDLE,
  PENDING,
  COMPLETED,
  FAILED
}


export interface IPBEDataBag {
  lobType: string;
  identifier: string;
  subNo: string;
  nickname: string;
  overageDataValue: number;
  overageDataCharge: number;
  showUsageLink: boolean | null | undefined;
  overageDataUnit: string;
  acctNo: string;
  encryptAcctNo: string;
  encryptedIdentifier: string;
  encryptedBan: string;
  seqNo?: number;
  equipmentName: string | null | undefined;
  totalCalls: number;
  totalCharge: number;
  totalCallDuration: number;
  serviceName?: string;
  startDate: string;
  endDate: string;
  devicePurchasePrice: number,
  agreementCredit: number,
  reducedDevicePrice: number,
  deviceTax: number,
  totalDevicePrice: number,
  downPayment: number,
  deviceReturnOption: number,
  financedAmount: number,
  actualAgreementEndDate: string;
  originalAgreementEndDate: string;
  monthsRemaining: number;
  totalFinancedAmountBalance: number;
  totalAgreementCreditBalance: number;
  currentInstallmentNo: number;
  totalInstallmentNo: number;
  deviceName: string;
  remainingBalance: number;
  totalBalance: number;
  remainingBalanceDate: string;
  monthlyPayment: number;
  firstPaymentDate: string;
  lastPaymentDate: string;
  isEligibleForDeviceUpgrade: boolean;
  currentInstallmentNumber?: number;
  totalInstallmentNumberTerm?: number;
  leagueName?: string;
  isLeagueCancelled?: boolean;
  installmentAmount?: number;
  date : string;
  accNoEncrypt : string;
  isSubscriberCancelled?: boolean;
  totalTextMessages: number;
  totalEvents?: number;
  maxEventsToShow?: number;
  billCycle?: number;
  billCycleMonth?: number;
  overageType?: string;
  tiers:ITier[];
  latestBillNo:number;
}

export interface ITier {
  rateDetail: string;
  dataUsed: string;
  tierChanged: number;
}

export interface IPBE {
  pbeId: string;
  actionUrl: string;
  featureCode: string;
  timelineEntryPointKey: string;
  titleKey: string;
  descriptionKey1: string;
  descriptionKey2: string;
  pbeIconName: string;
  pbeDataBag: IPBEDataBag;
  triggerPbeOmniture: (data: any) => void;
}