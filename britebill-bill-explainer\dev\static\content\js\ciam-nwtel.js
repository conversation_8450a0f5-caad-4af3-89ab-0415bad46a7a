var CIAM_NWTEL = window.CIAM_NWTEL || {
    init: function () {
        // custom pre-init scripts
        this.preInit();

        // global/core events (only scripts that could possibly be moved to core should be added here)
        this.initEvents();

        // custom events
        this.initCustomEvents();

        // components (only scripts for components that could possibly be moved to core should be added here)
        this.initComponents();

        // custom post-init scripts
        this.postInit();

        // custom for credentials recovery
        this.selectRadioRecoveryInit();

        // for show password
        this.showPasswordInit();

    },
    preInit: function () {
        // START commented-out sample code to define a different set of functions to validate in input strength measurer
        //$('#password.js-input-measurer').data('input-measurer-tests', [
        //    function (value) {
        //        return /.{8,}/.test(value);
        //    },
        //    function (value) {
        //        return /[A-Z]/.test(value);
        //    },
        //    function (value) {
        //        return /[a-z]/.test(value);
        //    },
        //    function (value) {
        //        return /\d/.test(value);
        //    }
        //]);
        // END commented-out sample code to define a different set of functions to validate in input strength measurer
    },
    initEvents: function () { // only scripts that could possibly be moved to core should be added here
        var $body = $('body');

        if ($body.data('js-click-on-space-delegate-initialized') !== true) {
            $body.data('js-click-on-space-delegate-initialized', true).on('keydown', '.js-click-on-space-delegate', function (e) {
                var $this;

                if (32 === (e.which || e.keyCode || 0)) {
                    e.preventDefault();
                    e.stopPropagation();

                    $this = $(this);
                    if ($this.is('a')) {
                        this.click();
                    } else {
                        $this.click();
                    }
                }
            });
        }

        if ($body.data('js-escape-tooltip-initialized') !== true) {
            $body.data('js-escape-tooltip-initialized', true).on('keydown', function (e) {
                var selectors = $body.data('js-tooltip-selectors') || '.js-tooltip',
                    $matches;

                if (27 === (e.which || e.keyCode || 0)) {
                    $matches = $(selectors).filter(':visible:not(.js-persist-on-esc)');
                    if ($matches.length > 0 && $matches.tooltip) {
                        $matches.tooltip('hide');
                    }
                }
            });
        }
    },
    initCustomEvents: function () {
        var resizeFn;
        $(window).resize(function () {
            clearTimeout(resizeFn);
            resizeFn = setTimeout(CIAM_NWTEL.setPasswordStrengthTooltipPlacement, 200);
        });

        $('#password.js-input-measurer').on('inputmeasure', function () {
            CIAM_NWTEL.updatePasswordStrengthTooltipContent();
        });

        $('.js-tooltip').on('jstooltipinitialized', function () {
            var $tooltip = $(this);

            // workaround for bootstrap/popover removing original aria-describedby attribute value from triggers
            $tooltip.data('aria-describedby', $tooltip.attr('aria-describedby')).on('show.bs.tooltip', function () {
                var $this = $(this);

                $this.data('aria-describedby', $this.attr('aria-describedby'));
            }).on('shown.bs.tooltip', function () {
                var $this = $(this),
                    originalAriaDescribedBy = $this.data('aria-describedby'),
                    ariaDescribedBy;

                if (originalAriaDescribedBy) {
                    ariaDescribedBy = $this.attr('aria-describedby');

                    if (ariaDescribedBy.indexOf(originalAriaDescribedBy) === -1) {
                        $this.attr('aria-describedby', originalAriaDescribedBy + ' ' + ariaDescribedBy);
                    }
                }
            }).on('hidden.bs.tooltip', function () {
                var $this = $(this),
                    originalAriaDescribedBy = $this.data('aria-describedby'),
                    ariaDescribedBy;

                if (originalAriaDescribedBy) {
                    ariaDescribedBy = $this.attr('aria-describedby') || '';

                    if (ariaDescribedBy.indexOf(originalAriaDescribedBy) === -1) {
                        $this.attr('aria-describedby', $.trim(originalAriaDescribedBy + ' ' + ariaDescribedBy));
                    }
                }
            });
        });
    },
    initComponents: function () { // only scripts for components that could possibly be moved to core should be added here
        // tooltips - custom
        $('.js-tooltip:not(.js-tooltip-initialized)').addClass('js-tooltip-initialized').each(function () {
            var $this = $(this);

            $this.tooltip();

            // fire custom post init event
            $this.trigger('jstooltipinitialized');
        });

        // progressbar - jquery-ui
        $('.js-milestone-progressbar:not(.js-milestone-progressbar-initialized)').addClass('js-milestone-progressbar-initialized').each(function () {
            var $this = $(this),
                options = $this.data(),
                milestones = [0, 50, 100],
                dataMilestones = $this.data('milestones'),
                classes = ['bgRed', 'bgYellow', 'bgGreen'],
				dataClasses = $this.data('milestones-classes'),
				valuetext,
				finalValuetext,
				dataValuetext = $this.data('valuetext'),
				changeEventListener = function () {
					var $input = $(this),
						min = parseFloat($input.attr('aria-valuemin')),
						percentage = (parseFloat($input.attr('aria-valuenow')) - min) / (parseFloat($input.attr('aria-valuemax')) - min) * 100,
						fillClassRemove = '',
						fillClass = '';

					milestones.forEach(function (milestone, i) {
						if (percentage < milestone) {
							fillClassRemove += classes[i] + ' ';
						} else {
							if (fillClass.length > 0) {
								fillClassRemove += fillClass + ' ';
							}

							fillClass = classes[i];
						}
					});

					$input.find('.ui-progressbar-value').removeClass(fillClassRemove).addClass(fillClass);

					// if data-valuetext is defined, set aria-valuetext accordingly
					if (valuetext) {
						for (var valuetextUpperBreakpoint in valuetext) {
							if (percentage >= parseFloat(valuetextUpperBreakpoint)) {
								finalValuetext = valuetext[valuetextUpperBreakpoint];
							}
						}
						$this.attr('aria-valuetext', finalValuetext);
					}
				};

            if (dataMilestones) {
                milestones = [];
                dataMilestones.split(',').forEach(function (data) {
                    milestones.push(parseFloat(data));
                });
            }

            if (dataClasses) {
                classes = dataClasses.split(',');
			}

			if (dataValuetext) {
				valuetext = dataValuetext;
			}

            // add change event listener for progress-based styling. this is how outside source can update this component
            options = $.extend(options, {
				change: changeEventListener
            });

			$this.progressbar(options);

			// trigger change listener upon init to process any default/init value
			changeEventListener.call(this);

            // fire custom post init event
            $this.trigger('jsmilestoneprogressbarinitialized');
        });

        // milestone summary - custom
        $('.js-milestone-summary:not(.js-milestone-summary-initialized)').addClass('js-milestone-summary-initialized').each(function () {
            var $this = $(this),
                $items = $this.find('.js-milestone-summary-item'),
                milestones = [],
                milestoneItems = [];

            $items.each(function () {
                var $item = $(this),
                    milestone = $item.data('milestone');

                $item.addClass('d-none');

                if (milestone != null) {
                    milestone = parseFloat(milestone);
                    milestones.push(milestone);
                    milestoneItems.push($item);
                }
            });

            // add custom event listener for change of milestone reached. this is how outside source can update this component
            $this.on('milestonechange', function (e, percentage) {
                var furthestMilestone;

                if (percentage != null) {
                    milestones.forEach(function (milestone, i) {
                        if (percentage < milestone) {
                            milestoneItems[i].addClass('d-none');
                        } else {
                            if (furthestMilestone) {
                                furthestMilestone.addClass('d-none');
                                furthestMilestone = null;
                            }
                            furthestMilestone = milestoneItems[i];
                            furthestMilestone.removeClass('d-none');
                        }
                    });
                }
            });

            // fire custom post init event
            $this.trigger('jsmilestonesummaryinitialized');
        });

        // readonly checklist - custom
        $('.js-readonly-checklist:not(.js-readonly-checklist-initialized)').addClass('js-readonly-checklist-initialized').each(function () {
            var $this = $(this),
                $items = $this.find('.js-readonly-checklist-item'),
                arrValues = $this.data('values') || [],
                i,
                count = $items.length,
                val;

            for (i = 0; i < count; i++) {
                val = arrValues[i];

                if (val === true) {
					$items.eq(i).find('.js-readonly-checklist-unchecked').addClass('d-none');
					$items.eq(i).find('.js-readonly-checklist-checked').removeClass('d-none');
                } else {
					arrValues[i] = false;
					$items.eq(i).find('.js-readonly-checklist-unchecked').removeClass('d-none');
					$items.eq(i).find('.js-readonly-checklist-checked').addClass('d-none');
                }
            }

            $this.data('values', arrValues);

            // add custom event listener for setting checklist state. passed args should be [index, value]. this is how outside source can update this component
            $this.on('checklistchange', function (e, i, isChecked) {
                var arrValues = $this.data('values');

                if (i != null && isChecked != null) {
					if (isChecked) {
						$items.eq(i).find('.js-readonly-checklist-unchecked').addClass('d-none');
						$items.eq(i).find('.js-readonly-checklist-checked').removeClass('d-none');
                        arrValues[i] = true;
					} else {
						$items.eq(i).find('.js-readonly-checklist-unchecked').removeClass('d-none');
						$items.eq(i).find('.js-readonly-checklist-checked').addClass('d-none');
                        arrValues[i] = false;
                    }
                }

                $this.data('values', arrValues);
            });

            // fire custom post init event
            $this.trigger('jsreadonlychecklistinitialized');
        });

        // input strength measurer (needs other bound components to have a purpose) - custom
        $('.js-input-measurer:not(.js-input-measurer-initialized)').addClass('js-input-measurer-initialized').each(function () {
            var $this = $(this),
                inputTimeoutFn,
                $summary = $this.data('input-measurer-summary-id'), // corresponding milestone summary component
                $meter = $this.data('input-measurer-meter-id'), // corresponding progressbar component
                $checklist = $this.data('input-measurer-checklist-id'), // corresponding readonly checklist component
                delay = $this.data('input-measurer-delay') || 200,
                tests = $this.data('input-measurer-tests'),
                defaultTests,
                dataValuesMap = $this.data('input-measurer-values-map'),
                valuesMap;

            if ($summary) {
                $summary = $('#' + $summary);
            }

            if ($meter) {
                $meter = $('#' + $meter);
            }

            if ($checklist) {
                $checklist = $('#' + $checklist);
            }

            if (dataValuesMap) {
                valuesMap = dataValuesMap;
            }

            // array of default functions that will validate the input. if a corresponding readonly checklist component is bound, this should be in the same order as the items in that list
            defaultTests = [
                function (value) {
                    return /.{8,}/.test(value);
                },
                function (value) {
                    return /[A-Z]/.test(value);
                },
                function (value) {
                    return /[a-z]/.test(value);
                },
                function (value) {
                    return /\d/.test(value);
                }
            ];

            // if data-input-measurer-tests is set, use it instead
            tests = tests && Array.isArray(tests) ? tests : defaultTests;

            $this.on('input', function () {
                var $input = $(this);

                clearTimeout(inputTimeoutFn);

                inputTimeoutFn = setTimeout(function () {
                    var value = $input.val(),
                        testCount = tests.length,
                        testPassedCount = 0,
                        milestonePercentage,
                        newPercentage;

                    tests.forEach(function (testFn, i) {
                        var testPassed = testFn(value);

                        if (testPassed) {
                            testPassedCount++;
                        }

                        // if a corresponding readonly checklist is bound, trigger its corresponding event with the new data
                        if ($checklist) {
                            $checklist.trigger('checklistchange', [i, testPassed]);
                        }
                    });

                    milestonePercentage = testPassedCount / testCount * 100;

                    // if for some reason the percentage values to be displayed/announced doesn't match the actual percentage of tests passed, use input-measurer-values-map to define the mapping
                    for (var realValue in valuesMap) {
                        if (milestonePercentage >= parseFloat(realValue)) {
                            newPercentage = parseFloat(valuesMap[realValue]);
                        }
                    }
                    milestonePercentage = newPercentage || milestonePercentage;

                    // if a corresponding milestone summary is bound, trigger its corresponding event with the new data
                    if ($summary) {
                        $summary.trigger('milestonechange', milestonePercentage);
                    }

                    // if a corresponding progressbar is bound, trigger its corresponding event with the new data
                    if ($meter) {
                        $meter.progressbar('value', milestonePercentage);
                    }

                    // trigger custom event for other listeners
                    $input.trigger('inputmeasure');
                }, delay);
            });

            // trigger upon init
            $('.js-input-measurer').trigger('input');

            // fire custom post init event
            $this.trigger('jsinputmeasurerinitialized');
        });
    },
    postInit: function () {
        this.setPasswordStrengthTooltipPlacement();
        this.updatePasswordStrengthTooltipContent();
        this.checkPageLoadAutoFocus();
    },
    setPasswordStrengthTooltipPlacement: function () {
        var $triggers = $('#password-strength-tooltip.js-tooltip, #password.js-tooltip');

        $triggers.each(function () {
            var $trigger = $(this),
                oldPlacement,
                oldOffset,
                placement,
                offset,
                hasChanges = false,
                bsTooltipData = $trigger.data('bs.tooltip'),
                isVisible = bsTooltipData && $(bsTooltipData.tip).is(':visible');
            
            if ($trigger.attr('id') === 'password') {
                if (window.matchMedia('(min-width: 768px)').matches) {
                    if ($trigger.data('tooltip-disposed')) {
                        $trigger.removeData('tooltip-disposed').tooltip().trigger('jstooltipinitialized');

                        if ($trigger.data('tooltip-visible')) {
                            $trigger.removeData('tooltip-visible').tooltip('show');
                        }
                    }
                } else {
                    $trigger.data({
                        'tooltip-disposed': true,
                        'tooltip-visible': isVisible || (bsTooltipData && $.contains(document, bsTooltipData.tip))
                    }).tooltip('dispose').attr('aria-describedby', $trigger.data('aria-describedby'));
                    return;
                }
            }

            oldPlacement = $trigger.data('placement');
            oldOffset = $trigger.data('offset');

            if (window.matchMedia('(min-width: 992px)').matches) {
                placement = 'right';
                offset = $trigger.attr('id') === 'password' ? '0, 53px' : '0, 22px';
            } else {
                placement = 'bottom';
                offset = $trigger.attr('id') === 'password' ? '50% + 23px, 10px' : '0, 22px';
            }

            if (oldPlacement !== placement) {
                $trigger.attr('data-placement', placement).data('placement', placement);
                hasChanges = true;
            }

            if (oldOffset !== offset) {
                $trigger.attr('data-offset', offset).data('offset', offset);
                hasChanges = true;
            }

            if (hasChanges) {
                $trigger.tooltip('dispose').tooltip().attr('aria-describedby', $trigger.data('aria-describedby')).trigger('jstooltipinitialized');

                if (isVisible) {
                    $trigger.tooltip('show');
                }
            }
        });
    },
    updatePasswordStrengthTooltipContent: function () {
        var $triggers = $('#password-strength-tooltip.js-tooltip, #password.js-tooltip');

        $triggers.each(function () {
            var $trigger = $(this),
                $tooltipContent,
                strTooltipContent,
                newId,
                $checklist,
                $meterSummary,
                bsTooltipData,
                $tooltip,
                $tooltipInner,
                $checklistOld,
                $meterSummaryOld;

            $tooltipContent = $('#tooltip-clone-target');

            // prevent duplicate IDs
            newId = uuidv4();
            $checklist = $tooltipContent.children().first().clone();
            $meterSummary = $tooltipContent.children().last().clone();

            $checklist.find('[id]').each(function () {
                var $child = $(this);

                $child.attr('id', $child.attr('id') + '-' + newId);
            });
            $meterSummary.find('[id]').each(function () {
                var $child = $(this);

                $child.attr('id', $child.attr('id') + '-' + newId);
            });

            // this is quite complex only because we're animating the progressbar. if that's unnecessary, we can revert to code from previous changeset
            bsTooltipData = $trigger.data('bs.tooltip');
            $tooltip = bsTooltipData ? $(bsTooltipData.tip) : null;
            if ($tooltip && $tooltip.is(':visible')) {
                // if tooltip is currently shown, animate the progressbar and just replace the rest. this is probably better performance-wise than initializing the clones of components inside the tooltip everytime it is shown/recreated
                $tooltipInner = $tooltip.find('.tooltip-inner');
                $checklistOld = $tooltipInner.children().first();
                $meterSummaryOld = $tooltipInner.children().last();
                $checklistOld.replaceWith($checklist);
                $meterSummaryOld.children().first().replaceWith($meterSummary.children().first());
                $meterSummaryOld = $meterSummaryOld.find('.js-milestone-progressbar');
                $meterSummary = $meterSummary.find('.js-milestone-progressbar');
                $meterSummaryOld.attr('aria-valuenow', $meterSummary.attr('aria-valuenow'));
                $meterSummaryOld.find('.ui-progressbar-value').attr({
                    'style': $meterSummary.find('.ui-progressbar-value').attr('style'),
                    'class': $meterSummary.find('.ui-progressbar-value').attr('class')
                });
                strTooltipContent = $tooltipInner.html();
                $trigger.attr('data-original-title', strTooltipContent).removeAttr('title');
            } else {
                // if tooltip isn't currently shown, just update data-original-title
                strTooltipContent = $checklist.get(0).outerHTML + $meterSummary.get(0).outerHTML;
                $trigger.attr('data-original-title', strTooltipContent).removeAttr('title');
            }
        });
    },
    checkPageLoadAutoFocus: function () {
        var $autoFocusElContainer = $('.js-auto-focus-on-load:visible').first(),
            $target = $autoFocusElContainer.data('auto-focus-target-selector');

        // if data-auto-focus-target-selector is set, we only check for it. otherwise, we find the first link, button, or element with tabindex. the container is included in the pool of elements to check
        if ($target) {
            $target = $($target);
            $target.focus();
        } else {
            $autoFocusElContainer.find('a:visible, button:visible, [tabindex]:visible').addBack('a, button, [tabindex]').first().focus();
        }
    },
    selectRadioRecoveryInit: function() {
        if($(".radio-select").length > 0) {
            var $radioSelect = $(".radio-select");
            
            $('.form-select').hide();

            // check if radio button is checked on load 
            if( $radioSelect.is(":checked").length > 0 ) CIAM_NWTEL.selectRadioRecovery($radioSelect.is(":checked") );

            // radio button on change
            $radioSelect.on('change', function() {
                CIAM_NWTEL.selectRadioRecovery(this);
            });
        }
    },
    selectRadioRecovery: function($radioSelect) {
        var $formSelect = $('.form-select'),
            $recoveryInput = $('.recovery-form-input');
       
        $recoveryInput.removeClass('d-none');
        $formSelect.parent().css('height', $formSelect.first().height()+"px");
        $formSelect.hide();
        $formSelect.filter('[data-selected="'+ $radioSelect.value +'"]').slideDown();
    },
    showPasswordInit: function(){
        $('.btn-show-password').on('click', function (e) {
            var state = e.currentTarget.getAttribute('aria-pressed'),
            password = $(this).prevAll().eq(1),
            srPasswordText = $(this).prevAll().eq(0),
            eye_icon = $(this).children();
            e.currentTarget.setAttribute("aria-pressed", state === "false" ? "true" : "false");
            srPasswordText.text(state === "true" ? "Password hidden" : "Password shown");
            if(password.attr("type")== "password"){
                password.attr("type","text");
                eye_icon.removeClass('icon-eye-open').addClass('icon-eye-hide');
            }
            else{
                password.attr("type","password");
                eye_icon.removeClass('icon-eye-hide').addClass('icon-eye-open');
            }
        });
    }
};

$(document).ready(function () {
    CIAM_NWTEL.init();
});

function uuidv4() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}