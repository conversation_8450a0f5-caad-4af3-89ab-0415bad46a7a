import * as React from "react";
import { IBillExplainerContainer } from "../models";
import BillExplainerBody from "./BillExplainerBody/BillExplainerBody";
import BillExplainerHeader from "./BillExplainerComponent/BillExplainerHeader";
import BillExplainerTop from "./BillExplainerTop/BillExplainerTop";


const BillExplainer = (props: IBillExplainerContainer) => {
    const handleLogout = () => {
        window.location.href = props.configLinks ? props.configLinks.LOG_OUT_URL : "/";
    };

    return (
        <div className="liquid-container">
            <BillExplainerHeader
                handleLogout={handleLogout}
                configLinks={props.configLinks}
                isUXPMode={props.isUXPMode} />
            <BillExplainerTop
                pbeCategory={props.pbeCategory}
                service={props.subscriberDetails?.subscriberType}
                serviceID={props.subscriberDetails?.phoneNumber}
                nickName={props.subscriberDetails?.nickName}
                titleKey={props?.titleKey} />
            <BillExplainerBody
                {...props} />
        </div>
    );
};

export default BillExplainer;