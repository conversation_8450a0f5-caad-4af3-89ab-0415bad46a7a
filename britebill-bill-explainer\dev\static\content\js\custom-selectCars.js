
$(function () {
    var CarMakeByYear = [
        {
            "year" : 2018,
            "manufacturers" : {
                "Ford" : ["F-150", "Explorer", "Mustang", "Expedition", "Escape", "EcoSport"],
                "Honda" : ["Accord - Hybrid Touring", "Accord - Touring", "Odyssey - Touring"],
                "Lincoln" : ["MKC", "MKZ", "Continental","MKX", "Navigator"]
            }
        },
        {
            "year" : 2019,
            "manufacturers" : {
                "Lincoln" : ["MKC", "MKZ", "Continental", "Nautilus", "Navigator"]
            }
        }
    ];

    $('#selectCarYear').on('change', function () {
        var carYearSelected = $(this).val();
        var carMakeOptions = "<option selected='selected' value=''>Select car make</option>";
        var carModelOptions = "<option selected='selected' value=''>Select car model</option>";
        if(carYearSelected.length > 0) {
            $('.withoutSelected').removeClass('hide');
            $('.withSelected').addClass('hide');
            let getManufacturers = $.grep(CarMakeByYear, function(car) {
                return car.year == carYearSelected;
            });
            let manufacturers = getManufacturers[0].manufacturers;
            $.each( manufacturers, function( make, models ) {
                carMakeOptions += "<option value='"+ make +"'>" + make + "</option>";
            });
        }
        document.getElementById("selectCarMake").innerHTML = carMakeOptions;
        document.getElementById("selectCarModel").innerHTML = carModelOptions;
    });

    $('#selectCarMake').on('change', function () {
        var carYearSelected = $("#selectCarYear").val();
        var carMakeSelected = $(this).val();

        var carModelOptions = "<option selected='selected' value=''>Select car model</option>";
        if(carMakeSelected.length > 0) {
            $('.withoutSelected').removeClass('hide');
            $('.withSelected').addClass('hide');
            let getModels = $.grep(CarMakeByYear, function(car) {
                return car.year == carYearSelected;
            });
            let models = getModels[0].manufacturers[carMakeSelected];
            $.each( models, function( make, models ) {
                carModelOptions += "<option value='"+ models +"'>" + models + "</option>";
            });
        }
        document.getElementById("selectCarModel").innerHTML = carModelOptions;
    });

    $('#selectCarModel').on('change', function () {
        var carModelSelected = $(this).val();
        if(carModelSelected.length > 0) {
            $('.withoutSelected').addClass('hide');
            $('.withSelected').removeClass('hide');
            $('.withSelected').find('.selectedModel').html(carModelSelected);
        }
    });
});