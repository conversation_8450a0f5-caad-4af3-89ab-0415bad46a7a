/* V1-0 */
/* Last update Feb 21, 2020 */
 
@media print {
    .no-bg-print{background:none !important;}
    .no-print,.no-print *{display:none!important}
    .print-blue{color:#003778!important}
    .print-only-logo{color:#003778!important;display:block !important}
    .print-only-text{color:#003778!important;display:block !important;font-size: 19px;align-self:flex-end}
    .print-flex{display:flex !important}
    .stickyItemWrapper,footer_grey,.subnav-scroll,.banner-crop-img,.footer_grey_wrapper,.connector-active-lob,.banner-crop-img,.backtotop_tablet_mobile_wrapper,ul.connector-areas,.federal-bar,.connector-search-wrap,#connector-search-button,.global-navigation .connector-nav-open-button{display:none!important}
    .size300,.size440{height:auto!important}
    .connector-brand a:before{font-size:40px!important}
    /* .txtWhite{color:#000!important} */
    .bgBlueGradient{background-color:#fff!important}
    .accordion-wrap .collapse-accordion-accessible-toggle{display:block!important}
    .accordion-group .accordionContent{display:block!important}
    .card-collapsible-child{height:400px}
    .card-collapsible-child.expanded div{height:calc(200% + 15px);width:calc(200% + 15px);display:block}
    .card-collapsible-child .card-collapsible-child-content{opacity:1;max-height:250px;overflow:hidden;display:block}
    .card-collapsible-child .card-collapsible-child-accordion{display:none!important}
    .relative.parentImgCont{position:absolute!important}
    .relative.parentImgCont + liquid-container{margin-top:32px!important}
    .relative.parentImgCont + bgWhite{margin-top:32px!important}
    .relative.parentImgCont .position-absolute{top:-4px}
    .graph-container{display:none!important}

    .global-navigation .bellSlimSemibold-Nav{
        font-family:"bellslim_font_heavy",Helvetica,Arial,sans-serif;letter-spacing:-1px
    }
    /* .bgWhite{padding-top:50px} */
    .connector{padding-bottom:30px;background:#fff!important}
     h1{font-size:32px!important}
    .btn-default{color:#003778!important}
    /* .bgGray19{border:1px solid #D4D4D4} */
    .bgGray19{background:white;}
    .print-txt-white {color:#fff!important;}
    .d-flex-print {display:flex!important;}
    .mainContent {max-width: 100%!important;width:100%!important}
    .marginAutoCheck {margin: unset;}
    .mainContent img.img-responsive{height:500px;width:500px}
    .scrollableContainer.scrollable-table-desktop {width:100%;overflow:unset!important;overflow-x:unset!important;position:static!important;}
    .scrollable-table-desktop .table.custom_6ColumnTable th:nth-child(4), .scrollable-table-desktop .table.custom_6ColumnTable tbody td:nth-child(4), .scrollable-table-desktop .table.custom_6ColumnTable th:nth-child(5), .scrollable-table-desktop .table.custom_6ColumnTable tbody td:nth-child(5), .scrollable-table-desktop .table.custom_6ColumnTable th:nth-child(6), .scrollable-table-desktop .table.custom_6ColumnTable tbody td:nth-child(6),
    .scrollable-table-desktop .table.custom_6ColumnTable th:first-child, .scrollable-table-desktop .table.custom_6ColumnTable tbody td:first-child, .scrollable-table-desktop .table.custom_6ColumnTable th:nth-child(2), .scrollable-table-desktop .table.custom_6ColumnTable tbody td:nth-child(2) {
        width: 175px;
    }
    
    .mainContent, .mainNonFloatcontent .mainContent {
        max-width: 100% !important;
        width: 100% !important
    }

    .mainContent, .mainNonFloatcontent .mainContent.printpreview {
        max-width: 100% !important;
        width: 100% !important
    }
    .marginAutoCheck {margin: unset;}
    .mainContent img.img-responsive{height:500px;width:500px}
    .scrollableContainer.scrollable-table-desktop {
        width: calc(100%);
        overflow: unset !important;
        overflow-x: unset !important;
        position: static !important;
    }
    .scrollable-table-desktop .table.custom_6ColumnTable{
        width: calc(100%);
    }

    .scrollable-table-desktop .table.custom_6ColumnTable .custom_2ColumnHead th:first-child {
    width: 379px;

    }

    .scrollableContainerShadow.scrollable-shadow-desktop:after, .scrollableContainerShadow.scrollable-shadow-desktop:before  {
        display: none;
    }
    div>h1{width:100%;padding-right:80px;}
    .global-navigation .connector>.container{background:none !important}

    @page {
        margin: 10mm 0 10mm 0;
    }
    

    tr.bgBlue.txtWhite th, div.accessible-accordion div.static-header{
        background-color: #00549a !important;
        color: #fff !important;
        -webkit-print-color-adjust: exact;
    }

    dt {
        background-color: #f4f4f4 !important;
        -webkit-print-color-adjust: exact;
    }

    .table.custom_6ColumnTable tbody tr, .table.custom_6ColumnTable {
        border-bottom: 1px solid #dee2e6 !important;
    }

    .table.custom_6ColumnTable tbody td {
        border-right: 1px solid #dee2e6 !important;
    }

    .table.custom_6ColumnTable th:last-child, .table.custom_6ColumnTable tbody td:last-child {
        border-right: 1px solid #dee2e6;
    }

    .marginAutoCheck {
        margin: 0px 0px;
    }

    tr.bgBlue.txtWhite th, div.accessible-accordion div.static-header {
        background-color: #00549a !important;
        color: #fff !important;
        -webkit-print-color-adjust: exact;
    }

    .marginAutoCheck {
        margin: 0px 0px;
    }

    /* Multi step */
    div.accessible-accordion span.icon {
        display: none;
    }

    a.txtNoUnderline {
        text-decoration: none !important;
    }

    .accordion-wrap .collapse-accordion-accessible-toggle{display:block!important}


    .iframeContainer .embed-responsive{position:static!important}

    .break-print{
        page-break-after: always;
    }

    /*Article Page*/
    .mainNonFloatcontent .txtContainer, .mainNonFloatcontent .d-flex  {
        justify-content: unset;
        display: block !important;
    }
    
    /*Print Logo for IE and Firefox*/

    .row {
        display: unset;
        flex-wrap: unset;
    }

    .flex-direction-row-reverse {
        flex-direction: unset;
    }


    /* Content Trimmed on IE */
    .container.liquid-container img {
        width: calc(100% - 70px);
        overflow: unset !important;
        overflow-x: unset !important;
        position: static !important;
    }

/*Specific fix form Chrome*/
 img.img-responsive:not(*:root){
     display:unset!important
 }


}

/*Added this media print and screens for mobile of News release listings
    mark up. Do not delete
*/
@media print and (min-width: 320px) and (max-width: 767px) {   
    .news-title-print h1 {
        margin-bottom:20px;
    }
}
/* css rules for ipad landscape */
@media print and (device-width: 768px) and (device-height: 1024px) and (orientation:portrait) {
     h1.txtSize32.txtSize24-xs{
         overflow-wrap: break-word;
         margin-right:20%;
         width: 80%;
     }
}

@media print and (device-width: 1024px) and (device-height: 768px) and (orientation:landscape) {
   h1.txtSize32.txtSize24-xs{
         overflow-wrap: break-word;
         margin-right:20%;
         width: 80%;
     } 
}
/*ipad air 2 fix*/
@media only print and (min-device-width: 768px) and (max-device-width: 1024px) and (-webkit-min-device-pixel-ratio: 2){
    .printShrinkImg{
        max-width: 90%;
    }

}

/*Remove to fix the issue that separate the image from the text*/
/*@media print and (max-width: 991px) {
    .container.liquid-container img {
        width: calc(100% - 100px);
    }
}*/