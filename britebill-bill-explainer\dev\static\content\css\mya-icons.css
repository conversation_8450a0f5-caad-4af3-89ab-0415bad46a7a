@font-face{font-family:'mya-virgin';src:url(../fonts/mya-icon.eot?#iefix) format("embedded-opentype"),url(../fonts/mya-icon.woff) format("woff"),url(../fonts/mya-icon.ttf) format("truetype"),url(../fonts/mya-icon.svg) format("svg");font-weight:400;font-style:normal}

.icon-mya{font-style:normal;speak:none;font-weight:400;font-variant:normal;text-transform:none;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}
.icon-mya, .icon-mya:before {
    font-family: 'mya-virgin';
    position: relative
}

.icon-VM-logo .path1:before {
    content: "\e9d3";
    color: #fff;
}
.icon-VM-logo .path2:before {
    content: "\e9d4";
    color: #c00;
    margin-left: -1.9658203125em;
}
.icon-VM-logo .path3:before {
    content: "\e9d5";
    color: #000;
    margin-left: -1.9658203125em;
}
.icon-VM-logo .path4:before {
    content: "\e9d6";
    color: #000;
    margin-left: -1.9658203125em;
}
.icon-VM-logo .path5:before {
    content: "\e9d7";
    color: #000;
    margin-left: -1.9658203125em;
}
.icon-VM-logo .path6:before {
    content: "\e9d8";
    color: #000;
    margin-left: -1.9658203125em;
}
.icon-Big_check_confirm .path1:before {
    content: "\e9b8";
    color: #2c9e25;
}
.icon-Big_check_confirm .path2:before {
    content: "\e9b9";
    color: #fff;
    margin-left: -1em;
}
.icon-star3:before {
    content: "\e986";
}
.icon-calendar:before {
    content: "\e91c";
}
.icon-Big_info_bg1:before {
    content: "\e9d2";
}
.icon-ios_history_filled:before {
    content: "\e900";
}
.icon-hashtag:before {
    content: "\e901";
}
.icon-BIG_WARNING .path1:before {
    content: "\e902";
    color: #e99e00;
}
.icon-BIG_WARNING .path2:before {
    content: "\e903";
    color: #fff;
    margin-left: -1em;
}
.icon-big_X:before {
    content: "\e904";
}
.icon-edit:before {
  content: "\e92c";
}
.icon-arrow_left:before {
  content: "\e013";
}
.icon-arrow_right:before {
  content: "\e012";
}
.icon-triangle-down:before {
  content: "\e9da";
}

.icon-Neutral .path1:before {
    content: "\e907";
    color: #f4f4f4;
}
.icon-Neutral .path2:before {
    content: "\e908";
    color: #c00;
    margin-left: -1em;
}

.icon-Sad .path1:before {
    content: "\e905";
    color: #f4f4f4;
}
.icon-Sad .path2:before {
    content: "\e906";
    color: #c00;
    margin-left: -1em;
}

.icon-Smile .path1:before {
    content: "\e909";
    color: #f4f4f4;
}
.icon-Smile .path2:before {
    content: "\e90a";
    color: #c00;
    margin-left: -1em;
}

/*Add icon April 13, 2020*/

.icon-Virgin-Logo-Mobile .path1:before {
    content: "\e91e";
    color: #fefefe;
}

.icon-Virgin-Logo-Mobile .path2:before {
    content: "\e91f";
    color: #ec1a37;
    margin-left: -1.9580078125em;
}

.icon-Virgin-Logo-Mobile .path3:before {
    content: "\e920";
    color: #fefefe;
    margin-left: -1.9580078125em;
}

.icon-Virgin-Logo-Mobile .path4:before {
    content: "\e921";
    color: #fefefe;
    margin-left: -1.9580078125em;
}

.icon-Virgin-Logo-Mobile .path5:before {
    content: "\e922";
    color: #120c0e;
    margin-left: -1.9580078125em;
}

.icon-Virgin-Logo-Mobile .path6:before {
    content: "\e923";
    color: #fefefe;
    margin-left: -1.9580078125em;
}

.icon-Virgin-Logo-Tablet .path1:before {
    content: "\e917";
    color: #fefefe;
}

.icon-Virgin-Logo-Tablet .path2:before {
    content: "\e918";
    color: #ec1a37;
    margin-left: -1.9375em;
}

.icon-Virgin-Logo-Tablet .path3:before {
    content: "\e919";
    color: #fefefe;
    margin-left: -1.9375em;
}

.icon-Virgin-Logo-Tablet .path4:before {
    content: "\e91a";
    color: #fefefe;
    margin-left: -1.9375em;
}

.icon-Virgin-Logo-Tablet .path5:before {
    content: "\e91b";
    color: #120c0e;
    margin-left: -1.9375em;
}

.icon-Virgin-Logo-Tablet .path6:before {
    content: "\e91d";
    color: #fefefe;
    margin-left: -1.9375em;
}

