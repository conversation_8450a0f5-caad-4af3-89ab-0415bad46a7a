import * as React from "react";
import { P<PERSON><PERSON>eader, TeleTableView} from "singleban-components";
import { InjectedIntlProps, injectIntl } from "react-intl";
import { IPBE } from "../../models";
import { modalOpenedOmniture } from "../../utils/Utility";
interface Props {
    pbe: IPBE;
}

const PBETelePremiumSportsInstallments= (props: Props & InjectedIntlProps) => {
    const { intl: { formatMessage , formatNumber}, pbe } = props;
    const title =  formatMessage({ id:"PBE_TELE_PREMIUM_SPORTS_INSTALLMENTS_TITLE"},{
      titlekey:pbe.pbeDataBag.leagueName
   });
    const description1 = formatMessage({ id:"PBE_TELE_PREMIUM_SPORTS_INSTALLMENTS_DESCRIPTION_3"});
    const description2 = !pbe.pbeDataBag.isLeagueCancelled? formatMessage({ id:"PBE_TELE_PREMIUM_SPORTS_INSTALLMENTS_DESCRIPTION_1"}) : formatMessage({id :"PBE_TELE_PREMIUM_SPORTS_INSTALLMENTS_DESCRIPTION_2"})
    const imageClassName = "icon-07_ranked-_n1_circle";
    const TableItem = [{
            col1:formatMessage({ id:"PBE_TELE_PREMIUM_SPORTS_INSTALLMENTS_AMOUNT"}),
            col2: formatNumber(pbe?.pbeDataBag?.installmentAmount?? 0.00,{
                currency: "CAD",
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
                style: "currency"
            })
         },
         {
            col1:formatMessage({ id:"PBE_TELE_PREMIUM_SPORTS_INSTALLMENTS_TOTAL_INSTALLMENT"}),
            col2: formatMessage({ id:"PBE_TELE_PREMIUM_SPORTS_INSTALLMENTS_TOTAL_INSTALLMENT_PAID"},{
               currentInstallment:pbe.pbeDataBag.currentInstallmentNumber,
               totalInstallments:pbe.pbeDataBag.totalInstallmentNumberTerm
            })
         }
    ];
    React.useEffect(() => {        
        modalOpenedOmniture(props?.pbe?.triggerPbeOmniture, title, description1);
    }, [title, description1]);
    return (
        <>   
            <PBEHeader descriptionKey={description1}  titleKey={title} iconClassName={imageClassName} descriptionKey2 ={description2} IsStyleApplicable={true}/>
            <TeleTableView TeleTableItem={TableItem} />
        </>
    );
};
export default (injectIntl(PBETelePremiumSportsInstallments));