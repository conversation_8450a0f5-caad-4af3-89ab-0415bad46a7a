
$(document).ready(function () {
    $('.modal').on('shown.bs.modal', function () {
        overwriteTabIndexAndAriaHiddenDifferentHierarchy($(this));
    });


    $('.modal').on('hidden.bs.modal', function () {
        revertTabIndexAndAriaHiddenDifferentHierarchy($(this));
    });
//This function sets the other sections' tabindex to -1 and aria-hidden to true when the passed modal is opened to trap keyboard and screenreader focus.
    function overwriteTabIndexAndAriaHiddenDifferentHierarchy(currEl, tempMoveToProperPos) {
        var parent, index, originClass;
        // if tempMoveToProperPos === true, the modal will temporarily be moved and become an immediate child of the document body
        if (tempMoveToProperPos === true) {
            index = currEl.index();
            if (index > 0) { // has previous sibling, mark it
                originClass = 'origin-' + (new Date()).getTime();
                currEl.prev().addClass(originClass);
                currEl.data('originprev', originClass).appendTo('body');
            } else { // remember parent
                originClass = 'origin-' + (new Date()).getTime();
                currEl.parent().addClass(originClass);
                currEl.data('originparent', originClass).appendTo('body');
            }
        }
        // process the current element's siblings
        currEl.siblings().each(function () {
            var el = $(this), tabindex = el.attr('tabindex'), ariaHidden = el.attr('aria-hidden');
            if (null != tabindex && "" !== tabindex) {
                el.data('oldtabindex', tabindex);
            }
            el.attr('tabindex', -1);
            if (null != ariaHidden && "" !== ariaHidden) {
                el.data('oldariahidden', ariaHidden);
            }
            el.attr('aria-hidden', true);
            el.find('a,area,input,select,textarea,button,iframe,[tabindex],[contentEditable=true]').each(function () {
                el = $(this), tabindex = el.attr('tabindex');
                if (null != tabindex && "" !== tabindex) {
                    el.data('oldtabindex', tabindex);
                }
                el.attr('tabindex', -1);
            });
        });
        // use recursion to process each ancestor until the body root is reached
        parent = currEl.parent();
        if (parent.length > 0 && !parent.is('body')) {
            overwriteTabIndexAndAriaHiddenDifferentHierarchy(currEl.parent());
        }
    }
    /* 
     * This function reverts the other sections' tabindex and aria-hidden to their original values when the passed modal is closed to remove keyboard and screenreader focus trapping.
     */
    function revertTabIndexAndAriaHiddenDifferentHierarchy(currEl) {
        var parent, origParentClass, origParent, origPrevSibClass, origPrevSib;
        // process the current element's siblings
        currEl.siblings().each(function () {
            var el = $(this), tabindex = el.data('oldtabindex'), ariaHidden = el.attr('oldariahidden');
            if (null != tabindex) {
                el.attr('tabindex', tabindex);
                el.removeData('oldtabindex');
            } else {
                el.removeAttr('tabindex');
            }
            if (null != ariaHidden) {
                el.attr('aria-hidden', ariaHidden);
                el.removeData('oldariahidden');
            } else {
                el.removeAttr('aria-hidden');
            }
            el.find('a,area,input,select,textarea,button,iframe,[tabindex],[contentEditable=true]').each(function () {
                el = $(this), tabindex = el.data('oldtabindex');
                if (null != tabindex) {
                    el.attr('tabindex', tabindex);
                    el.removeData('oldtabindex');
                } else {
                    el.removeAttr('tabindex');
                }
            });
        });
        // use recursion to process each ancestor until the body root is reached
        parent = currEl.parent();
        if (parent.length > 0 && !parent.is('body')) {
            revertTabIndexAndAriaHiddenDifferentHierarchy(currEl.parent());
        }
        // this returns the modal to its original position if it was temporarily moved by overwriteTabIndexAndAriaHiddenDifferentHierarchy
        origParentClass = currEl.data('originparent');
        if (origParentClass) {
            origParent = $('.' + origParentClass);
            currEl.prependTo(origParent);
            origParent.removeClass(origParentClass);
            currEl.removeData('originparent');
        } else {
            origPrevSibClass = currEl.data('originprev');
            if (origPrevSibClass) {
                origPrevSib = $('.' + origPrevSibClass);
                currEl.insertAfter(origPrevSib);
                origPrevSib.removeClass(origPrevSibClass);
                currEl.removeData('originprev');
            }
        }
    }


    //radio button box
    $(document).on('change', '.ctrl_radioBtn input[type="radio"]', function () {
        var $this = $(this);
        var $parentDiv = $this.closest('[id^=radio]');
        if ($this.hasClass('selected-rectangle')) {
            $parentDiv.find('.rectangle').addClass('selected-rectangle');
            $this.closest('label').removeClass('selected-rectangle');

        }
        else {
            $parentDiv.find('.rectangle').removeClass('selected-rectangle');
            $this.closest('label').addClass('selected-rectangle');

        }

    });

    //mask/unmask password
    $(document).on('click', '.maskUnMaskPwsBtn', function () {
        if ($(this).text().toLowerCase() == "show") {
            $(this).find('span').html('HIDE');
            $(this).closest('.form-group').find('input').attr("type", "text");
        } else {
            $(this).find('span').html('SHOW');
            $(this).closest('.form-group').find('input').attr("type", "password");
        }

    });

});

