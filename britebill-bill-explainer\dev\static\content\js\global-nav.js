$(document).ready(function () {

    $('body').append('<div class="screen" aria-hidden="true"></div>');

    //Start Federal Bar Events
    $(document).on('click', '.federal-bar-store-locator a', function (e) {
        $('li.connector-area').removeClass('active').find('.menu-flyout').removeClass('menu-flyout-visible').removeClass('menu-flyout-has-been-expanded');
        $('.federal-bar-link-provinces').removeClass('active');
        $('.federal-bar-store-locator-popup').addClass('federal-bar-links');
        $(this).parent().toggleClass('active');
        e.stopPropagation();
    });

    $(document).on('click', '.show-store-locator', function (event) {
        $this = $(this);
        $this.closest('.federal-bar-store-locator-popup').toggleClass('federal-bar-links');
        $this.closest('.federal-bar-store-locator-popup').find('#searchField').focus();
        event.stopPropagation();
    });

    $(document).on('click', '.federal-bar-store-locator-popup', function (e) {
        return false;
    });

    $(document).on('click', 'label.label', function (event) {
        var $this = $(this);
        if ($this.hasClass('active')) {
            $this.find('.store-locator-filter-checkbox').prop('checked', false);
        } else {
            $this.find('.store-locator-filter-checkbox').prop('checked', true);
        }
        $this.toggleClass('active').toggleClass('focused');
        event.stopPropagation();
        return false;
    });

    $(document).on('focusin', '.store-locator-filter-checkbox', function () {
        $(this).closest('.label').addClass('focused');
    });

    $(document).on('focusout', '.store-locator-filter-checkbox', function () {
        $(this).closest('.label').removeClass('focused');
    });

    $(document).on('click', '.connector-cart-button', function () {
        $(this).closest('.shopping-cart-button').toggleClass('active');
        e.stopPropagation();
    });
    $(document).on('click', '.shopping-cart-popup', function () {
        return false;
    });

    $(document).on('click', '.footer-header-current-province', function (e) {
        $('li.connector-area').removeClass('active').find('.menu-flyout').removeClass('menu-flyout-visible').removeClass('menu-flyout-has-been-expanded');
        $('.federal-bar-store-locator').removeClass('active');
        $('.federal-bar-store-locator-popup').addClass('federal-bar-links');
        $('.federal-bar-link-provinces').toggleClass('active');
        e.stopPropagation();
    });

    $(document).on('keyup', '.ui-autocomplete-input', function () {
        var $this = $(this);
        if ($this.val().length > 0) {
            $('button[type="reset"]').addClass('active');
        } else {
            $('button[type="reset"]').removeClass('active');
        }
    });

    $(document).on('click', 'button[type="reset"]', function () {
        $('button[type="reset"]').removeClass('active');
    });

    $(document).on('click', '#connector-search-button', function (e) {
        $this = $(this);
        $this.toggleClass('active');
        $this.closest('.connector').toggleClass('connector-search-active');
        $('.connector-search-wrap').toggleClass('active');
    });
    //End of Federal Bar Events


    $(document).on('click', 'li.connector-area', function (event) {
        //if (window.matchMedia("(max-width: 991.98px)").matches) {
        $('.federal-bar-store-locator').removeClass('active');
        $('.federal-bar-link-provinces').removeClass('active');
        $('.federal-bar-store-locator-popup').addClass('federal-bar-links');
        var $this = $(this);
        if ($this.hasClass('active')) {
            $this.removeClass('active').find('.menu-flyout').removeClass('menu-flyout-visible').removeClass('menu-flyout-has-been-expanded');
            $('.menu-flyout-item-active').removeClass('menu-flyout-item-active');
        } else {
            $('li.connector-area').removeClass('active').find('.menu-flyout').removeClass('menu-flyout-visible').removeClass('menu-flyout-has-been-expanded');
            $('.menu-flyout-item-active').removeClass('menu-flyout-item-active');
            $this.addClass("active");
            $this.find('.menu-flyout').addClass('menu-flyout-visible');
        }
        //}
        event.stopPropagation();
    });
    $(document).on('click', '.sub-nav-root > li', function (event) {
        var $this = $(this);
        if ($this.hasClass('menu-flyout-item-active')) {
            if (window.matchMedia("(max-width: 991.98px)").matches) {
                $('.sub-nav-root > li').removeClass('menu-flyout-item-active');
            }
        } else {
            $('.sub-nav-root > li').removeClass('menu-flyout-item-active');
            $this.addClass("menu-flyout-item-active");
            if (!$this.hasClass('no-sub-nav')) {

                if (!$this.closest('.menu-flyout-visible').hasClass('menu-flyout-has-been-expanded')) {
                    $this.closest('.menu-flyout-visible').addClass('menu-flyout-has-been-expanded');
                    if (window.matchMedia("(min-width: 992px)").matches) {
                        $this.find('.sub-nav-group').width(0).animate({ "width": "180%" }, 225, function () {});
                    }
                }
            }
        }
        event.stopPropagation();
    });
    $(document).on('mouseover', '.sub-nav-root > li', function (event) {
        if (window.matchMedia("(min-width: 992px)").matches) {
            $(this).trigger('click');
        }
        event.stopPropagation();
    });
    $(document).on('click', '.connector-nav-open-button, .screen', function () {
        $this = $('.connector-nav-open-button');
        $('body').toggleClass('connector-active');
        $this.toggleClass('active');
        if ($this.hasClass('active')) {
            $this.attr('title', 'Close Mobile Navigation');
        } else {
            $this.attr('title', 'Open Mobile Navigation');
        }
    });

    $(document).on('click', function (e) {
        if (window.matchMedia("(min-width: 992px)").matches) {
            $('li.connector-area').removeClass('active').find('.menu-flyout').removeClass('menu-flyout-visible').removeClass('menu-flyout-has-been-expanded');
            $('.federal-bar-store-locator').removeClass('active');
            $('.federal-bar-link-provinces').removeClass('active');
            $('.federal-bar-store-locator-popup').addClass('federal-bar-links');
            $('.shopping-cart-button').removeClass('active');
        }
    });


    //Secondary Navigation

    $(document).on('focusin', '.global-navigation .secondary-nav-lob', function () {
        $(this).closest('li').find('.secondary-nav-dropdown').show();
    });

    $(document).on('focusout', '.global-navigation .secondary-nav-dropdown a:last-child', function () {
        $(this).closest('li').find('.secondary-nav-dropdown').hide();
    });

    $(document).on('mousemove', function () {
        $('body').find('.secondary-nav-dropdown').hide();
    });



})