@font-face {
  font-family: 'bell-icon';
  src: url(../fonts/shop-icons.eot?#iefix) format('embedded-opentype'),
    url(../fonts/shop-icons.woff) format('woff'),
    url(../fonts/shop-icons.ttf) format('truetype'),
    url(../fonts/shop-icons.svg) format('svg');
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'bell-icon-outline';
  src: url(../fonts/shop-icons.eot?iw8dli);
  src: url(../fonts/shop-icons.eot?#iefixiw8dli) format('embedded-opentype'),
    url(../fonts/shop-icons.ttf?iw8dli) format('truetype'),
    url(../fonts/shop-icons.woff?iw8dli) format('woff'),
    url(../fonts/shop-icons.svg?iw8dli/shop-icons) format('svg');
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'bell-icon2';
  src: url(../fonts/shop-icons.eot?#iefix) format('embedded-opentype'),
    url(../fonts/shop-icons.woff) format('woff'),
    url(../fonts/shop-icons.ttf) format('truetype'),
    url(../fonts/shop-icons.svg) format('svg');
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'bell-icon3';
  src: url(../fonts/shop-icons.eot?#iefix) format('embedded-opentype'),
    url(../fonts/shop-icons.woff) format('woff'),
    url(../fonts/shop-icons.ttf) format('truetype'),
    url(../fonts/shop-icons.svg) format('svg');
  font-weight: 400;
  font-style: normal;
  font-display: block;
}
.icon,
.icon2,
.icon3 {
  font-family: 'bell-icon';
  font-style: normal;
  speak: none;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-o {
  font-style: normal;
  speak: none;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon:before,
.icon2:before {
  font-family: 'bell-icon';
  position: relative;
  top: 0px;
}
.icon3,
.icon3:before {
  font-family: 'bell-icon3';
  position: static;
}
.icon-o:before {
  font-family: 'bell-icon-outline';
}

/* START Global header and footer icons */
.icon-chevron:before,
.icon-chevron-up:before,
.icon-chevron-right:before,
.icon-chevron-down:before,
.icon-chevron-left:before {
  content: '\e012';
  display: inline-block;
}
.icon-chevron-up:before {
  -webkit-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  transform: rotate(-90deg);
  -webkit-transform-origin: 45% 40%;
  -ms-transform-origin: 45% 40%;
  transform-origin: 45% 40%;
}
.icon-chevron-down:before {
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}
.icon-chevron-left:before {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}
.icon-plus:before {
  content: '\e007';
  /* top: 3px !important; */
}
.icon-bell-logo:before {
  content: '\e600';
}
.icon-cart:before {
  content: '\e617';
  top: 3px !important;
}
.icon-silhouette:before {
  content: '\e616';
  top: 3px !important;
}
.icon-voice-search:before {
  content: '\e91f';
}
.icon-magnifying-glass:before {
  content: '\e615';
  top: 0 !important;
}
.icon-mobile-menu:before {
  content: '\e618';
}
.icon-close1:before {
  content: '\e624';
}
.icon-home:before {
  content: '\e61c';
}
.icon-chevron-bold:before {
  content: '\e61d';
}
.icon-o-handset:before {
  content: '\e610';
}
.icon-o-location:before {
  content: '\e599';
}
.icon-location-pin:before {
  content: '\e620';
}
.icon-search:before {
  content: '\e919';
}
.icon-facebook:before {
  content: '\e619';
}
.icon-twitter:before {
  content: '\e612';
}
.icon-blog-en:before {
  content: '\e90e';
}
.icon-linked-in-logo:before {
  content: '\e929';
}
.icon-youtube:before {
  content: '\e928';
}
.icon-back-to-top:before {
  content: '\e925';
}
/* END Global header and footer icons */
.icon-small_icon_arrow_pill:before {
  content: '\e908';
}
.icon-exclamation-circled:before {
  content: '\e922';
}
.icon-small_icon_expand:before {
  content: '\e90c';
}
.icon-small_icon_collapse:before {
  content: '\e90b';
}
.icon-close:before {
  content: '\eaa2';
}
.icon-small_icon_select_trigger_half:before {
  content: '\e920';
}
.icon-mobile_phone_case:before {
  content: '\e9fc';
}
.icon-screen_protector:before {
  content: '\e9f9';
}
.icon-battery:before {
  content: '\e9fe';
}
.icon-hands_free:before {
  content: '\e9fd';
}
.icon-music:before {
  content: '\e9fb';
}
.icon-star_2:before {
  content: '\e9ff';
}
.icon-smart_accessories:before {
  content: '\e9f8';
}
.icon-sales_and_clearance:before {
  content: '\e9fa';
}
.icon-store_locator:before {
  content: '\e9ee';
}
.icon-calendar:before {
  content: '\e961';
}
.icon-15-01_phonecall_wot:before {
  content: '\e903';
}
.icon-play_hover_multi .path1:before {
  content: '\e97f';
  color: #000;
  opacity: 0.5978;
}
.icon-play_hover_multi .path2:before {
  content: '\e980';
  color: #fff;
  margin-left: -1em;
}
.icon-mobile_phone:before {
  content: '\e9a8';
}
.icon-wi_fi:before {
  content: '\e973';
}
.icon-mobile-tower-bg:before {
  content: '\ea26';
}
.icon-fiber_bl_bg:before {
  content: '\e9c3';
}
.icon-checkmark-circled:before {
  content: '\e921';
}
.icon-check_engine:before {
  content: '\e9e8';
}
.icon-connected_car:before {
  content: '\e9ed';
}
.icon-do_not_disturb:before {
  content: '\e9ec';
}
.icon-dnld-speed-2:before {
  content: '\e9cd';
}
.icon-fuel:before {
  content: '\e9eb';
}
.icon-wifi_bl_wot:before {
  content: '\e97c';
}
.icon-international_calling_code:before {
  content: '\e9e9';
}
.icon-ftp_manager:before {
  content: '\e9ea';
}
.icon-small_icon_call:before {
  content: '\e909';
}
.icon-Request_a_callback:before {
  content: '\e948';
}
.icon-have_us_call_you:before {
  content: '\e9af';
}
.icon-check-light:before {
  content: '\e603';
}
.icon-video_surveillance:before {
  content: '\e9ae';
}
.icon-info:before {
  content: '\e954';
}
.icon-checkmark:before {
  content: '\e943';
}
.icon-moving:before {
  content: '\ea06';
}
.icon-world_bl_wot:before {
  content: '\e9ef';
}
.icon-small_icon_checkmark_outline:before {
  content: '\e90a';
}
.icon-Small_Delivery_bl_ot:before {
  content: '\ea2c';
}
.icon-Small_laptop_bl_ot:before {
  content: '\ea2b';
}
.icon-o-clock:before {
  content: '\e606';
}
.icon-4g_bl_bg:before {
  content: '\e9f3';
}
.icon-lte_bl_bg:before {
  content: '\e9f4';
}
.icon-data_internet_usage_1:before {
  content: '\e993';
}
.icon-video_chat_bl_wot:before {
  content: '\ea0b';
}
.icon-data_internet_usage_2:before {
  content: '\e98b';
}
.icon-data_internet_usage_3:before {
  content: '\e98c';
}
.icon-Big-expand:before {
  content: '\e949';
}
.icon-Big-collapse:before {
  content: '\e94a';
}
.icon-5G_bl_wot:before {
  content: '\ea0d';
}
.icon-camera_lens_bl_wot:before {
  content: '\ea17';
}
.icon-hardware_optimatization_bl_wot:before {
  content: '\ea0e';
}
.icon-star-solid:before {
  content: '\ea28';
}
.icon-cart_bl_bg:before {
  content: '\e9f0';
}
.icon-support:before {
  content: '\e991';
}
.icon-Small_Location:before {
  content: '\e96a';
}
.icon-mobile_phone_bl_ot:before {
  content: '\e9db';
}
.icon-checkmark_bl_ot:before {
  content: '\e9dd';
}
.icon-reset:before {
  content: '\e9dc';
}
.icon-mobile_tower_bl_wot:before {
  content: '\e9d7';
}
.icon-mobile_internet:before {
  content: '\e9f2';
}
.icon-lte:before {
  content: '\e9f1';
}
.icon-paperbill-bg:before {
  content: '\ea00';
}
.icon-graph_descending_2_bl_bg:before {
  content: '\e9de';
}
.icon-07-07_unlimited_wot:before {
  content: '\e902';
}
.icon-no_bill_no_contract:before {
  content: '\e9df';
}
.icon-chevron-up-2:before {
  content: '\e013';
}
.icon-mobile_phone_wc .path1:before {
  content: '\e9d9';
  color: #fff;
}
.icon-mobile_phone_wc .path2:before {
  content: '\e9da';
  color: #00549a;
  margin-left: -1em;
}
.icon-cloud_backup:before {
  content: '\e9d4';
}
.icon-sim_card:before {
  content: '\e9d3';
}
.icon-battery_full:before {
  content: '\e9d0';
}
.icon-user_profile_bl_ot:before {
  content: '\ea2a';
}
.icon-smartphone_care_bl_ot:before {
  content: '\ea29';
}
.icon-power:before {
  content: '\e9d2';
}
.icon-personal_mobile_bl_bg:before {
  content: '\e9f6';
}
.icon-checkmark-no-outline:before {
  content: '\ea02';
}
.icon-Big_info:before {
  content: '\e96b';
}
.icon-fast-speed:before {
  content: '\ea3c';
}
.icon-lte-network:before {
  content: '\ea3b';
}
.icon-mobile-tower-signal:before {
  content: '\ea3a';
}
.icon-crave_tv:before {
  content: '\ea03';
}
.icon-mobile_tower_signal_bl_bg:before {
  content: '\ea05';
}
.icon-forgot_password_bl_bg:before {
  content: '\ea07';
}
.icon-canada_bl_bg:before {
  content: '\ea08';
}
.icon-world_bl_bg:before {
  content: '\ea09';
}
.icon-share_plan_bl_bg:before {
  content: '\ea0a';
}
.icon-bluetooth_bl_wot:before {
  content: '\ea0c';
}
.icon-mobile_HD_bl_wot:before {
  content: '\ea0f';
}
.icon-bring_your_own_mobile_bl_wot:before {
  content: '\ea10';
}
.icon-mobile-app_bl_wot:before {
  content: '\ea11';
}
.icon-smart-pen_bl_wot:before {
  content: '\ea12';
}
.icon-news_or_micro_bl_wot:before {
  content: '\ea13';
}
.icon-touch_id_bl_wot:before {
  content: '\ea14';
}
.icon-ranked_n1_bl_wot:before {
  content: '\ea15';
}
.icon-video_games_bl_wot:before {
  content: '\ea16';
}
.icon-camera_photo_bl_wot:before {
  content: '\ea18';
}
.icon-screen_size_bl_wot:before {
  content: '\ea19';
}
.icon-speaker_bl_wot:before {
  content: '\ea1a';
}
.icon-picture_bl_wot:before {
  content: '\ea1b';
}
.icon-water_resistant_bl_wot:before {
  content: '\ea1c';
}
.icon-gorilla_glass_bl_wot:before {
  content: '\ea1d';
}
.icon-battery_full_bl_wot:before {
  content: '\ea1e';
}
.icon-quad_pixel-technology_bl_wot:before {
  content: '\ea1f';
}
.icon-folder_phone_bl_wot:before {
  content: '\ea20';
}
.icon-5G_bl_bg:before {
  content: '\ea21';
}
.icon-home_bl_bg:before {
  content: '\ea22';
}
.icon-special_event_bl_bg:before {
  content: '\ea23';
}
.icon-double_pods_bl_wot:before {
  content: '\ea24';
}
.icon-download_bl_wot:before {
  content: '\ea25';
}
.icon-promo_bl_wot:before {
  content: '\ea27';
}
.icon-tablet_bl_bg:before {
  content: '\ea2d';
}
.icon-sim_card_bl_bg:before {
  content: '\ea2e';
}
.icon-fast_speed_bl_bg:before {
  content: '\ea2f';
}
.icon-lte_network_bl_bg:before {
  content: '\ea30';
}
.icon-smart_accessories_bl_bg:before {
  content: '\ea34';
}
.icon-business_store_bl_ot:before {
  content: '\ea35';
}
.icon-have_us_call_you_bl_ot:before {
  content: '\ea36';
}
.icon-group_people_bl_wot:before {
  content: '\ea31';
}
.icon-family_bl_wot:before {
  content: '\ea32';
}
.icon-group_bl_wot:before {
  content: '\ea33';
}
.icon-account-management:before {
  content: '\ea37';
}
.icon-get-started:before {
  content: '\ea38';
}
.icon-smartphone-care:before {
  content: '\ea39';
}
.icon-keyboard_bl_wot:before {
  content: '\ea3d';
}
.icon-movies_bl_wot:before {
  content: '\ea3e';
}
.icon-no_hearing_bl_wot:before {
  content: '\ea3f';
}
.icon-move_bl_wot:before {
  content: '\ea40';
}
.icon-eye_round_bl_wot:before {
  content: '\ea41';
}
.icon-smart_touch_guide_bl_wot:before {
  content: '\ea42';
}
.icon-video_play_bl_wot:before {
  content: '\ea43';
}
.icon-personal_bl_wot:before {
  content: '\ea44';
}
.icon-double-mobile-bg:before {
  content: '\ea45';
}
.icon-01-07_group_partipants_bg:before {
  content: '\e911';
}
.icon-email:before {
  content: '\e992';
}
.icon-reset_bl_bg:before {
  content: '\ea46';
}
.icon-checkmark_bl_bg:before {
  content: '\ea47';
}
/* Add Mobility icon Mar 25, 2021 */
.icon-processor_bl_wot:before {
  content: '\ea48';
}
/* Add Mobility icon Mar 30, 2021 */
.icon-calendar-with-pad:before {
  content: '\ea49';
}

.icon-home_wireless-with-pad:before {
  content: '\ea4a';
}

.icon-tv-with-pad:before {
  content: '\ea4b';
}

.icon-mobile_phone-with-pad:before {
  content: '\ea4c';
}

.icon-laptop-with-pad:before {
  content: '\ea4d';
}

.icon-01-13_user_profile_bg:before {
  content: '\e91d';
}
.icon-external-link:before {
  content: '\eae1';
}

/* CUSTOM CSS ICON FROM SHOP */
.checked-list li [class^='icon-'],
.checked-list li [class*=' icon-'] {
  color: #00549a;
  font-size: 16px;
  line-height: 18px;
  margin-right: 10px;
}

.huge-offer-details .offer-price-col .call-icon-block {
  margin-top: 15px;
}

.force-icon-font.icon {
  font-family: 'bell-icon';
}

.force-icon-font.icon2 {
  font-family: 'bell-icon2';
}

.force-icon-font.icon:before,
.force-icon-font.icon2:before {
  position: static;
}

.anchor-text + .anchor-icon {
  margin-left: 10px;
}

.anchor-icon ~ .anchor-text {
  margin-left: 10px;
}

/*For Custom Play button hover effect*/
.iconBlock:hover .icon2.path1:before {
  opacity: 0.6;
}
.iconBlock .icon2.path1:before {
  top: -6px;
  left: 2px;
  opacity: 0.3;
  color: #00549a;
}
.iconBlock .icon2.path1.txtBlack:before {
  color: #000;
}
.iconBlock .icon2.path2:before {
  top: -6px;
}

.iconBlockBlack:hover .icon2.path1:before {
  opacity: 0.6;
}
.iconBlockBlack .icon2.path1:before {
  top: -6px;
  opacity: 0.3;
  color: #000;
}
.iconBlockBlack .icon2.path2:before {
  top: -6px;
  left: -2px;
}

.icon-play_hover_multi .icon2.path1,
.icon-play_hover_multi .icon2.path2 {
  font-size: 82px;
}
/*For Custom Play button hover effect*/

footer .footer-icon .icon-o-chat-bubble,
footer .footer-icon .icon-o.icon-o-handset,
footer .footer-icon .icon-o.icon-o-location {
  top: -8px;
  left: 0;
}

/* CUSTOM CSS ICON FROM SHOP */

/* mobile only */
@media (max-width: 767.98px) {
  .more-ways-to-shop .content-ways-to-shop > li > a .anchor-icon {
    flex-shrink: 0;
    height: 60px;
    width: 60px;
  }

  .more-ways-to-shop .content-ways-to-shop > li > a .anchor-icon:before {
    font-size: 60px;
  }
}

/* tablet and larger */
@media (min-width: 768px) {
  .more-ways-to-shop .content-ways-to-shop > li > a .anchor-icon {
    height: 74px;
    margin-bottom: 15px;
    width: 74px;
  }

  .more-ways-to-shop .content-ways-to-shop > li > a .anchor-icon:before {
    font-size: 68px;
  }
}

@media all and (-ms-high-contrast: active) and (max-width: 480px),
  (-ms-high-contrast: none) and (max-width: 480px) {
  .icon-o-locationNoPad:before {
    /* Enter your style code */
    padding-right: 15px;
  }
}
