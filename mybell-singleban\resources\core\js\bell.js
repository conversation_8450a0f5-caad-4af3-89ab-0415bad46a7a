//v1.1 
//BRF Framework - Updated 2021 Nov 09
//when using eslint/jslint, do not enable the automatic conversion of regular equality operators (== and !=) to type-safe equality operators (=== and !==). if you do so, make sure you review each change

(function () {
    // do not move this inside document ready. this should run as soon as it is parsed
    // check all visible images with the lazyloading class and if it is readily visible in the viewport, load it immediately and remove from lazyloading queue
    $('.lazy:visible').each(function () {
        var element, src;

        // if we are not passing 0 as threshold value to the jQuery.lazy config, we need to pass the correct threshold as third parameter to isInViewport
        if (isInViewport(this)) {
            element = $(this);
            src = element.data('src');

            if (src) {
                element.removeClass(element.data('loading-class'));
                if (this.tagName.toLowerCase() === 'img') {
                    element.attr('src', src);
                } else {
                    element[0].style.backgroundImage = "url('" + src + "')";
                }
                element.removeData('src').removeAttr('data-src');
                // support same height elements
                resetSameHeightElements(element.parents('.same-height'));
                processSameHeightElements(element.parents('.same-height'));
                // trigger custom lazy-loaded event. the timeout is necessary to allow the listeners to be attached first
                setTimeout(function () {
                    element.trigger('lazy-loaded');
                }, 0);
            }
        }
    });
})();

function initBRF() {
    cssScrollCustom();
    reviewScroll();
    equalColumns();
    attachGenericHandlers();
    if (typeof jQuery.fn.Lazy === 'function') {
        $('.lazy').each(function () {
            var el = $(this);

            el.Lazy({
                effect: "fadeIn",
                effectTime: 100,
                threshold: 0,
                afterLoad: function (element) {
                    element.removeClass(element.data('loading-class'));
                    // support same height elements
                    element.parents('.same-height').trigger('resize');
                    // trigger custom lazy-loaded event. the timeout is necessary to allow the listeners to be attached first
                    setTimeout(function () {
                        element.trigger('lazy-loaded');
                    }, 0);
                },
                onError: function (element) {
                    // loading failed but technically it's no longer loading so let's remove the loading class as well
                    element.removeClass(element.data('loading-class'));
                }
            });
        });
    }

    //For Asset Tabs 2 with Accessibility 
    AssetsTabsTwoInitialize.init();
    AssetsTabsTwoInitialize.resized();
    //Intialized Floating side menu
    Sidemenu.Init();
    window.addEventListener('keydown', focusTabControlOnce);
}

// helper function to check if an element is visible in viewport (note that this doesn't check for visibility like visibility:hidden or display:none)
function isInViewport(pElement, pIsEntireElem, pThreshold) {
    var elementBound = pElement.getBoundingClientRect(),
        threshold = pThreshold || 0,
        windowEl = $(window),
        windowHeight = windowEl.height(),
        windowWidth = windowEl.width();

    // if pIsEntireElem === true, perform additional tests to verify that the element is entirely visible
    if (true === pIsEntireElem) {
        if (elementBound.top < 0 || elementBound.left < 0 || elementBound.bottom > windowHeight || elementBound.right > windowWidth) {
            return false;
        }
    }

    // check if element is in viewport area from top or bottom
    if (windowHeight + threshold > elementBound.top && -threshold < elementBound.bottom) {
        // check if element is in viewport area from left or right
        return windowWidth + threshold > elementBound.left && -threshold < elementBound.right;
    }

    return false;
}

function focusTabControlOnce(e) {
    if (e.keyCode === 9) { // Listen to tab events to enable outlines (accessibility improvement)
        document.body.classList.add('is_tabbing');
        window.removeEventListener('keydown', focusTabControlOnce);
        window.addEventListener('mousedown', focusMouseDownControlOnce);
    }
}
function focusMouseDownControlOnce() {
    document.body.classList.remove('is_tabbing');
    window.removeEventListener('mousedown', focusMouseDownControlOnce);
    window.addEventListener('keydown', focusTabControlOnce);
}

function attachGenericHandlers() {
    $('.click-on-space').on('keypress', function (event) {
        var key = event.which || event.keyCode || 0;
        if (key === 32) {
            event.preventDefault();
            event.stopPropagation();
            $(this).trigger("click");
        }
    });

    $('.click-on-enter').on('keypress', function (event) {
        var key = event.which || event.keyCode || 0;
        if (key === 13) {
            event.preventDefault();
            event.stopPropagation();
            $(this).trigger("click");
        }
    });
}


//Add event handler for when page/section loaders are shown
$('body').on('keydown keyup keypress click', fnClickAndKeyNavBlocker);
$('body').on('keydown keyup keypress click', '*', fnClickAndKeyNavBlocker);
function fnClickAndKeyNavBlocker(e) {
    // block click and key events while masked
    if ('click' === e.type || 9 === e.keyCode || 13 === e.keyCode || 32 === e.keyCode) {
        if ($('body').hasClass('masked')) {
            e.stopImmediatePropagation();
            e.preventDefault();
            return;
        }
    }
}

function showLoaderWithMask(loaderSelector) {
    $('body').addClass('masked');
    $('<div class="loaderOverlayBackground transparent"></div>').appendTo('body').fadeIn();
    $(loaderSelector).show();
}

function hideLoaderWithMask(loaderSelector) {
    $('body').removeClass('masked');
    $('.loaderOverlayBackground').remove();
    $(loaderSelector).hide();
}

/* 
 * This function sets the other sections' tabindex to -1 and aria-hidden to true when the passed element is shown to trap keyboard and screenreader focus.
 */
function overwriteTabIndexAndAriaHiddenDifferentHierarchy(currEl, tempMoveToProperPos) {
    var parent, index, originClass;

    // if tempMoveToProperPos === true, the element will temporarily be moved and become an immediate child of the document body
    if (tempMoveToProperPos === true) {
        index = currEl.index();
        if (index > 0) { // has previous sibling, mark it
            originClass = 'origin-' + (new Date()).getTime();
            currEl.prev().addClass(originClass);
            currEl.data('originprev', originClass).appendTo('body');
        } else { // remember parent
            originClass = 'origin-' + (new Date()).getTime();
            currEl.parent().addClass(originClass);
            currEl.data('originparent', originClass).appendTo('body');
        }
    }

    // process the current element's siblings
    currEl.siblings().each(function () {
        var el = $(this), tabindex = el.attr('tabindex'), ariaHidden = el.attr('aria-hidden');

        if (undefined !== tabindex) {
            el.data('oldtabindex', tabindex);
        }
        el.attr('tabindex', -1);

        if (undefined !== ariaHidden && "" !== ariaHidden) {
            el.data('oldariahidden', ariaHidden);
        }
        el.attr('aria-hidden', true);

        el.find('a,area,input,select,textarea,button,iframe,[tabindex],[contentEditable=true]').each(function () {
            el = $(this), tabindex = el.attr('tabindex');

            if (undefined !== tabindex && "" !== tabindex) {
                el.data('oldtabindex', tabindex);
            }
            el.attr('tabindex', -1);
        });
    });

    // use recursion to process each ancestor until the body root is reached
    parent = currEl.parent();
    if (parent.length > 0 && !parent.is('body')) {
        overwriteTabIndexAndAriaHiddenDifferentHierarchy(currEl.parent());
    }
}

/* 
 * This function reverts the other sections' tabindex and aria-hidden to their original values when the passed element is hidden to remove keyboard and screenreader focus trapping.
 */
function revertTabIndexAndAriaHiddenDifferentHierarchy(currEl) {
    var parent, origParentClass, origParent, origPrevSibClass, origPrevSib;

    // process the current element's siblings
    currEl.siblings().each(function () {
        var el = $(this), tabindex = el.data('oldtabindex'), ariaHidden = el.attr('oldariahidden');

        if (undefined !== tabindex) {
            el.attr('tabindex', tabindex);
            el.removeData('oldtabindex');
        } else {
            el.removeAttr('tabindex');
        }

        if (undefined !== ariaHidden) {
            el.attr('aria-hidden', ariaHidden);
            el.removeData('oldariahidden');
        } else {
            el.removeAttr('aria-hidden');
        }

        el.find('a,area,input,select,textarea,button,iframe,[tabindex],[contentEditable=true]').each(function () {
            el = $(this), tabindex = el.data('oldtabindex');

            if (undefined !== tabindex) {
                el.attr('tabindex', tabindex);
                el.removeData('oldtabindex');
            } else {
                el.removeAttr('tabindex');
            }
        });
    });

    // use recursion to process each ancestor until the body root is reached
    parent = currEl.parent();
    if (parent.length > 0 && !parent.is('body')) {
        revertTabIndexAndAriaHiddenDifferentHierarchy(currEl.parent());
    }

    // this returns the element to its original position if it was temporarily moved by overwriteTabIndexAndAriaHiddenDifferentHierarchy
    origParentClass = currEl.data('originparent');
    if (origParentClass) {
        origParent = $('.' + origParentClass);
        currEl.prependTo(origParent);
        origParent.removeClass(origParentClass);
        currEl.removeData('originparent');
    } else {
        origPrevSibClass = currEl.data('originprev');
        if (origPrevSibClass) {
            origPrevSib = $('.' + origPrevSibClass);
            currEl.insertAfter(origPrevSib);
            origPrevSib.removeClass(origPrevSibClass);
            currEl.removeData('originprev');
        }
    }
}


// old code handles modals only if initial window width is > 976px. this doesn't make much sense so we'll look for a class (scrollable-body) and if it is present, we'll process the modal anyway
function setModalMaxHeightInit() {
    var scrollableBodyModal,
        innerFn = function (modalEl) {
            modalEl.on('show.bs.modal', function () {
                $(this).show();
                setModalMaxHeight(this);
            });
            $(window).resize(function () {
                // bootstrap 4 uses 'show' class instead of 'in'
                if ($('.modal.show, .modal.in').length !== 0) {
                    new setModalMaxHeight($('.modal.show, .modal.in'));
                }
            });
        };

    //Vertically center modal window and make content scrollable if modal is too long
    if ($(window).width() > 976) { //for desktop only 
        innerFn($('.modal'));
    } else {
        scrollableBodyModal = $('.modal.scrollable-body');
        if (scrollableBodyModal.length > 0) {
            innerFn(scrollableBodyModal);
        }
    }
}
setModalMaxHeightInit();

function setModalMaxHeight(element) {
    this.$element = $(element);
    this.$content = this.$element.find('.modal-content');
    var borderWidth = this.$content.outerHeight() - this.$content.innerHeight();
    // if the new 'scrollable-body' class is set, we'll use the margins defined in the digital styleguide (60px each for top and bottom for desktop and tablet. nothing is defined for mobile, but based on one mockup, we'll use 45px)
    var dialogMargin = this.$element.hasClass('scrollable-body') ? ($(window).width() < 768 ? 45 : 120) : ($(window).width() < 768 ? 20 : 60);
    var contentHeight = $(window).height() - (dialogMargin + borderWidth);
    var headerHeight = this.$element.find('.modal-header').outerHeight() || 0;
    var subHeight = this.$element.find('.modal-sub-header').outerHeight() + 1 || 0;
    var footerHeight = this.$element.find('.modal-footer').outerHeight() || 0;
    var maxHeight = contentHeight - (headerHeight + subHeight + footerHeight),
        modalBody = this.$element.find('.modal-body'),
        modalBodyVerticalMargin = parseInt(modalBody.css('margin-top'), 10) + parseInt(modalBody.css('margin-bottom'), 10);

    // if the modal-body has any top or bottom margin, we need to deduct it
    this.$element
        .find('.modal-body').css({
            'max-height': maxHeight - modalBodyVerticalMargin,
            'overflow-y': 'auto'
        });
}


//added to give focus to the modal close button when the modal is launched from a link. Tabbing will then start from this button onward so the first tab will always bring you to the NEXT FOCUSABLE ELEMENT INSIDE THE MODAL
function setFocusTimeout(item) {
    var focusTimeout = window.setTimeout(focusOnCloseBtn, 500);
    function focusOnCloseBtn() {
        $($(item).attr('data-target')).find('.modal-header').find('button').focus();
        clearTimeout(focusTimeout);
    }
}

//View more details - expand/collapse
var iconOpen = 'icon icon-collapse-outline-circled',
    iconClose = 'icon icon-exapnd-outline-circled';
$(document).on('show.bs.collapse hide.bs.collapse', '.accordion', function (e) {
    var $target = $(e.target);
    $target.siblings('.accordion-heading').find('span.icon, span.icon2, span.icon3, i').toggleClass(iconOpen + ' ' + iconClose);
    if (e.type === 'show')
        $target.prev('.accordion-heading').find('.accordion-toggle').addClass('active');
    if (e.type === 'hide')
        $(this).find('.accordion-toggle').not($target).removeClass('active');
});
//For search filters background colour change when opened
$(".search-filter .accordion-toggle").click(function () {
    $(this).toggleClass("bgBlueDark");
});

//Focuses modal close button when shown
$('.modal').on('shown.bs.modal', function () {
    $(this).find('.close').focus();
    $('.modal-header-gray a').focus();
});

//Mobile hamburger selectbox tab container selection
$(".custom-selection").change(function () {
    var option_activeTab = $('option:selected', this).attr('data-rel');
    $(".tab-content").hide();
    $("#" + option_activeTab).show();
    $(".tab_heading").removeClass("option_active");
    $(this).addClass("option_active");
});
//Popup tooltips/menus
$('.trigger-popup').on('click touch', function () {
    $('.trigger-popup').next().hide();
    $(this).next().show();
});
$(document).on('click touch', function (event) {
    if (!$(event.target).parents().addBack().is('.trigger-popup')) {

        $('.popup').not(this).hide();
    }
});
$('.federal-bar-store-locator-popup.popup').on('click touch', function (event) {
    event.stopPropagation();
});
//Dropdown menus
$('.trigger-dropdown').on('mouseover focus click touch', function () {
    $('.trigger-dropdown').next().hide();
    $(this).next().show();
});

$(document).on('mouseover focus click touch', function (event) {
    if (!$(event.target).parents().addBack().is('.trigger-dropdown')) {
        $('.connector-drop-down').not(this).hide();
    }
});

$('a[data-enter="true"]').on('keyup', function (e) {
    if (e.which === 13) {
        var enterTarget = $(this).find('.tool-tip');
        var enterTargetHeight = 66 + 18;/* 18 arrow half size */
        if (enterTarget.length > 0) {
            enterTarget.addClass('entered');
            enterTarget.css({
                'top': '-' + enterTargetHeight + 'px'
            });
        }
    }
});

$('.connector-drop-down').on('mouseover focus click touch', function (event) {
    event.stopPropagation();
});

//Prevents url redirect on first tap on mobile devices
if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
    $(".trigger-dropdown").bind('click touch', stopEventOnce);
    $(".trigger-dropdown").on('focusout', function () {
        $(".trigger-dropdown").bind('click touch', stopEventOnce);
    });
}
function stopEventOnce(event) {
    event.preventDefault();
    $(this).unbind('click touch', stopEventOnce);
    return false;
}

//Detects mobile browser and uses pure css scrollbar instead of js
function cssScrollCustom() {
    var isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);
    var isSafari = /Safari/.test(navigator.userAgent) && /Apple Computer/.test(navigator.vendor);
    if (isChrome || isSafari) {
        if ($('div').hasClass('scroll-wrap')) {
            $('.scroll-wrap').removeClass('scrollbar-inner').addClass('scrollAdjust');
            $('.scrollbar-content').removeClass('scrollbar-content vPadding20-left-xs').addClass('vPadding20');

            if ($(window).width() > 999) {
                $('.modal .scrollbar-area').css("padding", "15px 15px 0 0");
                $('.modal-scroll-area').css("padding", "0");
            }
        }
    }
    else if ($(window).width() > 999) {
        if (typeof $('.scrollbar-inner').scrollbar !== 'undefined') {
            $(".scrollbar-inner").scrollbar();

        }
    }
}
//for custom scrollbar visibility for mobile and tablet
function reviewScroll() {
    if ($(window).width() < 999) {
        if (typeof $('.scrollAdjust').scrollbar !== 'undefined') {
            $(".scrollbar-inner").scrollbar();
            $(".modal-body").removeClass("scrollAdjust");
            $(".modal-body").css("padding", "30px");
        }
    }
    //Enables the continue button when user scrolls to the bottom or expands the T&C box
    $('.terms-scroll').scroll(function () {
        if ($(this).scrollTop() === $(this)[0].scrollHeight - $(this).height()) {
            $('.btn.btn-default.disabled').removeClass('disabled');
        }
    });
    $('.accordion-toggle').click(function () {
        $('.btn.btn-default.disabled').removeClass('disabled');
    });
    $(window).on('beforeunload', function () {
        $('.terms-scroll').scrollTop(0);
    });
}

/* START functions for same height elements (supports multiple groups and supports breakpoint-specific processing) */
// this function removes inline styles for height to all .same-height elements
function resetSameHeightElements(pSameHeightElement) {
    if (pSameHeightElement !== undefined) {
        pSameHeightElement.closest('.same-height-wrap').find('.same-height[data-same-height-index=' + pSameHeightElement.data('same-height-index') + ']').css('min-height', '');
    } else {
        $('.same-height-wrap .same-height').css('min-height', '');
    }
}

// this function equalizes the heights of all .same-height elements per .same-height-wrap container. grouping/matching is done based on their same-height-index attribute value
function processSameHeightElements(pSameHeightElement) {
    var mobileMax = 767.98,
        tabletMax = 991.98,
        initializing = true,
        wrapEl,
        specificIndex;

    if (pSameHeightElement !== undefined) {
        initializing = false;
        wrapEl = pSameHeightElement.closest('.same-height-wrap');
        specificIndex = pSameHeightElement.data('same-height-index');
    } else {
        wrapEl = $('.same-height-wrap');
    }

    wrapEl.each(function () {
        var sameHeightWrap = $(this),
            skipBreakpoints = sameHeightWrap.data('same-height-skip'),
            sameHeightElements,
            indexArr;

        // if there are skip breakpoint flags, check if resizing should be skipped (note that resetSameHeightElements still gets fired)
        if (undefined !== skipBreakpoints) {
            skipBreakpoints = $.trim(skipBreakpoints).toLowerCase().split(',');

            if (window.matchMedia('(max-width: ' + mobileMax + 'px)').matches) {
                if (skipBreakpoints.indexOf('m') > -1) {
                    return;
                }
            } else if (window.matchMedia('(max-width: ' + tabletMax + 'px)').matches) {
                if (skipBreakpoints.indexOf('t') > -1) {
                    return;
                }
            } else if (skipBreakpoints.indexOf('d') > -1) {
                return;
            }
        }

        indexArr = [];
        sameHeightElements = sameHeightWrap.find('.same-height' + (undefined === specificIndex ? '' : '[data-same-height-index=' + specificIndex + ']'));

        sameHeightElements.each(function () {
            var sameHeightEl = $(this),
                index = sameHeightEl.data('same-height-index');

            // check same-height-index groups only once per group
            if (indexArr.indexOf(index) === -1) {
                var maxHeight = 0,
                    equalElements = sameHeightElements.filter(function () {
                        var tempEl = $(this),
                            height = tempEl.outerHeight(),
                            ret = tempEl.data('same-height-index') === index;

                        if (ret && height > maxHeight) {
                            maxHeight = height;
                        }

                        return ret;
                    });

                equalElements.css('min-height', maxHeight);
                indexArr.push(index);
            }
        });

        if (initializing) {
            // listen for an event that can be fired which forces the resizing of all elements in the same group as the scope element. 
            // fire this when element height changes(see image lazyload afterLoad event listener above for example).
            sameHeightElements.on('resize', function () {
                var triggerEl = $(this);

                resetSameHeightElements(triggerEl);
                processSameHeightElements(triggerEl);
            });
        }
    });
}
/* END functions for same height elements (supports multiple groups and supports breakpoint-specific processing) */

//All document ready
$(document).ready(function () {
    /* process same height elements on document ready and attach listener to window resize event */
    var resizeTimeoutFn;

    processSameHeightElements();

    // when an accordion is toggled and animation has completed, trigger resize on all of its parents with same-height class
    $('.same-height .collapse').on('shown.bs.collapse hidden.bs.collapse', function () {
        $(this).parents('.same-height').trigger('resize');
    });

    $(window).on('resize', function () {
        clearTimeout(resizeTimeoutFn);
        resizeTimeoutFn = setTimeout(function () {
            // for 'same height elements', we first need to remove any inline height setting before we recompute and set the new heights
            resetSameHeightElements();
            processSameHeightElements();
        }, 200);
    });

    // this intialization adds the tooltip to the body and should be used for static-text tooltips that doesn't need to receive focus (read through aria-describedby)
    $('.tooltip-static').tooltip();

    // START focusable tooltips for screen reader compatibility
    $('[data-toggle="tooltip"][data-tooltipnofocus!=true]:not(.tooltip-static)').on('shown.bs.tooltip', function () {
        $(this).find('.tooltip').attr('tabindex', 0);
    });
    // END focusable tooltips for screen reader compatibility


    // START Tooltip Auto Placement
    function fnTooltipPreAdjustment() {
        $(this).find('.tooltip.top, .tooltip.bottom').css('opacity', 0);
    }

    /* modified code to handle deeper nested elements. note that this supports absolute positioned tooltips only which is the default */
    function fnTooltipAdjustment() {
        var tooltip, parent, height, scrollTop, elementOffset, distance, tailHeight, marginTopDiff;

        tooltip = $(this).find('.tooltip.top');
        if (tooltip.length > 0) {
            // check top overflow
            parent = tooltip.parent();
            // tooltip height doesn't include the arrow and the arrow sometimes overlap the parent, so let's compute the height manually
            height = tooltip.height() + tooltip.find('.tooltip-arrow')[0].getBoundingClientRect().bottom - tooltip[0].getBoundingClientRect().bottom;
            scrollTop = $(window).scrollTop();
            elementOffset = parent.offset().top;
            distance = elementOffset - scrollTop;

            if (height > distance) {
                setTimeout(function () {
                    if (tooltip.hasClass('top')) {
                        marginTopDiff = parseFloat(tooltip.css('margin-top'));
                        tooltip.removeClass('top').addClass('bottom');
                        marginTopDiff -= parseFloat(tooltip.css('margin-top'));
                        tailHeight = tooltip[0].getBoundingClientRect().top - tooltip.find('.tooltip-arrow')[0].getBoundingClientRect().top;
                        tooltip.css('top', tooltip.position().top + height + parent.height() + tailHeight + marginTopDiff + "px");
                    }
                    tooltip.css('opacity', 1);
                }, 0);
            } else {
                tooltip.css('opacity', 1);
            }
            return;
        }

        tooltip = $(this).find('.tooltip.bottom');
        if (tooltip.length > 0) {
            // check bottom overflow
            parent = tooltip.parent();
            // tooltip height doesn't include the arrow and the arrow sometimes overlap the parent, so let's compute the height manually
            height = tooltip.height() + tooltip[0].getBoundingClientRect().top - tooltip.find('.tooltip-arrow')[0].getBoundingClientRect().top;
            distance = $(window).height() - parent[0].getBoundingClientRect().bottom;

            if (height > distance) {
                setTimeout(function () {
                    if (tooltip.hasClass('bottom')) {
                        marginTopDiff = parseFloat(tooltip.css('margin-top'));
                        tooltip.removeClass('bottom').addClass('top');
                        marginTopDiff -= parseFloat(tooltip.css('margin-top'));
                        tailHeight = tooltip.find('.tooltip-arrow')[0].getBoundingClientRect().bottom - tooltip[0].getBoundingClientRect().bottom;
                        tooltip.css('top', tooltip.position().top - height - parent.height() - tailHeight + marginTopDiff + "px");
                    }
                    tooltip.css('opacity', 1);
                }, 0);
            } else {
                tooltip.css('opacity', 1);
            }
            return;
        }

        tooltip.css('opacity', 1);
    }

    $('[data-toggle="tooltip"]').on('inserted.bs.tooltip', function () {
        fnTooltipPreAdjustment.apply(this);
    });

    $('[data-toggle="tooltip"]').on('shown.bs.tooltip', function () {
        fnTooltipAdjustment.apply(this);
    });

    // handle onscroll adjustment
    $(window).scroll(function () {
        var timeout;

        this.clearTimeout(timeout);
        timeout = setTimeout(function () {
            $('.tooltip.in').each(function () {
                var triggerEl = $(this).parent().first();
                fnTooltipPreAdjustment.apply(triggerEl);
                fnTooltipAdjustment.apply(triggerEl);
            });
        }, 100);
    });
    // END Tooltip Auto Placement

    //LazyLoad
    //if (typeof $('img.lazy').lazyload !== 'undefined') {
    //    $("img.lazy").lazyload({ effect: "fadeIn" });
    //}
    // $('.modal').on('shown.bs.modal', function () {
    //   setTimeout(function () {
    //       if ($("img.lazy").length > 0) {
    //           $("img.lazy").lazyload({ effect: "fadeIn" });
    //       }
    //
    //   }, 100);
    // });

    $('.modal').on('shown.bs.modal', function () {
        var modalEl = $(this);
        setTimeout(function () {
            var lazyImages;
            if (typeof jQuery.fn.Lazy === 'function') {
                lazyImages = modalEl.find("img.lazy");
                //if (lazyImages.length > 0) {
                lazyImages.Lazy({ effect: "fadeIn" });
                //}
            }
        }, 100);
    });

    $('.collapse').on('shown.bs.collapse', function () {
        var modalEl = $(this);
        setTimeout(function () {
            var lazyImages;
            if (typeof jQuery.fn.Lazy === 'function') {
                lazyImages = modalEl.find("img.lazy");
                //if (lazyImages.length > 0) {
                lazyImages.Lazy({ effect: "fadeIn" });
                //}
                modalEl.trigger('resize');
            }
        }, 100);
    });

    //Load more search result lists, by default 5 at a time 
    $(".list-wrapper").each(function (index) {
        $(this).find('.search-result-list li:lt(' + $(this).attr('view-child') + ')').show();
    });
    $('.load-more').click(function () {
        var $myWrapper = $(this).closest('.list-wrapper');
        var x = parseInt($myWrapper.attr('view-child'), 10);
        var liSize = $myWrapper.find('.search-result-list li').size();
        x = x + 5 <= liSize ? x + 5 : liSize;
        $myWrapper.attr('view-child', x);
        $myWrapper.find('.search-result-list li:lt(' + x + ')').fadeIn();
        if (x === liSize) {
            $myWrapper.find('.load-more').addClass('disabled');
        }
        if ($('img').hasClass('lazy')) {
            $("img.lazy").lazyload();
        }
    });

    //Start Check to see if the window is top if not then display backtotop button
    function showBacktoTopBtn() {
        var sP = $(window).outerHeight() + $(this).scrollTop();
        if (sP > $(window).outerHeight() + 100 && $(window).outerWidth() < 992) {
            $('.scrollToTop.mobile').fadeIn();
        } else {
            $('.scrollToTop.mobile').fadeOut();
        }
    }

    showBacktoTopBtn();

    $(window).resize(function () {
        showBacktoTopBtn();
    });

    $(window).scroll(function () {
        showBacktoTopBtn();
    });
    //End Check to see if the window is top if not then display backtotop button

    //Click event to scroll to top
    $('.scrollToTop').click(function () {
        $('html, body').animate({ scrollTop: 0 }, 500);
        return false;
    });

    //BRF tabs
    $("ul.tabs li").click(function () {
        $(".tab-content").hide();
        var activeTab = $(this).attr("data-rel");
        $("#" + activeTab).fadeIn();
        $("ul.tabs li").removeClass("active_tabs");
        $(this).addClass("active_tabs");
        $(".tab_heading").removeClass("d_active");
        $(".tab_heading[data-rel^='" + activeTab + "']").addClass("d_active");
    });

    //simple accordion
    $('.accordionButton').click(function () {
        var $group = $(this).parent().closest('.accordion-group');

        // if a group is container is set, let's limit the changes to its contents instead of affecting other accordions
        if ($group.length > 0) {
            $group.find('.accordionButton').removeClass('open');
            $group.find('.accordionContent').slideUp('normal');
        } else {
            $('.accordionButton').removeClass('open');
            $('.accordionContent').slideUp('normal');
        }

        if ($(this).next().is(':hidden') === true) {
            $(this).addClass('open');
            $(this).next().slideDown('normal');
        }
    });
    $('.accordionContent').hide();

    //generic expand/collapse icon toggling based on aria-expanded of the trigger. if using Bootstrap 4 collapse with proper attributes, just use this because toggling of display and aria-expanded is already handled by Bootstrap
    $('.collapse-trigger').click(function (e) {
        var trigger = $(this),
            removeIconClass = trigger.data('icon-expand') || "icon-exapnd-outline-circled",
            addIconClass = trigger.data('icon-collapse') || "icon-collapse-outline-circled",
            temp;

        if (trigger.attr('aria-expanded') === "true") {
            temp = addIconClass;
            addIconClass = removeIconClass;
            removeIconClass = temp;
        }

        trigger.find('span.icon, span.icon2, span.icon3, i').first().removeClass(removeIconClass).addClass(addIconClass);
    });


    // Click functions for Message Boxes
    $('#message-box-warning-toggle').click(function () {
        $('.message-box-warning-script').fadeIn(1000);
        //Get the window height and width
        var winH = $(window).height();
        var winW = $(window).width();

        //if close button is clicked
        $('.close-button').click(function (e) {
            //Cancel the link behavior
            e.preventDefault();
            $('.message-box-warning-script').fadeOut(500);
        });
        setTimeout(function () {
            $('.message-box-warning-script').fadeOut(1000);
        }, 7000);
    });
    $('#message-box-success-toggle').click(function () {
        $('.message-box-success-script').fadeIn(1000);
        //Get the window height and width
        var winH = $(window).height();
        var winW = $(window).width();

        //if close button is clicked
        $('.close-button').click(function (e) {
            //Cancel the link behavior
            e.preventDefault();
            $('.message-box-success-script').fadeOut(500);
        });
        setTimeout(function () {
            $('.message-box-success-script').fadeOut(1000);
        }, 7000);
    });


    //Update information for collapse/expand element
    $('.expand-info-toggle').click(function (e) {
        var $this = $(this);
        var $accrd = $('#accordion-scrollSample');
        setTimeout(function () {
            var accrdHeight = $this.attr('aria-expanded') === "true" ? "auto" : "";
            $accrd.css({
                height: accrdHeight
            });
        }, 510);

    });

    //this for modal focus trapping
    addAriaHiddenToModalSiblings = function ($this) {
        $this.siblings().not('.modal').not('script').each(function (i, v) {
            var $thisSibling = $(v);
            if (!$thisSibling.hasClass('modal')) {
                if (!($thisSibling.attr('aria-hidden') !== undefined)) {
                    $thisSibling.removeAttr('aria-hidden');
                    $thisSibling.attr('aria-hidden', true);
                } else {
                    if ($thisSibling.attr('aria-hidden') === "false") {
                        $thisSibling.attr('aria-hidden', true);
                        $thisSibling.addClass('hasExistingAriaFalse');
                    } else {
                        $thisSibling.addClass('hasExistingAria');
                    }
                }
            }
        });
    };

    addAriaHiddenToModalParentsSiblings = function ($this) {
        var modalPArents = $this.parents().not('body').not('html').length;
        var currentParent = $this.parent();

        for (var i = 0; i <= modalPArents - 1; i++) {
            currentParent.siblings().not('.modal').not('script').each(function (i, v) {
                var $thisSibling = $(v);
                if (!$thisSibling.hasClass('modal')) {
                    if (!($thisSibling.attr('aria-hidden') !== undefined)) {
                        $thisSibling.removeAttr('aria-hidden');
                        $thisSibling.attr('aria-hidden', true);
                    } else {
                        if ($thisSibling.attr('aria-hidden') === "false") {
                            $thisSibling.attr('aria-hidden', true);
                            $thisSibling.addClass('hasExistingAriaFalse');
                        } else {
                            $thisSibling.addClass('hasExistingAria');
                        }
                    }
                }
            });
            currentParent = currentParent.parent();
        }
    };

    removeAriaHiddenToModalSiblings = function ($this) {
        $this.siblings().not('.modal').each(function (i, v) {
            var $thisSibling = $(v);
            if (!$thisSibling.hasClass('modal')) {
                if ($thisSibling.hasClass('hasExistingAria')) {
                    $thisSibling.removeClass('hasExistingAria');
                } else {
                    if ($thisSibling.hasClass('hasExistingAriaFalse')) {
                        $thisSibling.attr('aria-hidden', false);
                        $thisSibling.removeClass('hasExistingAriaFalse');
                    } else {
                        $thisSibling.removeAttr('aria-hidden');
                    }
                }
            }
        });
    };

    removeAriaHiddenToModalParentsSiblings = function ($this) {
        var modalPArents = $this.parents().not('body').not('html').length;
        var currentParent = $this.parent();

        for (var i = 0; i <= modalPArents - 1; i++) {
            currentParent.siblings().not('.modal').each(function (i, v) {
                var $thisSibling = $(v);
                if (!$thisSibling.hasClass('modal')) {
                    if ($thisSibling.hasClass('hasExistingAria')) {
                        $thisSibling.removeClass('hasExistingAria');
                    } else {
                        if ($thisSibling.hasClass('hasExistingAriaFalse')) {
                            $thisSibling.attr('aria-hidden', false);
                            $thisSibling.removeClass('hasExistingAriaFalse');
                        } else {
                            $thisSibling.removeAttr('aria-hidden');
                        }
                    }
                }
            });
            currentParent = currentParent.parent();
        }
    };

    $("[data-toggle='modal']").on('click', function () {
        $this = $($(this)[0].dataset.target);
        $this.removeAttr('aria-hidden');

        //add aria hidden upon open in adroid device to handle the accessibiliy issue
        var ua = navigator.userAgent.toLowerCase();
        var isAndroid = ua.indexOf("android") > -1;

        if (isAndroid) {
            addAriaHiddenToModalSiblings($this);
            addAriaHiddenToModalParentsSiblings($this);
        }
    });


    //remove the aria hidden upon close in adroid device to handle the accessibiliy issue
    $(".modal").on("click.dismiss.bs.modal", '[data-dismiss="modal"]', function (c) {
        var $this = $(this).parents('.modal');
        var ua = navigator.userAgent.toLowerCase();
        var isAndroid = ua.indexOf("android") > -1;

        //$this.attr('aria-hidden', true); commented this out since its not needed, if the modal has display none it will be hidden from SR
        var modalDisplayed = $.grep($('.modal').not($this), function (v, i) {
            return $(v).css('display') === "block";
        });

        if (modalDisplayed.length < 1 && isAndroid) {
            removeAriaHiddenToModalSiblings($this);
            removeAriaHiddenToModalParentsSiblings($this);
        }
    });

    //This script is for the new copy of existing accordion that is accessible. The bell.js is using a specific class that's why the script is not reusable to the duplicate of existing accordion.
    //Accordion expand/collapse
    $('.accordion-accessible-toggle').click(function (e) {
        var $this = $(this),
            $toggleIcon = $this.find('span.icon, span.icon2, span.icon3, i').first(),
            $expandedAttr = $this.attr('aria-expanded') === "true" ? "false" : "true",
            iconExpand = $this.data('icon-expand') || "icon-exapnd-outline-circled",
            iconCollapse = $this.data('icon-collapse') || "icon-collapse-outline-circled",
            newIconClass = $this.attr('aria-expanded') === "true" ? iconExpand : iconCollapse;

        $this.parent().closest(".accordion-wrap").find('.collapse-accordion-accessible-toggle').slideToggle();
        $toggleIcon.removeClass(iconExpand + ' ' + iconCollapse);

        $toggleIcon.addClass(newIconClass);
        $this.attr('aria-expanded', $expandedAttr);
    });
    //---END

    //Applying Aria properties to the accordion with 'accordionbutton and accordion content' design markup
    $('.accordionButton').click(function (e) {
        var $this = $(this),
            $group = $this.parent().closest('.accordion-group'),
            $labelCont = $this.find('a');

        $expandedAttr = $labelCont.attr('aria-expanded') === "true" ? "false" : "true";

        //Reset the accessibility label of the other accordion
        $group.find('.accordionButton a').not($labelCont).attr('aria-expanded', 'false');

        //Update the accessibility label of the accordion
        $labelCont.attr('aria-expanded', $expandedAttr);
    });

    //$('#accordionReadMore').click(function (e) {
    //    //debugger;
    //    var $this = $(this);
    //    var txt = $this.attr('aria-expanded') === "false" ? "Read less" : "Read more";
    //    $this.attr('aria-label', txt);
    //    $this.html(txt);
    //})

    $('.read-more-toggle').click(function (e) {
        var $this = $(this);

        if ($this.attr('aria-expanded') === 'true') {
            $this.attr('aria-expanded', false).attr('aria-label', $this.find('.read-more-show-text').text());
        } else {
            $this.attr('aria-expanded', true).attr('aria-label', $this.find('.read-more-hide-text').text());
        }
    });

    $('.read-more-toggle').keypress(function (e) {
        if (13 === (e.which || e.keyCode || 0)) {
            e.preventDefault();
            e.stopPropagation();
            $(this).trigger('click');
        }
    });

    //Important to make tootltip inner links accesible with keyboard

    $('.tooltip-interactive').each(function () {
        var $this = jQuery(this);
        $this.tooltip({
            container: $this
        });
    });

});

//Accordion expand/collapse 
// add support for exclusive accordion groups. this looks for the ancestor with accordion-wrap class and limits toggling of accordions to those within it only. if the class isn't found, the accordion affects all other accordions in the page
$('.accordion-tog').click(function () {
    var trigger = $(this),
        exclusiveContainer = trigger.parent().closest('.accordion-wrap');

    if (exclusiveContainer.length === 0) {
        exclusiveContainer = $(document);
    }

    exclusiveContainer.find('.collapse-accordion').slideToggle();
    if (exclusiveContainer.find('.accordPanel').attr('aria-expanded') === 'false') {
        trigger.attr('aria-expanded', 'true');
        trigger.find('span.icon, span.icon2, span.icon3, i').first().attr('aria-expanded', 'true');
        exclusiveContainer.find('.accordion-heading>a>i.icon').addClass('icon-collapse-outline-circled').removeClass('icon-exapnd-outline-circled');
    }
    else {
        trigger.attr('aria-expanded', 'false');
        trigger.find('span.icon, span.icon2, span.icon3, i').first().attr('aria-expanded', 'false');
        exclusiveContainer.find('.accordion-heading>a>i.icon').addClass('icon-exapnd-outline-circled').removeClass('icon-collapse-outline-circled');
    }
});
$('.accordion-tog-1').click(function () {
    $('.collapse-accordion-1').slideToggle();
    if ($('.accordPanel-1').attr('aria-expanded') === 'false') {
        $(this).attr('aria-expanded', 'true');
        $('div>i').attr('aria-expanded', 'true');
        $('.accordion-heading>a>div>i.icon').addClass('icon-collapse-outline-circled');
        $('.accordion-heading>a>div>i.icon').removeClass('icon-exapnd-outline-circled');
    }
    else {
        $(this).attr('aria-expanded', 'false');
        $('div>i').attr('aria-expanded', 'false');
        $('.accordion-heading>a>div>i.icon').addClass('icon-exapnd-outline-circled');
        $('.accordion-heading>a>div>i.icon').removeClass('icon-collapse-outline-circled');
    }
});
//Adds space below the footer when the dock is open to allow the full page to scroll in view 
$(".dock-handle").click(function () {
    var height = $(".summaryTable").height();
    $(".add-height-to-div").height(height + 100);
});

//Enables the dock button when a rate plan has been selected
$('.usageDiv.usageActive').on('click touch', function () {
    $('.btn-default-blue').removeClass("disabled");
});

//Creates equal height columns inside of the modal window
$('.modal').on('show.bs.modal', function () {
    setTimeout(function () {
        var arr = $.makeArray();
        if ($(window).width() > 768) {
            $('.eq-height .eq-height-div').each(function () {
                arr.push($(this).outerHeight());
            });
            $('.eq-height .eq-height-div').css('height', Math.max.apply(Math, arr) + 'px');
        }
    }, 300);
});

//Hides the flyout menus when user presses esc button
$('.connector-area').on('mouseover focus click touch', function (e) {
    $('.connector-lob-flyout').css({ "opacity": "1", "display": "block" });
});
$(document).on('keydown', function (e) {
    if (e.keyCode === 27) {
        $('.connector-lob-flyout').css({ "opacity": "0", "display": "none" });
        $('.secondary-nav-dropdown').css({ "display": "none" });
        $('.popup').css({ "display": "none" });
    }
});


//For Asset Tabs 2 with Accessibility 
var AssetsTabsTwoInitialize = {
    init: function () {
        this.resizeId;
        this.breakpoint;

        AssetsTabsTwo.init();
        this.checkBreakpoint();
    },

    resized: function () {
        clearTimeout(this.resizeId);
        this.resizeId = setTimeout(this.doneResizing, 300);

    },

    doneResizing: function () {
        AssetsTabsTwoInitialize.checkBreakpoint();
    },

    getWindowWidth: function () {
        return $(window).width();
    },

    checkBreakpoint: function () {

        if (this.getWindowWidth() <= 767 && this.breakpoint !== 'device') {
            this.breakpoint = 'device';
            AssetsTabsTwo.resetTabsToInitState();
        } else if (this.getWindowWidth() >= 768 && this.breakpoint !== 'desktop') {
            this.breakpoint = 'desktop';
            AssetsTabsTwo.resetTabsToInitState();
        }
    }
};

var AssetsTabsTwo = {
    init: function () {
        this.tablist = $('.tabs[role="tablist"]').each(function () { });

        // Add or substract depending on key pressed
        this.direction = {
            37: -1,
            38: -1,
            39: 1,
            40: 1
        };

        // For easy reference
        this.keys = {
            end: 35,
            home: 36,
            left: 37,
            up: 38,
            right: 39,
            down: 40,
            delete: 46
        };

        // Apply Tab lists function only if it's an existing element 
        if (this.getTabListLength() !== 0) {
            var targetElement = null;
            if (this.getTabListLength() > 1) {


                // Bind Per Multiple Tab Lists
                for (ctr = 0; ctr < this.getTabListLength(); ctr++) {
                    targetElement = this.tablist[ctr];

                    this.activateTabList(targetElement);
                }

            } else {
                targetElement = this.tablist[0];

                // Bind Per Single Tab Lists
                this.activateTabList(targetElement);

            }
        }

        this.selectOnMobile();
    },

    activateTabList: function (targetElement) {
        // Bind listeners
        for (i = 0; i < this.generateTabs(targetElement).length; ++i) {
            this.addListeners(targetElement, i);
        }
    },

    // Get How Many Tablist in the page
    getTabListLength: function () {
        return this.tablist.length;
    },

    // Get How Many Tabs on Every Tab list in the page
    getTabsLength: function (tabs) {
        return tabs.length;
    },

    // Get all tabs
    generateTabs: function (targetElement) {

        var tabs = $(targetElement).find('[role="tab"]');

        return tabs;
    },

    // Get all tab panels
    generatePanels: function (targetElement) {
        var targetElementPanel = $(targetElement).attr('id');

        var panels = $('[data-tablist="' + targetElementPanel + '"]').find('[role="tabpanel"]');

        return panels;
    },

    addListeners: function (targetElement, index) {
        var that = this;
        this.generateTabs(targetElement)[index].addEventListener('click', function () {

            that.clickEventListener(targetElement);
        });
        this.generateTabs(targetElement)[index].addEventListener('keydown', function () {
            that.keydownEventListener(targetElement);
        });
        this.generateTabs(targetElement)[index].addEventListener('keyup', function () {
            that.keyupEventListener(targetElement);
        });

        // Build an array with all tabs (<button>s) in it
        this.generateTabs(targetElement)[index].index = index;
    },

    // When a tab is clicked, activateTab is fired to activate it
    clickEventListener: function (targetElement) {

        var tab = event.target;

        this.activateTab(tab, false, targetElement);
    },

    // Handle keydown on tabs
    keydownEventListener: function (targetElement) {
        var key = event.keyCode;
        var that = this;


        switch (key) {
            case that.keys.end:
                event.preventDefault();
                // Activate last tab
                that.activateTab(that.generateTabs(targetElement)[that.generateTabs(targetElement).length - 1]);
                break;
            case that.keys.home:
                event.preventDefault();
                // Activate first tab
                that.activateTab(that.generateTabs(targetElement)[0]);
                break;

            // Up and down are in keydown
            // because we need to prevent page scroll >:)
            case that.keys.up:
            case that.keys.down:
                that.determineOrientation(event, targetElement);
                break;
        }
    },

    // Handle keyup on tabs
    keyupEventListener: function (targetElement) {
        var key = event.keyCode;
        var that = this;

        switch (key) {
            case that.keys.left:
            case that.keys.right:
                that.determineOrientation(event, targetElement);
                break;
            case that.keys.delete:
                that.determineDeletable(event);
                break;
        }
    },

    // When a tablists aria-orientation is set to vertical,
    // only up and down arrow should function.
    // In all other cases only left and right arrow function.
    determineOrientation: function (event, targetElement) {

        var key = event.keyCode;
        var vertical = $(targetElement).attr('aria-orientation') === 'vertical';
        var proceed = false;

        if (vertical) {
            if (key === this.keys.up || key === this.keys.down) {
                event.preventDefault();
                proceed = true;
            }
        }
        else {
            if (key === this.keys.left || key === this.keys.right) {
                proceed = true;
            }
        }

        if (proceed) {
            this.switchTabOnArrowPress(event, targetElement);
        }
    },

    switchTabOnArrowPress: function (event, targetElement) {
        var pressed = event.keyCode;
        var that = this;

        for (x = 0; x < this.generateTabs(targetElement).length; x++) {
            this.generateTabs(targetElement)[x].addEventListener('focus', function () {

                that.focusEventHandler(targetElement);
            });
        }

        if (this.direction[pressed]) {

            var target = event.target;


            if (target.index !== undefined) {

                if (this.generateTabs(targetElement)[target.index + this.direction[pressed]]) {

                    this.generateTabs(targetElement)[target.index + this.direction[pressed]].focus();
                }
                else if (pressed === this.keys.left || pressed === this.keys.up) {

                    this.focusLastTab(targetElement);
                }
                else if (pressed === this.keys.right || pressed === this.keys.down) {

                    this.focusFirstTab(targetElement);
                }
            }
        }
    },

    // Activates any given tab panel
    activateTab: function (tab, setFocus, targetElement) {

        setFocus = setFocus || true;
        // Deactivate all other 
        this.deactivateTabs(targetElement);

        // Remove tabindex attribute
        tab.removeAttribute('tabindex');

        // Set the tab as selected
        tab.setAttribute('aria-selected', 'true');

        // Get the value of aria-controls (which is an ID)
        var controls = tab.getAttribute('aria-controls');

        // Remove hidden attribute from tab panel to make it visible
        $('#' + controls).removeAttr('hidden');
        $('#' + controls).show();

        tab.classList.add('active_tabs');

        // Set focus when required
        if (setFocus) {
            tab.focus();
        }
    },

    // Deactivate all tabs and tab panels
    deactivateTabs: function (targetElement) {
        var that = this;

        for (t = 0; t < this.generateTabs(targetElement).length; t++) {
            this.generateTabs(targetElement)[t].setAttribute('tabindex', '-1');
            this.generateTabs(targetElement)[t].setAttribute('aria-selected', 'false');
            this.generateTabs(targetElement)[t].classList.remove('active_tabs');
            this.generateTabs(targetElement)[t].removeEventListener('focus', function () {

                that.focusEventHandler(targetElement);
            });
        }

        for (p = 0; p < this.generatePanels(targetElement).length; p++) {
            this.generatePanels(targetElement)[p].setAttribute('hidden', 'hidden');
            this.generatePanels(targetElement)[p].style.display = 'none';
        }
    },

    // Focus First Tab
    focusFirstTab: function (targetElement) {
        this.generateTabs(targetElement)[0].focus();
    },

    // Focus Last Tab
    focusLastTab: function (targetElement) {
        this.generateTabs(targetElement)[this.generateTabs(targetElement).length - 1].focus();
    },

    focusEventHandler: function (targetElement) {
        var target = event.target;

        setTimeout(AssetsTabsTwo.checkTabFocus(targetElement, target), 300);
    },

    // Only activate tab on focus if it still has focus after the delay
    checkTabFocus: function (targetElement, target) {
        focused = document.activeElement;

        if (target === focused) {
            this.activateTab(target, false, targetElement);
        }
    },

    // Change tabpanel display when using select list 
    selectOnMobile: function () {
        $('.custom-selection[role="listbox"]').on('change', function () {
            var target = $(this).find("option:selected").attr('data-rel');
            $('#' + target).removeAttr('hidden');
            $('#' + target).show();
        });
    },

    // Reset all tabs and panels to initial state
    resetTabsToInitState: function () {

        // Check if tab list is more than one
        $('.tabs[role="tablist"]').each(function () {

            // Check if tabs is more than one
            $(this).find('[role="tab"]').each(function () {

                $(this).attr('tabindex', '-1');
                $(this).attr('aria-selected', 'false');
                $(this).removeClass('active_tabs');
            });

            $(this).find('[role="tab"]:first-child').addClass('active_tabs');
            $(this).find('[role="tab"]:first-child').attr('tabindex', '0');
            $(this).find('[role="tab"]:first-child').attr('aria-selected', 'true');
        });

        $('.tab_container').each(function () {

            $(this).find('[role="tabpanel"]').each(function () {
                $(this).attr('hidden', 'hidden');
            });

            $(this).find('[role="tabpanel"]:first-child').removeAttr('hidden');
            $(this).find('[role="tabpanel"]:first-child').show();
        });

        $('.custom-selection').each(function () {
            $(this).find('option').each(function () {
                $(this).prop('selected', function () {
                    return this.defaultSelected;
                });
            });
        });

    }
};


$(window).on('resize', function () {
    AssetsTabsTwoInitialize.resized();
});



//for equal height columns
(function (factory) {
    //'use strict';
    //if (typeof define === 'function' && define.amd) {
    //    define(['jquery'], factory);
    //} else if (typeof exports !== 'undefined') {
    //    module.exports = factory(require('jquery'));
    //} else {
    factory(jQuery);
    //}

}(function ($) {
    'use strict';
    var ColEqualizer = window.ColEqualizer || {};
    ColEqualizer = (function () {
        function ColEqualizer(element) {
            var _ = this;
            _.el = element;
            _.colHeight(_.el);
            _.winLoad();
        }
        return ColEqualizer;
    }());

    ColEqualizer.prototype.colHeight = function (element) {
        var _ = this;
        $(element).each(function (index, el) {
            _.colReset(el);
            var tallest = 0;
            $('[class*=col-]', el).each(function (i, e) {
                var testHeight = $(e).height();
                if (testHeight > tallest) {
                    tallest = testHeight;
                }
            });
            if (tallest > 0) {
                $('[class*=col-]', el).height(tallest);
            }
        });

    };
    ColEqualizer.prototype.colReset = function (el) {
        $('[class*=col-]', el).height('auto');
    };

    ColEqualizer.prototype.resizeWindow = function () {
        var _ = this;
        var viewWidth = window.outerWidth;
        if (viewWidth <= 0) {
            viewWidth = $(window).width();
        }
        // If set, minWidth shows/hides the nav based on the size of the browser
        // If minWidth not set, nav will always show
        if (100 <= viewWidth) {
            _.colHeight(_.el);
        } else {
            _.colReset(_.el);
        }
    };

    ColEqualizer.prototype.winLoad = function () {
        var _ = this;
        var $win = $(window);
        $win.load(function () {
            $win.on('resize', function () {
                _.resizeWindow();
            });
            _.resizeWindow();
        });
    };

    $.fn.colequalizer = function () {
        var _ = this;
        return _.each(function (index, element) {
            element.navinator = new ColEqualizer(element);
        });
    };
}));
function equalColumns() {
    try {
        $('.col-eq').colequalizer();
    } catch (err) {
        // ignore error
    }
}

//function to adjust child element width to parent for mobile and above
//a is parentClass name, b is child class name, c is to set px width for mobile view port
//e.g.("div.float_socialIcon_parent", ".social_icons_wrapper","200px");
function parent_width_matcher(_parentClass, _childClass, _mobileWidth) {
    if (window.innerWidth >= 768) {
        try {
            if ($(_parentClass).length) {
                var par_width = $(_parentClass).width();
                $(_childClass).width(par_width);
            }
        } catch (e) {
            // ignore error
        }
    } else {
        $(_childClass).width(_mobileWidth);
    }
}

//function to toggle opacity of element
//a is element class name and b is bool (true/false)
//eg. ('div.container',true') 
function showHideElementToggle(_el, _showIcons) {
    _showIcons ? $(_el).css('opacity', '0') :
        $(_el).css('opacity', '1');
}

// Element position switcher for Tablet and Desktop and hide element when near footer
//for mobile the positon switch to relative
// eg. ('div.float_socialIcon_parent .social_icons_wrapper' ,150) where social icons will be sticky on tablet and above and relative on mobile
// and 150 is the value of the pixels from bottom to hide it
function StickyFloatSwitcher(a, _b) {
    if ($(a).length) {
        var $el = $(a);
        var isPositionFixed = $el.css('position') === 'fixed';
        if (window.innerWidth >= 768) {
            if ($(this).scrollTop() > 250 && !isPositionFixed) {
                $el.css({ 'position': 'fixed', 'top': '10px' });
            }
            if ($(this).scrollTop() < 250 && isPositionFixed) {
                $el.css({ 'position': 'static', 'top': '10px' });
            }
            if ($(window).scrollTop() + $(window).height() >= $(document).height() - _b) {
                showHideElementToggle($el, true);
            }
            else {
                showHideElementToggle($el, false);
            }
        }
    }
}
//Right side Floating side menu container 
var Sidemenu = {
    IsStickyMenu: false,
    Stickied: true,
    Mobile: false,
    //mainCont can be <main> height if the component need to be touching top
    mainCont: ".mainNonFloatcontent",
    //parent container class for getting width and set to stickyItemWrapper
    parentCont: "div.WideItemContainer",
    //sticky element
    stickyElem: ".stickySide_awesome",
    stickyItemWrapper: ".stickyItemWrapper",
    sideItemContainer: ".mainNonFloatcontent",
    floatposClassName: 'pos-fixed-top-imp',
    Init: function () {
        //check if the page contains the stickySide only then load the sticky behaviour
        if ($(Sidemenu.stickyElem).length > 0) {
            Sidemenu.IsStickyMenu = true;
            if (window.innerWidth < 768) {
                Sidemenu.Mobile = true;
                if ($(Sidemenu.stickyItemWrapper).hasClass(Sidemenu.floatposClassName)) {
                    $(Sidemenu.stickyItemWrapper).removeClass(Sidemenu.floatposClassName);
                }
            } else {
                Sidemenu.Mobile = false;
                Sidemenu.widthmatch(Sidemenu.parentCont, Sidemenu.stickyItemWrapper, "auto");
            }
        }
    },
    widthmatch: function (_parentClass, _childClass, _mobileWidth) {
        if (window.innerWidth >= 768) {
            try {
                if ($(_parentClass).length) {
                    var par_width = $(_parentClass).width();
                    $(_childClass).width(par_width);
                }
            } catch (e) {
                // ignore error
            }
        } else {
            $(_childClass).width(_mobileWidth);
        }
    },
    StickyScroll: function (e) {
        if (Sidemenu.Mobile || !Sidemenu.IsStickyMenu) {
            return;
        }
        var whereAmI = $(window).scrollTop();
        var buffer = 55;

        var $mainElem = $(Sidemenu.mainCont);
        //whole main container position
        var topCorner = $mainElem.position();
        //whole main container height
        var ccheight = $mainElem.height();

        var absoluteBottom = ccheight + topCorner.top; //this gives the absolute bottom corner

        //content panel height
        var mainHeight = $(Sidemenu.sideItemContainer).height();

        var lcheight = $(Sidemenu.stickyElem).height();
        var lcBottom = whereAmI + lcheight;
        var tooFar = absoluteBottom - (lcBottom + buffer);

        if (mainHeight > lcheight) {
            if (whereAmI > topCorner.top && tooFar > 0) {// && (limit < ccheight)) {
                if (!Sidemenu.Stickied) {
                    $(Sidemenu.stickyItemWrapper).addClass(Sidemenu.floatposClassName);
                    $(Sidemenu.stickyElem).css("top", 0);
                    Sidemenu.Stickied = true;
                }
            } else if (tooFar < 0) {
                $(Sidemenu.stickyElem).css("top", tooFar);
                Sidemenu.Stickied = false;
            } else {//if (whereAmI < topCorner.top){//snap back to where we should be
                if (Sidemenu.Stickied) {
                    $(Sidemenu.stickyItemWrapper).removeClass(Sidemenu.floatposClassName);
                    $(Sidemenu.stickyElem).css("top", 0);
                    Sidemenu.Stickied = false;
                }
            }
        }
    }
};

$(window).scroll(function (e) {
    if (Sidemenu.IsStickyMenu) {
        Sidemenu.StickyScroll(e);
    }
});

$(window).resize(function () {
    Sidemenu.Init();
});


//Features Boxes Checkbox active state
$(document).on('click', '.checkbox-selection', function () {
    if ($(this).prop("checked")) {
        $(this).parent().parent().parent().parent().removeClass("borderGrayLight6").addClass("borderBlue border-2px");
    } else {
        $(this).parent().parent().parent().parent().removeClass("borderBlue border-2px").addClass("borderGrayLight6 ");
    }
});

//Sticky header on scroll function
if (document.getElementById("fixedHeader")) {
    window.onscroll = function () { fixedHeader(); };
    var header = document.getElementById("fixedHeader");
    var fixed = header.offsetTop;
}
function fixedHeader() {
    if (window.pageYOffset > fixed) {
        header.classList.add("fixed-active-lob");
        $('body').addClass("fixed-nav");
    } else {
        header.classList.remove("fixed-active-lob");
        $('body').removeClass("fixed-nav");
    }
}

//Bell find a store filter options flyout
$('.find-a-store-filter-js').on('click touch', function () {
    $('.find-a-store-filter').removeClass('hide');
    $('.bell-stores-options').addClass('hide');
    $('.federal-bar-store-locator-popup').addClass('filter-options');
});
$(document).on('click touch', function () {
    $('.find-a-store-filter').addClass('hide');
    $('.bell-stores-options').removeClass('hide');
    $('.federal-bar-store-locator-popup').removeClass('filter-options');
});

//Trap focus on mobile global nav 
$(document).on('click', '.connector-nav-open-button, .screen', function () {
    $("main").find('*').not('[tabindex="-1"]').attr("tabindex", "-1").addClass('tabneg');
    $("footer").find('*').not('[tabindex="-1"]').attr("tabindex", "-1").addClass('tabneg');
    $(".connector-active-lob").find('*').not('[tabindex="-1"]').attr("tabindex", "-1").addClass('tabneg');
    //Adding aria-hidden=true and ariahiddentrue class to the elements that should not be read by the screenreader when the navigation menu is open
    $('body .tabneg').not('[aria-hidden="true"]').attr("aria-hidden", "true").addClass('ariahiddentrue');
});
$(document).on('click', 'body.connector-active .connector-nav-open-button, .screen', function () {
    //Remove aria-hidden=true and ariahiddentrue class to the elements that should not be read by the screenreader when the navigation menu is open
    $('body .ariahiddentrue').removeAttr('aria-hidden').removeClass('ariahiddentrue');
    $('body .tabneg').removeAttr('tabindex').removeClass('tabneg');
});


$(document).ready(function () {
    initBRF();
    $(document).on('hidden.bs.modal', function (event) {
        if ($('.modal:visible').length) {
            $('body').addClass('modal-open');
        }
    });
});

/*
Start Accessibility fix for modal (focus trapper)
Reference: https://www.w3.org/TR/wai-aria-practices-1.1/#dialog_modal 
If focus is on the last tabbable element inside the dialog, moves focus to the first tabbable element inside the dialog.
If focus is on the first tabbable element inside the dialog, moves focus to the last tabbable element inside the dialog.
*/
$('.modal').on('keydown', function (e) {
    let tabPressed = e.key === 'Tab' || e.keyCode === 9;

    // proceed only to the remaining script if tab key is pressed
    if (!tabPressed) {
        return;
    }

    // treat radio group as one tabbable element to get the correct first focusable element and last focusable element inside the modal
    if ($(':focus').is(':radio')) {
        let name = $(':focus').attr('name');
        $(':focus').removeClass('not-tabbable');
        $(this).find(':radio[name="' + name + '"]:not(:focus)').addClass('not-tabbable');
    }

    // get all focusable elements inside the modal then get first and last focusable element.
    let focusableElements = $(this).find('a[href], button, input, textarea, select, details, [tabindex]').filter(':not([tabindex="-1"])').filter(':not([disabled])').filter(':visible').filter(':not(.not-tabbable)');
    let firstFocusable = focusableElements.first();
    let lastFocusable = focusableElements.last();

    // disable tab if no focusable element on modal
    if (focusableElements.length == 0) {
        e.preventDefault();
    }

    // if tab + shift key is pressed, check if focus is in the first focusable element then reroute the next focus to the last focusable element
    if (e.shiftKey) {
        if (firstFocusable.is(':focus')) {
            lastFocusable.focus();
            e.preventDefault();
        }
    }
    // if tab key is pressed, check if focus is in the last focusable element then reroute the next focus to the first focusable element
    else {
        if (lastFocusable.is(':focus')) {
            firstFocusable.focus();
            e.preventDefault();
        }
    }
});
// End accessibility fix for modal (focus trapper)