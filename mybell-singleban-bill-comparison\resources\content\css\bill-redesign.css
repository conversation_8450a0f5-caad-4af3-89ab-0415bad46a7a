﻿/*Start Header and <PERSON>er Override*/
@media (min-width: 320px) and (max-width: 1229.98px) {
    .bill-redesign .container,
    .gh-complete .container,
    .gf-complete .container,
    .standard-step-flow-header .container {
        padding-left: 15px;
        padding-right: 15px;
        margin: 0;
        max-width: none;
        width: 100%;
    }
}

@media (min-width: 1230px) {
    .bill-redesign .container,
    .gh-complete .container,
    .gf-complete .container,
    .standard-step-flow-header .container {
        padding-left: 0;
        padding-right: 0;
        margin: 0 auto;
        max-width: 1200px;
        width: 100%;
    }
}

/*from searchui.css - for dropdown chevron icons*/
.gh-complete .gn-mybell a.trigger-dropdown .icon-global-nav.icon-chevron.chevron-down {
    top: 0px !important;
}

/*End Header and Footer Override*/

/* START - standard-step-flow-header */
.standard-step-flow-header .btn.btn-primary-white {
    padding: 7px 28px;
}

/*Helper class - Start*/
body.bill-redesign-accss.is_tabbing *:focus {
    outline: 0 !important;
    box-shadow: none !important;
}

.bill-redesign .big-price {
    font-size: 40px;
    line-height: 46px;
    color: #00549A;
}

    .bill-redesign .big-price span {
        font-size: 18px;
        letter-spacing: -0.45px;
        line-height: 22px;
        position: relative;
        vertical-align: top;
        top: 7px;
    }

        .bill-redesign .big-price span:first-of-type {
            margin-right: 2px;
        }

        .bill-redesign .big-price span:last-of-type {
            margin-left: 4px;
        }

.bill-redesign .big-price-header {
    font-size: 40px;
    line-height: 46px;
    color: #00549A;
}

    .bill-redesign .big-price-header span {
        font-size: 18px;
        letter-spacing: -0.45px;
        line-height: 22px;
        position: relative;
        vertical-align: top;
        top: 7px;
    }

        .bill-redesign .big-price-header span:first-of-type {
            margin-right: 2px;
        }

        .bill-redesign .big-price-header span:last-of-type {
            margin-left: 4px;
        }

.bill-redesign .active_tabs::after {
    display: none;
}

.bgVeryLightBlue {
    background: #EDF3F8;
}
.txtCoolBlue {
    color: #C2CEDF;
}

.dimension-20 {
    width: 20px;
    height: 20px;
}

.dimension-27 {
    width: 27px;
    height: 27px;
}

.dimension-60 {
    width: 60px;
    height: 60px;
}

.txtSize7 {
    font-size: 7px;
}

.txtSize25 {
    font-size: 25px;
}

.txtSize27 {
    font-size: 27px;
}

.txtSize55 {
    font-size: 55px;
}

.txtSize90 {
    font-size: 90px;
}

.line-height-20 {
    line-height: 20px;
}

.line-height-22 {
    line-height: 22px;
}

.width-45 {
    width: 45px;
}

.width-60 {
    width: 60px;
}

.width-67 {
    width: 67px;
}

.width-90 {
    width: 90px;
}

.width-93 {
    width: 93px;
}

.width-113 {
    width: 113px;
}

.width-150 {
    width: 150px;
}

.width-190 {
    width: 190px;
}

.width-195 {
    width: 195px;
}

.height-31 {
    height: 31px;
}

.left-0 {
    left: 0;
}

.right-0 {
    right: 0;
}

.pad-h-0-forced {
    padding-left: 0px !important;
    padding-right: 0px !important;

}

.pad-h-6 {
    padding-left: 6px;
    padding-right: 6px;
}

.pad-v-8 {
    padding-top: 8px;
    padding-bottom: 8px;
}

.pad-l-0 {
    padding-left: 0px !important;
}

.pad-t-3 {
    padding-top: 3px;
}

.pad-b-3 {
    padding-bottom: 3px;
}

.pad-b-12 {
    padding-bottom: 12px;
}

.pad-l-13 {
    padding-left: 13px;
}

.pad-l-75 {
    padding-left: 75px;
}

.pad-r-13 {
    padding-right: 13px;
}

.pad-r-77 {
    padding-right: 77px;
}

.margin-top-neg-5 {
    margin-top: -5px;
}

.margin-top-neg-10 {
    margin-top: -10px;
}

.margin-t-11 {
    margin-top: 11px;
}
.margin-neg-top-104 {
    margin-top: -104px;
}

.margin-neg-top-124 {
    margin-top: -124px;
}

.margin-top-neg-145 {
    margin-top: -145px;
}
.margin-l-60 {
    margin-left: 60px;
}
.max-width-135 {
    max-width: 135px;
}

.max-width-290 {
    max-width: 290px;
}

.min-width-301 {
    min-width: 301px;
}

.max-width-301 {
    max-width: 301px;
}

.max-width-353 {
    max-width: 353px;
}

.max-width-95-pct {
    max-width: 95%;
}

.min-height-65 {
    min-height: 65px;
}

.column-spacer-15 {
    margin-left: -7.5px;
    margin-right: -7.5px;
}

    .column-spacer-15 > div {
        padding-left: 7.5px;
        padding-right: 7.5px;
    }

.txtPreWrap {
    white-space: pre-wrap;
}

.white-space-normal {
    white-space: normal;
}

.borderRadius10-t-l {
    border-top-left-radius: 10px;
}

.borderRadius10-t-r {
    border-top-right-radius: 10px;
}

.borderRadius10-b-l {
    border-bottom-left-radius: 10px;
}

.borderRadius10-b-r {
    border-bottom-right-radius: 10px;
}

.borderRadius10-left {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}

.borderRadius10-right {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
}

.borderRadiusAll4 {
    border-radius: 4px;
}

.borderRadiusAll5 {
    border-radius: 5px;
}

.borderRadius5-top {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

.border-white-right {
    border-right: 1px solid #ffffff;
}

.border-r-blue {
    border-right: 1px solid #00549a;
}

.border-1-l-GrayLight9 {
    border-left: 1px solid #979797;
}

.border-t-0 {
    border-top: 0
}

.top-gradient::after {
    background: linear-gradient(rgba(0,0,0,0.12) 0,rgba(0,0,0,0) 100%);
    top: 0;
    content: '';
    height: 100px;
    left: 0;
    pointer-events: none;
    position: absolute;
    width: 100%;
}

.bill-redesign .border-lightGray-bottom-2px {
    border-bottom: 2px solid #D4D4D4;
}

.bill-redesign .border-black-bottom-2px {
    border-bottom: 2px solid #0E0E0E;
}

.box-gradient {
    /*height: 200px;
    background-color: gray;*/ /* For browsers that do not support gradients */
    background-image: radial-gradient(circle, #F2F2F2 0%, #FFFFFF 100%, #FFFFFF 100%);
}

.txtWithIconUnderlineOnInteraction,
.txtWithIconUnderlineOnInteraction:hover,
.txtWithIconUnderlineOnInteraction:focus {
    text-decoration: none !important;
}

    .txtWithIconUnderlineOnInteraction:hover .interaction-decor-target,
    .txtWithIconUnderlineOnInteraction:focus .interaction-decor-target {
        text-decoration: underline !important;
    }

/*Helper class - End*/

.bill-redesign .header-tab-control a {
    align-items: center;
    display: inline-flex;
}

.bill-redesign .form-control-border {
    border-color: #949596;
}

.bill-redesign .form-control-select {
    padding-left: 10px;
}
    .bill-redesign .form-control-select + span {
        right: 15px;
        top: 5px;
    }
/*Hierarchy Design in Accordion*/
.vertical-bullet {
    margin-left:5px;
    border-left: 1px solid lightGray;
    height: 30px;

}
.horizontal-bullet {
    border-bottom: 1px solid lightGray;
    width: 10px;
    margin-bottom: 20px;
}
.height-12 {
    height: 12px;
}

.card-dimension {
    height: 20px;
}
/*Tooltip Billing Custom*/

.payment-tooltip .tooltip {
    max-width: 290px;
}

.payment-tooltip .tooltip-inner {
    max-width: 290px;
    padding: 25px;
}

.history-disabled-tooltip .tooltip {
    max-width: 315px;
}

.history-disabled-tooltip .tooltip-inner {
    max-width: 315px;
    padding: 15px;
}


/*Bar Graph Tooltip*/
.bar-tooltip .tooltip {
    max-width: 118px;
}

.bar-tooltip .tooltip-inner {
    max-width: 118px;
    padding: 20px;
}

.bar-tooltip .tooltip.bs-tooltip-top .arrow, .tooltip.bs-tooltip-auto[x-placement^=top] .arrow {
    margin-left: -40px;
}

/*Hierarchy Design in Accordion*/
.bill-redesign .modal.modal-tooltip .modal-body {
    padding: 0px 30px 30px;
    margin-bottom: 0px;
    margin-top: 0px;
}

.bill-history .modal.modal-tooltip .modal-body {
    padding: 0px 20px 15px;
    margin-bottom: 0px;
    margin-top: 0px;
}
.bill-redesign .close:not(:disabled):not(.disabled):focus,
.bill-redesign .close:not(:disabled):not(.disabled):hover {
    opacity: 1;
}

/*Billing Grid Header - Start*/
.billing-treegrid-header-bg {
    height: 339px;
}

.billing-treegrid-header {
    margin-top: -339px;
}

.billing-treegrid-header-d-print-accordion,
.billing-treegrid-header-d-download-accordion {
    left: -90px;
    z-index: 2;
    transition-duration: 0s;
}

.billing-treegrid-header-d-download-accordion {
    left: -74px;
}

    .billing-treegrid-header-d-print-accordion button:hover,
    .billing-treegrid-header-d-download-accordion button:hover,
    .billing-treegrid-header-d-print-accordion button:focus,
    .billing-treegrid-header-d-download-accordion button:focus,
    .billing-services-print-modal .billing-services-print-btns button:hover,
    .billing-services-download-modal .billing-services-download-btns button:hover,
    .billing-services-print-modal .billing-services-print-btns button:focus,
    .billing-services-download-modal .billing-services-download-btns button:focus {
        background-color: #F4F4F4;
    }

    .billing-treegrid-header-d-print-accordion .arrow,
    .billing-treegrid-header-d-download-accordion .arrow {
        position: relative;
        /*margin-top: 5px;*/
        background: #fff;
    }

        .billing-treegrid-header-d-print-accordion .arrow:after,
        .billing-treegrid-header-d-download-accordion .arrow:after {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-color: transparent;
            border-top: 0;
            top: -10px;
            left: 50%;
            border-bottom-color: #fff;
            border-width: 10px;
            transform: translateX(-50%);
        }
/*Billing Grid Header - End*/

/*Treegrid - start*/

.billing-treegrid {
    width: 100%;
    white-space: nowrap;
    border-collapse: separate;
    table-layout: fixed;
    height: 1px;
}

    .billing-treegrid > td {
        height: 100%;
    }

    .billing-treegrid tr {
        display: table-row;
        cursor: default;
    }

    /* Extra space between columns for readability */
    .billing-treegrid th,
    .billing-treegrid td {
        overflow-x: hidden;
        text-overflow: ellipsis;
    }


    /*.billing-treegrid tr:focus,
.billing-treegrid td:focus,
.billing-treegrid a:focus {
    outline: 2px solid hsl(216, 94%, 70%) !important;
    outline: 2px solid hsl(216, 94%, 70%) !important;
    box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
    outline: 2px solid hsl(216, 94%, 70%) !important;
    background-color: hsl(216, 80%, 97%);
    box-shadow: none;
}*/

    /*.billing-treegrid a:focus {
    border-bottom: none;
}*/

    /* Hide collapsed rows */
    .billing-treegrid tr.hidden {
        display: none !important;
    }

/*default border transparent*/
.billing-treegrid-row-services td .billing-treegrid-cell-wrap {
    border-width: 2px 0px;
    border-style: solid;
    border-color: transparent;
}

.billing-treegrid-row-services td:first-child .billing-treegrid-cell-wrap {
    border-width: 2px 0px 2px 2px;
}

.billing-treegrid-row-services td:last-child .billing-treegrid-cell-wrap {
    border-width: 2px 2px 2px 0px;
}

/*border on hover*/
.billing-treegrid-row-services:hover,
.billing-treegrid-row-services:focus {
    position: relative;
}

    .billing-treegrid-row-services:hover::before,
    .billing-treegrid-row-services:focus::before {
        content: '';
        height: 100%;
        width: calc(100% - 30px);
        position: absolute;
        top: 0px;
        left: 15px;
        display: block;
        border: 2px solid #00549A;
        background-color: rgba(194,206,223,0.17);
        box-shadow: 0 6px 25px 0 rgba(0,0,0,0.12);
        z-index: 1;
        pointer-events: none;
        border-radius: 10px;
    }

/*billing-treegrid-row-services gray border bottom*/
.billing-treegrid-row-services hr {
    width: calc(100% - 60px);
    left: 30px;
}
.billing-treegrid-row-services:hover hr,
.billing-treegrid-row-services:focus hr {
    border-color: transparent;
}


.billing-treegrid {
    height: auto;
}

    .billing-treegrid,
    .billing-treegrid thead,
    .billing-treegrid tbody {
        display: flex !important;
        flex-direction: column;
    }

        .billing-treegrid thead tr {
            min-height: 79px;
        }

        .billing-treegrid tbody tr.billing-treegrid-row-services {
            min-height: 80px;
        }

        .billing-treegrid tbody tr.billing-treegrid-row-subtotal {
            min-height: 78px;
        }

        .billing-treegrid tbody tr.billing-treegrid-row-misc {
            min-height: 58px;
        }

        .billing-treegrid tbody tr.billing-treegrid-row-total {
            min-height: 102px;
        }

        .billing-treegrid tr {
            display: flex !important;
            width: 100%;
            position: relative !important;
        }

            .billing-treegrid tr th {
                display: inline-flex !important;
                justify-content: center;
                align-items: flex-end;
                flex-grow: 0;
                flex-shrink: 0;
                flex-direction: column;
            }

                .billing-treegrid tr th:first-child {
                    align-items: flex-start;
                }

            .billing-treegrid tr td {
                display: inline-flex !important;
                justify-content: flex-end;
                align-items: stretch;
                flex-grow: 0;
                flex-shrink: 0;
            }

            .billing-treegrid tr > th:nth-child(2),
            .billing-treegrid tr > td:nth-child(2) {
                /*width: 90px !important;*/
                width: 120px !important;
            }

            .billing-treegrid tr > th:nth-child(3),
            .billing-treegrid tr > td:nth-child(3) {
                /*width: 150px !important;*/
                width: 150px !important;
            }

            .billing-treegrid tr > th:last-child:not(:first-child),
            .billing-treegrid tr > td:last-child:not(:first-child) {
                width: 170px !important;
            }

            .billing-treegrid tr > th:first-child,
            .billing-treegrid tr > td:first-child {
                width: 100% !important;
                flex-grow: 1;
                flex-shrink: 1;
                justify-content: flex-start;
            }

            .billing-treegrid tr > td > div {
                flex-basis: 100%;
            }

            /*Row services focus - start*/

            .billing-treegrid tr:focus,
            .billing-treegrid-row-services:focus {
                outline: 0 !important;
                box-shadow: none !important;
                position: relative;
            }

                .billing-treegrid tr:focus::after {
                    content: '';
                    height: calc(100% - 6px);
                    width: calc(100% - 29px);
                    position: absolute;
                    top: 3px;
                    left: 15px;
                    display: block;
                    box-shadow: 0 0 3px 1px #5fb0fc, 0 0 3px 2px #8ec6fc;
                    z-index: 1;
                    pointer-events: none;
                    border-radius: 10px;
                }

            .billing-treegrid tr.billing-treegrid-row-services:focus::after,
            .billing-treegrid tr.billing-treegrid-subtotal-mobility:focus::after,
            .billing-treegrid tr.billing-treegrid-subtotal-tv:focus::after,
            .billing-treegrid tr.billing-treegrid-subtotal-internet:focus::after,
            .billing-treegrid tr.billing-treegrid-subtotal-homephone:focus::after,
            .billing-treegrid tr.billing-treegrid-subtotal-other:focus::after {
                height: calc(100% + 6px);
                width: calc(100% - 24px);
                position: absolute;
                top: -3px;
                left: 12px;
            }

            .billing-treegrid tr.billing-treegrid-row-2:focus::after,
            .billing-treegrid tr.billing-treegrid-row-3:focus::after,
            .billing-treegrid tr.billing-treegrid-row-4:focus::after,
            .billing-treegrid tr.billing-treegrid-row-5:focus::after {
                height: 100%;
                width: calc(100% - 130px);
                position: absolute;
                top: 0px;
                left: 99px;
            }

            .billing-treegrid tr.billing-treegrid-row-user:focus::after {
                width: calc(100% - 54px);
                left: 27px;
            }

            .billing-treegrid tr.billing-treegrid-row-sharing:focus::after {
                height: calc(100% - 12px);
                width: calc(100% - 54px);
                top: 11px;
                left: 27px;
            }

            .billing-treegrid tr.billing-treegrid-row-ads:focus::after {
                height: calc(100% - 20px);
                top: 20px;
            }

            .billing-treegrid tr.billing-treegrid-subtotal-mobility:focus::after,
            .billing-treegrid tr.billing-treegrid-subtotal-tv:focus::after,
            .billing-treegrid tr.billing-treegrid-subtotal-internet:focus::after,
            .billing-treegrid tr.billing-treegrid-subtotal-homephone:focus::after,
            .billing-treegrid tr.billing-treegrid-subtotal-other:focus::after {
                height: calc(100% - 20px);
                top: 20px;
            }

            .billing-treegrid tr.billing-treegrid-user-subtotal:focus::after {
                height: calc(100% - 20px);
                top: 10px;
            }



/*Treegrid cell focus - start*/
.billing-treegrid td:focus {
    outline: 0 !important;
    box-shadow: none !important;
    position: relative;
}

            .billing-treegrid td:focus::after {
                content: '';
                height: calc(100% - 18px);
                width: calc(100% - 6px);
                position: absolute;
                top: 9px;
                left: 3px;
                display: block;
                box-shadow: 0 0 3px 1px #5fb0fc, 0 0 3px 2px #8ec6fc;
                z-index: 1;
                pointer-events: none;
                border-radius: 5px;
            }

        .billing-treegrid td:first-child:focus::after {
            width: calc(100% - 33px);
            position: absolute;
            left: 29px;
            border-radius: 5px;
        }

        .billing-treegrid td:last-child:focus::after {
            width: calc(100% - 27px);
        }

        .billing-treegrid .billing-treegrid-row-misc td:first-child:focus::after {
            left: 22.5px;
        }

        .billing-treegrid .billing-treegrid-row-total:focus::after {
            height: calc(100% - 12px);
            top: 6px;
        }

        .billing-treegrid .billing-treegrid-row-total td:first-child:focus::after {
            left: 16.5px;
        }

        .billing-treegrid tr.billing-treegrid-row-3 td:focus::after,
        .billing-treegrid tr.billing-treegrid-row-4 td:focus::after,
        .billing-treegrid tr.billing-treegrid-row-5 td:focus::after {
            width: calc(100% - 6px);
            height: calc(100% - 6px);
            top: 3px;
            left: 3px;
        }

        .billing-treegrid tr.billing-treegrid-row-3 td:first-child:focus::after,
        .billing-treegrid tr.billing-treegrid-row-4 td:first-child:focus::after,
        .billing-treegrid tr.billing-treegrid-row-5 td:first-child:focus::after {
            width: calc(100% - 103px);
            left: 99px;
        }

        .billing-treegrid tr.billing-treegrid-row-3 td:last-child:focus::after,
        .billing-treegrid tr.billing-treegrid-row-4 td:last-child:focus::after,
        .billing-treegrid tr.billing-treegrid-row-5 td:last-child:focus::after {
            width: calc(100% - 35px);
        }

        .billing-treegrid tr.billing-treegrid-row-user td:first-child:focus::after {
            width: calc(100% - 43px);
            left: 34px;
        }

        .billing-treegrid tr.billing-treegrid-row-sharing td:focus::after {
            height: calc(100% - 18px);
            width: calc(100% - 33px);
            top: 14px;
            left: 27px;
        }

        .billing-treegrid tr.billing-treegrid-row-user td:last-child:focus::after {
            width: calc(100% - 27px);
        }

        .billing-treegrid tr.billing-treegrid-row-sharing td:last-child:focus::after {
            width: calc(100% - 30px);
            left: 3px;
        }

        .billing-treegrid tr.billing-treegrid-subtotal-mobility td:focus::after,
        .billing-treegrid tr.billing-treegrid-subtotal-tv td:focus::after,
        .billing-treegrid tr.billing-treegrid-subtotal-internet td:focus::after,
        .billing-treegrid tr.billing-treegrid-subtotal-homephone td:focus::after,
        .billing-treegrid tr.billing-treegrid-subtotal-other td:focus::after {
            height: calc(100% - 33px);
            width: calc(100% - 6px);
            top: 24px;
        }

        .billing-treegrid tr.billing-treegrid-subtotal-mobility td:first-child:focus::after,
        .billing-treegrid tr.billing-treegrid-subtotal-tv td:first-child:focus::after,
        .billing-treegrid tr.billing-treegrid-subtotal-internet td:first-child:focus::after,
        .billing-treegrid tr.billing-treegrid-subtotal-homephone td:first-child:focus::after,
        .billing-treegrid tr.billing-treegrid-subtotal-other td:first-child:focus::after {
            width: calc(100% - 33px);
            left: 25px;
        }

        .billing-treegrid tr.billing-treegrid-subtotal-mobility td:last-child:focus::after,
        .billing-treegrid tr.billing-treegrid-subtotal-tv td:last-child:focus::after,
        .billing-treegrid tr.billing-treegrid-subtotal-internet td:last-child:focus::after,
        .billing-treegrid tr.billing-treegrid-subtotal-homephone td:last-child:focus::after,
        .billing-treegrid tr.billing-treegrid-subtotal-other td:last-child:focus::after {
            width: calc(100% - 27px);
        }

        .billing-treegrid tr.billing-treegrid-user-subtotal td:focus::after {
            height: calc(100% - 26px);
            top: 13px;
        }





.billing-treegrid-row-total .big-price,
.billing-treegrid-row-mob-total .big-price {
    color: #ffffff;
    font-size: 30px;
    letter-spacing: -0.75px;
    line-height: 22px;
}

    .billing-treegrid-row-total .big-price span,
    .billing-treegrid-row-mob-total .big-price span {
        font-size: 14px;
        letter-spacing: -0.35px;
        line-height: 22px;
        top: -3px;
    }

        .billing-treegrid-row-total .big-price span:last-of-type,
        .billing-treegrid-row-mob-total .big-price span:last-of-type {
            margin-left: 2px;
        }

/*Row expanded chevron - start*/
.billing-treegrid tr[aria-expanded="true"] .icon-chevron-up:before {
    -webkit-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    transform: rotate(-90deg);
    -webkit-transform-origin: center;
    -ms-transform-origin: center;
    transform-origin: center;
}

.billing-treegrid tr[aria-expanded="false"] .icon-chevron-up:before {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-transform-origin: center;
    -ms-transform-origin: center;
    transform-origin: center;
}

/*Row Service expanded*/
.billing-treegrid-row-services[aria-expanded="true"] .billing-treegrid-cell-wrap,
.billing-treegrid-subtotal-mobility .billing-treegrid-cell-wrap,
.billing-treegrid-subtotal-tv .billing-treegrid-cell-wrap,
.billing-treegrid-subtotal-internet .billing-treegrid-cell-wrap,
.billing-treegrid-subtotal-homephone .billing-treegrid-cell-wrap,
.billing-treegrid-subtotal-other .billing-treegrid-cell-wrap {
    background-color: rgba(194,206,223,0.17);
}

.billing-treegrid-row-services[aria-expanded="true"] hr,
.billing-treegrid-row-user[aria-expanded="true"] hr {
    border: none;
}

.billing-treegrid-row-services[aria-expanded="true"] .billing-treegrid-row-services-amount {
    display: none;
}

.billing-treegrid-row-services[aria-expanded="true"] .icon-increase,
.billing-treegrid-row-services[aria-expanded="true"] .icon-decrease {
    display: none;
}

/*Row Expanded Blue border*/
.billing-treegrid-row-services[aria-expanded="true"]:before,
.billing-treegrid-row-2:before,
.billing-treegrid-row-3:before,
.billing-treegrid-row-4:before,
.billing-treegrid-row-5:before,
.billing-treegrid-subtotal-mobility:before,
.billing-treegrid-subtotal-tv:before,
.billing-treegrid-subtotal-internet:before,
.billing-treegrid-subtotal-homephone:before,
.billing-treegrid-subtotal-other:before {
    content: '';
    height: 100%;
    width: calc(100% - 30px);
    position: absolute;
    top: 0px;
    left: 15px;
    display: block;
    border: 2px solid #00549A;
    z-index: 1;
    border-radius: 10px;
}

.billing-treegrid-row-services[aria-expanded="true"]:before,
.billing-treegrid-row-services[aria-expanded="true"]:hover::before {
    border-bottom: 0px;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
}

.billing-treegrid-row-2:before,
.billing-treegrid-row-3:before,
.billing-treegrid-row-4:before,
.billing-treegrid-row-5:before {
    border-top: 0px;
    border-bottom: 0px;
    border-radius: 0px;
}

.billing-treegrid-subtotal-mobility:before,
.billing-treegrid-subtotal-tv:before,
.billing-treegrid-subtotal-internet:before,
.billing-treegrid-subtotal-homephone:before,
.billing-treegrid-subtotal-other:before {
    border-top: 0px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
}

/*Billing History-accordion*/
.billing-download-accordion {
    left: -55px;
    z-index: 2;
    transition-duration: 0s;
}
    .billing-download-accordion button:hover,
    .billing-download-accordion button:focus {
        background-color: #F4F4F4;
    }

    .billing-download-accordion .arrow {
        position: relative;
        background: #fff;
    }

        .billing-download-accordion .arrow:after {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-color: transparent;
            border-top: 0;
            top: -10px;
            left: 51%;
            border-bottom-color: #fff;
            border-width: 10px;
            transform: translateX(-50%);
        }

/*End Billing History-accordion*/


/*Bill Comparison - Start*/
    .billing-treegrid-row-chart-bar-previous {
        height: 91px;
    }
    .billing-treegrid-row-chart-bar-current {
        height: 63px;
    }
/*Bill Comparison - End*/

/*PBE - Start*/

.pbe-chart {
    max-width: 795px;
}

.pbe-line {
    width: 100%;
    height: 10px;
    border-left: 1px solid #979797;
    border-right: 1px solid #979797;
    background: linear-gradient(#979797,#979797) center/100% 1px no-repeat;
    position: absolute;
    top: -5px;
}

.pbe-new-activation-chart .pbe-no-services-progress-bar-striped,
.pbe-service-removed-chart .pbe-no-services-progress-bar-striped {
    background-image: linear-gradient(135deg, #c2cedf 45%, #00549a 45%, #00549a 50%, #c2cedf 50%, #c2cedf 95%, #00549a 95%, #00549a 100%);
    background-size: 10.00px 10.00px;
    width: 90%;
}

.pbe-new-activation-chart .pbe-prev-new-service,
.pbe-new-activation-chart .pbe-curr-new-service,
.pbe-service-removed-chart .pbe-prev-refund {
    width: 95%;
}

.pbe-new-activation-chart .pbe-curr-new-service {
    width: 50%;
}


.pbe-proration-chart .pbe-prev-new-package {
    width: 93%;
}

.pbe-proration-chart .pbe-curr-new-package,
.pbe-service-removed-chart .pbe-curr-no-service {
    width: 51%;
}

.pbe-proration-chart .pbe-prev-package-change {
    width: 85%;
}

.pbe-proration-chart .pbe-prev-refund {
    width: 93%;
}

.pbe-new-activation-chart .pbe-prev-new-service-line-wrap,
.pbe-new-activation-chart .pbe-curr-new-service-line-wrap,
.pbe-proration-chart .pbe-prev-new-package-line-wrap,
.pbe-proration-chart .pbe-curr-new-package-line-wrap,
.pbe-proration-chart .pbe-prev-refund-line-wrap,
.pbe-service-removed-chart .pbe-prev-refund-line-wrap,
.pbe-service-removed-chart .pbe-prev-refund-line-wrap,
.pbe-service-removed-chart .pbe-curr-no-service-line-wrap {
    position: absolute;
    top:10px;
    width: 95%;
}

.pbe-proration-chart .pbe-prev-new-package-line-wrap {
    top: -55px;
}

.pbe-proration-chart .pbe-prev-refund-line-wrap {
    top: 45px;
}

.pbe-new-activation-chart .pbe-curr-new-service-line-wrap,
.pbe-new-activation-chart .pbe-curr-new-service-line-wrap,
.pbe-proration-chart .pbe-curr-new-package-line-wrap,
.pbe-service-removed-chart .pbe-curr-no-service-line-wrap {
    width: 50%;
}

.pbe-proration-chart .pbe-prev-new-package-line-wrap,
.pbe-proration-chart .pbe-prev-refund-line-wrap {
    width: 93%;
}

.pbe-new-activation-chart .pbe-prev-new-service-line-inner,
.pbe-new-activation-chart .pbe-curr-new-service-line-inner,
.pbe-proration-chart .pbe-prev-new-package-line-inner,
.pbe-proration-chart .pbe-curr-new-package-line-inner,
.pbe-proration-chart .pbe-prev-refund-line-inner,
.pbe-service-removed-chart .pbe-prev-refund-line-inner,
.pbe-service-removed-chart .pbe-curr-no-service-line-inner {
    background-color: #949596;
    height: 57px;
    width: 1px;
    margin-right: 0;
    margin-left: auto;
}

.pbe-proration-chart .pbe-prev-new-package-line-inner,
.pbe-proration-chart .pbe-prev-refund-line-inner,
.pbe-service-removed-chart .pbe-prev-refund-line-inner,
.pbe-service-removed-chart .pbe-curr-no-service-line-inner {
    height: 72px;
}

    .pbe-new-activation-chart .pbe-prev-new-service-line-inner:before,
    .pbe-new-activation-chart .pbe-curr-new-service-line-inner:before,
    .pbe-proration-chart .pbe-prev-new-package-line-inner:before,
    .pbe-proration-chart .pbe-curr-new-package-line-inner:before,
    .pbe-proration-chart .pbe-prev-refund-line-inner:before,
    .pbe-service-removed-chart .pbe-prev-refund-line-inner:before,
    .pbe-service-removed-chart .pbe-curr-no-service-line-inner:before {
        content: '';
        display: block;
        margin-left: -4.5px;
        margin-bottom: -4.5px;
        height: 10px;
        width: 10px;
        border-radius: 50%;
        background-color: #949596;
    }

    .pbe-proration-chart .pbe-prev-new-package-line-inner:before {
        position: absolute;
        bottom: 0;
    }

.pbe-new-activation-chart .pbe-prev-service-activated,
.pbe-new-activation-chart .pbe-curr-service-activated,
.pbe-service-removed-chart .pbe-prev-service-removed {
    width: 90%;
}

.pbe-new-activation-chart .pbe-prev-service-activated-blue-line-wrap,
.pbe-new-activation-chart .pbe-curr-service-activated-blue-line-wrap,
.pbe-proration-chart .pbe-prev-package-change-blue-line-wrap,
.pbe-service-removed-chart .pbe-prev-service-removed-blue-line-wrap {
    position: relative;
    width: 90%;
}

.pbe-proration-chart .pbe-prev-package-change-blue-line-wrap {
    width: 85%;
}

.pbe-new-activation-chart .pbe-prev-service-activated-blue-line-inner,
.pbe-new-activation-chart .pbe-curr-service-activated-blue-line-inner,
.pbe-proration-chart .pbe-prev-package-change-blue-line-inner,
.pbe-service-removed-chart .pbe-prev-service-removed-blue-line-inner {
    width: 6px;
    background-color: #0075FF;
    border: 1px solid #f4f4f4;
    height: 40px;
    position: absolute;
    bottom: 0;
    right: 0;
}

.pbe-proration-chart .pbe-prev-package-change-blue-line-inner {
    height: 76px;
}

.pbe-proration-chart .pbe-new-package-empty-bar {
    width: 85%;
}

.pbe-proration-chart .pbe-old-package-bar,
.pbe-service-removed-chart .pbe-old-service-bar {
    border: 1px solid #949596;
    width: 85%;
}

.pbe-service-removed-chart .pbe-old-service-bar {
    width: 90%;
}

.pbe-tag {
    background-color: rgba(0,84,154,0.2);
}

/*Start Pagination*/
.bill-redesign .page-number-active {
    background-color: #00549a;
    color: #ffffff;
    font-weight: bold;
}

.bill-redesign .page-number-nav {
    height: 30px;
    width: 30px;
    border-radius: 50%;
}

.bill-redesign .pagination > li > a, .pagination > li > span {
    border-radius: 0;
    padding: 0px 0px;
}


.bill-pagination {
    color: #00549A;
}

.pagination {
    font-size: 14px;
    text-align: center;
}

.bill-pagination ol {
    list-style-type: none;
    padding: 0;
    margin: 0;
    display: inline-block;
}

.bill-pagination li.active a {
    background: #00549A;
    color: #fff;
    display: flex;
}

    .bill-pagination li.active a:hover {
        text-decoration: none;
        cursor: default;
    }

.bill-prev.disabled, .bill-next.disabled {
    cursor: default;
    color: #949596;
}

    .bill-prev.disabled:hover, .bill-next.disabled:hover {
        cursor: default;
        text-decoration: none;
    }

.bill-prev {
    margin-right: 2.5px;
}

.bill-next {
    margin-left: 2.5px;
}
/*End Pagination*/

.overflow-information {
    overflow: hidden;
    display: inline-block;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 90%;
}


/*End Pagination*/
@media all and (-ms-high-contrast:none) {
    .billing-treegrid-row-services:focus::after {
        left: 17px;
    }
}
/*Treegrid - End*/

/*Billing services Mobile - start*/
.billing-services-mob-list li button:hover,
.billing-services-mob-list li button:focus {
    background-color: rgba(194,206,223,0.17);
}

/*Print/Download Dialog - Mobile - Start*/
.billing-services-print-modal .modal-dialog,
.billing-services-download-modal .modal-dialog {
    width: 194px;
}

.billing-services-print-modal .modal-header,
.billing-services-download-modal .modal-header {
    height: auto;
}

.billing-services-print-modal .modal-dialog .modal-content .modal-header .close,
.billing-services-download-modal .modal-dialog .modal-content .modal-header .close {
    margin: 0;
    padding: 0;
    border: 0;
}

/*Mobility Modal - Start */

.bell-services-mobility-modal button {
    padding: 0px;
}

.bell-services-mobility-modal li button[aria-expanded="false"] .icon-chevron-up:before {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-transform-origin: center;
    -ms-transform-origin: center;
    transform-origin: center;
}

.bell-services-mobility-modal li button[aria-expanded="true"] .icon-chevron-up:before {
    -webkit-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    transform: rotate(-90deg);
    -webkit-transform-origin: center;
    -ms-transform-origin: center;
    transform-origin: center;
}

.bell-services-mobility-modal li button[aria-expanded="true"] + hr {
    background: none;
    clear: both;
    float: none;
    width: 100%;
    height: 1px;
    border: none;
    margin: -1px 0;
}


.bell-services-mobility-modal .table > tbody > tr > th {
    padding: 15px 0 0 15px;
}

.bell-services-mobility-modal .table td {
    padding: 0;
}

.bell-services-mobility-modal .table {
    table-layout: unset;
}

/*End Mobility Modal*/

@media (max-width: 767.98px) {

    .bill-redesign .big-price {
        font-size: 30px;
        line-height: normal;
    }

        .bill-redesign .big-price span {
            font-size: 14px;
            line-height: 22px;
            letter-spacing: -0.35px;
            vertical-align: top;
            top: 4px;
        }

            .bill-redesign .big-price span:last-of-type {
                margin-left: 4px;
            }

    .billing-treegrid-row-mob-total .big-price span:last-of-type {
        margin-left: 2px;
    }

    .dimension-60-xs {
        width: 60px;
        height: 60px;
    }

    .txtSize13-xs {
        font-size: 13px;
    }

    .txtSize22-xs {
        font-size: 22px;
    }

    .txtSize25-xs {
        font-size: 27px;
    }

    .txtSize28-xs {
        font-size: 28px;
    }

    .txtSize35-xs {
        font-size: 35px;
    }

    .txtSize38-xs {
        font-size: 38px;
    }

    .margin-top-neg-10-xs {
        margin-top: -10px;
    }

    .pad-h-xs-0-force {
        padding-left: 0 !important;
        padding-right: 0 !important
    }

    .max-width-xs-260 {
        max-width: 260px;
    }
    .payment-bill-summary-content {
        background: #FFFFFF;
    }
    /*Hierarchy Design in Accordion*/
    .vertical-bullet-xs {
        margin-left: 5px;
        border-left: 1px solid lightGray;
        height: 46px;
    }

    .horizontal-bullet-xs {
        border-bottom: 1px solid lightGray;
        width: 10px;
        margin-bottom: 35px;
    }

    .height-12 {
        height: 12px;
    }

    .bill-redesign .form-control-select + span {
        right: 10px;
        top: 3px;
    }
    /*Hierarchy Design in Accordion*/

    .billing-services-print-modal .modal-dialog,
    .billing-services-download-modal .modal-dialog {
        position: relative;
    }
    /*Modal design in tooltip*/
    .billing-tooltip-info .modal.modal-tooltip .tooltip-dialog {
        margin: auto 10px;
    }

        .billing-tooltip-info .modal.modal-tooltip .tooltip-dialog .close {
            padding: 11px;
        }

    .bill-history-info .modal.modal-tooltip .tooltip-dialog {
        margin: auto 45px;
    }

        .bill-history-info .modal.modal-tooltip .tooltip-dialog .close {
            padding: 10px;
        }
    /*Start Bar Graph*/
    .barcontainer {
        position: relative;
        border-radius: 5px 5px 0 0;
        margin: 0 auto;
        min-width: 40%;
        max-width: 60%;
        z-index: 1;
        margin-bottom: 30px;
    }

    .bar {
        display: inline-block;
        bottom: 0;
        border-radius: 5px 5px 0 0;
        background: #00549A;
        width: 30px;
        text-align: center;
        color: white;
    }

        .bar:hover {
            background: #003778;
        }

    .barlabel {
        position: absolute;
        bottom: -30px;
        width: 100%;
        color: #555555;
        padding-top: 12px;
    }

    .bar:nth-child(2) {
        left: -20%;
        height: 60px;
    }

    .bar:nth-child(3) {
        left: 20%;
    }

    .bar:nth-child(4) {
        left: -7%;
        height: 88px;
    }

    .bar:nth-child(5) {
        left: 50%;
    }

    .bar:nth-child(6) {
        left: 6%;
        height: 44px;
    }

    .bar:nth-child(7) {
        left: 17%;
        height: 71px;
    }
    /*End Bar Graph*/
    
    /* Card Carousel Start */
    .tile-carousel {
        padding: 0 30px;
    }

        .tile-carousel.slick-initialized {
            margin-left: -15px;
            margin-right: -7.5px;
         
        }
            .tile-carousel.slick-initialized .slick-list .slick-track {
                margin-left: -15px;
            }

            .tile-carousel.slick-initialized.centered-mobile-slide .slick-list .slick-track {
                margin-left: 0;
            }

            .tile-carousel.slick-initialized .slick-slide > div {
                padding-left: 7.5px;
                padding-right: 7.5px;
            }
            /* Card Carousel Start */
            .tile-carousel.slick-initialized .slick-list {
                overflow: visible;
            }

            .tile-carousel.slick-initialized .slick-slide {
                margin-right: 0;
                margin-left: 0;
            }

                .tile-carousel:not(.slick-initialized) > div,
                .tile-carousel.slick-initialized .slick-slide > div > div {
                    flex: auto;
                }

                .tile-carousel.slick-initialized .slick-slide > div > div {
                    margin-bottom: 0;
                }

                .tile-carousel.slick-initialized .radio-card,
                .tile-carousel.slick-initialized .slick-slide > div > div {
                    width: auto;
                    max-width: none;
                }

    /* Card Carousel End */

    /*PBE Start*/
    .pbe-new-activation-chart .pbe-curr-new-service {
        width: 80%;
    }

    .pbe-new-activation-chart .pbe-curr-new-service-line-wrap,
    .pbe-proration-chart .pbe-curr-new-package-line-wrap {
        width: 20%;
    }

    .pbe-proration-chart .pbe-curr-new-package {
        width: 75%;
    }

    .pbe-proration-chart .pbe-curr-new-package-line-wrap {
        width: 25%;
    }

    .pbe-service-removed-chart .pbe-prev-refund-line-inner,
    .pbe-service-removed-chart .pbe-curr-no-service-line-inner {
        height: 85px;
    }
    /*Start Pagination*/
    .bill-pagination li:first-child,
    .bill-pagination li.active,
    .bill-pagination li.active-sibling:nth-last-child(2),
    .bill-pagination li:last-child {
        display: inline-block !important;
    }

        .bill-pagination li:first-child:nth-last-child(n+6) ~ li {
            display: none;
        }

            .bill-pagination li:first-child:nth-last-child(n+6) ~ li:nth-last-child(-n + 3) {
                display: inline-block;
            }

            .bill-pagination li:first-child:nth-last-child(n+6) ~ li:nth-last-child(3):before {
                content: "\2026";
                font-size: 24px;
                display: inline-block;
                margin-right: 2.5px;
            }

        .bill-pagination li:first-child:nth-last-child(n+6).active:before, li:first-child:nth-last-child(n+6) ~ li.active:before {
            content: "\2026";
            font-size: 24px;
            display: inline-block;
            margin-right: 2.5px;
        }

        .bill-pagination li:first-child:nth-last-child(n+6).active:after, li:first-child:nth-last-child(n+6) ~ li.active:after {
            content: "\2026";
            font-size: 24px;
            display: inline-block;
            margin-left: 2.5px;
        }

        .bill-pagination li:first-child:nth-last-child(n+6).active:nth-child(-n + 2):before,
        .bill-pagination li:first-child:nth-last-child(n+6) ~ li.active:nth-child(-n + 2):before,
        .bill-pagination li:first-child:nth-last-child(n+6).active:nth-last-child(-n + 2):before,
        .bill-pagination li:first-child:nth-last-child(n+6) ~ li.active:nth-last-child(-n + 2):before,
        .bill-pagination li:first-child:nth-last-child(n+6).active:nth-child(-n + 2):after,
        .bill-pagination li:first-child:nth-last-child(n+6) ~ li.active:nth-child(-n + 2):after,
        .bill-pagination li:first-child:nth-last-child(n+6).active:nth-last-child(-n + 2):after,
        .bill-pagination li:first-child:nth-last-child(n+6) ~ li.active:nth-last-child(-n + 2):after {
            display: none;
        }

        .bill-pagination li:first-child:nth-last-child(n+6).active ~ li:nth-last-child(-n + 3),
        .bill-pagination li:first-child:nth-last-child(n+6) ~ li.active ~ li:nth-last-child(-n + 3) {
            display: none;
        }

        .bill-pagination li:first-child:nth-last-child(n+6).active ~ li:nth-child(-n + 3),
        .bill-pagination li:first-child:nth-last-child(n+6) ~ li.active ~ li:nth-child(-n + 3) {
            display: inline-block;
        }

        .bill-pagination li:first-child:nth-last-child(n+6).active ~ li:nth-child(-n + 2):after,
        .bill-pagination li:first-child:nth-last-child(n+6) ~ li.active ~ li:nth-child(-n + 2):after {
            display: none;
        }

        .bill-pagination li:first-child:nth-last-child(n+6).active ~ li:nth-child(3):after,
        .bill-pagination li:first-child:nth-last-child(n+6) ~ li.active ~ li:nth-child(3):after {
            content: "\2026";
            font-size: 24px;
            display: inline-block;
            margin-left: 2.5px;
        }
    /*End of Pagination*/
}

@media (min-width: 768px) {

    .same-height-316 {
        height: 316px;
    }

    .bill-redesign .form-absolute-right {
        position: absolute;
        top: 0;
        right: 0;
        margin-top: 76px;
        max-width: 189px;
    }

    .bill-redesign .infoblock-slider .slick-list {
        margin: 0 -7.5px;
        padding: 0;
    }

    .bill-redesign .infoblock-slider .slick-track {
        margin-left: 0;
    }

    .bill-redesign .slick-overflow-visible .slick-list {
        overflow: visible;
    }

    .bill-redesign .infoblock-slider .slick-slide {
        margin-right: 7.5px;
        margin-left: 7.5px;
    }

    /*call to action - focus outline*/
    .bill-redesign-accss-footer a.call-to-action:focus::before {
        width: calc(100% - 12px);
        border-radius: 30px;
        left: -9px;
    }

    /*Start Pagination*/
    .bill-pagination li:first-child,
    .bill-pagination li.active-sibling,
    .bill-pagination li.active,
    .bill-pagination li.active + li,
    .bill-pagination li:last-child {
        display: inline-block !important;
    }

        .bill-pagination li:first-child:nth-last-child(n+8) ~ li {
            display: none;
        }

            .bill-pagination li:first-child:nth-last-child(n+8) ~ li.active-sibling:before {
                content: "\2026";
                font-size: 24px;
                display: inline-block;
                margin-right: 2.5px;
            }

            .bill-pagination li:first-child:nth-last-child(n+8) ~ li.active + li:after {
                content: "\2026";
                font-size: 24px;
                display: inline-block;
                margin-left: 2.5px;
            }

            .bill-pagination li:first-child:nth-last-child(n+8) ~ li:nth-last-child(-n + 5) {
                display: inline-block;
            }

            .bill-pagination li:first-child:nth-last-child(n+8) ~ li:nth-last-child(5):before {
                content: "\2026";
                font-size: 24px;
                display: inline-block;
                margin-right: 2.5px;
            }

            .bill-pagination li:first-child:nth-last-child(n+8) ~ li:nth-child(-n + 2):before,
            .bill-pagination li:first-child:nth-last-child(n+8) ~ li:nth-last-child(-n + 2):before,
            .bill-pagination li:first-child:nth-last-child(n+8) ~ li.active-sibling:nth-last-child(-n + 4):before,
            .bill-pagination li:first-child:nth-last-child(n+8) ~ li:nth-child(-n + 2):after,
            .bill-pagination li:first-child:nth-last-child(n+8) ~ li:nth-last-child(-n + 2):after,
            .bill-pagination li:first-child:nth-last-child(n+8) ~ li.active-sibling:nth-last-child(-n + 4):after {
                display: none !important;
            }

            .bill-pagination li:first-child:nth-last-child(n+8).active ~ li:nth-last-child(-n + 5),
            .bill-pagination li:first-child:nth-last-child(n+8) ~ li.active ~ li:nth-last-child(-n + 5) {
                display: none;
            }

                .bill-pagination li:first-child:nth-last-child(n+8).active ~ li:nth-last-child(-n + 5):before,
                .bill-pagination li:first-child:nth-last-child(n+8) ~ li.active ~ li:nth-last-child(-n + 5):before {
                    display: none;
                }

            .bill-pagination li:first-child:nth-last-child(n+8).active ~ li:nth-child(-n + 5),
            .bill-pagination li:first-child:nth-last-child(n+8) ~ li.active ~ li:nth-child(-n + 5) {
                display: inline-block;
            }

            .bill-pagination li:first-child:nth-last-child(n+8).active ~ li:nth-child(-n + 4):after,
            .bill-pagination li:first-child:nth-last-child(n+8) ~ li.active ~ li:nth-child(-n + 4):after {
                display: none;
            }

    li:first-child:nth-last-child(n+8).active ~ li:nth-child(5):after,
    li:first-child:nth-last-child(n+8) ~ li.active ~ li:nth-child(5):after {
        content: "\2026";
        font-size: 24px;
        display: inline-block;
        margin-left: 2.5px;
    }

    .bill-pagination li:first-child:nth-last-child(n+8).active:before,
    .bill-pagination li:first-child:nth-last-child(n+8) ~ li.active:before,
    .bill-pagination li:first-child:nth-last-child(n+8).active:after, li:first-child:nth-last-child(n+8) ~ li.active:after {
        display: none;
    }
    /*End Pagination*/


    /*Start Bar Graph*/
    .barcontainer {
        position: relative;
        border-radius: 5px 5px 0 0;
        margin: 0 auto;
        min-height: 10vw;
        max-height: 50vw;
        min-width: 40%;
        max-width: 60%;
        z-index: 1;
        margin-bottom: 30px;
    }

    .bar {
        display: inline-block;
        bottom: 0;
        border-radius: 5px 5px 0 0;
        background: #00549A;
        width: 15%;
        text-align: center;
        color: white;
    }

        .bar:hover {
            background: #003778;
        }

    .barlabel {
        position: absolute;
        bottom: -30px;
        width: 100%;
        color: #555555;
        padding-top: 12px;
    }

    .bar:nth-child(2) {
        left: -12%;
        height: 37px;
    }

    .bar:nth-child(3) {
        left: 20%;
    }

    .bar:nth-child(4) {
        left: 8%;
        height: 103px;
    }

    .bar:nth-child(5) {
        left: 50%;
    }

    .bar:nth-child(6) {
        left: 28%;
        height: 60px;
    }

    .bar:nth-child(7) {
        left: 48%;
        height: 80px;
    }

    /*End Bar graph*/
}

@media (min-width: 992px) {
    .margin-top-neg-190-md {
        margin-top: -190px;
    }

    .margin-t-md-8{
        margin-top: 8px;
    }

    .bill-redesign .form-absolute-right {
        position: absolute;
        top: 0;
        right: 0;
        margin-top: 195px;
        max-width: 189px;
    }

    .flex-direction-column-md {
        flex-direction: column;
    }

    .bill-redesign .hidden-md {
        display: none;
    }

    .bgGray19-md {
        background-color: #f4f4f4;
    }

    /*Start Bar Graph*/
    /*Bar Graph*/
    .barcontainer {
        position: relative;
        border-radius: 5px 5px 0 0;
        margin: 0 auto;
        min-width: 40%;
        max-width: 60%;
        z-index: 1;
        margin-bottom: 30px;
        min-height: 100px;
        max-height: 496px;
    }


    .bar {
        display: inline-block;
        bottom: 0;
        border-radius: 5px 5px 0 0;
        background: #00549A;
        width: 11%;
        text-align: center;
        color: white;
    }

        .bar:hover {
            background: #003778;
        }

    .barlabel {
        position: absolute;
        bottom: -30px;
        width: 100%;
        color: #555555;
        padding-top: 12px;
    }

    .bar:nth-child(2) {
        left: 4%;
        height: 37px;
    }

    .bar:nth-child(3) {
        left: 20%;
    }

    .bar:nth-child(4) {
        left: 19%;
        height: 103px;
    }

    .bar:nth-child(5) {
        left: 50%;
    }

    .bar:nth-child(6) {
        left: 34%;
        height: 60px;
    }

    .bar:nth-child(7) {
        left: 49%;
        height: 80px;
    }
    /*End Bar graph*/
}

@media (min-width: 992px) and (max-width: 1140px) {
    .bill-redesign .margin-r-md-30 {
        margin-right: 30px;
    }
}

/* START - Focus Outline  Styles */

.bill-redesign-accss .bill-redesign a:focus,
.bill-redesign-accss .bill-redesign button:focus,
.bill-redesign-accss .bill-redesign .tooltip-static:focus,
.bill-redesign-accss .bill-redesign .tooltip:focus,
.bill-redesign-accss-header a:not(.connector-brand):focus,
.bill-redesign-accss-header button:focus,
body.is_tabbing .bill-redesign-accss-header .global-navigation .connector-active-secondary-nav a:focus,
.bill-redesign-accss-footer a:focus,
.bill-redesign-accss-footer button:focus,
#bills[tabindex="0"]:focus .billhistory-d,
#bills[tabindex="0"]:focus .billhistory-m,
#payments[tabindex="0"]:focus .paymenthistory-d,
#payments[tabindex="0"]:focus .paymenthistory-m,
.bill-redesign-accss .standard-step-flow-header a:focus,
.bill-redesign-accss .standard-step-flow-header button:focus,
.bill-redesign-accss .bell-services-mobility-modal a:focus {
    outline: none !important;
    box-shadow: none !important;
    position: relative;
}

    .bill-redesign-accss .bill-redesign a:focus::after,
    .bill-redesign-accss .bill-redesign button:focus::after,
    .bill-redesign-accss .bill-redesign .tooltip-static:focus::after,
    .bill-redesign-accss .bill-redesign .tooltip:focus::after,
    .bill-redesign-accss-header a:not(.connector-brand):not(.services-selection):focus::after,
    .bill-redesign-accss-header button:focus::before,
    .bill-redesign-accss-footer a:focus::before,
    .bill-redesign-accss-footer button:focus::before,
    .bill-redesign-accss-footer .legal-links a:focus::before,
    .bill-redesign-accss-header .connector-mobile-bar .connector-brand:focus::before,
    .bill-redesign-accss-header a.services-selection:focus::before,
    #bills[tabindex="0"]:focus .billhistory-d::after,
    #bills[tabindex="0"]:focus .billhistory-m::after,
    #payments[tabindex="0"]:focus .paymenthistory-d::after,
    #payments[tabindex="0"]:focus .paymenthistory-m::after,
    .bill-redesign-accss .standard-step-flow-header a:focus::after,
    .bill-redesign-accss .standard-step-flow-header button:focus::after {
        content: '';
        height: calc(100% + 6px);
        width: calc(100% + 6px);
        position: absolute;
        top: -3px;
        left: -3px;
        display: block;
        box-shadow: 0 0 3px 1px #5FB0FC, 0 0 3px 2px #8EC6FC;
        z-index: 1;
        pointer-events: none;
        border-radius: inherit;
    }

.bill-redesign-accss-header .connector-log-out-button:focus {
    overflow: initial;
}

.bill-redesign-accss-header .connector-nav-open-button:focus,
.bill-redesign-accss-header .connector-mobile-bar .connector-brand:focus,
.bill-redesign-accss-footer .skip-to-main-link:focus {
    position: absolute;
}

    .bill-redesign-accss-header .connector-mobile-bar .connector-brand:focus::before {
        height: calc(100% - 9px);
        width: calc(100% + 6px);
        top: 3px;
    }

.bill-redesign-accss .scrollToTop.mobile:focus {
    position: fixed;
}

.bill-redesign-accss .bill-redesign .btn:focus::after,
.bill-redesign-accss-footer .btn:focus::before,
.bill-redesign-accss .standard-step-flow-header button:focus::after {
    height: calc(100% + 12px);
    width: calc(100% + 12px);
    top: -6px;
    left: -6px;
}

.bill-redesign-accss .bell-services-mobility-modal li button:focus::after {
    height: calc(100% + 30px);
    width: calc(100% - 8px);
    position: absolute;
    top: -12px;
    left: 5px;
}

.bill-redesign-accss .bell-services-mobility-modal button:focus::after {
    height: calc(100% - 8px);
    width: calc(100% - 8px);
    position: absolute;
    top: 5px;
    left: 5px;
}

.bill-redesign .btn-default-white:focus {
    background-color: #FFFFFF;
    border-color: #FFFFFF;
}

.bill-redesign .btn-primary:active,
.bill-redesign .btn-primary:not(:disabled):not(.disabled):active,
.bill-redesign .btn-primary:focus {
    padding: 7px 30px;
}

.bill-redesign-accss .billing-services-mob-list a:focus::after,
.bill-redesign-accss .billing-services-mob-list button:focus::after,
.bill-redesign-accss .billing-services-print-modal .billing-services-print-btns button:focus::after,
.bill-redesign-accss .billing-services-download-modal .billing-services-download-btns button:focus::after {
    height: calc(100% - 6px);
    width: calc(100% - 6px);
    position: absolute;
    top: 3px;
    left: 3px;
}

.bill-redesign-accss .bill-redesign select:focus {
    box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc !important;
}

#bills[tabindex="0"]:focus .billhistory-m::after,
#payments[tabindex="0"]:focus .paymenthistory-m::after {
    width: calc(100% + 5px);
    left: -5px;
}

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    /* seems like IE rendering engine has an issue dealing with box-shadow directly applied to elements. use outline on default state to hide weird artifacts being left-out on blur */
    .bill-redesign-accss-header a,
    .bill-redesign-accss-footer a {
        outline: 7px solid transparent;
    }
}

/* END - Focus Outline  Styles */




