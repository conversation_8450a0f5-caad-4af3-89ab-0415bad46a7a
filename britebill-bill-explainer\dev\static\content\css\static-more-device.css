
/*HELPER CLASS START*/

.bg-no-repeat {
    background-repeat: no-repeat;
}

.margin-neg-left-20-xs {
    margin-left:-20px !important;
}


.txtGray {
    color: #c8cbcc;
}
.txtSkyBlue {
    color: #61CDF4;
}


.border-color-grey {
    border-color:#555;
}

/* HELPER CLASS END */

/*CUSTOM CLASS START */

.apple-h2 {
    font-size: 34px;
    font-weight: 600 !important;
    line-height: 1.0625em;
}

.apple-typography-p, .apple-txt-typography {
    font-family: "SF Pro Display","SF Pro Icons","Helvetica Neue","Helvetica","Arial","sans-serif" !important;
}

.apple-typography-p {
    font-size: 16px;
    letter-spacing: .012em;
    text-align: center;
    margin: 0 auto !important;
}

.apple-typography-p2 {
    letter-spacing: .011em;
    line-height: 1.42857143;
    font-size: 24px;
}

.apple-txt-typography {
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    line-height: 1.0625em;
    letter-spacing: -.04em;
    font-size: 34px;
    font-weight: 600 !important;
}

.apple-txt-background-1 {
    background-image: url('../../../../assets/img_tmp/Mobility/iPhone_XR/txt-bg-1.jpg');
}

.apple-txt-background-2 {
    background-image: url('../../../../assets/img_tmp/Mobility/iPhone_XR/txt-bg-2.jpg');
}

.apple-txt-background-3 {
    background-image: url('../../../../assets/img_tmp/Mobility/iPhone_XR/txt-bg-3.jpg');
}

.apple-txt-background-4 {
    background-image: url('../../../../assets/img_tmp/Mobility/iPhone_XR/txt-bg-4.jpg');
}

.apple-txt-background-5 {
    background-image: url('../../../../assets/img_tmp/Mobility/iPhone_XR/txt-bg-5.jpg');
}

.sup-orange {
    font-size: 50% !important;
    -webkit-text-fill-color: #be4e3a;
    color: #be4e3a !important;
}

.small-R {
    font-size: 80%;
    vertical-align: baseline;
}

a.txtNoUnderline {
    text-decoration:none;
}


/*custom for buy now button*/
.button-container {
    width: 37%;
}   

a.black-focus:focus {
    box-shadow: 0 0 0 3px #000, 0 0 2px 3px #000, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}

a.txtSkyBlue:focus {
    color: #61CDF4;
}
.iphone-se-compare-btn{
    width:29%;
    height:0.64%;
    bottom:12.35%;
    left:35.6%;
}
/* CUSTOM CLASS END */

/* START iPhone 12 */

.bottom-15 {bottom: 15px}

.img-h-centerView {
    margin-left: 50%;
    transform: translateX(-50%);
    min-width:100%;
}

.img-responsive-height {
    max-height: 100%;
}

.apple-devider {
    border-bottom: 1px solid #d6d6d6;
}

.bgGray98-apple {background-color: #fafafa}
.bgLightOrange-apple {background-color: #FBF6F1;}
.bgLightGreen-apple {background-color: #E7F4E0;}
.bgLightBlue-apple {background-color: #C9E2EF;}
.apple-bg-gray {background-color:#f5f5f7}


.width-585 {width: 585px;}
.max-width-106 {max-width: 106px;}
.max-width-530 {max-width: 530px;}
.max-width-815 {max-width: 815px;}

.height-300 {height: 300px;}
.height-330 {height: 330px;}
.max-height-270 {max-height: 270px;}

.margin-t-neg-65 {
    margin-top: -65px;
}
.margin-l-neg-75 {
    margin-left: -75px;
}
.margin-l-neg-95 {
    margin-left: -95px;
}

.pad-t-80 {
    padding-top: 80px;
}

.iPhone12-details-container {max-width: 280px;}

.iPhone12-container.container,
.iPhone12-details-container.container {
    padding-left: 0;
    padding-right: 0;
}

.iPhone12-images-container {
    bottom: 0;
    right: 0;
}

a.iPhone12-font-link {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", "Helvetica", "Arial", sans-serif, "SF Pro Icons";
    color: #0066cc;
}
a.iPhone12-font-link:hover {
    text-decoration: underline;
}
.text-spacing-normal {
    letter-spacing: normal;
}
.apple-default {
    font-weight: 600;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", "Helvetica", "Arial", sans-serif, "SF Pro Icons";
}

.apple-custom-header {
    font-size: 40px;
    line-height: 44px;
    text-align: center;
}

.apple-custom-caption {
    font-size: 32px;
    line-height: 36px;
    font-weight: 600;
}

.apple-custom-intro {
    font-size: 19px;
    line-height: 27px;
    text-align: center;
}

.apple-custom-eyebrow {
    font-size: 21px;
    line-height: 25px;
}

.apple-custom-headline {
    font-size: 32px;
    line-height: 36px;
    font-weight: 600;
}
.apple-40-headline-reduced {
    font-size: 32px;
    line-height: 36px;
    letter-spacing: 0.004em;
}
.apple-custom-desc {
    font-size: 19px;
    line-height: 27px;
    letter-spacing: normal;
}

.custom-17-body .apple-custom-desc {
    font-size: 14px;
    line-height: 20px;
    letter-spacing: -0.016em;
}

.apple-custom-details {
    color: #555;
    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
    font-family: "SF Pro Display","SF Pro Icons","Helvetica Neue","Helvetica","Arial",sans-serif;
}


.apple-typography-10 {
    font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif;
    line-height: 17.1429px;
    font-size: 12px;
    display: block;
    color: #555 !important;
}

.apple-typography-12 {
    font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif;
    font-weight: 400;
    font-size: 12px;
    line-height: 16.0005px;
}

.apple-typography-5 {
    line-height: 1.125;
    font-weight: 600;
    letter-spacing: 0em;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", "Helvetica", "Arial", sans-serif, "SF Pro Icons";
    color: #1d1d1f;
}
.apple-typography-6 {
    font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif;
    font-weight: 500;
    line-height: 1.235;
}

.apple-text-black-3 {color: #1d1d1f;}
.apple-text-black-4 {color: #555 !important;}
.apple-text-light-grey {color: #86868b;}
.apple-text-light-grey-2 {color: #a1a1a6;}

/* START compare iphone */
.iphone-12-pro, .iphone-12 {
    width: 80px;
}
.iphone-12-mini {
    width: 72px;
}
/* END compare iphone */

/* Start color swatches */
.apple-swatch {
    display: inline-block;
    margin: 0 9px 0 0;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    box-shadow: inset 0 0 1px 0 rgba(0, 0, 0, 0.2);
    display: block;
}
.apple-swatch:last-child{
    margin-right:0px;
}
.apple-swatch::after {
    border-radius: inherit;
    box-shadow: inset 0 2px 1.5px rgba(0, 0, 0, 0.1);
    content: "";
    display: block;
    height: inherit;
    position: absolute;
    width: inherit;
}
/* End color swatches */

.apple-btn {
    border-radius: 18px;
    font-size: 11px;
    line-height: 1.235;
    padding: 8px 11px 8px 11px;
}
.apple-btn-outline-dark {
    background-color: transparent;
    border: 1px solid #1d1d1f;
    color: #1d1d1f;
}
.apple-btn-outline-dark:hover {
    border: 1px solid #6e6e73;
    color: #6e6e73;
}



/* END iPhone 12 */

/** MEDIA QUERY */

@media (min-width: 520px) {
    .about-device-container {
        width: 480px;
    }
}

@media (min-width: 640px) {
    .about-device-container {
        width: 600px;
    }

    .button-container {
        width: 60%;
    }
}

@media (max-width: 639px) {
    .apple-h2, .apple-txt-typography {
        font-size: 25px;
    }

    .apple-typography-p2 {
        font-size: 20px;
    }

    .button-container {
        width: 43%;
    }
}

@media (max-width: 991.98px) and (min-width: 768px) {
    .width-percent-80-sm {
        width: 90%;
    }

    .button-container {
        width: 52%;
    }
}

@media (min-width: 992px) {
    .width-percent-60-md {
        width: 60%;
    }

    .about-device-container {
        width: 960px;
    }
    
}
@media (min-width: 992px) and (max-width:999.98px) {
    .about-device-container.iphone-se-container {
        width: 600px;
    }
}

@media (min-width:992px) and (max-width:1239px) {

    /*custom text padding*/
    .text-pad {
        padding-left:40px !important;
        padding-right:40px !important;
    }
}

/** MEDIA QUERY for IPHONE 12*/

@media (max-width: 734px)  {
    .iPhone12-container .col-xs-50 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    
    .iPhone12-container .first-col-padding .col-xs-50 {
        padding-left: 0;
        padding-right: 0;
    }
    .iPhone12-container .first-col-padding .col-xs-50:nth-of-type(odd) {
        padding-right: 18px;
    }
    
    .iPhone12-container .col-xs-50 {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .iPhone12-container .margin-b-0-xs {
        margin-bottom: 0;
    }
    .iPhone12-container .pad-h-0-xs {
        padding-left: 0;
        padding-right: 0;
    }
    
    .custom-17-body .apple-custom-details {
        font-size: 12px;
        line-height: 14px;
    }
    
    .absolute-xs {
        position: absolute;
    }

    

}
@media (min-width: 735px)  {

    /*#region Helpers*/
    .relative-sm {
        position: relative;
    }
    
    .iPhone12-container .col-sm-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    .iPhone12-container .col-sm-5 {
        flex: 0 0 41.6666666667%;
        max-width: 41.6666666667%;
    }
    .iPhone12-container .col-sm-7 {
        flex: 0 0 58.3333333333%;
        max-width: 58.3333333333%;
    }
    .iPhone12-container .d-sm-none {
        display: none !important;
    }
    .iPhone12-container .d-sm-block {
        display: block !important;
    }
    .iPhone12-container .d-sm-flex {
        display: flex !important;
    }
    
    .iPhone12-container .margin-t-0-sm {
        margin-top: 0;
    }
    .iPhone12-container .margin-b-0-sm {
        margin-bottom: 0px;
    }
    
    .iPhone12-container .margin-b-40-sm{
        margin-bottom: 40px;
    }
    .iPhone12-container .margin-b-60-sm {
        margin-bottom: 60px;
    }
    
    .iPhone12-container .pad-h-5-sm {
        padding-left: 5px;
        padding-right: 5px;
    }
    
    .iPhone12-container .pad-h-15-sm {
        padding-left: 15px;
        padding-right: 15px;
    }
    .iPhone12-container .pad-r-55-sm {
        padding-right: 55px;
    }

    .iPhone12-container .flex-sm-row-reverse {
        flex-direction: row-reverse!important;
    }
    .iPhone12-container .justify-content-sm-start {
        justify-content: flex-start!important;
    }
    
    /*#endregion */
    
    .iPhone12-images-container {
        right: -98px;
    }
    
    .iPhone12-header {
        margin-left:0 ;
    }
    .iPhone12-container .txtSize50-sm{
        font-size: 50px;
    }

    .apple-custom-header {
        text-align: left;
    }
    .apple-custom-intro {
        text-align: left;
    }

    /* Start compare iphone */
    .iphone-12-pro, .iphone-12 {
        width: 144px;
    }

    .iphone-12-mini {
        width: 129px;
    }
    /* End compare iphone */
    
    .apple-swatch {
        width: 20px;
        height: 20px;
    }

    .apple-btn {
        border-radius: 18px;
        font-size: 17px;
        line-height: 1.235;
        padding: 8px 17px 8px 17px;
    }
    
}
@media (min-width: 735px) and (max-width: 1068px){

    .iPhone12-details-container {max-width: 692px;}
    .custom-width-iphone12-1-1 .iPhone12-details-container {
        max-width: 630px;
    }
    .custom-width-iphone12-2 > div {
        max-width: 735px;
    }

    .iPhone12-container .pad-t-80-sm{
        padding-top: 80px;
    }
    .iPhone12-container .pad-b-40-sm{
        padding-bottom: 40px;
    }
    
    .iPhone12-container .pad-l-50-sm {
        padding-left: 50px;
    }
    .iPhone12-container .pad-l-15-sm {
        padding-left: 15px;
    }
    .iPhone12-container .pad-r-25-sm {
        padding-right: 25px;
    }
    .iPhone12-container .pad-r-40-sm {
        padding-right: 40px;
    }
    .iPhone12-container .pad-r-50-sm {
        padding-right: 50px;
    }
    .iPhone12-container .pad-r-70-sm {
        padding-right: 70px;
    }
    .iPhone12-container .pad-r-90-sm {
        padding-right: 90px;
    }
    .iPhone12-container .pad-v-30-sm {
        padding-top: 30px;
        padding-bottom: 30px;
    }
    .iPhone12-container .margin-v-0-sm {
        margin-top: 0;
        margin-bottom: 0;
    }
    .iPhone12-container .margin-v-25-sm {
        margin-top: 25px;
        margin-bottom: 25px;
    }
    .iPhone12-container .margin-t-40-sm{
        margin-top: 40px;
    }
    
    .iPhone12-container .margin-t-25-sm{
        margin-top: 25px;
    }
    .iPhone12-container .margin-b-50-sm {
        margin-bottom: 50px;
    }

    .apple-custom-header {
        font-size: 48px;
        line-height: 52px;
        text-align: left;
    }

    .apple-custom-intro {
        font-size: 21px;
        line-height: 29px;
    }
    
    .apple-custom-desc {
        font-size: 17px;
        line-height: 25px;
    }
    
    .iPhone12-container .txtSize17-sm {
        font-size: 17px;
    }

    .height-310-sm {height: 310px;}
    .max-height-380-sm {max-height: 380px;}
}

@media (min-width: 1000px){
        .img-hidden-md{
            display: none !important;
        }
        .img-block-md{
            display: block !important;
        }
        .iphone-se-compare-btn{
            width:7.4%;
            height:0.52%;
            bottom:9.25%;
            left:45.8%;
        }
    }
@media (min-width: 1069px) and (max-width: 1239px){
    .custom-width-iphone12-1 .iPhone12-details-container {
        max-width: 920px;
    }
}
@media (min-width: 1069px){
    
    .iPhone12-details-container {max-width: 960px;}
    
    .custom-width-iphone12-2 > div {
        max-width: 1070px;
    }
    
    .iPhone12-container .col-lg-5 {
        flex: 0 0 41.6666666667%;
        max-width: 41.6666666667%;
    }
    .iPhone12-container .col-lg-7 {
        flex: 0 0 58.3333333333%;
        max-width: 58.3333333333%;
    }
    .iPhone12-container .d-lg-none {
        display: none !important;
    }
    .iPhone12-container .d-lg-block {
        display: block !important;
    }

    .iPhone12-container .margin-t-45-lg{
        margin-top: 45px;
    }
    .iPhone12-container .margin-t-80-lg {
        margin-top: 80px;
    }
    .iPhone12-container .margin-v-80-lg {
        margin-top: 80px;
        margin-bottom: 80px;
    }
    .iPhone12-container .margin-b-25-lg{
        margin-bottom: 25px;
    }
    .iPhone12-container .margin-b-60-lg {
        margin-bottom: 60px;
    }
    
    .iPhone12-container .margin-b-95-lg {
        margin-bottom: 95px;
    }
    
    .iPhone12-container .pad-t-20-lg{
        padding-top: 20px;
    }

    .iPhone12-container .pad-h-20-lg {
        padding-left: 20px;
        padding-right: 20px;
    }
    
    .iPhone12-container .pad-h-25-lg {
        padding-left: 25px;
        padding-right: 25px;
    }
    
    .iPhone12-container .pad-h-30-lg {
        padding-left: 30px;
        padding-right: 30px;
    }
    
    .iPhone12-container .pad-h-55-lg{
        padding-left: 55px;
        padding-right: 55px;
    }

    .iPhone12-container .pad-l-40-lg{
        padding-left: 40px;
    }
    
    .iPhone12-container .pad-r-25-lg{
        padding-right: 25px;
    }
    
    .iPhone12-container .pad-r-35-lg{
        padding-right: 30px;
    }
    .iPhone12-container .pad-r-30-lg{
        padding-right: 30px;
    }
    .iPhone12-container .pad-r-45-lg{
        padding-right: 45px;
    }
    
    .iPhone12-container .pad-r-50-lg{
        padding-right: 50px;
    }
    
    .iPhone12-container .pad-r-60-lg{
        padding-right: 60px;
    }
    .iPhone12-container .pad-r-75-lg {
        padding-right: 75px;
    }
    .iPhone12-container .pad-r-120-lg {
        padding-right: 120px;
    }
    .iPhone12-container .pad-r-150-lg{
        padding-right: 150px;
    }
    
    .iPhone12-images-container {
        right: -155px;
    } 
    
    .iPhone-left-container { padding: 0;}

    .apple-custom-header {
        font-size: 80px;
        line-height: 84px;
        font-weight: 600;
        padding-right: 170px;
    }
    .apple-custom-caption {
        font-size: 40px;
        line-height: 44px;
        font-weight: 600;
    }
    .apple-custom-intro {
        font-size: 24px;
        line-height: 32px;
    }
    
    .apple-custom-headline {
        font-size: 40px;
        line-height: 44px;
        font-weight: 600;
    }

    .apple-40-headline-reduced {
        font-size: 40px;
        line-height: 44px;
        letter-spacing: 0em;
        font-weight: 600;
    }
    
    .apple-custom-desc {
        font-size: 21px;
        line-height: 25px;
    }
    .custom-17-body .apple-custom-desc {
        font-size: 17px;
        line-height: 21px;
        letter-spacing: normal;
        font-weight: 400;
    }
    
    .apple-custom-details {
        font-size: 17px;
        line-height: 21px;
    }

    .iPhone12-container.apple-typography-caption {
        font-size: 40px;
        font-weight: 600;
        letter-spacing: 0em;
        line-height: 44px;
    }

    .iPhone12-container .txtSize19-lg {
        font-size: 19px;
    }
    
    
    .iPhone12-container .txtSize40-lg {
        font-size: 40px;
    }
    .height-450-lg {height: 450px;}
    .max-height-472-lg {max-height: 472px;}
}

@media (min-width: 1240px) {

    .about-device-container {
        width: 1200px;
        padding-top: 80px !important;
        padding-bottom: 80px !important;
        padding-left:55px !important;
        padding-right:55px !important;
    }
    .about-device-container.iphone-se-container {
        padding: 0px !important;
        
    }

    .button-container {
        width: 65%;
    }

    .text-pad {
        padding-left: 90px !important;
        padding-right: 90px !important;
    }

    /*START CUSTOM Iphone-12 classes */
    .iPhone12-details-container {max-width: 980px;}
    
    .iPhone12-images-container {
        right: -75px;
    } 
    
    .iPhone12-container .d-xl-none {
        display: none !important;
    }
    .iPhone12-container .d-xl-block {
        display: block !important;
    }

    .iPhone12-container .margin-h-20-lg{
        margin-left: 20px;
        margin-right: 20px;
    }

    .iPhone12-container .pad-l-25-xl{
        padding-left: 25px;
    }
    
    .iPhone12-container .pad-r-5-xl {
        padding-right: 5px;
    }
    
    .iPhone12-container .pad-r-15-xl{
        padding-right: 15px;
    }
    
    .iPhone12-container .pad-r-70-xl{
        padding-right: 70px;
    }

    .iPhone12-container .pad-r-95-xl {
        padding-right: 95px;
    }    
    
    
    .max-height-460-xl {max-height: 460px;}

    
    /* Start compare iphone */
    .iphone-12-pro, .iphone-12 {
        width: 171px;
    }
    .iphone-12-mini {
        width: 154px;
    }
    /* End compare iphone */
    
    .apple-swatch {
        width: 22px;
        height: 22px;
    }

    /*END CUSTOM Iphone-12 classes */

}

/* START IPAD PRO 2018 */
width-525 {
    width: 525px;
}

.apple-typography {
    color: #6A6A6A;
    font-size: 30px;
    line-height: 1.0625em;
    letter-spacing: -.04em;
    font-family: "SF Pro Display","SF Pro Icons","Helvetica Neue","Helvetica","Arial","sans-serif" !important;
    font-weight: 600;
}

.apple-typography-2 {
    font-size: 24px !important;
    color: #000 !important;
}

.apple-typography-3 {
    font-size: 16px !important;
    color: #61CDF4 !important;
}

.apple-text-black-2 {
    color: #000;
}

.iPadPro2018-container .margin-r-auto {
    margin-left: 0 !important;
}

.border-top-black {
    border-top: 1px solid #000 !important;
}

.iPadPro2018-container .show-lg-only {
    display: none;
}

.iPadPro2018-container .show-xl-only {
    display: none;
}

/* MEDIA QUERY */
@media (max-width: 519px) {
    .iPadPro2018-container {
        max-width: 489px;
        margin-left: auto;
        margin-right: auto;
    }

    .iPadPro2018-container .margin-h-10-percent-xs {
        margin-left: 10%;
        margin-right: 10%;
    }

    .iPadPro2018-container .margin-r-45-xs {
        margin-right: 45px;
    }

    .iPadPro2018-container .no-pad-t-xs {
        padding-top: 0;
    }

    .iPadPro2018-container .pad-t-40-xs {
        padding-top: 40px;
    }

    .max-width-285-xs {
        max-width: 285px;
    }
}

@media (min-width: 520px) {
    .iPadPro2018-container .d-sm-block {
        display: block !important;
    }

    .iPadPro2018-container .d-sm-none {
        display: none !important;
    }
}

@media (min-width: 520px) and (max-width: 639px) {
    .iPadPro2018-container {
        width: 480px;
        margin-left: auto;
        margin-right: auto;
    }

    .iPadPro2018-container .iPadPro2018-details-container {
        max-width: 385px;
        margin-left: auto;
        margin-right: auto;
    }
}

@media (min-width: 640px) and (max-width: 999px) {
    .iPadPro2018-container {
        width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .iPadPro2018-container .iPadPro2018-details-container {
        max-width: 480px;
        margin-left: auto;
        margin-right: auto;
    }

    .iPadPro2018-container .pad-h-md-30 {
        padding-left: 30px;
        padding-right: 30px;
    }

    .iPadPro2018-container .pad-h-md-50 {
        padding-left: 50px;
        padding-right: 50px;
    }
}



@media (min-width: 1000px) {
    .iPadPro2018-container .d-lg-block {
        display: block !important;
    }

    .iPadPro2018-container .d-lg-none {
        display: none !important;
    }

    .iPadPro2018-container .show-lg-only {
        display: block;
    }

    .margin-l-20-percent-lg {
        margin-left: 20%;
    }
}

@media (min-width: 1000px) and (max-width: 1239px)
{
    .iPadPro2018-container {
        width: 960px;
        margin-left: auto;
        margin-right: auto;
    }

    .iPadPro2018-container .text-container {
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    .iPadPro2018-container .offer-tile-container {
        max-width: 768px;
        margin-left: auto;
        margin-right: auto;
    }
}

@media (min-width: 1240px) {
    .iPadPro2018-container {
        width: 1200px;
        margin-left: auto;
        margin-right: auto;
    }

    .iPadPro2018-container .text-container {
        max-width: 624px;
        margin-left: auto;
        margin-right: auto;
    }

    .iPadPro2018-container .offer-tile-container {
        max-width: 960px;
        margin-left: auto;
        margin-right: auto;
    }

    .iPadPro2018-container .show-xl-only {
        display: block;
    }

    .iPadPro2018-container .d-xl-block {
        display: block !important;
    }

    .iPadPro2018-container .d-xl-none {
        display: none !important;
    }

    .margin-l-27-percent-xl {
        margin-left: 27%;
    }
}

/* START END PRO 2018 */

/*START custom for iPad Pro 2021*/
.ipad-pro-21 .bgBlack .custom-container{
    color: #f5f5f7;
}
.ipad-pro-21 .custom-container{
    max-width: 280px;
    margin: auto;
}
.ipad-pro-21 .pad-h-10{padding-left:10px;padding-right:10px;}
.ipad-pro-21 .pad-h-50{padding-left:50px;padding-right:50px;}
.ipad-pro-21 .pad-t-30{padding-top:30px;}
.ipad-pro-21 .pad-b-80{padding-bottom:80px;}
.ipad-pro-21 .pad-b-90{padding-bottom:90px;}
.ipad-pro-21 .d-none{display: none!important;}
.ipad-pro-21 .margin-t-80{margin-top: 80px;}
.ipad-pro-21 .margin-b-10{margin-bottom: 10px;}
.ipad-pro-21 .margin-b-20{margin-bottom: 20px;}
.ipad-pro-21 .margin-b-25{margin-bottom: 25px;}
.ipad-pro-21 .margin-b-30{margin-bottom: 30px;}
.ipad-pro-21 .margin-b-35{margin-bottom: 35px;}
.ipad-pro-21 .margin-t-neg-20{margin-top: -20px;}
.ipad-pro-21 .margin-l-neg-10{margin-left: -10px;}
.ipad-pro-21 .margin-l-neg-30{margin-left: -30px;}
.ipad-pro-21 .margin-l-neg-80{margin-left: -80px;}
.ipad-pro-21 .margin-t-neg-100{margin-top: -100px;}
.ipad-pro-21 .margin-l-neg-180{margin-left: -180px;}
.ipad-pro-21 .margin-l-neg-205{margin-left: -205px;}
.ipad-pro-21 .margin-l-neg-270{margin-left: -270px;}
.ipad-pro-21 .width-15{width:15px;}
.ipad-pro-21 .width-36{width:36px;}
.ipad-pro-21 .width-40{width:40px;}
.ipad-pro-21 .width-43{width:43px;}
.ipad-pro-21 .width-45{width:45px;}
.ipad-pro-21 .width-60{width:60px;}
.ipad-pro-21 .width-63{width:63px;}
.ipad-pro-21 .width-70{width:70px;}
.ipad-pro-21 .width-105{width:105px;}
.ipad-pro-21 .width-125{width:125px;}
.ipad-pro-21 .width-320{width:320px;}
.ipad-pro-21 .width-330{width:330px;}
.ipad-pro-21 .width-340{width:340px;}
.ipad-pro-21 .width-415{width:415px;}
.ipad-pro-21 .width-490{width:490px;}
.ipad-pro-21 .width-510{width:510px;}
.ipad-pro-21 .width-650{width:650px;}
.ipad-pro-21 .width-50-percent{width: 50%;}
.ipad-pro-21 .height-43{height: 43px;}
.ipad-pro-21 .height-105{height: 105px;}
.ipad-pro-21 .height-120{height: 120px;}
.ipad-pro-21 .height-120{height: 120px;}
.ipad-pro-21 .height-135{height: 135px;}
.ipad-pro-21 .height-240{height: 240px;}
.ipad-pro-21 .height-280{height: 280px;}
.ipad-pro-21 .height-345{height: 345px;}
.ipad-pro-21 .bg-pink{background-color:#ecc5c1;}
.ipad-pro-21 .bg-green{background-color:#ccdfc9;}
.ipad-pro-21 .bg-blue{background-color:#cee3f6;}
.ipad-pro-21 .bg-dark-grey{background-color:#181818;}
.ipad-pro-21 .bg-dark-grey2{background-color:#68696d;}
.ipad-pro-21 .bg-light-grey{background-color:#f5f5f7;}
.ipad-pro-21 .bg-light-grey2{background-color:#e2e3e4;}
.ipad-pro-21 .border-b-grey{border-bottom:1px solid #d6d6d6;}
.ipad-pro-21 .color-inverted{color:#f5f5f7;}
.ipad-pro-21 .color-light-grey2{color:#6e6e73;}
.ipad-pro-21 .color-light-grey3{color:#86868b;}
.ipad-pro-21 .color-black{color:#1d1d1f;}
.ipad-pro-21 .typography-1{
    font-size: 19px;
    line-height: 23px;
    letter-spacing: 0.012rem;
    font-weight: 600;
}
.ipad-pro-21 .typography-2{
    font-size: 19px;
    line-height: 27px;
    font-weight: 400;
}
.ipad-pro-21 .typography-3{
    color: #86868b;
    font-size: 21px;
    line-height: 25px;
    letter-spacing: 0.011rem;
    font-weight: 600;
}
.ipad-pro-21 .typography-4{
    font-size: 32px;
    line-height: 36px;
    letter-spacing: -0.5px;
    font-weight: 600;
}
.ipad-pro-21 .typography-5{
    color: #a1a1a6;
    line-height: 20px;
}
.ipad-pro-21 .typography-6{
    font-size: 40px;
    line-height: 40px;
    font-weight: 600;
    background: linear-gradient(90deg, #EC3C5B 0%, #CC3DA8 44%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
.ipad-pro-21 .typography-7{
    color: #86868b;
    line-height: 20px;
}
.ipad-pro-21 .typography-8{
    font-size: 14px;
    line-height: 1.4285914286;
    font-weight: 600;
    letter-spacing: 0em;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", "Helvetica", "Arial", sans-serif, "SF Pro Icons";
}
.ipad-pro-21 .typography-9{
    font-size: 28px;
    line-height: 32px;
    letter-spacing: -0.5px;
    font-weight: 600;
}
.ipad-pro-21 .typography-11{
    font-size: 12px;
}
.ipad-pro-21 .img-h-centerView {
    margin-left: 50%;
    transform: translateX(-50%);
    min-width:0;
}
.ipad-pro-21 .device-color-option{
    width: 14px;
    height: 14px;
    border-radius: 50%;
}
.ipad-pro-21 .device-color-option:after{
    border-radius: inherit;
    box-shadow: inset 0 2px 1.5px rgb(0 0 0 / 10%);
    content: "";
    display: block;
    height: inherit;
    position: absolute;
    width: inherit;
}
.ipad-pro-21 .apple-btn{
    border-radius: 18px;
    font-size: 17px;
    line-height: 1.235;
    padding: 8px 17px 8px 17px;
}
/*sm*/
@media (min-width: 735px){
    .ipad-pro-21 .d-block-sm{display: block!important;}
    .ipad-pro-21 .d-none-sm{display: none!important;}
    .ipad-pro-21 .d-flex-sm{display: flex!important;}
    .ipad-pro-21 .position-absolute-sm{position: absolute;}
    .ipad-pro-21 .position-relative-sm{position: relative;}
    .ipad-pro-21 .right-0-sm{right: 0;}
    .ipad-pro-21 .pad-25-sm{padding: 25px;}
    .ipad-pro-21 .pad-h-0-sm{padding-left: 0px;padding-right: 0px;}
    .ipad-pro-21 .pad-h-20-sm{padding-left: 20px;padding-right: 20px;}
    .ipad-pro-21 .pad-h-25-sm{padding-left: 25px;padding-right: 25px;}
    .ipad-pro-21 .pad-h-60-sm{padding-left: 60px;padding-right: 60px;}
    .ipad-pro-21 .pad-v-20-sm{padding-top: 20px;padding-bottom: 20px;}
    .ipad-pro-21 .pad-v-25-sm{padding-top: 25px;padding-bottom: 25px;}
    .ipad-pro-21 .pad-v-40-sm{padding-top: 40px;padding-bottom: 40px;}
    .ipad-pro-21 .pad-v-50-sm{padding-top: 50px;padding-bottom: 50px;}
    .ipad-pro-21 .pad-t-50-sm{padding-top: 50px;}
    .ipad-pro-21 .pad-t-80-sm{padding-top: 80px;}
    .ipad-pro-21 .pad-t-165-sm{padding-top: 165px;}
    .ipad-pro-21 .pad-b-20-sm{padding-bottom: 20px;}
    .ipad-pro-21 .pad-b-90-sm{padding-bottom: 90px;}
    .ipad-pro-21 .pad-b-120-sm{padding-bottom: 120px;}
    .ipad-pro-21 .pad-b-125-sm{padding-bottom: 125px;}
    .ipad-pro-21 .pad-r-0-sm{padding-right: 0px;}
    .ipad-pro-21 .pad-r-15-sm{padding-right: 15px;}
    .ipad-pro-21 .pad-r-25-sm{padding-right: 25px;}
    .ipad-pro-21 .pad-r-30-sm{padding-right: 30px;}
    .ipad-pro-21 .pad-r-35-sm{padding-right: 35px;}
    .ipad-pro-21 .pad-r-55-sm{padding-right: 55px;}
    .ipad-pro-21 .pad-r-60-sm{padding-right: 60px;}
    .ipad-pro-21 .pad-l-0-sm{padding-left: 0px;}
    .ipad-pro-21 .pad-l-30-sm{padding-left: 30px;}
    .ipad-pro-21 .pad-l-40-sm{padding-left: 40px;}
    .ipad-pro-21 .margin-t-0-sm{margin-top: 0px;}
    .ipad-pro-21 .margin-t-45-sm{margin-top: 45px;}
    .ipad-pro-21 .margin-t-165-sm{margin-top: 165px;}
    .ipad-pro-21 .margin-r-25-sm{margin-right: 25px;}
    .ipad-pro-21 .margin-b-0-sm{margin-bottom: 0px;}
    .ipad-pro-21 .margin-b-10-sm{margin-bottom: 10px;}
    .ipad-pro-21 .margin-b-20-sm{margin-bottom: 20px;}
    .ipad-pro-21 .margin-b-25-sm{margin-bottom: 25px;}
    .ipad-pro-21 .margin-b-35-sm{margin-bottom: 35px;}
    .ipad-pro-21 .margin-b-40-sm{margin-bottom: 40px;}
    .ipad-pro-21 .margin-b-50-sm{margin-bottom: 50px;}
    .ipad-pro-21 .margin-b-65-sm{margin-bottom: 65px;}
    .ipad-pro-21 .margin-l-auto-sm{margin-left: auto;}
    .ipad-pro-21 .margin-l-neg-25-sm{margin-left: -25px;}
    .ipad-pro-21 .margin-l-neg-120-sm{margin-left: -120px;}
    .ipad-pro-21 .margin-l-neg-180-sm{margin-left: -180px;}
    .ipad-pro-21 .margin-l-neg-190-sm{margin-left: -190px;}
    .ipad-pro-21 .margin-l-neg-240-sm{margin-left: -240px;}
    .ipad-pro-21 .margin-t-neg-65-sm{margin-top: -65px;}
    .ipad-pro-21 .right-0-sm{right: 0;}
    .ipad-pro-21 .width-25-percent-sm{width: 25%;}
    .ipad-pro-21 .width-40-percent-sm{width: 40%;}
    .ipad-pro-21 .width-50-percent-sm{width: 50%;}
    .ipad-pro-21 .width-60-percent-sm{width: 60%;}
    .ipad-pro-21 .width-17-sm{width: 17px;}
    .ipad-pro-21 .width-40-sm{width: 40px;}
    .ipad-pro-21 .width-52-sm{width: 52px;}
    .ipad-pro-21 .width-120-sm{width: 120px;}
    .ipad-pro-21 .width-145-sm{width: 145px;}
    .ipad-pro-21 .width-400-sm{width: 400px;}
    .ipad-pro-21 .width-425-sm{width: 425px;}
    .ipad-pro-21 .width-475-sm{width: 475px;}
    .ipad-pro-21 .width-555-sm{width: 555px;}
    .ipad-pro-21 .width-630-sm{width: 630px;}
    .ipad-pro-21 .width-660-sm{width: 660px;}
    .ipad-pro-21 .width-780-sm{width: 780px;}
    .ipad-pro-21 .height-190-sm{height: 190px;}
    .ipad-pro-21 .height-205-sm{height: 205px;}
    .ipad-pro-21 .height-215-sm{height: 215px;}
    .ipad-pro-21 .height-350-sm{height: 350px;}
    .ipad-pro-21 .height-395-sm{height: 395px;}
    .ipad-pro-21 .height-430-sm{height: 430px;}
    .ipad-pro-21 .text-center-sm{text-align: center;}
    .ipad-pro-21 .txtSize48-sm{font-size: 48px;}

    .ipad-pro-21{
        margin-left: 15px;
        margin-right: 15px;
    }
    .ipad-pro-21 .typography-1{
        font-size: 28px;
        line-height: 32px;
        letter-spacing: 0.007rem;
    }
    .ipad-pro-21 .typography-2{
        font-size: 21px;
        line-height: 29px;
    }
    .ipad-pro-21 .typography-4{
        font-size: 40px;
        line-height: 44px;
    }
    .ipad-pro-21 .typography-8{
        font-size: 17px;
        line-height: 1.3529611765;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", "Helvetica", "Arial", sans-serif, "SF Pro Icons";
    }

    .ipad-pro-21 .typography-9{
        font-size: 32px;
    }
    .ipad-pro-21 .typography-11{
        font-size: 14px;
    }
    .ipad-pro-21 .custom-container{
        max-width: 692px;
        margin: auto;
    }
    .ipad-pro-21 .pad-15-between-sm>div:first-child{
        padding-right:7.5px;
    }
    .ipad-pro-21 .pad-15-between-sm>div:last-child{
        padding-left:7.5px;
    }
    .ipad-pro-21 .device-color-option{
        width: 20px;
        height: 20px;
    }
}
/*md*/
@media (min-width: 1069px){


    .ipad-pro-21 .d-none-md{display: none!important;}
    .ipad-pro-21 .d-block-md{display: block!important;}
    .ipad-pro-21 .no-pad-md{padding: 0px!important;}
    .ipad-pro-21 .pad-v-60-md{padding-top: 60px;padding-bottom: 60px;}
    .ipad-pro-21 .pad-t-75-md{padding-top: 75px;}
    .ipad-pro-21 .pad-t-90-md{padding-top: 90px;}
    .ipad-pro-21 .pad-t-180-md{padding-top: 180px;}
    .ipad-pro-21 .pad-r-0-md{padding-right: 0px;}
    .ipad-pro-21 .pad-h-30-md{padding-left: 30px;padding-right: 30px;}
    .ipad-pro-21 .pad-h-50-md{padding-left: 50px;padding-right: 50px;}
    .ipad-pro-21 .pad-r-30-md{padding-right: 30px;}
    .ipad-pro-21 .pad-r-25-md{padding-right: 25px;}
    .ipad-pro-21 .pad-r-35-md{padding-right: 35px;}
    .ipad-pro-21 .pad-r-80-md{padding-right: 80px;}
    .ipad-pro-21 .pad-l-0-md{padding-left: 0px;}
    .ipad-pro-21 .pad-l-80-md{padding-left: 80px;}
    .ipad-pro-21 .pad-l-55-md{padding-left: 55px;}
    .ipad-pro-21 .margin-t-30-md{margin-top: 30px;}
    .ipad-pro-21 .margin-b-15-md{margin-bottom: 15px;}
    .ipad-pro-21 .margin-b-30-md{margin-bottom: 30px;}
    .ipad-pro-21 .margin-b-40-md{margin-bottom: 40px;}
    .ipad-pro-21 .margin-b-55-md{margin-bottom: 55px;}
    .ipad-pro-21 .margin-b-60-md{margin-bottom: 60px;}
    .ipad-pro-21 .margin-b-90-md{margin-bottom: 90px;}
    .ipad-pro-21 .margin-l-145-md{margin-left: 145px;}
    .ipad-pro-21 .margin-l-neg-15-md{margin-left: -15px;}
    .ipad-pro-21 .margin-l-neg-65-md{margin-left: -65px;}
    .ipad-pro-21 .margin-l-neg-205-md{margin-left: -205px;}
    .ipad-pro-21 .width-470-md{width: 470px;}
    .ipad-pro-21 .width-495-md{width: 495px;}
    .ipad-pro-21 .width-560-md{width: 560px;}
    .ipad-pro-21 .width-610-md{width: 610px;}
    .ipad-pro-21 .width-830-md{width: 830px;}
    .ipad-pro-21 .width-870-md{width: 870px;}
    .ipad-pro-21 .width-40-percent-md{width: 40%;}
    .ipad-pro-21 .width-60-percent-md{width: 60%;}
    .ipad-pro-21 .width-100-percent-md{width: 100%;}
    .ipad-pro-21 .height-245-md{height: 245px;}
    .ipad-pro-21 .height-405-md{height: 405px;}
    .ipad-pro-21 .height-490-md{height: 490px;}
    .ipad-pro-21 .height-505-md{height: 505px;}
    .ipad-pro-21 .txtSize64-md{font-size: 64px;}
    .ipad-pro-21{
        margin-left: 38px;
        margin-right: 38px;
    }
    .ipad-pro-21 .custom-container{
        max-width:980px;
        margin:auto;
    }
    .ipad-pro-21 .typography-4{
        font-size: 48px;
        line-height: 57px;
    }
    .ipad-pro-21 .typography-6{
        font-size: 28px;
        line-height: 32px;
        letter-spacing: 0.007rem;
        background: linear-gradient(90deg, #EC3C5B 0%, #CC3DA8 44%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    .ipad-pro-21 .typography-9{
        font-size: 40px;
    }
    .ipad-pro-21 .typography-10{
        font-size: 17px;
        line-height: 25px;
        font-weight: 500;
    }
    .ipad-pro-21 .typography-11{
        font-size: 17px;
    }
}
/*xl*/
@media(min-width: 1240px){
    .ipad-pro-21{
        margin-left: 0;
        margin-right: 0;
    }
    .ipad-pro-21 .custom-container2{
        width: 1069px;
    }
}
/*END custom for iPad Pro 2021*/


/* For IE 11*/
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {

    .apple-txt-background-1,
    .apple-txt-background-2,
    .apple-txt-background-3,
    .apple-txt-background-4,
    .apple-txt-background-5 {
        background: none !important;
    }

    .apple-txt-typography {
        color: #fff !important;
    }
}
