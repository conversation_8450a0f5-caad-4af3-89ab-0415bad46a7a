﻿/*Version 07 April, 2021 2:13PM - Unimited share Bell*/



.custom-unlit-share-bell .right-addon .glyphicon, .right-addon .cal_custom {
    color: #00549a;
    top:-5px;
    font-size:26px;
}

.custom-unlit-share-bell .contentBox_notification {
    padding: 0 0 0 0;
}



.custom-unlit-share-bell .unli-shared-notification-grid .notificationPage_table_head div.shared-table-Cell:nth-of-type(1) {
    width:530px;
}

.custom-unlit-share-bell .unli-shared-notification-grid .notificationPage_table_head div.sharedPage_table-cell:nth-of-type(2) {
    width: 410px;
}

.bgBlueGraphBarLight  {
    background-color: #8CB8D6;
}


/*Override for Manage Features CR02*/

.custom-unlit-share-manage-feature-bell .product-img-wrap {
    margin-bottom:0;
}

.custom-unlit-share-manage-feature-bell .product-img-wrap .usageUnlimited .icon-unlimited:before {
    top:-2px;
}


/*info icon alignment in unlit share */
.custom-unlit-share-manage-feature-bell .usageWheelContainer .alignIconWithText::before {
    top: 0.1em !important;
}

.custom-unlit-share-manage-feature-bell .blueNoteTag {
    padding:0 10px;
}
.custom-unlit-share-manage-feature-bell .blueNoteTag .promo-label-sm {
    background-color: #003778;
    width: 70%;
    font-size: 9px;
    margin: 0px;
}

.custom-unlit-table-cell {
    margin-top: 21px;
    z-index: 999;
    position: fixed;
}

.custom-unlit-no-b-border {
    border-bottom: 0px !important;
}

.custom-unlit-border-light-gray {
    border: 1px solid #d1d1d1;
}

.tablist-pills-container.custom-unlit-tablist-pills ul li[aria-selected="true"] {
    background-color: #00549A;
    color: white;
}
/*For Tablet and Mobile*/
@media screen and (max-width: 991.98px) {
    .custom-unlit-share-manage-feature-bell .blueNoteTag .promo-label-sm {
        width: 100%;
        margin: 0px;
    }
    .margin-t-sm-2 {
        margin-top:2px;
    }

    .custom-unlit-table-cell {
        margin-top: 21px;
        z-index: 999;
        position: relative;
    }
 }


/*Rate plan share group*/

.share-group-current-plan .grayTagCurrent {
    width:100%;
    display:flex;
    background-color:#555;
    color:#fff;
    text-transform:uppercase;
    justify-content:center;
    font-size:9px;
    padding:6px 0;
}


.rate-plan-current-share-group {
    background: #FFF;
    height: auto;
    margin: 0 0 0 88px;
    border: 1px solid #D4D4D4;
    border-radius: 10px;
    padding: 20px 30px 5px 30px;
    width: 100%;
    /* max-width: 318px; */
    box-shadow: 0 6px 25px 0 rgb(0 0 0 / 12%);
}

.current-share-gray-tag {
    background-color: #555;
    color: #fff;
    text-transform: uppercase;
    justify-content: center;
    font-size: 9px;
    padding: 6px 0;
}

.rate-plan-current-share-group .current-share-title,
.rate-plan-current-share-group .current-share-group-content,
.rate-plan-current-share-group .rate-plan-current-share-icons {
    display: flex;
    justify-content: center;
}

.rate-plan-current-share-group .current-share-account {
    display: flex;
    border-top: 1px solid #E1E1E1;
    margin-top: 5px;
    padding-top: 10px;
    width:100%;
    justify-content:space-between;
    flex-direction:column;
}

    .rate-plan-current-share-group .current-share-account ul {
        display: flex;
        justify-content: space-between;
    }

.rate-plan-current-share-group .current-share-account-gb li:nth-of-type(2),
.rate-plan-current-share-group .current-share-account-name li:nth-of-type(2) {
    list-style-type: none;
}

.rate-plan-current-share-group .current-share-account ul li {
    flex-basis: 100%;
}

.rate-plan-current-share-group .current-share-group-content,
.rate-plan-current-share-group .data-desc {
    display: flex;
    flex-direction: column;
}

.rate-plan-current-share-group .rate-plan-current-share-icons {
    padding: 0px 44px;
    margin-top: 25px;
}


.rate-plan-current-share-group .rate-plan-current-share-icons-desc:first-of-type {
    margin-right: 54px;
}

.rate-plan-current-share-group .rate-plan-current-share-icons-desc .icon-unls  {
    font-size: 30px;
}

.rate-plan-current-share-group .rate-plan-current-share-icons-desc .data-desc {
    margin-top: 20px;
}

/* Share group*/


/* OVERRIDES for sharegroup*/
.rate-plan-current-share-group {
    padding:0px;
    margin: 0px 5px;
}

.rate-plan-current-share-group > .radio-card-content {
    padding: 30px 30px 5px 30px;
    height: 263px;
}

.rate-plan-current-share-group .rate-plan-current-share-icons {
    margin: 0px;
    padding: 0px;
}

.rate-plan-current-share-group .rate-plan-current-share-icons-desc:first-of-type {
    margin: 0px;
}

.rate-plan-current-share-group > .card-footer {
    min-height: 51px;
}

.brt10 {
    border-radius: 10px 10px 0px 0px;
}

.brt15 {
    border-radius: 15px 15px 0px 0px;
}

.cu-lb-sg-change .checkbox-card .graphical_ctrl_checkbox {
    padding-left: 25px;
}

.cu-lb-sg-change .checkbox-card {
    background: #FFF;
    height: auto;
    border: 1px solid #D4D4D4;
    border-radius: 10px;
    width: 100%;
    box-shadow: 0 6px 25px 0 rgba(0,0,0,0.12);
}

.radio-card-focus, .checkbox-card-focus {
    outline: 0!important;
    box-shadow:0 0 0 0px #003778, 0 0 0px 1px #003778, 0 0 0px 2px #003778, 0 0 0px 3px #003778 !important;
}

.adding-share-group .radio-card-focus, .checkbox-card-focus {
    outline: 0!important;
    box-shadow:0 0 0 0px #00549A, 0 0 0px 1px #00549A, 0 0 0px 2px #00549A, 0 0 0px 3px #00549A !important;
}

.adding-share-group .checkbox-card .specialCardTag {
    width: 110px;
    height: 20px;
    padding: 0px 5px;
    position: relative;
    border-radius: 3px;
}

.add-multiple-sg-scroll .modal-content{
    height:481px;
}

.add-multiple-sg-scroll .modal-body{
    height: 336px;
    overflow-y: auto;
    margin-top:10px;
}

.top-shadow-scrolld {
    box-shadow: 0 0 36px 0 rgb(0 0 0 / 30%) !important;
    z-index: 9999;
}

.modal-sharegroup .card-footer{
    border-top: none;
}


@media (min-width: 768px) {
    .cu-lb-sg-change {
        max-width: 645px;
        min-height: 610px;
    }

    .add-multiple-sg-scroll .cu-lb-sg-change {
        max-width: 645px;
        min-height: 481px;
    }

    .pdetails-modal {
        max-width: 645px;
    }

    .cu-lb-sg-change .radio-card {
        max-width: 280px;
        min-height: 317px;
    }

    .cu-lb-sg-change .modal-footer {
        justify-content: start;
    }

    .cu-lb-sg-change .checkbox-card {
        /* max-width: 185px;
        min-height: 192px; */
        margin: 8px;
        padding: 20px 30px
    }

    .adding-share-group .cu-lb-sg-change .checkbox-card {
        max-width: 585px;
        min-height: 100px;
        margin: 8px;
        padding: 20px 30px;
    }    
    
    #sg-moveMultiMembers {
        min-height: 528px;
    }

    #sg-moveMultiMembersreq {
        min-height:0;
    }

    .cu-lb-sg-change .checkbox-card {
        margin: 5px;
    }

    .checkbox-card .specialCardTag {
        min-width: 94px;
        width: auto;
        display: inline;
        height: 20px;
        padding: 0px 5px;
        position: relative;
        top:-30px;
        border-radius: 3px;
    }

    .cu-lb-sg-change .radio-card {
        margin-bottom:15px;
    }

    .modal-sharegroup .radio-card:nth-of-type(odd) {
        margin-left: 0px;
    }

    .modal-sharegroup .radio-card:nth-of-type(even) {
        margin-right: 0px;
    }
}


@media (max-width: 767px){
    .cu-lb-sg-change {
        min-width: 320px !important;
        min-height: 610px;
    }

    .cu-lb-sg-change  .radio-card {
        min-width: 284px;
        min-height: 317px;
        margin:8px;
        margin-left: 4px;
    }

    .cu-lb-sg-change .modal-footer {
        justify-content: start;
    }

    .sg-modal-container .modal {
        min-width: 320px;
    }

    .cu-lb-sg-change .checkbox-card {
        /* max-width: 290px; */
        min-height: 100px;
        margin: 15px 0px 5px;
        padding: 0px 30px 20px;
    }

    .checkbox-card-col {
        align-items: center !important;
        display: flex;
    }

    .cb-checkbox {
        display: flex;
        align-items: center;
        padding: 0px;
    }

    .cb-checkbox > label,
    .cb-checkbox > label > input,
    .cb-checkbox > label > span {
        width: 24px;
        height: 24px;
        margin: 5px 0px;
        padding: 0px;
    }

    #sg-moveMultiMembers .modal-dialog {
        min-height: 783px;
        height: auto;
        top: 80px;
        bottom: unset;
        display: flex;
    }

    #sg-moveMultiMembers .modalh-mod {
        height: auto !important;
    }

    #exampleModal .modalh-mod {
        height: auto !important;
    }


    #sg-moveMultiMembers .pdetails {
        min-height: 783px;
        height: auto;
        top: 50px;
        bottom: unset;
        display: flex;
    }

    #exampleModal .modal-header-gray,
    #sg-moveMultiMembers .modal-header-gray {
        min-height: 78px;
        padding: 15px;
    }

    #sg-moveMultiMembers .modal-footer {
        box-sizing: border-box;
        height: 75px;
        width: 320px;
        border: 1px solid #E1E1E1;
        background-color: #F4F4F4;
        border-radius: 0px;
    }

    #sg-moveMultiMembers .footer-mod{
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    
    .modal-body-scrollable .modal-content-a{
        height: auto !important;
    }

    .add-multiple-sg-scroll .modal-footer {
        box-sizing: border-box;
        height: 105px;
        width: 320px;
        border: 1px solid #E1E1E1;
        background-color: #F4F4F4;
        border-radius: 0px;
    }

    .checkbox-card .specialCardTag {
        width: max-content;
        height: 20px;
        padding: 0px 5px;
        position: relative;
        top:-10px;
        border-radius: 3px;
    }

    .margin-neg-20-t-xs {margin-top: -20px}

    .margin-neg-10-t-xs {margin-top: -10px}

    .pad-t-120{
        padding-top: 120px;
    }
    .modal-dialog .modal-footer{
        box-shadow: 0 0 36px 0 rgb(0 0 0 /30%);
    }

    .no-margin-m {
        margin:0;
        padding-top: 30px; 
        padding-bottom: 30px;
    }
    
}

.sg-info-cnt {
    width: auto;
    border-radius: 10px;
    background-color: #F4F4F4;
    padding: 30px 15px;
    margin: 0px;
}

.sg-info-header {
    color: #111111;
}

.sg-info-body {
    margin-bottom:0px;
}

/* HUG fix */
main.hug-main {
    overflow-y:hidden;
}
@media (max-width: 1000px) {
    .hug-main .tablist-pills-container ul li {
        font-size: 12px;
        font-family: Arial, "Helvetica Neue", Helvetica, sans-serif;
    }

    .hug-footer > .container.liquid-container {
        margin-left: 15px !important;
        margin-right: 15px !important;
    }
}

@media (min-width: 768px) and (max-width: 991.98px) {
    .hug-main .pad-h-sm-10 {
        padding-left: 10px !important;
        padding-right: 10px !important;
    }

    .hug-main .pad-h-sm-15 {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

    .hug-main .tablist-pills-container ul li {
        padding: 8px 20px !important;
    }

    .hug-main .hide-sm {
        display: none;
    }

    .hug-main .block-sm {
        display: block;
    }
}

@media (min-width: 320px) and (max-width: 767.98px) {
    .hug-footer > .container.liquid-container {
        margin-left: 0px !important;
        margin-right: 0px !important;
    }

    .hug-main .full-width-xs {
        max-width: 100% !important;
        width: 100% !important;
    }
    .main-hug-body .device-details-header .hidden-sm {
        display: none !important;
    }

    .modal-sharegroup .ctrl_radioBtn .ctrl_element {
        top: -3px;
    }
}

.tablist-pills-slider-container .tab-slider-pill:focus {
    outline: 0 !important;
    box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5FB0FC, 0 0 2px 5px #8EC6FC !important;
}

.hug-main a.txtUnderline:hover {
    text-decoration:none;
}

.hug-main .promo-label-grey {
    font-size: 10px;
    color: #FFFFFF;
    background-color: #555;
    text-transform: uppercase;
    height: 20px;
    line-height: 20px;
    vertical-align: middle;
    padding-top: 0px;
    padding-bottom: 0px;
    display: inline-block;
    margin-left: 5px;
}

.hug-main .rateplan-sharegroup-cards > .sharegroup-cards-inner .sharegroup-cards-inner-label {
    top: -4px;
    border-radius: 2px;
}

/*Override for the icons in notifcation usage card*/
.main-mobility-overview #UsageOverviewPanel .icon {
    background-image: none !important;
}

/*Sharegroup flow overrides*/
/*Modal Overrides in sharegroup*/


@media screen and (max-width: 767.98px) {
    .modal-body-scrollable .modal-dialog {
        min-height: 100%;
        display: flex;
        align-items: center;
    }

    .full-height-modals .modal-body-scrollable .modal-body,
    .modal-body-scrollable .modal-body {
        overflow-y:auto;
        overflow-x:hidden;
        height: 90%;
        margin-bottom: 0px;
        align-items: center;
    }


    .modal-body-scrollable .modal-content {
        height: 100vh;
    }

        .modal-body-scrollable .card-group,
        .full-height-modals.modal {
            align-items: center;
        }

    .modal-body-scrollable .modal-body .card-group {
        padding-bottom:20px;
    }

    .modal-body-scrollable .modal-footer {
        padding: 20px 30px;
        position: sticky;
        background-color: #f0f0f0;
    }

    .modal-body-scrollable.modal,
    .full-height-modals.modal{
        width: 100%;
        min-width: 100% !important;
    }

    .modal-body-scrollable .modal-footer.top-shadow-scroll {
        -webkit-box-shadow: 0 0 36px 0 rgb(0 0 0 / 30%) !important;
        -moz-box-shadow: 0 0 36px 0 rgb(0 0 0 / 30%) !important;
        box-shadow: 0 0 36px 0 rgb(0 0 0 / 30%) !important;
    }


    /*Ovverride for full width and height modal*/


    .full-height-modals .modal .modal-dialog {
        height: 100vh!important;
        min-height: 100vh!important;
        top: 0!important;
    }

    .full-height-modals .modal {
        width: 100%;
        min-width: 100% !important
    }


    .full-height-modals .modal .card-group {
        display:flex;
        align-items:center;
    }

    .full-height-modals .modal .modal-dialog .modal-footer {
        min-width:100%!important;
    }

}   


.modal-body-scrollable .modal-dialog .close,
.full-height-modals .modal .modal-dialog .close {
    position: relative;
    top: 10px;
    right: 5px;
}

        /*.full-height-modals .modal .scrollable-modal-content {
            overflow-y: auto;
            overflow-x: hidden;
            height: 90%;
            margin-bottom: 0px;
            align-items: center;
        }

        .full-height-modals .modal .scrollable-container-modal .radio-card {
            min-height:337px;
        }*/

.full-height-modals .modal .modal-dialog .modal-footer {
    padding: 20px 30px!important;
}



.full-height-modals .modal .modal-dialog .modal-footer {
    padding:45px 30px;
}

.add-multiple-sg-scroll .modal .modal-dialog .modal-footer {
    padding:0px 0px;
}


.full-height-modals .modal .modal-dialog .modal-body {
    margin-bottom:0px;
    overflow-y:visible!important;
}

.full-height-modals .modal .sg-info-cnt {
    padding: 30px 30px;
}

@media (min-width:320px) and (max-width:464px) {
    .modal-body-scrollable .modal-title,
    .full-height-modals .modal .modal-title {
        max-width: 280px!important;
    }
}

@media (min-width:465px) and (max-width:767.98px) {

    .modal-body-scrollable .modal-title,
    .full-height-modals .modal .modal-title {
        max-width: 300px;
    }
}


/*Slick overrides buttons*/
.sharegroup-flow-override .infoblock-slider-t .slick-next:hover,
.sharegroup-flow-override .infoblock-slider-t .slick-prev:hover,
.sharegroup-flow-override .infoblock-slider-t .slick-next:focus,
.sharegroup-flow-override .infoblock-slider-t .slick-prev:focus,
.sharegroup-flow-override .infoblock-slider-t .slick-prev:active,
.sharegroup-flow-override .infoblock-slider-t .slick-next:active {
    background-color: #fff;
}

.sharegroup-flow-override .infoblock-slider-t .slick-next:before,
.sharegroup-flow-override .infoblock-slider-t .slick-prev:before {
    color: #0e5ba1;
}

.sharegroup-flow-override .infoblock-slider-ts .slick-next:hover,
.sharegroup-flow-override .infoblock-slider-ts .slick-prev:hover,
.sharegroup-flow-override .infoblock-slider-ts .slick-next:focus,
.sharegroup-flow-override .infoblock-slider-ts .slick-prev:focus,
.sharegroup-flow-override .infoblock-slider-ts .slick-prev:active,
.sharegroup-flow-override .infoblock-slider-ts .slick-next:active {
    background-color: #fff;
}

.sharegroup-flow-override .infoblock-slider-ts .slick-next:before,
.sharegroup-flow-override .infoblock-slider-ts .slick-prev:before {
    color: #0e5ba1;
}


.lnkUnderline,
.lnkNoUnderline:hover {
    text-decoration: underline;
}

.lnkNoUnderline,
.lnkUnderline:hover {
    text-decoration: none;
}

.cd-sg-add-single-lb .card-footer,
.cd-sg-add-multi-lb1 .card-footer,
.cd-sg-1b1 .card-footer {
    padding: 15px 30px;
}

.cd-sg-add-single-lb .graphical_ctrl input,
.cd-sg-add-multi-lb1 .graphical_ctrl input,
.cd-sg-1b1 .graphical_ctrl input {
    width: 24px;
    height: 24px;
}

.cd-sg-add-single-lb .ctrl_element,
.cd-sg-add-multi-lb1 .ctrl_element,
.cd-sg-1b1 .ctrl_element {
    left: 11px;
}

.cd-sg-add-single-lb .rate-plan-current-share-group > .radio-card-content,
.cd-sg-add-multi-lb1 .rate-plan-current-share-group > .radio-card-content,
.cd-sg-1b1 .rate-plan-current-share-group > .radio-card-content {
    padding: 30px 29px;
}

.cd-sg-add-single-lb p[class="data-desc"],
.cd-sg-add-multi-lb1 p[class="data-desc"],
.cd-sg-1b1 p[class="data-desc"] {
    margin-bottom: 0px;
}

.cd-sg-1d2 .checkbox-card-focus,
.cd-sg-add-multi-lb2 .checkbox-card-focus {
    box-shadow: 0 0 0 0px #00549A, 0 0 0px 1px #00549A, 0 0 0px 2px #00549A, 0 0 0px 3px #00549A !important;
}

.cd-sg-details-expanded-lb hr {
    margin: 15px 0px;
}


/*Override for french in Hug main*/

:lang(fr) .hug-main .tablist-pills-slider-container > div {
    margin-right: 30px;
}

:lang(fr) .hug-main .tablist-pills-slider-container > div:last-child {
    margin-right: 0px;
}

.hug-main .tablist-pills-slider-container > div > div.spacer30 {
    height: 0;
}

@media (min-width:768px) and (max-width:1035px) {
    :lang(fr) .hug-main .tablist-pills-slider-container {
        padding: 0 0px;
    }

        :lang(fr) .hug-main .tablist-pills-slider-container > div {
            margin-right: 30px;
        }

            :lang(fr) .hug-main .tablist-pills-slider-container > div:last-child {
                margin-right: 0px;
            }
}


@media screen and (max-width:767.98px) {
    :lang(fr) .hug-main .tablist-pills-slider-container > div {
        margin-right: 0px;
    }

        :lang(fr) .hug-main .tablist-pills-slider-container > div:last-child {
            margin-right: 0px;
        }

    .hug-main .max-width-rate-plans > label {
        width: 100%;
    }
}


.header-preferences {
    margin-left: 0px;
}

.nav-item {
    cursor:pointer;
}

.select-hover-dropdown {
    display: none;
    position: absolute;
    top: 30px;
    width: 250px;
    z-index: 9999;
    padding-top: 23px;
}

.select-hover-dropdown ul {
    display: block;
    padding:0px;
}

.select-hover-dropdown ul > li {
    display: list-item;
    vertical-align:middle;
    white-space: nowrap;
    list-style-type:none;
    width: 100%;
    height: 60px;
}

.select-hover-dropdown a.services-selection {
    position:relative;
    padding: 10px;
    color: #555;
    border-bottom: 1px solid #d4d4d4;
    margin-right: 0;
    height: 60px;
    width: 100%;
    box-sizing:border-box;
    display:block;
}

.select-hover-dropdown a.services-selection:after {
    font-family: 'bell-icon';
    content: "\e012";
    color: #00549a;
    font-size: 17px;
    font-style: normal;
    speak: none;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    right: 8px;
    opacity: 1;
}

.select-hover-dropdown a.services-selection.active .my-service, .select-hover-dropdown a.services-selection.active .my-service:hover {
    color: #fff;
}

.select-hover-dropdown a.services-selection:hover {
    background-color: #f4f4f4;
    color: #555;
    text-decoration: none;
}

.select-hover-dropdown a.services-selection.active {
    color: #c2cedf;
    background-color: #00549a;
    text-decoration: none;
}

.select-hover-dropdown a > .icon-pos {
    position: absolute;
    left: 10px;
    top: 8px;
    color: #00549a;
    text-align: center;
    width: 40px;
    display: table;
    height: 100%;
    font-size: 18px;
    line-height:1;
}

.select-hover-dropdown a > .icon-pos > i.icon-o{
    font-style: normal;
    speak: none;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
}

.select-hover-dropdown a > .icon-pos > .icon_mobility-menu:before {
    content: "\e904";
}

.icon-pos.gn-menu i.icon-unls {
    font-size: 32px;
}

.icon-pos.gn-menu i.icon-unls.icon-group_bl_wot {
    font-size: 28px;
}

a.login-register-button, a.log-out-button-menu {
    color: #fff;
}

a.login-register-button:hover, a.log-out-button-menu:hover {
    color: #c2cedf;
}

.page-sharegroup .global-navigation.gn-shop .connector-login-modal {
    padding:0px;
}
.page-sharegroup .connector-login-modal.user-control-menu a {
    height: 55px;
}

.modal-sharegroup .adjustheight{
    height:80px;
}


.modal-dialog > .modal-content .scrollAdjust::-webkit-scrollbar{
    width:5px;
}

.modal-dialog .scrollAdjust{

    /* margin-top: 20px; */
    margin-right: 3px;
    overflow-y: auto;
    margin-left: 5px;
    padding-top: 0px !important;
}

.no-border {
    border:0;
}



.modal-xs-r{
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.round-img-container {
    border-radius: 50%;
    width: 60px;
    height: 60px;
    background-color: #f4f4f4;
    display: flex;
    justify-content: center;
    align-items: center;
}
 .round-img-container.diam-80{
    width:80px;
    height:80px;
    display:flex;
    justify-content:center;
    align-items:center;
}


 /*Specific target for round image container in specific component inside the modal only*/
@media screen and (max-width:767.98px) {

    .modal-sharegroup .move-sg-modal-round-img-container {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        background-color: #f4f4f4;
        flex-basis:22%;
    }

    .cmp-slick .slick-track {
        transition: transform 500ms ease 0s;
    }
}

@media (min-width:768px) {
    .modal-sharegroup .move-sg-modal-round-img-container {
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        background-color: #f4f4f4;
    }
}
