import * as React from "react";
import { InjectedIntlProps, injectIntl } from "react-intl";
import { P<PERSON><PERSON>eader, PBEFooter } from "singleban-components";
import { IPBE } from "../../models";
import { CURRENCY_OPTIONS, modalOpenedOmniture } from "../../utils/Utility";

interface Props {
    pbe: IPBE;
    isPBEModalLinkDisabled: boolean;
}

const PBEHomePhoneLongDistance = (props: Props & InjectedIntlProps) => {
    const { intl: { formatMessage, formatNumber }, pbe } = props;
    const title = formatMessage({ id: "PBE_HOME_PHONE_LONG_DISTANCE_TITLE" });
    const description = formatMessage({ id: "PBE_HOME_PHONE_LONG_DISTANCE_DESC" }, {
        numMinutes: pbe?.pbeDataBag?.totalCallDuration,
        charge: formatNumber(pbe?.pbeDataBag?.totalCharge, CURRENCY_OPTIONS)
    });
    const imageClassName = "icon-05_long_distance_circle";
    const PBEFooterItems = [(pbe?.pbeDataBag?.showUsageLink ? {
        ctaLink: formatMessage({ id: "HP_LONG_DISTANCE_USAGE_LINK" }, {
            acctNo: pbe?.pbeDataBag?.encryptedIdentifier,
            subNo: pbe?.pbeDataBag?.encryptedIdentifier,
            ban: pbe?.pbeDataBag?.encryptedBan
        }),
        iconClassName: "icon-10_usage",
        titleKey: formatMessage({ id: "HP_LONG_DISTANCE_FOOTER_TITLE1" }),
        ctaTitleKey: formatMessage({ id: "HP_LONG_DISTANCE_FOOTER_SUBTITLE1" }),
        isFirstRow: true,
        id: "pbe-hp-long-distance-usage"
    } : null), {
        ctaLink: formatMessage({ id: "HP_LONG_DISTANCE_ADD_LONG_DISTANCE_LINK" }, {
            acctNo: pbe?.pbeDataBag?.encryptedIdentifier,
            subNo: pbe?.pbeDataBag?.identifier,
        }),
        iconClassName: "icon-17_world",
        titleKey: formatMessage({ id: "HP_LONG_DISTANCE_FOOTER_TITLE2" }),
        ctaTitleKey: formatMessage({ id: "HP_LONG_DISTANCE_FOOTER_SUBTITLE2" }),
        isFirstRow: false,
        id: "pbe-hp-long-distance-upgrade"
    }];
    React.useEffect(() => {
        modalOpenedOmniture(props?.pbe?.triggerPbeOmniture, title, description);
    },[title, description]);
    return (
        <>
            <PBEHeader descriptionKey={description} titleKey={title} iconClassName={imageClassName} />
            <PBEFooter footerItems={PBEFooterItems} isPBEModalLinkDisabled={props.isPBEModalLinkDisabled}/>
        </>
    );
};


export default (injectIntl(PBEHomePhoneLongDistance));