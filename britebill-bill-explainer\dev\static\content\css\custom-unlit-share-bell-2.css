/*Version 07 Dec, 2020 1:47PM - Unimited share Bell*/

.custom-unlit-share-bell .right-addon .glyphicon, .right-addon .cal_custom {
    color: #00549a;
    top: -5px;
    font-size: 26px;
}

.custom-unlit-share-bell .contentBox_notification {
    padding: 0 0 0 0;
}



.custom-unlit-share-bell .unli-shared-notification-grid .notificationPage_table_head div.shared-table-Cell:nth-of-type(1) {
    width: 530px;
}

.custom-unlit-share-bell .unli-shared-notification-grid .notificationPage_table_head div.sharedPage_table-cell:nth-of-type(2) {
    width: 410px;
}

.bgBlueGraphBarLight {
    background-color: #8CB8D6;
}


/*Override for Manage Features CR02*/

.custom-unlit-share-manage-feature-bell .product-img-wrap {
    margin-bottom: 0;
}

    .custom-unlit-share-manage-feature-bell .product-img-wrap .usageUnlimited .icon-unlimited:before {
        top: -2px;
    }


/*info icon alignment in unlit share */
.custom-unlit-share-manage-feature-bell .usageWheelContainer .alignIconWithText::before {
    top: 0.1em !important;
}

.custom-unlit-share-manage-feature-bell .blueNoteTag {
    padding: 0 10px;
}

    .custom-unlit-share-manage-feature-bell .blueNoteTag .promo-label-sm {
        background-color: #003778;
        width: 70%;
        font-size: 9px;
        margin: 0px;
    }

.custom-unlit-table-cell {
    margin-top: 21px;
    z-index: 999;
    position: fixed;
}

.custom-unlit-no-b-border {
    border-bottom: 0px !important;
}

.custom-unlit-border-light-gray {
    border: 1px solid #d1d1d1;
}
/*For Tablet and Mobile*/
@media screen and (max-width: 991.98px) {
    .custom-unlit-share-manage-feature-bell .blueNoteTag .promo-label-sm {
        width: 100%;
        margin: 0px;
    }

    .margin-t-sm-2 {
        margin-top: 2px;
    }

    .custom-unlit-table-cell {
        margin-top: 21px;
        z-index: 999;
        position: relative;
    }

    .main-mobility-overview .donutGraph .chart-label-center-blueLight {
        font-size: 0.18em;
    }
}


/*Rate plan share group*/
.share-group-current-plan .grayTagCurrent {
    width: 100%;
    display: flex;
    background-color: #555;
    color: #fff;
    text-transform: uppercase;
    justify-content: center;
    font-size: 9px;
    padding: 6px 0;
}


.rate-plan-current-share-group {
    background: #FFF;
    height: auto;
    margin: 0 0 0 88px;
    border: 1px solid #D4D4D4;
    border-radius: 10px;
    padding: 20px 30px 5px 30px;
    width: 100%;
    max-width: 318px;
}

.current-share-gray-tag {
    background-color: #555;
    color: #fff;
    text-transform: uppercase;
    justify-content: center;
    font-size: 9px;
    padding: 6px 0;
}

.rate-plan-current-share-group .current-share-title,
.rate-plan-current-share-group .current-share-group-content,
.rate-plan-current-share-group .rate-plan-current-share-icons {
    display: flex;
    justify-content: center;
}

.rate-plan-current-share-group .current-share-account {
    display: flex;
    border-top: 1px solid #E1E1E1;
    margin-top: 5px;
    padding-top: 10px;
    width: 100%;
    justify-content: space-between;
    flex-direction: column;
}

    .rate-plan-current-share-group .current-share-account ul {
        display: flex;
        justify-content: space-between;
    }

.rate-plan-current-share-group .current-share-account-gb li:nth-of-type(2),
.rate-plan-current-share-group .current-share-account-name li:nth-of-type(2) {
    list-style-type: none;
}

.rate-plan-current-share-group .current-share-account ul li {
    flex-basis: 100%;
}

.rate-plan-current-share-group .current-share-group-content,
.rate-plan-current-share-group .data-desc {
    display: flex;
    flex-direction: column;
}

.rate-plan-current-share-group .rate-plan-current-share-icons {
    padding: 0px 44px;
    margin-top: 25px;
}


.rate-plan-current-share-group .rate-plan-current-share-icons-desc:first-of-type {
    margin-right: 54px;
}

.rate-plan-current-share-group .rate-plan-current-share-icons-desc .icon-unls {
    font-size: 30px;
}

.rate-plan-current-share-group .rate-plan-current-share-icons-desc .data-desc {
    margin-top: 20px;
}



.rate-plan-current-share-group .rate-plan-account-container {
    width: 100%;
    flex-direction: column;
    display: flex;
    padding: 0 20px;
}

    .rate-plan-current-share-group .rate-plan-account-container .rate-plan-account {
        width: 100%;
    }

        .rate-plan-current-share-group .rate-plan-account-container .rate-plan-account p {
            width: 50%;
        }

/* For filter tab pills*/
.tablist-pill-slider-container {
    display: flex;
    font-size: 16px;
    justify-content: center;
    flex-flow: wrap;
}

    .tablist-pill-slider-container .slider-tablist {
        background-color: #f4f4f4;
        border: 1px solid #fff;
        border-radius: 30px;
        display: flex;
        flex-direction: row;
        padding: 5px;
        list-style: none;
        margin: 0;
        color: #00549a;
        text-align: center
    }


    .tablist-pill-slider-container .tablist-pill-with-title {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 30px;
    }

        .tablist-pill-slider-container .tablist-pill-with-title:nth-of-type(3) {
            margin-right: 0px;
        }

    .tablist-pill-slider-container .slider-tablist .tab-slider-pill {
        padding: 7px 36px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center
    }

        .tablist-pill-slider-container .slider-tablist .tab-slider-pill[aria-selected=true] {
            border: 1px solid #e1e1e1;
            border-radius: 30px;
            background-color: #fff;
            box-shadow: 0 0 25px 0 rgba(0,0,0,.12);
            color: #111
        }


    .tablist-pill-slider-container.tablist-pills-container-blue .slider-tablist {
        padding: 3px;
    }

        .tablist-pill-slider-container.tablist-pills-container-blue .slider-tablist .tab-slider-pill[aria-selected=true] {
            background-color: #00549A;
            color: #fff;
        }

/*For Mobile view*/
@media (max-width: 767.98px) {
    .tablist-pill-slider-container .slider-tablist .tab-slider-pill {
        padding: 10px 20px;
    }

    .tab-control .header-tab-control .slider-tablist .tab-slider-pill:not(:last-child) {
        margin-right: 20px;
    }

    .tablist-pill-slider-container .slider-tablist .tab-slider-pill {
        font-size: 12px;
    }

    .tablist-pill-slider-container {
        display: flex;
        justify-content: center;
        flex-direction: column;
    }

        .tablist-pill-slider-container .tablist-pill-with-title {
            margin-right: 0;
            margin-bottom: 20px;
        }

    .rate-plan-current-share-group {
        margin: 0 0 0 22px;
        width: 100%;
        max-width: 287px;
        padding: 20px 10px 5px;
    }

    .sharegroup-rate-plan-alert {
        flex-direction: column;
        align-items: flex-start !important;
    }

}


/*For Tablet View*/

@media (min-width:768px) and (max-width:991px) {
    .sharegroup-rate-plan-alert.rate-plan .content-width {
        width: 100%;
    }
}

/*CUSTOM breakpoint for Tablist pill only*/
@media (min-width:767.98px) and (max-width:1063.98px) {
    .tablist-pill-slider-container .slider-tablist .tab-slider-pill {
        font-size: 12px;
    }
}

@media (min-width:767.98px) {
    .tablist-pill-slider-container .slider-tablist .tab-slider-pill {
        font-size: 12px;
    }
}


@media (min-width:767.98px) and (max-width: 845px) {
    .tablist-pill-slider-container .slider-tablist .tab-slider-pill {
        padding: 5px 20px;
    }
}

@media (min-width:845px) and (max-width: 920px) {
    .tablist-pill-slider-container .slider-tablist .tab-slider-pill {
        padding: 5px 30px;
    }
}

@media (min-width:1064px) {
    .tablist-pill-slider-container .slider-tablist .tab-slider-pill {
        font-size: 16px;
    }
}


.custom-unlit-share-manage-feature-bell a.icon.more-link:after {
    top: 0.2rem !important;
}

.custom-unlit-share .fixed-footer > .container > .inlineBlock:nth-last-of-type(3) {
    transform: translateY(0%);
}

.custom-unlit-share .donutCarretWarn .warnDesc p {
    margin-bottom: 0px;
}

/*Alert current rate-plan*/

.sharegroup-rate-plan-alert {
    display: flex;
    align-items: center;
}

    /* .sharegroup-rate-plan-alert .icon-alert-circled { margin-right:10px;} */

    /* fix */
    .sharegroup-rate-plan-alert .icon2:before {
        top: -5px !important;
    }

    .sharegroup-rate-plan-alert.-with-note {
        display: flex;
        align-items: flex-start;
    }

        .sharegroup-rate-plan-alert.-with-note .select-rate-plan-note {
            margin-top: 15px;
        }

            .sharegroup-rate-plan-alert.-with-note .select-rate-plan-note li {
                margin-bottom: 10px;
            }



/* rate plan*/

@media screen and (max-width:999px) {

    .rateplan-settings .pad-15-left-right-sm {
        padding-left: 15px;
        padding-right: 15px;
    }


    .rateplan-sharegroup-cards > .sharegroup-cards-inner {
        width: 32.22% !important;
    }

    .rateplan-sharegroup-cards > .sharegroup-cards-inner2 {
        padding-left: 0px !important;
        padding-right: 0px !important;
    }


    .sharegroup-cards-today-info {
        height: 128px;
    }

    .rateplan-sharegroup-cards > .sharegroup-cards-2cols-width {
        width: 49.2% !important;
    }

    .rateplan-sharegroup-cards .pad-lr-30-sm {
        padding-left: 30px;
        padding-right: 30px;
    }

    .rateplan-sharegroup-cards .sharegroup-cards-3cols-width .sharegroup-cards-bottom-info {
        max-width: 185px !important;
        width: 100%;
    }

    .rateplan-sharegroup-cards .txtCenter-sm {
        text-align: center;
    }

    .rateplan-sharegroup-cards .same-height-220-d-260-sm-auto-xs {
        min-height: 260px !important;
    }

    .sharegroup-cards-3cols-width .current-share-title {
        max-width: 140px;
        width: 100%;
    }

    .sharegroup-cards-3cols-width .pad-h-sm-10 {
        padding-left: 10px;
        padding-right: 10px
    }
}

@media screen and (max-width:767px) {

    .hideBR-xs {
        display: none;
    }

    .pad-T20-B10-xs {
        padding-bottom: 10px;
        padding-top: 20px
    }

    .margin-top-10-xs {
        margin-top: 10px;
    }

    .rateplan-bill-radio-cell {
        display: block !important;
        width: 100% !important;
        max-width: 100% !important;
        margin-bottom: 10px;
    }

    .rateplan-sharegroup-cards {
        flex-direction: column;
    }

        .rateplan-sharegroup-cards > .sharegroup-cards-inner {
            max-width: 100% !important;
            width: 100% !important;
            margin-bottom: 15px !important;
        }

        .rateplan-sharegroup-cards > .sharegroup-cards-inner {
            margin-bottom: 30px !important;
        }

        .rateplan-sharegroup-cards > .sharegroup-cards-inner2 {
            padding-left: 0px !important;
            padding-right: 0px !important;
        }

    .pad-top-bot-sharegroup-card {
        padding-bottom: 15px !important;
    }

    .width-xs-100-sharegroup-card, .sharegroup-cards-3cols-width .current-share-title {
        width: 100% !important;
        max-width: 100%;
    }

    .rateplan-sharegroup-cards > .sharegroup-cards-inner .current-share-title {
        width: 100%;
    }

    .sharegroup-cards-today-info, .rateplan-sharegroup-cards .same-height-220-d-260-sm-auto-xs {
        height: auto !important;
        min-height: auto !important;
    }

    .rateplan-sharegroup-cards .sharegroup-cards-bottom-info, .sharegroup-cards-3cols-width .sharegroup-cards-icons-container, .rateplan-sharegroup-cards .top-bot-col .sharegroup-cards-bottom-info {
        max-width: 220px !important;
        width: 100% !important;
    }

    .rateplan-sharegroup-cards .autoheight-xs {
        height: auto !important;
        min-height: auto !important;
    }

    .rateplan-settings .pad-30-left-right-xs {
        padding-left: 30px;
        padding-right: 30px;
    }
}

@media screen and (max-width:355px) {
    .maxwidth960 li.plan-details-item, .maxwidth960 .rate-plan-include-feature {
        padding-right: 0px !important;
        padding-left: 0px !important;
    }
}

.custom-unlit-share .lineHeight1p3 {
    line-height: 1.3;
}

.custom-unlit-share .lineHeight1p2 {
    line-height: 1.2;
}


.maxwidth960 .rate-plan-include-feature {
    padding: 0 25px;
}

/* adding a standalone centralized carret */
.carretCenter {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 15px 15px 0 15px;
    border-color: #e1e1e1 transparent transparent transparent;
    margin: 0 auto;
}

.view-sharegroup-rate-plan .grayBorder {
    display: table;
    width: 100%;
    border: 1px solid #D4D4D4;
    background: #fff;
    box-shadow: 0 2px 3px 0 rgba(0,0,0,0.1);
}



​
.view-sharegroup-rate-plan .grayBorder {
    display: table;
    width: 100%;
    border: 1px solid #D4D4D4;
    background: #fff;
    box-shadow: 0 2px 3px 0 rgba(0,0,0,0.1);
}



/*For Focus*/
.tablist-pill-slider-container .tab-slider-pill:focus {
    outline: 0 !important;
    box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5FB0FC, 0 0 2px 5px #8EC6FC !important;
}

body.is_tabbing *:focus {
    outline: 0 !important;
    box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5FB0FC, 0 0 2px 5px #8EC6FC;
}

/* change rate plan - settings */
.rateplan-bill-radio-cell {
    max-width: 290px;
    margin-right: 30px;
    display: inline-block;
    padding: 20px 15px;
}

.rateplan-bill-radio-input {
    display: block;
}

.arialFont {
    font-family: Arial, Helvetica, sans-serif;
}

.darkBlue3 {
    color: #003778;
}


/*Override for flex 2 column 3 cloumns cards for rateplan-share-group-cards*/
.rateplan-sharergrp-container-flexible > .rateplan-sharegroup-cards {
    display: flex;
    justify-content: inherit;
    max-width: 100%;
}

    .rateplan-sharergrp-container-flexible > .rateplan-sharegroup-cards > .sharegroup-cards-2cols-width {
        flex-basis: 100%;
        width: 100%;
        max-width: 100%;
    }

    .rateplan-sharergrp-container-flexible  .sharegroup-cards-2cols-width:first-of-type {
        margin-right:30px;

    }

    .rateplan-sharergrp-container-flexible  .sharegroup-cards-2cols-width:nth-of-type(3n) {
        margin-left: 30px;
    }

    .rateplan-sharergrp-container-flexible .sharegroup-cards-2cols-width:nth-of-type(4n) {
        margin-left: 30px;
        flex-wrap:wrap;
    }


@media screen and (max-width: 767px) {
    .rateplan-sharergrp-container-flexible .sharegroup-cards-2cols-width:first-of-type,
    .rateplan-sharergrp-container-flexible .sharegroup-cards-2cols-width:nth-of-type(3n),
    .rateplan-sharergrp-container-flexible .sharegroup-cards-2cols-width:nth-of-type(4n) {
        margin-right: 0px;
        margin-left:0px;
    }

    .rateplan-sharergrp-container-flexible > .rateplan-sharegroup-cards > .sharegroup-cards-2cols-width {
        flex-basis:100%;
    }
}


/*For IE target*/
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) and (max-width: 767px) {
    .rateplan-sharergrp-container-flexible > .rateplan-sharegroup-cards > .sharegroup-cards-2cols-width {
        flex-basis: auto;
    }
}


.rateplan-sharegroup-cards {
    display: flex;
    max-width: 900px;
    justify-content: space-between;
    width: 100%;
}

    .rateplan-sharegroup-cards > .sharegroup-cards-inner {
        width: 290px;
        padding: 30px 30px 20px 30px;
        margin: 0px;
        position: relative;
    }

    .rateplan-sharegroup-cards > .sharegroup-cards-inner2 {
        padding: 30px 0px 0px 0px
    }

    .rateplan-sharegroup-cards > .sharegroup-cards-inner .current-share-title {
        margin: 0 auto 10px;
    }

    .rateplan-sharegroup-cards > .sharegroup-cards-inner .rate-plan-account-container {
        padding: 0px
    }

    .rateplan-sharegroup-cards > .sharegroup-cards-inner .sharegroup-cards-inner-label {
        position: absolute;
        margin: 0px;
        top: -6px;
        left: 50%;
        transform: translate(-50%,-6px);
        width: auto;
        padding-left: 8px;
        padding-right: 8px;
        text-align: center;
        white-space: nowrap;
    }

        .rateplan-sharegroup-cards > .sharegroup-cards-inner .sharegroup-cards-inner-label.bgBlue {
            background-color: #00549a;
            width: auto !important;
        }

.sharegroup-cards-today-info {
    padding: 30px 15px
}

.sharegroup-cards-inner-exlamation-icon {
    border: 1px solid red;
    color: red;
    background: red;
}

.pad-top-bot-sharegroup-card {
    padding-top: 15px;
    padding-bottom: 45px;
}

.width90 {
    width: 90%;
}

.nxt-bill-period {
    width: auto !important;
}

.min-100-h {
    height: 100px;
}

/*  sharegroup 2 cols */

.rateplan-sharegroup-cards > .sharegroup-cards-inner-new {
    padding: 0px;
    border-radius: 10px;
}

.sharegroup-cards-inner-top {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.noBorderRadius {
    border-radius: 0;
}

.sharegroup-cards-inner-bottom {
    border-radius: 0px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.sharegroup-cards-pad-lr-40 {
    padding-left: 40px;
    padding-right: 40px;
}


.rateplan-sharegroup-cards > .sharegroup-cards-2cols-width {
    max-width: 443px;
    width: 100%;
}

.rateplan-sharegroup-cards .sharegroup-cards-icons-container {
    max-width: 185px;
    width: 100%;
    margin: auto;
}

.rateplan-sharegroup-cards .sharegroup-cards-bottom-info {
    margin: auto;
    max-width: 230px;
    width: 100%;
}

.rateplan-sharegroup-cards .same-height-220-d-260-sm-auto-xs {
    min-height: 220px;
    padding-bottom: 5px;
}

.rateplan-sharegroup-cards .sharegroup-cards-2cols-width .sharegroup-cards-icons-container {
    max-width: 230px;
}


/** Mobility Overview **/
/* mobility tooltip fix */
@media screen and (max-width:767px) {
    .mobility-t .tooltip-custom {
        display: none !important;
    }

    .rateplan-sharegroup-cards > .sharegroup-cards-inner:last-child {
        margin-bottom: 0px !important;
    }
}

.mobility-tooltip .modal-dialog {
    margin: unset !important;
    position: unset !important;
    bottom: unset !important;
    top: unset !important;
    left: unset !important;
    right: unset !important;
    max-width: 290px !important;
    width: 100% !important;
    height: unset !important;
    background-color: transparent;
}

/*Ovveride sor Slick buttons*/

.mobility-data-usage-card .slick-prev:hover, 
.mobility-data-usage-card .slick-next:hover, 
.mobility-data-usage-card .slick-prev:focus, 
.mobility-data-usage-card .slick-next:focus {
    background: #003676;
    box-shadow: unset !important;
}


/* START IE10+ CSS styles go here */

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) and (max-width:692px) {
    .rateplan-sharegroup-cards.ie-two-column .rate-plan-current-share-group .data-desc {
        width: 60%;
    }

    .rateplan-sharegroup-cards.ie-two-column  .rate-plan-current-share-group .rate-plan-item {
        display: flex;
    }

    .rateplan-sharegroup-cards.ie-two-column div.rate-plan-current-share-group:nth-of-type(1) {
        margin-right: 0 !important
    }

    .rateplan-sharegroup-cards.ie-two-column div.rate-plan-current-share-group:nth-of-type(2) {
        margin-left: 0 !important;
    }
}

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {

    .rateplan-sharegroup-cards.ie-two-column div.rate-plan-current-share-group:nth-of-type(1) {
        margin-right: 15px;
    }

    .rateplan-sharegroup-cards.ie-two-column div.rate-plan-current-share-group:nth-of-type(2) {
        margin-left: 15px;
    }

    .rateplan-sharegroup-cards.ie-two-column  .rate-plan-current-share-group .rate-plan-item .current-share-group-content {
        width: 100%;
    }
}

/*  END IE10+ CSS styles go here */

/*Custom Styles*/

.main-mobility-overview .usageWheelContainer .tri-up-gray.-no-border-after-triangle:before {
    border-width: 0;
}


.main-mobility-overview .wss-slick-slider.-remove-focus-pad-slick-dots .slick-dots button:focus {
    padding-left: 0px;
    padding-right: 0px;
}


/* CR001 Lighbox fix */
.cr001-main a:hover, .ico-shrink div > a:hover, .cr001-main a:focus, .ico-shrink div > a:focus {
    text-decoration: none !important;
    border-bottom: 0px !important
}
/* end of CR001 Lightbox fix */

/*Override for Accordion Trigger*/
.unlit-share-usage-overview .accordion-heading a.accordPanel:hover,
.unlit-share-usage-overview .accordion-heading a.accordPanel:focus,
.unlit-share-usage-overview .accordion-heading a.accordPanel:active{
    text-decoration: none !important;
}

.unlit-share-usage-overview .accordion-heading a .icon.icon-uev.icon-expand-bold:before,
.unlit-share-usage-overview .accordion-heading a .icon.icon-uev.icon-collapse-bold:before {
    top:0px
}

.noUnderlineHover:hover,
.noUnderlineHover:focus,
.noUnderlineHover:active {
    text-decoration: none !important;
}

.noUnderlineHover .icon.icon-uev.icon-expand-bold:before,
.noUnderlineHover .icon.icon-uev.icon-collapse-bold:before {
    top: 0px
}

/*Override for Accordion Trigger*/


/* change rate confirmation */
@media (max-width: 767.98px) {
    .confirmation-details-full-xs .sharegroup-cards-3cols-width .sharegroup-cards-bottom-info {
        max-width: 220px !important;
        width: 100% !important;
    }

    .rateplan-sharegroup-confirmation .no-borders-xs {
        border: 0px !important;
    }

    .rateplan-sharegroup-confirmation .same-height {
        min-height: auto !important;
        height: auto !important;
    }

    .hug-main .rateplan-sharegroup-cards .pad-h-xs-15{
        padding-left:15px !important;
        padding-right:15px !important;
    }
}

@media screen and (max-width:999px) {

    .rateplan-sharegroup-confirmation .rateplan-sharegroup-cards {
        flex-direction: column;
    }

    .rateplan-sharegroup-cards > .confirmation-2cols-multi {
        max-width: 100% !important;
        width: 100% !important
    }
}

.txtSize9 {
    font-size: 9px;
}

.confirmation-email .promo-label-grey {
    font-size: 9px;
}

.confirmation-email .promo-label-blue {
    padding: 4px;
    font-size: 9px;
}

.rateplan-sharegroup-cards > .confirmation-2cols-multi {
    max-width: 443px;
}

.rateplan-sharegroup-confirmation .sharegroup-cards-inner-label {
    z-index: 2;
}


.blueBorder {
    border: 1px solid #00549a
}

.no-border-bottom {
    border-bottom: 0px !important
}
/* end of confirmation  */

.promo-label-grey {
    font-size: 10px;
    color: #ffffff;
    background-color: #555;
    text-transform: uppercase;
    padding: 5px 10px;
    vertical-align: middle;
    display: inline-block;
    margin-left: 5px;
}

