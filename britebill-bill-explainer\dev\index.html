<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>Widget Demo</title>
    <meta charset="utf8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2,user-scalable=yes">

    <!-- =================BRF framework======================= -->
    <!-- keep this order to allow helpers in allBrowsers_framework.css to work properly -->
    <link href="./static/core/css/bootstrap.min.css" rel="stylesheet">


    <!--despite being in the content folder, this should be above other stylesheets to allow them (especially helpers) to override icon styles-->
    <link href="./static/content/css/bill-redesign-icons.css" rel="stylesheet">

    <!-- core stylesheets -->
    <link href="./static/core/css/bell.css" rel="stylesheet">
    <link href="./static/core/css/allBrowsers_framework.css" rel="stylesheet">
    <link href="./static/core/css/global-nav-prime.css" rel="stylesheet">
    <link href="./static/core/css/global-nav-mybell.css" rel="stylesheet">
    <link href="./static/core/css/widgets/slick.css" rel="stylesheet">
    <link href="./static/core/css/carets.css" rel="stylesheet">
    <link href="./static/content/css/bell-slick.css" rel="stylesheet">

    <!-- Content stylesheet  -->
    <link href="./static/content/css/bill-redesign.css" rel="stylesheet">

    <script src="./static/core/js/jquery-3.5.1.min.js"></script>
</head>

<body>
    <div style="text-align:center;">
        <button id="changeLocale">Bill Comparison Change page locale to <strong></strong></button>
    </div>

    <br /><br />

    <!-- <div id="container"></div> -->

    <main id="maincontent" class="bill-redesign bgWhite">
        <div id="billing-explainer" role="tabpanel" aria-labelledby="tab-billing-explainer">
            <div id="container"></div>
        </div>
    </main>

    <script src="./static/core/js/widgets/jquery.lazy.min.js"></script>
    <script src="./static/core/js/widgets/popper.min.js"></script>
    <script src="./static/core/js/widgets/slick.min.js"></script>
    <script src="./static/core/js/bootstrap.min.js"></script>
    <script src="./static/core/js/global-nav.js"></script>
    <script src="../node_modules/bwtk/dist/polyfill/polyfill.min.js"></script>
    <script src="../node_modules/bwtk/dist/requirejs.min.js"></script>
    <script src="../node_modules/bwtk/dist/loader.min.js"></script>
    <script src="../node_modules/bwtk/dist/polyfill/polyfill.min.js"></script>
    <script src="../node_modules/bwtk/dist/requirejs.min.js"></script>
    <script src="../node_modules/bwtk/dist/loader.min.js"></script>
    
    <script>
        function loadScript(source) {
            var script = document.createElement("script");
            script.src = source;
            document.body.append(script);
        }

        var loader = new BwtkLoader();
        loader.setPaths({
            "redux": "../static/v2-7/redux/redux.min",
            "react": "../static/v2-7/react-production/react.production.min",
            "react-dom": "../static/v2-7/react-dom-production/react-dom.production.min",
            "scarlet": "../static/v2-7/scarlet/scarlet.min",
            "rxjs": "../static/v2-7/rxjs/Rx.min.copy",
            "redux-actions": "../static/v2-7/redux-actions/redux-actions.min",
            "react-redux": "../static/v2-7/react-redux/react-redux.min",
            "react-intl": "../static/v2-7/react-intl/react-intl.min",
            "prop-types": "../static/v2-7/prop-types/prop-types.min",
            "redux-observable": "../static/v2-7/redux-observable/redux-observable.min"
        });
        loader.addWidget("britebill-bill-explainer", "http://127.0.0.1:8883/dist/widget.js");
        loader.start(function(require) {
            var bwtk = require("bwtk");

            var btn = document.getElementById("changeLocale");
            btn.onclick = function() {
                var newLocale = loc.locale === "en" ? "fr" : "en";
                loc.setLocale(newLocale);
                updateLocaleButton(btn, loc);
            };

            bwtk.Init({
                "localization.webServicesPath": "http://127.0.0.1:8883/localization",
            });
            
            var config = bwtk.ServiceLocator.instance.getService(bwtk.CommonServices.Config);
            config.setConfig(bwtk.LoggerConfigKeys.SeverityLevel, bwtk.LoggerSeverityLevel.All);

            var loc = bwtk.ServiceLocator.instance.getService(bwtk.CommonServices.Localization);

            bwtk.RenderWidget("britebill-bill-explainer", document.getElementById("container"));

            updateLocaleButton(btn, loc);
       

     
            var store = bwtk.ServiceLocator.instance.getService(bwtk.CommonServices.Store);
             store.createGlobalActionListener(function (action) {
                try {
                    switch (action.type) {
                        case "FETCH_BILLS_COMPLETED":
                            if (action.payload && action.payload.billList && action.payload.billList.length > 0) {
                                activeBill = {};
                                action.payload.billList.forEach(function (bill) {
                                    if ("@seqNo" && bill.seqNo.toString() === "@seqNo") {
                                        activeBill = bill;
                                    } else if (bill.isLatest) {
                                        activeBill = bill;
                                    }
                                });
                            }
                            loadScript("./static/core/js/bell.js");
                            loadScript("./static//core/js/widgets/slick.min.js");
                            // loadScript("./static/content/js/bill-redesign.js");
                            break;
                        case "SET_LOCALE":
                            // TODO: On 'Bill ready, payment due" case, check why the header's tooltip is not shown upon changing locale. Right now the below line of code is the only think that make it works, even though no timeout was set. 
                            setTimeout(() => $('[data-toggle="tooltip"]').tooltip());
                            break;
                    }
                } catch (error) {
                    console.log(error);
                }
            });
        });
        var updateLocaleButton = function(btn, loc) {
                btn.getElementsByTagName("strong")[0].innerHTML = loc.locale === "en" ? "FR" : "EN";
        }
    </script>
</body>
</html>
