import * as React from "react";
import { InjectedIntlProps, injectIntl } from "react-intl";
import { P<PERSON>Header, PBEFooter } from "singleban-components";
import { IPBE } from "../../models";
import { modalOpenedOmniture } from "../../utils/Utility";

interface Props {
    pbe: IPBE;
    isPBEModalLinkDisabled: boolean;
}

const PBEDeviceReturnOptionFee = (props: Props & InjectedIntlProps) => {
    const { intl: { formatMessage }, pbe } = props;
    const title = formatMessage({ id: "PBE_DRO_FEE_TITLE" });
    const description = formatMessage({ id: "PBE_DRO_DESC" });
    const imageClassName = "icon-10_reset_circle";
    const PBEFooterItems = [{
        ctaLink: formatMessage({ id: "DRO_AGREEMENT_LINK" }, {
            encryptedAcctNo: pbe?.pbeDataBag?.encryptAcctNo,
            subNo: pbe?.pbeDataBag?.subNo
        }),
        iconClassName: "icon-07_bill",
        titleKey: formatMessage({ id: "PBE_DRO_FOOTER_TITLE2" }),
        ctaTitleKey: formatMessage({ id: "PBE_DRO_FOOTER_SUBTITLE2" }),
        isFirstRow: false,
        id: "pbe-dro-view-agreement"
    }];

    React.useEffect(() => {
        modalOpenedOmniture(props?.pbe?.triggerPbeOmniture, title, description);
    },[title, description]);

    return (
        <>
            <PBEHeader descriptionKey={description} titleKey={title} iconClassName={imageClassName} />
            <PBEFooter footerItems={PBEFooterItems} isPBEModalLinkDisabled={props.isPBEModalLinkDisabled} />
        </>
    );
};


export default (injectIntl(PBEDeviceReturnOptionFee));