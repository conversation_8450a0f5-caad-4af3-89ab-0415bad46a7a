// Start HomePage Slider

(function (bell, $) {

    var isHomePage = document.getElementById("isHome").innerHTML; // added element
    $.widget("rsx.hpBannerCarousel", {
        version: "0.5",
        widgetEventPrefix: "hpBannerCarousel",
        links: [],
        options: {
            slidesContainer: '',
            pauseOnFocus: true,
            pauseOnHover: true,
            infinite: true,
            autoplay: true,
            autoplaySpeedMobile: 10000,
            autoplaySpeed: 6000, //default speed for all carousel slides
            dots: true,
            arrows: false,
            customPaging: function (slider, i) {
                return '<button class="hp-banner-carousel-button" type="button">' + (i + 1) + '</button>';
            },
            dotsClass: 'hp-banner-carousel-buttons',
            initialSlide: 0,
            omnitureShow_s_oAPT: '347-0-0',
            omnitureClick_s_oAPT: '348-0-0',
            track_omniture: false,
            adobeTargetCssClass: "at-element-marker",
        },

        _create: function () {
            var self, that;
            self = that = this;
            var canonical = $('link[rel=\'canonical\']').attr("href").split('/').pop(); // added element
            var alternate = $('link[rel=\'alternate\']').attr("href").split('/').pop(); // added element

            if (window.location.pathname == '/' || window.location.pathname == '/Accueil') { self.options.track_omniture = true; }
            //in order to save the tab state on browser back button, it saves tab index in this hidden input

            var progressInterval,
                percentComplete,
                progressStep = 50,
                progressIndicatorLength,
                progressIndicatorUnit,
                progressIndicatorTotal,
                progressIndicatorTotalRounded,
                overrideMouseOverOut = false;

            this.autoplaySpeed = that.options.autoplaySpeed;

            if (navigator.userAgent.match(/Android/i)
                || navigator.userAgent.match(/webOS/i)
                || navigator.userAgent.match(/iPhone/i)
                || navigator.userAgent.match(/iPad/i)
                || navigator.userAgent.match(/iPod/i)
                || navigator.userAgent.match(/BlackBerry/i)
                || navigator.userAgent.match(/Windows Phone/i)) {
                this.autoplaySpeed = that.options.autoplaySpeedMobile;
            }

            var playButtonLabel = "Pause rotation of banners";
            var pauseButtonLabel = "Resume rotation of banners";

            if (!alternate && !canonical)
                canonical = "Accueil";
            this.$window = $(window);
            this.$slidesContainer = this.element.find(self.options.slidesContainer);
            this.$slides = this.$slidesContainer.children();
            this.$pauseButton = this.element.find(".hp-banner-carousel-pause");
            this.$accessibilityLabel = this.$pauseButton.find(".sr-only");
            this.$progressIndicator = this.$pauseButton.find(".hp-banner-carousel-progress > circle");

            progressIndicatorLength = (typeof SVGElement.prototype.getTotalLength !== "undefined") ? Math.round(this.$progressIndicator.get(0).getTotalLength()) : 125;
            progressIndicatorUnit = progressIndicatorLength / 100;

            this.$progressIndicator.css({ "stroke-dasharray": progressIndicatorLength });

            resumeRotation();

            this.$accessibilityLabel.text(playButtonLabel);

            function pauseRotation() {
                if (Math.abs(progressIndicatorTotalRounded) < 1) {
                    self.$progressIndicator.addClass("hp-banner-carousel-progress_initial");
                }
                self.$pauseButton.attr("data-pressed", true);
                self.$accessibilityLabel.text(pauseButtonLabel);

            }

            function resumeRotation() {
                self.$progressIndicator.removeClass("hp-banner-carousel-progress_initial");
                self.$pauseButton.attr("data-pressed", false);
                self.$accessibilityLabel.text(playButtonLabel);
            }

            this.$pauseButton.on("click tap", function () {

                var isPaused = self.$pauseButton.attr("data-pressed") === "true";

                if (typeof s_oTrackPage === "function" && isHomePage === "True") {
                    s_oTrackPage({ s_oAPT: "647-0-0", s_oBTN: self.$accessibilityLabel.text() });
                }

                if (isPaused) {
                    resumeRotation();
                } else {
                    pauseRotation();
                }
                overrideMouseOverOut = true;
            });

            this.$slidesContainer.parent().on("mouseenter", function () {

                if (!!('ontouchstart' in window) === false) {

                    if (!overrideMouseOverOut) {
                        pauseRotation();
                    }
                }
            }).on("mouseleave", function () {

                if (!overrideMouseOverOut) {
                    resumeRotation();
                }
            }).on("click tap swipe", function (e) {

                if (e.target === self.$pauseButton[0]) {
                    return;
                }
                overrideMouseOverOut = true;
                pauseRotation();
            });

            $(document).on("visibilitychange", function () {
                if (document.visibilityState === "hidden") {
                    pauseRotation();
                } else {
                    resumeRotation();
                }
            });

            var startAutoplay = function () {

                percentComplete = 0;
                progressIndicatorTotal = 0;
                updateProgressIndicator();
                progressInterval = setInterval(progressIntervalHandler, progressStep);
            }

            var updateProgressIndicator = function () {
                percentComplete += progressStep / that.autoplaySpeed * 100;
                progressIndicatorTotal = percentComplete * progressIndicatorUnit * -1 + 1;
                progressIndicatorTotalRounded = Math.round(progressIndicatorTotal * 10) / 10;
                self.$progressIndicator.css({ "stroke-dashoffset": progressIndicatorTotalRounded });
            }

            var progressIntervalHandler = function () {

                if (self.$pauseButton.attr("data-pressed") === "false") {

                    updateProgressIndicator();

                    if (percentComplete >= 100) {
                        self.$slidesContainer.slick('slickNext');
                        resetAutoplayProgress();
                    }
                }
            }

            var resetAutoplayProgress = function () {
                clearInterval(progressInterval);
                startAutoplay();
            }

            $("#hpBannerCarousel :focusable").focusin(function () {

                if (!!('ontouchstart' in window) === false) {
                    if (!overrideMouseOverOut) {
                        pauseRotation();
                    }
                }
            }).focusout(function () {
                if (!overrideMouseOverOut) {
                    resumeRotation();
                }
            });

            this.$slider = this.$slidesContainer
                .on("init", function (event, self) {
                    self.$slider.removeClass("hp-banner-carousel-height");
                    self.options.initialSlide = self.currentSlide;
                    if (that.options.autoplay) {
                        startAutoplay();
                    }

                    $("#hpBannerCarousel .hp-banner-carousel-button")
                        .focusin(function () {
                            if (!overrideMouseOverOut) {
                                pauseRotation();

                            }
                        }).focusout(function () {
                            if (!overrideMouseOverOut) {
                                resumeRotation();
                            }
                        }).on("keyup", function (e) {

                            if (e.type === "keyup" && (e.which === 37 || e.which === 39)) {
                                overrideMouseOverOut = true;
                                $("#hpBannerCarousel .slick-active .hp-banner-carousel-button").focus();
                                progressIndicatorTotalRounded = 0;
                                pauseRotation();
                            }
                        });

                    $("#hpBannerCarousel .hp-banner-carousel-button")
                        .on("click tap", function () {
                            if (typeof s_oTrackPage === "function" && isHomePage === "True") {
                                s_oTrackPage({ s_oAPT: "647-0-0", s_oBTN: $(this).attr("aria-label") });
                            }
                        });
                    that._syncAdobeTarget(self);
                }).slick({
                    pauseOnFocus: self.options.pauseOnFocus,
                    pauseOnHover: self.options.pauseOnHover,
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    infinite: self.options.infinite,
                    adaptiveHeight: self.options.adaptiveHeight,
                    arrows: false,
                    autoplay: false, //relies on custom implementation
                    waitForAnimate: false,
                    dots: self.options.dots,
                    customPaging: self.options.customPaging,
                    dotsClass: self.options.dotsClass,
                }).on("afterChange", function (event, slick, currentSlide, nextSlide) {
                    that._track(currentSlide);
                }).on("beforeChange", function (event, slick, currentSlide, nextSlide) {
                    resetAutoplayProgress();
                });
            that._syncAdobeTarget(self);
            var CarouselImpressions = setInterval(function () {
                if (typeof s_oTrackPage === "function" || typeof s_track === "function") {
                    that._track(self.options.initialSlide);
                    clearInterval(CarouselImpressions);
                }
            }, 100);
        },

        _trackOmniture: function (code, id) {
            if (isHomePage === "True") {
                if (typeof s_oTrackPage === "function") {
                    s_oTrackPage({ s_oAPT: code, s_oBID: id });
                } else if (typeof s_track === "function") {
                    s_oTrackPage({ s_oAPT: code, s_oBID: id });
                }
            }
        },

        _track: function (currentSlide) {
            if (this.options.track_omniture && this.links[currentSlide] == undefined) {
                var banner = $(this.$slides[currentSlide]).find(".js-omni-banner");
                var omnitureVal = $(banner).data("omni-s_obid");
                this._trackOmniture(this.options.omnitureShow_s_oAPT, omnitureVal);
                this.links[currentSlide] = true;
            }
        },
        /**
         * synchronize banner with Adobe to prevent flickering issue or banner copy not complete
         *
         * @param {any} self
         */
        _syncAdobeTarget: function (self) {
            if (this.options.infinite) {
                let leftClonedSlide = self.$slider.find(".slick-slide.slick-cloned").first();
                let rightClonedSlide = self.$slider.find(".slick-slide.slick-cloned").last();
                let allSlides = self.$slider.find(".slick-slide");
                var firstSlide, lastSlide = "";
                /*mapping first slide and last slide with their cloned slides accordingly*/
                $.each(allSlides, function (index, $slide) {
                    if ($(this).data("slickIndex") === leftClonedSlide.data("slickIndex") + 1) {
                        firstSlide = $(this);
                    }
                    if ($(this).data("slickIndex") === rightClonedSlide.data("slickIndex") - 1) {
                        lastSlide = $(this);
                    }
                });
                /*find adobe target slider and replace by order*/
                if (lastSlide.find("div").hasClass(this.options.adobeTargetCssClass)) {
                    leftClonedSlide.html(lastSlide.html());
                }
                if (firstSlide.find("div").hasClass(this.options.adobeTargetCssClass)) {
                    rightClonedSlide.html(firstSlide.html());
                }
                if (leftClonedSlide.find("div").hasClass(this.options.adobeTargetCssClass)) {
                    lastSlide.html(leftClonedSlide.html());
                }
                if (rightClonedSlide.find("div").hasClass(this.options.adobeTargetCssClass)) {
                    firstSlide.html(rightClonedSlide.html());
                }
            }
        }

    });

    var activeClass = "js-omni-button";

    var trackOmnitureBannerButton = function () {
        if (isHomePage === "False") {
            $("div[id*='carousel-banner-id-']").each(function () {
                var activeOption = $(this);
                $(this).find(".js-omni-button").removeClass("js-omni-button");
                //activeOption
                //    .siblings()
                //    .removeClass(activeClass)
                //    .each(function () {
                //        $(this).find(activeClass).removeClass(activeClass);
                //    });

            });
        }
    };
    trackOmnitureBannerButton();

})({}, jQuery);

$('#hpBannerCarousel').hpBannerCarousel({
'slidesContainer': ".slider-rotating-header",
'autoplay-speed': "6000",
'autoplay-speed-mobile': "6000",
'class':"init"
})

// End HomePage Slider