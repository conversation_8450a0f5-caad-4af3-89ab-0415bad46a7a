/* This css file is exclusive only for apple tv page as most of the images on this page are set as background-image */

.subsection-pad-width {
    width: 90%;
}

.display-img-container {
    width: 960px;
}

.p-sky {
    width: 70%;
}

.thumbnail-bg-gray {
    background-color: #161616;
}

.tv-txtDark<PERSON>rey {
    color: #ccc;
}

.legal-width {
    width: 90%;
}

.legal-text {
    font-size: 11px;
    line-height: 1.45455;
    font-weight: 400;
    letter-spacing: .005em;
}

.remote-and-text-image {
    margin-left: -90px;
}

.with-the-best-satell {
    height: 152px;
    width: 330px;
    color: #111111;
    font-family: BellSlim;
    font-size: 32px;
    font-weight: 900;
    letter-spacing: -0.5px;
    line-height: 38px;
}

.bg-gradient-black {
    background-image: url('../../../../assets/img_tmp/TV/img_1b_medium.jpg');
    background-repeat: no-repeat;
    background-size: 1068px 545px;
    background-position: center top;
}

.black-bg-apple-space {
    height: 150px;
}

.subsection-pad-height {
    height: 60px;
}

.bg-gradient-green {
    background-image: url('../../../../assets/img_tmp/TV/img_7_bg.jpg');
    background-repeat: no-repeat;
    background-size: 1068px 510px;
    background-position: center center;
}

.p-sky {
    width: 80%;
}

.display-img-container {
    width: 600px;
}

@media (min-width: 992px) {
    .bg-gradient-black {
        background-image: url('../../../../assets/img_tmp/TV/img_1b.jpg');
        background-repeat: no-repeat;
        background-size: 2560px 919px;
        background-position: center top;
    }

    .black-bg-apple-space {
        height: 250px;
    }

    .subtitle {
        font-size: 21px;
        line-height: 1.38105;
        font-weight: 400;
        letter-spacing: .011em;
        color: #ccc;
    }

    .subsection-pad-height {
        height: 170px;
    }

    .bg-gradient-green {
        background-image: url('../../../../assets/img_tmp/TV/img_7_bg.jpg');
        background-repeat: no-repeat;
        background-size: 2560px 708px;
        background-position: center center;
    }

    .p-sky {
        width: 70%;
    }
}

@media (min-width: 320px) and (max-width: 767.98px) {
    .bg-gradient-black {
        background-image: url('../../../../assets/img_tmp/TV/img_1b_small.jpg');
        background-size: 736px 510px;
        background-repeat: no-repeat;
        background-position: center top;
    }

    .black-bg-apple-space {
        height: 110px;
    }

    .subtitle {
        font-size: 18px;
        line-height: 1.38105;
        font-weight: 400;
        letter-spacing: .011em;
        color: #ccc;
    }

    .subsection-pad-height {
        height: 40px;
    }

    .subsection-pad-width {
        width: 100%;
    }

    .bg-gradient-green {
        background-image: url('../../../../assets/img_tmp/TV/img_7_bg.jpg');
        background-repeat: no-repeat;
        background-size: 736px 593px;
        background-position: center center;
    }

    .display-img-container {
        width: auto;
    }
}