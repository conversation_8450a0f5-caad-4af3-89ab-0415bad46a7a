import * as React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, P<PERSON>Footer } from "singleban-components";
import { InjectedIntlProps, injectIntl } from "react-intl";
import { IPBE } from "../../models";
import { modalOpenedOmniture } from "../../utils/Utility";

interface Props {
    pbe: IPBE;
    isPBEModalLinkDisabled: boolean;
}

const PBELongDistanceNetworkConnection = (props: Props & InjectedIntlProps) => {
    const { intl: { formatMessage }, pbe } = props;
    const title = formatMessage({ id: "PBE_LONG_DISTANCE_NETWORK_CONNECTION_TITLE" });
    const description = formatMessage({ id: "PBE_LONG_DISTANCE_NETWORK_CONNECTION_DESCRIPTION" });
    const imageClassName = "icon-05_long_distance_circle";
    const footerItems = [{
        ctaLink: formatMessage({ id: "PBE_LONG_DISTANCE_ADD_LONG_DISTANCE_LINK" }, {            
            encryptedAcctNo: pbe?.pbeDataBag?.encryptedIdentifier,
            subNo: pbe?.pbeDataBag?.identifier
        }),
        iconClassName: "icon-17_world",
        titleKey: formatMessage({ id: "PBE_LONG_DISTANCE_NETWORK_CONNECTION_FOOTER1_TITLE" }),
        ctaTitleKey: formatMessage({ id: "PBE_LONG_DISTANCE_NETWORK_CONNECTION_FOOTER1_SUBTITLE" }),
        isFirstRow: true,
        id: "pbe-long_distance_network-add-a-long-distance-plan"
    }];
    React.useEffect(() => {
        modalOpenedOmniture(props?.pbe?.triggerPbeOmniture, title, description);
    },[title, description]);

    return (
        <>
            <PBEHeader descriptionKey={description} titleKey={title} iconClassName={imageClassName} />
            <PBEFooter footerItems={footerItems} isPBEModalLinkDisabled={props.isPBEModalLinkDisabled} />
        </>
    );
};


export default (injectIntl(PBELongDistanceNetworkConnection));