import * as React from "react";
import { FormattedHTMLMessage, InjectedIntlProps, injectIntl } from "react-intl";
import { PBEFooter } from "singleban-components";
import { IPBE, IStoreState } from "../../models";
import { CURRENCY_OPTIONS, DATE_OPTIONS, getPBECacheKey } from "../../utils/Utility";
import { connect } from "react-redux";

interface Props {
    pbe: IPBE;
    isPBEModalLinkDisabled: boolean;
}

interface IUsageDetail {
    EventType: string;
    EventDescription: string;
    GrandTotal: number;
    Date: string;
}

interface IDataFromActionURL {
    UsageDetails: IUsageDetail[];
}

interface MapStateToProps {
    dataFromActionURL: IDataFromActionURL;
}

const PBEMobilityEvents = (props: Props & InjectedIntlProps & MapStateToProps) => {
    const { intl: { formatMessage, formatNumber, formatDate }, pbe, dataFromActionURL } = props;
    const footerItems = [{
        ctaLink: formatMessage({ id: "MOBILITY_EVENTS_VIEW_LOCAL_LINK" }, {
            acctNo: pbe?.pbeDataBag?.encryptedBan,
            subNo: pbe?.pbeDataBag?.subNo,
            seqNo: pbe?.pbeDataBag?.seqNo,
            billCycle: pbe?.pbeDataBag?.billCycle,
            billCycleMonth: pbe?.pbeDataBag?.billCycleMonth,
            pageSize: pbe?.pbeDataBag?.maxEventsToShow
        }),
        iconClassName: "icon-10_store_locator_or_location",
        titleKey: formatMessage({ id: "MOBILITY_EVENTS_VIEW_LOCAL_TITLE" }),
        ctaTitleKey: formatMessage({ id: "MOBILITY_EVENTS_VIEW_LOCAL_SUBTITLE" }),
        isFirstRow: true,
        id: "pbe-mobility-events-view-local-link"
    }, {
        ctaLink: formatMessage({ id: "MOBILITY_EVENTS_VIEW_ROAMING_LINK" }, {
            acctNo: pbe?.pbeDataBag?.encryptedBan,
            subNo: pbe?.pbeDataBag?.subNo,
        }),
        iconClassName: "icon-17_world",
        titleKey: formatMessage({ id: "MOBILITY_EVENTS_VIEW_ROAMING_TITLE" }),
        ctaTitleKey: formatMessage({ id: "MOBILITY_EVENTS_VIEW_ROAMING_SUBTITLE" }),
        isFirstRow: false,
        id: "pbe-mobility-events-view-roaming-link"
    }];
    const showMoreThanMax = pbe?.pbeDataBag?.totalEvents! > pbe?.pbeDataBag?.maxEventsToShow!;

    return (
        <>
            <div className="margin-b-30 margin-h-xs-15 margin-h-30">
                <div className="d-flex flex-wrap margin-b-15">
                    <span className="icon-bill-redesign icon-32_event_ticket_circle txtBlue txtSize72" aria-hidden="true"></span>
                </div>
                <h2 id="pbe-modal-title" className="margin-b-15 small-title txtBlack js-omniture--s_oPRM">
                    {formatMessage({ id: "MOBILITY_EVENTS_TITLE" })}
                </h2>
                <p className="margin-b-10 js-omniture--s_oLBC">
                    <FormattedHTMLMessage id={pbe?.pbeDataBag?.totalEvents! > 1 ? "MOBILITY_EVENTS_DESCRIPTION_MULTIPLE_EVENTS" : "MOBILITY_EVENTS_DESCRIPTION_ONE_EVENT"} values={{
                        amount: formatNumber(pbe?.pbeDataBag?.totalCharge, CURRENCY_OPTIONS),
                        numEvents: pbe?.pbeDataBag?.totalEvents
                    }} />
                </p>
                <p className="margin-b-0 js-omniture--s_oLBC">
                    <FormattedHTMLMessage id={"MOBILITY_EVENTS_DESCRIPTION_2"} />
                </p>
            </div>
            {dataFromActionURL?.UsageDetails !== undefined && dataFromActionURL?.UsageDetails !== null ? <div className="margin-b-30 margin-h-xs-15 margin-h-30">
                <div className="box-round-grey pad-xs-15 pad-20">
                    <div className="d-block relative">
                        <div className="d-flex">
                            <span className="margin-r-xs-15 margin-r-20">
                                <span className="surtitle-black">{formatMessage({ id: "MOBILITY_EVENTS_TABLE_TITLE" })}</span>
                            </span>
                        </div>
                        <ul className="pbe-list-service listStyleNone pad-0 margin-t-15 margin-b-0 js-same-width-delegate">
                            {dataFromActionURL?.UsageDetails?.slice(0, pbe?.pbeDataBag?.maxEventsToShow).map((usageDetail, index) =>
                                <li key={usageDetail?.EventDescription + index} className="d-flex flex-column flex-sm-row margin-h-xs-0 margin-b-10">
                                    <span className="js-same-width-delegate-item d-flex margin-r-xs-0 margin-r-20">
                                        <strong>{formatDate(new Date(usageDetail.Date), DATE_OPTIONS)}</strong>
                                        <span className="sr-only">&nbsp;</span>
                                    </span>
                                    <span className="d-flex container-flex-grow-fill flex-justify-space-between">
                                        <span className="margin-r-xs-15 margin-r-20">
                                            <span>{usageDetail?.EventDescription}</span>
                                            <span className="sr-only">,&nbsp;</span>
                                        </span>
                                        <span>
                                            <span aria-hidden="true">{formatNumber(usageDetail?.GrandTotal, CURRENCY_OPTIONS)}</span>
                                            <span className="sr-only">{formatNumber(usageDetail?.GrandTotal, CURRENCY_OPTIONS)}</span>
                                        </span>
                                    </span>
                                </li>
                            )}
                        </ul>
                    </div>
                    {showMoreThanMax ? <div className="d-flex margin-t-15">
                        <span className="margin-r-xs-15 margin-r-20">
                            <span>{formatMessage({ id: "MOBILITY_EVENTS_MORE_THAN_MAX" })}</span>
                        </span>
                    </div> : null}
                    <hr className={`${showMoreThanMax ? "margin-b-15 margin-t-5" : "margin-v-15"} border-lightGray`} aria-hidden="true" />
                    <div className="d-block relative">
                        <div className="d-flex">
                            <span className="text-nowrap margin-r-xs-0 margin-r-20">
                                <span className="surtitle-black">{formatMessage({ id: "MOBILITY_EVENTS_TABLE_TOTAL" })}</span>
                                <span className="sr-only">&nbsp;</span>
                            </span>
                            <span className="d-flex container-flex-grow-fill justify-end">
                                <span className="d-inline noTxt txtRight">
                                    <span className="text-nowrap txtSize14 line-height-18" aria-hidden="true">{formatNumber(pbe?.pbeDataBag?.totalCharge, CURRENCY_OPTIONS)}</span>
                                    <span className="sr-only">{formatNumber(pbe?.pbeDataBag?.totalCharge, CURRENCY_OPTIONS)}</span>
                                </span>
                            </span>
                        </div>
                    </div>
                </div>
            </div> : null}
            {pbe?.pbeDataBag?.showUsageLink ? <PBEFooter footerItems={footerItems} isPBEModalLinkDisabled={props.isPBEModalLinkDisabled} /> : null}
        </>
    );
};

const mapStateToProps = (state: IStoreState, ownProps: Props): MapStateToProps => {
    return (
        {
            dataFromActionURL: state.pbeDataCache[getPBECacheKey(ownProps.pbe)]
        }
    );
};


export default injectIntl(connect(mapStateToProps)(PBEMobilityEvents));