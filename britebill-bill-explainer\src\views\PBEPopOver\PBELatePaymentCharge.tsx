import * as React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "singleban-components";
import { InjectedIntlProps, injectIntl } from "react-intl";
import { IPBE } from "../../models";
import { DATE_OPTIONS, modalOpenedOmniture } from "../../utils/Utility";
interface Props {
    pbe: IPBE;
    isPBEModalLinkDisabled: boolean;
}

const PBELatePaymentCharge = (props: Props & InjectedIntlProps) => {
    const { intl: { formatMessage, formatDate }, pbe } = props;
    const title = formatMessage({ id: "PBE_LATE_PAYMENT_CHARGE_TITLE" });
    const description1 = formatMessage({ id: "PBE_LATE_PAYMENT_CHARGE_DESCRIPTION_1" }, { date: formatDate(new Date(pbe?.pbeDataBag?.date), DATE_OPTIONS) });
    const description2 = "";
    const accountNo = formatMessage({ id: "PBE_LATE_PAYMENT_CHARGE_ACCOUNT_NUMBER" }, {
        accountNumber: pbe.pbeDataBag.acctNo
    });
    const footerItems = [{
        ctaLink: formatMessage({ id: "PBE_LATE_PAYMENT_CHARGE_LINK" }, {
            acctNo:pbe.pbeDataBag.accNoEncrypt
        }),
        iconClassName: "",
        ctaTitleKey: formatMessage({ id: "PBE_LATE_PAYMENT_CHARGE_BUTTON_TEXT" }),
        isFirstRow: true,
        id: "PBE_LATEPAYMENT_CHARGE_LINK"
    }];
    
    React.useEffect(() => {
        modalOpenedOmniture(props?.pbe?.triggerPbeOmniture, title, description1+description2);
    },[title, description1, description2]);
    return (
        <> 
           <PBEHeader titleKey={title} descriptionKey={description1} descriptionKey2={description2} accountNo={accountNo}/>
           {pbe.pbeDataBag.isLatest && <PBEFooter footerItems={footerItems} isPBEModalLinkDisabled={props.isPBEModalLinkDisabled}/>}
        </>
    );
};

export default (injectIntl(PBELatePaymentCharge));