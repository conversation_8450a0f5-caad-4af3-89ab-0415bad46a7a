﻿/*v1.1
Latest update 2021 Jul 22
Rules
1 Please keep the fonts grouped together at the top of the file.
2 Please keep the media queries grouped at the bottom of the file.
BellSlim font*/
@font-face {font-family: 'bellslimregular';src: url(../fonts/bellslim_regular-webfont.woff2) format("woff2"),url(../fonts/bellslim_regular-webfont.woff) format("woff");font-weight: 400;font-style: normal;font-display: swap;}
@font-face {font-family: 'bellslim_mediumregular';src: url(../fonts/bellslim_medium-webfont.woff2) format("woff2"),url(../fonts/bellslim_medium-webfont.woff) format("woff");font-weight: 400;font-style: normal;font-display: swap;}
@font-face {font-family: 'bellslim_semiboldregular';src: url(../fonts/bellslim_semibold-webfont.woff2) format("woff2"),url(../fonts/bellslim_semibold-webfont.woff) format("woff");font-weight: 400;font-style: normal;font-display: swap;}
@font-face {font-family: 'bellslim_font_heavy';src: url(../fonts/bellslim_heavy-webfont.woff2) format("woff2"),url(../fonts/bellslim_heavy-webfont.woff) format("woff");font-weight: 400;font-style: normal;font-display: swap;}
@font-face {font-family: 'bellslim_font_black';src: url(../fonts/bellslim_black-webfont.woff2) format("woff2"),url(../fonts/bellslim_black-webfont.woff) format("woff");font-weight: 400;font-style: normal;font-display: swap;}

.bellSlim{font-family:"bellslim_mediumregular",Helvetica,Arial,sans-serif;letter-spacing:-1px}
.bellSlimRegular{font-family:"bellslimregular",Helvetica,Arial,sans-serif;letter-spacing:-1px}
.bellSlimSemibold{font-family:"bellslim_semiboldregular",Helvetica,Arial,sans-serif;letter-spacing:-1px}
.bellSlimHeavy{font-family:"bellslim_font_heavy",Helvetica,Arial,sans-serif;letter-spacing:-1px}
.bellSlimBlack{font-family:"bellslim_font_black",Helvetica,Arial,sans-serif;letter-spacing:-1px}
.sans-serif{font-family:"Helvetica",Arial,sans-serif;letter-spacing:0}

/* START generic helpers override */
.connector-active-lob a > div {
    color: #fff;
    font-size: 20px;
    font-family: 'bell-slim';
    font-weight: 400;
    letter-spacing: -.6px;
    line-height: 1.1;
    margin: 0;
    margin-top: -4px;
}

.big-title,
.title,
.small-title,
.subtitle-2,
.subtitle-2-reg,
.surtitle,
.surtitle-black {
    max-width: 100%;
    width: auto;
}

.big-title,
.title,
.small-title,
.subtitle-2,
.subtitle-2-reg,
.surtitle-black,
.surtitle-black-reg {
    color: #111;
}

.big-title,
.title,
.small-title {
    font-family: "bellslim_font_black", Helvetica, Arial, sans-serif;
    font-size: 26px;
    font-weight: 400;
    letter-spacing: -.4px;
    line-height: 28px;
}

.small-title {
    font-size: 22px;
    line-height: 24px;
}

.subtitle-2,
.surtitle,
.surtitle-black {
    font-weight: 700;
    font-size: 14px;
}

.subtitle-2,
.subtitle-2-reg {
    font-family: Helvetica, Arial, sans-serif;
    letter-spacing: normal;
    line-height: 22px;
}

.subtitle-2 {
    font-size: 18px;
}

.subtitle-2-reg {
    font-size: 18px;
    font-weight: 400;
}

.long-text {
    color: #2b2b2b;
    font-size: 16px;
    line-height: 25px;
    margin-bottom: 10px;
}

.small-text {
    font-size: 12px;
    line-height: 14px;
}

.borderRadiusAll20 {
    border-radius: 20px;
}


body{color:#555;background-color:#e1e1e1;overflow-x:hidden;font-family: Helvetica,Arial,sans-serif;font-size:14px;line-height:18px}
main{overflow-x:hidden; overflow-y:auto;}


h1,.h1{font-size:40px;font-weight:200;letter-spacing:-1.2px;line-height:1}
h2,.h2{font-size:32px;font-weight:200;letter-spacing:-.5px;line-height:1}
h3,.h3{font-size:24px;font-weight:400;letter-spacing:-.6px;line-height:1}
h1, h2, h3, h4, h5, h6 {margin: 0}
p {margin-bottom: 15px}

/*START UTILITY CLASS*/
.col-lg-1,.col-lg-10,.col-lg-11,.col-lg-12,.col-lg-2,.col-lg-3,.col-lg-4,.col-lg-5,.col-lg-6,.col-lg-7,.col-lg-8,.col-lg-9,.col-md-1,.col-md-10,.col-md-11,.col-md-12,.col-md-2,.col-md-3,.col-md-4,.col-md-5,.col-md-6,.col-md-7,.col-md-8,.col-md-9,.col-sm-1,.col-sm-10,.col-sm-11,.col-sm-12,.col-sm-2,.col-sm-3,.col-sm-4,.col-sm-5,.col-sm-6,.col-sm-7,.col-sm-8,.col-sm-9,.col-xs-1,.col-xs-10,.col-xs-11,.col-xs-12,.col-xs-2,.col-xs-3,.col-xs-4,.col-xs-5,.col-xs-6,.col-xs-7,.col-xs-8,.col-xs-9,.col-1,.col-2,.col-3,.col-4,.col-5,.col-6,.col-7,.col-8,.col-9,.col-10,.col-11,.col-12,.col-auto,.col-sm-auto,.col-md-auto,.col-lg-auto{padding-right: 0;padding-left: 0}
ol,ul{padding-left:17px}
.table-cell{display:table-cell!important;float:none!important}
.table{display:table!important;margin-bottom:0;table-layout:fixed;width:100%}

/*Replacement of panel body*/
.card-body{padding:20px 40px}
.img-responsive{display:block;max-width:100%;height:auto}
  
/*Replacement of well class that was removed in bootstrap*/
.grid-container{min-height:20px;padding:20px 35px;margin-bottom:20px;background-color:#f5f5f5;border:1px solid #e3e3e3;border-radius:4px;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.05);box-shadow:inset 0 1px 1px rgba(0,0,0,.05)}
.grid-container-sm{padding:9px;border-radius:3px}

/*START SHOW/HIDE PASSWORD BUTTOn*/
.maskUnMaskPwsBtn{right:12px;top:7px;border:medium none;background:#bbbec3;width:auto;height:25px;color:#000;border-radius:5px;font-size:11px}
/*END SHOW/HIDE PASSWORD BUTTOn*/

.scrollToTop.mobile{opacity:.9;right:12px;bottom:12px;display:none;width:50px;height:50px;position:fixed;padding-top:7px;border-radius:50%;z-index:999;-webkit-box-shadow:0 0 26px -6px rgba(0,0,0,0.75);-moz-box-shadow:0 0 26px -6px rgba(0,0,0,0.75);box-shadow:0 0 26px -6px rgba(0,0,0,0.75)}

/*END UTILITY CLASS*/

/* icon helper classes */
.icon-circle-xsmall,.icon-circle-small,.icon-circle-medium,.icon-circle-large,.icon-circle-xlarge{display:inline-block;position:relative;width:18px;height:18px;border:2px solid #00549a;border-radius:50%;color:#00549a}
.icon-circle-xsmall .text,.icon-circle-small .text,.icon-circle-medium .text,.icon-circle-large .text,.icon-circle-xlarge .text{display:inline-block;text-align:center;width:100%}

.icon-circle-xsmall.icon-circle_solid,.icon-circle-small.icon-circle_solid,.icon-circle-large.icon-circle_solid,.icon-circle-xlarge.icon-circle_solid{border-color:#00549a;background-color:#00549a}
.icon-circle-xsmall.icon-circle_inverse,.icon-circle-small.icon-circle_inverse,.icon-circle-medium.icon-circle_inverse,.icon-circle-large.icon-circle_inverse,.icon-circle-xlarge.icon-circle_inverse{border-color:#fff}
.icon-circle-xsmall.icon-circle_solid:before,.icon-circle-small.icon-circle_solid:before,.icon-circle-medium.icon-circle_solid:before,.icon-circle-large.icon-circle_solid:before,.icon-circle-xlarge.icon-circle_solid:before,.icon-circle-xsmall.icon-circle_inverse:before,.icon-circle-small.icon-circle_inverse:before,.icon-circle-medium.icon-circle_inverse:before,.icon-circle-large.icon-circle_inverse:before,.icon-circle-xlarge.icon-circle_inverse:before,.icon-circle-xsmall.icon-circle_solid,.icon-circle-small.icon-circle_solid,.icon-circle-medium.icon-circle_solid,.icon-circle-large.icon-circle_solid,.icon-circle-xlarge.icon-circle_solid,.icon-circle-xsmall.icon-circle_inverse,.icon-circle-small.icon-circle_inverse,.icon-circle-medium.icon-circle_inverse,.icon-circle-large.icon-circle_inverse,.icon-circle-xlarge.icon-circle_inverse{color:#fff}
.icon-circle-xsmall.icon-circle_solid.icon-circle_inverse,.icon-circle-small.icon-circle_solid.icon-circle_inverse,.icon-circle-medium.icon-circle_solid.icon-circle_inverse,.icon-circle-large.icon-circle_solid.icon-circle_inverse,.icon-circle-xlarge.icon-circle_solid.icon-circle_inverse{border-color:#fff;background-color:#fff}
.icon-circle-xsmall.icon-circle_solid.icon-circle_inverse:before,.icon-circle-small.icon-circle_solid.icon-circle_inverse:before,.icon-circle-medium.icon-circle_solid.icon-circle_inverse:before,.icon-circle-large.icon-circle_solid.icon-circle_inverse:before,.icon-circle-xlarge.icon-circle_solid.icon-circle_inverse:before{color:#00549a}
.icon-circle-small .icon-circle-txt{font-size:24px;width:37px;text-align:center;display:inline-block;line-height:1.5}
[class*="icon-arrow-"]{transition:-webkit-transform .5s cubic-bezier(.55,0,.1,1);transition:transform .5s cubic-bezier(.55,0,.1,1)}
.icons-group{white-space:nowrap}
.icons-group > .icon-o:not(:first-child){margin-left:-20px}

/*custom icon sizes*/
.icon-small-42:before,.icon-circle-small-42:before{font-size:42px;height:42px}
.icon-small-180:before,.icon-circle-small-180:before{font-size:180px}

/*Style for icons with around circle*/
.icon-blue-circle{height:72px;width:72px;border-radius:50%;background-color:#00549a;color:#fff;font-size:36px;display:inline-block;position:relative}
.icon-blue-circle:before{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}
.icon-white-circle{height:72px;width:72px;border-radius:50%;background-color:#fff;color:#00549a;font-size:36px;display:inline-block;position:relative}
.icon-white-circle:before{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}

/*Solid icons with content dark background*/
.icon.icon-bgWhite:before{left:-1px}
.icon.icon-bgWhite span{display:inline-block;float:left;width:.8em;height:.8em;background-color:#fff;border-radius:50%;margin-right:-1em;margin-top:.2em}
/*End*/

/* Some common circle  icon presets */
.toggle-more{display:inline-block;position:relative;font-size:18px;padding-left:30px;line-height:1.3}
.toggle-more:before{content:'';display:block;position:absolute;width:22px;height:22px;border:1px solid #0066a4;border-radius:50%;left:0}
.toggle-more:after{font-family:'bell-icon';content:'\e007';position:absolute;top:11px;left:11px;-webkit-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%);font-size:11px;line-height:1;color:#0066a4}
.more-link{position:relative;padding-right:25px;display:inline-block}
.more-link:after{font-family:'bell-icon';content:'\e608';position:absolute;top:2px;right:5px;line-height:1}
.more-link.more-link_before{padding-left:25px}
.more-link.more-link_before:after{left:0}
ul.more-info-list{list-style:none;padding-left:0;margin-right:20px;display:table}
ul.more-info-list li{padding-top:10px}
ul.more-info-list li:before{content:"\e608";color:#00549a;font-family:'bell-icon';display:table-cell;margin-left:-20px}
ul.more-info-list.linkBlue li:before{color:#00549a}
ul.more-info-list.linkWhite li:before,ul.more-info-list.linkWhite li a{color:#fff}
ul.more-info-list li a,ul.more-info-list li p{padding-left:8px;display:table-cell}

/*links*/
a{color:#00549a;text-decoration:none}
a.txtWhite:hover,a.txtWhite:focus{color:#fff}
a:hover,a:focus{color:#003778;text-decoration:underline}
a:hover.txtNoUnderline,a:focus.txtNoUnderline{text-decoration:none}
.tabOuter_container a:hover.txtNoUnderline,.tabOuter_container a:focus.txtNoUnderline{color:#fff}
a:focus,li.ui-menu-item:focus{outline-width:2px; outline-style:solid; outline-color:#4d90fe!important;outline-offset:2px}
a:link .icon:before,a:visited .icon:before,a:hover .icon:before,a:focus .icon:before a:active .icon:before{text-decoration:none;display:inline-block}
a.error:link,a.error:visited,a.error:hover,a.error:active{color:#BD2025}

a.skip-to-main-link:focus{outline:2px solid #2672cb}

/*START PROGRESS STEPS*/
 
.progressive-steps .icon-circle-xsmall{color:#fff;display:inline-block;position:relative;border:2px solid #babec2;border-radius:50%}
.progressive-steps .icon-circle-xsmall.icon-circle_solid{background:#00549A}
.progressive-steps .icon-circle-xsmall{height:13px;width:13px}
.progressive-steps.complete .icon-circle-xsmall,.progressive-steps.active .icon-circle-xsmall{height:24px;width:24px;border:5px solid #fff}
.progressive-steps.complete .icon-circle-xsmall{margin-bottom:2px;background:#fff}
.progressive-steps.complete .icon-check-mark:before{font-size:8px;top:-2px;position:relative}
.progressive-steps.next .icon-circle-xsmall{height:30px;width:30px}
.progressive-steps .icon.icon-check::before{font-size:11px}
.progressive-steps .sm-tick-icon .icon-check::before{font-size:15px;color:#fff}
.progressive-steps-line{position:relative;top:17px;margin:0 100px;height:2px}
.progressive-steps-line.one-step{margin:0 200px}
.progressive-steps-line.two-steps{margin:0 25%}
.progressive-steps-line.three-steps{margin:0 17%}
.progressive-steps-line.four-steps{margin:0 12.5%}
.progressive-steps-line.five-steps{margin:0 10%}
.progressive-steps-line.six-steps{margin:0 8%}

/*progress for 6 steps only */
.progressive-steps-progress-1{position:relative;top:15px;margin:0 8%;width:0;height:2px}
.progressive-steps-progress-2{position:relative;top:15px;margin:0 8%;width:calc(100% / 6);height:2px}
.progressive-steps-progress-3{position:relative;top:15px;margin:0 8%;width:calc(100% / 3);height:2px}
.progressive-steps-progress-4{position:relative;top:15px;margin:0 8%;width:calc(100% / 2);height:2px}
.progressive-steps-progress-5{position:relative;top:15px;margin:0 8%;width:calc(100% / 1.5);height:2px}
.progressive-steps-progress-6{position:relative;top:15px;margin:0 8%;width:auto;height:2px}

/*progress for 5 steps only */
.progressive-steps-progress-1-of-5{position:relative;top:15px;margin:0 10%;width:0;height:2px}
.progressive-steps-progress-2-of-5{position:relative;top:15px;margin:0 10%;width:calc(100% / 5);height:2px}
.progressive-steps-progress-3-of-5{position:relative;top:15px;margin:0 10%;width:calc(100% / 2.5);height:2px}
.progressive-steps-progress-4-of-5{position:relative;top:15px;margin:0 10%;width:calc(100% / 1.66);height:2px}
.progressive-steps-progress-5-of-5{position:relative;top:15px;margin:0 10%;width:auto;height:2px}

/*progress for 4 steps only */
.progressive-steps-progress-1-of-4{position:relative;top:15px;margin:0 12.5%;width:0;height:2px}
.progressive-steps-progress-2-of-4{position:relative;top:15px;margin:0 12.5%;width:calc(100% / 3.9);height:2px}
.progressive-steps-progress-3-of-4{position:relative;top:15px;margin:0 12.5%;width:calc(100% / 2);height:2px}
.progressive-steps-progress-4-of-4{position:relative;top:15px;margin:0 12.5%;width:auto;height:2px}

/*progress for 3 steps only */
.progressive-steps-progress-1-of-3{position:relative;top:15px;margin:0 17%;width:0;height:2px}
.progressive-steps-progress-2-of-3{position:relative;top:15px;margin:0 17%;width:calc(100% / 3);height:2px}
.progressive-steps-progress-3-of-3{position:relative;top:15px;margin:0 17%;width:auto;height:2px}

/*progress for 2 steps only */
.progressive-steps-progress-1-of-2{position:relative;top:15px;margin:0 25%;width:0;height:2px}
.progressive-steps-progress-2-of-2{position:relative;top:15px;margin:0 25%;width:auto;height:2px}
.progressive-mobile-progressbar{display:none;width:100%;margin:0}
.progressive-mobile-bar-left{background-color:#fff;height:4px}
.progressive-mobile-bar-right{background-color:#babec2;height:2px;margin-top:1px}
.progressive-steps.left-side .icon-circle-xsmall{position:relative;left:-8px;top:-10px;height:15px;width:15px;overflow:hidden}
.progressive-steps.right-side .icon-circle-xsmall{position:relative;right:-8px;top:-9px;height:15px;width:15px;overflow:hidden}
.progressive-steps.active.mobile .icon-circle-xsmall{position:relative;top:-16px}
/*END PROGRESS STEPS*/

/*START MODALS*/
.modal:after {
content: "";
width: 100vw;
height: 100vh;
background-color: #000;
position: fixed;
opacity: .5;
top: 0;
left: 0;
z-index: -1;
}
.modal-backdrop.in,
.modal-backdrop.show {
opacity: 0.1 !important;
}
/* Modal changes from shop */
.modal-content{border-radius: 10px 10px 0 0;-webkit-box-shadow:0 0 30px rgba(0,0,0,0.3);-moz-box-shadow:0 0 30px rgba(0,0,0,0.3);box-shadow:0 0 30px rgba(0,0,0,0.3)}
.modal-header .close{margin-top:-7px;margin-right:-15px}
.modal-lg .modal-header .close{margin-top:-7px;margin-right:-17px}
.modal-lg.bell-modal-lg .modal-header .close{padding:15px;margin:-5px -15px -15px -18px}
.modal .modal-close_cros{border:0;background-color:transparent;width:40px;height:40px}
.modal .modal-md .close{padding:15px;margin:-7px -15px -15px -18px}
.modal.modal-tooltip .modal-body,.modal-header,.modal-footer{padding:15px 30px}
.modal.modal-tooltip{z-index:99999}
.modal.modal-tooltip .modal-body{padding:0 40px 40px}
/* Modal changes from shop */
.modal-body{margin-bottom:30px;margin-top: 30px;padding: 0 15px}
.modalSelectNav{width:90%;margin:auto}
.close{opacity:1}
.close:hover,.close:focus{opacity:1}
.modal-footer{text-align:left;border-top:none}
.modal-footer .btn + .btn{margin-bottom:0;margin-left:0}
button.close:focus{border:1px dotted}
.modal-content{border:0}
.modal-title.line-height-20{line-height:23px;margin-top:0px}
.modal-title{line-height:26px;}
.unfocus,.unfocus:focus{border:0;outline:0}
.modal .modal-md .close{margin-top:-20px}
.modal-header-gray{height:74px;background-color:#e1e1e1}
.modal-header-blue{height:74px;background-color:#00549a}
.modal-dialog{width:645px;max-width:100%}
.modal.modal-tooltip .modal-content{border:0}
/*fix for view portview width issue on mobile*/
.modal{width:calc(var(--vw, 1vw) * 100)}

/* Modal changes from shop */
.modal-dialog{
    border-radius: 10px 10px 0 0;
}

.modal-header {
    align-items: center;
    background-color: #f0f0f0;
    padding: 15px 20px 15px 15px;
}

.modal-dialog.modal-dialog-fullscreen {
    max-width: 1200px;
    width: 100%;
}

.modal-dialog .modal-content .modal-header .close {
    margin: 0 -15px 0 15px;
    padding: 15px;
    border: 0;
}

.modal-body {
    margin-bottom: 30px;
    margin-top: 30px;
    padding: 0 15px;
}

/* some browsers such as IE and FF ignore padding-bottom when content overflows so we'll use :after pseudo-element instead */
.modal-open .modal-body.scrollAdjust {
    padding-bottom: 0;
}

.modal.modal-status .modal-dialog {
    border-radius: 10px;
    box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
    max-width: calc(100% - 30px);
    position: relative;
}

.modal.modal-status .modal-content {
    border-radius: 10px;
}

.modal.modal-status .modal-body {
    padding: 0 30px;
}

/*Modal overlay*/
.modal:after {
content: "";
width: 100vw;
height: 100vh;
background-color: #000;
position: fixed;
opacity: .5;
top: 0;
left: 0;
z-index: -1;
}
.modal-backdrop.in,
.modal-backdrop.show {
opacity: 0;
}

/*MODAL WINDOW VERTICAL CENTERING*/
.modal{text-align:center;padding:0;z-index:99999}
.modal:before{content:'';display:inline-block;height:100%;vertical-align:middle;margin-right:0}
.modal-dialog{display:inline-block;text-align:left;vertical-align:middle;margin: auto;}
/*MODAL WINDOW VERTICAL CENTERING*/
.modal.modal-tooltip .modal-content{border:0}
/*END MODALS*/

/*START TOOLTIP*/
.tooltip{width:315px;position:absolute!important}
.tooltip-inner{max-width:315px;padding:40px;color:#555;font-size:14px;text-align:left;background-color:#FFF;border-radius:0;
               box-shadow: 0 0 11px 0 rgba(0,0,0,0.27);
               -webkit-box-shadow: 0 0 11px 0 rgba(0,0,0,0.27);-moz-box-shadow: 0 0 11px 0 rgba(0,0,0,0.27);}
.tooltip-lg .tooltip{width:700px}
.tooltip-lg .tooltip .tooltip-inner{max-width:700px;padding:30px}
.tooltip.show{opacity:1}
.tooltip .arrow, .tooltip.bs-tooltip-auto .arrow{height:30px}
.tooltip.bs-tooltip-right,.tooltip.bs-tooltip-auto[x-placement^=right]{padding:0 5px;margin-left:20px}
.tooltip.bs-tooltip-right .arrow,.tooltip.bs-tooltip-auto[x-placement^=right] .arrow{margin-top:-25px}
.tooltip.bs-tooltip-right .arrow,.tooltip.bs-tooltip-auto[x-placement^=right] .arrow{-webkit-filter: drop-shadow(-6px 0px 3px rgba(0,0,0,0.12)); 
    -moz-filter: drop-shadow(-6px 0px 3px rgba(0,0,0,0.12));
    filter: drop-shadow(-6px 0px 3px rgba(0,0,0,0.12));}
.tooltip.bs-tooltip-right .arrow::before,.tooltip.bs-tooltip-auto[x-placement^=right] .arrow::before{border-width:25px 25px 25px 0;border-right-color:#fff}
.tooltip.bs-tooltip-left,.tooltip.bs-tooltip-auto[x-placement^=left]{padding:0 5px;margin-right:20px}
.tooltip.bs-tooltip-left .arrow,.tooltip.bs-tooltip-auto[x-placement^=left] .arrow{margin-top:-25px}
.tooltip.bs-tooltip-left .arrow::before,.tooltip.bs-tooltip-auto[x-placement^=left] .arrow::before{border-width:25px 0 25px 25px;border-left-color:#fff}
.tooltip.bs-tooltip-left .arrow,.tooltip.bs-tooltip-auto[x-placement^=left] .arrow{-webkit-filter: drop-shadow(4px -1px 2px rgba(0,0,0,0.17)); 
    -moz-filter: drop-shadow(4px -1px 2px rgba(0,0,0,0.17));
    filter: drop-shadow(4px -1px 2px rgba(0,0,0,0.17));}

.tooltip.bs-tooltip-top,.tooltip.bs-tooltip-auto[x-placement^=top]{padding:0 5px;margin-bottom:20px}
.tooltip.bs-tooltip-top .arrow,.tooltip.bs-tooltip-auto[x-placement^=top] .arrow{margin-left:-35px;margin-bottom:-25px}
.tooltip.bs-tooltip-top .arrow::before,.tooltip.bs-tooltip-auto[x-placement^=top] .arrow::before{border-width:25px 25px 0;border-top-color:#fff}
.tooltip.bs-tooltip-top .arrow,.tooltip.bs-tooltip-auto[x-placement^=top] .arrow{-webkit-filter: drop-shadow(0px 9px 5px rgba(0,0,0,0.12)); 
    -moz-filter: drop-shadow(0px 9px 5px rgba(0,0,0,0.12));
    filter: drop-shadow(0px 9px 5px rgba(0,0,0,0.12));}
.tooltip.bs-tooltip-bottom,.tooltip.bs-tooltip-auto[x-placement^=bottom]{padding:0 5px;margin-top:20px}
.tooltip.bs-tooltip-bottom .arrow,.tooltip.bs-tooltip-auto[x-placement^=bottom] .arrow{margin-left:-35px;margin-top:-25px}
.tooltip.bs-tooltip-bottom .arrow::before,.tooltip.bs-tooltip-auto[x-placement^=bottom] .arrow::before{border-width:0 25px 25px;border-bottom-color:#fff}
.tooltip.in{filter:alpha(opacity=100);opacity:1}
.tooltip.bs-tooltip-bottom .arrow,.tooltip.bs-tooltip-auto[x-placement^=bottom] .arrow{-webkit-filter:drop-shadow(0px -8px 5px rgba(0,0,0,0.12)); 
    -moz-filter: drop-shadow(0px -8px 5px rgba(0,0,0,0.12));
    filter: drop-shadow(0px -8px 5px rgba(0,0,0,0.12));}
/*END TOOLTIP*/


/* IE10+ CSS styles for tooltip*/
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) { 
    .tooltip.bs-tooltip-right .arrow{ 
       box-shadow: -15px 15px 30px rgba(0,0,0,0.42); 
    }
    .tooltip.bs-tooltip-left .arrow{ 
       box-shadow: 15px 15px 30px rgba(0,0,0,0.42); 
    }
    .tooltip.bs-tooltip-top .arrow{ 
       box-shadow: 20px 10px 30px rgba(0,0,0,0.42); 
    }
    .tooltip.bs-tooltip-bottom .arrow{ 
       box-shadow: 20px -5px 30px rgba(0,0,0,0.42); 
    }
}

/* START CHECKBOX SWITCH */.form-switch-container{width:120px;border:1px solid #d4d4d4;height:30px;border-radius:15px;font-size:12px}
.form-switch-toggle > span,.form-switch-toggle > span{color:#fff}
.form-switch-toggle span span,.form-switch-toggle label,.form-switch-toggle span span,.form-switch-toggle label{color:#fff}
.form-switch-toggle span span,.form-switch-toggle input:checked ~ span span:first-child,.form-switch-toggle.form-switch-candy label{color:#00549a}
.form-switch-toggle input ~ span span:first-child,.form-switch-toggle input:checked ~ span span:nth-child(2),.form-switch-candy input:checked + label{color:#fff}
.form-switch-toggle a,.form-switch-toggle span span{display:none}
.form-switch-toggle{display:block;cursor:pointer;height:30px;position:relative;overflow:visible;padding:0;margin-left:0;margin-bottom:0}
.form-switch-toggle *{box-sizing:border-box}
.form-switch-toggle a{display:block;transition:all .3s ease-out 0}
.form-switch-toggle label,.form-switch-toggle > span{line-height:30px;vertical-align:middle}
.form-switch-toggle label{font-weight:700;margin-bottom:2px;max-width:100%;color:#555}
.form-switch-toggle input{position:absolute;opacity:0;z-index:5}
.form-switch-toggle input:checked ~ a{right:2%}
.form-switch-toggle > span{position:absolute;left:-100px;width:100%;margin:0;padding-right:100px;text-align:left}
.form-switch-toggle > span span{position:absolute;left:2px;z-index:5;display:block;width:50%;margin-left:100px;text-align:center}
.form-switch-toggle > span span:last-child{left:50%}
.form-switch-toggle a{position:absolute;right:48%;top:48%;z-index:4;display:block;width:50%;height:24px;border-radius:12px;padding:0;transform:translateY(-48%)}
.form-blue-onoffswitch{position:relative;width:70px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none}
.form-blue-onoffswitch-checkbox{position:absolute;height:28px;width:70px;opacity:0}
.form-blue-onoffswitch-label{display:block;overflow:hidden;cursor:pointer;border-radius:50px}
.form-blue-onoffswitch-inner{display:block;width:200%;margin-left:-100%}
.form-blue-onoffswitch-inner:before,.form-blue-onoffswitch-inner:after{display:block;float:left;width:50%;height:30px;padding:0;line-height:30px;font-size:12px;color:#fff;font-family:Trebuchet,Arial,sans-serif;font-weight:700;box-sizing:border-box}
.form-blue-onoffswitch-inner:before{content:"YES";padding-left:13px;background-color:#00549a;color:#FFF}
.form-blue-onoffswitch-inner:after{content:"NO";padding-right:14px;background-color:#003778;color:#FFF;text-align:right}
.form-blue-onoffswitch-switch{display:block;width:24px;margin:3px;background:#D4D4D4;position:absolute;top:0;bottom:0;border-radius:50px;transition:all .3s ease-in 0}
.form-blue-onoffswitch-checkbox:checked + .form-blue-onoffswitch-label .form-blue-onoffswitch-inner{margin-left:0}
.form-blue-onoffswitch-checkbox:checked + .form-blue-onoffswitch-label .form-blue-onoffswitch-switch{right:0;background:#fff}
/* END CHECKBOX SWITCH*/


/*START RANGE SLIDER*/.rangeslider,.rangeslider__fill{display:block;-moz-box-shadow:inset 0 1px 3px rgba(0,0,0,0.3);-webkit-box-shadow:inset 0 1px 3px rgba(0,0,0,0.3);box-shadow:inset 0 1px 3px rgba(0,0,0,0.3);-moz-border-radius:10px;-webkit-border-radius:10px;border-radius:10px}
.rangeslider{background:#fff;position:relative;-moz-box-shadow:inset 0 1px 3px 0 rgba(0,0,0,0.2);-webkit-box-shadow:inset 0 1px 3px 0 rgba(0,0,0,0.2);box-shadow:inset 0 1px 3px 0 rgba(0,0,0,0.2)}
.rangeslider--horizontal{height:10px;width:100%}
.rangeslider--vertical{width:20px;min-height:150px;max-height:100%}
.rangeslider--disabled{filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=40);opacity:.4}
.rangeslider__fill{background:#00549A;position:absolute}
.rangeslider--horizontal .rangeslider__fill{top:0;height:100%}
.rangeslider--vertical .rangeslider__fill{bottom:0;width:100%}
.rangeslider__handle{background:#fff;cursor:pointer;display:inline-block;width:24px;height:24px;position:absolute;-moz-box-shadow:inset 0 1px 3px 0 rgba(0,0,0,0.14);-webkit-box-shadow:inset 0 1px 3px 0 rgba(0,0,0,0.14);box-shadow:inset 0 1px 3px 0 rgba(0,0,0,0.14);-moz-border-radius:50%;-webkit-border-radius:50%;border-radius:50%;background:#003778}
.rangeslider__handle:after{content:"";display:block;width:12px;height:12px;margin:auto;position:absolute;top:0;right:0;bottom:0;left:0;-moz-border-radius:50%;-webkit-border-radius:50%;border-radius:50%;background:#fff}
.rangeslider__handle:active,.rangeslider--active .rangeslider__handle{background-image:url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4gPHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IGlkPSJncmFkIiBncmFkaWVudFVuaXRzPSJvYmplY3RCb3VuZGluZ0JveCIgeDE9IjAuNSIgeTE9IjAuMCIgeDI9IjAuNSIgeTI9IjEuMCI+PHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzAwMDAwMCIgc3RvcC1vcGFjaXR5PSIwLjEiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMwMDAwMDAiIHN0b3Atb3BhY2l0eT0iMC4xMiIvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZCkiIC8+PC9zdmc+IA==);background-size:100%;background-image:-webkit-gradient(linear,50% 0%,50% 100%,color-stop(0%,rgba(0,0,0,0.1)),color-stop(100%,rgba(0,0,0,0.12)));background-image:-moz-linear-gradient(rgba(0,0,0,0.1),rgba(0,0,0,0.12));background-image:-webkit-linear-gradient(rgba(0,0,0,0.1),rgba(0,0,0,0.12));background-image:linear-gradient(rgba(0,0,0,0.1),rgba(0,0,0,0.12))}
.rangeslider--horizontal .rangeslider__handle{top:-8px;touch-action:pan-y;-ms-touch-action:pan-y}
.rangeslider--vertical .rangeslider__handle{left:-10px;touch-action:pan-x;-ms-touch-action:pan-x}
input[type="range"]:focus + .rangeslider .rangeslider__handle{-moz-box-shadow:0 0 8px rgba(255,0,255,0.9);-webkit-box-shadow:0 0 8px rgba(255,0,255,0.9);box-shadow:0 0 8px rgba(255,0,255,0.9)}
.image-bottom-right{bottom:0;right:0}
/*END RANGE SLIDER*/


/*START FORM CONTROLS*/.form-control{border-radius:0;color:#333}
.form-control-blue.form-control{color:#fff}
.has-error .form-control:focus{border-color:#fe0000}
.has-error .form-control{border-color:#fe0000}
label{font-weight:400}
.radio{margin-bottom:15px}
.has-error .checkbox,.has-error .checkbox-inline,.has-error .control-label,.has-error .help-block,.has-error .radio,.has-error .radio-inline,.has-error.checkbox label,.has-error.checkbox-inline label,.has-error.radio label,.has-error.radio-inline label{color:#fe0000}
.help-block{color:#333}
.variableWidthInput{width:100%}
.variableWidthInput2{width:90%}
.form-control:focus{border-color:#003778}
.form-control.form-control-gray{background-color:#f4f4f4}
.form-control.form-control-blue{background-color:#00549a}
.form-control{display:block;width:100%;height:44px;background-color:#fff;background-image:none;border:2px solid #d4d4d4;padding-left:15px;padding-right:15px;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0);box-shadow:inset 0 1px 1px rgba(0,0,0,.0)}

/*Prepare the select element*/
select{padding:9px;border:1px solid #ccc}

/*IE 10 version fixes*/
select::-ms-expand{display:none}
select::-ms-value{background:none;color:#5a5a5a}
/*IE 10 version fixes*/
.form-control-select{box-shadow:none;-webkit-appearance:none;-moz-appearance:none;cursor:pointer;padding-right:38px}

/*Select box wrapper element*/
.form-control-select-box{position:relative;display:inline-block}
/*Overtlay icon for custom select box*/
.form-control-select-box.select-box-gray:after{background-color:#f0f0f0}
.form-control-select-box.select-box-blue:after{background-color:#00549a;color:#fff}
.form-group.error .form-control-select-box:after{color:#bd2025}
.form-control-select-box:after{font-family:"bell-icon";content:"\e601";font-size:18px;background-color:#fff;color:#00549a;right:2px;top:2px;padding:11px 14px 10px 0;height:44px;position:absolute;pointer-events:none}
TEXTAREA.form-control{min-height:100px;padding-top:10px;padding-bottom:10px}
.form-label{color:#000;display:inline-block;font-weight:700;margin-bottom:10px}
.form-group.error,.form-group.error .form-label,.form-group.error .form-control{color:#BD2025;border-color:#BD2025;transition:border-color .5s cubic-bezier(.55,0,.1,1),color .5s cubic-bezier(.55,0,.1,1)}
.form-inline .form-group,.form-inline .form-control{display:inline-block;width:auto;vertical-align:middle}
.form-control-validated-wrap.success,.form-control-validated-wrap.error{padding-right:60px;display:inline-block;position:relative}
.form-control-validated-wrap .icon{position:absolute;right:0;height:40px;width:40px;top:5px;border:2px solid #555;border-radius:50%;display:none}
.form-control-validated-wrap.success .icon{border-color:#378E42;display:block}
.form-control-validated-wrap.error .icon{border-color:#BD2025;display:block}
.form-control-validated-wrap .icon:before{position:absolute;right:8px;top:9px;font-size:19px}
.form-control-validated-wrap.success .icon:before{color:#378E42}
.form-control-validated-wrap.error .icon:before{color:#BD2025}
.form-padding [class^="col-md"],.form-padding [class*=" col-md"]{padding:0 20px}
.form-padding [class^="col-md"] [class^="col-sm"],.form-padding [class*=" col-md"] [class*=" col-sm"]{padding:0 20px}
.form-item-note-error{padding-top:5px;padding-bottom:10px}
.disabled,.disabled label,.disabled input{opacity:.5;cursor:default}
.error{color:#BD2025;border-color:#BD2025}
.graybg.form-control-select{background-color:#f4f4f4}


@media (max-width: 519.98px) {
    .form-label{margin-bottom:10px}
    .form-control-validated-wrap.success,.form-control-validated-wrap.error{padding-right:0}
    .form-control-validated-wrap.success .form-control,.form-control-validated-wrap.error .form-control{padding-right:50px}
    .form-control-validated-wrap .icon{top:10px;right:10px;height:30px;width:30px}
    .form-control-validated-wrap .icon:before{right:5px;top:5px;font-size:16px;font-weight:700}
    .video-group div{padding-left:0}
}

@media (min-width: 520px) {
    .form-group{display:table;width:100%}
    .form-group > .row{display:table-row}
    .form-group > .form-label-col,.form-group > .form-control-col,.form-group > .row > .form-label-col,.form-group > .row > .form-control-col{display:table-cell;float:none;vertical-align:middle}
    .form-group > .form-label-col[class^="col-"],.form-group > .row > .form-label-col[class^="col-"]{text-align:right;padding-right:40px}
    .form-label-col .form-label{margin-bottom:0}
}
.form-input-group{position:relative}
.form-input-group input{padding-right:46px}
.form-input-group i{position:absolute;right:0;padding:4px 4px 0 0;pointer-events:none;font-size:42px}

/* Steps */
.form-steps{counter-reset:form-steps;list-style:none;margin-top:0;transition:margin .5s cubic-bezier(.55,0,.1,1);padding-left:0;border:1px solid #d4d4d4}
.form-steps > li{background-color:#fff;transition:background-color .5s cubic-bezier(.55,0,.1,1),color .5s cubic-bezier(.55,0,.1,1);font-family:'bell-slim',Helvetica,Arial,sans-serif;font-size:22px;font-weight:700}
.form-steps > li[class*="col-"]{padding:20px}
.form-steps > li:before{counter-increment:form-steps;content:counter(form-steps) '. '}
.form-steps > li.active{background-color:#00549a;color:#fff}

/* fix vertical alignment of text inside the select element */
.form-control-select{padding-bottom:9px;padding-top:11px;padding-left:15px;height:44px;line-height:18px;font-size:14px}
/* default select font color for IE Edge */
select.form-control::-ms-value{color:#333}
/* default seect arrow styles use a next sibling span for the arrow styles are copied from bell.css pseudoelement arrow */
.form-control-select + span {font-family: "bell-icon";font-size: 18px;background-color: transparent;right: 2px;top: 2px;padding: 11px 14px 10px 0;height: 44px;position: absolute;pointer-events: none;color: #00549a;padding: 10px 9px 6px 0;height: 41px;top: 0}

/* blue select font color for IE Edge */
.form-control-select-box.select-box-blue .form-control-select::-ms-value{color:#fff}
/* blue select arrow color */
.form-control-select-box.select-box-blue .form-control-select + span{color:#fff}
/* error select font color for IE Edge */
.form-group.error select.form-control::-ms-value{color:#bd2025}
/* error select arrow color */
.form-group.error .form-control-select-box .form-control-select + span{color:#bd2025}
/* remove bell.css pseudoelement arrow */
.form-control-select-box:after{display:none}
@media (max-width: 991.98px) {
.form-control-select-box:after{font-size:16px;padding:11px 20px 10px 0}
.form-steps > li + li[class*="col-"]{border-top-width:1px}
.form-steps > li + li[class*="col-"]:not(.active){border-top-width:1px}
}

@media (min-width: 992px) {
.form-steps > li + li[class*="col-"]:not(.active){border-left-width:1px}
}

/* Required */
.form-required:before{content:'*';display:inline-block;margin-right:.25em;font-weight:700;/*color:#BD2025*/}

/* Date */
@media (max-width: 519.98px) {
.form-control-date a{display:block;margin-top:10px}
}
@media (min-width: 520px) {
.form-control-date input[type="text"]{width:50%}
.form-control-date a{position:absolute;bottom:55px;left:calc(50% + 15px)}
}

/* Edit */
.form-review-edit{position:absolute;top:18px;right:0;transition:transform .5s cubic-bezier(.55,0,.1,1);width:45px;height:45px;border-radius:50%;background-color:#00549a;font-size:54px;color:#fff}
.form-review-edit i,.form-review-edit i:before{display:block}
.form-review-edit i{position:relative;width:100%;height:100%}
.form-review-edit i:before{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%);font-size:48px;color:#fff}
@media (max-width: 519.98px) {
.form-review-edit{-webkit-transform:translate(-30px,25%);-ms-transform:translate(-30px,25%);transform:translate(-30px,25%)}
}
@media (min-width: 520px) and (max-width: 639.98px),(min-width: 992px) and (max-width: 1199.98px) {
.form-review-edit{-webkit-transform:translate(-20px,25%);-ms-transform:translate(-20px,25%);transform:translate(-20px,25%)}
}
@media (min-width: 640px) and (max-width: 991.98px),(min-width: 1200px) {
.form-review-edit{-webkit-transform:translate(-40px,25%);-ms-transform:translate(-40px,25%);transform:translate(-40px,25%)}
}
/*END FORM CONTROLS*/

/*START CUSTOM SCROLLBAR*/
.scrollbar-content{padding:0 10px 0 30px}
.scrollAdjust::-webkit-scrollbar{width:5px}
.scrollAdjust::-webkit-scrollbar-track{-webkit-border-radius:8px;border-radius:8px;background:#e1e1e1}
.scrollAdjust::-webkit-scrollbar-thumb{height:40px;-webkit-border-radius:8px;border-radius:8px;background:#003778}
.scrollAdjust{overflow:scroll;overflow-x:hidden}
/*END CUSTOM SCROLLBAR*/

/*START FORMS NOTIFICATION*/.notification{position:relative;padding-left:55px}
.notification span.icon:nth-of-type(1){content:"";display:block;position:absolute;width:40px;height:40px;border:2px solid #0066a4;border-radius:50%;left:0;top:-10px}
.notification span.icon.icon_hcontrast{border-color:#009afe}
.notification.notification_multiline span.icon{top:auto}
.notification span.icon.icon_hcontrast:before{color:#009afe}
.notification span.icon:before{position:absolute;left:8px;top:8px;font-size:20px;line-height:1;color:#0066a4}
.notification.success span.icon{border-color:#378E42}
.notification.success span.icon:before{color:#378E42}
.notification.warning span.icon{border-color:#DFA32A}
.notification.warning .icon:before{color:#DFA32A}
.notification.error{color:inherit}
.notification.error span.icon{border-color:#BD2025}
.notification.error span.icon:before{color:#BD2025}
/*END FORMS NOTIFICATION*/

/*START BUTTONS */
.btn-primary,.btn-primary:active,.btn-primary:not(:disabled):not(.disabled):active,.btn-primary:focus{color:#003778;background-color:transparent;border:2px solid #003778;font-size:15px;padding:8px 30px;text-align:center;cursor:pointer; line-height: 17px;}
.btn-primary:hover,.btn-primary:active:focus{color:#00549a;border-color:#00549a;background-color:#ccd7e4}
.btn-primary.disabled,.btn-primary.disabled:hover,.btn-primary.disabled:focus{color:#babec2;background-color:transparent;border:2px solid #babec2;cursor:default}
.btn-primary-white,.btn-primary-white:active,.btn-primary-white:focus{color:#003778;background-color:transparent;border:2px solid #fff;font-size:15px;padding:10px 32px;text-align:center;cursor:pointer;color:#fff}
.btn-primary-white:hover,.btn-primary-white:active:focus{color:#d4dce8;border-color:#d4dce8;background-color:#3376ae;color:#fff}
.btn-primary-white.disabled,.btn-primary-white.disabled:active,.btn-primary-white.disabled:focus{color:#6698c2;border:2px solid #6698c2}
/*Button override from shop*/
.btn-primary {
    /*background-color: #fff;*/
}
.btn-default-white{color:#003778;background-color:#fff;border:2px solid #fff;font-size:15px;padding:10px 32px;text-align:center;cursor:pointer}
.btn-default-white:hover,.btn-default-white:focus,.btn-default-white:active:focus{color:#003778;background-color:#d4dce8;border-color:#d4dce8}
.btn-default-white.disabled,.btn-default-white.disabled:active,.btn-default-white.disabled:focus{background-color:#6698c2;border-color:#6698c2}
.btn-default-white.connector-btn-small{font-size:14px;padding:9px 20px}


/*button added for widget*/
.btn-error{color:#fff;background-color:#BD2025;border:2px solid #BD2025;font-size:18px;padding:12px 32px;text-align:center;cursor:pointer}
.btn-error:hover,.btn-error:focus{color:#fff}
/*button added for widget*/
.btn-default{color:#fff;background-color:#003778;border:2px solid #003778;font-size:15px;padding:10px 28px;text-align:center;cursor:pointer}
.btn-default:hover,.btn-default:active:hover,.btn-default.active:hover,.open > .dropdown-toggle.btn-default:hover,.btn-default:focus,.btn-default:active:focus,.btn-default.active:focus,.open > .dropdown-toggle.btn-default:focus,.btn-default:active.focus,.btn-default.active.focus,.open > .dropdown-toggle.btn-default.focus{color:#fff;background-color:#00549a;border-color:#00549a}
.btn-default.disabled,.btn-default.disabled:hover,.btn-default.disabled:focus{background-color:#babec2;border:2px solid #babec2;cursor:default}
.btn-default-blue{color:#fff;background-color:#00549a;border:2px solid #00549a;font-size:15px;padding:10px 32px;text-align:center;cursor:pointer}
.btn-default-blue:hover,.btn-default-blue:active:hover,.btn-default-blue.active:hover,.open > .dropdown-toggle.btn-default-blue:hover,.btn-default-blue:focus,.btn-default-blue:active:focus,.btn-default-blue.active:focus,.open > .dropdown-toggle.btn-default-blue:focus,.btn-default-blue:active.focus,.btn-default-blue.active.focus,.open > .dropdown-toggle.btn-default-blue.focus{color:#fff;background-color:#003778;border-color:#003778}
.btn-default-blue.disabled,.btn-default-blue.disabled:hover,.btn-default-blue.disabled:focus{background-color:#babec2;border:2px solid #babec2;cursor:default}
.btn-topNav{background-color:transparent;border:2px solid #fff;color:#fff;display:block;margin-top:-54px;font-size:14px;font-weight:400}
.btn-topNav:hover{background-color:#3376ae;border:2px solid #fff;color:#fff}
.btn-topNav:focus{color:#fff}
.btn.btn-modalSubNav{border-radius:6px;font-size:13px;padding:1px 15px;font-weight:700;color:#2390B8;background-color:#e2e2e2}
.btn.btn-modalSubNav:hover,.btn.btn-modalSubNav.active,.btn.btn-modalSubNav:focus{color:#FFF;background-color:#727272;-webkit-box-shadow:inset 0 2px 13px 0 rgba(0,0,0,0.75);-moz-box-shadow:inset 0 2px 13px 0 rgba(0,0,0,0.75);box-shadow:inset 0 2px 13px 0 rgba(0,0,0,0.75);outline:none}
.variableWidthButton{float:left;width:auto}
.btn-back-arrow{color:#fff}
.btn-back-arrow:hover,.btn-back-arrow:active,.btn-back-arrow:focus{color:#c2cedf}
button.btn.disabled,fieldset[disabled] button.btn{pointer-events:none}
.btn.disabled.tooltip-interactive{opacity:1;pointer-events:auto!important}

/*Button changes from shop*/
.btn {
    border-radius: 20px;
    line-height: 17px;
    padding: 7px 30px;
}

/*END BUTTONS */

/*Generic helper responsive*/
@media (min-width: 768px) {
    .big-title, .title {
        font-size: 32px;
        letter-spacing: -.5px;
        line-height: 38px;
    }

    .small-title {
        font-size: 24px;
        line-height: 26px;
    }

    .btn-small-sm-up {
        padding: 7px 28px !important;
        line-height: 18px;
    }
}

@media (min-width: 992px) {
    .big-title {
        font-size: 40px;
        letter-spacing: -.7px;
        line-height: 46px;
    }
}


/*START FOOTER*/
hr{border-color:#bcbdbf}
.footerList a,.footerList span{display:inline-block;padding-right:25px}
ul.footerList li a{text-decoration:none}
.footerIcon{padding-right:25px;float:right}

footer .btn-primary {
    background-color: transparent;
}

footer .btn {
    height: auto;
}

.footer-links-three-columns {
    column-count: 3;
    column-width: 203px;
    column-gap: 30px;
}

.footer-select-province{border:none;background:none;padding:0;border-radius:0;width:auto;padding-top:8px}
/*END FOOTER*/

.hidden-tooltip-target-xs{display:inline-block}
.hidden-tooltip-target-lg{display:none}
.modal.modal-tooltip .modal-header{border-bottom:0}

/*START Accordion*/
/*accordion with scroll*/
.accordion-scrollable-2[aria-expanded="false"]{display:block;height:55px!important;overflow-y:auto}
.accordion-scrollable-2[aria-expanded="true"]{display:block;height:200px!important;overflow-y:auto}
.accordion-scrollable-2{display:block!important;height:55px;overflow-y:auto;-moz-transition:height .5s ease;-webkit-transition:height .5s ease;-o-transition:height .5s ease;transition:height .5s ease}
.accordion-group a:hover,.accordionButton a:hover,.accordion-group a:focus,.accordionButton a:focus{text-decoration:none}
.accordion-group a:not(.txtNoUnderline):hover div:nth-child(2),.accordionButton a:not(.txtNoUnderline):hover div:nth-child(2){text-decoration:underline}
.accordion-group a:not(.txtNoUnderline):focus div:nth-child(2),.accordionButton a:not(.txtNoUnderline):focus div:nth-child(2){text-decoration:underline}
.expand-info-toggle[aria-expanded="true"] .icon-expand-small,.expand-info-toggle[aria-expanded="false"] .icon-collapse-small{display:none}
.expand-info-toggle[aria-expanded="true"] .icon-collapse-small,.expand-info-toggle[aria-expanded="false"] .icon-expand-small{display:inline-block}

/*Simple accordion expandcollapse icon toggle*/
.accordionButton.open .icon-exapnd-outline-circled:before{content:"\e90e"}
.icon-expand-bold.icon-collapse-outline-circled:before{content:"\e96b"}

/*ShowHide Accordion Pure CSS*/
.new-list-item{display:none}
.show-list-item{display:none}
.hide-list-item:target + .show-list-item{display:inline}
.hide-list-item:target{display:none}
.hide-list-item:target ~ .new-list-item{display:block}
.read-more-toggle[aria-expanded=false] .read-more-show-text,.read-more-toggle[aria-expanded=true] .read-more-hide-text{display:inline}
.read-more-toggle[aria-expanded=false] .read-more-hide-text,.read-more-toggle[aria-expanded=true] .read-more-show-text{display:none}
.read-more-toggle[aria-expanded=false] ~ .read-more-target{display:none}
.read-more-toggle[aria-expanded=true] ~ .read-more-target{display:block}
/*End Accordion*/

/*START pagination*/
.pagination > li > a,.pagination > li > span{border-radius:50%;border:0;background:none}
.pagination > .active > a,.pagination > .active > a:focus,.pagination > .active > a:hover,.pagination > .active > span,.pagination > .active > span:focus,.pagination > .active > span:hover{background-color:#00549a;border-color:#00549a;color:#fff}
.pagination > li:first-child > a,.pagination > li:first-child > span,.pagination > li:last-child > a,.pagination > li:last-child > span{border-top-left-radius:50%;border-bottom-left-radius:50%;background:none}
.pagination > li:first-child > a:focus,.pagination > li:first-child > span:focus,.pagination > li:last-child > a:focus,.pagination > li:last-child > span:focus{background:none}
.pagination > li:first-child > a:hover,.pagination > li:first-child > span:hover,.pagination > li:last-child > a:hover,.pagination > li:last-child > span:hover{background:none}
.pagination > li > a:hover,.pagination > li > span:hover,.pagination > li > a:focus,.pagination > li > span:focus{z-index:2;color:#23527c;background-color:#eee;border-color:#ddd}
.pagination > .active > a,.pagination > .active > a:focus,.pagination > .active > a:hover,.pagination > .active > span,.pagination > .active > span:focus,.pagination > .active > span:hover{cursor:default}
.pagination > li > a,.pagination > li > span{position:relative;float:left;padding:6px 12px;line-height:1.42857143;color:#337ab7;text-decoration:none}
/*END Pagination*/

/*wide Pagination*/
.pagination-num-wide .disable a{color:#BABEC2}
.pagination-num-wide li.active a{background-color: #00549a;}
.pagination-num-wide li.active a:focus, .pagination-num-wide li.active a:hover{background-color: #3376ae;  cursor: pointer;}
.pagination-num-wide ul.pagination li:not(.active) a:focus, .pagination-num-wide ul.pagination li:not(.active) a:hover{background-color: #eee}
@media (min-width: 320px) and (max-width:767.98px) {
    .pagination-num-wide{margin-left:-15px;margin-right:-15px}
    .pagination-num-wide .prev-option .option-arrow{padding:15px}
    .pagination-num-wide .next-option .option-arrow{padding:15px}
    .pagination-num-wide li:not(:first-child){     padding-left: 5px;}
}
/*wide Pagination end*/

/*Price format input*/
.input-symbol:after{position:absolute;top:40px;left:40px;content:"$"}
.price-format-input.form-control{padding-left:30px}
/*END Price format input*/

/*Simple header*/
.spacerHeader{height:80px}
.bellBlueBanner{background:rgba(0,0,0,0) radial-gradient(circle at center center,#00549a 0px,#003778 60%,#003778 30%);height:446px}
.simplified-header{background:#00549a none repeat scroll 0 0;box-shadow:0 10px 39px 0 rgba(0,0,0,0.2);height:75px;position:relative;text-align:center;z-index:50}
.simplified-header-area-title{color:#fff;font-size:24px;line-height:3.1;letter-spacing:-1px;margin:0 auto;overflow:hidden;position:relative;text-overflow:ellipsis;white-space:nowrap;left:-30px;width:50%}
.simplified-header-back{left:0;position:absolute;right:auto;top:0;width:55px;padding:20px 10px}
.bell-header-bg{background:#00549a;height:75px}
.bell-header-res-bg{background:#00549a;height:75px}
.bell-header-res-back,.bell-header-res-back:visited{color:#fff}
.bell-header-res-back:hover,.bell-header-res-back:visited:hover{color:#c2cedf}
.bell-header-res-back-centeralign{line-height:2.2}
.page-header-simplified-mobile{line-height:1.2em}
.simple-blue-header{background-color:#00549A;box-shadow:0 0 50px 0 rgba(0,0,0,0.2)}
@media screen and (max-width: 767.98px) {
.bell-header-res-bg{height:54px}
.page-header-simplified-mobile{font-weight:400;font-family:"Helvetical",Arial,sans-serif;letter-spacing:0}
.simple-blue-header{box-shadow: inset 0.7px 0.7px 2px 0 rgba(0,0,0,0.24), 1px 2px 4px 0 rgba(0,0,0,0.24);}
}
@media screen and (max-width: 991.98px) {
.simple-blue-header{height:54px}
}
@media  screen and (min-width: 992px) {
.simple-blue-header{height:75px}
}
/*END Simplified header*/


/*Federal Bar*/
.federal-bar{background:#2d2e33 none repeat scroll 0 0;height:33px;padding:10px 0}
.federal-bar-links.federal-bar-links_left{float:left}
.federal-bar-links{display:inline-block;font-size:11px;text-transform:uppercase}
.federal-bar-links a,.federal-bar-links a:link,.federal-bar-links a:visited{color:#babec2;text-decoration:none}
.federal-bar-links.federal-bar-links_left .txt-white-active{color:#fff;text-decoration:none}
.federal-bar-links.federal-bar-links_left a:hover,.federal-bar-links.federal-bar-links_right a:hover{color:#fff;text-decoration:none}
.federal-bar-links.federal-bar-links_left > a{margin-right:15px}
.federal-bar-links.federal-bar-links_right{float:right}
.federal-bar-links a,.federal-bar-links a:link,.federal-bar-links a:visited{color:#babec2;text-decoration:none}
.federal-bar-links.federal-bar-links_right > a{margin-left:15px}
.federal-bar-links a,.federal-bar-links a:link,.federal-bar-links a:visited{color:#babec2;text-decoration:none}
.federal-bar-links.federal-bar-links_right > a.footer-header-current-language{margin-left:0}
.footer-header-current-province::after{background-color:#d4d4d4;content:"";display:inline-block;height:12px;margin:-2px 7px 0;vertical-align:middle;width:1px}
.federal-bar-select-provinces{margin-right:7px}

.footer-header-current-language::before{background-color:#d4d4d4;content:"";display:inline-block;height:12px;margin:-2px 7px 0 0;vertical-align:middle;width:1px}
@media screen and (max-width: 991.98px) {
    .footer-header-current-language::before{content: none;}
}
.footer-header-current-language.hide-line::before {
    width: 0;
}
/*End federal bar*/


/*Custom selectbox*/
.bell-custom-select{display:inline-block;position:relative;z-index:2}
.bell-custom-select.focused .custom-select-trigger{border:2px solid #96b8ef;border-radius:3px}
.bell-custom-select button{padding:0}
.bell-custom-select > select{display:block;position:absolute;z-index:3;top:0;left:0;width:100%;height:100%;opacity:0;cursor:pointer;-webkit-appearance:none}
/*IE 9+ Fixes*/
@media all and (-ms-high-contrast: none),(-ms-high-contrast: active) {
.bell-custom-select > select{top:15px;height:45%}
}
@media screen and (min-width:0\0) {
.bell-custom-select > select{top:15px;height:45%}
}
.custom-select-trigger{background-color:#fff;border:2px solid #e2e2e2;outline:0;width:100%;text-align:left;cursor:pointer}
.custom-select-trigger-label{margin:15px 45px 15px 15px;display:block}
.custom-select.custom-select_nowrap .custom-select-trigger-label{overflow:hidden;white-space:nowrap;text-overflow:ellipsis;display:block}
.custom-select-trigger > .icon{position:absolute;top:50%;right:15px;color:#00549a;-webkit-transform:translateY(-50%);-ms-transform:translateY(-50%);transform:translateY(-50%)}
.error .custom-select-trigger{border-color:#BD2025}
.error .icon-select-trigger{color:#BD2025}
.custom-select_light-on-dark .custom-select-trigger{border-color:#b0afb4;background-color:transparent;font-size:16px;color:#fff}
.custom-select_light-on-dark .icon-select-trigger{color:#fff}
/*End Custom Select box*/

/*Custom hamburger select form mobile devices*/
select.custom-selection{-webkit-appearance:none;-moz-appearance:none;cursor:pointer;color:#fff;background-color:#00549a;padding:15px;padding-top:10px;padding-bottom:10px;height:50px;font-size:15px}
select.custom-selection::-ms-value{color:#fff}
option.tab_selection{background-color:#fff;color:#555;font-size:14px}
div.selection-box{position:relative}
div.selection-box:after{font-family:"bell-icon";content:'\e618';font-size:21px;background-color:#003778;color:#fff;right:0;top:1px;padding:10px 18px;height:48px;position:absolute;pointer-events:none}
div.selection-box:before{content:'';position:absolute;pointer-events:none;display:block}
div.selection-box.search-arrow-down:after{content:"\e618"}
/*END custom hamburger select form mobile devices END*/
 
/*Start 5050 Columns content containers*/
.fifty-fifty-column .icon-list,.fifty-fifty-column .icon-list-txtBlue{margin:0;padding:0;list-style:none!important}
.fifty-fifty-column .icon-list > li,.fifty-fifty-column .icon-list-txtBlue > li{margin-left:1.8em;position:relative;padding-bottom:7px}
.fifty-fifty-column .icon-list > li:last-child,.fifty-fifty-column .icon-list-txtBlue > li:last-child{padding-bottom:0}
.fifty-fifty-column .icon-list > li:before{font-family:'bell-icon';content:"\e603";position:absolute;top:2px;left:-2.3em;font-size:10px;font-weight:700;color:#fff}
.fifty-fifty-column .icon-list-txtBlue > li:before{color:#00549a;font-family:'bell-icon';content:"\e603";position:absolute;top:5px;left:-2.3em;font-size:10px;font-weight:700}
.content-container > [class^="col-"],.content-container > [class*=" col-"]{padding:40px}
.fifty-fifty-column .pad-v-40{padding-top:40px!important;padding-bottom:40px!important}
@media (max-width:767.98px) {
.content-container > [class^="col-"],.content-container > [class*=" col-"]{padding:30px 20px;padding:30px 20px}
}
@media (min-width: 520px) and (max-width: 991.98px) {
.iWhyBell-simple .image-bottom-right{right:70px}
}

/*Start 5050 Columns contemt containers*/

/*Start Search Bar */
/* Search Form */
.search-bar [type="search"]{position:relative;width:100%;padding-right:175px;padding-left:40px;border:0;background-color:#fff;color:#555;height:70px;border-radius:60px;display:inline-block;font-size:18px;border:1px solid #c8c8c8;-webkit-box-shadow:inset 1px 1px 2px 1px rgba(240,235,240,1);-moz-box-shadow:inset 1px 1px 2px 1px rgba(240,235,240,1);box-shadow:inset 1px 1px 2px 1px rgba(240,235,240,1)}
.search-bar [type="search"]::-ms-clear{display:none}
.search-bar [type="reset"]{position:relative;float:right;margin-top:-45px;background:none;border:0;right:135px;width:40px;display:none}
.search-bar [type="submit"]{position:relative;margin-top:-70px;padding:22px 0;border:0;float:right}
.search-bar [type="reset"]:focus:after,.search-bar [type="submit"]:focus:after{color:#00549A}
.search-bar [type="reset"]:after,.search-bar [type="reset"]:before,.search-bar [type="submit"]:after{display:block;position:absolute;top:50%;left:0}
.search-bar [type="reset"]:after,.search-bar [type="submit"]:after{font-family:'bell-icon';line-height:1}
.search-bar [type="reset"]:focus .icon{opacity:1}
.search-bar [type="reset"] .icon{color:#bbb;font-size:18px}
.search-bar [type="reset"].active{display:block}
.search-bar [type="submit"]{width:45px}
.search-bar [type="submit"]:after{-webkit-transform:translateX(-50%) translateY(-50%);-ms-transform:translateX(-50%) translateY(-50%);transform:translateX(-50%) translateY(-50%);font-size:28px;color:#003778}
.search-bar .search-btn{width:130px;color:#fff;font-size:18px;height:70px;background-color:#003778;border-radius:0 60px 60px 0;-moz-border-radius:0 60px 60px 0;-webkit-border-radius:0 60px 60px 0}
.search-bar input[type="search"]::-webkit-input-placeholder{color:#bebebe}
.search-bar input[type="search"]::-moz-placeholder{color:#bebebe}
.search-bar input[type="search"]:-ms-input-placeholder{color:#bebebe}
.search-bar input[type="search"]:-moz-placeholder{color:#bebebe}
.footer-modal-shadow{-webkit-box-shadow:0 -5px 20px -6px rgba(0,0,0,0.3);-moz-box-shadow:0 -5px 20px -6px rgba(0,0,0,0.3);box-shadow:0 -5px 20px -6px rgba(0,0,0,0.3)}

/* search bar autocomplete*/
.search-bar-wrap .caret_top-lg::after,.search-bar-wrap .caret_top-lg.caret_outline::before,.search-bar-wrap .caret_bottom-lg::after,.search-bar-wrap .caret_bottom-lg.caret_outline::before{left:70px;border-width:22px}
.search-bar-wrap .ui-autocomplete{display:block;float:none;top:215px!important;right:auto;bottom:auto;left:auto;padding:0;transition:height .35s cubic-bezier(.55,0,.1,1),padding .35s cubic-bezier(.55,0,.1,1);background-color:#fff;box-shadow:0 0 40px rgba(0,0,0,.3);position:absolute}
.search-bar-wrap ul.ui-autocomplete > li.ui-menu-item{padding:10px 20px;list-style:none;cursor:pointer}
.search-bar-wrap ul.ui-autocomplete > li.ui-menu-item > a.ui-corner-all{text-decoration:none;color:#555;cursor:pointer;display:block}
.search-bar-wrap ul.ui-autocomplete > li.ui-menu-item:hover{background-color:#e1e1e1}
.search-bar-wrap .ui-autocomplete-term{font-weight:700}
.search-bar-wrap .ui-autocomplete:empty{height:0;padding-top:0;padding-bottom:0}
.search-bar-wrap .ui-autocomplete:empty:after{content:none}
.search-bar-wrap .ui-menu-item,.search-bar-wrap .ui-menu-item > a{color:#000}
.search-bar-wrap .ui-menu-item{margin:2px -4px}
.search-bar-wrap .ui-menu-item > a:hover,.search-bar-wrap .ui-menu-item > a:active{background-color:#e2e2e2}
.search-bar-wrap .ui-menu-item .ui-autocomplete-term{font-weight:700}
.modal .modal-dialog.modal-lg.bell-modal-lg.search-filers-modal{background-color:#00549a}
.modal-header.bgBlueDark{border-bottom:none} 

/*Media queries*/ 
@media(max-width:991.98px) { 
.search-bar .search-btn{background:none!important}
.search-bar [type="submit"]{padding:14px 0;margin-right:-20px}
.search-bar [type="reset"]{right:70px}
.search-bar [type="search"]{padding-right:110px}
}
@media(max-width:767.98px) {
.search-filers-modal .btn.btn-default-white,.search-filers-modal .btn.btn-primary-white{width:100%} 
}
/*END  Search Bar */

/*Start  Custom radio button and checkboxes*/
.graphical_ctrl{position:relative;padding-left:35px}
.graphical_ctrl input{position:absolute;width:48px;z-index:-1;height:48px;opacity:0;top:-16px;left:-9px}
.ctrl_element{position:absolute;top:-3px;left:0;height:24px;width:24px;background:#fff; box-shadow: inset 0 0px 3px 0 rgba(0,0,0,0.2);border:1px solid #ccc;}
.ctrl_radioBtn .ctrl_element{border-radius:50%}
.graphical_ctrl input:checked:focus ~ .ctrl_element,.graphical_ctrl input[type="radio"]:focus ~ .ctrl_element{outline-width:2px;outline-style:solid;outline-color:#4d90fe;box-shadow:0 0 3px 2px rgba(178,209,228,1)}
.graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element{outline-width:1px;outline-style:dashed;outline-color:#4d90fe}
.graphical_ctrl input:checked ~ .ctrl_element{background:#003778;border:1px solid #003778}
.graphical_ctrl input:checked:focus ~ .ctrl_element{background:#003778}
.graphical_ctrl input:disabled ~ .ctrl_element{background:#e6e6e6;opacity:.6;border:1px solid #e6e6e6;pointer-events:none}
.ctrl_element:after{content:'';position:absolute;display:none}
.graphical_ctrl input:checked ~ .ctrl_element:after{display:block}
.graphical_ctrl_checkbox .ctrl_element:after{left:8px;top:4px;width:6px;height:11px;border:solid #fff;border-width:0 2px 2px 0;display:inline-block;-webkit-transform:rotate(45deg);-moz-transform:rotate(45deg);-o-transform:rotate(45deg);transform:rotate(45deg)}
.graphical_ctrl_checkbox input:disabled ~ .ctrl_element:after{border-color:#7b7b7b}
.ctrl_radioBtn .ctrl_element:after{left:5px;top:5px;height:12px;width:12px;border-radius:50%;background:#fff}
.ctrl_radioBtn input:disabled ~ .ctrl_element:after{background:#7b7b7b}
.ctrl_lg .graphical_ctrl{padding-left:40px}
.ctrl_lg .ctrl_element{height:30px;width:30px;top:-5px}
.ctrl_lg .ctrl_radioBtn .ctrl_element:after{left:6px;top:6px;height:16px;width:16px}
.error-ctrl .ctrl_element{border:1px solid #BD2025}
.error-ctrl .graphical_ctrl input:checked ~ .ctrl_element{background-color:#BD2025;border:1px solid #BD2025}
.error-ctrl .error_radio_lg .ctrl_radioBtn .ctrl_element:after{top:6px;left:6px}
.error-ctrl .ctrl_radioBtn .ctrl_element:after{top:5px;left:5px}
.chk_radius{border-radius:3px}
.ctrl_lg .graphical_ctrl_checkbox .ctrl_element:after{height:15px;left:10px;top:4px;width:9px}
/*On dark Background*/
label.on-dark-bg{color:#fff}
.graphical_ctrl_checkbox.on-dark-bg .ctrl_element:after{border:solid #fff;border-width:0 2px 2px 0;display:inline-block;-webkit-transform:rotate(45deg);-moz-transform:rotate(45deg);-o-transform:rotate(45deg);transform:rotate(45deg)}
.graphical_ctrl.on-dark-bg input:checked ~ .ctrl_element:after{border:solid #003778;border-width:0 2px 2px 0}
.graphical_ctrl.on-dark-bg input:checked ~ .ctrl_element{background:#fff;border:1px solid #003778}
.selected-rectangle {
    font-weight: bold;
    color: #003778;
} 
@media (max-width: 991.98px) {    
    /*for custom radio button and checkboxs starts*/
    /*graphical_ctrl{padding-left:35px}*/
.ctrl_element{height:24px;width:24px;top:-3px}
.error-ctrl .ctrl_radioBtn .ctrl_element:after,.error-ctrl .error_radio_lg .ctrl_radioBtn .ctrl_element:after{top:6px;left:6px}
.ctrl_radioBtn .ctrl_element:after{left:5px;top:5px;height:12px;width:12px}
.graphical_ctrl_checkbox .ctrl_element:after{left:7px;top:2px;width:8px;height:14px}
    /*for custom radio button and checkboxs ends*/
}

@media (min-width: 320px) and (max-width: 767.98px) {
    .ctrl_radioBtn .ctrl_element {
        top: 14px;
        left: 15px;
    }
     /*boxes for radio buttons*/
    .rectangle {
        padding: 15px 50px;
        border: 1px solid #D4D4D4;
        background-color: #FFFFFF;
        box-shadow: 0 2px 3px 0 rgba(0,0,0,0.1);
    }

    .selected-rectangle {
        border: 2px solid #00549A;
    }
}
/*END  Custom radio button and checkboxes*/


/*LOADER*/
.loading-indicator-circle{display:inline-block;width:37px;height:37px;margin-right:10px;vertical-align:middle;-webkit-animation:spin 1.1s linear infinite;-moz-animation:spin 1.1s linear infinite;animation:spin 1.1s linear infinite}
@-moz-keyframes spin {
100%{-moz-transform:rotate(360deg)}
}
@-webkit-keyframes spin {
100%{-webkit-transform:rotate(360deg)}
}
@keyframes spin {
100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}
}
.loader-fixed{width:300px;left:50%;margin-left:-150px;position:fixed;top:45%;padding:20px;z-index:99999;-webkit-box-shadow:0 0 40px rgba(0,0,0,0.4);-moz-box-shadow:0 0 40px rgba(0,0,0,0.4);box-shadow:0 0 40px rgba(0,0,0,0.4)}
.page-loader-msg{display:block;float:right;align-self:center;width:210px}
.loaderOverlayBackground{background:#000 none repeat scroll 0 0;display:none;height:100%;left:0;opacity:.7;position:fixed;top:0;width:100%;z-index:55555}
.pagLoader{display:none}
.loader-container .centerElement{margin:0 auto;display:table}
.loading-mbm-spinner{background-image:url(../../content/img/loader_inner.png);background-repeat:no-repeat;display:inline-block;width:70px;height:70px;vertical-align:middle;-webkit-animation:spin 2s linear infinite;-moz-animation:spin 2s linear infinite;animation:spin 2s linear infinite}
.loaderMBMBG{background-image:url(../../content/img/loader_inner_outer.png);background-repeat:no-repeat;display:inline-block;width:75px;height:75px;top:17px;left:113px;position:absolute}
.mbmLoaderFix{opacity:.9;width:300px;left:50%;margin-left:-150px;position:fixed;top:45%;padding:20px;z-index:99999}
/* End of loader centered within a container */

/* SMALL Loader centered within a container */
.loader-fixed.small-loader{text-align:center;width:auto;margin-left:-50px;max-width:120px;padding:20px 10px;box-shadow:none}
.loading-indicator-circle.square45{width:45px;height:45px}
/* End of SMALL Loader centered within a container */
/*END  LOADER*/

/*BRF Tabs*/
ul.tabs{display:table;table-layout:fixed;padding-left:0;margin-bottom:0;list-style:none}
ul.tabs li.active_tabs{background-color:#00549a;z-index:2}
ul.tabs li.active_tabs label{position:relative;top:-5px}
ul.tabs li.active_tabs .active-tab-top{background-color:#00549a;display:block;height:10px;left:0;opacity:1;position:absolute;top:-10px;width:100%;z-index:-1}
ul.tabs li.active_tabs::before{background:rgba(0,0,0,0) linear-gradient(94deg,#04225e 45%,rgba(4,34,94,0) 50%,rgba(4,34,94,0) 100%);content:"";height:100%;opacity:1;position:absolute;right:-10px;top:0;width:10px;position:absolute;top:0}
ul.tabs li.active_tabs.last-active-tab::before{background:rgba(0,0,0,0) linear-gradient(95deg,#04225e 40%,rgba(4,34,94,0) 35%,rgba(4,34,94,0) 100%);opacity:.35}
.active_tabs::after{content:"";position:absolute;bottom:-15px;border-width:15px 15px 0;border-style:solid;border-color:#00549a transparent;display:block;width:0;left:0;right:0;margin-left:auto;margin-right:auto}
ul.tabs li{border-right:1px solid #092442;text-align:center}
ul.tabs li.last-active-tab{border-right:0 solid transparent}
ul.tabs li{cursor:pointer;padding:20px;background-color:#003778;color:#fff;font-size:16px;display:table-cell;vertical-align:middle;float:none;position:relative}
ul.tabs li > i{font-size:55px;vertical-align:middle;margin:0 auto;display:table}
ul.tabs.tabs_vertical li a{color:#c2cedf;word-break:normal}
ul.tabs.tabs_vertical li.active_tabs a{color:#fff}
ul.tabs.tabs_vertical li{display:block;padding:20px 40px;text-align:left;font-weight:400;font-size:24px;letter-spacing:-.6px;line-height:1.4;margin:0;font-family:"bellslim_mediumregular",Helvetica,Arial,sans-serif;border-bottom:1px solid #01215e}
ul.tabs.tabs_vertical li:before,ul.tabs.tabs_vertical li.active_tabs:before{font-family:'bell-icon';color:#fff;content:"\e012";position:absolute;right:20px;top:50%;-webkit-transform:translateY(-50%);-ms-transform:translateY(-50%);transform:translateY(-50%);font-size:18px;background:none;height:inherit;width:inherit}
ul.tabs.tabs_vertical li.active_tabs.active_tabs::after{border-color:transparent}
.tabH{display:inline-block}
.tab_container{border-top:none;clear:both}
.tab-content{display:none}
.tab-content.first{display:block}

/*BRF Tabs small*/
ul.tabs-small li{padding:0;height:65px;font-size:15px}
@media (min-width: 768px) {
ul.tabs-small li.active_tabs,ul.tabs-small li.active_tabs .active-tab-top{padding-bottom:10px}
}


/* Start of Tabs 2  with accessibility */
.tabs2 .tabs{display:table;table-layout:fixed;padding-left:0;margin-bottom:0;list-style:none}
.tabs2 #tabs2-without-icon.tabs.tabs-small [role="tab"]{padding:0;height:65px;font-size:15px}
.tabs2 .tabs .active_tabs[role="tab"]::before{background:rgba(0,0,0,0) linear-gradient(94deg,#04225e 45%,rgba(4,34,94,0) 50%,rgba(4,34,94,0) 100%);content:"";height:100%;opacity:1;position:absolute;right:-10px;top:0;width:10px}
.tabs2 .tabs .active_tabs[role="tab"] .active-tab-top{background-color:#00549a;display:block;height:10px;left:0;opacity:1;position:absolute;top:-10px;width:100%;z-index:-1}
.tabs2 .tabs [role="tab"]{cursor:pointer;padding:20px;background-color:#003778;color:#fff;font-size:16px;display:initial;vertical-align:bottom;float:none;position:relative;border:none;flex:1;border-right:1px solid #092442;text-align:center}
.tabs2 .tabs [role="tab"] i{font-size:55px;vertical-align:middle;margin:0 auto;display:table}
.tabs2 .tabs .active_tabs{background-color:#00549a;z-index:2}
/* End of Tabs 2 */

/* Start of Tabs on shop/tv/internet */
.header-tab-control li a {text-decoration: none}
.side-tab-control .header-tab-control,.tab-control{background:#f4f4f4}
.tab-control .header-tab-control,.tablist-underlined{overflow-x:auto}
.side-tab-control .header-tab-control ul{display:flex}
.tab-control .header-tab-control ul,.tablist-underlined{padding-top:30px;white-space:nowrap}
.tab-control .header-tab-control ul li,.tablist-underlined [role=tab]{display:inline-block}
.tab-control .header-tab-control ul li:not(:last-child),.tablist-underlined [role=tab]:not(:last-child){margin-right:30px}
.tab-control .header-tab-control ul li:last-child,.tablist-underlined [role=tab]:last-child{margin-right:15px}
.tablist-underlined a[role=tab]{text-decoration:none}
.tab-control .header-tab-control ul li a.active,.tab-control .header-tab-control ul li a:focus,.tab-control .header-tab-control ul li a:hover,.tab-control .header-tab-control ul li a[aria-current]:not([aria-current=false]),.tablist-underlined [role=tab].active,.tablist-underlined [role=tab]:focus,.tablist-underlined [role=tab]:hover,.tablist-underlined [role=tab][aria-selected]:not([aria-selected=false]){border-bottom:4px solid #00549a;padding-bottom:11px}
.header-tab-control ul li a.active,.header-tab-control ul li a[aria-current]:not([aria-current=false]),.tablist-underlined [role=tab].active,.tablist-underlined [role=tab][aria-selected]:not([aria-selected=false]){color:#111}
.tab-panels-container [role=tabpanel][tabpanel-selected=false]:not(.slick-slide){display:none}
.side-tab-control .header-tab-control ul{border-left:1px solid #e1e1e1;border-right:1px solid #e1e1e1;flex-direction:column;overflow:hidden}
.side-tab-control .header-tab-control ul li a{border-top:1px solid #e1e1e1}.side-tab-control .header-tab-control ul li:last-child a{border-bottom:1px solid #e1e1e1}
.side-tab-control .header-tab-control ul li a{font-size:16px;justify-content:space-between;padding:20px 15px;width:100%}
.side-tab-control .header-tab-control ul li a.active,.side-tab-control .header-tab-control ul li a:focus,.side-tab-control .header-tab-control ul li a:hover,.side-tab-control .header-tab-control ul li a[aria-current]:not([aria-current=false]){background:#fff}
.side-tab-control .header-tab-control ul li a.active,.side-tab-control .header-tab-control ul li a[aria-current]:not([aria-current=false]){border:1px solid #e1e1e1;border-right:0;box-shadow:0 6px 25px 0 rgba(0,0,0,.12)}
.side-tab-control .header-tab-control ul li a .icon3{font-size:13px;margin-left:20px;width:7px}
.side-tab-control .header-tab-control ul li a.active .icon3,.side-tab-control .header-tab-control ul li a[aria-current]:not([aria-current=false]) .icon3{display:none}
.tablist-pills-container{display:flex;font-size:16px;justify-content:center}
.tablist-pills-container ul{background-color:#f4f4f4;border:1px solid #fff;border-radius:30px;display:flex;flex-direction:row;padding:5px;list-style:none;margin:0;color:#00549a;text-align:center}
.tablist-pills-container ul li{padding:10px 25px;cursor:pointer;display:flex;justify-content:center;align-items:center}
.tablist-pills-container ul li[aria-selected=true]{border:1px solid #e1e1e1;border-radius:30px;background-color:#fff;box-shadow:0 0 25px 0 rgba(0,0,0,.12);color:#111}

@media (max-width: 767.98px) {
    .tablist-pills-container ul li {padding: 10px 20px;}
    .tab-control .header-tab-control ul li:not(:last-child) {margin-right: 20px;}
}

@media (min-width: 768px) {
    .tab-control .header-tab-control ul li,.tablist-underlined [role=tab] {font-size: 18px;line-height: 26px;}
    .tab-control .header-tab-control ul li:last-child,.tablist-underlined [role=tab]:last-child {margin-right: 30px;}
    .side-tab-control .header-tab-control ul {border: 0;}
}

@media (min-width: 992px) {
    .tab-control .header-tab-control ul li:last-child,.tablist-underlined [role=tab]:last-child {margin-right: 16px;}
}
/* END of Tabs on shop/tv/internet */

/*Alert Message Box*/
.alert-msg-middle {
    width: 85%
}
/*END Alert Message Box*/

/*Features Checkboxes*/
.box-shadow-1 {
    box-shadow: 0 2px 3px 0 rgba(0,0,0,0.1);
    -webkit-box-shadow: 0 2px 3px 0 rgba(0,0,0,0.1);
}

.check-select-cta {
    padding-left: 35px
}

.checkbox-selection ~ .ctrl_element {
    top: -5px
}

@media (max-width: 991.98px) {
    .check-select-cta {
        padding-left: 45px
    }
}
/*END Features Checkboxes*/



/*Message Boxes Start*/
.message-box-wrapper {
    position: fixed;
    top: 130px;
    left: 50%;
    z-index: 999;
}

.message-box {
    position: relative;
    left: -50%;
    background: #fff;
    padding: 15px 15px 0px 15px;
    border: 0;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    border-bottom: 5px solid #fff;
    -webkit-box-shadow: 0px 0px 60px 0px rgba(0,0,0,0.3);
    -moz-box-shadow: 0px 0px 60px 0px rgba(0,0,0,0.3);
    box-shadow: 0px 0px 60px 0px rgba(0,0,0,0.3);
    min-width: 160px;
    max-width: 600px;
}

.message-box-warning {
    border-bottom: 5px solid #E4AA1F;
}

.message-box-success {
    border-bottom: 5px solid #339043;
}

.message-box-close-button {
    position: absolute;
    top: 15px;
    margin-top: -5px;
    margin-right: -5px;
    right: 15px;
    font-size: 9px;
}

.message-box-icon {
    position: absolute;
}

.message-box-text {
    word-wrap: break-word;
    padding-left: 35px;
    padding-right: 10px;
    padding-bottom: 5px;
    padding-top: 5px;
}

@media (max-width: 767.98px) {
    .message-box-wrapper {
        margin-right: -100px;
    }
}
/*Message Boxes END*/


/* media queries keep at the bottom*/
@media screen and (min-width: 992px) and (max-width: 999.98px) {
    .back-to-top, .scrollToTop.mobile {
        display: none
    }
}

@media screen and (max-width:999.98px) {
    .simplified-header-area-title {
        left: 0px
    }

    .container-steps {
        float: left;
        width: 960px;
        overflow-x: hidden !important
    }

        .container-steps.overflow-right {
            float: right
        }

    .federal-bar {
        display: none
    }

    .bellSlim-sm {
        font-family: "bellslim_mediumregular", Helvetica, Arial, sans-serif
    }
}

@media screen and (max-width:991.98px) {
    .variableWidthButton {
        float: left;
        width: 100%
    }

    .modal:before, .modal-dialog {
        vertical-align: middle
    }

    .modal.modal-tooltip {
        padding-top: 0px;
    }
}

@media screen and (max-width:767.98px) {
    .modal.modal-tooltip {
        bottom: unset
    }

        .modal.modal-tooltip .tooltip-dialog {
            margin: auto 20px;
            -webkit-transform: translate(0%, -50%);
            transform: translate(0%, -50%);
            position: relative;
            bottom: 50%;
        }

            .modal.modal-tooltip .tooltip-dialog .modal-header {
                padding-bottom: 10px
            }

            .modal.modal-tooltip .tooltip-dialog button.close:focus {
                outline: 1px;
                outline-color: #00549a;
            }

            .modal.modal-tooltip .tooltip-dialog .close {
                padding: 15px;
                font-size: 12px
            }

    .modal-header-blue {
        height: 60px;
        background-color: #00549a;
        padding-top: 22px
    }

    .modal-header-gray {
        height: 60px;
        background-color: #e1e1e1;
        padding-top: 22px
    }

    .sans-serif-xs {
        font-family: "Helvetical",Arial, sans-serif;
        letter-spacing: 0
    }

    .flipFloatLR {
        float: right
    }

    .modal:before, .modal-dialog {
        vertical-align: middle
    }

    .modal.modal-tooltip {
        padding-top: 0px;
    }

    .mobile-progressbar {
        display: block
    }

    .modal .modal-dialog.modal-lg.bell-modal-lg {
        margin: 0px;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100%;
        background-color: #f0f0f0;
        width: 100%
    }

    .modal .modal-dialog.modal-lg-2.bell-modal-lg {
        background-color: #fff
    }

    .modal .modal-dialog.center-screen.modal-md.bell-modal-md {
        margin: 0 auto;
        position: relative;
        width: 92%;
    }

    .modal .modal-dialog.modal-md.bell-modal-md {
        margin: 0;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        top: auto;
        height: auto;
        background-color: transparent;
    }

    .modal-dialog.modal-md.bell-modal-md.bgGray19 {
        background-color: #f4f4f4
    }

    .bell-modal-lg .modal-content, .bell-modal-md .modal-content {
        border-radius: 10px;
        -webkit-box-shadow: 0 0 0px rgba(0,0,0,0);
        -moz-box-shadow: 0 0 0px rgba(0,0,0,0);
        box-shadow: 0 0 0px rgba(0,0,0,0);
    }

    .hidden-tooltip-target-xs {
        display: none
    }

    .hidden-tooltip-target-lg {
        display: inline-block
    }

    .primary-cta {
        display: none
    }
    /*Modal changes from SHOP*/
    .modal .modal-dialog {
        height: auto;
        max-height: calc(100% - 45px);
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
    }
}

@media (max-width:639.98px) {
    .form-control-select-box:after {
        font-size: 16px;
        padding: 11px 6px 10px 0;
    }

    .steps-progress {
        display: none
    }

    .mobile-progressbar {
        display: block
    }
}

@media (min-width:640px) {
    .footerIcon, .footerList a, .footerList span {
        display: inline-block;
        padding-right: 25px
    }

    .footerIcon {
        float: right
    }
}

@media screen and (max-width:640.98px) {
    .card-body {
        padding: 20px
    }

    .simplified-header-area-title {
        left: 0px;
        width: 70%
    }
}

@media (min-width:520px) {
    .footerIcon, .footerList a, .footerList span {
        display: block;
        text-align: center;
        padding: 0 0 15px 0;
        margin: auto
    }

    .footerIcon {
        text-align: center;
        float: none
    }

    .mobile-progressbar {
        width: 100% !important
    }

    .secondary-cta-hidden-xs {
        display: block
    }
}

@media screen and (max-width:520.98px) {
    .simplified-header-area-title {
        left: 0px
    }

    .card-body {
        padding: 20px 20px
    }

    .variableWidthButton {
        float: none;
        width: 100%
    }

    .modal.modal-tooltip {
        position: fixed;
        padding-top: 0px;
        width: 100%;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%)
    }

    .modal-scroll-area {
        padding: 0
    }

    .bellSlim-sm {
        font-family: "Helvetica", Arial, sans-serif
    }

    .secondary-cta-hidden-xs {
        display: none !important
    }

    .primary-cta {
        display: block !important
    }
}

@media (max-width:519.98px) {
    .icon-circle-large:before {
        font-size: 52px
    }

    .footerIcon, .footerList a, .footerList span {
        display: block;
        text-align: center;
        padding: 0 0 10px 0;
        margin: auto
    }

    .footerIcon {
        text-align: center;
        float: none
    }

    .hidden-xs {
        display: none
    }

    .mobile-progressbar {
        display: block
    }
}

@media (min-width:992px) {
    .footerIcon, .footerList a, .footerList span {
        display: inline-block;
        padding-right: 25px
    }

    .footerIcon {
        float: right
    }

    .modal-scroll-area {
        padding: 30px 40px 0 30px
    }
}

@media (min-width:1200px) {
    .variableWidthInput {
        width: 160px
    }

    .variableWidthInput2 {
        width: 160px
    }

    .flipFloatLR {
        float: left
    }

    .flipTxtAlignRL {
        text-align: right
    }

    .modal .modal-lg.bell-modal-lg {
        width: 1190px;
        margin-left: -9px
    }

    .modal-scroll-area {
        padding: 30px 40px 0 30px
    }
}

@media (max-width:1199.98px) {
    .steps-line {
        margin: 0 80px
    }

        .steps-line.two-steps {
            margin: 0 240px
        }

        .steps-line.three-steps {
            margin: 0 160px
        }

        .steps-line.four-steps {
            margin: 0 120px
        }

        .steps-line.five-steps {
            margin: 0 100px
        }

        .steps-line.six-steps {
            margin: 0 80px
        }

    .steps-progress-2 {
        margin: 0 80px;
        width: 160px
    }

    .steps-progress-3 {
        margin: 0 80px;
        width: 320px
    }

    .steps-progress-4 {
        margin: 0 80px;
        width: 480px
    }

    .steps-progress-5 {
        margin: 0 80px;
        width: 640px
    }

    .steps-progress-6 {
        margin: 0 80px;
        width: 800px
    }

    .modal.modal-tooltip {
        position: fixed;
        width: 100%;
        height: 100%;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        overflow: hidden;
    }
}

@media (min-width:1200px) {
    .container {
        padding-right: 0px;
        padding-left: 0px
    }

    .footerIcon, .footerList a, .footerList span {
        display: inline-block;
        padding-right: 25px
    }

    .footerIcon {
        float: right
    }

    .modal .modal-lg.bell-modal-lg {
        width: 1200px;
        margin-left: -9px;
        max-width: 100%
    }

    .modal-scroll-area {
        padding: 30px 40px 0 30px
    }
}

@media (max-width:639.98px) {
    .vPadding20-left-xs {
        padding-left: 20px;
    }
}

/* Start For Datepicker*/
.ui-widget-header {
    background: #00549a !important;
}

.ui-widget-header {
    color: #fff !important;
    font-weight: normal !important;
}

    .ui-widget-header a {
        color: #ffffff !important;
    }

.icon-reversed {
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
    display: inline-block;
    margin-left: 3px;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default, .ui-button, html .ui-button.ui-state-disabled:hover, html .ui-button.ui-state-disabled:active {
    background: #fff;
    border: 0;
}

    .ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active, a.ui-button:active, .ui-button:active, .ui-button.ui-state-active:hover {
        background: #00549a;
        color: #fff;
    }

.ui-datepicker .ui-datepicker-prev {
    /*top: 4px !important;*/
    top: 50% !important;
    transform: translateY(-50%) !important;
    left: 8px !important;
    cursor: pointer;
}

.ui-datepicker .ui-datepicker-next {
    /*top: 4px !important;*/
    top: 50% !important;
    transform: translateY(-50%) !important;
    cursor: pointer;
}

.ui-datepicker .ui-datepicker-next-hover {
    right: 2px !important;
}

.ui-state-hover, .ui-widget-content .ui-state-hover {
    background: #00549a !important;
    font-weight: normal;
    color: #ffffff !important;
}

.ui-datepicker a:focus, .ui-datepicker a:hover {
    text-decoration: none !important;
}

.ui-datepicker th {
    color: #555;
    background-color: #f5f5f5;
    border-bottom: 0px !important;
    font-weight: normal !important;
    padding: .1em .3em !important;
}

.tooltip-datepicker .tooltip-inner {
    padding: 1px !important;
}

.ui-datepicker.ui-widget.ui-widget-content {
    border: 1px solid #D4D4D4 !important;
    box-shadow: 0 0 21px 0 rgba(0,0,0,0.2);
    background: #fff !important;
}

.ui-datepicker .ui-datepicker-header {
    border-radius: 0px !important;
}

.ui-datepicker td span, .ui-datepicker td a {
    text-align: center;
}

.ui-datepicker .ui-datepicker-next {
    /*right: 0px !important;*/
    right: 8px !important;
}

.ui-datepicker-calendar thead tr th:first-child, .ui-datepicker-calendar tbody {
    padding-left: 15px !important;
}

    .ui-datepicker-calendar tbody tr td:first-child {
        padding-left: 15px !important;
    }

    .ui-datepicker-calendar tbody tr td:last-child {
        padding-right: 15px !important;
    }

.ui-datepicker-calendar thead tr th:last-child, .ui-datepicker-calendar tbody {
    padding-right: 15px !important;
}

.ui-datepicker {
    padding: 0px !important;
}

.datePicker {
    padding-top: 27px;
}

.ui-datepicker table {
    background-color: #fff !important;
    margin: 0;
    padding: 4px 0;
}

.ui-icon, .ui-widget-content .ui-datepicker-next .ui-icon, .ui-widget-content .ui-datepicker-prev .ui-icon {
    background-image: none;
    color: transparent;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.ui-datepicker .ui-datepicker-prev span, .ui-datepicker .ui-datepicker-next span {
    /*margin-left: -14px;*/
    margin-left: 0;
    left: 50%;
    transform: translateX(-50%);
}

.ui-datepicker .ui-datepicker-prev span {
    margin-left: -2px;
}

select.ui-datepicker-month,
select.ui-datepicker-year {
    background: #fff /*#dcdcdc*/;
    /* custom dropdown arrow styles below */
    padding-bottom: 9px;
    padding-top: 11px;
    height: 44px;
    line-height: 18px;
    font-size: 14px;
    box-shadow: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
    padding-right: 20px;
    margin: 1px !important;
}

    select.ui-datepicker-month:focus,
    select.ui-datepicker-year:focus {
        border-color: #66afe9;
        outline: 0;
        -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), inset 0 0 3px rgba(102,175,233,.6);
        box-shadow: inset 0 1px 1px rgba(0,0,0,.075), inset 0 0 3px rgba(102,175,233,.6);
    }

    /* custom dropdown arrow styles below */
    select.ui-datepicker-month + span,
    select.ui-datepicker-year + span {
        font-family: "bell-icon";
        font-size: 18px;
        background-color: transparent;
        top: 50%;
        height: auto;
        position: absolute;
        display: block; /* need to be block level for pointer-events none to work on IE and Edge */
        pointer-events: none;
        color: #00549a;
        padding: 0;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    select.ui-datepicker-month + span {
        left: 50%;
        transform: translate(-20px, -50%);
    }

    select.ui-datepicker-year + span {
        left: 90%;
        transform: translate(-40px, -50%);
    }

.ui-datepicker-current.ui-state-default {
    opacity: 1;
}

.ui-state-hover,
.ui-widget-content .ui-state-hover,
.ui-widget-header .ui-state-hover,
.ui-state-focus,
.ui-widget-content .ui-state-focus,
.ui-widget-header .ui-state-focus,
.ui-button:hover,
.ui-button:focus {
    border: none;
}

/*Next Prev icon*/
.ui-datepicker .ui-icon {
    text-indent: inherit;
}

.ui-icon::after, .ui-widget-content .ui-datepicker-next .ui-icon::after {
    font-family: 'bell-icon' !important;
    content: "\e012";
    font-size: 14px;
    background-color: transparent;
    color: #fff;
    right: 0px;
    top: -2px;
    padding: 0;
    height: 24px;
    position: absolute;
    pointer-events: none;
}

.ui-icon::after, .ui-widget-content .ui-datepicker-prev .ui-icon::after {
    font-family: 'bell-icon2' !important;
    content: "\ea04";
    font-size: 14px;
    background-color: transparent;
    color: #fff;
    right: 0px;
    top: -2px;
    padding: 0;
    height: 24px;
    position: absolute;
    pointer-events: none;
}

/*Calendar icon*/
.date-picker {padding-bottom: 9px;padding-top: 11px;padding-left: 15px;height: 44px;line-height: 18px;font-size: 14px}

.ui-datepicker-trigger {
    background: none;
    border: none;
    position: absolute;
    top: 50%;
    transform: translateY(10%);
    width: 24px;
    height: 24px;
    right: 30px;
}

.date-picker-box.margin-30-right .ui-datepicker-trigger {
    margin-right: 22px;
}

.date-picker-box .ui-datepicker-trigger img {
    display: none;
}

.ui-datepicker-trigger::after {
    font-family: "bell-icon2";
    content: '\ea33';
    font-size: 24px;
    background-color: transparent;
    color: #00549a;
    right: 0px;
    top: 0;
    height: 24px;
    position: absolute;
    transform: translateY(-40%)
}

.date-picker-box input[disabled] ~ .ui-datepicker-trigger img, .date-picker-box input::-ms-clear {
    display: none;
}

.date-picker-box input[disabled] ~ .ui-datepicker-trigger::after {
    color: transparent;
    content: '';
}
/* END For Datepicker*/

/* START For Video Gallery Component*/
.video-group span {
    display: block;
    padding: 10px;
    font-size: 12px;
}

.video-group .iframe-container {
    position: relative;
    height: 120px;
    background-position: center !important;
    background-size: cover !important;
    background-repeat: no-repeat;
    width: 100%;
    cursor: pointer;
}

.video-group div.iframe-container .play-icon {
    background-repeat: no-repeat;
    width: 60px;
    height: 60px;
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 48px;
    color: #000;
    z-index: 2;
    opacity: 0.8;
    -webkit-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
}

.video-group img,
.video-group iframe {
    width: 100%;
    height: 100%;
    display: block;
    position: relative;
}

.video-group img {
    z-index: 1;
}

.video-group iframe {
    z-index: 3;
}

.video-main #main-iframe {
    width: 100%;
    height: 550px;
    background-color: #000;
}

.video-tag {
    position: absolute;
    bottom: 0;
    left: 0;
    padding: 1px;
    background: #00549a;
    z-index: 1;
    color: white;
}

.video-group div.iframe-container.active::before {
    content: '';
    position: absolute;
    z-index: 2;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    box-shadow: inset 0 0 0 5px #00549a;
}
/* END For Video Gallery Component*/


/*Flex boxes automated padding control 2up, 3up, 4up, 5up, and 6up*/
.flex-2up, .flex-3up, .flex-4up {
    margin-left: -15px;
    margin-right: -15px
}

.col-lg-5th {
    width: 20%;
    float: left
}

@media (max-width:767.98px) {
    .flex-5up .col-lg-5th.col-xs-12 {
        width: 100%;
        padding-left: 0;
        padding-right: 0;
    }
   
}

@media (max-width:991.98px) {
    .col-lg-5th.col-sm-6 {
        width: 50%;
    }
    .flex-6up .col-md-2:nth-of-type(2n+1) {
        padding-left: 0
    }
     .flex-6up .col-md-2:nth-of-type(2n) {
        padding-right: 0
    }
}

@media (min-width:992px) {
    .flex-5up .col-lg-5th:nth-of-type(5n+1) {
        padding-left: 0
    }

    .flex-5up .col-lg-5th:nth-of-type(5n) {
        padding-right: 0
    }
    
    .flex-6up .col-lg-2:nth-of-type(6n+1) {
        padding-left: 0
    }
    
    .flex-6up .col-lg-2:nth-of-type(6n) {
        padding-right: 0
    }
}
/*END Flex boxes automated padding control 2up, 3up, 4up, 5up, and 6up*/

/*to overide out of the box bootstrap*/
.modal-backdrop {
    z-index: 1102
}

@media (max-width:519.98px) { 
}

@media (max-width:520.98px) { 
}

@media (min-width:520px) {
    .container {
        width: 480px
    }
}

@media (min-width:768px) {
    .container {
        width: 600px
    }

    .modal-backdrop.in {
        opacity: 0.5
    }

    /*Modal changes from shop*/
    .modal-dialog {
        border-radius: 10px;
        max-height: calc(100% - 120px);
    }

    .modal-header {
        height: 70px;
        padding: 0 30px;
    }

    .modal-body {
        padding: 0 30px;
    }

    .modal.modal-status .modal-body {
        padding-top: 10px;
        padding-bottom: 15px;
    }

    .modal-status-icon {
        font-size: 36px;
    }
}

@media (min-width:992px) {
    .container {
        width: 980px
    }

    .modal-backdrop.in {
        opacity: 0.5
    }
}

@media (min-width:1200px) {
    .container {
        width: 980px
    }

    .modal-backdrop.in {
        opacity: 0.5
    }
}

@media (min-width:1240px) {
    .container {
        width: 1200px
    }

    .modal-backdrop.in {
        opacity: 0.5
    }
}
/*Bridges the gap between Bootsrap and Bell media quieries*/
@media screen and (min-width: 992px) and (max-width: 999.98px) {
}

/* custom made for fullscreen modal (tablet to medium desktop sizes) only*/
@media (min-width: 768px) and (max-width: 1240px) {
    .modal.show .modal-dialog.modal-dialog-fullscreen {
        height: auto;
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        transform: translateY(-50%);
    }
}

/* webkit only hack: custom scroll (.scrollAdjust) only works on webkit browsers so only adjust the margin and padding for those browsers */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .modal-open .modal-body.scrollAdjust:not(*:root) {
        margin-right: 10px;
        padding-right: 20px;
    }
}

/*START Assets bell packages*/

/*Package Columns*/
.package-columns .btc-table-cell-sm {
    display: table-cell;
    float: none;
    padding: 10px;
    left: -10px;
}

/*Internet Packages*/
.internet-packages .internet-packages-name {
    padding: 40px;
    vertical-align: middle;
}

.internet-packages > [class^="col-"]:last-child, .internet-packages > [class*=" col-"]:last-child {
    border-bottom: 1px solid #d4d4d4;
}

.internet-packages .ipc-plan-details {
    padding-left: 0px;
    padding-right: 20px;
    border-right: 1px solid #d4d4d4;
}

.internet-packages-price {
    padding: 20px;
    vertical-align: middle;
}

.internet-packages .package-items {
    display: inline-block;
    padding: 0px 20px 20px 20px;
    text-align: left;
    vertical-align: top;
}

.rateplan-include.internet-packages-packge-icons-list i {
    display: block;
    height: 48px;
    width: 48px;
}

.internet-packages-recommended-bar {
    margin-bottom: 10px;
}

/*Package Containers with sidebar*/
.package-cont-with-sidebar ul.btc-category-result-list li.bgWhite {
    padding: 40px;
}

.package-cont-with-sidebar ul.btc-category-result-list li {
    list-style: none;
}

.package-cont-with-sidebar .btc-category-result-list ol, .package-cont-with-sidebar .btc-category-result-list ul, .package-cont-with-sidebar ul.btc-category-result-list {
    padding-left: 0px;
    margin-bottom: 0;
}

/*3 Column Package Containers with sidebar*/
.pc-three-col-with-sidebar .fibe-packages-channels-row {
    display: table;
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

.pc-three-col-with-sidebar .fibe-packages-channel {
    display: table-cell;
    vertical-align: middle;
    text-align: center;
    padding: 10px 5px;
}

.pc-three-col-with-sidebar .fibe-packages-channel > img {
    width: 100%;
    height: auto;
    margin: 0 auto;
    max-width: 52px;
    max-height: 52px;
}

.pc-three-col-with-sidebar .fibe-reccomended {
    position: absolute;
    top: 20px;
    padding: 7px;
    width: 50%;
    text-align: center;
    height: 30px;
    text-transform: uppercase;
}

/*Image and Text Box*/

.text-and-image > [class^="col-"], .text-and-image > [class*=" col-"] {
    display: table-cell;
    float: none;
    vertical-align: middle;
}

/*Channel Logo Tabs*/

.channel-logo-tabs .bundle-row-main:first-child .bundle-logo-box {
    border-top: 1px solid #d4d4d4;
}

.channel-logo-tabs .bundle-logo-row-box .col-xs-6, .channel-logo-tabs .bundle-logo-row-box .col-sm-6.col-xs-12, .channel-logo-tabs .bundle-row-main .bundle-logo-row-box, .channel-logo-tabs .bundle-row-main {
    position: inherit;
}

.channel-logo-tabs .bundle-logo-box {
    border-right: 1px solid #d4d4d4;
    border-bottom: 1px solid #d4d4d4;
    padding: 10px;
}

.channel-logo-tabs .tv-channels-logo-box-tabs img {
    width: 65px;
    display: block;
    margin: 0 auto 10px auto;
    position: relative;
}

/*picture-with-content*/
.picture-with-content .pad-v-40 {
    padding-top: 40px !important;
    padding-bottom: 40px !important;
}

/*Asset bell package generic*/
.item-price {
    font-size: 47px;
    color: #00549a;
    display: block;
    white-space: nowrap;
    line-height: .8;
    letter-spacing: -1px;
    margin-bottom: 10px;
}

.item-price > sup {
    top: -1.05em;
    font-size: 40%;
    letter-spacing: 0;
}

p.item-note {
    font-size: 12px;
    margin-bottom: 10px;
    display: block;
    line-height: 1;
}

span.item-note {
    font-size: 12px;
    margin-bottom: 5px;
    margin-top: 5px;
    display: block;
    line-height: 1;
}

.item-h4 {
    font-size: 20px;
    letter-spacing: -.5px;
    line-height: 1.3;
    font-weight: 600;
}


.btc-package-price {
    font-size: 30px;
    color: #00549a;
    display: block;
    white-space: nowrap;
    line-height: 1;
    letter-spacing: -1px;
    margin-top: 10px;
}

.btc-package-price > sup {
    top: -1.05em;
    font-size: 40%;
    letter-spacing: 0;
}

.icon-circle-large .bell-package-icon:before, .icon-circle-large .bell-package-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
}

.pop-over-box-small-right-10lines {
    position: absolute;
    right: -110px;
    top: -70px;
    z-index: 100;
    width: 200px;
    display: none;
    background-color: white;
    padding: 20px;
    border: 1px solid #d4d4d4;
    box-shadow: 0 0 40px rgba(0,0,0, .3);
}

.pop-over-box-small-right-8lines {
    position: absolute;
    right: -110px;
    top: -50px;
    z-index: 100;
    width: 200px;
    display: none;
    background-color: white;
    padding: 20px;
    border: 1px solid #d4d4d4;
    box-shadow: 0 0 40px rgba(0,0,0, .3);
}

.product-purchase-options {
    width: 100%;
    border-left: initial;
    border-right: initial;
    background: #fff;
    font-size: 0;
}

.price-w-terms-sup {
    font-size: 40%;
    color: #00549a;
    white-space: nowrap;
}


.product-purchase-option.active, .product-purchase-option.active .price-w-terms-price, .product-purchase-option.active .price-w-terms-sup, .product-purchase-option.active .label-text, .product-purchase-option.active .icon, .product-purchase-option.active a, .product-purchase-option.active button, .product-purchase-option.active a:hover {
    color: #fff;
}

.product-purchase-option {
    display: inline-block;
    position: relative;
    z-index: 1;
    width: 100%;
    font-size: 14px;
}

.product-purchase-option-name {
    width: 30%;
    float: left;
}

.product-purchase-option-details, .product-purchase-option-name {
    padding: 20px;
}

.product-purchase-option-name label {
    margin-top: 10px;
}

.product-purchase-option-details {
    width: 70%;
    float: left;
}
.product-purchase-option-price:last-child {
    padding-right: 0;
}

.product-purchase-option-price-label, .product-purchase-option-price {
    display: table-cell;
    vertical-align: top;
    padding-right: 10px;
}

.product-purchase-option-pricing {
    display: table;
    width: 100%;
}

.price-w-terms {
    display: table;
    font-size: 47px;
}

.product-purchase-option-details .price-w-terms {
    font-size: 38px;
}

.product-purchase-option:before {
    opacity: 0;
    content: "";
    position: absolute;
    z-index: -1;
    top: 0;
    left: -10px;
    width: calc(100% + 20px);
    height: 100%;
    box-shadow: 0 0 10px rgba(0,0,0, .3);
    transition: opacity .2s cubic-bezier(.55,0,.1,1);
}

.product-purchase-option.active:before {
    opacity: 1;
}

.product-purchase-option-name:before, .product-purchase-option-details:before {
    content: "";
    position: absolute;
    z-index: -1;
    top: 0;
    height: 100%;
    background-color: #fff;
    transition: all .2s cubic-bezier(.55,0,.1,1);
    border-bottom: 1px solid #d4d4d4;
}

.product-purchase-option-name:before {
    width: 30%;
    left: 0px;
    background-color: #f0f0f0;
}

.product-purchase-option.active .product-purchase-option-name:before {
    width: calc(30% + 10px);
    left: -10px;
    background-color: #fff;
    background-color: #00549a;
}

.price-w-terms-before, .price-w-terms-price, .price-w-terms-after {
    display: table-cell;
    vertical-align: top;
}


.price-w-terms-before, .price-w-terms-price, .price-w-terms-after {
    display: table-cell;
}

.product-purchase-option.active .product-purchase-option-details:before {
    width: calc(70% + 10px);
    right: -10px;
    background-color: #003778;
}

.product-purchase-option-details:before {
    border-left: 1px solid #d4d4d4;
    width: 70%;
    right: 0;
    background-color: #fff;
}

.product-purchase-option-name:before, .product-purchase-option-details:before {
    content: "";
    position: absolute;
    z-index: -1;
    top: 0;
    height: 100%;
    background-color: #fff;
    transition: all .2s cubic-bezier(.55,0,.1,1);
    border-bottom: 1px solid #d4d4d4;
}

.pop-over-box-medium {
    position: absolute;
    left: -45%;
    top: 100%;
    z-index: 100;
    width: 250px;
    display: none;
    background-color: white;
    padding: 20px;
    border: 1px solid #d4d4d4;
    box-shadow: 0 0 40px rgba(0,0,0, .3);
}

.pop-over-box-medium3 {
    position: absolute;
    left: -100%;
    top: 110%;
    z-index: 100;
    width: 250px;
    display: none;
    background-color: white;
    padding: 20px;
    border: 1px solid #d4d4d4;
    box-shadow: 0 0 40px rgba(0,0,0, .3);
}

.pop-over-box-small {
    position: absolute;
    top: 115%;
    left: -20%;
    z-index: 100;
    width: 180px;
    display: none;
    background-color: white;
    padding: 20px;
    border: 1px solid #d4d4d4;
    box-shadow: 0 0 40px rgba(0,0,0, .3);
}

.pop-over-box {
    position: absolute;
    top: 115%;
    left: -40%;
    z-index: 100;
    width: 250px;
    display: none;
    background-color: white;
    padding: 20px;
    box-shadow: 0 0 40px rgba(0,0,0, .3);
}

@media (max-width: 519.98px) {
    .internet-packages .package-items {
        display: inline-block;
        padding: 0px 30px 10px 40px;
        text-align: left;
        vertical-align: top;
    }

    .internet-packages .ipc-plan-details {
        border-right: 0px solid #d4d4d4;
    }

    .internet-packages-price {
        padding: 40px;
        vertical-align: middle;
    }
}

@media (min-width: 520px) and (max-width: 639.98px) {
    .internet-packages-recommended-bar {
        position: absolute;
        top: 0;
        left: 0;
    }

    .internet-packages .package-items {
        display: inline-block;
        padding: 0px 20px 10px 40px;
        text-align: left;
        vertical-align: top;
    }

    .internet-packages-price {
        padding: 40px;
        vertical-align: middle;
    }

    .border-top-price {
        padding-top: 20px;
        border-top: 1px solid #d4d4d4;
    }

    .internet-packages .ipc-plan-details {
        padding-bottom: 10px;
        border-bottom: 0px solid #d4d4d4;
        border-right: 0px solid #d4d4d4;
    }
}

@media (min-width:640px) and (max-width:991.98px) {
    .internet-packages-name {
        padding: 20px 40px 20px 40px;
        vertical-align: middle;
        vertical-align: middle;
        border-right: 0;
    }

    .internet-packages-recommended-bar {
        position: absolute;
        top: 0;
        left: 0;
    }

    .rateplan-include.internet-packages-packge-icons-list {
        padding-left: 15px;
        vertical-align: middle;
    }

    .internet-packages-price {
        padding: 0 40px 40px;
        vertical-align: middle;
    }

    .package-columns .package-box {
        padding-bottom: 20px;
    }

    .internet-packages .ipc-plan-details {
        padding-bottom: 0px;
        border-bottom: 0px solid #d4d4d4;
        border-right: 0;
    }

    .btc-addons-content-tab2-border-left {
        border-left: 1px solid #d4d4d4;
    }

    .btc-package-price-box {
        padding-left: 30px;
    }

    .package-cont-with-sidebar .col-table-lg {
        display: table;
        table-layout: fixed;
    }

        .package-cont-with-sidebar .col-table-lg > [class^="col-"], .package-cont-with-sidebar .col-table-lg > [class*=" col-"] {
            display: table-cell;
        }
}

@media (min-width:640px) and (max-width:767.98px) {
    .border-bottom {
        border-bottom: 0px solid #d4d4d4 !important;
    }
}

@media (max-width:767.98px) {
    .package-columns .package-box {
        padding-bottom: 20px;
    }

    .width-adjust-custom {
        width: 100%;
    }
}

@media (min-width:768px) and (max-width:991.98px) {
    .package-cont-with-sidebar ul.btc-category-result-list li {
        border-top: 1px solid #d4d4d4;
    }

    .package-cont-with-sidebar ul.btc-category-result-list li:first-child {
        border-top: 0px solid #d4d4d4;
    }
}

@media (max-width:991.98px) {
    .pc-three-col-with-sidetab .fibe-reccomended {
        position: absolute;
        top: 0;
        font-size: 12px;
        left: 0;
        padding: 10px;
        text-transform: uppercase;
        text-align: center;
        background-color: #00377a;
        color: #fff;
        width: 100%;
        margin-bottom: 20px;
        height: 37px;
    }

    .display-inline-block-top-sm {
        display: inline-block;
        vertical-align: top;
    }
}

@media (min-width:992px) {
    .rateplan-include.internet-packages-packge-icons-list i {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }

    .internet-packages-recommended-bar {
        width: 60%;
        margin-top: 5px;
    }

    .picture-with-content .fibe-tabs-w-img {
        margin-bottom: 20px;
    }
}

@media (max-width:1199.98px) {
    .img-responsive-md {
        display: block;
        max-width: 100%;
        height: auto;
    }
}

@media (min-width:992px) and (max-width:1199.98px) {
    .package-columns .package-box:nth-child(2) {
        padding: 0px 5px;
    }

    .package-columns .package-box:nth-child(1) {
        padding-right: 5px;
    }

    .package-columns .package-box:nth-child(3) {
        padding-left: 5px;
    }

    .package-columns .package-box {
        width: 32.5%;
    }

    .margin-10-right-md {
        margin-right: 10px;
    }

    .rateplan-include.internet-packages-packge-icons-list {
        padding: 60px 0 20px;
        vertical-align: middle;
    }

    .internet-packages-name {
        margin: 40px 0px 40px 0;
        vertical-align: middle;
        vertical-align: middle;
    }

    .internet-packages-price {
        padding: 60px 0 20px 0px;
        vertical-align: middle;
    }

    .btc-addons-content-tab2-border-left {
        border-left: 1px solid #d4d4d4;
    }

    .btc-package-price-box {
        padding-left: 30px;
    }

    .pad-40-left-important {
        padding-left: 40px !important;
    }
}

@media (min-width:1200px) {
    .package-columns .package-box:nth-child(2) {
        padding: 0px 15px;
    }

    .package-columns .package-box {
        width: 32.5%;
    }

    .rateplan-include.internet-packages-packge-icons-list {
        padding: 60px 40px 40px;
        vertical-align: middle;
    }

    .internet-packages-price {
        padding: 60px 20px 40px 0;
        vertical-align: middle;
    }

    .package-cont-with-sidebar ul.btc-category-result-list li:first-child {
        border-top: 0px solid #d4d4d4;
    }

    .package-cont-with-sidebar ul.btc-category-result-list li {
        border-top: 1px solid #d4d4d4;
    }

    .product-purchase-option-price > .note {
        display: none;
    }
}


/*END Assets bell packages*/

/*START Bell TV Receivers*/

.col-pad-receiver {
    margin: 0 -40px;
}

/*END Bell TV Receivers*/

/*START Responsive Tables*/
.billHistoryTable.table.table > tbody > tr > td,
.billHistoryTable.table.table > tbody > tr > th,
.billHistoryTable.table.table > tfoot > tr > td,
.billHistoryTable.table.table > tfoot > tr > th,
.billHistoryTable.table.table > thead > tr > td,
.billHistoryTable.table.table > thead > tr > th {
    vertical-align: middle;
    font-family: inherit;
    letter-spacing: inherit;
}

.billHistoryTable > thead th {
    height: 75px;
    padding: 2px 20px 0;
    border-bottom: none;
    background-color: #003778;
    color: #fff;
    font-weight: normal;
    font-size: 18px;
    position: relative;
    /*Possible solution to overflow problem*/
    overflow: hidden;
    text-overflow: ellipsis;
}

.billHistoryTable.table.table-sortable > thead > tr > th[aria-sort="ascending"],
.billHistoryTable.table.table-sortable > thead > tr > th[aria-sort="descending"],
.billHistoryTable.table.table-sortable > thead > tr > th.sortedAsc,
.billHistoryTable.table.table-sortable > thead > tr > th.sortedDesc {
    background-color: #00549a;
    padding-right: 50px;
}

.billHistoryTable.table-bordered {
    border: 1px solid #ddd;
}

    .billHistoryTable.table-bordered > thead > tr > th, .billHistoryTable.table-bordered > tbody > tr > th, .billHistoryTable.table-bordered > tfoot > tr > th,
    .billHistoryTable.table-bordered > thead > tr > td, .billHistoryTable.table-bordered > tbody > tr > td, .billHistoryTable.table-bordered > tfoot > tr > td {
        border: 1px solid #ddd;
    }

table.billHistoryTable {
    border-collapse: collapse;
    border-spacing: 0;
}

.billHistoryTable td, .billHistoryTable th {
    padding: 0
}

.billHistoryTable > thead th {
    height: 74px;
    padding: 2px 45px 0 20px;
    border: none;
}

    .billHistoryTable > thead th:not(:last-child) {
        border-right: 1px solid #fff;
    }

/* Icons */
.billHistoryTable.table.table-sortable > thead > tr > th .carret {
    position: absolute;
    top: 50%;
    right: 20px;
    font-size: 20px;
    margin-top: -12px;
}

.billHistoryTable.table.table-sortable > thead > tr > th[aria-sort="ascending"] > .carret:after,
.billHistoryTable.table.table-sortable > thead > tr > th[aria-sort="descending"] > .carret:after,
.billHistoryTable.table.table-sortable > thead > tr > th.sortedAsc > .carret:after,
.billHistoryTable.table.table-sortable > thead > tr > th.sortedDesc > .carret:after {
    font-family: 'bell-icon';
}

.billHistoryTable.table.table-sortable > thead > tr > th[aria-sort="ascending"] > .carret:after,
.billHistoryTable.table.table-sortable > thead > tr > th.sortedAsc > .carret:after {
    content: "\e91e";
}

.billHistoryTable.table.table-sortable > thead > tr > th[aria-sort="descending"] > .carret:after,
.billHistoryTable.table.table-sortable > thead > tr > th.sortedDesc > .carret:after {
    content: "\e91d";
}

.billHistoryTable td:nth-child(2) .icon-play-icon {
    margin-left: 10px;
}

.billHistoryTable > thead > tr > th .icon-envelope2 {
    font-size: 11px;
    font-weight: 900;
    text-shadow: 0 0 0px;
}

.billHistoryTable > tbody > tr > td .icon-envelope2 {
    font-size: 11px;
    font-weight: 900;
    text-shadow: 0 0 0px;
}

.billHistoryTable.table.table-striped > tbody > tr:nth-of-type(odd), .table > tbody > tr.altRow {
    background-color: #f4f4f4;
}

.billHistoryTable th:nth-child(1), .billHistoryTable td:nth-child(1) {
    width: 23.8%;
}

.billHistoryTable > tbody td {
    height: 61.5px;
    padding: 0 20px;
}

.billHistoryTable > tbody .altRow > td {
    height: 62px;
}

/* Media Queries */
@media screen and (max-width: 991.98px) and (min-width: 640px) {
    .billHistoryTable th:nth-child(1), .billHistoryTable td:nth-child(1) {
        width: 20%;
    }

    .billHistoryTable th:nth-child(2), .billHistoryTable td:nth-child(2),
    .billHistoryTable th:nth-child(3), .billHistoryTable td:nth-child(3) {
        width: 29.9%;
    }

    .container-padding {
        padding: 0 40px;
    }

    .billHistoryTable th:nth-child(1), .billHistoryTable td:nth-child(1) {
        width: 20%;
    }

    .billHistoryTable th:nth-child(2), .billHistoryTable td:nth-child(2),
    .billHistoryTable th:nth-child(3), .billHistoryTable td:nth-child(3) {
        width: 29.9%;
    }

    .billHistoryTable th:nth-child(1), .billHistoryTable td:nth-child(1) {
        padding: 0 0 0 19px;
    }

    .billHistoryTable th:nth-child(4), .billHistoryTable td:nth-child(4) {
        padding: 0;
    }

    .billHistoryTable > thead th {
        height: 56px;
    }

    .billHistoryTable > tbody td {
        height: 61px;
    }

    .billHistoryTable > tbody .altRow > td {
        height: 63px;
    }

    .billHistoryTable > thead th {
        height: 55px;
        font-size: 16px;
    }

    .billHistoryTable > tbody td {
        height: 60px;
    }
}

@media screen and (max-width: 991.98px) {
    .i-icon {
        margin: -18px;
        border: 18px solid transparent;
    }

    .billHistoryTable.table.table-sortable > thead th .carret {
        right: 20px;
        top: 27px;
    }
}

@media screen and (max-width: 639.98px) {
    .responsiveTable .card-body {
        padding: 0;
        border: none;
    }

    .container-padding {
        padding: 0 15px;
    }

    .billHistoryTable th:nth-child(2), .billHistoryTable td:nth-child(2),
    .billHistoryTable th:nth-child(3), .billHistoryTable td:nth-child(3) {
        width: auto;
        min-width: 88px;
    }

    .billHistoryTable th:nth-child(1), .billHistoryTable td:nth-child(1) {
        width: 80px;
    }

    .billHistoryTable th:nth-child(4), .billHistoryTable td:nth-child(4) {
        width: 44px;
    }

    .billHistoryTable > thead th {
        height: 55px;
    }

    .billHistoryTable > tbody td {
        height: 55px;
    }

    .billHistoryTable > tbody .altRow > td {
        height: 54px;
    }

    .billHistoryTable > thead th, .billHistoryTable > tbody td {
        padding: 0 13px 0 9px;
    }

        .billHistoryTable > thead th:first-child, .billHistoryTable > tbody td:first-child {
            padding-left: 13px;
            padding-right: 5px;
        }

        .billHistoryTable > thead th:last-child {
            font-size: 16px;
        }

        .billHistoryTable > tbody td:last-child {
            font-size: 16px;
            padding-right: 10px;
        }

    .billHistoryTable.table.table-sortable > thead > tr > th .carret {
        right: 6px;
        top: 28px;
    }

    .billHistoryTable.table.table-sortable > thead > tr > th:last-child .carret {
        display: none;
    }

    .billHistory footer .col-sm-6 {
        padding: 0;
    }

    .billHistoryTable.table > thead > tr > th,
    .billHistoryTable.table > tbody > tr > td,
    .billHistoryTable.table.table-sortable > thead > tr > th.sortedAsc,
    .billHistoryTable.table.table-sortable > thead > tr > th.sortedDesc {
        padding: 0 10px;
        font-size: 12px;
    }

        .billHistoryTable.table.table-sortable > thead > tr > th[aria-sort="ascending"] > .carret:after,
        .billHistoryTable.table.table-sortable > thead > tr > th[aria-sort="descending"] > .carret:after,
        .billHistoryTable.table.table-sortable > thead > tr > th.sortedAsc > .carret:after,
        .billHistoryTable.table.table-sortable > thead > tr > th.sortedDesc > .carret:after {
            font-family: 'bell-icon2';
            font-size: 4px;
        }

    .billHistoryTable.table.table-sortable > thead > tr > th .carret {
        font-size: 4px;
        margin-top: -4px;
    }

    .billHistoryTable.table.table-sortable > thead > tr > th[aria-sort="ascending"] > .carret:after,
    .billHistoryTable.table.table-sortable > thead > tr > th.sortedAsc > .carret:after {
        content: "\e940";
    }

    .billHistoryTable.table.table-sortable > thead > tr > th[aria-sort="descending"] > .carret:after,
    .billHistoryTable.table.table-sortable > thead > tr > th.sortedDesc > .carret:after {
        content: "\e93f";
    }
}


/* TABLE WITH VISIBLE AND HIDDEN COLUMNS */
.table-visible-hidden.table > tbody > tr > td, .table > tbody > tr > th, .table-visible-hidden.table > tfoot > tr > td, .table > tfoot > tr > th, .table-visible-hidden.table > thead > tr > td, .table-visible-hidden.table > thead > tr > th {
    padding: 22px 20px;
}

    .table-visible-hidden.table > tbody > tr > td p {
        margin-bottom: 0;
    }

.table-visible-hidden.table > thead > tr > th {
    font-family: "bellslimregular", Helvetica, Arial, sans-serif;
    letter-spacing: -1px;
    font-size: 24px;
    font-weight: normal;
}

.table-visible-hidden .table-bordered > thead > tr > th {
    border: 1px solid #054071;
}

.table-visible-hidden.table > thead:first-child > tr:first-child > th {
    border-top: 1px solid #054071;
}

.table-visible-hidden.table > thead:first-child > tr:first-child > th {
    border: 1px solid #054071;
}

@media (max-width: 767.98px) {
    .BlueDarkBorder table > thead:first-child > tr:first-child > th {
        border: none;
    }

    .table-visible-hidden.table > tbody > tr > td, .table > tbody > tr > th, .table > tfoot > tr > td, .table > tfoot > tr > th, .table > thead > tr > td, .table > thead > tr > th {
        padding: 20px 20px;
    }

    .table-visible-hidden.table > thead > tr > th {
        font-family: "bellslimregular", Helvetica, Arial, sans-serif;
        letter-spacing: -1px;
        font-size: 24px;
        font-weight: normal;
        padding: 20px 20px;
    }
}
/* TABLE WITH EXPANDING ROWS */
.responsiveTable .icon-expand-bold:before {
    font-family: 'bell-icon2';
}

/* Loader */
.table-expandable .loading-indicator {
    text-align: center;
    width: 80%;
    left: 10%;
    position: relative;
    height: 100px;
    padding-top: 12px;
}

    .table-expandable .loading-indicator .loading-indicator-circle {
        margin: 0;
        width: 48px;
        height: 48px;
        display: inline-block;
        -webkit-animation: spin 1.1s linear infinite;
        -moz-animation: spin 1.1s linear infinite;
        animation: spin 1.1s linear infinite;
    }

    .table-expandable .loading-indicator .loading-indicator-label {
        margin: 0;
        display: block;
        margin-bottom: -5px;
    }

    .table-expandable .loading-indicator .loading-indicator-color {
        stroke: #04569b;
    }

/*Table*/
.table-expandable.comparisonTable {
    overflow: hidden;
    position: relative;
}

.table-expandable .t-view {
    display: table;
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
}

.table-expandable .t-col-group {
    display: table;
    width: 100%;
    table-layout: fixed;
    border-collapse: separate;
    height: 100%;
    position: relative;
}

.table-expandable .t-row-group {
    display: table-row-group;
}

    .table-expandable .t-row-group .t-row-detail {
        display: none;
    }

.table-expandable .t-row {
    display: table-row;
    opacity: 1;
}

.table-expandable .t-row-group.active .t-row-detail {
    display: table-row;
}

.table-expandable .t-row-group.active .t-row-summary .data-column .t-col > span {
    display: none;
}

.table-expandable .t-col {
    display: table-cell;
    vertical-align: middle;
}

.table-expandable .control-column {
    width: 34.7%;
}

.table-expandable .data-column {
    width: auto;
}

    .table-expandable .data-column .t-col {
        width: 33.3334%;
    }

    .table-expandable .data-column:not(.noShadow) {
        position: relative;
        overflow: hidden;
    }

        .table-expandable .data-column:not(.noShadow):before, .data-column:not(.noShadow):after {
            content: " ";
            width: 15px;
            height: 100%;
            position: absolute;
            color: rgba(0, 0, 0, 0.35);
            top: 0;
            z-index: 3;
        }

        .table-expandable .data-column:not(.noShadow):before {
            box-shadow: -16px 0 16px 16px;
            left: -15px;
        }

        .table-expandable .data-column:not(.noShadow):after {
            box-shadow: -16px 0 16px -16px inset;
            right: 0;
        }

/*Information overlay*/
.responsiveTable .card-body {
    padding: 30px;
}

.responsiveTable .table-expandable .tblOverlay {
    pointer-events: none;
    position: absolute;
    z-index: 2;
}

    .responsiveTable .table-expandable .tblOverlay .data-column {
        overflow: hidden;
        pointer-events: none;
    }

        .responsiveTable .table-expandable .tblOverlay .data-column .t-col {
            display: table-cell;
            height: 0;
            overflow: hidden;
            padding-top: 357px;
            vertical-align: top;
            opacity: 0;
        }

            .responsiveTable .table-expandable .tblOverlay .data-column .t-col.active {
                background-color: rgba(45, 45, 45, 0.9);
                height: 1200px;
                opacity: 1;
            }

            .responsiveTable .table-expandable .tblOverlay .data-column .t-col.failed {
                background-color: rgba(225, 225, 225, 0.7);
            }

                .responsiveTable .table-expandable .tblOverlay .data-column .t-col.failed p {
                    padding: 0 18px;
                    line-height: 16.5px;
                    pointer-events: all;
                }

            .responsiveTable .table-expandable .tblOverlay .data-column .t-col.nodata {
                background-color: transparent;
            }

                .responsiveTable .table-expandable .tblOverlay .data-column .t-col.nodata p {
                    padding: 0 18px;
                    line-height: 16.5px;
                    pointer-events: all;
                }

/*chartRow*/
.table-expandable .chartRow {
    height: 256px;
}

    .table-expandable .chartRow .control-column li {
        display: block;
        text-align: right;
        margin-right: 19px;
        margin-top: 25.2px;
    }

        .table-expandable .chartRow .control-column li:first-child {
            margin-top: 15px;
        }

    .table-expandable .chartRow .data-column {
        vertical-align: bottom;
        border-top: 1px solid #d2d2d2;
    }

    .table-expandable .chartRow .t-col-group {
        margin-bottom: 15px;
        height: 205px;
    }

    .table-expandable .chartRow .data-column .t-col {
        text-align: center;
        vertical-align: bottom;
        height: 100%;
    }

    .table-expandable .chartRow .data-column .chart-scales {
        fill: none;
        stroke: #9eabb0;
        stroke-dasharray: 4 4;
        stroke-width: 1px;
    }

    .table-expandable .chartRow .data-column .chart-bar {
        fill: #00549a;
    }

/*headerRow*/
.table-expandable .headerRow {
    height: 59px;
}

    .table-expandable .headerRow .data-column .t-col {
        border-right: 1px solid #fff;
    }

        .table-expandable .headerRow .data-column .t-col:last-child {
            border-right: none;
        }

/*Data controls*/
.table-expandable .dataControls {
    position: absolute;
    top: 16px;
    left: 0;
    width: 100%;
    z-index: 4;
}

    .table-expandable .dataControls .data-slider-button {
        position: absolute;
        display: block;
        width: 24px;
        height: 24px;
        margin: 0 20px;
        text-decoration: none;
        padding: 0;
        color: #fff;
        border-radius: 0;
    }

        .table-expandable .dataControls .data-slider-button:hover, .dataControls .data-slider-button:active, .dataControls .data-slider-button:focus {
            text-decoration: none;
            color: #fff;
        }

        .table-expandable .dataControls .data-slider-button .icon2:before {
            top: -3px;
        }

.table-expandable .dataSliderRight {
    right: 0;
}

/*Footer*/
.table-expandable .footerRow {
    height: 99px;
}

    .table-expandable .footerRow .control-column {
        padding-left: 26px;
        padding-bottom: 5px;
        padding-right: 20px;
    }

        .table-expandable .footerRow .control-column .brandFont {
            letter-spacing: -0.55px;
        }

    .table-expandable .footerRow .t-col {
        border-right: 1px solid #505050;
    }

    .table-expandable .footerRow .data-column .t-col {
        padding-right: 28px;
        padding-top: 10px;
    }

        .table-expandable .footerRow .data-column .t-col:last-child {
            border-right: none;
            padding-right: 32px;
        }

        .table-expandable .footerRow .data-column .t-col .footer-amount {
            margin-bottom: 10px;
        }

/*Data rows*/
.table-expandable .t-cell {
    border-bottom: 1px solid #d4d4d4;
}

.table-expandable .dataRow .t-col {
    height: 50px;
}

.table-expandable .dataRow .t-row-group > .t-row-detail > .control-column {
    line-height: 14px;
    padding-top: 3px;
    padding-left: 57px;
}

.table-expandable .dataRow .data-column > .t-col-group > .t-col {
    padding-top: 3px;
    border-right: 1px solid #d4d4d4;
}

    .table-expandable .dataRow .data-column > .t-col-group > .t-col:empty {
        border-bottom: none;
        background-color: #d4d4d4;
    }

.table-expandable .dataRow .t-row-group .t-col:last-child {
    border-right: none;
}

.table-expandable .dataRow .control-column.t-col {
    padding: 18px;
}

    .table-expandable .dataRow .control-column.t-col.disabled {
        pointer-events: none;
    }

.table-expandable .dataRow .t-row-group .control-column {
    position: relative;
}

    .table-expandable .dataRow .t-row-group .control-column .txtBlack2 {
        display: inline-block;
        margin-top: 2px;
    }

.table-expandable .dataRow .data-column .t-col {
    padding-right: 30px;
}

/* Icons */
.table-expandable .t-row-group.active .icon-expand-bold {
    position: relative;
}

    .table-expandable .t-row-group.active .icon-expand-bold:before {
        content: "\e96b";
    }

.table-expandable .footerRow .data-column .t-col .icon {
    margin-left: 8px;
}

.table-expandable .footerRow .data-column .t-col a.icon-play-icon {
    margin-top: -2px;
    margin-left: 0;
    margin-right: 24px;
    color: #fff !important;
    text-decoration: none !important;
}

    .table-expandable .footerRow .data-column .t-col a.icon-play-icon:before {
        content: "\e608";
        position: absolute;
        right: -25px;
        font-size: 17px;
        margin-top: -8px;
        top: 50%;
    }

.table-expandable .dataRow .t-row-group .control-column .icon {
}

.table-expandable .dataRow .data-column .t-col .i-icon {
    margin-right: 10px;
}

.table-expandable .dataRow .icon-expand-bold:before {
    top: -1px;
}

.table-expandable .icon-prev-btn:before, .table-expandable .icon-next-btn:before {
    display: inline-block;
    content: "\e012";
    position: static;
}

.table-expandable .icon-prev-btn:before {
    -webkit-transform: scaleX(-1);
    -ms-transform: scaleX(-1);
    transform: scaleX(-1);
    margin-left: -3px;
}


.table-expandable .dataRow .t-row-group > .t-row-summary > .control-column > a {
    display: flex;
    position: relative;
    flex-direction: row;
}

    .table-expandable .dataRow .t-row-group > .t-row-summary > .control-column > a > i.icon {
        display: flex;
        margin: auto;
        margin-right: 15px;
        margin-left: 0;
        align-items: center;
    }

    .table-expandable .dataRow .t-row-group > .t-row-summary > .control-column > a:hover, .dataRow .t-row-group > .t-row-summary > .control-column > a:focus {
        text-decoration: none;
    }

        .table-expandable .dataRow .t-row-group > .t-row-summary > .control-column > a:hover > span, .dataRow .t-row-group > .t-row-summary > .control-column > a:focus > span {
            text-decoration: underline;
        }

    .table-expandable .dataRow .t-row-group > .t-row-summary > .control-column > a > span {
        display: flex;
        margin: auto 0;
        align-items: center;
    }


/* Media queries */
@media screen and (max-width: 639.98px) and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    /* IE10 CSS styles go here */
    .table-expandable .chartRow .t-col-group .t-col svg {
        height: 103px;
    }
}

@media screen and (min-width: 992px) {
    .table-expandable .dataSliderLeft,
    .table-expandable .dataSliderRight {
        display: none;
    }
}

@media screen and (max-width: 991.98px) and (min-width: 640px) {
    .table-expandable .control-column {
        width: 49.8%;
    }

    .responsiveTable .table-expandable .tblOverlay .data-column .t-col.failed {
        padding-top: 373px;
    }

        .responsiveTable .table-expandable .tblOverlay .data-column .t-col.failed > p {
            line-height: 1.167;
        }

    .table-expandable .headerRow .data-column .t-col {
        padding: 0 8%;
        line-height: 15px;
    }

    .table-expandable .dataControls .data-slider-button {
        width: 44px;
        height: 44px;
        margin: -8px 5px;
    }

    .table-expandable .footerRow .data-column .t-col {
        padding-right: 30px;
    }

        .table-expandable .footerRow .data-column .t-col .icon {
            margin-left: 8px;
        }
}

@media screen and (max-width: 991.98px) {
    .table-expandable .i-icon {
        margin: -18px;
        border: 18px solid transparent;
    }

    .table-expandable .dataRow .i-icon {
        margin-right: -10px !important;
    }

    .table-expandable .dataRow .t-row-group > .t-row-detail > .control-column {
        letter-spacing: 0.2px;
        padding-left: 57px;
    }
}

@media screen and (max-width: 639.98px) {

    .responsiveTable.card {
        margin: 0 -15px;
        padding: 0;
        border: none;
    }

        .responsiveTable.card > .card-body {
            padding: 0;
            border: none;
        }

    .table-expandable .dataControls .data-slider-button {
        width: 44px;
        height: 44px;
        margin: -10px;
    }

    .table-expandable .loading-indicator {
        width: 100%;
        left: 0;
    }

    .table-expandable .control-column {
        width: 47%;
    }

    .table-expandable.comparisonTable {
        font-size: 12px;
    }

    .responsiveTable .table-expandable .tblOverlay .data-column .t-col {
        padding-top: 240px;
    }

        .responsiveTable .table-expandable .tblOverlay .data-column .t-col p {
            padding: 0 10px;
        }

        .responsiveTable .table-expandable .tblOverlay .data-column .t-col.failed {
            padding-top: 248px;
        }

            .responsiveTable .table-expandable .tblOverlay .data-column .t-col.failed > p {
                line-height: 1.273;
            }

    .table-expandable .chartRow {
        height: 141px;
    }

        .table-expandable .chartRow .control-column {
            padding-top: 5px;
        }

            .table-expandable .chartRow .control-column li {
                margin-right: 15px;
                margin-top: 5px;
            }

                .table-expandable .chartRow .control-column li:first-child {
                    margin-top: 5px;
                }

        .table-expandable .chartRow .data-column .chart-scales {
            stroke-dasharray: 8 8;
            stroke-width: 2px;
        }

        .table-expandable .chartRow .t-col-group {
            margin-bottom: 8px;
            height: 103px;
        }

            .table-expandable .chartRow .t-col-group .t-col {
                height: 103px;
            }

    .table-expandable .headerRow {
        height: 59px;
    }

        .table-expandable .headerRow .data-column .t-col {
            padding: 0 5%;
            line-height: 1.167;
        }

    .table-expandable .dataControls .data-slider-button {
        width: 44px;
        height: 44px;
        margin: -10px;
    }

    .table-expandable .footerRow {
        height: 97px;
    }

        .table-expandable .footerRow .control-column {
            padding-left: 15px;
        }

            .table-expandable .footerRow .control-column .brandFont {
                letter-spacing: 0.5px;
                line-height: 17px;
                margin-top: -15px;
            }

            .table-expandable .footerRow .control-column .txtBlueExtraLight {
                letter-spacing: -0.4px;
            }

        .table-expandable .footerRow .data-column .t-col {
            padding-top: 23px;
            padding-right: 16px;
            padding-bottom: 8px;
        }

            .table-expandable .footerRow .data-column .t-col:last-child {
                padding-right: 16px;
            }

            .table-expandable .footerRow .data-column .t-col .footer-amount {
                margin-bottom: 15px;
            }

    .table-expandable .dataRow .t-col {
        height: 40px;
    }

    .table-expandable .dataRow .t-row.reduce-xs .t-col {
        height: 40px;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }

    .table-expandable .dataRow .t-col-group {
        line-height: 15px;
    }

    .table-expandable .dataRow .t-row-group > .t-row > .control-column {
        line-height: 15px;
    }

    .table-expandable .dataRow .t-row-group > .t-row-summary > .control-column {
        height: 53.2px;
    }

    .table-expandable .dataRow .t-row-group > .t-row-detail > .control-column {
        padding-left: 48px;
        padding-right: 0;
        letter-spacing: 0;
        line-height: 13px;
    }

    .table-expandable .dataRow .t-row-group > .t-row-detail .t-col.control-column {
        padding-top: 12px;
        padding-bottom: 12px;
    }

    .table-expandable .dataRow .control-column.t-col {
        line-height: 15.5px;
    }

    .table-expandable .dataRow .data-column > .t-col-group > .t-col {
        padding-right: 16px;
        padding-top: 12px;
        padding-bottom: 12px;
        letter-spacing: 0.2px;
    }

        .table-expandable .dataRow .data-column > .t-col-group > .t-col:last-child {
            padding-right: 15px;
        }

    .table-expandable .dataRow .i-icon {
        margin-right: -1px;
        font-size: 14px;
    }

    .table-expandable .icon-prev-btn:before,
    .table-expandable .icon-next-btn:before {
        font-weight: 600;
    }
}

@media all and (-ms-high-contrast:none) {

    *::-ms-backdrop, .table-expandable .table.table-sortable > thead > tr > th .carret {
        top: auto;
        margin-top: auto;
    }

    *::-ms-backdrop, select.custom-selection:focus {
        outline: 1px dotted;
    }
}
/*END Responsive Tables*/

/*START Asset Links*/
.hot-tile.my-bell {
    background: #01215E;
}

.col-border, .col-border > .col-border-item, .col-border > [class*=" col-"], .col-border > [class^=col-] {
    border: 0 solid #e1e1e1;
}

.border_blue, .col-border_blue, .col-border_blue > .col-border-item, .col-border_blue > [class*=" col-"], .col-border_blue > [class^=col-] {
    border-color: #003778
}

.border_blue-dark, .col-border_blue-dark, .col-border_blue-dark > .col-border-item, .col-border_blue-dark > [class*=" col-"], .col-border_blue-dark > [class^=col-] {
    border-color: #0a2543
}

.border_blue-fade, .col-border_blue-fade, .col-border_blue-fade > .col-border-item, .col-border_blue-fade > [class*=" col-"], .col-border_blue-fade > [class^=col-] {
    border-color: #578dbb
}

.border_grey-light, .col-border_grey-med, .col-border_grey-med > .col-border-item, .col-border_grey-med > [class*=" col-"], .col-border_grey-med > [class^=col-] {
    border-color: #bebebe
}

.hot-tile {
    background: #00549a;
    text-align: center;
    color: #fff;
    display: block;
    transition: background-color .5s cubic-bezier(.55,0,.1,1);
    padding: 40px 20px
}

    .hot-tile:link, .hot-tile:visited {
        color: #fff;
        text-decoration: none
    }

    .hot-tile:hover {
        color: #fff;
        background: #00357a
    }

    .hot-tile:active {
        color: #fff
    }

    .hot-tile h3 {
        margin: 10px 0;
        color: #fff
    }

        .hot-tile h3 > span {
            font-family: bell-icon;
            font-size: 15px
        }

    .hot-tile .font_slim {
        font-size: 24px;
        display: block;
        margin: 10px 0
    }

.hot-tile-content p {
    opacity: .8
}

.hot-tile-content > p:last-of-type {
    margin-bottom: 0
}

.hot-tiles_left .hot-tile {
    text-align: left
}

.hot-tile-content-wrap > img {
    width: 80px;
    height: 80px
}


@media (max-width: 991.98px) and (min-width: 520px) {
    .col-border > .col-border-item:nth-child(-n+2), .col-border > [class*=" col-"]:nth-child(-n+2), .col-border > [class^=col-]:nth-child(-n+2) {
        border-top-width: 0;
    }
}


@media (max-width:519.98px) {
    .hot-tiles {
        display: table
    }

        .hot-tiles > .hot-tile {
            display: table-row;
            height: 100%;
            padding: 15px 35px 15px 20px;
            position: relative
        }

    .hot-tile-content {
        display: table-cell;
        width: 100%;
        height: 60px;
        vertical-align: middle
    }

    .hot-tile-content-wrap {
        display: block;
        position: relative
    }

    .hot-tile, .hot-tile h3 {
        text-align: left
    }

        .hot-tile p {
            display: none
        }

        .hot-tile .icon-o, .hot-tile-content-wrap > img {
            position: absolute;
            top: 50%;
            -webkit-transform: translateY(-50%);
            -ms-transform: translateY(-50%);
            transform: translateY(-50%);
            width: 55px;
            height: 55px
        }

        .hot-tile .icon-circle-large {
            border-width: 1px
        }

        .hot-tile .icon-o:before {
            font-size: 52px
        }

        .hot-tile h3 {
            padding: 0 0 0 75px;
            font-size: 28px
        }

        .hot-tile:after {
            font-family: bell-icon;
            font-size: 20px;
            display: inline;
            position: absolute;
            content: '\e012';
            right: 10px;
            top: calc(50% - 10px)
        }

        .hot-tile h3 > span {
            font-size: 20px
        }

    .hot-tile-content-wrap > img {
        width: 55px;
        height: 55px
    }
}

@media(min-width:520px) {
    .hot-tile h3:after {
        font-family: bell-icon;
        font-size: 15px;
        display: inline;
        position: relative;
        content: '\e012'
    }
}

@media (min-width:991px) {
    .hot-tile, .hot-tile h3 {
        text-align: left
    }
}


@media (min-width: 520px) {
    .col-border > .col-border-item, .col-border > [class*=" col-"], .col-border > [class^=col-] {
        border-top-width: 1px;
        border-left-width: 1px;
    }
}

@media (min-width: 992px) {
    .col-border-md_col-3 > .col-border-item:nth-child(-n+3), .col-border-md_col-3 > [class*=" col-"]:nth-child(-n+3), .col-border-md_col-4 > .col-border-item:nth-child(-n+4), .col-border-md_col-4 > [class*=" col-"]:nth-child(-n+4), .col-border-md_col-4 > [class^=col-]:nth-child(-n+4), .col-border > .col-border-item:nth-child(4n+1) .col-border-md_col-3 > [class^=col-]:nth-child(-n+3), .col-border > [class*=" col-"]:nth-child(-n+4), .col-border > [class^=col-]:nth-child(-n+4) {
        border-top-width: 0;
    }
}

@media (min-width: 520px) {
    .col-border > .col-border-item, .col-border > [class*=" col-"], .col-border > [class^=col-] {
        border-top-width: 1px;
        border-left-width: 1px;
    }
}


@media (max-width: 519.98px) {
    .col-border > .col-border-item ~ .col-border-item, .col-border > [class*=" col-"] ~ [class*=" col-"], .col-border > [class^=col-] ~ [class^=col-] {
        border-top-width: 1px;
    }
}
/*END Asset Links*/


/*START footer grey Navigation */

.footer_grey .footer_grey_wrapper {
    background-color: #f4f4f4;
}

.footer_grey .footer_container {
    padding-top: 45px;
    padding-bottom: 45px;
}

/*footer skip to main content*/
.footer_grey .footer_skip_to_main_link, .simple-footer .footer_skip_to_main_link {
    display: inline-block;
    padding: 7px 12px;
    position: absolute;
    left: -300px;
    text-decoration: underline;
    border-bottom-right-radius: 8px;
    transition: left .3s ease-out;
    background-color: #fff;
    z-index: 3000;
    font-size: 13px;
    color: #00549a;
}

    .footer_grey .footer_skip_to_main_link:focus, .simple-footer .footer_skip_to_main_link:focus {
        left: 0;
    }

/*Bread Crumbs*/
    .bread_crumbs_wrapper ol.bread_crumbs_container > li.bread_crumbs_item {
        display: inline-block;
    }

.bread_crumbs_wrapper ol.bread_crumbs_container li.bread_crumbs_item > i {
    margin: 0 10px;
}

.bread_crumbs_wrapper ol.bread_crumbs_container li.bread_crumbs_item:last-child > i {
    display: none;
}

.bread_crumbs_wrapper ol.bread_crumbs_container li.bread_crumbs_item.active > a {
    color: #111111;
    text-decoration: none;
    cursor: default;
}
/*Bread Crumbs*/

/*4 Columns Links List*/
.fourcol_links_list_wrapper ul.fourcol_ll_container {
    column-count: 4; 
    list-style-type: none;
}

    .fourcol_links_list_wrapper ul.fourcol_ll_container > li.fourcol_ll_item {
        margin-top: 5px;
        display: block;
    }
    .fourcol_links_list_wrapper ul.fourcol_ll_container > li.fourcol_ll_item { 
          display: inline-block;
          width: 100%
    }
        .fourcol_links_list_wrapper ul.fourcol_ll_container > li.fourcol_ll_item:first-child {
            margin-top: 0;
        }

/*4 Columns Links List*/

/*Email Links List*/
.email_list_wrapper ul.email_list_cont > li.email_list_column {
    width: calc(25% - 26px);
    margin-right: 30px;
    display: block;
}

.email_list_wrapper ul.email_list_cont > li.email_list_column:last-child {
    margin-right: 0;
}
/*Email Links List*/

/*Site Links List*/
.site_links_list_wrapper ul.site_links_list_cont li{
    display: inline-block;
}

.site_links_list_wrapper ul.site_links_list_cont li > a {
    margin: 0 15px;
}

.site_links_list_wrapper ul.site_links_list_cont li:first-child > a {
    margin-left: 0;
}

.site_links_list_wrapper ul.site_links_list_cont li:last-child > a {
    margin-right: 0;
}

.site_links_list_wrapper ul.site_links_list_cont li > .divider {
    color: #E1E1E1;
}
/*Site Links List*/

/*Social Icon List*/
.social_icons_wrapper ul.social_icons_cont {
    text-align: left;
}

    .social_icons_wrapper ul.social_icons_cont li {
        display: inline-block;
    }

    .social_icons_wrapper ul.social_icons_cont a {
        display: table-cell;
        vertical-align: middle;
        text-align: center;
    }

    .social_icons_wrapper ul.social_icons_cont li .icon_background {
        background-color: #00549a;
        color: #fff;
        height: 36px;
        width: 36px;
        border-radius: 50%;
        display: inline-block;
        text-align: center;
    }

        .social_icons_wrapper ul.social_icons_cont li .icon_background > i:before {
            top: 25%;
            left: 0;
        }
/*Social Links Icon List*/

/*Back top top button*/
.backtotop_tablet_mobile_wrapper .backtotop_tablet_mobile .scrollToTop.mobile {
    opacity: 1;
    bottom: 15px;
    right: 15px;
    width: auto;
    height: auto;
    position: fixed;
    padding-top: 0;
    border-radius: 0;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.backtotop_tablet_mobile_wrapper .backtotop_tablet_mobile .icon_background {
    height: 49px;
    width: 49px;
    background-color: rgba(255,255,255,0.5);
    box-shadow: 0 1px 29px 0 rgba(0,0,0,0.25);
    border-radius: 50%;
    display: inline-block;
    text-align: center;
}

    .backtotop_tablet_mobile_wrapper .backtotop_tablet_mobile .icon_background > i:before {
        top: 25%;
        left: 0;
    }
/*Back top top button*/

/*footer Media Queries*/
@media (min-width: 992px) {
    /*footer Social Links*/
    .social_icons_wrapper ul.social_icons_cont {
        text-align: right;
    }
}

@media (max-width: 991.98px) {
    .footer_grey .footer_container {
        padding-top: 30px;
        padding-bottom: 30px;
    }
}

@media (min-width: 768px) and (max-width: 991.98px) {
    /*4 Columns Links List*/
    .fourcol_links_list_wrapper ul.fourcol_ll_container {
        columns: 3;
        -webkit-columns: 3;
        min-height: 1px;
    }
    /*4 Columns Links List*/

    /*Email Links List*/
    .email_list_wrapper ul.email_list_cont > li.email_list_column {
        width: calc(33.33% - 25px);
        margin-top: 30px;
    }

        .email_list_wrapper ul.email_list_cont > li.email_list_column:nth-child(-n+3) {
            margin-top: 0;
        }

        .email_list_wrapper ul.email_list_cont > li.email_list_column:nth-child(3) {
            margin-right: 0;
        }
    /*Email Links List*/
}

@media (max-width: 767.98px) {
    /*4 Columns Links List*/
    .fourcol_links_list_wrapper ul.fourcol_ll_container {
        columns: 1;
    }
    /*4 Columns Links List*/

    /*Email Links List*/
    .email_list_wrapper ul.email_list_cont > li.email_list_column {
        width: 100%;
        margin-top: 30px;
        margin-right: 0;
    }

        .email_list_wrapper ul.email_list_cont > li.email_list_column:first-child {
            margin-top: 0;
        }
    /*Email Links List*/

    /*Site Links List*/
    .site_links_list_wrapper ul.site_links_list_cont li > a {
        margin: 0 5px;
    }

    .site_links_list_wrapper ul.site_links_list_cont li:first-child > a {
        margin-left: 0;
    }

    .site_links_list_wrapper ul.site_links_list_cont li:last-child > a {
        margin-right: 0;
    }
    /*Site Links List*/
}

/*END Footer Grey Navigation*/
 
/*for mobile devices to disable zooming in at the element*/
@media screen and (-webkit-min-device-pixel-ratio:0) and (max-device-width:767px) { 
    select.mos_zoom,
    textarea.mos_zoom,
    input.mos_zoom{
        font-size: 16px; 
    }
    .form-control-select, .date-picker{
        font-size: 16px; 
    }
}

@media screen and (max-width: 767.98px) {
.message-block .icon-width-40,.message-block .content-width,.upgrade-my-device .icon-width-40,.md-icon-info-block-full .icon-width-40,.small-icon-info-block-half-full .icon-width-40{width:100%}
.container-fullWidth-xs{width:100%}
.steps-textsize{font-size:12px}
}
@media (min-width: 320px) and (max-width: 991.98px) {
.container, .container.liquid-container{width:100%;max-width:100%}
.container{padding-left:30px;padding-right:30px}
}
@media (min-width: 992px) and (max-width: 1239.98px) {
.container{padding-left:45px;padding-right:45px}
}
@media (min-width:992px) {
.container, .container.liquid-container{width:100%;max-width:100%}
}
@media (min-width:1200px) {
.container, .container.liquid-container{width:100%;max-width:100%}
}
@media (min-width:1240px) {
.container, .container.liquid-container{width:1200px}
}

/*BRF3 New Styleguide container*/
@media (min-width: 320px) and (max-width: 767.98px) { 
.brfpad .container, .brfpad .container.liquid-container{padding-left:15px;padding-right:15px}
}
@media (min-width: 768px) and (max-width: 991.98px) { 
.brfpad .container, .brfpad .container.liquid-container{padding-left:30px;padding-right:30px}
}
@media (min-width: 992px) and (max-width: 1239.98px) {
.brfpad .container, .brfpad .container.liquid-container{padding-left:16px;padding-right:16px}
}


/*fix for firefox input elements which show no text at all using native bootstrap*/
.firefoxFix{height:38px;padding:6px 12px}

a.btn:focus {
    text-decoration: none;
}
.btn-default:focus{
    color: #fff;
    background-color: #00549a;
    border-color: #00549a;
}
input.searchBox:focus, .search-bar-footer input.ui-autocomplete-input:focus{
    border:2px solid #003778; 
        -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6);
}

body.is_tabbing *:focus {
  outline: 0!important;
  box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}
body:not(.is_tabbing) button:focus,
body:not(.is_tabbing) input:focus,
body:not(.is_tabbing) select:focus,
body:not(.is_tabbing) a:focus,
body:not(.is_tabbing) [tabindex="0"] {
    outline: none;
}