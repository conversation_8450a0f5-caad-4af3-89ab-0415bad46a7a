﻿//document ready
$(function () {
    var isFrench = getIsFrench(),
        closeText = isFrench ? 'Fermer' : 'Close';

    // localization
    $.datepicker.setDefaults($.datepicker.regional[isFrench ? 'fr' : '']);

    // initialize each datepicker separately so we could accept data-* options
    jQuery('.date-picker').each(function () {
        var input = $(this), showOn = input.data('show-on') || 'button', passedShowOn, implementedShowOn, yearRange = input.data('year-range') || "c-5:c+6", yearSplit, currentYear;

        /* jquery-ui supports 'focus', 'button', or 'both' for showOn value (focus refers to input focus). 
         * however, since we are moving the focus inside the datepicker and trapping it there whenever it is shown, it's not smart to do this on focus because it can be disorienting.
         * so, instead of focus (which i will be blocking and default to button when set as data-show-on option), we'll enable showing it on click (or ENTER keypress) instead by setting the data-show-on attribute to 'click' or 'both'.
         * in detail, if data-show-on equals:
         * 'button' - default value. datepicker is shown only upon clicking the button trigger
         * 'click' - datepicker is shown only upon clicking the input or pressing ENTER or SPACE while it is focused
         * 'both' - datepicker is shown in both 'button' and 'click' scenarios
         * 'focus' - this option will be blocked so nothing will happen
         * any other value - invalid option so nothing will happen
         */
        switch (showOn) {
            case 'button':
                passedShowOn = 'button';
                implementedShowOn = 'button';
                break;
            case 'both':
                passedShowOn = 'button';
                implementedShowOn = 'button and click';
                break;
            case 'click':
                passedShowOn = 'none'; // passing none or any invalid string will cause the datepicker to not show on anything
                implementedShowOn = 'click';
                break;
            case 'focus':
                passedShowOn = 'none';
                implementedShowOn = 'none';
                break;
            default: // invalid
                passedShowOn = 'none';
                implementedShowOn = 'none';
                break;
        }

        // jquery-ui's usage of 'c' in yearRange is for current selected year. but if used as standalone (ex., '1983:c'), it doesn't make sense. so let's override it to mean actual current year instead when used like that
        yearSplit = yearRange.split(':');
        currentYear = new Date().getFullYear();
        if ('c' === yearSplit[0]) {
            yearSplit[0] = currentYear;
        }
        if ('c' === yearSplit[1]) {
            yearSplit[1] = currentYear;
        }
        yearRange = yearSplit.join(':');
 
        // initialize the datepicker
        input.datepicker({
            dateFormat: input.data('date-format'),
            showOn: passedShowOn,
            buttonImageOnly: false,
            showButtonPanel: true,
            changeMonth: input.data('change-month') !== false, //true by default
            changeYear: input.data('change-year') !== false, //true by default
            yearRange: yearRange,
            closeText: closeText,
            onClose: function () {
                setTimeout(function () {
                    var calendarDiv = $('#ui-datepicker-div'),
                        trigger = $('#' + calendarDiv.data('trigger-id'));

                    trigger.prev().addBack('input').off('keydown', preventSpaceKeydown);
                    trigger.focus();
                    calendarDiv.off('keydown');

                    revertTabIndexAndAriaHiddenDifferentHierarchy(calendarDiv);
                }, 0);
            },
            onChangeMonthYear: function () {
                setTimeout(function () {
                    setupCalendarDates();
                    setupPrevNextButtons();
                    setupMonthYearDropdowns();
                    setupTodayButton();
                }, 0);
            },
            onSelect: function () {
                setHighlightState($('.ui-state-hover')[0], $('#ui-datepicker-div')[0]);
            }
        });

        // initialize the generated show calendar button
        initializeShowCalendarTrigger(implementedShowOn, input);
    });
});

// this function initializes the show calendar trigger (either button click or input click or both) and associates it with the calendar upon showing since we only have one calendar instance
function initializeShowCalendarTrigger(implementedShowOn, input) {
    var buttonTrigger, labelId, initButton = false, initInput = false;

    switch (implementedShowOn) {
        case 'none':
            return;
        case 'button':
            initButton = true;
            break;
        case 'click':
            initInput = true;
            break;
        case 'button and click':
            initButton = true;
            initInput = true;
            break;

    }

    // improve/tweak jquery's implementation of showing/hiding of datepicker by clicking the button
    if (initButton) {
        buttonTrigger = input.next('.ui-datepicker-trigger');
        if (buttonTrigger.length > 0) {
            // Add aria-describedby to the button referring to the input's label
            buttonTrigger.attr('aria-label', getIsFrench() ? 'Montrer le calendrier' : 'Show calendar').html("");
            labelId = getInputLabelId(input);
            if (labelId !== null) {
                buttonTrigger.attr('aria-describedby', labelId);
            }

            buttonTrigger.on('click', function () {
                // $.datepicker._datepickerShowing is updated before click fires so we check the inverted value
                if (!$.datepicker._datepickerShowing && this === $.datepicker._curInst.trigger[0]) {
                    $.datepicker.dpDiv.data('trigger-id', getSetId(this));
                    input.datepicker('hide');
                } else {
                    onShowCalendar.call(this);
                }
            });
        }
    }

    // implement showing/hiding of datepicker by clicking the input or pressing ENTER or SPACE while it is focused
    if (initInput) {
        input.on('click', function () {
            if ($.datepicker._datepickerShowing && this === $.datepicker._lastInput) {
                $.datepicker.dpDiv.data('trigger-id', getSetId(this));
                input.datepicker('hide');
            } else {
                input.datepicker('show');
                onShowCalendar.call(this);
            }
        }).on('keypress', function (e) {
            var key = e.which || e.keyCode || 0;

            if (13 === key || 32 === key) {
                e.preventDefault();
                e.stopPropagation();
                input.click();
            }
        });
    }
}

// this function sets up the calendar everytime it is shown
function onShowCalendar() {
    var triggerId = getSetId(this);

    $(this).prev().addBack('input').on('keydown', preventSpaceKeydown);

    setTimeout(function () {
        var calendarDivEl = $('#ui-datepicker-div'),
            calendarDiv = calendarDivEl[0],
            highlightedDate;

        // priority of focus is selected date followed by current date followed by the first date of currently visible month
        (calendarDivEl.find('.ui-state-active') || calendarDivEl.find('.ui-datepicker-today a') || calendarDivEl.find('.ui-state-default')).focus();

        // set reference to button trigger on the calendar
        calendarDivEl
            .data('trigger-id', triggerId)
            .attr({
                'role': 'application',
                'aria-label': getIsFrench() ? 'Sélecteur de date de calendrier' : 'Calendar date picker'
            });

        // set aria-label of each date(number) of the calendar
        setupCalendarDates();

        // set various attributes of the previous and next buttons of the calendar and attach click event handlers
        setupPrevNextButtons();

        // set labels for month and year dropdowns
        setupMonthYearDropdowns();

        setupTodayButton();

        highlightedDate = $('.ui-state-active');
        if (highlightedDate.length === 0) {
            highlightedDate = $(getHighlightedDate(calendarDiv));
            if (highlightedDate.length === 0) {
                highlightedDate = $('.ui-state-default:not(.ui-datepicker-close, .ui-datepicker-current)');
            }
        }
        setHighlightState(highlightedDate[0], calendarDiv);

        // call function to override keydown events inside the calendar div
        onKeydownCalendar(calendarDiv);

        // hide other elements from screenreaders whenever the calendar is shown
        overwriteTabIndexAndAriaHiddenDifferentHierarchy(calendarDivEl);
    }, 0);
}

// add an aria-label to the date link indicating the currently focused date
function setupCalendarDates() {
    var cleanUps = $('.amaze-date'),
        datePickDiv = document.getElementById('ui-datepicker-div'),
        calendarDiv = $(datePickDiv),
        triggerEl,
        inputEl,
        dateFormat,
        shortYearCutoff,
        settings;

    // get date format and settings
    triggerEl = $('#' + calendarDiv.data('trigger-id'));
    inputEl = triggerEl.prev().addBack('input');
    dateFormat = inputEl.datepicker('option', 'dateFormat');
    shortYearCutoff = inputEl.datepicker('option', 'shortYearCutoff');
    shortYearCutoff = (typeof shortYearCutoff !== "string" ? shortYearCutoff : new Date().getFullYear() % 100 + parseInt(shortYearCutoff, 10));
    settings = {
        shortYearCutoff: shortYearCutoff,
        dayNamesShort: inputEl.datepicker('option', 'dayNamesShort'),
        dayNames: inputEl.datepicker('option', 'dayNames'),
        monthNamesShort: inputEl.datepicker('option', 'monthNamesShort'),
        monthNames: inputEl.datepicker('option', 'monthNames')
    };

    $(cleanUps).each(function (clean) {
        // each(cleanUps, function (clean) {
        clean.parentNode.removeChild(clean);
    });

    $('a.ui-state-default', datePickDiv)
        .attr({
            'role': 'button',
            'href': 'javascript:void(0)'
        }).each(function (index, date) {
            var currentRow = $(date).closest('tr'),
                currentTds = $('td', currentRow),
                currentIndex = $.inArray(date.parentNode, currentTds),
                headThs = $('thead tr th', datePickDiv),
                dayIndex = headThs[currentIndex],
                daySpan = $('span', dayIndex)[0],
                monthEl = $('.ui-datepicker-month', datePickDiv),
                yearEl = $('.ui-datepicker-year', datePickDiv),
                monthName = monthEl.find('option:selected').text(),
                year = yearEl.find('option:selected').text(),
                number = date.innerHTML,
                formattedDate;


            if (monthName.length === 0) {
                monthName = monthEl.text();
            }

            if (year.length === 0) {
                year = yearEl.text();
            }

            if (!monthName || !number || !year) {
                return;
            }

            // support formatting based on datepicker option
            formattedDate = $.datepicker.formatDate(dateFormat, new Date(year + '-' + ($.inArray(monthName, settings.monthNamesShort) + 1) + '-' + date.innerHTML), settings);
            // AT Reads: {date} {month} {year} {day:optional} ex. "December 18 2014 Thursday"
            date.setAttribute('aria-label', formattedDate + (daySpan ? ' ' + daySpan.title : ''));
        });
}

// this function sets various attributes of the previous and next buttons of the calendar and attaches click event handlers
function setupPrevNextButtons() {
    var calendarDivEl = $('#ui-datepicker-div'),
        prevEl = calendarDivEl.find('.ui-datepicker-prev'),
        nextEl = calendarDivEl.find('.ui-datepicker-next'),
        prevNextButtons = prevEl.add(nextEl);
    
    // set-up calendar previous and next buttons
    prevNextButtons.attr({
        'role': 'button',
        'href': 'javascript:void(0)'
    }).removeAttr('title aria-disabled');

    if (prevEl.hasClass('ui-state-disabled')) {
        prevEl.attr('aria-disabled', 'true');
    }

    if (nextEl.hasClass('ui-state-disabled')) {
        nextEl.attr('aria-disabled', 'true');
    }

    setLabelPrevNextButtons(prevNextButtons);

    // delegation won't work here for whatever reason, so we are
    // forced to attach individual click listeners to the prev /
    // next month buttons each time they are added to the DOM
    prevEl.on('click', handlePrevClicks);
    nextEl.on('click', handleNextClicks);
}

// this function sets the aria-label attribute and custom arrows of the month and year selectors
function setupMonthYearDropdowns() {
    var calendarDivEl = $('#ui-datepicker-div'),
        monthDropdown = calendarDivEl.find('select.ui-datepicker-month'),
        yearDropdown = calendarDivEl.find('select.ui-datepicker-year'),
        monthSelectorLabel = 'month selector',
        yearSelectorLabel = 'year selector';

    if (getIsFrench()) {
        monthSelectorLabel = 'sélecteur de mois';
        yearSelectorLabel = "sélecteur d'année";
    }

    monthDropdown.attr('aria-label', monthSelectorLabel).on('change', function () {
        setTimeout(function () {
            $('.ui-datepicker-month').focus();
        }, 0);
    });
    yearDropdown.attr('aria-label', yearSelectorLabel).on('change', function () {
        setTimeout(function () {
            $('.ui-datepicker-year').focus();
        }, 0);
    });

    if (monthDropdown.next('.custom-dropdown-arrow').length === 0) {
        monthDropdown.after('<span class="custom-dropdown-arrow" aria-hidden="true">&#58881;</span>');
    }

    if (yearDropdown.next('.custom-dropdown-arrow').length === 0) {
        yearDropdown.after('<span class="custom-dropdown-arrow" aria-hidden="true">&#58881;</span>');
    }

}

// this function sets the text of the current date button and modifies its behavior to select the date instead of just highlighting it
function setupTodayButton() {
    var calendarDivEl = $('#ui-datepicker-div'),
        todayButton = calendarDivEl.find('.ui-datepicker-current'),
        todayLabel = getIsFrench() ? "Aujourd'hui" : "Today";

    todayButton.text(todayLabel).click(function () {
        setTimeout(function () {
            var activeDate = $('.ui-state-highlight') || $('.ui-state-active');
            if (activeDate.length > 0) {
                activeDate.click();
            }
        }, 0);
    });
}

// this function overrides the keydown events inside the calendar div
function onKeydownCalendar(calendarDiv) {
    $(calendarDiv).on('keydown', function (e) {
        var key = e.which || e.keyCode || 0,
            target = e.target,
            targetEl = $(target),
            newTarget,
            highlightedDate = getHighlightedDate(calendarDiv),
            triggerEl,
            inputEl,
            activeDate,
            firstOfMonth,
            daysOfMonth,
            lastDay;

        if (!highlightedDate) {
            highlightedDate = $('.ui-state-active');
            if (highlightedDate.length === 0) {
                highlightedDate = $('.ui-state-default:not(.ui-datepicker-close, .ui-datepicker-current)');
            }
            setHighlightState(highlightedDate[0], calendarDiv);
        }

        if (27 === key) { // ESC
            triggerEl = $('#' + $(this).data('trigger-id'));
            inputEl = triggerEl.prev().addBack('input');
            inputEl.datepicker('hide');
            return;
        } else if (9 === key && e.shiftKey) { // SHIFT + TAB
            e.preventDefault();
            if (targetEl.not('.ui-datepicker-close, .ui-datepicker-current').hasClass('ui-state-default')) { // date values
                $('.ui-datepicker-next')[0].focus();
            } else if (targetEl.hasClass('ui-datepicker-next')) { // next button
                newTarget = $('.ui-datepicker-year');
                if (newTarget.is('select')) {
                    newTarget.focus();
                } else {
                    newTarget = $('.ui-datepicker-month');
                    if (newTarget.is('select')) {
                        newTarget.focus();
                    } else {
                        $('.ui-datepicker-prev').focus();
                    }
                }
            } else if (targetEl.hasClass('ui-datepicker-year')) { // year dropdown
                newTarget = $('.ui-datepicker-month');
                if (newTarget.is('select')) {
                    newTarget.focus();
                } else {
                    $('.ui-datepicker-prev').focus();
                }
            } else if (targetEl.hasClass('ui-datepicker-month')) { // month dropdown
                $('.ui-datepicker-prev').focus();
            } else if (targetEl.hasClass('ui-datepicker-prev')) { // prev button
                $('.ui-datepicker-close').focus();
            } else if (targetEl.hasClass('ui-datepicker-close')) { // close button
                $('.ui-datepicker-current').focus();
            } else if (targetEl.hasClass('ui-datepicker-current')) { // current button
                activeDate = $('.ui-state-highlight') || $('.ui-state-active');
                if (activeDate.length > 0) {
                    activeDate.focus();
                }
            }
        } else if (key === 9) { // TAB
            e.preventDefault();
            if (targetEl.not('.ui-datepicker-close, .ui-datepicker-current').hasClass('ui-state-default')) { // date values
                $('.ui-datepicker-current').focus();
            } else if (targetEl.hasClass('ui-datepicker-current')) { // today button
                $('.ui-datepicker-close').focus();
            } else if (targetEl.hasClass('ui-datepicker-close')) { // close button
                $('.ui-datepicker-prev').focus();
            } else if (targetEl.hasClass('ui-datepicker-prev')) { // prev button
                newTarget = $('.ui-datepicker-month');
                if (newTarget.is('select')) {
                    newTarget.focus();
                } else {
                    newTarget = $('.ui-datepicker-year');
                    if (newTarget.is('select')) {
                        newTarget.focus();
                    } else {
                        $('.ui-datepicker-next').focus();
                    }
                }
            } else if (targetEl.hasClass('ui-datepicker-month')) { // month dropdown
                newTarget = $('.ui-datepicker-year');
                if (newTarget.is('select')) {
                    newTarget.focus();
                } else {
                    $('.ui-datepicker-next').focus();
                }
            } else if (targetEl.hasClass('ui-datepicker-year')) { // year dropdown
                $('.ui-datepicker-next').focus();
            } else if (targetEl.hasClass('ui-datepicker-next')) { // next button
                activeDate = $('.ui-state-highlight') || $('.ui-state-active');
                if (activeDate.length > 0) {
                    activeDate.focus();
                }
            }
        } else if (key === 37) { // LEFT arrow key
            if (!targetEl.hasClass('ui-datepicker-close') && !targetEl.hasClass('ui-datepicker-current') && targetEl.hasClass('ui-state-default')) {
                previousDay(target);
            }

            if (targetEl.is(':not(select)')) {
                e.preventDefault();
            }
        } else if (key === 39) { // RIGHT arrow key
            if (!targetEl.hasClass('ui-datepicker-close') && !targetEl.hasClass('ui-datepicker-current') && targetEl.hasClass('ui-state-default')) {
                nextDay(target);
            }

            if (targetEl.is(':not(select)')) {
                e.preventDefault();
            }
        } else if (key === 38) { // UP arrow key
            if (!targetEl.hasClass('ui-datepicker-close') && !targetEl.hasClass('ui-datepicker-current') && targetEl.hasClass('ui-state-default')) {
                upHandler(target, calendarDiv);
            }

            if (targetEl.is(':not(select)')) {
                e.preventDefault();
            }
        } else if (key === 40) { // DOWN arrow key
            if (!targetEl.hasClass('ui-datepicker-close') && !targetEl.hasClass('ui-datepicker-current') && targetEl.hasClass('ui-state-default')) {
                downHandler(target, calendarDiv);
            }

            if (targetEl.is(':not(select)')) {
                e.preventDefault();
            }
        } /* //no need to listen for ENTER keydown since the click event gets fired when this is pressed on the buttons which we already have event listeners for
        else if (key === 13) { // ENTER
            if (targetEl.hasClass('ui-datepicker-prev')) {
                handlePrevClicks();
            } else if (targetEl.hasClass('ui-datepicker-next')) {
                handleNextClicks();
            }
        }*/ else if (32 === key) { // SPACE
            if (targetEl.hasClass('ui-datepicker-prev') || targetEl.hasClass('ui-datepicker-next') || (!targetEl.hasClass('ui-datepicker-close') && !targetEl.hasClass('ui-datepicker-current') && targetEl.hasClass('ui-state-default'))) {
                e.preventDefault();
                target.click();
            }
        } else if (33 === key) { // PAGE UP
            if (!targetEl.hasClass('ui-datepicker-close') && !targetEl.hasClass('ui-datepicker-current') && targetEl.hasClass('ui-state-default')) {
                moveOneMonth(target, 'prev');
            }

            if (targetEl.is(':not(select)')) {
                e.preventDefault();
            }
        } else if (34 === key) { // PAGE DOWN
            if (!targetEl.hasClass('ui-datepicker-close') && !targetEl.hasClass('ui-datepicker-current') && targetEl.hasClass('ui-state-default')) {
                moveOneMonth(target, 'next');
            }

            if (targetEl.is(':not(select)')) {
                e.preventDefault();
            }
        } else if (36 === key) { // HOME
            if (!targetEl.hasClass('ui-datepicker-close') && !targetEl.hasClass('ui-datepicker-current') && targetEl.hasClass('ui-state-default')) {
                firstOfMonth = targetEl.closest('tbody').find('.ui-state-default')[0];
                if (firstOfMonth) {
                    firstOfMonth.focus();
                    setHighlightState(firstOfMonth, $('#ui-datepicker-div')[0]);
                }
            }

            if (targetEl.is(':not(select)')) {
                e.preventDefault();
            }
        } else if (35 === key) { // END
            if (!targetEl.hasClass('ui-datepicker-close') && !targetEl.hasClass('ui-datepicker-current') && targetEl.hasClass('ui-state-default')) {
                daysOfMonth = targetEl.closest('tbody').find('.ui-state-default');
                lastDay = daysOfMonth[daysOfMonth.length - 1];
                if (lastDay) {
                    lastDay.focus();
                    setHighlightState(lastDay, $('#ui-datepicker-div')[0]);
                }
            }

            if (targetEl.is(':not(select)')) {
                e.preventDefault();
            }
        }
    });
}

///////////////////////////////
//////////////////////////// //
///////////////////////// // //
// UTILITY-LIKE THINGS // // //
///////////////////////// // //
//////////////////////////// //
///////////////////////////////
function isOdd(num) {
    return num % 2;
}

function moveOneMonth(currentDate, dir) {
    var button = (dir === 'next')
        ? $('.ui-datepicker-next')[0]
        : $('.ui-datepicker-prev')[0];

    if (!button) {
        return;
    }

    var ENABLED_SELECTOR = '#ui-datepicker-div tbody td:not(.ui-state-disabled)';
    var $currentCells = $(ENABLED_SELECTOR);
    var currentIdx = $.inArray(currentDate.parentNode, $currentCells);

    button.click();
    setTimeout(function () {
        var $newCells = $(ENABLED_SELECTOR);
        var newTd = $newCells[currentIdx];
        var newAnchor = newTd && $(newTd).find('a')[0];

        while (!newAnchor) {
            currentIdx--;
            newTd = $newCells[currentIdx];
            newAnchor = newTd && $(newTd).find('a')[0];
        }

        setHighlightState(newAnchor, $('#ui-datepicker-div')[0]);
        newAnchor.focus();

    }, 0);

}

function handleNextClicks() {
    setTimeout(function () {
        prepHighlightState();
        $('.ui-datepicker-next').focus();
    }, 0);
}

function handlePrevClicks() {
    setTimeout(function () {
        prepHighlightState();
        $('.ui-datepicker-prev').focus();
    }, 0);
}

function previousDay(dateLink) {
    var container = document.getElementById('ui-datepicker-div');
    if (!dateLink) {
        return;
    }
    var td = $(dateLink).closest('td');
    if (!td) {
        return;
    }

    var prevTd = $(td).prev(),
        prevDateLink = $('a.ui-state-default', prevTd)[0];

    if (prevTd && prevDateLink) {
        setHighlightState(prevDateLink, container);
        prevDateLink.focus();
    } else {
        handlePrevious(dateLink);
    }
}


function handlePrevious(target) {
    var container = document.getElementById('ui-datepicker-div');
    if (!target) {
        return;
    }
    var currentRow = $(target).closest('tr');
    if (!currentRow) {
        return;
    }
    var previousRow = $(currentRow).prev();

    if (!previousRow || previousRow.length === 0) {
        // there is not previous row, so we go to previous month...
        previousMonth();
    } else {
        var prevRowDates = $('td a.ui-state-default', previousRow);
        var prevRowDate = prevRowDates[prevRowDates.length - 1];

        if (prevRowDate) {
            setTimeout(function () {
                setHighlightState(prevRowDate, container);
                prevRowDate.focus();
            }, 0);
        }
    }
}

function previousMonth() {
    var prevLink = $('.ui-datepicker-prev')[0];
    var container = document.getElementById('ui-datepicker-div');
    prevLink.click();
    // focus last day of new month
    setTimeout(function () {
        var trs = $('tr', container),
            lastRowTdLinks = $('td a.ui-state-default', trs[trs.length - 1]),
            lastDate = lastRowTdLinks[lastRowTdLinks.length - 1];

        setHighlightState(lastDate, container);
        lastDate.focus();

    }, 0);
}

///////////////// NEXT /////////////////
/**
 * Handles right arrow key navigation
 * @param  {HTMLElement} dateLink The target of the keyboard event
 */
function nextDay(dateLink) {
    var container = document.getElementById('ui-datepicker-div');
    if (!dateLink) {
        return;
    }
    var td = $(dateLink).closest('td');
    if (!td) {
        return;
    }
    var nextTd = $(td).next(),
        nextDateLink = $('a.ui-state-default', nextTd)[0];

    if (nextTd && nextDateLink) {
        setHighlightState(nextDateLink, container);
        nextDateLink.focus(); // the next day (same row)
    } else {
        handleNext(dateLink);
    }
}

function handleNext(target) {
    var container = document.getElementById('ui-datepicker-div');
    if (!target) {
        return;
    }
    var currentRow = $(target).closest('tr'),
        nextRow = $(currentRow).next();

    if (!nextRow || nextRow.length === 0) {
        nextMonth();
    } else {
        var nextRowFirstDate = $('a.ui-state-default', nextRow)[0];
        if (nextRowFirstDate) {
            setHighlightState(nextRowFirstDate, container);
            nextRowFirstDate.focus();
        }
    }
}

function nextMonth() {
    nextMon = $('.ui-datepicker-next')[0];
    var container = document.getElementById('ui-datepicker-div');
    nextMon.click();
    // focus the first day of the new month
    setTimeout(function () {
        var firstDate = $('a.ui-state-default', container)[0];
        setHighlightState(firstDate, container);
        firstDate.focus();
    }, 0);
}

/////////// UP ///////////
/**
 * Handle the up arrow navigation through dates
 * @param  {HTMLElement} target   The target of the keyboard event (day)
 * @param  {HTMLElement} cont     The calendar container
 */
function upHandler(target, cont) {
    var prevLink = $('.ui-datepicker-prev')[0];
    var rowContext = $(target).closest('tr');
    if (!rowContext) {
        return;
    }
    var rowTds = $('td', rowContext),
        rowLinks = $('a.ui-state-default', rowContext),
        targetIndex = $.inArray(target, rowLinks),
        prevRow = $(rowContext).prev(),
        prevRowTds = $('td', prevRow),
        parallel = prevRowTds[targetIndex],
        linkCheck = $('a.ui-state-default', parallel)[0];

    if (prevRow && parallel && linkCheck) {
        // there is a previous row, a td at the same index
        // of the target AND theres a link in that td
        setHighlightState(linkCheck, cont);
        linkCheck.focus();
    } else {
        // we're either on the first row of a month, or we're on the
        // second and there is not a date link directly above the target
        prevLink.click();
        setTimeout(function () {
            var newRows = $('tr', cont),
                lastRow = newRows[newRows.length - 1],
                lastRowTds = $('td', lastRow),
                tdParallelIndex = $.inArray(target.parentNode, rowTds),
                newParallel = lastRowTds[tdParallelIndex],
                newCheck = $('a.ui-state-default', newParallel)[0];

            if (lastRow && newParallel && newCheck) {
                setHighlightState(newCheck, cont);
                newCheck.focus();
            } else {
                // theres no date link on the last week (row) of the new month
                // meaning its an empty cell, so we'll try the 2nd to last week
                var secondLastRow = newRows[newRows.length - 2],
                    secondTds = $('td', secondLastRow),
                    targetTd = secondTds[tdParallelIndex],
                    linkCheck = $('a.ui-state-default', targetTd)[0];

                if (linkCheck) {
                    setHighlightState(linkCheck, cont);
                    linkCheck.focus();
                }

            }
        }, 0);
    }
}

//////////////// DOWN ////////////////
/**
 * Handles down arrow navigation through dates in calendar
 * @param  {HTMLElement} target   The target of the keyboard event (day)
 * @param  {HTMLElement} cont     The calendar container
 */
function downHandler(target, cont) {
    var nextLink = $('.ui-datepicker-next')[0];
    var targetRow = $(target).closest('tr');
    if (!targetRow) {
        return;
    }
    var targetCells = $('td', targetRow),
        cellIndex = $.inArray(target.parentNode, targetCells), // the td (parent of target) index
        nextRow = $(targetRow).next(),
        nextRowCells = $('td', nextRow),
        nextWeekTd = nextRowCells[cellIndex],
        nextWeekCheck = $('a.ui-state-default', nextWeekTd)[0];

    if (nextRow && nextWeekTd && nextWeekCheck) {
        // theres a next row, a TD at the same index of `target`,
        // and theres an anchor within that td
        setHighlightState(nextWeekCheck, cont);
        nextWeekCheck.focus();
    } else {
        nextLink.click();

        setTimeout(function () {
            var nextMonthTrs = $('tbody tr', cont),
                firstTds = $('td', nextMonthTrs[0]),
                firstParallel = firstTds[cellIndex],
                firstCheck = $('a.ui-state-default', firstParallel)[0];

            if (firstParallel && firstCheck) {
                setHighlightState(firstCheck, cont);
                firstCheck.focus();
            } else {
                // lets try the second row b/c we didnt find a
                // date link in the first row at the target's index
                var secondRow = nextMonthTrs[1],
                    secondTds = $('td', secondRow),
                    secondRowTd = secondTds[cellIndex],
                    secondCheck = $('a.ui-state-default', secondRowTd)[0];

                if (secondRow && secondCheck) {
                    setHighlightState(secondCheck, cont);
                    secondCheck.focus();
                }
            }
        }, 0);
    }
}

function prepHighlightState() {
    var highlight;
    var cage = document.getElementById('ui-datepicker-div');
    highlight = $('.ui-state-highlight', cage)[0] ||
        $('.ui-state-default', cage)[0];
    if (highlight && cage) {
        setHighlightState(highlight, cage);
    }
}

// Set the highlighted class to date elements, when focus is recieved
function setHighlightState(newHighlight, container) {
    $(getHighlightedDate(container)).removeClass('ui-state-highlight');
    $(newHighlight).addClass('ui-state-highlight');
}


// grabs the current date based on the highlight class
function getHighlightedDate(container) {
    return $('.ui-state-highlight', container)[0];
}

// append the corresponding value to the label of the next month and previous month buttons
function setLabelPrevNextButtons(buttons) {
    var isFrench = getIsFrench(),
        localizationDict = $.datepicker.regional[isFrench ? 'fr' : ''],
        monthNames = localizationDict.monthNames,
        monthNamesShort = localizationDict.monthNamesShort,
        calendarDivEl = $('#ui-datepicker-div'),
        monthEl = calendarDivEl.find('.ui-datepicker-month'),
        yearEl = calendarDivEl.find('.ui-datepicker-year'),
        currentMonth,
        currentYear,
        monthIndex;

    if (monthEl.is('select')) {
        currentMonth = monthEl.find('option:selected').text();
        monthIndex = $.inArray(currentMonth, monthNamesShort);
    } else {
        currentMonth = monthEl.text();
        monthIndex = $.inArray(currentMonth, monthNames);
    }

    if (yearEl.is('select')) {
        currentYear = yearEl.find('option:selected').text();
    } else {
        currentYear = yearEl.text();
    }

    currentYear = encodeURI(currentYear);

    (buttons || calendarDivEl.find('.ui-datepicker-prev, .ui-datepicker-next')).each(function () {
        var buttonEl = $(this),
            buttonText,
            isNext = buttonEl.hasClass('ui-datepicker-next'),
            newIndex = isNext ? monthIndex + 1 : monthIndex - 1,
            newYear = currentYear;

        if (isNext && monthIndex === 11) {
            newYear = parseInt(currentYear, 10) + 1;
            newIndex = 0;
        } else if (!isNext && monthIndex === 0) {
            newYear = parseInt(currentYear, 10) - 1;
            newIndex = monthNames.length - 1;
        }

        if (isFrench) {
            buttonText = isNext
                ? 'Le mois prochain, ' + firstToCap(monthNames[newIndex]) + ' ' + newYear
                : 'Le mois précédent, ' + firstToCap(monthNames[newIndex]) + ' ' + newYear;
        } else {
            buttonText = isNext
                ? 'Next month, ' + firstToCap(monthNames[newIndex]) + ' ' + newYear
                : 'Previous month, ' + firstToCap(monthNames[newIndex]) + ' ' + newYear;
        }

        buttonEl.find('.ui-icon').html(buttonText);
    });
}

// Returns the string with the first letter capitalized
function firstToCap(s) {
    return s.charAt(0).toUpperCase() + s.slice(1);
}

// this blocks the space (32) key
function preventSpaceKeydown(e) {
    var key = e.which || e.keyCode || 0;

    if (32 === key) {
        e.preventDefault();
        e.stopPropagation();
    }
}

// function to get the id of the passed element. if no id is set, a new one is generated, set, and returned
function getSetId(el) {
    var triggerId = el.id;

    if (!triggerId) {
        triggerId = Math.random().toString(36).substring(2) + (new Date()).getTime().toString(36);
        el.id = triggerId;
    }

    return triggerId;
}

// returns the id of the passed input's label. if the label has no id, a new one is set and returned
function getInputLabelId(input) {
    var inputLabel = input.attr('aria-labelledby'), id;

    if (inputLabel != null && inputLabel.length > 0) {
        return inputLabel;
    }

    inputLabel = input.closest('label');
    if (inputLabel.length === 0) {
        inputLabel = $('label[for=' + input.attr('id') + ']');
    }

    if (inputLabel.length > 0) {
        id = inputLabel.attr('id');
        if (id != null && id.length > 0) {
            return id;
        }

        id = 'label-' + (new Date()).getTime();
        inputLabel.attr('id', id);
        return id;
    }

    return null;
}

function getIsFrench() {
    return $('html').attr('lang').substring(0, 2).toLowerCase() === 'fr';
}