/***************************************************************
    Following code is for simplified header and footer, expected
    to be merged with virgin.css after the review of
    BRF team. If not approved, it should be removed from TFS.
    ----------------------------------------------------------
***************************************************************/

.simplified-header {
    background: #e10a0a none repeat scroll 0 0;
    box-shadow: 0 0 50px 0 rgba(0,0,0,0.2);
    height: 75px;
    letter-spacing: 0.5px;
    position: relative;
    text-align: center;
    z-index: 999;
}
.simplified-header .page-heading {
    left: 0;
    right: 0;
    z-index: 0;
}
.simplified-header .page-back-button, .simplified-header .page-right-button {
    z-index: 1;
}
.simplified-header .responsive-simplified-header-back {
    text-decoration: none;
}
.sans-serif {
    font-family:Arial, Helvetica, sans-serif;
}
.simplified-footer .footer-links li {
    border-right: 1px solid #333333;
    padding-left: 20px;
    line-height: 1;
}
.simplified-footer .footer-links li:first-child {
    padding-left: 0px;
}
.simplified-footer .footer-links li:last-child {
    border-right: none;
}
@media screen and (max-width:767.98px) {
    .simplified-footer .footer-links li {
        padding: 0 !important;
        border-right: none;
        margin-bottom: 15px;
    }
    .simplified-footer .footer-links li a {
        margin: 0 !important;
    }
}

/**************************************************************
    Following styles expected to be inherit from virgin.css
    -------------------------------------------------------
***************************************************************/

body {
    background-color: #EFEFEF;
}

.btn {
    border-radius: 2px;
    font-size: 16px;
    font-weight: bold;
    padding: 11px 26px; /* 4px less on each side because of borders */
    cursor: pointer;
    font-family: Arial, Helvetica, sans-serif;
}

.btn-secondary-inverted {
    background: none;
    border: 2px solid #fff;
    color: #FFF;
}

.btn-secondary {
    background: none;
    border: 2px solid #333;
    color: #333;
}

/***************************************************************
    Following should be in allBrowser_Framework.css
    -------------------------------------------------------
***************************************************************/

.line-height-1 {
    line-height: 1;
}