import * as React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, PBEFooter, PBEOverageDiagram, MobilityViewTable } from "singleban-components";
import { InjectedIntlProps, injectIntl } from "react-intl";
import { IPBE, ITier } from "../../models";
import { REGULAR, SHAREGROUP, FLEX, GB, MB } from "../../../src/utils/Constants";
import { modalOpenedOmniture } from "../../utils/Utility";
import { CURRENCY_OPTIONS} from "../../utils/Utility";
interface Props {
    pbe: IPBE;
    isPBEModalLinkDisabled: boolean;
}

const getTierName = (tiers: ITier[], intl: ReactIntl.InjectedIntl) => {
    const nonExceededTierRateDetail = tiers.find((tier) => tier.dataUsed !== "Exceeded" && tier.dataUsed !== "")?.rateDetail ?? "";
    if (!!nonExceededTierRateDetail && nonExceededTierRateDetail !== "") {
        if (nonExceededTierRateDetail.indexOf(".") !== -1) {
            let rateDetail = "";
            rateDetail = nonExceededTierRateDetail.replace('.00', '');
            return {
                tierName: `${rateDetail}`,
                tierFullName: `${rateDetail}`
            }
        }
        else {
            return {
                tierName: `${nonExceededTierRateDetail}`,
                tierFullName: `${nonExceededTierRateDetail}`
            }
        }
    } else {
        return {
            tierName: "",
            tierFullName: ""
        }
    }
}

const PBEMobilityDataOverage = (props: Props & InjectedIntlProps) => {
    const { intl: { formatMessage, formatHTMLMessage, formatNumber }, pbe } = props;
    const title = formatMessage({ id: "PBE_MOBILITY_DATA_OVERAGE_TITLE1" });
    const Overagetype = pbe.pbeDataBag.overageType;
    const overageDataUnitSpecified = (pbe?.pbeDataBag?.overageDataUnit === "GB" || pbe?.pbeDataBag?.overageDataUnit === "Go") ? GB : MB;
    const description1 = formatHTMLMessage({ id: Overagetype === REGULAR ? "PBE_MOBILITY_DATA_OVERAGE_DESCRIPTION_1" : Overagetype === SHAREGROUP ? "PBE_MOBILITY_DATA_OVERAGE_DESCRIPTION_2" : "PBE_MOBILITY_DATA_OVERAGE_DESCRIPTION_3" }, {
        overageValue: formatNumber(pbe?.pbeDataBag?.overageDataValue),
        unit: (pbe?.pbeDataBag?.overageDataUnit === "GB" || pbe?.pbeDataBag?.overageDataUnit === "Go") ? formatMessage({ id: "GB_DATA_UNIT" }) : formatMessage({ id: "MB_DATA_UNIT" }),
        unitName: (pbe?.pbeDataBag?.overageDataUnit === "GB" || pbe?.pbeDataBag?.overageDataUnit === "Go") ? formatMessage({ id: "GB_DATA_UNIT_FULL_FORM" }) : formatMessage({ id: "MB_DATA_UNIT_FULL_FORM" }),
        amount: (pbe?.pbeDataBag?.overageDataUnit === "GB" || pbe?.pbeDataBag?.overageDataUnit === "MB")? pbe?.pbeDataBag?.overageDataCharge.toFixed(2): pbe?.pbeDataBag?.overageDataCharge.toFixed(2).replace(".", ","),
        dollarAmount: Math.trunc(pbe?.pbeDataBag?.overageDataCharge),
        cents: pbe?.pbeDataBag?.overageDataCharge - Math.trunc(pbe?.pbeDataBag?.overageDataCharge),
        ...getTierName(props?.pbe?.pbeDataBag?.tiers, props?.intl)
    })
    const description2 = pbe.pbeDataBag.overageType === SHAREGROUP ? formatHTMLMessage({ id: "PBE_MOBILITY_DATA_OVERAGE_DESCRIPTION_2.1" }) : null;
    const anchorTag = formatHTMLMessage({ id: "PBE_MOBILITY_DATA_OVERAGE_VIEW_DETAILS" }, {
        viewDetailedUsageHref: formatMessage({ id: "PBE_MOBILITY_DATA_OVERAGE_VIEW_DETAILS_LINK" }, {
            acctNo: pbe?.pbeDataBag?.encryptedBan,
            subNo: pbe?.pbeDataBag?.subNo,
            seqNo: pbe?.pbeDataBag?.latestBillNo,
            billCycle: pbe?.pbeDataBag?.billCycle,
            billCycleMonth: pbe?.pbeDataBag?.billCycleMonth
        })
    });
    const overageDiagramPath = "PBE_MOBILITY_DATA_OVERAGE_DIAGRAM_PATH"
    const overageValue = formatNumber(pbe?.pbeDataBag?.overageDataValue);
    const showUsageLink = pbe?.pbeDataBag?.showUsageLink;
    const serviceName = pbe?.pbeDataBag?.serviceName;
    const overageBy = pbe?.pbeDataBag?.identifier;
    const nickName = pbe?.pbeDataBag?.nickname;
    const overageByLabel = formatMessage({ id: "PBE_MOBILITY_DATA_OVERAGE_BY" });
    
    const footerItems = showUsageLink ? [{
        ctaLink: formatMessage({ id: "PBE_MOBILITY_DATA_OVERAGE_MOBILITY_LINK_1" }, {
            acctNo: pbe?.pbeDataBag?.encryptedBan,
            subNo: pbe?.pbeDataBag?.subNo
        }),
        iconClassName: "icon-10_usage",
        titleKey: formatMessage({ id: "PBE_MOBILITY_DATA_OVERAGE_FOOTER_1" }),
        ctaTitleKey: formatMessage({ id: "PBE_MOBILITY_DATA_OVERAGE_FOOTER_1_SUBTITLE" }),
        isFirstRow: true,
        id: "pbe-Mobility-device-Charge"
    },
    {
        ctaLink: formatMessage({ id: "PBE_MOBILITY_DATA_OVERAGE_MOBILITY_LINK_2" }, {
            acctNo: pbe?.pbeDataBag?.encryptedBan,
            subNo: pbe?.pbeDataBag?.subNo,
            mdn: pbe?.pbeDataBag?.identifier
        }),
        iconClassName: "icon-09_unlimited",
        titleKey: formatMessage({ id: (Overagetype === FLEX) ? "PBE_MOBILITY_DATA_OVERAGE_FOOTER_2_TIER" : "PBE_MOBILITY_DATA_OVERAGE_FOOTER_2" }),
        ctaTitleKey: formatMessage({ id: "PBE_MOBILITY_DATA_OVERAGE_FOOTER_2_SUBTITLE" }),
        isFirstRow: false,
        id: "pbe-Mobility-device"
    }]
    :
    [{
        ctaLink: formatMessage({ id: "PBE_MOBILITY_DATA_OVERAGE_MOBILITY_LINK_2" }, {
            acctNo: pbe?.pbeDataBag?.encryptedBan,
            subNo: pbe?.pbeDataBag?.subNo,
            mdn: pbe?.pbeDataBag?.identifier
        }),
        iconClassName: "icon-09_unlimited",
        titleKey: formatMessage({ id: (Overagetype === FLEX) ? "PBE_MOBILITY_DATA_OVERAGE_FOOTER_2_TIER" : "PBE_MOBILITY_DATA_OVERAGE_FOOTER_2" }),
        ctaTitleKey: formatMessage({ id: "PBE_MOBILITY_DATA_OVERAGE_FOOTER_2_SUBTITLE" }),
        isFirstRow: false,
        id: "pbe-Mobility-device"
    }];
    React.useEffect(() => {        
        modalOpenedOmniture(props?.pbe?.triggerPbeOmniture, title, description1+description2);
    }, [title, description1, description2]);
    return (
        <>
            {(pbe.pbeDataBag.overageType === SHAREGROUP || pbe.pbeDataBag.overageType === REGULAR) ? (
                <>
                    <PBEHeader descriptionKey={description1} titleKey={title} showUsageLink={showUsageLink} descriptionKey2={description2} IsStyleApplicable={true} anchorTag={anchorTag} isHTMLDescription={true} />
                    <PBEOverageDiagram overageValue={overageValue} overageDiagramPath={overageDiagramPath} overageBy={overageBy} serviceName={serviceName} serviceNickname={nickName} overageByLabel={overageByLabel} overageUnit={overageDataUnitSpecified} showNote={false} />
                </>
            ) : (
                <>
                    <PBEHeader descriptionKey={description1} titleKey={title} showUsageLink={showUsageLink} descriptionKey2={description2} IsStyleApplicable={true} anchorTag={anchorTag} isHTMLDescription={true} serviceId={pbe.pbeDataBag.identifier} serviceName={serviceName} serviceNickName={nickName} />
                    <MobilityViewTable TierItem={pbe.pbeDataBag.tiers} />
                </>)}
            <PBEFooter footerItems={footerItems} isPBEModalLinkDisabled={props.isPBEModalLinkDisabled}/>
        </>
    );
};


export default (injectIntl(PBEMobilityDataOverage));
