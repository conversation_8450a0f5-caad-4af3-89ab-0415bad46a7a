import * as React from "react";
import { InjectedIntlProps, injectIntl } from "react-intl";
import { P<PERSON>Header, PBEFooter } from "singleban-components";
import { IPBE } from "../../models";
import { CURRENCY_OPTIONS, modalOpenedOmniture } from "../../utils/Utility";

interface Props {
    pbe: IPBE;
    isPBEModalLinkDisabled: boolean;
}

const PBEMobilityLongDistance = (props: Props & InjectedIntlProps) => {
    const { intl: { formatMessage, formatNumber }, pbe } = props;
    const title = formatMessage({ id: "PBE_MOB_LONG_DISTANCE_TITLE" });
    const description = formatMessage({ id: "PBE_MOB_LONG_DISTANCE_DESC" }, {
        totalTextMessages: pbe?.pbeDataBag?.totalCalls,
        totalCharge: formatNumber(pbe?.pbeDataBag?.totalCharge, CURRENCY_OPTIONS)
    });
    const imageClassName = "icon-05_long_distance_circle";
    const PBEFooterItems = [(pbe?.pbeDataBag?.showUsageLink ? {
        ctaLink: formatMessage({ id: "PBE_MOB_USAGE_LINK" }, {
            acctNo: pbe?.pbeDataBag?.encryptedBan,
            subNo: pbe?.pbeDataBag?.subNo,
            seqNo: pbe?.pbeDataBag?.seqNo
        }),
        iconClassName: "icon-10_usage",
        titleKey: formatMessage({ id: "PBE_MOB_LONG_DISTANCE_FOOTER_LINK_1_DESC" }),
        ctaTitleKey: formatMessage({ id: "PBE_MOB_LONG_DISTANCE_FOOTER_LINK_1_CTA" }),
        isFirstRow: true,
        id: "pbe-mob-long-distance-usage"
    } : null), {
        ctaLink: formatMessage({ id: "PBE_MOB_RATE_PLAN_LINK" }, {
            acctNo: pbe?.pbeDataBag?.encryptedBan,
            subNo: pbe?.pbeDataBag?.subNo,
        }),
        iconClassName: "icon-17_world",
        titleKey: formatMessage({ id: "PBE_MOB_LONG_DISTANCE_FOOTER_LINK_2_DESC" }),
        ctaTitleKey: formatMessage({ id: "PBE_MOB_LONG_DISTANCE_FOOTER_LINK_2_CTA" }),
        isFirstRow: false,
        id: "pbe-mob-long-distance-upgrade"
    }];
    React.useEffect(() => {
        modalOpenedOmniture(props?.pbe?.triggerPbeOmniture, title, description);
    },[title, description]);

    return (
        <>
            <PBEHeader descriptionKey={description} titleKey={title} iconClassName={imageClassName} />
            <PBEFooter footerItems={PBEFooterItems} isPBEModalLinkDisabled={props.isPBEModalLinkDisabled} />
        </>
    );
};


export default (injectIntl(PBEMobilityLongDistance));