.active_tabs::after{display: none}
.pad-40{padding:40px}
.pad-30{padding:30px}
.pad-20{padding:20px}
.pad-right-20{padding-right:20px}
.pad-right-15{padding-right:15px}
.pad-right-10{padding-right:10px}
.display-result{border-bottom:1px solid #d4d4d4}
.display-result.last{border-bottom:none}
.border-light-gray{border:1px solid #d3d3d3}
.border-light-gray-right{border-right:1px solid #d3d3d3}
.border-gray-right-lg-md{border-right:1px solid #d3d3d3}
a.accordion-toggle {
    display: block;
    width: 100%;
    height: 100%;
    padding:20px 30px;
    /*outline:none*/
}
ul.search-result-list{padding:0;margin:0}
ul.search-result-list li {
    list-style:none;
    display:none
}
.device-price-info,.cta-float-LR{float:right}
.col2{width:50%}
.icon.icon-exapnd-outline-circled.show-filters-mobile::before{top:0;left:0}
.bgGrayLight5 a.txtBlue-hover:hover,.bgGrayLight5 a.txtBlue-hover:focus{color:#00549a}

/* Search Form */
.search-bar [type="search"] {
    position: relative;
    width: 100%;
    padding-right: 175px;
    padding-left: 40px;
    border: 0;
    background-color: #fff;
    color: #555;
    height:70px;
    border-radius: 60px;
    display: inline-block;
    font-size:18px;
    border:1px solid #c8c8c8;
    -webkit-box-shadow: inset 1px 1px 2px 1px rgba(240,235,240,1);
    -moz-box-shadow: inset 1px 1px 2px 1px rgba(240,235,240,1);
    box-shadow: inset 1px 1px 2px 1px rgba(240,235,240,1);
}
.search-bar [type="search"]::-ms-clear {
    display: none;
}
.search-bar [type="reset"]{
    position: relative;
    float:right;
    margin-top:-45px;
    background:none;
    border: 0;
    right: 135px;
    width: 40px;
    display: none;
}
.search-bar [type="submit"] {
    position: relative;
    margin-top: -70px;
    padding:22px 0;
    border: 0;
    float: right;
}
.search-bar [type="reset"]:focus:after,
.search-bar [type="submit"]:focus:after {
    color: #00549A;
}
.search-bar [type="reset"]:after,
.search-bar [type="reset"]:before,
.search-bar [type="submit"]:after {
    display: block;
    position: absolute;
    top: 50%;
    left: 0%;
}
.search-bar [type="reset"]:after,
.search-bar [type="submit"]:after {
    font-family: 'bell-icon';
    line-height: 1;
}
.search-bar [type="reset"]:focus .icon{
    opacity: 1;
}
.search-bar [type="reset"] .icon {
    color: #bbb;
    font-size: 18px;
}
.search-bar [type="reset"].active {
    display: block;
}

.search-bar [type="submit"] {
    width: 45px;
}
.search-bar [type="submit"]:after {
    -webkit-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
    font-size: 28px;
    color: #003778;
}
.search-bar .search-btn{
    width:130px;
    color:#fff;
    font-size:18px;
    height:70px;
    background-color:#003778;
    border-radius: 0px 60px 60px 0px;
    -moz-border-radius: 0px 60px 60px 0px;
    -webkit-border-radius: 0px 60px 60px 0px;
}
.search-bar input[type="search"]::-webkit-input-placeholder {
    color: #bebebe;
}
.search-bar input[type="search"]::-moz-placeholder {
    color: #bebebe;
}
.search-bar input[type="search"]:-ms-input-placeholder {
    color: #bebebe;
}
.search-bar input[type="search"]:-moz-placeholder {
    color: #bebebe;  
}
.footer-modal-shadow{
-webkit-box-shadow: 0px -5px 20px -6px rgba(0,0,0,0.3);
-moz-box-shadow: 0px -5px 20px -6px rgba(0,0,0,0.3);
box-shadow: 0px -5px 20px -6px rgba(0,0,0,0.3);
}

/* search bar autocomplete*/
.search-bar-wrap .caret_top-lg::after,
.search-bar-wrap .caret_top-lg.caret_outline::before,
.search-bar-wrap .caret_bottom-lg::after,
.search-bar-wrap .caret_bottom-lg.caret_outline::before {
    left: 70px;
    border-width:22px
}
.search-bar-wrap .ui-autocomplete {
    display: block ;
    float: none;
    top: 215px!important;
    right: auto ;
    bottom: auto ;
    left: auto ;
    padding: 0px;
    transition: height .35s cubic-bezier(.55,0,.1,1),
        padding .35s cubic-bezier(.55,0,.1,1);
    background-color: #fff;
    box-shadow: 0 0 40px rgba(0,0,0, .3);
    position: absolute;
}
.search-bar-wrap ul.ui-autocomplete > li.ui-menu-item{
    padding: 10px 20px;
    list-style: none;
    cursor:pointer
}
.search-bar-wrap ul.ui-autocomplete > li.ui-menu-item > a.ui-corner-all{
    text-decoration: none;
    color: #555555;
    cursor: pointer;
    display: block;
}
.search-bar-wrap ul.ui-autocomplete > li.ui-menu-item:hover{
    background-color: #e1e1e1;
}
.search-bar-wrap .ui-autocomplete-term{
    font-weight: bold;
}
.search-bar-wrap .ui-autocomplete:empty {
    height: 0;
    padding-top: 0;
    padding-bottom: 0;
}
.search-bar-wrap .ui-autocomplete:empty:after {
    content: none;
}
.search-bar-wrap .ui-menu-item,
.search-bar-wrap .ui-menu-item > a {
    color: #000;
}
.search-bar-wrap .ui-menu-item {
    margin:2px -4px
}
.search-bar-wrap .ui-menu-item > a:hover,
.search-bar-wrap .ui-menu-item > a:active {
    background-color: #e2e2e2;
}
.search-bar-wrap .ui-menu-item .ui-autocomplete-term {
    font-weight: bold;
}
.modal .modal-dialog.modal-lg.bell-modal-lg.search-filers-modal {
    background-color: #00549a;
}
.modal-header.bgBlueDark {
    border-bottom: none;
}
.col-xs-modal-filters{width:50%}
.modal-divider-xs{display:none}

/*Media queries*/
@media(max-width:380.98px){.show-results-txt,.show-filter-txt{font-size:17px}.show-filters-mobile{font-size:24px}}
@media(max-width:519.98px){.col-xs-modal-filters{width:100%}.modal-divider-xs{display:block}}
@media(max-width:991.98px){.pad-40{padding:20px}.device-price-info,.cta-float-LR{float:left}.txtCenter-sm{text-align:center}
.search-bar .search-btn{background:none!important}
.search-bar [type="submit"] {padding:14px 0;margin-right:-20px}
.search-bar [type="reset"]{right: 70px;}
.search-bar [type="search"] {padding-right:110px}}
@media(max-width:767.98px){.search-filers-modal .btn.btn-default-white,.search-filers-modal .btn.btn-primary-white{width:100%}.sans-serif-xs{font-family:"Helvetical",Arial, sans-serif}img.device-search{width:55px}.txtCenter-xs{text-align:center}.border-gray-top-sm-xs{border-top:1px solid #d3d3d3}.border-gray-right-lg-md{border-right:none}}
