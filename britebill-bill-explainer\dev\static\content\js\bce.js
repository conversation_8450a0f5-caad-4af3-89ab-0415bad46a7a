//v1.1
var BCE = {
    init: function () {

        this.initializeCardSlider('.slidingCardsContainer', 3);
        this.resizeCardSlider();
        this.improvedCardSliderAccessibility();
        this.resizeInPageNavigation();
        this.onResizeCheckConnectorArea();
        this.initCardAccordionEvents();
        this.initializeScrollableTable();
        this.initalizeModal();
        this.scrollToActiveTabListMobile();
        this.pageEvent();
        this.resizeTableContentContainer('threeColumn_altColor_table_w_link');
    },

    pageEvent: function () {
        $(document).on('click', '.accordion-tog', function () {
            var $this = $(this);
            if ($this.attr('expanded-true') !== undefined) {
                if ($this.attr('aria-expanded') === 'true') {
                    $this.find('span').html($this.attr('expanded-true'));
                } else {
                    $this.find('span').html($this.attr('expanded-false'));
                }
            }
        });

        $(document).on('click', '[add-table-row="true"]', function () {
            var $this = $(this);
            BCE.addTableRow($this.attr('data-target'), $this.attr('items'), $this.attr('data-element'), $this.attr('row-count'));
        });

    },

    addTableRow: function (target, items, element, rowcount) {
        var count = rowcount !== undefined && rowcount === "true" ? $(target).children().length : 0;
        for (x = 1; x <= items; x++) {
            $(target).append(count > 0 ? element.replace('$rowcount', count + x) : element);
        }
    },

    resizeTableContentContainer: function (className) {
        $('.' + className + ' tbody tr p').css({ "height": "" });
        var $trs = $('.' + className + ' tbody tr');

        $trs.each(function () {
            var $tr = $(this);
            var $ps = $tr.find('td:first-child > p');
            var pchilds = 1;

            $ps.each(function () {
                var pheight = 0;
                var p = $tr.find('td p:nth-child(' + pchilds + ')');
                p.each(function () {
                    pheight = $(this).innerHeight() > pheight ? $(this).innerHeight() : pheight;
                });
                p.css({ "height": pheight + "px" });
                pchilds += 1;
            });
        });
    },

    scrollToActiveTabListMobile: function () {
        if ($('.bce-inpage-navigation').length > 0) {
            var el = $('.bce-tablist').find('.active');
            if (!el.is(':first-child')) {
                $('.bce-inpage-navigation').scrollLeft(el.offset().left - 30);
            }

        }

    },

    initalizeModal: function () {
        $(document).on('click', 'a[data-toggle="modal"]', function () {
            BCE.resizeModalBody($($(this).attr("data-target")));

        });
    },

    resizeModalBody: function ($this) {
        setTimeout(function () {

            var h = $(window).outerHeight() - 84;
            $('.bce-modal .modal-content').css({ "max-height": h + "px" });

            var headerHeight = $this.find('.bce-modal-header') ? $this.find('.bce-modal-header').innerHeight() : 0;
            var bodyHeight =h - headerHeight;
            $this.find('.bce-modal-body').css({ "max-height": bodyHeight + "px" });

        }, 200);

    },

    initializeScrollableTable: function () {
        $('[class^=scrollableContainer]').on('scroll', function () {
            var $this = $(this);
            var scrollPos = $this.scrollLeft();
            var width = $this.width();
            var scrollWidth = $this.get(0).scrollWidth;
            var container = $this.closest('[class^=scrollableContainerShadow]');

            if (scrollPos === 0) {
                container.removeClass('left');
            } else {
                container.addClass('left');
            }

            if (scrollPos + width === scrollWidth) {
                container.removeClass('right');
            } else {
                container.addClass('right');
            }
        });
    },

    resized: function () {
        this.resizeCardSlider();
        this.resizeInPageNavigation();
        this.onResizeCheckConnectorArea();
        this.resizeModalBody($('body').find(".modal.show"));
        this.resizeTableContentContainer('threeColumn_altColor_table_w_link');
    },

    initializeCardSlider: function (target, slideInitialViewValue) {
        var that = this;
        var slickSlideCount = $(target).find('.slickSlide').length;

        if ($(target).length > 0) {

                var fnSwitchDisplayArrow = function (target) {
                    var targetEl = target;

                    setTimeout(function () {
                        if (that.getWindowWidth() > 639) {
                            $(targetEl).find('.slick-arrow').each(function () {
                                if ($(this).hasClass('slick-disabled')) {
                                    $(targetEl).find('.slick-arrow.slick-disabled').css('display', 'none');
                                } else {
                                    $(targetEl).find('.slick-arrow').css('display', 'inline-block');
                                }
                            });
                        } else {
                            $(targetEl).find('.slick-arrow').css('display', 'none');
                        }

                    }, 0);
                };

                var fnRemoveDotLabels = function (target) {
                    var targetEl = target;

                    setTimeout(function () {
                        $(targetEl).find('.slick-arrow button').removeAttr('aria-label');
                    }, 0);
                };

                var fnAddAriaAccessibilitybyToSlide = function (target, slideView) {
                    var targetEl = target,
                        slideCount = $(targetEl).find('.slick-track .slick-slide').length,
                        slideDotsCount = $(targetEl).find('.slick-dots li').length;

                    var dotIdArray = [];
                    var slideIdArray = [];
                    $(targetEl).find('.slick-track .slick-slide').each(function () {
                        var slide = $(this),
                            slideIndex = slide.index() + 1;

                        $(targetEl).find('.slick-dots li').each(function () {
                            $(this).find('button').removeAttr('aria-label');

                            var dotIndex = $(this).index() + 1;
                            var dotId = $(this).find('button').attr('id');
                            var slideId = slide.attr('id');

                            if (slideIndex > slideView) {
                                if (dotIndex > 1 && $.inArray(dotId, dotIdArray) === -1) {
                                    slide.attr('aria-describedby', dotId);
                                    $(this).find('button').attr('aria-controls', slideId);
                                    dotIdArray.push(dotId);
                                    return false;
                                }
                            } else {
                                if (dotIndex === 1) {
                                    slideIdArray.push(slideId);

                                    if (slideIndex === slideView) {
                                        var ariaControlVal = '';
                                        for (var i = 0; i < slideIdArray.length; i++) {
                                            ariaControlVal = ariaControlVal + ' ' + slideIdArray[i];
                                        }
                                        $(this).find('button').attr('aria-controls', ariaControlVal);
                                    }

                                    slide.attr('aria-describedby', dotId);
                                    return false;
                                }
                            }
                        });

                    });

                };

                var fnCheckBreakPoint = function (target, slideView) {
                    setTimeout(function () {
                        if (that.getWindowWidth() > 991) {

                            fnAddAriaAccessibilitybyToSlide(target, 3);
                        } else if (that.getWindowWidth() > 639) {
                            fnAddAriaAccessibilitybyToSlide(target, 2);
                        } else {
                            fnAddAriaAccessibilitybyToSlide(target, 1);
                        }
                    }, 0);
                };

                $(target).slick({
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    infinite: false,
                    variableWidth: true,
                    dots: true,
                    responsive: [
                        {
                            breakpoint: 992,
                            settings: {
                                slidesToShow: 2,
                                slidesToScroll: 1,
                                variableWidth: true,
                                dots: true,
                                focusOnSelect: true
                            }
                        },
                        {
                            breakpoint: 640,
                            settings: {
                                slidesToShow: 1,
                                slidesToScroll: 1,
                                infinite: false,
                                dots: true,
                                centerMode: true,
                                centerPadding: '30px',
                                focusOnSelect: true,
                                arrows: false,
                                variableWidth: true
                            }
                        }
                    ]
                }).on('setPosition', function () {
                    BCE.resizeCardSlider();
                }).on('beforeChange', function (event, slick, currentSlide, nextSlide) {
                    var focusedEl = $(document.activeElement);
                    if (focusedEl.length > 0 && (focusedEl.attr('id') || "").indexOf('slick-slide-control') > -1) {
                        // focused el was a slick dot so slide was changed using arrow keys. move focus accordingly
                        $(this).find('.slick-dots li').eq(nextSlide).find('button').focus();
                    }
                }).on('afterChange', function (event, slick, currentSlide, nextSlide) {
                    BCE.changeCardSliderSwipePos(event, slick, currentSlide, nextSlide);

                    var slickDots = $(this).find('.slick-dots');

                    if (slickDots.length > 0) {
                        slickDots = slickDots.find('li');
                        slickDots.find('button').attr('tabindex', '-1');
                        slickDots.eq(currentSlide).find('button').attr('tabindex', '0');
                        setTimeout(function () {
                            slickDots.removeAttr('aria-controls aria-selected id');
                        }, 0);
                    }
                    fnRemoveDotLabels(this);
                    fnCheckBreakPoint(this);
                    fnSwitchDisplayArrow(this);
                }).on('breakpoint', function () {
                    fnRemoveDotLabels(this);
                    fnCheckBreakPoint(this);
                    fnSwitchDisplayArrow(this);
                });

                // initial run
                fnRemoveDotLabels(target);
                fnCheckBreakPoint(target);
                fnSwitchDisplayArrow(target);

                //For Removing the class hiding the slider when the page is loading
                $(target).closest('.slider-wrapper-init').removeClass('slider-wrapper-init');

            }

    },

    getWindowWidth: function () {
        var windowWidth = $(window).outerWidth();
        return windowWidth;
    },

    getCardSliderLastIndex: function () {
        $slickSlider = $('.slidingCardsContainer');
        var slideLength = $slickSlider.find('.slick-track .slick-slide:last-child').attr('data-slick-index');
        slideLength = parseInt(slideLength);

        return slideLength;
    },

    getCardSliderLength: function () {
        $slickSlider = $('.slidingCardsContainer');
        var slideLength = $slickSlider.find('.slick-track .slick-slide:last-child').attr('data-slick-index');

        slideLength = parseInt(slideLength) + 1;

        return slideLength;
    },

    getCardSliderMobilePad: function () {
        var mobilePad = $('.slidingCardsSection .container.liquid-container').css('padding-left');
        return parseInt(mobilePad, 10);
    },

    getCardSliderGap: function (pSelector) {
        $slickSlider = $('.slidingCardsContainer');
        var gap = $slickSlider.find('.slick-slide').css('margin-right');
        return gap = parseInt(gap, 10);
    },

    getCardSliderShowedSpace: function () {
        var showedSpace = 80;
        return showedSpace;
    },

    resizeCardSlider: function () {
        this.resizeCardSliderHeight();

        if (this.getWindowWidth() > 991) {
            this.resizeCardSliderWidthDesktop();
        } else if (this.getWindowWidth() > 639) {
            this.resizeCardSliderWidthTablet();
        } else {
            this.resizeCardSliderWidthMobile();
        }
    },

    initCardAccordionEvents: function () {
        if ($('.card-collapsible-child').length > 0) {
            var closeOnClickAccordion = true;
            $(document).click(function (e) {
                if ($('.card-collapsible-child.expanded').length > 0) {
                    var $findCard = $(e.target).closest('.card-collapsible-child.expanded');
                    if ($findCard.length <= 0) {
                        var $activeCard = $('.card-collapsible-child.expanded');
                        var $activeAccordionElem = $activeCard.find('.bce-card-collapsible');
                        $activeCard.removeClass('expanded');
                        $activeAccordionElem.attr('aria-expanded', 'false');
                        $activeCard.find('.card-collapsible-child-content').attr('aria-hidden', 'true');
                        $activeAccordionElem.find('i').addClass('icon-exapnd-outline-circled').removeClass('icon-collapse-outline-circled');
                    }
                }
            });

            $('body a').keyup(function (e) {
                if (e.which == 9) {
                    if ($('.card-collapsible-child.expanded').length > 0) {
                        var $findCard = $(this).closest('.card-collapsible-child.expanded');
                        if ($findCard.length <= 0) {
                            var $activeCard = $('.card-collapsible-child.expanded');
                            var $activeAccordionElem = $activeCard.find('.bce-card-collapsible');
                            $activeCard.removeClass('expanded');
                            $activeAccordionElem.attr('aria-expanded', 'false');
                            $activeCard.find('.card-collapsible-child-content').attr('aria-hidden', 'true');
                            $activeAccordionElem.find('i').addClass('icon-exapnd-outline-circled').removeClass('icon-collapse-outline-circled');
                        }
                    }
                }
                
            });

            $('.bce-card-collapsible').click(function () {
                if ($(this).attr('aria-expanded') === 'false') {
                    var $parentCont = $(this).closest('.card-collapsible-cont');
                    var $activeCard = $parentCont.find('.card-collapsible-child.expanded');
                    var $activeAccordionElem = $activeCard.find('.bce-card-collapsible');
                    $activeCard.removeClass('expanded');
                    $activeAccordionElem.attr('aria-expanded', 'false');
                    $activeCard.find('.card-collapsible-child-content').attr('aria-hidden', 'true');
                    $activeAccordionElem.find('i').addClass('icon-exapnd-outline-circled').removeClass('icon-collapse-outline-circled');

                    var $clickCard = $(this).closest('.card-collapsible-child');
                    $clickCard.addClass('expanded');
                    $(this).attr('aria-expanded', 'true');
                    $clickCard.find('.card-collapsible-child-content').attr('aria-hidden', 'false');
                    $(this).find('i').removeClass('icon-exapnd-outline-circled').addClass('icon-collapse-outline-circled');
                    closeOnClickAccordion = false;
                    $(this).closest('.card-collapsible-child').find('.card-collapsible-child-close').trigger('focus');
                }
                else {
                    var $clickCardRemove = $(this).closest('.card-collapsible-child');
                    $clickCardRemove.removeClass('expanded');
                    $(this).attr('aria-expanded', 'false');
                    $clickCardRemove.find('.card-collapsible-child-content').attr('aria-hidden', 'true');
                    $(this).find('i').addClass('icon-exapnd-outline-circled').removeClass('icon-collapse-outline-circled');
                }

                return false;
            });

            $('.bce-card-collapsible').keyup(function (e) {
                if (e.which == 9) {
                    if ($(this).closest('.card-collapsible-child.expanded').length <= 0) {
                        var $parentCont = $(this).closest('.card-collapsible-cont');
                        var $activeCard = $parentCont.find('.card-collapsible-child.expanded');
                        var $activeAccordionElem = $activeCard.find('.bce-card-collapsible');
                        $activeCard.removeClass('expanded');
                        $activeAccordionElem.attr('aria-expanded', 'false');
                        $activeCard.find('.card-collapsible-child-content').attr('aria-hidden', 'true');
                        $activeAccordionElem.find('i').addClass('icon-exapnd-outline-circled').removeClass('icon-collapse-outline-circled');
                    }
                }
                return false;
            });

            $('.card-collapsible-child-close').click(function () {
                var $parentElem = $(this).closest('.card-collapsible-child');
                var $accordionElem = $parentElem.find('.bce-card-collapsible');
                $accordionElem.find('i').addClass('icon-exapnd-outline-circled').removeClass('icon-collapse-outline-circled');
                $accordionElem.attr('aria-expanded', 'false');
                $parentElem.find('.card-collapsible-child-content').attr('aria-hidden', 'true');
                $parentElem.removeClass('expanded');
                $accordionElem.focus();
            });

            $('.card-collapsible-child-close').keyup(function (e) {
                if (e.keyCode === 13 && closeOnClickAccordion) {
                    $(this).trigger('click');
                }
                closeOnClickAccordion = true;
            });
        }

    },

    resizeInPageNavigation: function () {
        if ($('.bce-inpage-navigation .bce-tablist').length > 0) {
            var $inpagenavChildren = $('.bce-inpage-navigation .bce-tablist a');
            var totalWidth = 40;
            $inpagenavChildren.each(function (key, value) {
                totalWidth = totalWidth + value.offsetWidth;
            });
            $('.bce-inpage-navigation > div').removeAttr('style');
            if (totalWidth > $('.bce-inpage-navigation .bce-tablist')[0].offsetWidth) {
                $('.bce-inpage-navigation > div').css('width', totalWidth + 'px');
            }
        }
    },

    onResizeCheckConnectorArea: function () {
        var activeConnectorArea = $('.connector-area.active');
        if (window.matchMedia("(max-width: 991.98px)").matches) {
            if (activeConnectorArea.length > 0) {
                var menuFlyOut = activeConnectorArea.find('.menu-flyout');
                if (menuFlyOut.length > 0 && menuFlyOut.css('display') === 'none') {
                    if (!activeConnectorArea.hasClass('bce-no-flyout-open')) {
                        activeConnectorArea.addClass('bce-no-flyout-open');
                    }
                }
            }
        }
        else {
            activeConnectorArea.removeClass('bce-no-flyout-open');
        }
    },

    resizeCardSliderHeight: function (pSelector) {
        $slickSlider = $('.slidingCardsContainer');
        $slickSlider.find('.slick-slide').height('auto');

        var slickTrack = $slickSlider.find('.slick-track');
        var slickTrackHeight = $(slickTrack).height();

        $slickSlider.find('.slick-slide').css('height', slickTrackHeight + 'px');
    },

    resizeCardSliderWidthMobile: function (pSelector) {
        $slickSlider = $('.slidingCardsContainer');

        $slickSlider.find('.slick-slide').width(this.getWindowWidth() - 60);
    },

    resizeCardSliderWidthTablet: function (pSelector) {
        $slickSlider = $('.slidingCardsContainer');

        $slickSlider.find('.slick-slide').width($slickSlider.width() / 2 - this.getCardSliderGap() / 2 - 5);
    },

    resizeCardSliderWidthDesktop: function (pSelector) {
        $slickSlider = $('.slidingCardsContainer');

        $slickSlider.find('.slick-slide').width($slickSlider.width() / 3 - this.getCardSliderGap() / 2 - 3);
    },

    changeCardSliderSwipePos: function (event, slick, currentSlide, nextSlide) {
        $slickSlider = $('.slidingCardsContainer');
    },

    improvedCardSliderAccessibility: function (pSelector) {
        $slickSlider = $('.slidingCardsContainer');
        $slickSlider.removeAttr('role');
        var slickTrack = $slickSlider.find('.slick-track');
        if (slickTrack.length > 0) {
            var newId = 'slider-' + (new Date()).getTime();
            slickTrack.attr('id', newId);

            $slickSlider.find('.slick-prev, .slick-next').attr('aria-controls', newId);
            $slickSlider.find('.slick-list').attr('aria-live', 'polite');
        }
    }
};

//Focuses modal close button when shown
$('.modal').on('shown.bs.modal', function() {
    $(this).find('button[aria-label="close"]').focus();
});

var FloatingSubNav = {
    isMobile: false,
    activeSubNavIndex: 1,
    magicLine: '#magic-line',
    init: function (windowScroll) {

        this.addMagicLine('.subnavgroup');

        // Check if will desktop or not then if desktop apply the floating scroll function
        if (FloatingSubNav.checkIfNotMobile() === false) {
            FloatingSubNav.checkScrollPos(windowScroll);
            $('.subnav-scroll').removeClass('d-none');
            FloatingSubNav.checkScrollableActiveSection(windowScroll);
        } else {
            $('.subnav-scroll').removeClass('d-none');
            FloatingSubNav.positionToRelative('.subnav-scroll');
        }

        FloatingSubNav.bindEvents();

    },

    bindEvents: function () {
        var that = this;

        $('.subnavgroup li a').on('click', function () {
            var sectionToScroll = '#' + $(this).parent().attr('data-section');
            that.animateToSection(sectionToScroll);
        });
    },

    addMagicLine: function (element) {
        $(element).append("<li class='listStyleNone' id='magic-line'></li>");

    },

    checkScrollPos: function (scrollPos) {
        // Define variables
        var spaceBefore = 45; // space before scrollable area
        var spaceAfter = 50; // space after scrollable area
        var scrollableArea = '.scrollable-area';
        var elementToPositon = '.subnav-scroll';
        var endPosElement = 'footer';
        var totalSpacesBeforeScrollableArea = this.getScrollableAreaTopValue(scrollableArea, spaceBefore);

        if (scrollPos >= totalSpacesBeforeScrollableArea && scrollPos < this.getScrollableAreaEndValue(elementToPositon, endPosElement, spaceBefore, spaceAfter)) {
            this.positionToFix(elementToPositon, spaceBefore);
        } else if (scrollPos >= this.getScrollableAreaEndValue(elementToPositon, endPosElement, spaceBefore, spaceAfter)) {
            this.positionToAbsolute(elementToPositon, endPosElement);
        } else {
            this.positionToRelative(elementToPositon);
        }

    },

    checkScrollableActiveSection: function (scrollPos) {
        var that = this;
        var scrollableContents = '.scrollable-contents';
        var subNav = '.subnav-scroll';
        var scrollableContentsLength = $(scrollableContents).find('> div').length;
        var currentIndex = 1;
        scrollPos = Math.ceil(scrollPos);

        //if (scrollPos === $(window).height()) {
        if ($(window).scrollTop() + $(window).height() === $(document).height()){
            currentIndex = scrollableContentsLength;
        } else {
            for (var i = 1; i < scrollableContentsLength + 1; i++) {
                if (i === 1) {
                    if (scrollPos < FloatingSubNav.getSectionTopValue(scrollableContents, 1)) {
                        currentIndex = 1;
                        break;
                    }
                } else if (scrollPos >= FloatingSubNav.getSectionTopValue(scrollableContents, i - 1) && scrollPos < FloatingSubNav.getSectionTopValue(scrollableContents, i)) {
                    currentIndex = i;
                    break;
                } else if (scrollPos >= FloatingSubNav.getSectionTopValue(scrollableContents, scrollableContentsLength)) {
                    currentIndex = scrollableContentsLength;
                    break;
                }
            }
        }
        this.activeSubNavIndex = currentIndex;
        this.checkActiveSubNav(this.activeSubNavIndex, subNav);
    },

    getSectionTopValue: function (scrollableSection, ctr) {
        var scrollables = $(scrollableSection);
        if (scrollables.length > 0) {
            var scrollable = scrollables.find("#scrollable-content-" + ctr);
            if (scrollable.length > 0) {
                return Math.floor(scrollable.offset().top + scrollable.height());
            }
        }
    },

    checkActiveSubNav: function (activeIndex, subNav) {
        $(subNav).find('.subnavgroup li').removeClass('subnav_active');
        $(subNav).find('.subnavgroup li:nth-child(' + activeIndex + ')').addClass('subnav_active');

        this.animateMagicLine(activeIndex, subNav);
    },

    animateMagicLine: function (activeIndex, subNav) {
        var that = this;
        if ($(subNav).length > 0) {
            var target = $(subNav).find('.subnavgroup li:nth-child(' + activeIndex + ') a');
            var topPos = target.position().top;
            var newHeight = target.parent().height();
        }

        $(that.magicLine).stop().animate({
            top: topPos,
            height: newHeight
        });
    },

    animateToSection: function (sectionToScroll) {
        var topPos = $(sectionToScroll).offset().top;

        $('html, body').animate(
            {
                scrollTop: topPos
            },
            500,
            'linear'
        );
    },

    checkIfNotMobile: function () {

        var windowWidth = $(window).outerWidth();

        windowWidth > 767 ? isMobile = false : isMobile = true;

        return isMobile;
    },

    getScrollableAreaTopValue: function (scrollableArea, spaceBefore) {
        if ($(scrollableArea).length > 0) {
            return $(scrollableArea).position().top - spaceBefore;
        }

    },

    getScrollableAreaEndValue: function (subNavHeight, endPosElement, spaceBefore, spaceAfter) {

        return $(endPosElement).position().top - ($(subNavHeight).innerHeight() + spaceBefore + spaceAfter);
    },

    positionToFix: function (element, spaceBefore) {
        $(element).css({
            'position': 'fixed',
            'top': spaceBefore,
            'bottom': 'auto'
        });
    },

    positionToRelative: function (element) {
        $(element).css({
            'position': 'relative',
            'top': 'auto',
            'bottom': 'auto'
        });
    },

    positionToAbsolute: function (element, endPosElement) {
        $(element).css({
            'position': 'absolute',
            'top': 'auto',
            'bottom': 0
        });
    }
};

$(window).scroll(function () {
    checkScrollLocation();
    //hiding social floats on footer
});

$(document).ready(function () {
    var windowScroll = document.documentElement.scrollTop;

    BCE.init();
    if ($('.scrollable-area').length > 0) {
        FloatingSubNav.init(windowScroll);
        checkScrollLocation();
    }

    FocusUntrapper();
    StopYoutubeVideo();
});

//reset the scrollable container location
function checkScrollLocation(){
    if ($('.scrollable-area').length > 0) {
        var windowScroll = window.pageYOffset || document.documentElement.scrollTop;

        // Check if will desktop or not then if desktop apply the floating scroll function
        if (FloatingSubNav.checkIfNotMobile() === false) {
            FloatingSubNav.checkScrollPos(windowScroll);
            FloatingSubNav.checkScrollableActiveSection(windowScroll);
        } else {
            FloatingSubNav.positionToRelative('.subnav-scroll');
        }
    }
}
//function to trigger no focus
function FocusUntrapper() {
    if ($(".printAndBlur").length > 0) {
        $(".printAndBlur").keypress(function(event) {
            if (event.keyCode === 13) {
                window.print(); this.blur();
            }
        });
        $(".printAndBlur").click(function() {
            window.print(); this.blur();
        });
    }

    if ($(".noFocusBlur").length > 0) {
        $(".noFocusBlur").keypress(function (event) {
            if (event.keyCode === 13) {
               this.blur();
            }
        });
        $(".noFocusBlur").click(function () {
            this.blur();
            $(this).tooltip('hide');
            //alert('hidding');
        });
    }
}

//fix for tab focus going outside the nav when opened
$(document).on('click', '.connector-nav-open-button, .screen', function () {
    $("main").find('*').not('[tabindex="-1"]').attr("tabindex", "-1").addClass('tab_neg_one');
    $("footer").find('*').not('[tabindex="-1"]').attr("tabindex", "-1").addClass('tab_neg_one');
    $("body").addClass('overflow-hidden');
}); 
$(document).on('click', '.connector-active .connector-nav-open-button, .screen', function () { 
    $('body .tab_neg_one').removeAttr('tabindex').removeClass('tab_neg_one');
    $("body").removeClass('overflow-hidden');
}); 
//fix for tab focus going outside the nav when opened

//function to trigger stop youtube video on Close button
function StopYoutubeVideo() {
    // stop video when click close button
    $('.modal-dialog-video .modal-dialog-video-content .modal-dialog-video-close').on('click', function () {
        if($('iframe.youtube-video').length > 0) { 
            //reset the source 
            var iframeVideoUrl = $('iframe.youtube-video').attr("src");
            $('iframe.youtube-video').attr("src", iframeVideoUrl);

            $('iframe.youtube-video')[0].contentWindow.postMessage('{"event":"command","func":"' + 'stopVideo' + '","args":""}', '*');
        }

    });
    // stop video when click outside of the modal
    $('div.modal.fade').on('click', function () {
        if($('iframe.youtube-video').length > 0) {
            //reset the source
            var iframeVideoUrl = $('iframe.youtube-video').attr("src");
            $('iframe.youtube-video').attr("src", iframeVideoUrl);

            $('iframe.youtube-video')[0].contentWindow.postMessage('{"event":"command","func":"' + 'stopVideo' + '","args":""}', '*');
        }
    });
 }

var to = 0;
$(window).resize(function () {
    var windowScroll = document.documentElement.scrollTop;

    clearTimeout(to);
    to = setTimeout(function () {
        BCE.resized();

    }, 300);

    if ($('.scrollable-area').length > 0) {
        // Check if will desktop or not then if desktop apply the floating scroll function
        if (FloatingSubNav.checkIfNotMobile() === false) {
            FloatingSubNav.checkScrollPos(windowScroll);
            FloatingSubNav.checkScrollableActiveSection(windowScroll);
        } else {
            FloatingSubNav.positionToRelative('.subnav-scroll');
        }
    }

});

//radio button box
$(document).on('change', '.ctrl_radioBtn input[type="radio"]', function () {
    var $this = $(this);
    var $parentDiv = $this.closest('[id^=radio]');
    if ($this.hasClass('selected-rectangle')) {
        $parentDiv.find('.rectangle').addClass('selected-rectangle');
        $this.closest('label').removeClass('selected-rectangle');

    }
    else {
        $parentDiv.find('.rectangle').removeClass('selected-rectangle');
        $this.closest('label').addClass('selected-rectangle');

    }

});

//START - Events and Presentation Custom Card Event Listener for click and hover
//Global Variables
var cardEl = '.card-clickable[trigger-mainlink-onclick="true"]',
    cardMainLinkEl = '.card-clickable-mainlink',
    hoverAbleEl = '.card-clickable a:not(.card-clickable-mainlink),.card-clickable button',
    clickableEl = 'a, input, textarea, button, select, [tabindex]:not([tabindex="-1"]), iframe';
//Click Events
$(document).on('click', cardEl, function (e) {
    var el = $(this).find(cardMainLinkEl), target = $(e.target);
    e.stopPropagation();
    if (target.is(clickableEl) || el.length < 1) {
        return;
    }
    el.focus().get(0).click();
});

//Hover on Card
$(document).on('mouseenter', cardEl, function (e) {
    var el = $(this).find(cardMainLinkEl);
    e.stopPropagation();
    el.removeClass('hoverActive');
    el.addClass('hoverActive');
}).on('mouseleave', cardEl, function (e) {
    var el = $(this).find(cardMainLinkEl);
    e.stopPropagation();
    el.removeClass('hoverActive');
});
//Hover on Other Links
$(document).on('mouseenter', hoverAbleEl, function (e) {
    var el = $(this).parents(cardEl).find(cardMainLinkEl);
    e.stopPropagation();
    el.removeClass('hoverActive');
}).on('mouseleave', hoverAbleEl, function (e) {
    var el = $(this).parents(cardEl).find(cardMainLinkEl);
    e.stopPropagation();
    el.removeClass('hoverActive');
    el.hover();
});
//END - Events and Presentation Custom Card Event Listener for click and hover

/* ALLOW DECIMAL */
$(".allownumericwithdecimal").on("keypress keyup blur",function (event) {
    $(this).val($(this).val().replace(/[^0-9\.]/g,''));
           if ((event.which !== 46 || $(this).val().indexOf('.') !== -1) && (event.which < 48 || event.which > 57)) {
               event.preventDefault();
           }
       });

$(".roundoftwodecimal").on("blur",function (event) {
               var amt = parseFloat(this.value);
               $(this).val(amt.toFixed(2));

       });

       $(".allownumericwithdecimalcomma").keydown(function (e) {
        if (e.shiftKey) e.preventDefault();
        else {
            var nKeyCode = e.keyCode;
            //Ignore Backspace and Tab keys
            if (nKeyCode === 8 || nKeyCode === 9) return;
            if (nKeyCode === 188) return;
            if (nKeyCode === 190) return;
            if (nKeyCode < 95) {
                if (nKeyCode < 48 || nKeyCode > 57) e.preventDefault();
            } else {
                if (nKeyCode < 96 || nKeyCode > 105) e.preventDefault();
            }
        }
    });

/* POPUP PASS IMAGE */
$(".openImageDialog").on("click",function (event) {
    var myImagedata = $(this).data('id');
    $(".modal-dialog-image-body .img-modal-dest").attr("src", myImagedata);
});
/* POPUP PASS Video and data-label*/
$(".openVideoDialog").on("click", function (event) {
    var myVideoData = $(this).data('id'),
        label = $(this).data('label');
    var video_tag = "video#tag_video_clip";
    //get video from the data
    var $video = $(video_tag),
        videoSrc = $('source', $video).attr('src', myVideoData);
    if (label !== undefined) {
        $video.attr('aria-label', label);
    } else {
        $video.removeAttr('aria-label');
    }

    $video[0].load();
    //auto play
    $video[0].play();
});

//adding observer for body to stop playing the video if modal is closed
var $body_el = $('body');
var observer = new MutationObserver(function (mutations) {
    mutations.forEach(function (mutation) {
        if (mutation.attributeName === "class") {
            var attributeClassValue = $(mutation.target).prop(mutation.attributeName);
            //console.log("Class attribute changed to:", attributeValue);
            var modalOpened = attributeClassValue.includes("modal-open");
            //check if modal is closed now
            if (!modalOpened) {
                //console.log("n " + modalOpened);
                //check if video clip exist in page
                var video = $("#tag_video_clip").get(0);
                //console.log("existed video ");
                //console.log( video);
                if (video) {
                    //console.log("existed video.paused " + video.paused);
                    if (!video.paused) {
                        video.pause();
                    }
                    //console.log("existed video.paused " + video.paused);
                }
            }
        }
    });
});
observer.observe($body_el[0], {
    attributes: true
});

// Start HomePage Slider
var ele = document.getElementById("slider-rotating-carousel-component");

if (ele) {

    (function (bell, $) {

        $.widget("rsx.BannerSliderCarousel", {
            version: "0.5",
            widgetEventPrefix: "BannerSliderCarousel",
            links: [],
            options: {
                slidesContainer: '',
                pauseOnFocus: true,
                pauseOnHover: true,
                infinite: true,
                autoplay: true,
                autoplaySpeedMobile: 10000,
                autoplaySpeed: 6000, //default speed for all carousel slides
                dots: true,
                arrows: false,
                customPaging: function (slider, i) {
                    return '<button class="slider-rotating-carousel-button" type="button">' + (i + 1) + '</button>';
                },
                dotsClass: 'slider-rotating-carousel-buttons',
                initialSlide: 0,
                omnitureShow_s_oAPT: '347-0-0',
                omnitureClick_s_oAPT: '348-0-0',
                track_omniture: false,
                adobeTargetCssClass: "at-element-marker"
            },

            _create: function () {
                var self, that;
                self = that = this;
                var canonical = $('link[rel=\'canonical\']').attr("href").split('/').pop(); // added element
                var alternate = $('link[rel=\'alternate\']').attr("href").split('/').pop(); // added element

                if (window.location.pathname === '/' || window.location.pathname === '/Accueil') { self.options.track_omniture = true; }
                //in order to save the tab state on browser back button, it saves tab index in this hidden input

                var progressInterval,
                    percentComplete,
                    progressStep = 50,
                    progressIndicatorLength,
                    progressIndicatorUnit,
                    progressIndicatorTotal,
                    progressIndicatorTotalRounded,
                    overrideMouseOverOut = false;

                this.autoplaySpeed = that.options.autoplaySpeed;

                if (navigator.userAgent.match(/Android/i)
                    || navigator.userAgent.match(/webOS/i)
                    || navigator.userAgent.match(/iPhone/i)
                    || navigator.userAgent.match(/iPad/i)
                    || navigator.userAgent.match(/iPod/i)
                    || navigator.userAgent.match(/BlackBerry/i)
                    || navigator.userAgent.match(/Windows Phone/i)) {
                    this.autoplaySpeed = that.options.autoplaySpeedMobile;
                }

                var playButtonLabel = "Pause rotation of banners";
                var pauseButtonLabel = "Resume rotation of banners";

                if (!alternate && !canonical)
                    canonical = "Accueil";
                this.$window = $(window);
                this.$slidesContainer = this.element.find(self.options.slidesContainer);
                this.$slides = this.$slidesContainer.children();
                this.$pauseButton = this.element.find(".slider-rotating-carousel-pause");
                this.$accessibilityLabel = this.$pauseButton.find(".sr-only");
                this.$progressIndicator = this.$pauseButton.find(".slider-rotating-carousel-progress > circle");

                progressIndicatorLength = typeof SVGElement.prototype.getTotalLength !== "undefined" ? Math.round(this.$progressIndicator.get(0).getTotalLength()) : 125;
                progressIndicatorUnit = progressIndicatorLength / 100;

                this.$progressIndicator.css({ "stroke-dasharray": progressIndicatorLength });

                resumeRotation();

                this.$accessibilityLabel.text(playButtonLabel);

                function pauseRotation() {
                    if (Math.abs(progressIndicatorTotalRounded) < 1) {
                        self.$progressIndicator.addClass("slider-rotating-carousel-progress_initial");
                    }
                    self.$pauseButton.attr("data-pressed", true);
                    self.$accessibilityLabel.text(pauseButtonLabel);

                }

                function resumeRotation() {
                    self.$progressIndicator.removeClass("slider-rotating-carousel-progress_initial");
                    self.$pauseButton.attr("data-pressed", false);
                    self.$accessibilityLabel.text(playButtonLabel);
                }

                this.$pauseButton.on("click tap", function () {

                    var isPaused = self.$pauseButton.attr("data-pressed") === "true";

                    if (typeof s_oTrackPage === "function") { // && isHomePage === "True"
                        s_oTrackPage({ s_oAPT: "647-0-0", s_oBTN: self.$accessibilityLabel.text() });
                    }

                    if (isPaused) {
                        resumeRotation();
                    } else {
                        pauseRotation();
                    }
                    overrideMouseOverOut = true;
                });

                this.$slidesContainer.parent().on("mouseenter", function () {

                    if (!!('ontouchstart' in window) === false) {

                        if (!overrideMouseOverOut) {
                            pauseRotation();
                        }
                    }
                }).on("mouseleave", function () {

                    if (!overrideMouseOverOut) {
                        resumeRotation();
                    }
                }).on("click tap swipe", function (e) {

                    if (e.target === self.$pauseButton[0]) {
                        return;
                    }
                    overrideMouseOverOut = true;
                    pauseRotation();
                });

                $(document).on("visibilitychange", function () {
                    if (document.visibilityState === "hidden") {
                        pauseRotation();
                    } else {
                        resumeRotation();
                    }
                });

                var startAutoplay = function () {

                    percentComplete = 0;
                    progressIndicatorTotal = 0;
                    updateProgressIndicator();
                    progressInterval = setInterval(progressIntervalHandler, progressStep);
                };

                var updateProgressIndicator = function () {
                    percentComplete += progressStep / that.autoplaySpeed * 100;
                    progressIndicatorTotal = percentComplete * progressIndicatorUnit * -1 + 1;
                    progressIndicatorTotalRounded = Math.round(progressIndicatorTotal * 10) / 10;
                    self.$progressIndicator.css({ "stroke-dashoffset": progressIndicatorTotalRounded });
                };

                var progressIntervalHandler = function () {

                    if (self.$pauseButton.attr("data-pressed") === "false") {

                        updateProgressIndicator();

                        if (percentComplete >= 100) {
                            self.$slidesContainer.slick('slickNext');
                            resetAutoplayProgress();
                        }
                    }
                };

                var resetAutoplayProgress = function () {
                    clearInterval(progressInterval);
                    startAutoplay();
                };

                $("#slider-rotating-carousel-component :focusable").focusin(function () {

                    if (!!('ontouchstart' in window) === false) {
                        if (!overrideMouseOverOut) {
                            pauseRotation();
                        }
                    }
                }).focusout(function () {
                    if (!overrideMouseOverOut) {
                        resumeRotation();
                    }
                });

                this.$slider = this.$slidesContainer
                    .on("init", function (event, self) {
                        self.$slider.removeClass("slider-rotating-carousel-height");
                        self.options.initialSlide = self.currentSlide;
                        if (that.options.autoplay) {
                            startAutoplay();
                        }

                        $("#slider-rotating-carousel-component .slider-rotating-carousel-button")
                            .focusin(function () {
                                if (!overrideMouseOverOut) {
                                    pauseRotation();

                                }
                            }).focusout(function () {
                                if (!overrideMouseOverOut) {
                                    resumeRotation();
                                }
                            }).on("keyup", function (e) {

                                if (e.type === "keyup" && (e.which === 37 || e.which === 39)) {
                                    overrideMouseOverOut = true;
                                    $("#slider-rotating-carousel-component .slick-active .slider-rotating-carousel-button").focus();
                                    progressIndicatorTotalRounded = 0;
                                    pauseRotation();
                                }
                            });

                        $("#slider-rotating-carousel-component .slider-rotating-carousel-button")
                            .on("click tap", function () {
                                if (typeof s_oTrackPage === "function") { // && isHomePage === "True"
                                    s_oTrackPage({ s_oAPT: "647-0-0", s_oBTN: $(this).attr("aria-label") });
                                }
                            });
                        that._syncAdobeTarget(self);
                    }).slick({
                        pauseOnFocus: self.options.pauseOnFocus,
                        pauseOnHover: self.options.pauseOnHover,
                        slidesToShow: 1,
                        slidesToScroll: 1,
                        infinite: self.options.infinite,
                        adaptiveHeight: self.options.adaptiveHeight,
                        arrows: false,
                        autoplay: false, //relies on custom implementation
                        waitForAnimate: false,
                        dots: self.options.dots,
                        customPaging: self.options.customPaging,
                        dotsClass: self.options.dotsClass
                    }).on("afterChange", function (event, slick, currentSlide, nextSlide) {
                        that._track(currentSlide);
                    }).on("beforeChange", function (event, slick, currentSlide, nextSlide) {
                        resetAutoplayProgress();
                    });
                that._syncAdobeTarget(self);
                var CarouselImpressions = setInterval(function () {
                    if (typeof s_oTrackPage === "function" || typeof s_track === "function") {
                        that._track(self.options.initialSlide);
                        clearInterval(CarouselImpressions);
                    }
                }, 100);
            },

            _trackOmniture: function(code, id) {
                if (typeof s_oTrackPage === "function") {
                    s_oTrackPage({ s_oAPT: code, s_oBID: id });
                } else if (typeof s_track === "function") {
                    s_track({ s_oAPT: code, s_oBID: id });
                }
            },

            _track: function (currentSlide) {
                if (this.options.track_omniture && this.links[currentSlide] === undefined) {
                    var banner = $(this.$slides[currentSlide]).find(".js-omni-banner");
                    var omnitureVal = $(banner).data("omni-s_obid");
                    this._trackOmniture(this.options.omnitureShow_s_oAPT, omnitureVal);
                    this.links[currentSlide] = true;
                }
            },
            /**
             * synchronize banner with Adobe to prevent flickering issue or banner copy not complete
             *
             * @param {any} self the item to pass
             */
            _syncAdobeTarget: function (self) {
                if (this.options.infinite) {
                    let leftClonedSlide = self.$slider.find(".slick-slide.slick-cloned").first();
                    let rightClonedSlide = self.$slider.find(".slick-slide.slick-cloned").last();
                    let allSlides = self.$slider.find(".slick-slide");
                    var firstSlide, lastSlide = "";
                    /*mapping first slide and last slide with their cloned slides accordingly*/
                    $.each(allSlides, function (index, $slide) {
                        if ($(this).data("slickIndex") === leftClonedSlide.data("slickIndex") + 1) {
                            firstSlide = $(this);
                        }
                        if ($(this).data("slickIndex") === rightClonedSlide.data("slickIndex") - 1) {
                            lastSlide = $(this);
                        }
                    });
                    /*find adobe target slider and replace by order*/
                    if (lastSlide && lastSlide.find("div").hasClass(this.options.adobeTargetCssClass)) {
                        leftClonedSlide.html(lastSlide.html());
                    }
                    if (firstSlide && firstSlide.find("div").hasClass(this.options.adobeTargetCssClass)) {
                        rightClonedSlide.html(firstSlide.html());
                    }
                    if (leftClonedSlide && leftClonedSlide.find("div").hasClass(this.options.adobeTargetCssClass)) {
                        lastSlide.html(leftClonedSlide.html());
                    }
                    if (rightClonedSlide && rightClonedSlide.find("div").hasClass(this.options.adobeTargetCssClass)) {
                        firstSlide.html(rightClonedSlide.html());
                    }
                }
            }

        });
    })({}, jQuery);

    $('#slider-rotating-carousel-component').BannerSliderCarousel({
        'slidesContainer': ".slider-rotating-carousel",
        'autoplay-speed': "6000",
        'autoplay-speed-mobile': "6000",
        'class': "init"
    });

}

// End HomePage Slider
/* Sychronize Height for Table */
function SynchronizingTableHeight() {
    $('.special-table-heading-container').each(function () {
        var el = $(this),
            separateHeadersTable = el.find('.special-table-heading-side'),
            actualTable = el.find('.special-table-heading'),
            rowHeaders,
            actualRows,
            totalHeight = 0,
            groupCounter = 0;

        if (separateHeadersTable.length > 0) {
            separateHeadersTable.find('.table-heading-side-row').outerHeight(actualTable.find('.table-head').outerHeight());

            rowHeaders = separateHeadersTable.find('.table-body').toArray().reverse();
            actualRows = actualTable.find('.table-body').toArray().reverse();
            actualRows.forEach(function (pRow) {
                var row = $(pRow);

                if (row.hasClass('one-header-for-specific-rows')) {
                    totalHeight += row.outerHeight();
                    $(rowHeaders[groupCounter]).outerHeight(totalHeight);
                    totalHeight = 0;
                    groupCounter++;
                } else {
                    totalHeight += row.outerHeight();
                }
            });
        }
    });
}

$(document).ready( function () {
    SynchronizingTableHeight();
});

$(document).on('load',function () {
    SynchronizingTableHeight();
});

$(window).on('resize', function () {
    SynchronizingTableHeight();
});

/* Sychronize Height for Table */

/* for radio form */
$(document).ready(function () {
    // START datepicker custom script (implement date range)
    var datepickerCheckerstart = document.getElementById("datepicker-start-date");
    var datepickerCheckerend = document.getElementById("datepicker-end-date");

    if (datepickerCheckerstart && datepickerCheckerend) {
        var fromEl = $("#datepicker-start-date"),
            toEl = $("#datepicker-end-date"),
            dateFormat = fromEl.datepicker('option', 'dateFormat');

        fromEl.on("change", function () {
            toEl.datepicker("option", "minDate", getDate(this.value, dateFormat));
            // it's important to trigger the optionchange event whenever we change the datepicker option after initialization. this event is being handled in datepicker-Bell.js
            toEl.trigger('optionchange');
        });
        // END datepicker custom script
    }

    $(".graphical_ctrl input").focus(function(){
        $(this).parent().addClass("selected-rectangle");

    }).blur(function(){
        if ($(this).not(':checked').length) {
            $(this).parent().removeClass("selected-rectangle");
        }
    });

    //hoem page sliding cards height fix
    var $slidingCards = $('.slidingCardsSection');
    if ($slidingCards.length) {
         //check if slick is initialized
        if ($slidingCards.find('.slidingCardsContainer.slick-initialized').length !== 0) { 
            $slidingCards.addClass("loaded");
        }
    }

});

/*terminate video control*/
$(document).on('load',function () {
    $(".modal-dialog-video-close").click(function () {
        //$(".video-element").pause();
        //$(".video-element").find(".video-source").attr("src", null);
        $(".video-element")[0].src += "&autoplay=0";
    });

});