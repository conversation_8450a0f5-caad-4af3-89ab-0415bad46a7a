﻿
html {
    position: relative;
    min-height: 100%;
}

/*Container*/
.width486 {
    max-width: 486px;
    width: 100%;
    margin: 0 auto;
}

.width630 {
    max-width: 630px;
    width: 100%;
    margin: 0 auto;
}

.width400 {
    max-width: 400px;
    width: 100%;
    margin: 0 auto;
}

.width300 {
    max-width: 300px;
    width: 100%;
}

.bdrRtTop,
.border-top-1 {
    border-top: 1px solid #d4d4d4
}

.container.static {
    position: static;
}

footer>.container.liquid-container {
    padding-right: 0px !important;
    padding-left: 0px !important;
    margin-right: 16px;
    margin-left: 16px;
    width: auto;
}

.w-100 {
    width: 100%;
}

.btn {
    padding-bottom: 8px;
    padding-top: 8px;
}

.margin-b-2 {
    margin-bottom: 2px;
}

.simple-header.device-details-header {
    height: 75px;
}

.contained-header {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.icon-back {
    position: relative;
    left: -5px;
}

.icon-back .icon:before {
    left: -4px !important;
    top: -2px !important;
}

.icon-back span:last-child {
    margin-left: -2px;
}

.border-lightGray {
    border: 1px solid #e1e1e1;
}

.img-size110 {
    width: 100px;
    height: auto;
}

.position-top-1:before {
    top: 1px !important;
}

.txtGrayDark {
    color: #333333;
}

.txtSize23 {
    font-size: 23px;
}

.txtSize28 {
    font-size: 28px;
}

.lineHeight-14 {
    line-height: 14px;
}

.lineHeight-18 {
    line-height: 18px;
}

.lineHeight-22 {
    line-height: 22px;
}

.lineHeight-26 {
    line-height: 26px;
}

.lineHeight-30 {
    line-height: 30px;
}

.txtBlack4 {
    color: #222222;
}

.txtBlueExtraLight2 {
    color: #2390B9;
}

.txtGreen2 {
    color: #2C9E25;
}

.txtYellow2 {
    color: #E99E00;
}

.borderGrayLight3 {
    border: 1px solid #d7d7d7;
}

.care-plan-detail-card {
    width: 100%;
}

.height515 {
    height: 515px;
}

/* .price sup {
    top: -0.75em !important;
    font-size: 20px !important;
    letter-spacing: 0;
} */

/* SPC - Start */
.spc-option {
    position: relative;
    width: 300px;
}

.spc-option .shadow-none {
    box-shadow: none;
}

.spc-option .ctrl_element.check-box {
    left: 5px;
    top: -7px;
}

.spc-option .non-card-input .ctrl_element.radio-button {
    top: -16px;
    left: 5px;
}

.spc-option .non-card-label>div {
    margin-left: 8px;
}

.spc-option .ctrl_element.radio-button {
    top: -15px;
    left: 0px;
}

.spc-option .graphical_ctrl input {
    height: 42px;
    width: 42px;
}

.spc-option .ctrl_element.shadow-none {
    height: 24px;
    width: 24px;
}

.spc-option .graphical_ctrl_checkbox .ctrl_element.shadow-none:after {
    height: 14px;
    width: 8px;
    left: 7px;
    top: 2px;
}

.spc-option .step-links button {
    padding-bottom: 8px;
    padding-top: 8px;
}

.spc-option .price {
    line-height: 46px;
    font-size: 40px;
}

.spc-option .price sup {
    letter-spacing: -0.5px;
    vertical-align: super;
    line-height: 22px;
    font-size: 18px;
    top: 1px;
}

.spc-option .price sup:first-child {
    right: 2px;
}

.spc-option .price sup:last-child {
    left: 4px;
}

.spc-option .highlight {
    border: 3px solid #00549a;
    pointer-events: none;
    border-radius: 10px;
    position: absolute;
    height: 100%;
    width: 100%;
    opacity: 0;
    z-index: 1;
    left: 0px;
    top: 0px;
}

.spc-option.active-spc-option .highlight {
    opacity: 1;
}

.spc-carousel.prod-carousel>.container {
    width: 975px;
}

.spc-carousel.prod-carousel>.container:before,
.spc-carousel.prod-carousel>.container:after {
    height: calc(100% + 50px);
    width: calc(50% - 480px);
    margin-top: -25px;
}

.spc-carousel.prod-carousel .slick-dots {
    padding-bottom: 30px;
    padding-top: 15px;
}

.prod-carousel-content .slick-prev:before, .prod-carousel-content .slick-next:before  {
    top: 5px;
}

.hide{
    display: none;
}

/* SPC - End */

.displayIn-xs,
.displayIn-sm {
    display: none;
}

.section-box {
    border: 1px solid #e1e1e1;
    border-radius: 10px;
    overflow: hidden;
}


/* Modal / Compare Modal - Start */

.modal-open .modal {
    overflow-y: auto;
}

.modal-content {
    /* margin-bottom: 60px; */
}

.modal-reader {
    padding: 30px;
}

.old-solution,
.new-solution {
    border-bottom: 1px solid #d4d4d4;
    border-top: 1px solid #d4d4d4;
    overflow: hidden;
}

.old-solution {
    border-left: 1px solid #d4d4d4;
    border-bottom-left-radius: 10px;
    border-top-left-radius: 10px;
}

.new-solution {
    border-right: 1px solid #d4d4d4;
    border-bottom-right-radius: 10px;
    border-top-right-radius: 10px;
}

.old-solution .compare-modal-heading,
.old-solution .compare-modal-total-monthly {
    /* background-color: #f4f4f4; */
    padding: 12px 0px;
}

.new-solution .compare-modal-heading,
.new-solution .compare-modal-total-monthly {
    /* background-color: #00549a; */
    padding: 12px 0px;
}

.old-solution .compare-modal-body span,
.new-solution .compare-modal-body span {
    line-height: 14px;
}

.old-solution .compare-modal-body span.promo,
.new-solution .compare-modal-body span.promo {
    line-height: 18px;
}

.compare-modal .old-solution .promoWrap .promo {
    background-color: #555555;
    color: #ffffff;
}

.compare-modal .new-solution .promoWrap .promo {
    background: #00549A;
    color: #ffffff;
}

.old-solution .compare-modal-total-monthly,
.new-solution .compare-modal-total-monthly {
    padding: 15px;
}

.compare-modal .compare-table-wrap .compare-modal-device .compare-modal-row-block>div:nth-child(odd),
.compare-modal .compare-table-wrap .compare-modal-rateplan .compare-modal-row-block>div:nth-child(odd),
.compare-modal .compare-table-wrap .compare-modal-addons .compare-modal-row-block>div:nth-child(odd),
.compare-modal .compare-table-wrap .compare-modal-devicecareplan .compare-modal-row-block>div:nth-child(odd) {
    flex: 0 0 75%;
}

.compare-modal .compare-table-wrap .compare-modal-device .compare-modal-row-block>div:nth-child(even),
.compare-modal .compare-table-wrap .compare-modal-rateplan .compare-modal-row-block>div:nth-child(even),
.compare-modal .compare-table-wrap .compare-modal-addons .compare-modal-row-block>div:nth-child(even),
.compare-modal .compare-table-wrap .compare-modal-devicecareplan .compare-modal-row-block>div:nth-child(even) {
    flex: 0 0 25%;
}

.compare-modal-total-monthly .txtRight span {
    letter-spacing: normal !important;
    position: relative;
    left: -2px;
}

.compare-modal-total-monthly .txtRight span>sup:first-child {
    left: -1px;
}

.compare-modal-total-monthly .txtRight span>sup:last-child {
    left: 2px;
}

.modal-header .custom-margin-top {
    margin-top: -24px;
}

.custom-hug-header {
    white-space: break-spaces;
    line-height: normal;
}

/* Modal / Compare Modal - End */
/*For Tablet and Mobile - Start*/
@media screen and (max-width:991.98px) {
    footer > .container.liquid-container {
        margin-right: 30px;
        margin-left: 30px;
    }

    .spc-option {
        width: 350px;
    }

    .spc-carousel.prod-carousel > .container {
        width: 769px;
    }

    .d-sm-flex {
        display: flex;
    }

    .d-sm-flex-justify-space-between {
        justify-content:space-between;
    }
    #logout.btn-primary {
        padding: 10px 15px !important;
    }
    .sm-width350 {
        max-width: 350px !important;
        width: 100%;
        margin: 0 auto;
    }

    .txtSize18-sm {
        font-size: 18px;
    }
    
    .lineHeight-18-sm {
        line-height: 18px;
    }
    
    .lineHeight-22-sm {
        line-height: 22px;
    }

    .simple-header.device-details-header {
        height: 55px;
    }

    .block-sm {
        display: block;
    }

    .hidden-sm{
        display: none;
    }
    
    .displayIn-xs {
        display: block;
    }
 }
/*For Tablet and Mobile - End*/

/*For mobile only - Start*/
@media (max-width: 767px) {  
    .txtSize14-xs {
        font-size: 14px;
    }

    .txtSize22-xs {
        font-size: 22px;
    }

    .lineHeight-18-xs {
        line-height: 18px;
    }
    
    .lineHeight-24-xs {
        line-height: 24px;
    }

    
    .block-xs {
        display: block !important;
    }

    .hidden-xs {
        display: none !important;
    }

    
    .spc-option {
        width: auto;
    }

    .spc-option.spc-opt-out {
        margin: 0px 15px;
        width: 100%;
    }

    .position-fixed-xs {
        position: fixed !important;
        left: 0;
        bottom: 0;
        width: 100%;
        text-align: center;
    }

    .modal-dialog .fullHeight-xs {
        height: 100%;
    }

    footer > .container.liquid-container {
        padding-right: 15px !important;
        padding-left: 15px !important;
        margin-right: 0px;
        margin-left: 0px;
    }



    .modal-text {
        margin-bottom: 140px;
        margin-top: 75px;
        position: absolute;
        bottom: 0px;
        top: 0px;
    }

    /* Removing following class as per requirement in HUG */
    /* .modal .modal-dialog {
        height: auto;
        max-height: calc(100% - 45px);
        position: absolute;
        left: 0 !important;
        top: 0 !important;
        width: 100%;
    } */
    /* Removing following class as per requirement in HUG */

    .modal-text .text-content {
        overflow-y: scroll;
        height: 100%;
    }

    .modal-text .pad-xs-0 {
        padding: 0px !important;
    }

    .modal-dialog .heightAuto-xs {
        height: auto;
    }

    .modal-content {
        margin-bottom: 0px;
        height: auto;
    }

    .modal-reader {
        padding: 30px 15px;
    }

    .old-solution, .new-solution {
        border: 1px solid #d4d4d4;
        border-radius: 10px;
    }

    .compare-modal .compare-table-wrap .new-solution {
        margin-top: 15px;
    }

    .pad-xs-0{
        padding: 0 !important;
    }

    .justify-center-xs {
        justify-content: center;
    }

    .lineHeight-14-xs {
        line-height: 14px;
    }
    
    .lineHeight-22-xs {
        line-height: 22px;
    }

    .simple-header.device-details-header {
        height: 55px;
    }

    .float-none-xs {
        float: none;
    }

    .displayIn-xs {
        display: block;
    }

    .txtleft-xs {
        text-align: left !important;
    }

}
/*For mobile only - End*/


@font-face {
    font-family: "bell-icon";
    src: url("../fonts/bell-icon.eot?#iefix") format("embedded-opentype"), url("../fonts/bell-icon.woff") format("woff"), url("../fonts/bell-icon.ttf") format("truetype"), url("../fonts/bell-icon.svg") format("svg");
    font-weight: normal;
    font-style: normal
}

@font-face {
    font-family: "bell-icon-outline";
    src: url("../fonts/bell-icon-outline.eot?iw8dli");
    src: url("../fonts/bell-icon-outline.eot?#iefixiw8dli") format("embedded-opentype"), url("../fonts/bell-icon-outline.ttf?iw8dli") format("truetype"), url("../fonts/bell-icon-outline.woff?iw8dli") format("woff"), url("../fonts/bell-icon-outline.svg?iw8dli#bell-icon-outline") format("svg");
    font-weight: normal;
    font-style: normal
}

@font-face {
    font-family: "bell-icon2";
    src: url("../fonts/bell-icon2.eot?#iefix") format("embedded-opentype"), url("../fonts/bell-icon2.woff") format("woff"), url("../fonts/bell-icon2.ttf") format("truetype"), url("../fonts/bell-icon2.svg") format("svg");
    font-weight: normal;
    font-style: normal
}

@font-face {
    font-family: "bell-icon3";
    src: url("../fonts/bell-icon3.eot?#iefix") format("embedded-opentype"), url("../fonts/bell-icon3.woff") format("woff"), url("../fonts/bell-icon3.ttf") format("truetype"), url("../fonts/bell-icon3.svg") format("svg");
    font-weight: normal;
    font-style: normal
}

.icon {
    font-style: normal;
    speak: none;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-check-circled:before,
.icon-chevron-left:before,
.icon-i-solid:before {
    font-family: "bell-icon";
    position: relative;
    top: .1em;
}

.icon-chevron-left:before {
    content: "\e012";
    display: inline-block;
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
    top: 2px;
}


.icon-i-solid:before {
    content: "\e60d";
}

/*IE OVERRIDES*/

@media only screen and (max-width : 767px) and (-ms-high-contrast: none),
(-ms-high-contrast: active) {
    .block-xs-ie {
        display: inline;
    }
}

/*IE OVERRIDES*/

