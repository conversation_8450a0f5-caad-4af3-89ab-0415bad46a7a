﻿/* START Helper Class */

.height-10 {height: 10px;}
.height-125 {height: 125px;}
.height-130 {height: 130px;}
.height-135 {height: 135px;}
.height-150 {height: 150px}
.height-155 {height: 155px}
.height-165 {height: 165px}
.height-170 {height: 170px}
.height-175 {height: 175px;}
.height-180 {height: 180px;}
.height-185 {height: 185px;}
.height-200 {height: 200px}
.height-205 {height: 205px;}
.height-215 {height: 215px;}
.height-220 {height: 220px}
.height-225 {height: 225px}
.height-230 {height: 230px}
.height-250 {height: 250px}
.height-365 {height: 365px}
.height-400 {height: 400px}
.height-430 {height: 430px}
.height-440 {height: 440px}
.height-450 {height: 450px}
.height-500 {height: 500px}
.height-525 {height: 520px;} 
.height-680 {height: 680px;}
.max-height-100 {max-height: 100px;}
.max-height-125 {max-height: 125px;}
.max-height-130 {max-height: 130px;}
.max-height-150 {max-height: 150px;}
.max-height-175 {max-height: 175px;}
.max-height-190 {max-height: 190px;}
.max-height-205 {max-height: 205px;}
.max-height-210 {max-height: 210px;}
.max-height-270 {max-height: 270px;}
.max-height-405 {max-height: 405px;}

.max-height-100p {max-height: 100%;}

.min-height-14 {min-height: 14px;}
.min-height-175 {min-height: 175px;}
.min-height-180 {min-height: 180px;}
.min-height-230 {min-height: 230px;}

.width-auto {width:auto;}
.width-0 {width: 0;}
.width-15 {width: 15px;}
.width-60 {width: 60px;}
.width-70 {width: 70px;}
.width-75 {width: 75px;}
.width-90 {width: 90px;}
.width-100 {width: 100px;}
.width-105 {width: 105px;}
.width-110 {width: 110px;}
.width-120 {width: 120px;}
.width-130 {width: 130px;}
.width-135 {width: 135px;}
.width-145 {width: 145px;}
.width-150 {width: 150px;}
.width-160 {width: 160px;}
.width-170 {width: 170px;}
.width-175 {width: 175px;}
.width-180 {width: 180px;}
.width-190 {width: 190px;}
.width-195 {width: 195px;}
.width-205 {width: 205px;}
.width-210 {width: 210px;}
.width-215 {width: 215px;}
.width-220 {width: 220px;}
.width-230 {width: 230px;}
.width-245 {width: 245px;}
.width-260 {width: 260px;}
.width-270 {width: 270px;}
.width-290 {width: 290px;}
.width-300 {width: 300px;}
.width-310 {width: 310px;}
.width-320 {width: 320px;}
.width-325 {width: 325px;}
.width-355 {width: 355px;}
.width-375 {width: 375px;}
.width-430 {width: 430px;}
.width-455 {width: 455px;}
.width-480 {width: 480px;}
.width-485 {width: 485px;}
.width-550 {width: 550px;}
.width-640 {width: 640px;}
.width-765 {width: 765px;}

.max-width-90 {max-width: 90px;}
.max-width-105 {max-width: 105px;}
.max-width-100px {max-width: 100px;}
.max-width-110 {max-width: 110px}
.max-width-120 {max-width: 120px}
.max-width-124 {max-width: 124px}
.max-width-130 {max-width: 130px;}
.max-width-135 {max-width: 135px;}
.max-width-140 {max-width: 140px;}
.max-width-160 {max-width: 160px;}
.max-width-165 {max-width: 165px;}
.max-width-175 {max-width: 175px;}
.max-width-150 {max-width: 150px}
.max-width-180 {max-width: 180px}
.max-width-185 {max-width: 185px;}
.max-width-200 {max-width: 200px}
.max-width-210 {max-width: 210px}
.max-width-215 {max-width: 215px}
.max-width-220 {max-width: 220px}
.max-width-290 {max-width: 290px}
.max-width-305 {max-width: 305px;}
.max-width-310 {max-width: 310px;}
.max-width-350 {max-width: 350px;}
.max-width-480 {max-width: 480px;}
.max-width-760 {max-width: 760px;}

.max-width-100{max-width:100%}
.max-width-percent-135{max-width:135%}

.min-width-40 {min-width: 40px;}
.min-width-70 {min-width: 70px;}
.min-width-110 {min-width: 110px}
.min-width-135 {min-width: 135px}
.min-width-160 {min-width: 160px}
.min-width-205 {min-width: 205px}
.min-width-230 {min-width: 230px}
.min-width-280 {min-width: 280px}
.min-width-860 {min-width: 860px}

.margin-neg-t-50 {margin-top: -50px;}
.margin-neg-t-10 {margin-top: -10px;}
.margin-neg-b-10 {margin-bottom:-10px;}
.margin-neg-b-25 {margin-bottom:-25px;}
.margin-neg-l-10 {margin-left:-10px;}

.dimension-72{height:72px;width:72px}
.dimension-60{height:60px;width:60px}
.dimension-45{height:45px;width:45px}
.dimension-30{height: 30px;width: 30px}
.txtSize36{font-size:36px}
.txtSize74 {font-size: 74px}
.top-0{top:0}
.right-0{right:0}
.left-0{left:0}
.margin-b-n15{margin-bottom:-15px}
.tab-control{min-height:63px}


/* Signal Colors */
.bgCyan{background-color:#A2F5F0}
.bgDarkBlue {background-color: #0072AD}
.bgTurquoise {background-color: #40BFD8}

/* Text Colors */
.txtDimGray {color: #6A6A6A;}

.box-shadow-hover:hover {
    box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
}
.box-shadow-round-hover:hover {
    box-shadow: 0 6px 25px 0 rgba(0,0,0,.2);
}

.page-title {
    font-family: "bellslim_font_heavy", Helvetica, Arial, sans-serif;
    letter-spacing: -.2px;
    font-size: 20px;
    line-height: 22px;
}

.small-title-2 {
    font-family: "bellslim_font_black", Helvetica, Arial, sans-serif;
    font-weight: 400;
    letter-spacing: -.4px;
    font-size: 24px;
    line-height: 26px;
    color: #111;
}
.banner-title {
    font-family: "bellslim_font_black", Helvetica, Arial, sans-serif;
    font-size: 26px;
    font-weight: 400;
    letter-spacing: -.4px;
    line-height: 28px;
    color: #111;
}
.txtSize12{
    font-size: 12px;
}

.text-shadow {
    text-shadow: 2px 2px 4px #000000;
}
.max-width-none {
    max-width: none;
}

.borderRadiusAll4 {
    border-radius: 4px;
}
.borderRadiusAll34 {
    border-radius: 34px;
}
.v-line-container {
    position: relative;
    height: 100%;
}
.v-line-text{
    position:relative;
    z-index:2;
}
.v-line{
    position: absolute;
    content: "";
    height: 100%;
    background-color: #D4D4D4;
    width: 1px;
    top: 0px;
    left: 50%;
    z-index: 1
}
.h-line-container {
    position: relative;
    width: 100%;
    text-align:center;
}

.h-line-text {
    position: relative;
    z-index: 2;
    display:inline;
    text-align:center;
}

.h-line {
    position: absolute;
    content: "";
    width: 100%;
    background-color: #D4D4D4;
    height: 1px;
    top: 50%;
    z-index: 1
}
.border-radius-5{
    border-radius:5px;
} 
.circle-icon-74{
    width:74px;
    height:74px;
    border-radius:50%;
}
.z-index-1{
    z-index: 1;
}

.z-index-2{
    z-index: 2;
}

.text-tag-positioned-center{
    display: flex;
    justify-content: center;
    width: 100%;
}

.text-tag-secondary {
    text-transform: uppercase;
    background-color: #FFFFFF;
    padding: 3px 8px;
    color: #00549A;
    font-size: 10px;
    border-radius: 2px;
    line-height: 14px;
    border: 1px solid #00549A;
}

[class^=accordion] [data-toggle="collapse"]:focus {
    outline: 0 !important;
    box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}

.accordionTab [data-toggle="collapse"]:focus {
    box-shadow: none;
}

.border-radius-right-10{
    border-radius: 0 10px 10px 0;
}

.border-radius-top-10 {
    border-radius: 10px 10px 0 0;
}

.list-style-inside {
    list-style-position: inside;
}
.border-left-gray2{
    border-left: 1px solid #e1e1e1;
}
.border-right-gray2{
    border-right: 1px solid #E1E1E1;
}

.hover-blue:hover, .hover-blue:focus {
    color: #00549a;
}

.img-responsive-height {
    max-height: 100%;
}
.line-height-1{
    line-height:1;
}
.line-height-18{
    line-height:18px;
}

.table-counter thead {
    counter-reset: table-heading;
}

.table-counter th .counter-increment:before {
    counter-increment: table-heading;
    content: counter(table-heading);
    display: block;
    position: absolute;
    left: 15px;
    width: 30px;
    height: 30px;
    line-height: 28px;
    border: 2px solid #fff;
    border-radius: 50%;
    font-size: 16px;
    text-align: center;
    top: 15px;
    speak: none;
}

.slick-transparent-button.slick-prev:focus,
.slick-transparent-button.slick-next:focus,
.slick-transparent-button.slick-prev:hover,
.slick-transparent-button.slick-next:hover {
    box-shadow: none;
    border: 1px solid #00549a;
    color: #00549a;
}

.slick-transparent-button.slick-next:focus:before,
.slick-transparent-button.slick-prev:focus:before,
.slick-transparent-button.slick-next:hover:before,
.slick-transparent-button.slick-prev:hover:before {
    color: #00549a;
}

.slick-transparent-button.slick-prev.slick-disabled:focus:before, 
.slick-transparent-button.slick-next.slick-disabled:focus:before {
    opacity: 1;
}

/* End custom transparent button for slick carousel*/

.bg-blue-spotlight-radial-gradiant {
    background: radial-gradient(circle, #1CBCF4 0%, #0E75CD 45.47%, #024791 69.57%, #012F6A 100%)
}

/*START Checkbox Focus Outline*/
.focus_outline .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element, .radio-container.focused-element {
    outline: none !important;
    box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}

.focus_outline_blue .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element, body.is_tabbing .focus_outline_blue *:focus,
.focus_outline_blue [class^=accordion] [data-toggle="collapse"]:focus {
    outline: none !important;
    box-shadow: 0 0 0px 3px #00549a, 0 0 2px 3px #00549a, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}

.focus_outline_gray .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element,
body.is_tabbing .focus_outline_gray *:focus, .focus_outline_gray [class^=accordion] [data-toggle="collapse"]:focus {
    outline: none !important;
    box-shadow: 0 0 0px 3px #f4f4f4, 0 0 2px 3px #f4f4f4, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}

.focus_outline_extra-light-blue .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element,
body.is_tabbing .focus_outline_extra-light-blue *:focus {
    outline: none !important;
    box-shadow: 0 0 0px 3px #c2cedf, 0 0 2px 3px #c2cedf, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}

.outline-circle:focus{
    border-radius: 50%;
}

.tab-control .header-tab-control.overflow-visible {
    overflow: visible;
}

/*END Checkbox Focus Outline*/

/*CUSTOM SLIDER BG*/
.slider-bg-blue-gradient {
    background: linear-gradient(180deg, #00549A 0%, #003778 100%);
}

/*START Override Tab Vertical */

.side-tab-control ul.tabs {
    border-top: 1px solid #e1e1e1;
    border-bottom: 1px solid #e1e1e1;
}
.side-tab-control ul.tabs.tabs_vertical li:before{
    color: #00549A;
    font-size: 13px;
}
.side-tab-control ul.tabs.tabs_vertical li.active_tabs:before {
    display: none;
}
.side-tab-control ul.tabs li {
    border: none;
    padding: 0;
    background: #F4F4F4;
}
.side-tab-control ul.tabs li.active_tabs {
    border-left: 1px solid #e1e1e1;
    box-sizing: border-box;
    background: #FFFFFF;
    box-shadow: 0 6px 25px 0 rgba(0,0,0,0.12)
}
.side-tab-control ul.tabs.tabs_vertical li.active_tabs a {
    color: #111111;
}
.side-tab-control ul.tabs.tabs_vertical li {
    color: #00549A;
    font-family: Arial;
    font-size: 16px;
    letter-spacing: 0;
    line-height: 18px;
    border-bottom: 1px solid #e1e1e1;
    padding: 5px;
}
.side-tab-control ul.tabs.tabs_vertical li:last-child{
    border-bottom: none;
}

.side-tab-control ul.tabs.tabs_vertical li a {
    color: #00549A;
    text-decoration: none;
    padding: 18px 35px 18px 15px;
    display: block;
}

.side-tab-control ul.tabs.tabs_vertical li:hover {
    background: #fff;
}

.side-tab-control select.custom-selection {
    background: #FFFFFF;
    color: #111111;
    font-size: 14px;
    height: 44px;
    border: 2px solid #949596;
    border-radius: 4px;
}

.side-tab-control select.custom-selection:focus {
    border: 2px solid #96B8EF;
}

.side-tab-control .card-body {
        padding: 0px;
}

/* ie overrides */
.side-tab-control select::-ms-value{
    background: #FFFFFF;
    color: #111111
}


/*END Override Tab Vertical */

.pad-b-80 {
    padding-bottom: 80px;
}

@media (max-width: 991.98px) {
    /* Use this class together with .subtitle-2-reg to achieve 14px font size on xs - sm and 18px on md */
    .subtitle-2-reg-3 {
        font-size: 14px;
        line-height: 18px;
        color: #555;
    }
    .subtitle-2-reg-3.inverted {
        color: #fff;
    }

    .container.no-pad-sm {
        padding: 0;
    }

    .min-width-sm-200 {min-width: 200px;}
    
    .max-width-sm-75 {max-width: 75px;}
    .max-width-110-sm {max-width: 110px;}
    .max-width-105-sm {max-width: 105px;}
    .max-width-160-sm {max-width: 160px;}
    .max-width-sm-175 {max-width: 175px;}

    .border-b-sm-0 {
        border-bottom: 0;
    }

    .banner-image {
        width: 460px;
    }

    /*no Bullets*/
    .noBullets-sm {
        list-style: none;
        padding: 0;
    }
    .border-radius-bottom-sm-10 {
        border-radius: 0 0 10px 10px;
    }


    .img-unlimited-tile {
        height: 130px;
    }

    /*table scrollbar for tablet and mobile*/
    .table-scrollbar::-webkit-scrollbar {
        height: 8px;
    }

    .table-scrollbar::-webkit-scrollbar-track {
        background: #e1e1e1;
        height: 8px;
    }

    .table-scrollbar::-webkit-scrollbar-thumb {
        height: 8px;
        background: #003778;
    }

    .max-width-335-sm {
        max-width: 335px;
    }
    .button-link-2 {
        font-weight: bold;
    }
}

@media (min-width: 768px) {
    .dimension-72-sm {height:72px;width:72px}
    .txtSize28-sm {font-size: 28px;}
    .default-text-sm {
        font-size: 14px;
        line-height: 18px;
    }
    .height-auto-sm{height:auto;}
    .absolute-sm {position: absolute;}

    .width-auto-sm {width:auto;}
    .width-25-sm {width:25px;}
    .width-sm-30 {width:30px;}
    .width-sm-75 {width:75px;}
    .width-sm-135 {width:135px;}
    .width-sm-145 {width:145px;}
    .width-120-sm {width:120px;}
    .width-150-sm {width:150px;}
    .width-165-sm {width:165px;}
    .width-170-sm {width:170px;}
    .width-180-sm {width:180px;}
    .width-sm-185 {width:185px;}
    .width-190-sm {width:190px;}
    .width-205-sm {width:205px;}
    .width-210-sm {width:210px;}
    .width-225-sm {width:225px;}
    .width-235-sm {width:235px;}
    .width-240-sm {width:240px;}
    .width-250-sm {width:250px;}
    .width-255-sm {width:255px;}
    .width-sm-260 {width:260px;}
    .width-270-sm {width:270px;}
    .width-290-sm {width:290px;}
    .width-295-sm {width:295px;}
    .width-315-sm {width:315px;}
    .width-325-sm {width:325px;}
    .width-380-sm {width:380px;}
    .width-390-sm {width:390px;}
    .width-410-sm {width:410px;}
    .width-430-sm {width:430px;}
    .width-465-sm {width:465px;}
    .width-545-sm {width:545px;}
    .w-60-sm {width:60% !important;}
    .w-40-sm {width:40% !important;}

    .min-width-120-sm {min-width: 120px}
    .min-width-170-sm {min-width: 170px}
    .min-width-195-sm {min-width: 195px}
    .min-width-225-sm {min-width: 225px}
    .min-width-230-sm {min-width: 230px}
    .min-width-280-sm {min-width: 280px}
    .min-width-290-sm {min-width: 290px}
    .min-width-sm-280 {min-width: 280px}
    .min-width-960-sm {min-width: 960px;}

    .max-width-420-sm {max-width: 420px;}
    .max-width-sm-600{max-width:600px;}
    .max-width-130-sm {max-width: 130px;}
    .max-width-330-sm {max-width: 330px;}
    .max-width-585-sm {max-width: 585px;}

    .height-100-percent-sm{height:100%;}
    .height-40-sm {height: 40px;}
    .height-150-sm {height: 150px;}
    .height-185-sm {height: 185px;}
    .height-205-sm {height: 205px;}
    .height-250-sm {height: 250px;}
    .height-270-sm {height: 270px;}
    .height-350-sm {height: 350px;}
    .height-330-sm {height: 330px;}
    .height-380-sm {height: 380px;}
    .height-390-sm {height: 390px;}
    .height-436-sm {height: 436px;}
    .height-440-sm{height: 440px;}

    .max-height-380-sm {max-height: 380px;}
    .max-height-436-sm {max-height: 436px;}
    .max-height-none-sm {max-height: none;}
    .max-height-150-sm {max-height: 150px;}
    .max-height-205-sm {max-height: 205px;}
    .text-indent-sm-30 {
        text-indent: 30px;
    }
    .line-height-18-sm {line-height:18px;}
    .margin-b-15-sm{
        margin-bottom:15px;
    }
    .margin-t-neg-35-sm{margin-top: -35px;}
    .margin-neg-r-20-sm {margin-right:-20px;}
    /* Column spacer 30 - use this to achieve equal 30 spacing between columns and 30 spacing between column and container*/

    .column-spacer-sm-30 {
        margin: 0 -15px;
    }
    .column-spacer-sm-30 > div {
        padding: 0 15px;
    }

    .borderRadiusAll6-sm{
        border-radius: 6px;
    }
    .border-all-right-radius-10-sm {
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
    }

    .pad-b-sm-65 {
        padding-bottom: 65px
    }

    .pad-b-120-sm {
        padding-bottom: 120px;
    }

    .column-count-sm-3{
        column-count: 3;
    }

    .column-count-sm-2{
        column-count: 2;
        -webkit-columns: 2;
        -moz-columns: 2;
    }

    ul.column-count-sm-2 li {
        break-inside: avoid-column;   
    }

    ul.column-count-sm-2 li span {
        page-break-inside: avoid;
    }

    .infoblock-slider .slick-list {
        margin: 0 -7.5px;
        padding: 0;
    }

    .infoblock-slider .slick-slide {
        margin-right: 7.5px;
        margin-left: 7.5px;
    }

    .infoblock-slider .slick-track {
        margin-left: 0;
    }
    /** Product Slider (with thumbnail) BTC-6660 **/
    .product-thumbnail-nav,
    .product-thumbnail-modal-nav {
        margin: 0 auto;
    }

    .product-thumbnail-nav .slick-list,
    .product-thumbnail-modal-nav .slick-list {
        margin: 0 auto;
    }
    
    .product-thumbnail-nav .slick-slide,
    .product-thumbnail-modal-nav .slick-slide {
        margin: 5px;
    }
    .product-thumbnail-nav .slick-slide.slick-current,
    .product-thumbnail-modal-nav .slick-slide.slick-current {
        border-bottom: 3px solid #00549A;
        padding-bottom: 8px;
    }

    .product-thumbnail-nav .slick-list,
    .product-thumbnail-modal-nav .slick-list {
        max-width: 205px;
        padding: 0 4px;
    }

        .product-thumbnail-nav.two-slides .slick-list,
        .product-thumbnail-modal-nav.two-slides .slick-list {
            max-width: 140px;
        } 
    
    .product-slider > div:active .hover-search,
    .product-slider > div:focus .hover-search,
    .product-slider > div:hover .hover-search {
        opacity: 1;
    }
    /** END Product Slider (with thumbnail) BTC-6660 **/
    .border-right-gray2-sm{
        border-right: 1px solid #E1E1E1;
    }
    .border-bottom-0-sm{
        border-bottom: 0;
    }    
    /*START Override Tab Vertical */
    .side-tab-control .card-body {
        padding: 0 0 15px 7px;
    }
    /*END Override Tab Vertical */
    .banner-title {
        font-size: 32px;
        line-height: 38px;
        letter-spacing: -0.5px
    }
}

@media (min-width: 992px) {
    
    .relative-md {position: relative;}

    .min-width-185-md {min-width: 185px}
    .min-width-md-220 {min-width: 220px;}
    .min-width-250-md {min-width: 250px}
    .min-width-300-md {min-width: 300px}
    .min-width-375-md {min-width: 375px}
    .min-width-455-md {min-width: 455px}
    .min-width-480-md {min-width: 480px;}
    .min-width-490-md {min-width: 490px;}

    .max-width-218-md {max-width: 218px}
    .max-width-485{max-width:485px;}
    .max-width-505-md {max-width: 505px;}
    .max-width-600 {max-width: 600px;}
    .max-width-md-960{max-width:960px;}
    .max-width-105-md{max-width:105px;}

    .width-135-md {width: 135px;}
    .width-150-md {width: 150px;}
    .width-170-md {width:170px;}
    .width-185-md {width: 185px;}
    .width-190-md {width: 190px;}
    .width-215-md {width: 215px;}
    .width-235-md {width: 235px;}
    .width-250-md {width: 250px;}
    .width-290-md {width: 290px;}
    .width-340-md {width: 340px;}
    .width-375-md {width: 375px;}
    .width-390-md {width: 390px;}
    .width-420-md {width: 420px;}
    .width-430-md {width: 430px;}
    .width-435-md {width: 435px;}
    .width-455-md {width: 455px;}
    .width-465-md {width: 465px;}
    .width-470-md {width: 470px;}
    .width-475-md {width: 475px;}
    .width-480-md {width: 480px;}
    .width-490-md {width: 490px;}
    .width-515-md {width: 515px;}
    .width-550-md {width: 550px;}
    .width-565-md {width: 565px;}

    .min-height-365-md {min-height: 365px;}

    .max-height-none-md {max-height: none;}
    .max-height-365-md {max-height: 365px;}
    .max-height-420-md {max-height:420px;}

    .height-250-md {height: 250px;}
    .height-270-md {height: 270px;}
    .height-300-md {height: 300px}
    .height-355-md {height: 355px;}
    .height-365-md {height: 365px}
    .height-395-md {height: 395px}
    .height-md-400 {height: 400px}
    .height-420-md {height: 420px;}
    .height-440-md{height: 440px;}
    .height-660-md {height: 660px;}

    .margin-neg-30-r-md {margin-right: -30px;}
    .margin-r-neg-80 {margin-right: -80px;}
    .margin-neg-right-115 {margin-right: -115px;}


    .margin-r-65 {margin-right: 65px}
    .column-spacer-30-md {
        margin-left: -15px;
        margin-right: -15px;
    }
        .column-spacer-30-md > div {
            padding-left: 15px;
            padding-right: 15px;
        }


    .breakSpace-md {white-space: normal;}

    .infoblock-slider > div.box-shadow-round,
    .infoblock-slider > a {
        margin-left: 7.5px;
        margin-right: 7.5px;
    }

    .infoblock-slider > div.box-shadow-round:first-child{
        margin-left: 0;
     }

    .infoblock-slider > div.box-shadow-round:last-child{
        margin-right: 0;
    }

    .dimension-70-md {
        height: 70px;
        width: 70px
    }

    /*custom image for mobility promotions*/
    .lg-velvet-img {
        height: 165px;
    }
    .relative-md {
        position: relative;
    }
}

/* Tablet Only */
@media (max-width: 991.98px) and (min-width: 768px) {

    .width-140-sm{width:140px;}
    .txtSize48-sm {font-size: 48px;}
    .txtSize50-sm {font-size: 50px;}
    .img-h-centerView-sm {
        margin-left: 50%;
        transform: translateX(-50%);
        min-width:100%;
    }
    
    .column-spacer-30-sm {
        margin-left: -15px;
        margin-right: -15px;
    }

        .column-spacer-30-sm > div {
            padding-left: 15px;
            padding-right: 15px;
        }

    .column-spacer-15-sm {
        margin-left: -7.5px;
        margin-right: -7.5px;
    }
        .column-spacer-15-sm > div{
            padding-left: 7.5px;
            padding-right: 7.5px;
        }
    
    .margin-left-percent-59-sm {margin-left:59%;}
    .margin-neg-15-l-sm {margin-left: -15px;}
    .margin-neg-30-r-sm {margin-right: -30px;}
    
    .fullheight-sm {height: 100%;}

    .height-sm-180 {height: 180px;}
    .height-125-sm {height: 125px;}
    .height-135-sm {height: 135px;}
    .height-175-sm {height: 175px;}
    .height-sm-250 {height: 250px;}
    .height-sm-290 {height: 290px;}
    .height-305-sm {height: 305px;}
    .height-sm-310 {height: 310px;}
    .height-315-sm {height: 315px;}
    .height-sm-355 {height: 355px;}
    .height-385-sm {height: 385px;}
    .height-sm-400 {height: 400px;}
    .height-575-sm {height: 575px;}

    .max-height-315-sm {max-height: 315px;}
    .max-height-500-sm {max-height: 500px;}

    .width-45-percent-sm {width:40%;}

    .width-60-sm {width: 60px;}
    .width-100-sm {width: 100px;}
    .width-sm-120 {width: 120px;}
    .width-125-sm {width: 125px;}
    .width-sm-135 {width: 135px;}
    .width-sm-175 {width: 175px;}
    .width-sm-195 {width: 195px;}
    .width-sm-210 {width: 210px;}
    .width-sm-215 {width: 215px;}
    .width-sm-230 {width: 230px;}
    .width-sm-250 {width: 250px;}
    .width-sm-265 {width: 265px;}
    .width-sm-275 {width: 275px;}
    .width-sm-280 {width: 280px;}
    .width-sm-285 {width: 285px;}
    .width-sm-305 {width: 305px;}
    .width-sm-310 {width: 310px;}
    .width-sm-320 {width: 320px;}
    .width-365-sm {width: 365px;}

    .max-width-185-sm {width:185px;}
    .max-width-sm-535 {
        max-width: 535px;
    }

    
    .max-width-650-sm {max-width: 650px;}
    .max-width-695-sm {max-width: 695px;}
    .max-width-712-sm {max-width: 712px;}

    .max-width-none-sm {
        max-width: none;
    }

    .border-sm-0{
        border:none;
    }

    .margin-neg-l-sm-30,
    .margin-neg-30-l-sm{
        margin-left: -30px;
    }

    .margin-neg-r-sm-110{
        margin-right: -110px;
    }

    .margin-r-neg-55-sm {margin-right: -55px;}
    .margin-neg-top-30-sm {margin-top:-30px;}
    .margin-neg-b-10-sm {margin-bottom:-10px;}

    .width-465 {width:465px;}

    .upgrade-phone-img {
        width: calc(440px + 10%);
    }


    .max-width-85-sm { max-width:85px;}
    .max-width-125-sm { max-width:125px;}
    .max-width-300-sm { max-width:300px;}

    .break-space-sm{
        white-space: normal;
    }

    .img-margin-top-neg-10 {
        margin-top: -10px;
    }

    /*custom for table*/
    .table-th-width-percent {
        width: 52%;
    }
}

@media (max-width: 767.98px) {

    .width-150-xs{width:150px;}
    .width-190-xs{width:190px;}
    .fullheight-xs {height: 100%;}
    .max-height-xs-150 {max-height: 150px;}
    .max-height-xs-205 {max-height: 205px;}

    .border-gray2-xs{border: 1px solid #e1e1e1}
    .no-side-borders-xs {
        border-left: none;
        border-right: none;
    }
    .border-gray2-top-xs{border-top: 1px solid #e1e1e1}
    .box-round-top-xs{border-radius:0 0 10px 10px}
    .box-round-bottom-xs{border-radius:10px 10px 0 0}

    .width-xs-35-percent{width:35%}
    .width-xs-40-percent{width:40%;}
    .width-xs-61-percent{width:61%;}
    .width-xs-65-percent{width:65%}

    .width-xs-20 { width: 20px;}
    .width-55-xs {width:55px;}
    .width-60-xs {width:60px;}
    .width-xs-70 {width: 70px;}
    .width-85-xs {width:85px;}
    .width-xs-100px {width: 100px;}
    .width-xs-110 {width: 110px;}
    .width-xs-120 {width: 120px;}
    .width-125-xs {width: 125px;}
    .width-130-xs {width: 130px;}
    .width-xs-160 {width: 160px;}
    .width-xs-195 {width: 195px;}
    .width-xs-220 {width: 220px;}
    .width-xs-230 {width: 230px;}
    .width-xs-250 {width: 250px;}
    .width-xs-290 {width: 290px;}
    .width-555-xs {width: 555px;}


    .max-width-none-xs {max-width: none;}

    .min-width-xs-70 {min-width: 70px;}
    .min-width-xs-75 {min-width: 75px;}
    .min-width-xs-130 {min-width: 130px;}
    .min-width-xs-175 {min-width: 175px;}
    .min-width-xs-230 {min-width: 230px;}
    .min-width-xs-375 {min-width: 375px;}
    .min-width-xs-400 {min-width: 400px !important;}
    
    .min-width-140-xs {min-width:140px;}
    .min-width-165-xs {min-width: 165px;}
    .max-width-345-xs {max-width: 345px;}
    .min-width-425-xs {min-width: 425px;}
    .max-width-425-xs {max-width: 425px;}
    .min-width-450-xs {min-width: 450px;}
    .max-width-xs-455 {max-width: 455px;}
    .max-width-125-xs {max-width: 125px;}

    .height-95-xs {height: 95px;}
    .height-130-xs {height: 130px;}
    .height-270-xs {height: 270px;}
    .height-280-xs {height: 280px;}
    .height-295-xs {height: 295px;}
    .height-325-xs {height: 325px;}
    .height-355-xs {height: 355px;}
    .height-585-xs {height: 585px;}

    .margin-neg-20-t-xs {margin-top: -20px;}
    .margin-neg-55-t-xs {margin-top: -55px;}

    .margin-l-xs-neg-5 {margin-left: -5px;}
    .margin-neg-30-l-xs {margin-left: -30px;}
    
    .margin-r-xs-neg-40 {margin-right: -40px;}
    .margin-r-neg-50-xs {margin-right: -50px;}
    
    .margin-neg-xs-20-b{margin-bottom:-20px;}

    .box-shadow-xs, .box-shadow-round-xs {
        box-shadow: 0 6px 25px 0 rgba(0,0,0,.12);
    }

    .txtSize7-xs{font-size: 7px;}
    .txtSize32-xs{font-size: 32px;}
    .txtSize34-xs{font-size: 34px;}

    .breakSpace-xs{
        white-space: normal;
    }

    .border-all-bottom-radius-10-xs {
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
    }
    .pad-h-xs-7_half{
        padding-left:7.5px;
        padding-right:7.5px;
    }
    .bg-xs-white{
        background-color: #FFFFFF;
    }
    .border-xs-0{
        border:none;
    }
    .circle-icon-60-xs {
        width:60px;
        height:60px;
        border-radius:50%;
    }
    .border-gray2-top-xs {
        border-top: 1px solid #e1e1e1;
    }
    .line-height-22-xs {
        line-height: 22px;
    }
    .line-height-24-xs{
        line-height:24px;
    }
    .column-spacer-xs-20{
        margin-left:-5px;
        margin-right:-5px;
    }
    .column-spacer-xs-20 > div{
        padding-left:10px;
        padding-right:10px;
    }

    .column-spacer-xs-15{
        margin-left:-7.5px;
        margin-right:-7.5px;
    }
    .column-spacer-xs-15 > div{
        padding-left: 7.5px;
        padding-right: 7.5px;
    }

    .static-xs{
        position:static;
    }

    .upgrade-phone-img {
        width: calc(300px + 10%);
        max-width: none;
        margin-left: auto;
        margin-right: auto;
    }

    /*custom for slider mobile*/
    .unslickMobile {
        flex-wrap: wrap;
    }

    .white-dots.transform-slick-slide .slick-dots li.slick-active button {
        background-color: #ffffff;
    }

    .white-dots.transform-slick-slide .slick-dots li button {
        border: 1px solid #ffffff;
        opacity: 1;
        background-color: transparent;
    }

    .white-dots.transform-slick-slide.slick-initialized .slick-dots {
        position:absolute;
        top:0;
    }

    .promotion-girl-img {
        transform: translate(-10px, 25px);
    }
    
    /* infoblock style for mobile */
    .infoblock-slider .slick-list {
        margin: 0 -30px;
        padding: 0 37.5px;
    }

    .infoblock-slider .slick-slide {
        margin-right: 7.5px;
        margin-left: 7.5px;
    }

    .infoblock-slider .slick-track {
        margin-left: -15px;
    }

    .slick-arrow-clear.infoblock-slider .slick-track {
        margin-left: 0px;
    }
}

@media (min-width: 1240px) {
    .margin-b-lg-15 {
        margin-bottom: 15px;
    }
    .max-width-lg-1200{
        max-width: 1200px
    }
    .width-570-lg {
        width: 570px;
    }
    .width-680-lg {
        width: 680px;
    }
    .height-260-lg {
        height: 260px;
    }
    .pad-t-10-lg{
        padding-top: 10px;
    }
    .pad-t-15-lg {
        padding-top: 15px;
    }
    .pad-t-25-lg{
        padding-top: 25px;
    }
    .pad-t-30-lg {
        padding-top: 30px;
    }
    .pad-h-15-lg{
        padding-left: 15px;
        padding-right: 15px;
    }
    .pad-l-25-lg {
        padding-left:25px;
    }
    .pad-l-15-lg {
        padding-left: 15px;
    }
    .pad-l-30-lg {
        padding-left: 30px;
    }
    .pad-l-35-lg{
        padding-left: 35px;
    }
    .pad-l-50-lg{
        padding-left: 50px;
    }
    .pad-l-60-lg{
        padding-left: 60px;
    }
    .pad-r-0-lg{
        padding-right: 0px;
    }
    .pad-r-lg-10{
        padding-right: 10px;
    }
    .pad-r-25-lg {
        padding-right: 25px
    }
    .pad-r-30-lg{
        padding-right: 30px;
    }
    .pad-r-35-lg{
        padding-right: 35px;
    }
    .pad-r-lg-45 {
        padding-left: 45px;
    }
    .pad-r-50-lg {
        padding-right: 50px;
    }
    .pad-r-60-lg {
        padding-right: 60px;
    }
    .pad-h-45-lg {
        padding-left: 45px;
        padding-right: 45px
    }
    .margin-r-lg-0 {
        margin-right: 0
    }
    .margin-b-60-lg {
        margin-bottom: 60px;
    }
    .margin-l-25-lg {
        margin-left:25px;
    }
    .margin-h-lg-auto{
        margin-left: auto;
        margin-right: auto;
    }
    .banner-title {
        font-size: 40px;
        line-height: 46px;
        letter-spacing: -0.7px
    }
}
/* END Helper Class */

/* START Custom Class */

/* Accordion expand and collapse Text */
.accordion-accessible-toggle[aria-expanded="true"] .expand-text,
.accordion-accessible-toggle[aria-expanded="false"] .collapse-text {
    display: none
}


/*START disabled select color*/
.disabled-select.form-control-select[disabled="disabled"],
.disabled-select.form-control-select[disabled="disabled"] + span{
    color:#BABEC2;
}
/*END disabled select color*/

/*START SMALL BUTTON FILTERS*/
.btn.btn-search-filter{
    padding: 5px 6px 5px 13px;
    font-size: 12px;
    line-height: 14px;
}
/*END SMALL BUTTON FILTERS*/
.btn-filter {
    /* standard */
    border: 1px solid #f4f4f4;
    /* mockup */
    /* border: 1px solid #d4d4d4; */
    color: #111;
}

.btn-filter:hover {
    color: #fff;
    border-color: #00549a;
    background-color: #00549a;
}

/* Start - Set channel icon to 40px */
.icon-circle-medium img, .icon-circle-medium .channel-minicard-img > img {
    max-height: 40px;
    max-width: 40px;
}
/* End - Set channel icon to 40px */
/*START Tablist regular font*/
.tablist-underlined-reg.tablist-underlined [role=tab] {
    font-size: 14px;
    line-height: 18px;
}
.tablist-underlined-reg.tablist-underlined [role=tab]:not(:last-child) {
    margin-right: 20px;
}
/*END Tablist regular font*/

.close-alert{
    position: absolute;
    top:0;
    right:0;
}
.slick-dots li.slick-active button {
    background: #555;
}

.slick-dots li button {
    border: 2px solid #555;
}
/* infoblock slider custom css*/
.infoblock-slider .slick-dots {
    padding-top: 15px;
    line-height:0px;
}

    /* START Custom Arrows */
    .infoblock-slider .slick-arrow.slick-disabled {
        display: none;
    }

    .infoblock-slider .slick-prev, .infoblock-slider .slick-next {
        background: #fff;
        border: 1px solid #E1E1E1;
        box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
        margin: 0px 15px;
        margin-top: -15px;
        opacity: 1;
        transform: translateY(-50%);
    }

    .slick-arrow-clear.infoblock-slider .slick-prev, .slick-arrow-clear.infoblock-slider .slick-next {
        background: transparent;
        border: none;
        box-shadow: none;
        margin: 0px 40px;
        transform: translateY(27%);
    }
        .infoblock-slider .slick-next:before {
            top: 12px;
        }

        .infoblock-slider .slick-prev:hover,
        .infoblock-slider .slick-prev:focus,
        .infoblock-slider .slick-next:hover,
        .infoblock-slider .slick-next:focus {
            background: #fff;
            border: 1px solid #00549A;
            box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
            color: #00549a;
        }

        .slick-arrow-clear.infoblock-slider .slick-next:before {
            top: 11px;
            right:10px;
        }

        .slick-arrow-clear.infoblock-slider .slick-prev:before {
            top: 12px;
            left:10px;
        }

        .slick-arrow-clear.infoblock-slider .slick-prev:hover,
        .slick-arrow-clear.infoblock-slider .slick-prev:focus,
        .slick-arrow-clear.infoblock-slider .slick-next:hover,
        .slick-arrow-clear.infoblock-slider .slick-next:focus {
             border: 2px solid #00549A;
             background-color: #FFFFFF;
             box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
             color: #00549a;
        }



            .infoblock-slider .slick-prev:hover:before,
            .infoblock-slider .slick-prev:focus:before,
            .infoblock-slider .slick-next:hover:before,
            .infoblock-slider .slick-next:focus:before {
                color: #00549a;
                opacity: 1;
            }

    .infoblock-slider .slick-arrow.slick-disabled {
        display: none;
    }

    .infoblock-slider .slick-prev:focus, .infoblock-slider .slick-next:focus, .infoblock-slider .slick-next:focus:before, .infoblock-slider .slick-prev:focus:before {
        background: #fff;
        color: #00549a;
    }
    
    .slick-arrow-clear.infoblock-slider .slick-prev:focus, .slick-arrow-clear.infoblock-slider .slick-next:focus, .slick-arrow-clear.infoblock-slider .slick-next:focus:before, .slick-arrow-clear.infoblock-slider .slick-prev:focus:before {
        background: transparent;
    }

    /* END Custom Arrows */
/* infoblock slider custom css*/

.responsive-background-cover{
    background-size: cover;
    background-repeat:no-repeat;
    background-position:center center;
}
body.is_tabbing .tab-pills-focus>li:focus,
.tab-pills-focus>li.active:focus,
.tab-panels-container>div:focus {
    box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}
/*Class for Star Rating*/
.star-ratings .fill-ratings{
    position: absolute;
    top:0;
    overflow: hidden;
}
.star-ratings .empty-ratings{
    color:#babec2;
}
.star-ratings.ave-rating .fill-ratings,
.star-ratings.ave-rating .empty-ratings{
    font-size:25px;
}
/*END of Class for Star Rating*/

.borderRadiusLeft-10{
    border-radius: 10px 0 0 10px;
}
.borderRadiusRight-10{
    border-radius: 0 10px 10px 0;
}
.accordion-body ~ div > div > *:nth-child(2){display:none}
.accordion-body.show ~ div > div > *:first-child{display:none}
.accordion-body.show ~ div > div > *:nth-child(2){display:block}
.accordion-button-2 {
    z-index: 1;
    padding: 14px;
    background-color: #fff;
    box-shadow: 0 0 36px 0 rgba(0,0,0,.3);
}

/* Override disabled arrow on bell.css */
.disabled-arrow.form-control-select + span {
    color: #BABEC2;
}

/* Override radius on bell.css */
.mobility-form-custom .form-control {
    border-radius: 4px;
}

.topCenter-centered {
    top: 0;
    left: 50%;
    transform: translate(-50%,-50%);
}

.accordionContainer a[aria-expanded=true] span:not(.icon) {
    font-size: 14px;
    color: #0F0F0F;
}

.accordion-wrap .accordion-content {
    text-decoration: none;
}

/*START Radio Button*/
.radio-container {
    border: 1px solid #D4D4D4;
    background-color: #FFFFFF;
    box-shadow: 0 2px 3px 0 rgba(0,0,0,0.2);
    padding: 24px;
    border-radius: 4px;
}

.radio-container {
    padding: 15px;
    height: 54px;
    display: flex;
    align-items: center;
}

.graphical_ctrl input:checked ~ span {
    color: #111;
}

.ctrl_radioBtn .ctrl_element:after {
    left: 5px;
    top: 5px;
    height: 13px;
    width: 13px;
    border-radius: 50%;
    background: #fff;
}

.ctrl_element {
    position: absolute;
    top: -3px;
    left: 0;
    height: 25px;
    width: 25px;
    background: #fff;
    border: 1px solid #ccc;
}

.v-center {
    top: 50%;
    transform: translateY(-50%);
}

.graphical_ctrl input:checked ~ * {
    color: #003778;
}

.graphical_ctrl input[type="radio"]:focus ~ .ctrl_element {
    outline: none;
}

.checkbox-container.checked-border, .radio-container.checked-border {
    border: 2px solid #003778;
}


.display-radio-container {
    display: none;
}

    .display-radio-container.block {
        display: block;
    }

.radio-container-multi-sim-selected.radio-container.checked-border {
    border: 3px solid #00549A;
}
/*END Radio Button*/
/*START Multi Sim Card Radio Button*/
.radio-container-multi-sim-selected {
    border: 1px solid #e1e1e1;
    box-shadow: 0 6px 25px 0 rgba(0,0,0,.12);
    border-radius: 10px;
    height: auto;
}

    .radio-container-multi-sim-selected .big-price {
        right: 0;
        top: -5px;
    }

    .radio-container-multi-sim-selected .graphical_ctrl input ~ * {
        color: #555;
    }

    .radio-container-multi-sim-selected .graphical_ctrl input ~ div > span {
        color: #111;
    }

    .radio-container-multi-sim-selected .graphical_ctrl input:checked + * {
        font-weight: bold;
        color: #111;
    }

    .radio-container-multi-sim-selected .ctrl_radioBtn .ctrl_element {
        top: 0;
    }
/*END Multi Sim Card Radio Button*/


.price-strike{
    color: #555;
}
.price-strike del{
    text-decoration: line-through 2px;
    -webkit-text-decoration: line-through 2px;
}

.price_inactive {
    text-decoration: line-through;
    color: #BABEC2;
}

.bg-image-cover-no-repeat-bottom {
    background-size: cover;
    background-position: bottom;
    background-repeat: no-repeat;
}

.icon-circle-25 {
    height: 25px;
    width: 25px;
    border-radius: 50%;
    position: relative;
}

.margin-neg-10-t {
    margin-top: -10px;
}

.text-tag-positioned {
    position: absolute;
    top: -20px;
    left: 30px;
    transform: translateY(50%);
}

.margin-neg-40-t {
    margin-top: -40px;
}

.centerView {
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
}

.innerShadowContainer {
    box-shadow: inset 0 0 80px 30px rgba(0,0,0,0.05);
    width: 100%;
    height: 100%;
    top: 0;
    z-index: 3;
}

.destination-content-details > div {
    display: none;
}

.table-scrollable-wrapper tbody td {
    padding: 0;
}

    .table-scrollable-wrapper tbody td div.inner-content {
        padding: 15px 15px;
    }


/* Start of Coverage Map Custom Size */
.map-custom-height {
    height: 445px;
}

/* End of Coverage Map Size*/

.map-scrollbar::-webkit-scrollbar {
    height: 6px;
}

.map-scrollbar::-webkit-scrollbar-track {
    background: #e1e1e1;
    height: 6px;
}

.map-scrollbar::-webkit-scrollbar-thumb {
    height: 6px;
    background: #003778;
    border-radius:4px;
}

.list-description-wrap {
    max-width: 100%;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    position: relative;
}

    .list-description-wrap .list-description-item {
        width: calc(100%/2);
        margin-bottom: 15px;
    }

.img-h-centerView {
    margin-left: 50%;
    transform: translateX(-50%);
    min-width:100%;
}


.img-work-study-centerView {
    margin-left: 50%;
    transform: translateX(-50%);
}


.progress.progress-responsive-2{
    height: 26px;
}
/*START Tablist pills blue*/
.tablist-pills-container.tablist-pills-container-blue ul{
    padding:3px;
    border-radius: 18px;
}
.tablist-pills-container.tablist-pills-container-blue ul li{
    padding: 6px 20px;
}
.tablist-pills-container.tablist-pills-container-blue ul li[aria-selected=true]{
    background-color: #00549A;
    color:#fff;
    border-radius: 15px;
}
/*END Tablist pills blue*/
.tablist-pills-container-white ul {
    background: #fff;
    border: 1px solid #E1E1E1;
}

.box-vignette-vertical{
    background: linear-gradient(-180deg, #fafafa, #f4f4f4);
}
/* Start custom transparent button for slick carousel*/
.slick-transparent-button.slick-prev,
.slick-transparent-button.slick-next {
box-shadow: none;
border: 1px solid transparent;
color: #00549a;
background-color:transparent;
opacity:1;
}

.slick-transparent-button.slick-prev:focus,
.slick-transparent-button.slick-next:focus,
.slick-transparent-button.slick-prev:hover,
.slick-transparent-button.slick-next:hover {
    box-shadow: none;
    border: 1px solid #00549a;
    color: #00549a;
}

.slick-transparent-button.slick-next:focus:before,
.slick-transparent-button.slick-prev:focus:before,
.slick-transparent-button.slick-next:hover:before,
.slick-transparent-button.slick-prev:hover:before {
    color: #00549a;
}

.slick-transparent-button.slick-prev.slick-disabled:focus:before, 
.slick-transparent-button.slick-next.slick-disabled:focus:before {
    opacity: 1;
}

/* End custom transparent button for slick carousel*/

label.checkbox-filter.checked, label.radio-filter.checked {
    background: #003778;
    color: #fff;
}
label.radio-filter.checked{
    border-color:#003778;
}
input.checkbox-filter.checked + label, input.radio-filter.checked + label {
    background: #003778;
    color: #fff;
}
input.checkbox-filter:focus + label, input.radio-filter:focus + label {
    box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}

/*Fix for accessibility issue: hide focusable element under an inactive .slick-slide*/
.slick-slide[aria-hidden="true"] .tooltip-interactive, .slick-slide[aria-hidden="true"] .tooltip-static, [aria-hidden="true"] [role="radio"] {
    display: none !important;
}

/* fix for BTC-6508 see the motorola on tablet */
main a:hover > .anchor-text > span, main a:focus > .anchor-text > span {
    text-decoration: underline;
}

/* START Our Network Classes (BM-4750) */
.network-coverage-banner {
    background-color: #023877;
}

.network-coverage-container {
    top: -55%;
    right: 0;
}


.our-network-img-banner {
    background-repeat: no-repeat;
}

.our-network-img-5g {
    margin-left: 50%;
    transform: translateX(-53%);
}
/* END Our Network Classes (BM-4750) */

.more-about-carousel .image-wrap{
    width: 260px;
    height: 225px;
}

/* START Accordion Tab Panel */
.tab-track-border-left::before {
    content: "";
    width: 1px;
    background: #000;
    position: absolute;
}

.tab-track-border-left {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    position: relative;
}

.tab-track-thumb {
    top: 0;
    height: 0px;
    background: #FFF;
    width: 4px;
    transition: all 600ms cubic-bezier(0.64, 0.04, 0.35, 1);
}

.panel-with-arrow-bottom:after {
    content: "";
    border: 15px solid transparent;
    border-top-color: #F4F4F4;
    bottom: -30px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}
.animated-tab .slick-dots {
    position: absolute !important;
    top: 0px;
}

.animated-tab .white-dots .slick-dots li.slick-active button {
    background-color: #ffffff;
}

.animated-tab .white-dots .slick-dots li button {
    border: 1px solid #ffffff;
    opacity: 1;
    background-color: transparent;
}

.mobility-circle-icon.tooltip-interactive.active {
    z-index: 9999;
}

/* Start Simplified Header */

.simplified_header {
    box-shadow: 1px 2px 4px 0 rgba(0,0,0,0.24);
    z-index: 1001;
}

.simplified_header .connector-brand {
    top: auto;
    position: relative;
}

.simplified_header .connector-brand a:before,
.simplified_header .connector-brand:after {
    content: "";
}

.simplified_header .connector-nav {
    display: flex;
    top: auto;
    position: relative;
    background: transparent;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
    z-index: 50;
    overflow: auto;
}

.simplified_header .connector-nav {
    height: 55px;
}

.simplified_header .connector-nav.region-selector-popup {
    overflow: visible;
}

.simplified_header .connector .connector-brand a {
    top: -1px;
}

.simplified_header .federal-bar-links {
    font-size: 12px;
    line-height: 14px;
    letter-spacing: normal;
}

.simplified_header .vertical-text-divider::before {
    background-color: #fff;
    content: "";
    display: inline-block;
    height: 13px;
    margin: -2px 10px 0 10px;
    vertical-align: middle;
    width: 1px;
}

.simplified_header .federal-bar-links a,
.simplified_header .federal-bar-links a:link,
.simplified_header .federal-bar-links a:visited {
    color: #fff;
}

.simplified_header .federal-bar-links a:hover,
.simplified_header .federal-bar-links a:active {
    text-decoration: underline;
}

.simplified_header .federal-bar-select-provinces-popup a:hover {
    text-decoration: none;
}

.simplified_header a.active .checkbox:after {
    color: #00549a;
}

.simplified_header .federal-bar-select-provinces-popup a:hover .label-text {
    color: #555555;
    font-weight: normal;
}

.simplified_header .federal-bar-select-provinces-popup a {
    height: auto;
}

.simplified_header .federal-bar-select-provinces .checkbox {
    border: none;
    background-color: transparent;
    box-shadow: none;
    height: 22px;
}

.simplified_header .caret:after {
    display: block;
}

.simplified_header .popup.caret:after {
    border-width: 12px;
    left: calc(50% + 100px);
}

.simplified_header .federal-bar-select-provinces-popup {
    display: none;
    position: absolute;
    left: auto;
    right: -17px;
    z-index: 100;
    background: inherit;
    background-color: white;
    padding: 15px 10px;
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.3);
    top: 26px;
    width: 250px;
    text-transform: none;
    border-radius: inherit;
}

.simplified_header .federal-bar-select-provinces-popup {
    top: 29px;
}

.simplified_header .federal-bar-select-provinces .label {
    text-transform: initial;
    padding: 5px 5px 3px 7px;
    text-transform: none;
    cursor: pointer;
}

.simplified_header .federal-bar-select-provinces .label:hover,
.simplified_header .federal-bar-select-provinces .label:focus {
    background: #e1e1e1;
    border-radius: 3px;
}

.simplified_header .federal-bar-select-provinces .label.active .label-text {
    color: #00549a;
    font-weight: bold;
}

.simplified_header .federal-bar-select-provinces .label .label-text {
    font-size: 13px;
    color: #555555;
}

.simplified_header .federal-bar-select-provinces .label.disabled {
    cursor: default;
}
/* End Simplified Header */

.caption-table-top {
    color: inherit;
    caption-side: top;
}
.slick-small-container .slick-arrow {
    transform:translateY(50%);
}
body.is_tabbing .slick-small-container .slick-slide:focus .content {
    box-shadow: 0 0 0px 3px #f4f4f4, 0 0 2px 3px #f4f4f4, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}
.slick-small-container .slick-arrow:focus,
.slick-small-container .slick-arrow:hover {
    box-sizing: border-box;
    border: 1px solid #2B96E4;
    box-shadow: 0 0 1px 1px #2B96E4;
}
body.is_tabbing .slick-small-container .slick-slide:focus {
    box-shadow:none;
}
.tooltip-interactive.icon-info, .tooltip-static.icon-info {
    color: #949596;
}
@media (min-width: 992px) {

    .width-640 {width:640px;}
    .img-huawei-tablet {
        height: 200px;
    }

    .top-neg-15-md {
        top: -15%;
    }

    .margin-neg-85-t-md {margin-top: -85px;}
}

/* Tablet Only */
@media (max-width: 991.98px) and (min-width: 768px) {
    .width-175-sm{width:175px}
    .more-ways-to-shop .bell-bottom-links > li {
        margin: auto 0px;
    }

    .banner-crop-img.rightView-0-sm {
        position: absolute;
        height: 100%;
        width: auto;
        top: 50%;
        right: 0%;
        transform: translate(0%,-50%);
    }

    .text-tag-positioned {
        left: -1%;
        transform: translateY(50%);
    }

    .map-custom-height {
        height:338px;
    }

    .top-sm-15 {
        top: 15px;
    }

    .max-width-160-sm {
        max-width:160px;
    }

    /*custom for slick slider button*/
    .slick-arrow-clear.infoblock-slider .slick-prev, .slick-arrow-clear.infoblock-slider .slick-next {
        transform: translateY(20%);
        margin: 0px 30px;
    }

    /*custom for input search*/
    input::-webkit-input-placeholder {
        max-width: 88%;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }

    input::-moz-placeholder {
        max-width: 88%;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }

    .img-work-study-centerView {
        margin-left: 50%;
        transform: translateX(-56%);
    }

    .top-neg-10-sm {
        top: -10%;
    }

    /* START Our Network Classes (BM-4750) */
    .network-coverage-container {
        top: -45%;
        right: -60px;
    }

    .our-network-img-banner {
        background-position: calc(50% - 203px) 100%;
        background-size: 255%;
    }
    /* END Our Network Classes (BM-4750) */

    /*custom image for mobility promotions*/
    .lg-velvet-img {
        height: 205px;
    }

    .margin-neg-85-t-sm {margin-top:-85px;}

    .progress.progress-responsive.height-sm-55{
        height: 55px;
    }
}

@media (min-width: 768px) {
    .min-height-350-sm {min-height: 350px;}
    .tooltip-medium .tooltip{
        width:290px;
    }
    .tooltip-medium .tooltip-inner{
        padding:25px;
    }

    .tooltip-huge{
        width:440px;
        line-height: 18px;
    }
    .tooltip-huge .tooltip-inner{
        padding:25px;
        max-width: none;
    }

    .more-ways-to-shop .bell-bottom-links > li {
        flex: 1;
        justify-content: center;
        max-width: 215px;
        flex-basis: 100%;
    }

    .modal-dialog-md {
        width: 740px;
    }

    .modal-header{
        padding: 15px 30px 15px 30px;
    }

    .modal-header.multiline-header {
        height: auto;
        padding: 20px 20px 20px 30px;
    }
    .tab-control .header-tab-control ul li:last-child {
        margin-right: 0;
    }
    .border-sm-left-gray2 {
        border-left: 1px solid #e1e1e1;
    }
    /*START Porgress bar*/
    .progress.progress-responsive{
        height: 40px;
    }

    .progress.progress-responsive .progress-left-title span{
        font-size: 14px;
        color: #fff;
        padding-left:15px;
    }
    .progress.progress-responsive .progress-title div span{
        color: #fff;
    }
    .progress.progress-responsive .progress-right-title{
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
    }
    /*END Porgress bar*/

    /*START Porgress bar 2 Right Title*/
    .progress-right-title-2{
        position: absolute;
        top: 0;
        right: 0;
        height: 26px;
    }
    /*END Porgress bar*/


    .table-fixed-sm {
        table-layout: fixed;
    }

    .list-description-wrap .list-description-item {
        width: calc(100%/4);
        margin-bottom: 15px;
    }

    .margin-neg-right-75 {
        margin-right:-75px;
    }

    .radio-container-multi-sim-selected {
        padding: 30px 30px 25px 30px;
    }

        .radio-container-multi-sim-selected .ctrl_radioBtn .ctrl_element {
            top: -3px;
        }

    .width-percent-25 {
        width: 25%;
    }
    /* START Button-link Class */
    .button-link {
        border-radius: 20px;
        font-size: 15px;
        height: 35px;
        line-height: 17px;
        text-align: center;
        cursor: pointer;
        padding: 7px 28px;
        white-space: nowrap;
        color: #fff;
        background-color: #003778;
        border: 2px solid #003778;
        margin-left: auto;
    }

        .button-link:hover, .button-link:focus {
            color: #fff;
            background-color: #00549a;
            border-color: #00549a;
        }

        .button-link:focus {
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
    /* END Button-link Class */

    /* START Button-link-3 Class */
    .button-link-3 {
        border-radius: 20px;
        font-size: 15px;
        height: 35px;
        line-height: 17px;
        text-align: center;
        cursor: pointer;
        padding: 7px 28px;
        white-space: nowrap;
        color: #fff;
        background-color: #003778;
        border: 2px solid #003778;
        margin-left: auto;
    }

        .button-link-3:hover, .button-link-3:focus {
            color: #fff;
            background-color: #00549a;
            border-color: #00549a;
        }

        .button-link-3:focus {
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        a:hover.button-link-3 > .anchor-text,
        a:focus.button-link-3 > .anchor-text{
            text-decoration: none;
        }

    /* END Button-link-3 Class */
    .banner-tablist.tablist-underlined-reg.tablist-underlined [role=tab]:not(:last-child){
        margin-right: 30px;
    }

    /* Start Header Float Right Link Class (BTC-5100) */
    .title-viewAllLink{
        margin: 0;
    }

    .title-rightLink{
        position: absolute;
        bottom: 0;
        right: 0;
    }
    /* End Header Float Right Link Class (BTC-5100) */

    .more-about-carousel .image-wrap {
        width: 465px;
        height: 390px;
    }
    .more-about-carousel .button-wrap {
        height: 390px;
    }

    /* Custom for tab panel slider*/
    .tab-track-border-left::before {
        height: 385px;
    }

    .tab-track {
        border-left: 4px solid #FFF;
    }

    .panelFadeIn {
        animation: panelFadeInEffect ease 1s;
    }

    .tabDescriptionFadeIn {
        position: relative;
        animation: tabDescriptionFadeInEffect 800ms cubic-bezier(0.64, 0.04, 0.35, 1);
    }
    /*START Tablist pills blue*/
    .tablist-pills-container.tablist-pills-container-blue ul{
        border-radius: 30px;
        padding: 4px;
    }
    .tablist-pills-container.tablist-pills-container-blue ul li{
        padding: 8px 30px;
    }
    .tablist-pills-container.tablist-pills-container-blue ul li[aria-selected=true]{
        border-radius: 30px;
    }
    /*END Tablist pills blue*/

    /* Start Fullscreen modal */
    .modal.show .modal-dialog.v-centered {
        top: 50%;
        transform: translateY(-50%);
    }
    /* End Fullscreen modal */
    .slick-small-container .slick-arrow {
        transform: translateY(45%);
    }
}

@media (max-width: 767.98px) {
    .top-neg-30 {
        top: -30%;
    }

    .max-wdith-none-xs {max-width: none;}
    .max-width-285-xs {max-width:285px;}

    .more-ways-to-shop .bell-bottom-links > li > a .anchor-icon {
        flex-shrink: 0;
        height: 60px;
        width: 60px;
    }
    .more-ways-to-shop .bell-bottom-links > li > a .anchor-text {
        margin-left: 15px;
    }

    .title.banner-title{
        font-size: 24px;
        line-height: 26px;
    }
    .centerView-xs{
        margin-left:50%;
        transform: translateX(-50%);

    }
    .form-control-search-box.form-control-search-box-xs input {
        padding: 11px 16px 11px 32px;
    }
    .form-control-search-box.form-control-search-box-xs .icon {
        position: absolute;
        left: 20px;
        top: 15px;
        font-size:15px;
        color:#ccc;
    }

    .box-round-transform div:not(:only-child):first-child > div {
        border-radius: 10px 10px 0 0;
        border-bottom: none;
    }
    .box-round-transform div:not(:only-child):last-child > div {
        border-radius: 0 0 10px 10px;
    }

    /*custom for modal tooltip*/
    .modal.modal-tooltip .modal-body, .modal-footer .tooltip-mobile{
        padding: 0px 30px 30px;
        margin-bottom: 0px;
        margin-top: 0px;
    }

    .modal.modal-tooltip .modal-body .tooltip-mobile-overlay {
        background-color: rgba(0, 0, 0, 0.6)!important;
    }

        /*custom for image banner*/
        .our-network-img-banner {
        background-size: 350%;
        background-position: calc(50% - 225px) 74%, bottom;
    }

    /*custom image for mobility promotions*/
    .lg-velvet-img {
        height: 165px;
    }

    /*custom video banner*/
    .video-h-centerView {
        transform: translateX(-17%);
        min-width: 141%;
    }

    /* Mobile Slider */
    .transform-slick-slide.slick-initialized .slick-list {
        overflow: visible;
        margin: 0 -30px;
        margin-left: -40px;
        padding: 0 30px;
        padding-left: 30px;
        padding-right: 25px;
    }
    .transform-slick-slide.slick-initialized .slick-track {
        display: flex;
        margin-left: -22.5px;
        padding: 30px;
        padding-right: 40px;
        padding-left: 40px;
        width: 100%;
    }

    .transform-slick-slide.slick-initialized .slick-track.margin-n30-left {
        margin-left: -30px;
    }

        .transform-slick-slide.slick-initialized .slick-track.margin-n15-left {
            margin-left: -15px;
        }

    .slick-arrow-clear.infoblock-slider .slick-prev, .slick-arrow-clear.infoblock-slider .slick-next {
        margin: 0px 5px;
        transform: translateY(5%);
    }

    /*Slick full width*/
    .transform-slick-slide.slick-initialized.slick-full-width .slick-list {
        overflow: hidden;
        margin: 0px;
        margin-left: 0px;
        margin-right: 0px;
        padding: 0px;
        padding-left: 0px;
        padding-right: 0px;
    }
    .transform-slick-slide.slick-initialized.slick-full-width .slick-track {
        display: flex;
        margin-left: 0;
        padding: 0px;
        padding-right: 0px;
        padding-left: 0px;
    }
        .transform-slick-slide.slick-initialized.slick-full-width .slick-track.margin-n30-left {
            margin-left: 0px;
        }
        .transform-slick-slide.slick-initialized.slick-full-width .slick-track.margin-n15-left {
            margin-left: 0px;
        }
    /*End Slick full width*/

    .transform-slick-slide.slick-initialized {
        width: 100%;
        margin: 0 auto;
    }

    .transform-slick-slide.slick-initialized .slick-slide {
        height: auto;
    }

    .transform-slick-slide.slick-initialized .slick-slide > div {
        height: 100%;
    }

    .transform-slick-slide.slick-initialized .slick-slide .slickSlide {
        height: 100%;
    }

    .transform-slick-slide.slick-initialized .slick-slide .slickSlide .two-column-card {
        height: 100%;
    }

    .transform-slick-slide.slick-initialized .slick-dots {
        margin-top: -5px;
        margin-right: -20px;
    }

    /*START slick dot full width*/
    .transform-slick-slide.slick-initialized .slick-dots {
        margin-top: 0px;
        margin-right: 0px;
    }
    /*END slick dot full width*/

    .slick-container .slide-top-content {
        justify-content: center;
    }

    .slick-container .slide-top-content-info {
        padding-left: 10px;
    }

    .transform-slick-slide .slick-dots li button {
        border: 1px solid #555555;
        opacity: 1;
        background-color: #fff;
    }

    .transform-slick-slide .slick-dots li.slick-active button {
        background-color: #555555;
    }

    /*End of Mobile Slider*/

    .list-xs-inside {
        list-style-position: inside;
    }

    .column-spacer-15 > div {
        padding-left: 0;
        padding-right: 0;
    }
    
    .column-spacer-15 > .width-xs-50-percent {
        padding-left: 7.5px;
        padding-right: 7.5px;
        width: 50%;
    }

    .column-spacer-xs-15 > div{
        padding-left: 7.5px;
        padding-right: 7.5px;
    }

    .ctrl_radioBtn .ctrl_element {
        top: 50%;
        left: 0;
    }
    .small-price.small-price-xs,
    .small-price.small-price-xs > span{
        font-family: Arial;
        font-size: 14px;
        line-height: 18px;
        top:0;
        margin-right: 0;
        letter-spacing: normal;
        font-weight: bold;
    }
    .small-price.small-price-xs span:last-of-type{
        margin-left: 0;
    }
    /*START Porgress bar*/
    .progress.progress-responsive{
        height: 8px;
        overflow: visible;
        margin-bottom: 53px;
    }
    .progress.progress-responsive .progress-left-title span{
        font-size: 14px;
        bottom:-23px;
        position: absolute;
    }
    .progress.progress-responsive .progress-title div {
        bottom: -23px;
        position: absolute;
    }
    .progress.progress-responsive .progress-right-title{
        position: absolute;
        bottom:-18px;
        right: 0;
        height: 100%;
    }

    .margin-b-data-add-ons.progress.progress-responsive,
    .last-child-mb-0.progress.progress-responsive:last-child {
        margin-bottom: 37px;
    }
    .last-child-mb-0 .progress.progress-responsive:last-child {
        margin-bottom: 20px;
    }
    .margin-b-pay-per-use-flex-data.progress.progress-responsive {
        margin-bottom: 53px;
    }
    /*END Porgress bar*/
    .list-indent-15-xs li, .nested-list-indent-15-xs ul li, .nested-list-indent-15-xs ol li {
        margin-left: 15px;
        text-indent: -15px;
    }

    .text-tag-positioned {
        top: -10px;
        left: 45%;
        transform: translateX(-50%);
    }

    .text-tag-positioned-left {
        left: 5px;
    }

    /* Custom table with scroll */
    .table-scrollable-wrapper {
        border-right: 1px solid #d4d4d4;
    }

        .table-scrollable-wrapper::-webkit-scrollbar {
            height: 8px;
        }

        .table-scrollable-wrapper tbody td div.inner-content {
            padding: 15px 15px;
        }


    .map-custom-height {
        height:265px;
    }

        .table-scrollable-wrapper::-webkit-scrollbar-track {
            background: #e1e1e1;
            height: 8px;
        }

        .table-scrollable-wrapper::-webkit-scrollbar-thumb {
            height: 8px;
            background: #003778
        }

        .table-scrollable-wrapper-2 tbody td div.inner-content {
            padding: 12px 15px;
        }

    .expander-description {
        max-height: 95px;
        overflow: hidden;
        position: relative;
        transition: max-height 0.3s ease-out;
    }

        .expander-description:after {
            background: linear-gradient(180deg, rgba(255,255,255,0) 0%, #FFFFFF 100%);
            content: '';
            bottom:0;
            height: 58px;
            left: 0;
            pointer-events: none;
            position: absolute;
            width: 100%;
            display: block;
        }

        .expander-description[style]:after {
            display: none;
        }

    .expander-description-control[aria-expanded="true"] .collapse-text, .expander-description-control[aria-expanded="false"] .expanded-text {
        display: none;
    }

    .expander-description-control[aria-expanded="false"] .collapse-text, .expander-description-control[aria-expanded="true"] .expanded-text {
        display: inline;
    }

    .max-height-xs-155,
    .expander-description.max-height-xs-155{
        max-height: 155px;
    }

    .form-control-search-box input.no-border-radius-xs{
        border-radius: 0;
    }

    .width-315 {width:315px;}

    /* START mobile Override Opacity Dots Behavior */
    
    .opacityDots .slickDots-container{
        width: 132px;
        overflow: hidden;
        display: block;
        padding: 0;
        margin: 0 auto;
        height: 15px;
        position: relative;
    }
    
    .opacityDots .slickDots-container > ul.slick-dots {
        padding: 0;
        display: flex;
        transition: all 0.25s;
        position: relative;
        margin: 0;
        list-style: none;
        align-items: center;
        bottom: unset;
        height: 100%;
    }
    
    .opacityDots .slickDots-container > ul.slick-dots li {
        width: 0.625rem;
        height: 0.625rem;
        margin: 0 0.25rem;
    }
    
    .opacityDots .slickDots-container > ul.slick-dots li button {
        display: block;
    }
    
    .opacityDots .slick-dots li.opacity-20 {
        opacity: 0.2;
    }
    .opacityDots .slick-dots li.opacity-50 {
        opacity: 0.5;
    }
    
    /* END mobile Override Opacity Dots Behavior */
    
    /* AsideNav css */
    #magic-line {
        display: none;
    }

    .subnav-wrap {
        border: 1px solid #D4D4D4;
        margin-bottom: 30px;
        border-radius: 10px;
        padding-top: 25px;
        padding-right: 0;
    }
    /* END of AsideNav css */
    
    .modal-header .close.custom-modal-close-xs {
        margin: 0 -15px 0 0 !important;
        padding: 10px !important;
    }
    
    .border-lightGray-top-xs {
        border-top: 1px solid #d4d4d4;
    }

    /* START Button-link Class */
    .button-link {
        color: #00549a;
        text-decoration: underline;
    }

        .button-link:hover, .button-link:focus {
            text-decoration: none;   
        }
    /* End Button-link Class */


    /* START Button-link Class */
    .button-link-3 {
        font-weight: bold;
    }
    /* End Button-link Class */

    /* Start Header Float Right Link Class (BTC-5100) */
    .title-viewAllLink{
        text-align: right;
        margin-top: 15px;
        margin-bottom: 0px;
    }
    .title-rightLink{
        padding: 0;
        margin: 0;
        line-height: 1;
        color: #00549a;
        background-color: transparent;
        text-decoration: underline;
        white-space: nowrap;
        border: none;
    }
    /* End Header Float Right Link Class (BTC-5100) */

    /* START Our Network Classes (BM-4750) */
    .network-coverage-container {
        top: auto;
        bottom: -68px;
        right: -40px;
    }

    .our-network-img-5g {
        margin-left: 50%;
        transform: translateX(-50%);
    }
    /* END Our Network Classes (BM-4750) */

    /* tab margin for mobile */
    .tab-control .header-tab-control.tab-margin-xs ul li:not(:last-child) {
        margin-right: 10px;
    }
    .tab-control .header-tab-control.tab-margin-xs ul li:last-child {
        margin-right: 0px;
    }

    .right-15-xs {
        right: 15px;
    }
    .slick-small-container .slick-prev {margin-left:-5px;}
    .slick-small-container .slick-next {margin-right:-5px;}
}

/*Start of migrated carousel banner for homepage banners*/
.slider-rotating-carousel-component {
    position: relative;
}
.slider-rotating-carousel-list-left .slider-rotating-carousel-buttons {
    bottom: 33px;
    right: auto;
    left: 15px;
}
.slider-rotating-carousel-list-left.carousel-list-standard-position .slider-rotating-carousel-buttons {
    bottom: 18px;
}
.slider-rotating-carousel-list-right-md.carousel-list-standard-position .slider-rotating-carousel-pause {
    bottom: 15px;
}

/*fix for the  slider showing multiple images*/
.slider-rotating-carousel-component .slider-rotating-carousel {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease;
    -webkit-transition: opacity 0.3s ease;
    max-height: 445px;
}

.slider-rotating-carousel-component .slider-rotating-carousel.slick-initialized {
    visibility: visible;
    opacity: 1;
    max-height: initial;
}

.slider-rotating-carousel-banner.slider-rotating-carousel-height {
    height: 440px;
    overflow: hidden;
}

.slider-rotating-content-component-wrap {
    text-align: center;
    z-index: 1;
    padding: 30px 15px 15px;
}

/* Carousel styles - START */
.slider-rotating-carousel-buttons {
    position: absolute;
    bottom: 29px;
    left: 15px;
    height: 34px;
    border-radius: 16px;
    background-color: #fff;
    list-style: none;
    display: flex;
    flex-direction: row;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.2);
    margin: 0;
    padding: 4px 2px;
}

.slider-rotating-carousel-button {
    position: relative;
    border-radius: 50%;
    border: none;
    height: 26px;
    width: 26px;
    background-color: #fff;
    color: #003778;
    font-size: 14px;
    line-height: 16px;
    text-align: center;
    margin: 0 2px;
    padding: 0;
}

.slider-rotating-carousel-button:hover {
    text-decoration: none;
    cursor: pointer;
}

.slider-rotating-carousel-pause:hover,
.slider-rotating-carousel-component :hover,
.slider-rotating-carousel-pause-bg:hover,
.slider-rotating-carousel-pause-bg::before:hover,
.slider-rotating-carousel-pause-bg::before:hover {
    cursor: pointer;
}

.slider-rotating-carousel-button > * {
    pointer-events: none;
}

.slider-rotating-carousel-buttons > li.slick-active > .slider-rotating-carousel-button {
    color: #fff;
    background-color: #00549A;
}

.slider-rotating-carousel-pause {
    height: 38px;
    width: 38px;
    border-radius: 50%;
    border: none;
    background-color: transparent;
    position: absolute;
    bottom: 26px;
    right: 15px;
    z-index: 10;
    padding: 0;
    cursor: pointer;
}

.slider-rotating-carousel-pause-bg {
    height: 34px;
    width: 34px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    border-radius: 50%;
    background-color: #FFF;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.2);
    pointer-events: none;
}

svg.slider-rotating-carousel-progress {
    overflow: visible;
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
    transform: rotate(-90deg);
}

svg.slider-rotating-carousel-progress circle.slider-rotating-carousel-progress_initial {
    display: none;
}

svg.slider-rotating-carousel-progress circle {
    stroke: #0075FF;
    stroke-width: 3px;
    stroke-dasharray: 125;
    stroke-dashoffset: 0;
    fill: rgba(225,255,255,0);
}

.slider-rotating-carousel-pause[data-pressed="false"] .slider-rotating-carousel-pause-bg:before, .slider-rotating-carousel-pause[data-pressed="false"] .slider-rotating-carousel-pause-bg:after {
    content: "";
    display: block;
    position: absolute;
    top: 50%;
    left: calc(50% - 3px);
    transform: translate(-50%,-50%);
    height: 10px;
    width: 0;
    border: 1px solid #003778;
    z-index: 1;
    background-color: #003778;
}

.slider-rotating-carousel-pause[data-pressed="false"] .slider-rotating-carousel-pause-bg:before {
    left: calc(50% + 3px);
}

.slider-rotating-carousel-pause[data-pressed="true"] .slider-rotating-carousel-pause-bg:before {
    content: "";
    display: block;
    position: absolute;
    top: 50%;
    left: calc(50% + 1px);
    transform: translate(-50%,-50%);
    width: 0;
    height: 0;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 11px solid #003777;
    z-index: 1;
}
/* Carousel styles - END */

.bell-tag-position {
    top: 10px;
    left: 10px;
}
/* Custom banner styles - START */

.slider-rotating-carousel .default-height {
    height: 440px;
    overflow: hidden;
}


@media (max-width: 639px) {
    .slider-rotating-content-component-wrap {
        height: auto;
    }
}

@media (min-width: 640px) {
    .slider-rotating-content-component-wrap {
        padding-top: 15px;
        flex-grow: 1;
        width: 40%;
        padding-right: 20px;
        padding-left: 0;
        z-index: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: left;
    }
}

@media (min-width: 767.98px) {
    /* Carousel styles - START */
    .slider-rotating-carousel-buttons {
        bottom: 33px;
        right: 82px;
        left: auto;
    }
    .slider-rotating-carousel-list-left .slider-rotating-carousel-buttons {
        bottom: 32px;
        right: auto;
        left: 33px;
    }

    .slider-rotating-carousel-list-right-sm .slider-rotating-carousel-buttons {
        bottom: 32px;
        right: 82px;
        left: auto;
    }

    .slider-rotating-carousel-pause {
        bottom: 30px;
        right: 27px;
    }
    .slider-rotating-carousel .text-container {
        width: 413px;
    }
    .slider-rotating-carousel .image-container {
        width: 461px;
    }
    .slider-rotating-carousel .default-height {
        height: 440px;
        overflow: hidden;
    }

    .table-min-width-70-xs tr th {
        min-width: 70px;
    }

}

  
@media (min-width: 992px) {
    .slider-rotating-carousel-list-right-md.carousel-list-standard-position .slider-rotating-carousel-buttons {
        bottom: 28px;
    }
    .slider-rotating-carousel-list-left.carousel-list-standard-position .slider-rotating-carousel-pause {
        bottom: 25px;
    }
    .slider-rotating-carousel-list-right-md .slider-rotating-carousel-buttons {
        bottom: 32px;
        right: 82px;
        left: auto;
    }

    /* Default banner-height */
    .slider-rotating-carousel .default-height{
        height:440px;
        overflow:hidden;
    }
    .slider-rotating-carousel .max-width-330 {
        max-width:330px;
    }
    .slider-rotating-carousel .image-container{
        min-width:517px;
        max-width:100%;
    }
    .slider-rotating-carousel .text-container {
        width: 330px;
    }
    .slider-rotating-carousel-pause {
        right: 16px;
    }
    .slider-rotating-carousel-buttons {
        right: 71px;
    }
    .list-description-wrap .list-description-item {
        width: calc(100%/6);
    }

    .lg-velvet-img {
        height: 210px;
    }
    .close-alert{
        bottom:0;
    }

    /*custom for table*/
    .table-th-width-percent {
        width: 52%;
    }

    .width-300-md {
        width:300px;
    }
    .button-link-2 {
        border-radius: 20px;
        font-size: 15px;
        height: 35px;
        line-height: 17px;
        text-align: center;
        cursor: pointer;
        padding: 7px 28px;
        white-space: nowrap;
        color: #fff;
        background-color: #003778;
        border: 2px solid #003778;
        margin-left: auto;
    }

        .button-link-2:hover, .button-link-2:focus {
            color: #fff;
            background-color: #00549a;
            border-color: #00549a;
        }

        .button-link-2:focus {
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        a:hover.button-link-2 > .anchor-text,
        a:focus.button-link-2 > .anchor-text{
            text-decoration: none;
        }
    .more-about-carousel .image-wrap {
        width: auto;
        height: auto;
    }

    /* Custom for tab panel slider*/
    .tab-track-border-left::before {
        height: 395px;
    }
    /*Custom for two line text in progress bar desktop*/
    .progress.progress-responsive.height-md-55 {
        height: 55px;
    }

    /* Start Simplified header */
    .simplified_header .connector-brand-home {
        position: relative;
    }

    .simplified_header .connector-nav {
        height: 75px;
    }

    .simplified_header .connector .connector-brand a {
        top: auto;
    }
    /* End Simplified header */

    .slick-small-container .slick-arrow {
        transform: translateY(50%);
    }
}

@media (min-width:992px) and (max-width:1239px) {

    /*custom for BM-4750*/
    .our-network-img-banner {
        background-position: calc(50% - 160px) 100%;
        background-size: 205%;
    }
}

@media (min-width: 1100px) {
    .tab-track-border-left::before {
        height: 425px;
    }
    
    .max-width-none-lg {max-width: none;}
}

@media (min-width:1200px) {
    .tab-track-border-left::before {
        height: 375px;
    }
    .height-650-lg {
        height: 650px;
    }
    .height-375-lg {
        height: 375px;
    }
    .height-460-lg {
        height: 460px;
    }
    .pad-h-lg-35 {
        padding-left: 35px;
        padding-right: 35px;
    }

    .max-height-500-lg {max-height:500px;}

    .width-560-lg {width: 560px;}
    .width-585-lg {width: 585px;}
    .width-610-lg {width: 610px;}
    .width-620-lg {width: 620px;}
    

    .img-h-centerView-lg{
        min-width:unset;
    }

    .margin-top-neg-45-lg {
        margin-top:-45px;
    }

}

@media (max-width: 1239px) {
    .slick-overflow-visible .slick-list{
        overflow:visible;
    }
}

@media (min-width: 1240px) {
    /* Carousel styles - START */
    .slider-rotating-carousel-buttons {
        transform: translateX(600px);
        right: calc(50% + 55px);
        left: auto;
    }
    .slider-rotating-carousel-list-right-lg .slider-rotating-carousel-buttons {
        transform: translateX(600px);
        right: calc(50% + 55px);
        left: auto;
    }

    .slider-rotating-carousel-pause {
        transform: translateX(600px);
        right: 50%;
        left: auto;
    }       
    /* Carousel styles - END */
    .slider-rotating-content-component-wrap {
        width: 40%;
        padding-right: 60px;
    }

    /*START Grey block Carousel Widescreen Style*/
    .grey-block-slick .slick-list {
        padding: 0;
        margin: 0;
    }

    .grey-block-slick .slick-track {
        margin-left:0;
    }
    /*END Grey block Carousel Widescreen Style*/

    /*Carousel with border shadow Style START*/
    .box-shadow-slick{
        position: relative;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
    }

    .box-shadow-slick::before,
    .box-shadow-slick::after {
        display: flex;
        content: ' ';
    }
    
    .box-shadow-slick .slick-list::before,
    .box-shadow-slick .slick-list::after {
        content: "";
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 25px;
        background: #000;
        background: linear-gradient(90deg,rgba(255,255,255,1) 60%,rgba(255,255,255,0) 100%);
        z-index: 80;
    }

    .box-shadow-slick .slick-list::after  {
        left: auto;
        right: 0;
        background: linear-gradient(90deg,rgba(255,255,255,0) 0%,rgba(255,255,255,1) 60%);
    }
    
    .box-shadow-slick .slick-list {
        padding: 0 15px;
        overflow-y: visible;
        margin: 0 -7.5px;
    }

    .box-shadow-slick .slick-track {
        margin-left: 0;
    }
    
    .box-shadow-slick .slick-slide {
        position: relative;
        padding-top: 30px;
        padding-bottom: 50px;
    }
    
    .box-shadow-slick ul.slick-dots {
        margin-top: -15px;
    }

    /* Start hide inactive slides*/
    .hiddenInactive {
        margin-left: -30px;
        margin-right: -30px;
    }
    .hiddenInactive .slick-slide.offscreen {
        visibility: hidden;
    }

    .hiddenInactive .slick-list {

    padding-left: 30px;
    padding-right: 30px;
    overflow:hidden;
    padding-top: 30px;
    padding-bottom: 50px;
    margin-top:-30px;
    margin-bottom:-50px;
    }

    .hiddenInactive .slick-next{
        right:-5px;
        margin-top: 3px;
    }
    .hiddenInactive .slick-prev{
        left:-5px;
        margin-top: 3px;
    }
    .hiddenInactive.sliding .slick-list:before, .hiddenInactive.sliding .slick-list:after {
        content: "";
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 30px;
        background: #000;
        background: linear-gradient( 90deg,rgba(255,255,255,1) 60%,rgba(255,255,255,0) 100%);
        z-index: 80;
    }
    .hiddenInactive.sliding .slick-list:after {
        left: auto;
        right: 0;
        background: linear-gradient( 90deg,rgba(255,255,255,0) 0%,rgba(255,255,255,1) 60%);
    }
    /*End hide inactive slides*/

    /*Carousel with border shadow Style END*/

    
    /* custom banner for (BM-4750) */
    .our-network-img-banner {
        background-position: calc(50% - 140px) 100%;
        background-size: 140%;
    }
    .big-title-lg {
        font-size: 40px;
        letter-spacing: -.7px;
        line-height: 46px;
    }
    .modal.show .modal-dialog.v-centered {
        top: 0;
        transform: none;
        position:relative;
    }
}

/*Carousel style overwritten - same height shadow box*/
.slider-rotating-carousel-component {
    background: #FFF;
}

.slider-rotating-carousel-component .slick-track {
    display: table;
}

.slider-rotating-carousel-component .slick-slide {
    display: table-cell !important;
    vertical-align: middle;
    float: none;
    position: relative;
}

/*Adding carousel with tabs style*/
.slider-rotating-carousel-component + .nav-tabs-se {
    background: linear-gradient(to bottom,rgba(0,0,0,0.04) 0 100%);
    border-bottom: 1px solid #d4d4d4;
    background-color: #FFF;
}

.slider-rotating-carousel-component + .nav-tabs-se a.rsx-tabs-tab.rsx-active {
    position: relative;
    bottom: -1px;
    z-index: 1;
}

.slider-rotating-carousel-component + .nav-tabs-se .rsx-tabs:not(.rsx-tabs_vertical) .rsx-tabs-tab.rsx-active .rsx-tabs-tab-top {
    background-color: #fff;
    display: table-cell;
    float: none;
    vertical-align: middle;
}

.slider-rotating-carousel-pause:hover, .slider-rotating-carousel-buttons .slider-rotating-carousel-button:hover {
    -webkit-appearance: button;
    cursor: pointer;
}

/*End of migrated carousel banner for homepage banners*/
/* Override the existing line-height of tab-control li */
.tab-control .header-tab-control ul li {
    line-height: 18px;
}



/*Tile Price*/
.tile-price-down {
    width: 25%;
}

.tile-price-month {
    width: calc(55% - 15px);
}

.tile-price-apr {
    width: 20%;
}

/* Radio Button */

.tile-color-round {
    width: 17px;
    height: 17px;
    border-radius: 50%;
    border: 1px solid #999999;
}

.tile-color-round-2 {
    width: 14px;
    height: 14px;
    border-radius: 50%;
}

.tile-border {
    border: 2px solid #999999;
}

/*START Color pallette focus outline*/
.selected-color-pallette, .tile-color-round-2:focus {
    border-radius: 100%;
    border: 2px solid #00215E;
    background-clip: content-box;
    padding: 2px;
    width: 16px;
    height: 16px;
    box-shadow: none;
}

.tile-color-round-2:not(selected-color-pallette) {
    width: 16px;
    height: 16px;
}
/*END Color pallette focus outline*/

/* Phone Tile Colors */
.bgBlackPhone {background-color:#333;}
.bgGrey {background-color:#E5E5E5;}
.bgGrey3 {background-color:#999999;}
.bgGrey4 {background-color:#535059;}
.bgBluePhone {background-color:#1E68A7;}
.bgBluePhone2 {background-color:#00549A;}
.bgBluePhone3 {background-color:#48AEE6;}
.bgBluePhone4 {background-color:#004878;}
.bgBluePhone5 {background-color:#0F5E93;}
.bgBluePhone6 {background-color: #003778;}
.bgSilver {background-color: #cacaca;}
.bgCyan {background-color: #9AFAE2;}
.bgCyan2 {background-color:#9AFAE2;}
.bgRed {background-color:#C01E1E;}
.bgRed2 {background-color:#B40202;}
.bgRed3 {background-color:#b41325;}
.bgRed4 {background-color:#C91A28;}
.bgYellow2 {background-color:#EBBB4D;}
.bgKournikova {background-color:#F9D045;}
.bgMaroon {background-color:#881919;}
.bgMocha {background-color: #41444d;}
.bgIllusionSunset {background-color:#dc3c6f;}
.bgIllusionSunset2 {background-color:#FB3555;}
.bgLavender {background-color:#CED9FF;}
.bgLavender2 {background-color:#DCDBF7;}
.bgBittersweet {background-color:#FF6F5A;}
.bgPippin {background-color:#FFDCD3;}
.bgPink {background-color:#f99;}
.bgGreen {background-color:#aee1cd;}
.bgOrange {background-color:#f90;}
.bgBronze,.bgMysticBronze {background-color:#cd7f32;}
.bgMysticGrey, .bgAppleGrey {background-color:#999;}
.bgGrey2,.bgAppleSilver {background-color:#E4E4E4;}
.bgAppleRed {background-color:#af1e2d;}
.bgViolet,.bgPurple {background-color:#d1cdda;}
.bgGrainBrown,.bgAppleGold {background-color:#C9B8A3;}
.bgRoseGold,.bgGold {background-color:#EAC6B6;}
.bgAppleRed {background-color: #83b8f0;}
.bgAppleGreen {background-color: #97da65;}
.bgAppleRoseGold {background-color: #ffcccc;}



.color-selector .bgBittersweet {background-color:#FF6F5A;}
.color-selector .bgBlack {background-color:#333;}
.color-selector .bgDarkBlue2 {background-color:#05457A;}
.color-selector .bgBlue {background-color:#1E68A7;}
.color-selector .bgBlue2 {background-color:#00549A;}
.color-selector .bgBlue3 {background-color:#48AEE6;}
.color-selector .bgBlue4 {background-color:#004878;}
.color-selector .bgBlue5 {background-color:#0F5E93;}
.color-selector .bgBlue6 {background-color: #003778;}
.color-selector .bgBronze {background-color:#cd7f32;}
.color-selector .bgCyan {background-color: #9AFAE2;}
.color-selector .bgCyan2 {background-color:#9AFAE2;}
.color-selector .bgGrainBrown, .color-selector .bgAppleGold {background-color:#C9B8A3;}
.color-selector .bgGreen {background-color:#aee1cd;}
.color-selector .bgGrey {background-color:#E5E5E5;}
.color-selector .bgGrey2 {background-color:#E4E4E4;}
.color-selector .bgGrey3 {background-color:#999999;}
.color-selector .bgGrey4 {background-color:#535059;}
.color-selector .bgGrey5 {background-color:#C2C2DF;}
.color-selector .bgIllusionSunset {background-color:#dc3c6f;}
.color-selector .bgIllusionSunset2 {background-color:#FB3555;}
.color-selector .bgKournikova {background-color:#F9D045;}
.color-selector .bgLavender {background-color:#CED9FF;}
.color-selector .bgLavender2 {background-color:#DCDBF7;}
.color-selector .bgMaroon {background-color:#881919;}
.color-selector .bgMocha {background-color: #41444d;}
.color-selector .bgMysticBronze {background-color:#cd7f32;}
.color-selector .bgMysticGrey, .color-selector .bgAppleGrey {background-color:#999;}
.color-selector .bgOrange {background-color:#f90;}
.color-selector .bgPink {background-color:#f99;}
.color-selector .bgPippin {background-color:#FFDCD3;}
.color-selector .bgRed {background-color:#C01E1E;}
.color-selector .bgRed2 {background-color:#B40202;}
.color-selector .bgRed3 {background-color:#b41325;}
.color-selector .bgRed4 {background-color:#C91A28;}
.color-selector .bgRed5 {background-color:#B71A1A;}
.color-selector .bgRoseGold,.color-selector .bgGold {background-color:#EAC6B6;}
.color-selector .bgSilver {background-color: #cacaca;}
.color-selector .bgViolet, .color-selector .bgPurple {background-color:#d1cdda;}
.color-selector .bgWhite {background-color:#fff; border: 2px solid #999999;}
.color-selector .bgWhite2 {background-color:#fff;}
.color-selector .bgYellow2 {background-color:#EBBB4D;}
.color-selector .bgMustard {background-color:#E79426;}
.color-selector .bgAppleRed {background-color:#af1e2d;}
.color-selector .bgAppleSilver {background-color:#E4E4E4;}
.color-selector .bgMintGreen {background-color:#CAF0DA;}
.color-selector .bgAppleRed {background-color: #83b8f0;}
.color-selector .bgAppleGreen {background-color: #97da65;}
.color-selector .bgAppleRoseGold {background-color: #ffcccc;}


                                        

/* Start Car with multiple tooltips */

/* use together with .position-absolute */
.mobility-circle-icon {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background-color: rgba(255,255,255,0.5);
    cursor: pointer;
    box-shadow: 3px 3px 10px 0 rgba(17,17,17,0.66);
    transform: translate(-50%, -50%);
}
.mobility-circle-icon .inner-circle {
    width: 11px;
    height: 11px;
    border-radius: 50%;
    background-color: #FFF;
    top: 2px;
    left: 2px;
    cursor: pointer;
    transition: background-color .2s ease-in-out;
}
.mobility-circle-icon .inner-circle:hover {
    background-color: #003778
}

/* Start override the default tooltip */
.connected-car .tooltip {
    width: 230px;
}
.connected-car .tooltip p {
    line-height:18px;
}
.connected-car .tooltip-inner {
    padding: 30px;
    box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
}
.connected-car .tooltip-inner > div > div:first-child {
    margin-right:30px;
}
/* End override the default tooltip */

@media (min-width: 768px) {
    .mobility-circle-icon {
        width: 26px;
        height: 26px;
    }
    .mobility-circle-icon .inner-circle {
        width: 18px;
        height: 18px;
        top: 4px;
        left: 4px;
    }

    .max-width-790 {
        max-width: 790px;
    }

    .height-405 {
        height:405px;
    }
}
/* End Car with multiple tooltips */

@media screen and (-ms-high-contrast: active) and (-ms-high-contrast: none) {
    .ie-margin-l-neg-10 {
        margin-left: -10px;
    }
    .disabled-select.form-control-select[disabled]::-ms-value{
        color:#BABEC2;
    }
}

@media screen and (-ms-high-contrast: active) and (max-width: 767.98px), (-ms-high-contrast: none) and (max-width: 767.98px) {
    .ie-margin-0 {
        margin: 0px;
    }

    .ie-margin-l-xs-25 {
        margin-left: 25px;
    }
}


@-moz-document url-prefix() { 
    @media screen and (max-width: 767.98px) {        
        ol.list-indent-15-xs li {
            margin-left: 25px; 
            text-indent: -20px;
        }

        .list-indent-15-xs ul li, .list-indent-15-xs ol li {
            margin-left: 10px; 
            text-indent: -17px;
        }

        .margin-l-xs-neg-5 {
            margin-left: 0;
        }
    }

}

/* Start slick override for car connectivity */

.car-video-slider .slick-slide {
    width: 104px !important;
    margin-right: 10px;
}
.car-video-slider .slick-arrow {
    display:none!important;
}
.icon-play-xs-40.icon-play_hover_multi .icon2.path1, .icon-play-xs-40.icon-play_hover_multi .icon2.path2 {
    font-size: 40px;
}
.icon-play-xs-40.iconBlock .icon2.path2:before {
    top: 0px!important;
}

.icon-play-xs-40.iconBlock .icon2.path1:before {
    top: 0px;
    left: 0px;
}
.car-video-container #video-player {
    width: 100%;
    height: 135px;
}
.car-video-slider img {
    border: 2px solid rgba(0, 84, 154, 0);
}
.car-video-slider img.active {
    border: 2px solid #00549a;
}
.car-video-slider .slick-list {
    overflow:visible;
}
.car-video-slider .slick-dots li.slick-active button {
    background: #555;
    opacity: 1;
}
.car-video-slider .slick-dots li button {
    border: 2px solid #555;
}

@media screen and (min-width: 768px){
    .car-video-container #video-player {
        width: 100%;
        height: 337px;
    }
    .car-video-slider .slick-slide {
        width: 100px !important;
        margin-right: 10px;
    }
}

@media screen and (min-width:992px){
    .car-video-container #video-player {
        height: 468.28px;
        width:100%;
    }

    .height-520-md {
        height: 520px;
    }
}
/* End Slick Override for car connectivity*/

/* remove top position of play button */

.iconBlock .icon2.path2.top-0:before, .iconBlock .icon2.path1.top-0:before {
    top: 0px;
}
.iconBlock .icon2.path1.left-0:before {
    left: 0px;
}

/* Start Regular Search Bar */
.form-control-search-box input {
    background-color: #fff;
    border-radius: 34px;
    height: 44px;
    color: #555;
    padding: 11px 16px 11px 16px;
}
.form-control-search-box input:focus {
    border: 2px solid #003778;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6);
    outline: 0 !important;
    box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
    }
.form-control-search-box .icon {
    position: absolute;
    right: 22px;
    top: 10px;
}

/* End Regular Search Bar */
.background-center {
    background: center;
}
.background-no-repeat{
    background-repeat:no-repeat;
}
.background-cover{
    background-size:cover;
}


/* Start of Coverage Colors*/
.coverage-box {
    height: 30px;
    width: 30px;
    border-radius: 5px;
}

/* End of Coverage Colors*/

/* End of Signal Colors*/

.modal-dialog-video {
    width: 70%;
    max-width: 100%;
}

/* Start Google Map override */
.cluster span {
    top: -6px !important;
    position: absolute !important;
    background-color: white !important;
    line-height: 1 !important;
    border: 1px solid #00549A !important;
    padding-left: 2px !important;
    padding: 1px 2px 1px 2px !important;
    left: 18px !important;
    color: #00549A
}
/* End google map override */

.txtSize8 {
    font-size: 8px!important;
}

.txtSize6 {
    font-size: 6px!important;
}

.line-height-10 {
    line-height: 10px !important;
}

.min-width-450 {
    min-width: 450px;
}


/* START column divide with arrow override */
.two-column-arrow-divider {
    display: flex;
    flex-wrap: wrap;
}
    /*  */
    .two-column-arrow-divider > div:first-child {
        border: none;
        border-right: 1px solid #e1e1e1;
        padding: 0 30px 0 0;
        display: flex;
    }

        .two-column-arrow-divider > div:first-child:before {
            content: "";
            width: 30px;
            height: 30px;
            position: absolute;
            border: 1px solid #e1e1e1;
            right: 5px;
            transform: rotate(45deg) translateY(-100%);
            border-bottom: none;
            border-left: none;
            background-color: #fff;
            top: 50%;
        }
        /* grey color override */
        .two-column-arrow-divider.arrow-divider-grey > div:first-child:before {
            background-color: #f4f4f4;
        }

    .two-column-arrow-divider > div:last-child {
        padding: 0 0 0 30px;
    }

    @media (max-width: 767.98px) {
        /* start column divide with arrow (HIDDEN) override */
        .two-column-arrow-divider.arrow-divider-sm-hide > div:first-child {
            border: none;
            padding: 0;
            display: flex;
        }

            .two-column-arrow-divider.arrow-divider-sm-hide > div:first-child:before {
               display: none;
            }

        .two-column-arrow-divider.arrow-divider-sm-hide > div:last-child {
            padding: 0;
        }
        /* end column divide with arrow (HIDDEN) override */
    }
/* END column divide with arrow override */
/* #endregionVoLTE overrides */

/* AsideNav css */
#magic-line {
    position: absolute;
    width: 4px;
    background-color: #00549A;
    height: 52px;
    left: 0;
    top: 0;
}

.scrollable-area .subnav-scroll {
    width: 100% !important;
}

.subnav-wrap {
    /*border-left: 1px solid #e1e1e1;*/
    padding-right: 30px;
}

.subnavgroup .subnav_active a {
    color: #111;
}

.subnavgroup li a {
    padding-top: 9px;
    padding-bottom: 11px;
    display: block;
}
/* END of AsideNav css */

/* START Button filters */

.hide-filter {
    position: absolute;
    left: -9999px;
}

.filters {
  text-align: center;
}

.filters ul, .filters-pill ul {
    white-space: nowrap;
    list-style-type: none;
}

.filters li, .filters-pill li {
    display: inline-block;
    padding-bottom: 15px;
}

.filters label {
    padding: 0.5rem 1rem;
    margin-bottom: 0.25rem;
    border-radius: 2rem;
    min-width: 50px;
    line-height: normal;
    cursor: pointer;
    transition: all 0.1s;
}

.filters-pill label {
    padding: 7px 15px;
    font-size: 14px;
    line-height: 16px;
    margin: 7px 10px 5px 0;
    border-radius: 2rem;
    cursor: pointer;
    transition: all 0.1s;
}

/* END Button filters  */

/* START BTC-5100 */
    /* START Button tabs (SmartPhone Page) (BTC-5100) */
    .tablist-pill-Brands{
        white-space: nowrap;
    }
    .tablist-pill-Brands [role=tab]{
        color: #555;
        padding: 7px 15px;
        font-size: 14px;
        line-height: 16px;
        margin: 5px 10px 5px 0;
        border-radius: 2rem;
        cursor: pointer;
        transition: all 0.1s;
        display: block;
    }
    .tablist-pill-Brands [role=tab]:focus,
    .tablist-pill-Brands [role=tab]:hover {
        border-color: #003778;
        outline: none;
    }

    .tablist-pill-Brands [role=tab].active, .tablist-pill-Brands [role=tab][aria-selected]:not([aria-selected=false]) {
        background: #003778;
        color: #fff;
    }
    /* END Button tabs (SmartPhone Page) (BTC-5100) */

/* END BTC-5100 */


/* START Star Rating  */

body.is_tabbing .star-rating > input:focus + label {
    outline: none !important;
    box-shadow: 0 0 0px 3px #f4f4f4, 0 0 2px 3px #f4f4f4, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
    border-radius: 1px;
}

.star-rating label {
    color: #BABEC2;
}

.star-rating > label:before {
    font-size: 20px;
    display: inline-block;
    content: "\2606 ";
    color: #00549A;
}

.star-rating label.blue-star {
    color: #00549A;
}

.star-rating > label.blue-star:before {
    content: "\2605 ";
}

.solid-star {
    display: inline-block;
}

.solid-star:before {
    content: "\2605";
    top: 1px;
    margin-right: 10px;
}
/* END Star Rating  */

/* START Price Range Slider */
.ui-widget-content {
    background-color: #E1E1E1;
}

.ui-slider-horizontal {
    height: .6250em;
}

.ui-slider {
    position: relative;
    text-align: left;
}

.ui-slider-horizontal .ui-slider-range {
    top: 0;
    height: 10px;
}

.ui-slider .ui-slider-range {
    position: absolute;
    z-index: 1;
    font-size: .7em;
    display: block;
    border: 0;
    background-position: 0 0;
}

.ui-widget-header {
    background: #e9e9e9;
    color: #333333;
    font-weight: bold;
}

.ui-slider-horizontal .ui-slider-handle {
    top: -9px;
    margin-left: -.6em;
}

.ui-slider .ui-slider-handle {
    position: absolute;
    z-index: 2;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    box-shadow: 0 6px 25px 0 rgba(0,0,0,0.2);
    cursor: default;
    -ms-touch-action: none;
    touch-action: none;
    cursor: pointer;
}

.ui-state-hover,
.ui-widget-content .ui-state-hover,
.ui-widget-header .ui-state-hover,
.ui-state-focus,
.ui-widget-content .ui-state-focus,
.ui-widget-header .ui-state-focus,
.ui-button:hover,
.ui-button:focus {
    border: none;
}

.ui-corner-all, .ui-corner-bottom, .ui-corner-right, .ui-corner-br {
    border-bottom-right-radius: 15px;
}

.ui-corner-all, .ui-corner-bottom, .ui-corner-left, .ui-corner-bl {
    border-bottom-left-radius: 15px;
}

.ui-corner-all, .ui-corner-top, .ui-corner-right, .ui-corner-tr {
    border-top-right-radius: 15px;
}

.ui-corner-all, .ui-corner-top, .ui-corner-left, .ui-corner-tl {
    border-top-left-radius: 15px;
}

.label-left, .label-right {
    background-color: #ffffff;
    position: absolute;
    bottom: -25px;
    text-align: center;
    min-width: 25px;
}

/* END Price Range Slider */

/** Product Slider (with thumbnail) BTC-6660 **/
.product-slider .slick-arrow,
.product-modal-slider .slick-arrow{
    background: transparent;
    border-color: transparent;
    box-shadow: none;
}

.product-slider .slick-arrow:hover,
.product-slider .slick-arrow:focus,
.product-modal-slider .slick-arrow:hover,
.product-modal-slider .slick-arrow:focus {
    color: #003778;
}

.product-slider .hover-search {
    transition: .5s ease;
    opacity: 0;
    align-items: center;
    justify-content: center;
    margin: 0;
    top: 35%;
    left: 50%;
    transform: translate(-50%);
    position: absolute;
    display: block;
}

.product-slider .slick-track,
.product-modal-slider .slick-track {
    margin: 0;
    padding: 5px 0;
}

.product-slider .slick-list,
.product-modal-slider .slick-list,
.product-thumbnail-nav .slick-list,
.product-thumbnail-modal-nav .slick-list {
    margin: 0 auto;
}

/** END Product Slider (with thumbnail) BTC-6660 **/

/** Keyframes for Accordion Tab Panel **/
@keyframes panelFadeInEffect {
    from {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

@keyframes tabDescriptionFadeInEffect {
    from {
        bottom: -5px;
        opacity: 0
    }

    to {
        bottom: 0;
        opacity: 1
    }
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
    .modal-open .modal-body.scrollAdjust:not(*:root) {
        margin-right: 0px;
        padding-right: 0px;
    }
}
