import * as React from "react";
import { CustomFormattedDateWithoutYear, CustomFormattedDateWithoutYearLong, ExtractedFormattedMessage } from "singleban-components";
import { IChargeItem, IPBE } from "../models";
import { TYPE_NEW_ACTIVATION, TYPE_PRORATION, TYPE_REMOVAL, DEVICE_RETURN_OPTION_FEE, LONG_DISTANCE_NETWORK_CONNECTION, INTERNET_DATA_OVERAGE, HOME_PHONE_PAY_PER_USAGE, HOME_PHONE_LONG_DISTANCE_USAGE, HOME_PHONE_OTHER_CARRIER_USAGE, MOBILITY_ROAMING_USAGE, EQUIPMENT_NON_RETURN_FEE, SMARTPAY_BEGAN, SMARTPAY_EARLY_TERMINATION, MONTHLY_DEVICE_PAYMENT, PBE_TELE_PREMIUM_SPORTS_INSTALLMENTS, PBE_LATE_PAYMENT_CHARGE, MOB_TEXT_MESSAGES, MOB_LOCAL_CALLS, MOB_LONG_DISTANCE, PBE_MOBILITY_EVENTS, MOB_DATA_OVERAGE, PBE_TEMP_SERVICE_SUSPENSION, RTB_PRORATION } from "./Constants";

const moment = require("moment");

export const FormattedFromToDate = (props: { startDate: string | number | Date, endDate: string | number | Date }) => {
    // need to have space in between
    return (
        <>
            <CustomFormattedDateWithoutYear date={props.startDate}>{(date: string) => `${date}`}</CustomFormattedDateWithoutYear> <ExtractedFormattedMessage id="SR_ONLY_TO" /> <CustomFormattedDateWithoutYear date={props.endDate}>{(date: string) => `${date}`}</CustomFormattedDateWithoutYear>
        </>
    );
};

export const FormattedFromToDateSR = (props: { startDate: string | number | Date, endDate: string | number | Date }) => {

    return (
        <>
            <CustomFormattedDateWithoutYearLong date={props.startDate}>{(date: string) => `${date}`}</CustomFormattedDateWithoutYearLong> <ExtractedFormattedMessage id="ARIA_ONLY_TO" /> <CustomFormattedDateWithoutYearLong date={props.endDate}>{(date: string) => `${date}`}</CustomFormattedDateWithoutYearLong>
        </>
    );
};

export const getTitle = (type: string) => {
    let title = "";
    let UpperCaseType = type?.toUpperCase().trim();
    switch (UpperCaseType) {
        case TYPE_NEW_ACTIVATION:
            title = "PAGE_NEW_ACTIVATION_HEADER2";
            break;
        case TYPE_PRORATION:
            title = "PAGE_PRORATION_HEADER2";
            break;
        case TYPE_REMOVAL:
            title = "PAGE_REMOVED_HEADER2";
            break;
        case DEVICE_RETURN_OPTION_FEE:
            title = "DRO_FEE";
            break;
        case LONG_DISTANCE_NETWORK_CONNECTION:
            title = "LD_NETWORK_CONN_FEE";
            break;
        case INTERNET_DATA_OVERAGE:
            title = "INTERNET_USAGE";
            break;
        case EQUIPMENT_NON_RETURN_FEE:
            title = "EQUIPMENT_NON_RETURN_FEE";
            break;
        case HOME_PHONE_PAY_PER_USAGE:
            title = "HP_PAY_PER_USE";
            break;
        case HOME_PHONE_LONG_DISTANCE_USAGE:
            title = "HP_LONG_DISTANCE";
            break;
        case HOME_PHONE_OTHER_CARRIER_USAGE:
            title = "HP_OTHER_CARRIER";
            break;
        case MOBILITY_ROAMING_USAGE:
            title = "MOBILITY_ROAMING_USAGE";
            break;
        case SMARTPAY_BEGAN:
            title = "SMARTPAY_BEGAN";
            break;
        case SMARTPAY_EARLY_TERMINATION:
            title = "SMARTPAY_EARLY_TERMINATION";
            break;
        case MONTHLY_DEVICE_PAYMENT:
            title = "MONTHLY_DEVICE_PAYMENT";
            break;
        case PBE_TELE_PREMIUM_SPORTS_INSTALLMENTS:
            title = "TV_PREMIUM_SPORTS_INSTALLMENTS"
            break;
        case PBE_LATE_PAYMENT_CHARGE:
            title = "LATE_PAYMENT_CHARGE"
            break;
        case MOB_TEXT_MESSAGES:
            title = "MOB_TEXT_MESSAGES";
            break;
        case MOB_LOCAL_CALLS:
            title = "MOB_LOCAL_CALLS";
            break;
        case MOB_LONG_DISTANCE:
            title = "MOB_LONG_DISTANCE";
            break;
        case PBE_MOBILITY_EVENTS:
            title = "MOB_EVENTS"
            break;
        case MOB_DATA_OVERAGE:
            title = "MOB_DATA_OVERAGE"
            break;
        case PBE_TEMP_SERVICE_SUSPENSION:
            title = "TEMP_SERVICE_SUSPENSION"
            break;
        case RTB_PRORATION:
            title = "RTBPRORATION"
            break;
        default:
            title = "";
            break;
    }

    return {
        title
    };
};


export const getDateDifference = (startDate: string, endDate: string) => {
    if (!startDate || !endDate) {
        return 0;
    }
    const begin = moment(startDate);
    const end = moment(endDate);
    const result = moment.duration(Math.abs(end - begin)).asDays();
    const diffInDays = Math.ceil(result);
    return diffInDays;
};

export const getBarWidth = (diffInDays: number | null) => {
    // range from 0 - 100
    if (!diffInDays || diffInDays <= 0) { // special case, negative days
        return 0;
    } else if (diffInDays >= 30) { // special case, days over 30
        return 100;
    }
    const percentage = (diffInDays / 30);
    const width = Math.round(percentage * 100);
    return width;
};

export const getPreviousMonthDate = (startDate: string) => {
    const nextDate = moment(startDate).subtract(1, "month");
    return nextDate;
};

export const getPreviousData = (currentStartDate: string, currentEndDate: string, partialChgCrdItem: IChargeItem | null) => {
    const prevStartDate = getPreviousMonthDate(currentStartDate);
    const prevEndDate = getPreviousMonthDate(currentEndDate);
    const serviceModifyDate = partialChgCrdItem?.startDate.split('T')[0] || "";
    const diffInDays = getDateDifference(prevStartDate, serviceModifyDate);
    const chargedDays = getDateDifference(serviceModifyDate, prevEndDate);
    const width = getBarWidth(diffInDays);

    return {
        currentStartDate,
        currentEndDate,
        prevStartDate,
        prevEndDate,
        serviceModifyDate,
        chargedDays,
        width
    };
};

export const getBillPeriodByBillCloseDate = (billCloseDate: string) => {
    return {
        currentStartDate: billCloseDate,
        currentEndDate: moment(billCloseDate).add(1, "M").subtract(1, "d"),
        prevStartDate: moment(billCloseDate).subtract(1, "M"),
        prevEndDate: moment(billCloseDate).subtract(1, "d")
    };
};

// export const getRecurringChargesItems = (chargeItems: IChargeItem[]) => {
//     let recurringChargesItems: IChargeItem[] = [];

//     chargeItems?.map((item: IChargeItem) => {
//         if (item.chargeIdentifier.includes("Monthly"))
//         {
//             recurringChargesItems.push(item);
//         }
//     });

//     return recurringChargesItems;
// }

export const getGroupedItems = (chargeItems: IChargeItem[], startDate: string) => {
    let currentBillChgItems: IChargeItem[] = [];
    let partialChgCrdItems: IChargeItem[] = [];
    let refundItems: IChargeItem[] = [];
    // const cycleStartDate = moment(startDate);

    chargeItems?.map((item: IChargeItem) => {
        if (item.chargeIdentifier.includes("Monthly")) {
            currentBillChgItems.push(item);
        }
        else if (item.chargeIdentifier.includes("Partial")) {
            if (item.amount < 0) {
                refundItems.push(item);
            }
            else {
                partialChgCrdItems.push(item);
            }
        }
    });
    // chargeItems?.map((item: IChargeItem) => {
    //     let itemStartDate = moment(item.startDate);
    //     if (itemStartDate >= cycleStartDate) {
    //         currentBillChgItems.push(item);
    //     } else {
    //         if (item.amount < 0) {
    //             refundItems.push(item);
    //         } else {
    //             partialChgCrdItems.push(item);
    //         }
    //     }

    // });

    return {
        currentBillChgItems,
        partialChgCrdItems,
        refundItems
    };
};

export const getCharge = (partialItems: IChargeItem[]) => {
    let chargePartial = 0;
    partialItems?.map((item: IChargeItem) => {
        chargePartial += item.amount;
    });
    return chargePartial;
};


export const getLobData = (type: string | null | undefined) => {
    let lobName = "MOBILITY_LOB_NAME";

    const defaultLobData = {
        lobName
    };

    if (!type) {
        return defaultLobData;
    }
    switch (type.toUpperCase()) {
        case "BELL_MOBILITY":
        case "MOBILE":
            lobName = "MOBILITY_LOB_NAME";
            break;
        case "BELL_TV":
        case "TV":
        case "VIRGINTV":
            lobName = "TV_LOB_NAME";
            break;
        case "HOME_PHONE":
        case "HOMEPHONE":
            lobName = "FIXED_LOB_NAME";
            break;
        case "BELL_INTERNET":
        case "INTERNET":
            lobName = "BROADBAND_LOB_NAME";
            break;
        default:
            lobName = type;
    }
    return { lobName };
};

export const DATE_OPTIONS: ReactIntl.IntlComponent.DateTimeFormatProps = {
    year: "numeric",
    month: "short",
    day: "2-digit",
    timeZone: "utc",
};

export const DATE_OPTIONS_WITHOUT_YEAR: ReactIntl.IntlComponent.DateTimeFormatProps = {
    month: "short",
    day: "2-digit",
    timeZone: "utc",
};

export const CURRENCY_OPTIONS: ReactIntl.FormattedNumber.PropsBase = {
    currency: "CAD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
    style: "currency"
};

export const getPBECacheKey = (pbeData: IPBE) => `${pbeData?.pbeId}_${pbeData?.pbeDataBag?.subNo}`;

export const isNullUndefinedOrEmpty = (value: any) => value === "" || value === null || value === undefined;

export const modalOpenedOmniture: Function = (trigger: any, title: string, description: string) => {
    if (!trigger)
        return;
    trigger({
        title,
        description
    });
}

export const debounce = (callback: VoidFunction, msDuration: number) => {
    let timeout: NodeJS.Timeout;
    return () => {
        clearTimeout(timeout);
        timeout = setTimeout(callback, msDuration);
    }
}

export const removeHoursFromDate = (isoDate: string) => {
    const dateWithRemovedHours = new Date(isoDate);
    dateWithRemovedHours.setHours(0, 0, 0, 0);
    return dateWithRemovedHours.toISOString();
}