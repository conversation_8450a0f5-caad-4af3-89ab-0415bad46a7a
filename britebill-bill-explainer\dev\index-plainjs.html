<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>Widget Demo (classic mode)</title>
    <meta charset="utf8">
</head>

<body>
    <div style="text-align:center;">
        <button id="changeLocale">Change page locale to <strong></strong></button>
    </div>

    <br /><br />

    <div id="container"></div>

    <link rel="preload" as="script" data-bwtk-load="1" href="../node_modules/bwtk/dist/polyfill/polyfill.min.js" />
    <link rel="preload" as="script" data-bwtk-load="1" href="../node_modules/prop-types/prop-types.min.js" />
    <link rel="preload" as="script" data-bwtk-load="1" href="../node_modules/react/umd/react.production.min.js" />
    <link rel="preload" as="script" data-bwtk-load="1" href="../node_modules/react-dom/umd/react-dom.production.min.js" />
    <link rel="preload" as="script" data-bwtk-load="1" href="../node_modules/redux/dist/redux.min.js" />
    <link rel="preload" as="script" data-bwtk-load="1" href="../node_modules/react-redux/dist/react-redux.min.js" />
    <link rel="preload" as="script" data-bwtk-load="1" href="../node_modules/redux-actions/dist/redux-actions.min.js" />
    <link rel="preload" as="script" data-bwtk-load="1" href="../node_modules/rxjs/bundles/Rx.min.js" />
    <link rel="preload" as="script" data-bwtk-load="1" href="../node_modules/redux-observable/dist/redux-observable.min.js" />
    <link rel="preload" as="script" data-bwtk-load="1" href="../node_modules/scarlet/dist/scarlet.min.js" />
    <link rel="preload" as="script" data-bwtk-load="1" href="../node_modules/react-intl/dist/react-intl.min.js" />
    <link rel="preload" as="script" data-bwtk-load="1" href="../node_modules/bwtk/dist/inject.min.js" />   
    <link rel="preload" as="script" data-bwtk-load="1" href="../node_modules/bwtk/dist/bwtk.js" />
    
    <script src="../node_modules/bwtk/dist/loader-classic.min.js"></script>
    
    <script>
        BwtkLoader.load(function() {
            var btn = document.getElementById("changeLocale");
            btn.onclick = function() {
                var newLocale = loc.locale === "en" ? "fr" : "en";
                loc.setLocale(newLocale);
                updateLocaleButton(btn, loc);
            };

            bwtk.Init({
                "loader.registryPath": "http://127.0.0.1:8883/dist",
                "localization.webServicesPath": "http://127.0.0.1:8883/localization",
            });

            var loc = bwtk.ServiceLocator.instance.getService(bwtk.CommonServices.Localization);

            bwtk.RenderWidget("britebill-bill-explainer", document.getElementById("container"));

            updateLocaleButton(btn, loc);

            function updateLocaleButton(btn, loc) {
                btn.getElementsByTagName("strong")[0].innerHTML = loc.locale === "en" ? "FR" : "EN";
            }
        });
    </script>
</body>
</html>
