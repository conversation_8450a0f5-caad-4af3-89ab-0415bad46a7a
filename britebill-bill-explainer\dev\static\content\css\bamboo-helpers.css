﻿.no-border-top {
    border-top: none;
}
.no-border-bottom {
    border-bottom: none;
}
.width-200 {
    width: 200px;
}

.width-100 {
    width: 100px;
}

.width-150 {
    width: 150px;
}
.width-70{
    width:70%;
}
.pad-v-22{
    padding-top:22px;
    padding-bottom:22px;
}
.width-fit {
    width: fit-content;
    width: -moz-fit-content;
    justify-content:center;
}
.border-blue-3 {
    border: 3px solid #00549A;
}
.margin-top-3-neg{
    margin-top:-3px;
}
@media(min-width: 320px) and (max-width: 767.98px) {
    .txtLeft-xs {
        text-align: left;
    }
    .col1-xs {
        width: 100%
    }
    .no-border-xs{
        border: none;
    }
    .display-block-xs{
        display: block;
    }
}

.display-block,
.modal-footer.display-block {
    display: block;
}

.hr-lightGray{
    border-color: #d4d4d4;
}
@media(min-width:768px) {
    .text-right-md {
        text-align: right;
    }
}

.bgBlack3 {
    background: #333;
}