﻿/*//v1.1
//BCE custom css
    Last Updated 2019 Aug 1
*/
/* 1. Common Helpers */

/*font weight switch*/
.txtBold.bellSlim,.txtBold.bellSlimBlack,.txtBold.bellSlimHeavy{font-weight:400;}

/*BCE Icon2*/
/*.top-neg-icon .icon2::before{top:-1px!important;left:2px;}*/
.bottom-2{bottom:2px}
.minHeight-80{min-height:80px}
.icon.icon-exapnd-outline-circled.v2:before{top:1px}
a:hover .txtOnlyDecoration_hover, a:focus .txtOnlyDecoration_hover{text-decoration:underline}
.table-icon-container-right{margin:-18px;border:18px solid transparent}
.icon-top-minus-1:before {top:-1px;}
.icon-top-minus-2:before {top:-2px;}
.icon-top-minus-3:before {top:-3px;}

/*BCE only - existing in core*/
.align-center{align-items:center}
.margin-left-auto{ margin-left:auto}
.margin-top-auto{ margin-top:auto}
.radius-10{border-radius:10px}
.txtSize11{font-size:11px}
.txtSize36{font-size:36px}
.threeColumnContainer{margin:0 -7.5px}
.threeColumn-pad-15{padding:7.5px}
.leterSpace-neghalf{letter-spacing: -0.5px;}
.pad-7-left{padding: 0 0 0 7px;}
.pad-7-right{padding: 0 7px 0 0;}
.pad-8-left{padding: 0 0 0 8px;}
.pad-8-right{padding: 0 8px 0 0;}
.txt-4a{color:#4a4a4a;}

/* START custom !important from framework.css START */
.transparent {opacity: 0 !important;}
.bgGray-imp{background-color:#ccc !important}
.bgGray20 {background-color: #babec2 !important}
.valign-middle {vertical-align: middle !important}
.valign-top {vertical-align: top !important}
.absolute-imp{position:absolute !important}
.pos-fixed-top-imp{position:fixed !important;top:0}
.vPadding20-imp{padding:0 20px !important}
.no-pad-imp {padding: 0 !important}
.no-pad-right-imp {padding-right: 0 !important}
.no-pad-top-imp {padding-top: 0 !important}
.no-pad-bottom-imp {padding-bottom: 0 !important}
.pad-7-right-imp {padding-right: 7px !important}
.pad-8-left-imp {padding-left: 8px !important}
.pad-10-left-imp {padding-left: 10px !important}
.pad-10-right-imp {padding-right: 10px !important}
.pad-10-top-imp {padding-top : 10px !important}
.pad-10-bottom-imp {padding-bottom: 10px !important}
.pad-15-left-imp {padding-left: 15px !important}
.pad-15-right-imp {padding-right: 15px !important}
.pad-20-imp {padding: 20px !important}
.pad-25-right-imp {padding-right: 25px !important}
.pad-30-imp {padding: 30px !important;}
.pad-30-right-imp {padding-right: 30px !important}
.pad-30-left-imp {padding-left: 30px !important}
.pad-40-imp {padding: 40px !important}
.focus-default-outline:focus {outline: 5px auto -webkit-focus-ring-color !important;}

/*For Tablet and Mobile*/
@media screen and (max-width: 991.98px) {
    .no-pad-left-imp-sm{padding-left:0!important}
    .pad-15-left-sm-imp {padding-left: 15px !important}
    .pad-15-right-sm-imp {padding-right: 15px !important}
    .pad-30-right-imp-sm{padding-right:30px!important}
}

/*For Tablet only*/
@media (min-width:768px) and (max-width:991.98px) {
    .pad-20-40-sm-imp{padding: 20px 40px !important}
}

/*For Desktop*/
@media (min-width:992px) {
    .pad-30-md-imp {padding:30px !important}
}

/*For larger Desktop*/
@media screen and (min-width:1200px) {
    .d-lg-list-item {display: list-item !important;}
}

/*For mobile only*/
@media screen and (max-width:767.98px) {
    .no-pad-LR-xs-imp {
        padding-left: 0;
        padding-right: 0 !important
    }
    .no-pad-imp-xs {padding: 0 !important}
    .no-pad-right-imp-xs {padding-right: 0 !important}
    .no-pad-left-imp-xs {padding-left: 0 !important}
    .no-pad-top-imp-xs {padding-top: 0 !important}
    .no-pad-bottom-imp-xs {padding-bottom: 0 !important}
    .pad-7-right-imp-xs {padding-right: 7px !important}
    .pad-8-left-imp-xs {padding-left: 8px !important}
    .pad-10-left-imp-xs {padding-left: 10px !important}
    .pad-10-right-imp-xs {padding-right: 10px !important}
    .pad-10-bottom-imp-xs {padding-bottom: 10px !important}
    .pad-15-left-xs-imp {padding-left: 15px !important;}
    .pad-20-imp-xs {padding: 20px !important}
    .pad-25-right-imp-xs {padding-right: 25px !important;}
    .pad-30-imp-xs {padding: 30px !important;}
    .pad-30-right-imp-xs {padding-right: 30px !important;}
    .pad-40-imp-xs {padding: 40px !important}
    .no-margin-xs-imp {margin: 0 !important}
}

/* END custom !important from framework.css END */

/*internet explorer 11 overrides*/
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    .two-column-with-banner-image .container-flex-box-wrap {display: block;}
    .two-column-with-banner-image .imgLogo_cContainer {display: inline-block;margin-bottom:30px;}
    .two-column-with-banner-image .bottom-align-self {position: absolute; bottom: 0;}

     .scrollableContainerShadow-huge-table{position:relative;width:100%;} /* IE10 */
     *::-ms-backdrop, .scrollableContainerShadow-huge-table{position:relative;width:100%;} /* IE11 */
     *::-ms-backdrop, .scrollableContainerShadow-huge-table {position: relative;min-width: 200px;max-width: 1500px;}
     .noBorderRadiusOnIE {border-radius:0!important;}
     .special-table-heading-container .special-table-heading-side { min-width: 44px!important;}
     .special-table-heading-container .special-table-heading-side .table-body-side-row { margin-left:-8px!important;}

     .col-md-6{float:left;}
     .video-gallery .container-flex-box-wrap{display: block;}
     *::-ms-backdrop, .big-table-with-twelve-columns{overflow:visible;}

     /* Special six column table */
    .special-six-column-table {overflow:hidden; border-right:1px solid #d4d4d4;}
     *::-ms-backdrop,  .special-six-column-table {overflow:hidden; border-right:none;}
    .special-six-column-table.-not-whole-scroll .scrollableContainerShadow  {flex-basis:92.5%;}
    .special-six-column-table .scrollableContainerShadow  {flex-basis:100%;}
    /* Special six column table */
}

/*existing in core*/
.max-width-1200 {
    max-width : 1200px;
}
.width-320px {width : 320px;}
.lineHeight-14{line-height:14px}
.lineHeight-26{line-height:26px}
.width-90 {width : 90px;}
.width-140 {width: 140px;}
.height-45 {height: 45px;}
.background-opacity-4-onHover:hover{background-color:rgba(255,255,255,0.3)}
.noBorder{border:none}
.border-gray2{border:1px solid #E1E1E1;}
.box-shadow-gray{box-shadow: 0 6px 25px 0 rgba(0,0,0,0.12);}
.zIndex0{z-index:0}
.zIndex1{z-index:1}
.zIndex-2{z-index:2}
/*paddings*/
.pad-30-right-imp{padding-right:30px!important}
.pad-5-left-imp {padding-left: 5px !important}
/*margins*/
.margin-auto{margin:auto !important}
.negative-margin-12-left{margin-left:-12px}
.margin-neg-15 {margin:0 -15px;}

/* Button */
.btnHoverWhite:hover{color:#fff}
.btn-primary-2,.btn-primary-2:active,.btn-primary-2:focus{color:#003778;background-color:transparent;border:2px solid #003778;font-size:15px;padding:10px 29px;text-align:center;cursor:pointer}
.btn-primary-2:hover,.btn-primary-2:active:focus{color:#00549a;border-color:#00549a;background-color:#ccd7e4}

.disclaimerCheckbox .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element,.graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element.v2{outline-width:2px;outline-style:solid;outline-color:#4d90fe!important;outline-offset:2px}
.disclaimerCheckbox .ctrl_element,.ctrl_element.v2{height:24px;width:24px;top:-1px}
.ctrl_element.v2{top:-9px}
.disclaimerCheckbox .graphical_ctrl_checkbox .ctrl_element:after,.graphical_ctrl_checkbox .ctrl_element.v2:after{top:2px;left:7px;width:8px;height:14px}
.linkArrowRight > .icon{font-size:12px;margin-left:5px;top:-1px;position:relative}

.linkArrowRight:hover > .icon{text-decoration:none}
.bce-no-top:before{top:0}
.bce-top-negative-two:before{top:-2px}

/*For Width auto only*/
.width-auto {width: auto;}

/* Tooltip override width */
.tooltip-md .tooltip {width: 500px !important;}
.tooltip-md .tooltip .tooltip-inner {max-width: 500px;padding: 20px;}

/* 2. Links */
.hoverBold:hover{font-weight:700}
.hoverBorderUnderlineWhite:hover{border-bottom:2px solid #fff}
.bce-hover:hover{text-decoration:underline}
.bce-noHover:hover,.bce-noHover:focus{color:inherit;text-decoration:none}
.hoverBlueWhite:hover,.hoverBlueWhite:focus{color:#f2f4f8}
.underlineLinksBlue a{color: #0056b3; text-decoration:underline}
.containerLink{box-shadow: 0px -1px 8px 2px rgba(0,0,0,0.06);
-webkit-box-shadow: 0px -1px 8px 2px rgba(0,0,0,0.06);
-moz-box-shadow: 0px -1px 8px 2px rgba(0,0,0,0.06);}
a .containerLink:hover {box-shadow: 0 6px 25px 0 rgba(0,0,0,0.12);}

/* 5.Heights and widths*/
.fullWidth{width:100%}
.width-100px {width: 100px;}
.width-min-150px{min-width: 150px;}
.width-200px {width: 200px;}
.width-220px {width : 220px;}
.width-286px {width : 286px;}
.width-min-280px {min-width: 280px;}
.width-min-286px {min-width : 286px;}
.width-149px {width: 149px;}
.width-54perc{width:54%}
.width-50perc{width:50%}
.width-65perc{width:65%}
.width-46perc{width:46%}
.width-35perc{width:35%}

.max-width-200px {max-width: 200px;}
.textContainer{width:calc(100% - 60px)}
.iconContainer2{height:60px;width:60px}
.iconContainer2 .icon3{top:-4px}
.iconContainer3 .icon2:before{top:-1px}
.iconContainer4 .icon2:before{top:-3px}
.iconContainer5 .icon2:before{top:-2px}

/* stock info block */
.stock-info-block{position:absolute;margin-top:-40px;z-index:99}
.stock-info-block .hoverRoundIconCont{display: inline-block}
.stock-info-block .stock-block{width:auto;min-width:330px;box-shadow:0 0 24px 0 rgba(0,0,0,0.28);}
.stock-info-block .stock-block:hover,.stock-info-block .stock-block:focus{box-shadow:0 10px 20px 0 rgba(0,0,0,0.2)}
.stock-info-block .stock-block  .dfLiveInfoCont{display:flex; flex-flow:row}
.stock-info-block .stock-block .stock-icon{display:flex; border-radius:50%;height:42px;width:42px}
.stock-info-block .stock-block .stock-icon span.icon,.stock-info-block .stock-block .stock-icon span.icon2{align-self:center}
.stock-info-block .stock-block .liveInfoTable{margin-left: 15px;}
.stock-info-block .stock-block .liveInfoTable tr:first-child td{border-bottom:1px solid #d4d4d4}
.stock-info-block .stock-block .liveInfoTable tr td:first-child{padding-left:0}
.stock-info-block .stock-block .liveInfoTable tr td:last-child{padding-right:0;text-align: left;}
.stock-info-block .stock-block .liveInfoTable tr td{padding:6px;line-height:1}

.bannerTextContainer {top:-430px;}

/*custom header content*/
.homeCustomBanner{max-height:430px; overflow:hidden;}
.homeCustomContent {
    top: 0px;
    width: 350px;
    text-align-last: center;
}
    .homeCustomContent .full_floatContainer .floatCont_30 {
        overflow:visible;
    }

    .radius-10-top {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
    }

/*small images below the homepage*/
.img-canada-top100{width:60px; height:60.42px!important;}
.img-young-people{width:60px; height:60.42px!important;}
.img-montreal-top-employer{width:60px; height:60.42px!important;}
.img-diversity-employer{width: 66.46px;	height: 55.2px !important;}
.img-greenest-employer{width: 64.65px;	height: 43.2px !important;}

/*Video gallery - News Release - brand assets*/
.pad-45{padding:45px}
.pad-20-b{padding-bottom:20px}
.pad-30-bottom{padding-bottom:30px}
.pad-15-right{padding-right:15px}
.pad-15-left{padding-left:15px}
.insidePar{margin-bottom:30px}
.top-minus-10.icon2:before{top:-10px}
.video-container .icon-gallery_play_solid:before {left: 3px;}

.v2Button{width:auto;font-size:15px}
.simple-blue-header{background-color:#00549A;box-shadow:0 0 50px 0 rgba(0,0,0,0.2)}
  /*Footer Legal Links*/
.site_links_list_wrapper ul.site_links_list_cont li > a{margin-right:7px;margin-left:0}
.site_links_list_wrapper ul.site_links_list_cont li > a:after{content:"";display:inline-block;width:1px;height:16px;vertical-align:middle;background-color:#d4d4d4;margin:0 0 2px 10px}
.site_links_list_wrapper ul.site_links_list_cont li:last-child > a:after{display:none}

/*container link button*/
.button-flex-align-right {display: flex; justify-content:flex-end;}
.containerLink-content{margin-bottom: 0px;}

/*2 column with background color and rounded corners*/
.two-col-rounded-corners .icon-chevron-bold:before{top:-3px;}

@media (min-width: 768px) {
    .stock-info-block .stock-block .dfLiveInfoCont{margin-right: 30px;}
    .pad-15-left-sm-md{padding-left: 15px;}
}

.radius-40{border-radius:40px;}

/* Three column lists */
.three-column-lists > div{margin-bottom:15px;}
.three-column-lists > div:nth-child(3n+1){padding-right:8px;}
.three-column-lists > div:nth-child(3n+2){padding:0 8px;}
.three-column-lists > div:nth-child(3n+3){padding-left:8px;}
ul.simplelist li .extrapad{position:relative;left:10px;}
ul.simplelist li:not(:first-child){padding-top:10px;}
ul.pagination li.text a{padding:6px;}
.accordionContainer li a[aria-expanded=true] span:not(.icon){font-size:14px;color:#111;}
ul.simplelist.expanded div{margin-left:30px;text-indent:-30px;}
ul.simplelist.expanded li{border-bottom:0!important;}
ul.pagination li.active.text a{font-weight:700;text-decoration:underline;background:none!important;color:#00549A;padding:6px;}
ul.pagination li.text a:hover,ul.pagination li.text a:focus{background:none!important;color:#00549A;text-decoration:underline;}
ul.pagination li.text span{color:#00549A!important;padding:6px 2px;}
ul.pagination li.text:first-child{padding-right:10px;}
ul.pagination li.text:last-child{padding-left:10px;}
.pagination-num-wide li:not(:first-child){padding-left:5px;}

/* 6. Image Helpers*/
.image-cropper-container{position:relative;overflow:hidden;height:214px;width:214px;border-radius:107px;background-color:#F4F4F4}
img.rounded{display:inline;height:138%;width:auto;margin:15px 0 0 7px}

/* accordion */

.accordionContainer > ul  > li:not(:last-child){border-bottom:1px solid #D4D4D4;}
.accordionContainer > ul > li .accordionButton.open a[aria-expanded="true"] span:first-child{color:#111}
/* accordion ends */

/* list_with_links */
.list_with_links li:not(:last-child){margin-bottom:10px}
/* list_with_links end*/

/*Relative with Overflow hidden*/
.relative-overflow-hidden {position: relative;overflow: hidden;}

/*For Custom Play button hover effect*/
.iconBlock:hover .icon2.path1:before {opacity:0.6;}
.iconBlock .icon2.path1:before {top: -6px; left:2px; opacity: 0.3; color:#00549A;}
.iconBlock .icon2.path1.txtBlack:before {color:#000;}
.iconBlock .icon2.path2:before {top: -6px;}

.iconBlockBlack:hover .icon2.path1:before {opacity: 0.6;}
.iconBlockBlack .icon2.path1:before {top: -6px;opacity: 0.3;color: #000;}
.iconBlockBlack .icon2.path2:before {top: -6px;left:-2px;}
/*For Custom Play button hover effect*/

/*Rectangle logo with description*/
.rectangle-logo-with-description .rect_imgContainer {
    width:281px;
    height:166px;
}

/*.rectangle-logo-with-description .rect_imgContainer:hover,*/
.rectangle-logo-with-description .rectangle-logo-with-description a :hover {
    box-shadow:0 6px 25px 0 rgba(0,0,0,0.12);
}
/*Rectangle logo with description*/

/*style for the brand assets*/
.dwnl-link-box{position:absolute;bottom:10px;right:10px;border-radius:50%;background:#FFF;color:#FFF;cursor:pointer;text-decoration:none;width:33px;height:33px;padding:0;box-shadow:0 2px 8px 0 rgba(0,0,0,0.2);display:flex;justify-content:center;align-items:center}
.dwnl-link-box:hover,.dwnl-link-box:focus{text-decoration:none;box-shadow:0 2px 8px 0 rgba(0,0,0,0.3)}
.brandIconblock .rect_imgContainer.v2{box-sizing:border-box;border:1px solid #E1E1E1;border-radius:10px}
.brandIconblock .rect_imgContainer.v2 img.imgBrandLogo{height:auto; max-width: 100%}
.brandIconblock .rect_imgContainer.v2 .md-align-Logo{height:100%;width:100%}
.extrapadcmup{padding-top:15px}
.extwide .md-align-Logo.pad-30{
    padding-top: 30px;
    padding-left: 15px;
    padding-right: 15px;
}
.bg-size-cover {
        background-size:cover;
}
.bgpos.center-top-00 {
    background-position:50% 0%
}

@media screen and (max-width:767.98px) {
    .homeCustomBanner {
        height: 200px;
    }
    .brandIconblock{margin-right:-7.5px;margin-left:-7.5px}
    .brandIconblock .imgTxtcontainer{margin-right:7.5px;margin-left:7.5px}
    .brandIconblock .rect_imgContainer.v2{height:118px;width:122px}
}

@media (min-width: 768px) {
    .homeCustomBanner {
        height: 400px;
    }
    .homeCustomContent{
        height:400px;
    }
    .brandIconblock{margin-right:-15px;margin-left:-15px}
    .brandIconblock .imgTxtcontainer{margin-right:15px;margin-left:15px}
}
@media (min-width: 768px) and (max-width: 991.98px) {
.brandIconblock .rect_imgContainer.v2{height:127px;width:133px}
}
@media (min-width: 992px) {
.brandIconblock .rect_imgContainer.v2{height:121px;width:125px}
}

/*For Slick Sliding Cards Section With Summary*/
/*.slidingCardsSectionWithSummary .slidingCardsContainer .slick-slide {width: 389.5px!important;}
.slidingCardsSectionWithSummary .slidingCardsContainer .slick-slide .cardsImage img {max-height: 182px!important;}
.slidingCardsSectionWithSummary .pad-45-right {padding-right: 2px!important;}*/
.slidingCardsSectionWithSummary .cards .cardsContent .summary {
    color: #111;
}
.slidingCardsSectionWithSummary .bgBlue .cardsContent h3,
.slidingCardsSectionWithSummary .bgBlue .cardsContent .summary {color: #fff;}
.slidingCardsSectionWithSummary .bgBlue .cardsContent .txtLightGray {color: #c2cedf;}
.slidingCardsSectionWithSummary .bgBlue .bgBlue {background-color: #00549a; padding:30px;}
/*For Slick Sliding Cards Section With Summary*/

/*Mid Banner with Logo*/
.mid-banner-with-logos .shadowImageLg {height:auto;}
.mid-banner-with-logos .txtContMini {top:0; max-width:530px; width:100%;}
.mid-banner-with-logos .txtContMini h2 {line-height:38px;}
.mid-banner-with-logos .mid-banner-logos .imgLogo_cContainer img {height:70px;}
.mid-banner-with-logos .mid-banner-logos {margin-left: -6px;}
/*Mid Banner with Logo ends*/

/*Floating Absolute Stock Block*/
.floating-stock-block {box-shadow: 0 0 24px 0 rgba(0,0,0,0.28);z-index:100;}
.floating-stock-block .stock-icon span.icon-BCE_Logo::before {top: -2px;left: -1.5px;}

/*Floating Absolute Stock Block ends*/

/*Ordered List with big number padding*/
.ordered-list-with-number-space li {
    padding-left: 16px;
    margin-bottom: 10px;
}
/*Ordered List with big number padding*/

/*Unordered List with big number padding*/
ul.unordered-list-with-number-space li:not(.lowpad) {
    padding-left: 16px !important;
    margin-bottom: 10px !important;
    list-style-type: disc;
    border-bottom:none!important;
}

ul.unordered-list-with-number-space li.lowpad {
    padding-left: 2px !important;
    margin-bottom: 10px !important;
    list-style-type: disc;
    border-bottom:none!important;
}
/*Unordered List with big number padding*/

/*Sliding Cards Container*/
.slidingCardsContainer .slick-track {
    display: flex;
}

.slidingCardsContainer .slick-track .slick-slide {
    height:auto;
}

/*Sliding Cards Container*/

@media (min-width: 320px) and (max-width: 767.98px) {
    .image-cropper-container{height:160px;width:160px;border-radius:80.09px}

    /* For Changing orders in mobile*/
    .flex-column-reverse-xs {display: flex;flex-direction: column-reverse;}
}

/* 7. Page specific*/

/* MultiInfoBlock_Container_With_Text_BioImage */
.MultiInfoBlock_Container_With_Text_BioImage_ImgContainer{background: #F4F4F4;height:65px;width:65px;border-radius:50%;overflow:hidden;display:inline-block}
.MultiInfoBlock_Container_With_Text_BioImage_TxtContainer{display:inline-block;width:calc(100% - 65px);padding-left:15px}
/* MultiInfoBlock_Container_With_Text_BioImage ends*/

/*Executive President banner */
img.mid-crop{display:block;width:100%}

/* Responsibility Overview Banner */

/*Brand Visual Library Start*/
.image-container{height:auto;padding-left:7.5px;padding-right:7.5px;margin-bottom:0}
/*Brand Visual Library End*/

/* EditableTableContainer */
.bce-table.EditableTableContainer.five-column th{min-height:60px}
.bce-table.EditableTableContainer.five-column td{min-height:50px}
.bce-table.EditableTableContainer.five-column th, .bce-table.EditableTableContainer.five-column td{padding:10px 20px !important;width:21%}
.bce-table.EditableTableContainer.five-column th:first-child, .bce-table.EditableTableContainer.five-column td:first-child{width:6%}
.bce-table.EditableTableContainer.five-column th:nth-child(2), .bce-table.EditableTableContainer.five-column td:nth-child(2){width:29%}
.bce-table.EditableTableContainer.five-column th:last-child, .bce-table.EditableTableContainer.five-column td:last-child{width:23%}
/* EditableTableContainer ends*/

/* Editable_6ColumnEditableTable_view */
.bce-table.Editable_6ColumnEditableTable_view.six-column th{min-height:70px}
.bce-table.Editable_6ColumnEditableTable_view.six-column td{min-height:50px}
.bce-table.Editable_6ColumnEditableTable_view.six-column th, .bce-table.Editable_6ColumnEditableTable_view.six-column td{padding:10px 20px !important;width:19%}
.bce-table.Editable_6ColumnEditableTable_view.six-column th:last-child, .bce-table.Editable_6ColumnEditableTable_view.six-column td:last-child{width:18%}
.bce-table.Editable_6ColumnEditableTable_view.six-column th:first-child, .bce-table.Editable_6ColumnEditableTable_view.six-column td:first-child{width:6%}
/* Editable_6ColumnEditableTable_view ends*/

/* Custom dropdown */
#custom_dropdown{position:relative;width:320px}
#custom_dropdown [role="listbox"]{padding:0;background:#fff;border:1px solid #D4D4D4;border-top:0;box-shadow:5px 15px 25px 3px rgba(0,0,0,0.2)}
#custom_dropdown [role="option"]{display:block;padding:5px 20px;position:relative;line-height:18px;font-size:15px;margin-bottom:5px}
#custom_dropdown [role="option"]:last-child{margin-bottom:15px}
#custom_dropdown [role="option"].focused,#custom_dropdown [role="option"]:hover{color:#286da7;background:#e1e1e1;border-radius:2px;padding:5px 10px;margin-left:10px;margin-right:10px}
#custom_dropdown [role="option"][aria-selected="true"]::before{content:'âœ“';position:absolute;left:.5em}
#custom_dropdown button { font-size: 14px; }
#custom_dropdown button[aria-disabled="true"]{opacity:.5}
#custom_dropdown_button{border-radius:0;font-size:16px;text-align:left;padding:13px 15px;border:2px solid #D4D4D4;background-color:#fff;width:100%;position:relative;z-index:2}
#custom_dropdown_button::after{font-family:'bell-icon';content:"\e601";font-size:18px;background-color:transparent;right:0;top:0px;padding:11px 9px 10px 0;height:44px;position:absolute;pointer-events:none;color:#00549a;height:41px}
#custom_dropdown_button[aria-expanded="true"]{border:1px solid #D4D4D4;border-bottom:0;box-shadow:7px -10px 20px -5px rgba(0,0,0,0.1)}
#custom_dropdown_list{border-top:0;overflow-y:auto;position:absolute;margin:0;width:inherit;z-index:1}
/* Custom dropdown*/

/*Input UI Autocomplete*/
.ui-autocomplete-container{width:280px;right:0}
.ui-autocomplete-container .ui-widget-content{border:1px solid #f0f0f0;max-height:235px;overflow-y:auto;-webkit-box-shadow: 0px 10px 23px 0px rgba(221,221,221,1);
-moz-box-shadow: 0px 10px 23px 0px rgba(221,221,221,1);
box-shadow: 0px 10px 23px 0px rgba(221,221,221,1);}
.ui-autocomplete-container .ui-autocomplete .ui-menu-item{list-style:none;width:100%;padding-bottom: 6px; padding-top: 6px;margin-bottom: 6px;padding-right:5px;}
.ui-autocomplete-container .ui-autocomplete .ui-menu-item a{color:#595959;}
.ui-autocomplete-container .ui-autocomplete li:hover{background:#e1e1e1;border-radius:2px;}
.ui-autocomplete-container .ui-autocomplete li:hover a{text-decoration:none}

/*Input UI Autocomplete*/

/* Subnav floating*/
.subnav-wrap{border-left:1px solid #e1e1e1;padding-right:30px}
.subnavgroup{padding-left:18px}
.subnavgroup li a{padding-top:9px;padding-bottom:11px;display:block;cursor:pointer}
.subnavgroup li:nth-last-child(2) a{padding-bottom:0}
.subnavgroup .subnav_active a,.subnavgroup li a:hover{color:#111}
#magic-line{position:absolute;width:4px;background-color:#00549A;height:36px;left:0;top:0}
.total-elected-amount + span{position:absolute;top:50%;transform:translateY(-50%);left:15px;margin-right: 70px}
.total-elected-amount{color:#aaa; padding-left: 60px;}
.total-elected-amount:placeholder-shown{padding-left: 60px;}

/* Subnav floating*/
.threeColumn_altColor_table_w_link td p:last-child {margin-bottom: 0;}

/*Contact us header*/
.contact-us-container-main-header-image {height:118px;width:118px;overflow:hidden;display:inline-block}
.contact-us-container-main-header-content{display:inline-block;width:calc(100% - 118px);}
/*Contact us header*/

/*Blue Background Gradient*/
.bgBlueGradient {background: radial-gradient(circle, #0F92FF 0%, #00549A 100%);background-size: cover;}
/*Blue Background Gradient*/

/*Center-button-with-line-fluid*/
.center-button-with-line-fluid-container .center-button-with-line-fluid {flex-wrap:nowrap;overflow:hidden;}
.center-button-with-line-fluid-container .center-button-with-line-fluid .gray-line {height: 1px;width:500px;}
.center-button-with-line-fluid-container .center-button-with-line-fluid .button-holder {margin: 0 15px;max-width: 100%;white-space: nowrap;}
/*Center-button-with-line-fluid*/

/* News Release Block */
.news-release-block .news-release-block-body .icon2:before {top: -1px;}
.news-release-block .news-release-block-body .news-release-block-body-right .nrbd-box-1,.news-release-block .news-release-block-body .news-release-block-body-right .nrbd-box-2{flex:0 0 50%;max-width:50%;position:relative;width:100%;}
.news-release-block .news-release-block-body .news-release-block-body-right .nrbd-box-2{text-align:right;}

@media (max-width: 767.98px) {
    .news-release-block .news-release-block-body .news-release-block-body-right .nrbd-box-1,.news-release-block .news-release-block-body .news-release-block-body-right .nrbd-box-2{flex:none;max-width:none;position:relative;width:100%;}
    .news-release-block .news-release-block-body .news-release-block-body-right .nrbd-box-2{position:absolute;bottom:0;left:0;text-align:left;margin-left:30px;}
    .news-release-block .spacer-removed-mobile{display:none;}
    .stock-info-block .hoverRoundIconCont{display:block;}
    .stock-info-block{width:100%;box-shadow:0 0 24px 0 rgba(0,0,0,0.28);margin-top:-6px;}
    .stock-info-block .stock-block .liveInfoLogo{flex:0.2;min-width:55px;max-width:65px;justify-content:start;}
    .stock-info-block .stock-block .liveInfoTable{flex:1;width:80%;}
    .stock-info-block .stock-block .liveInfoTable tr td:last-child{text-align:right;}
    .slidingCardsSection.slidingCardsSectionWithSummary{
        padding-top:30px;
    }
}

@media (max-width: 767.98px) and (-ms-high-contrast: none), (-ms-high-contrast: active) {
.stock-info-block .stock-block .dfLiveInfoCont{justify-content:space-around;}
.stock-info-block .stock-block .liveInfoTable{width:80%;}

}

/* News Release Block */

/*Image and Video*/
/* SPACING FIX */

.four-column-image-container-group .four-column-image-container {margin-bottom: 15px;}
.three-column-blue-box-container:hover { cursor: pointer; }
.three-column-blue-box-container h3 { letter-spacing: normal; }

@media (min-width: 320px) and (max-width:767.98px) {
    .four-column-image-container-group .four-column-image-container:nth-child(2n+1){padding-right:10px}
    .four-column-image-container-group > .four-column-image-container:first-child,.four-column-image-container-group > .four-column-image-container:nth-of-type(2n+1){padding-left:0}
    .four-column-image-container-group .four-column-image-container:nth-child(2n-1){padding-left:5px;padding-right:5px}
    .four-column-image-container-group .four-column-image-container:nth-child(2n){padding-left:10px}
    .four-column-image-container-group > .four-column-image-container:nth-of-type(2n){padding-right:0}
    .modal .modal-dialog-image { width: auto;}
    .modal .modal-dialog-image .modal-dialog-image-content { width: auto; margin: 0 auto;}
}

@media (min-width: 768px) and (max-width: 991.98px) {
    .four-column-image-container-group .four-column-image-container:nth-child(3n+1){padding-right:10px}
    .four-column-image-container-group > .four-column-image-container:first-child,.four-column-image-container-group > .four-column-image-container:nth-of-type(3n+1){padding-left:0}
    .four-column-image-container-group .four-column-image-container:nth-child(3n-1){padding-left:5px;padding-right:5px}
    .four-column-image-container-group .four-column-image-container:nth-child(3n){padding-left:10px}
    .four-column-image-container-group > .four-column-image-container:nth-of-type(3n){padding-right:0}

    .three-column-blue-box-container .three-column-blue-box:nth-child(2n+1){padding-right:10px}
    .three-column-blue-box-container > .three-column-blue-box:first-child,.three-column-blue-box-container > .three-column-blue-box:nth-of-type(2n+1){padding-left:0}
    .three-column-blue-box-container .three-column-blue-box:nth-child(2n-1){padding-left:5px;padding-right:5px}
    .three-column-blue-box-container .three-column-blue-box:nth-child(2n){padding-left:10px}
    .three-column-blue-box-container > .three-column-blue-box:nth-of-type(2n){padding-right:0}
    .modal-dialog-image { width: auto;max-width: 100%;}
}

/*Desktop*/
@media (min-width: 992px) {
    .four-column-image-container-group > .four-column-image-container:first-child,.four-column-image-container-group > .four-column-image-container:nth-of-type(4n+1){padding-left:0}
    .four-column-image-container-group .four-column-image-container:nth-child(4n+1){padding-right:15px}
    .four-column-image-container-group .four-column-image-container:nth-child(4n-2){padding-left:5px;padding-right:10px}
    .four-column-image-container-group .four-column-image-container:nth-child(4n-1){padding-left:10px;padding-right:5px}
    .four-column-image-container-group .four-column-image-container:nth-child(4n){padding-left:15px;padding-right:0}
    .modal-dialog-image { width: auto;max-width: 100%;}
    .three-column-blue-box-container .three-column-blue-box:nth-child(3n+1){padding-right:10px}
    .three-column-blue-box-container > .three-column-blue-box:first-child,.three-column-blue-box-container > .three-column-blue-box:nth-of-type(3n+1){padding-left:0}
    .three-column-blue-box-container .three-column-blue-box:nth-child(3n-1){padding-left:5px;padding-right:5px}
    .three-column-blue-box-container .three-column-blue-box:nth-child(3n){padding-left:10px}
    .three-column-blue-box-container > .three-column-blue-box:nth-of-type(3n){padding-right:0}

}

 .fcic-box {position: relative;}

.fcic-box .fcic-box-overbox{background-color:rgba(0,0,0,0.5);height:100%;left:0;opacity:0;position:absolute;top:0;-webkit-transition:all 300ms ease-out;-moz-transition:all 300ms ease-out;-o-transition:all 300ms ease-out;-ms-transition:all 300ms ease-out;transition:all 300ms ease-out;width:100%;text-align:center;}
.fcic-box:hover .fcic-box-overbox{opacity:1;cursor:pointer;}
.fcic-box-outer-div{position:relative;float:left;top:50%;left:50%;transform:translate(-50%,-50%);background-color:rgba(0,0,0,0.5);width:75px;height:75px;border-radius:50%;margin:auto;}
.fcic-box-inner-div{position:relative;top:50%;transform:translateY(-50%);}
.video-container{position:relative;}
.fcic-box-link-div{position:absolute;bottom:15px;right:15px;border-radius:50%;background:#FFF;color:#FFF;cursor:pointer;text-decoration:none;width:33px;height:33px;padding:0; 	box-shadow: 0 2px 8px 0 rgba(0,0,0,0.2);}
.fcic-box-link-div a{position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);}
#black_overlay{display:block;position:absolute;top:0;left:0;width:100%;height:100%;background-color:#000;z-index:1001;-moz-opacity:0.8;opacity:.80;filter:alpha(opacity=80);}
#image{position:relative;border:16px solid #FFF;z-index:1002;}
.image-gallery-image-holder {position:relative;}
.image-gallery-description {padding:18px 0 0 0;}
.image-gallery-description .date {margin-bottom:5px}

/*Image and Video*/
/* CUSTOM POPUP */
.modal-dialog-image, .modal-dialog-video {
    display: inline-flex;
}
.modal-dialog-video{width:70%;max-width:100%;}
.modal-dialog-image-content{background:transparent;}
.modal-dialog-video-content{box-shadow:none;background:transparent;}
.modal-dialog-image .modal-dialog-image-content .modal-dialog-image-close{border:0;background-color:transparent;position:absolute;right:-30px;top:-90px;padding:30px;}
.modal-dialog-video .modal-dialog-video-content .modal-dialog-video-close{border:0;background-color:transparent;position:absolute;right:2px;top:-50px;padding:30px;}
/*.modal-dialog-image .modal-dialog-image-content .modal-dialog-image-close:focus-within,.modal-dialog-video .modal-dialog-video-content .modal-dialog-video-close:focus-within{border:1px solid #fff;}*/

/* CUSTOM POPUP */

.img-container.resp-v2{width:100%;height:180px;background-repeat:no-repeat;background-size:cover;background-position:center;}
/*Desktop*/
@media (min-width: 540px) {

.team-member-container-text-link-image .img-container,.team-member-container-main-header .img-container:not(.resp-v2){min-height:180px;max-height:180px;overflow-y:hidden;}
.team-member-container-text-link-image .img-container img,.team-member-container-main-header .img-container:not(.resp-v2) img{width:100%;min-height:180px;}

}

@media (max-width: 540px) {
.team-member-container-text-link-image .img-container .img-responsive:not(.resp-v2),.team-member-container-main-header .img-container .img-responsive:not(.resp-v2){position:absolute;right:-30px;}
}

/*Desktop*/
@media (min-width: 992px) {
    .executive-president.height-250{min-height:250px;height:auto}
    img.mid-crop{max-width:500px}
    img.mid-crop.mtop-tup-neg-35{margin:-35px 0}
    img.mid-crop.mtop-tup-neg-20-lg{margin:-20px 0}
}
/*Tablet*/
@media (min-width: 768px) and (max-width: 991.98px){
    .executive-president.height-250{min-height:250px;height:auto}
    img.mid-crop{max-width:500px}
    img.mid-crop.mtop-tup-neg-35{margin:-35px 0}
    img.mid-crop.mtop-tup-neg-35-sm{margin:-35px 0}
    .max-width-150-sm{max-width:150px}
    .pad-15-left-sm{padding-left:15px}
    .pad-15-right-sm{padding-right:15px}
    .pad-40-top-sm{padding-top:40px}
    .pad-40-bottom-sm{padding-bottom:40px}
    .spacer40-sm{height:40px}
    .spacer45-sm{height:45px}

    .search-filter-section .search-filter-results .right-content-container {
        display: flex;
        flex-direction: column-reverse;
    }

    /*Input UI Autocomplete*/
    .ui-autocomplete-container{width:230px;left:135px}

}
/*mobile only*/
@media (min-width: 320px) and (max-width:767.98px) {
    .txtBold-xs{font-weight:bold;}
    .no-pad-xs{padding: 0;}
    .modal-dialog-image{padding:30px;}
    .search-filter-section .form-group-label .v4-modified,.search-filter-section .filter-drop-down .form-control-select-box{width:100%!important;}
    .executive-president.height-250{min-height:250px;height:auto;}
    img.mid-crop{max-width:400px;}
    img.mid-crop.mtop-neg-xs{margin:-5px auto -15px;}

    .search-filter-section .search-filter-results .right-content-container {
        display: flex;
        flex-direction: column-reverse;
    }

/*iPhone compatibility of auto-complete*/
    .form-input-text-holder .ui-autocomplete .ui-menu-item {
        font-size: 16px;
    }

/*custom header banner*/
    .homeCustomBanner{max-height: 200px; overflow:hidden;}
    .homeCustomBanner img {max-width: 130%;}
    .homeCustomContent {
        margin-top: -10%;
        width: 100%;
    }
    /*mobile version of Rectangle logo with description */
    .rectangle-logo-with-description {
        flex-direction: column;
        align-items: center;
        margin-bottom: 20px
    }
.rectangle-logo-with-description .rect_imgContainer {margin-right:0;margin-bottom:20px;width:100%;height: 130px;}

 /*mobile version of Rectangle logo with description */

/*mobile for team members */

.team-member-container-main-header h2{font-size:14px;line-height:18px;}
.neg-30lR-xs{margin-left:-30px;margin-right:-30px;}
.team-member-container-text-link-image .img-container .img-responsive,.team-member-container-main-header .img-container .img-responsive{max-width:initial;}
.contact-us-content .d-none-mobile{display:none;}
.team-member-container-text-link-image .img-container:not(.resp-v2),.team-member-container-main-header .img-container:not(.resp-v2){min-height:180px;background-repeat:no-repeat;background-size:cover;background-position:center top;margin-left:-30px;margin-right:-30px;}

.img-container.resp-v2{
  width:120%;
  margin-left:-10%;}

/*Input UI Autocomplete*/
.ui-autocomplete-container{width:100%}

/*Four column bullet graph*/
.four-column-bullet-graph-container ul {align-items: unset!important;}
.four-column-bullet-graph-legend { align-self: flex-start!important;margin-top:0!important;}
.col-xs-2{width:50%}
.col-xs-4{width:50%; flex:0 0 50%;max-width:50%;}
/*Four column bullet graph*/
.line-height-22px-xs {line-height:22px;}
.line-height-25px-xs {line-height:25px!important;}

  /*For message error*/
 .error-message-container .msg-container p { width: 70%;margin: 0 auto;}

 /*video container download link*/
/*.video-container .fcic-box-link-div{bottom:85px; right:0;}*/

/*container link button*/
.containerLink-content{margin-bottom: 25px;}

}

/*Execs list*/

/*reset padding*/
 .bio_parent{margin-left:-7.5px;margin-right:-7.5px}

.bio_cont{height:auto;min-height:300px;padding-left:7.5px;padding-right:7.5px;margin-bottom:15px}
.imgcont-xs-shrink{width:100%;overflow:hidden}
.img-xs-shrink{width: 100%; height:auto}
a.executive-president:hover .bce-hover, a.executive-president:focus .bce-hover { text-decoration: underline; }
a.bio_cont:hover .bce-hover, a.bio_cont:focus .bce-hover { text-decoration: underline; }

 /*smaller mobile*/
@media  (max-width:574.98px) {
   .bio_cont{min-height:220px;margin-bottom:25px;width:auto;max-width:220px;flex-grow:1;flex:0 0 50%;max-width:50%}
    .imgcont-xs-shrink{min-width:120px;min-height:120px}

}
/*custom breakpoint for images*/
@media (min-width: 575px) and (max-width:767.98px) {
    .bio_cont{min-height:220px;min-width:150px;flex-grow:1;flex:0 0 33.33%;max-width:33.33%}
    .imgcont-xs-shrink{min-width:120px;min-height:120px}

}
@media (min-width: 768px) and (max-width:991.98px) {
   .imgcont-xs-shrink{min-width:215px;min-height:215px}
    /*spacers*/
    .spacer30-sm{height:30px}
}

/*Desktop*/
@media (min-width: 992px){
   .imgcont-xs-shrink{min-width:215px;min-height:215px}
 }

.executive-team-cont{margin-bottom:-15px}
.bioImageContainer img{align-self:flex-start}

/*Execs list*/
/*Exec TABS */
.bce-tablist .active span{border-bottom:4px solid #00549a;padding-bottom:13px;color:#111}
.bce-tablist a:last-child{padding-right:0}
.bce-inpage-navigation .bce-tablist a{display:inline-block}
.bce-inpage-navigation{overflow-x:auto !important}

/*governance*/
.RightdownloadContainer{padding-left:30px}
/*Our strategy cards*/
.card-collapsible-child{margin-bottom:15px}

.bce-card-collapsible[aria-expanded="false"] > span{display:block}

/*InfoBlock_Table_3Column_w_Modal*/
.scrollableContainer{background-color:#f4f4f4}
table.InfoBlock_Table_3Column_w_Modal{border:none}
.InfoBlock_Table_3Column_w_Modal th > div{min-height:80px;height:100%;display:flex;align-items:center;flex-wrap:wrap}
.InfoBlock_Table_3Column_w_Modal th > div > div{width:100%}
.InfoBlock_Table_3Column_w_Modal th > div > div:first-child{height:calc(100% - 36px);display:flex;align-items:center;justify-content:center}
.InfoBlock_Table_3Column_w_Modal th > div > div:last-child{margin-top:10px}
.InfoBlock_Table_3Column_w_Modal thead th{width:14%;min-height:50px;height:100%;padding:0;vertical-align:unset;border:none;display:inline-block}
.InfoBlock_Table_3Column_w_Modal thead th:not(:last-child),.InfoBlock_Table_3Column_w_Modal tbody td:not(:last-child){border-right:1px solid #dee2e6}
.InfoBlock_Table_3Column_w_Modal th:first-child,.InfoBlock_Table_3Column_w_Modal tbody td:first-child{width:44%}
.InfoBlock_Table_3Column_w_Modal tbody td{width:14%;border:none;display:inline-block}
.InfoBlock_Table_3Column_w_Modal tbody tr > td{padding:20px 30px}
.InfoBlock_Table_3Column_w_Modal tbody tr:not(:last-child){border-bottom:1px solid #dee2e6}
.InfoBlock_Table_3Column_w_Modal tbody tr > td:not(:first-child){display:flex;align-items:center;justify-content:center}
.InfoBlock_Table_3Column_w_Modal tbody tr:nth-child(odd){background-color:#fff}
/*InfoBlock_Table_3Column_w_Modal ends*/

/* 3Column_Table_Container_w_captions */
.bce-table thead{border:1px solid #00549a}
.bce-table tbody{border:1px solid #d4d4d4;border-top:none}
.bce-table th:not(:last-child){border-right:1px solid #111!important}
.bce-table.Column_Table_Container_w_captions.three-column th,.bce-table.Column_Table_Container_w_captions.three-column td{width:calc(100% / 3)}
.bce-table.Column_Table_Container_w_captions.four-column th,.bce-table.Column_Table_Container_w_captions.four-column td{width:calc(100% / 4)}
.bce-table.Column_Table_Container_w_captions.five-column th,.bce-table.Column_Table_Container_w_captions.five-column td{width:calc(100% / 5)}
.bce-table.Column_Table_Container_w_captions.three-to-one-column th,.bce-table.Column_Table_Container_w_captions.three-to-one-column td{width: 28%}
.bce-table.Column_Table_Container_w_captions.three-to-one-column th:last-child,.bce-table.Column_Table_Container_w_captions.three-to-one-column td:last-child{width: 16%}
.bce-table.Column_Table_Container_w_captions.three-to-one-column th{min-height:60px}
.bce-table.three-to-one-column th,.bce-table.three-to-one-column td{display:flex;align-items:center;line-height:18px;letter-spacing: normal}
.bce-table th,.bce-table td{font-weight:400;padding:12px 20px!important;border:none!important;display:inline-block}
/*.bce-table tr:last-child td{padding-right:0 !important;}*/
.bce-table th.vPadding24-imp{padding:24px 20px!important;}
.bce-table th.low-pad,.bce-table.low-pad td{font-weight:400;padding:12px 20px!important;border:none!important;display:inline-block}
.bce-table td:not(:last-child){border-right:1px solid #d4d4d4!important}
.bce-table tbody tr:not(:last-child){border-bottom:1px solid #d4d4d4!important}
.bce-table tbody tr:nth-child(odd){background-color:#fff}
.bce-table.Column_Table_Container_w_captions.five-column-auto{background-color:#f4f4f4}
.bce-table.Column_Table_Container_w_captions.five-column-auto th{min-height:60px;}
.bce-table.Column_Table_Container_w_captions.five-column-auto tbody tr:nth-child(odd){background-color:#fff}

.simple_table{width:100%;}
.simple_table thead{border:1px solid #00549a}
.simple_table tbody{border:1px solid #d4d4d4;border-top:none}
.simple_table th,.simple_table td{font-weight:400;padding:12px 20px;border:none;}
.simple_table td{min-height:50px; 	border-right: 1px solid #D4D4D4;}
.simple_table tbody tr:not(:last-child){border-bottom:1px solid #d4d4d4}
.simple_table tbody tr:nth-child(odd), .simple_table tr td.bgwhite{background-color:#fff}
.simple_table tr td span{flex-basis: 100%}
.simple_table .width-25pc{width:25%}
.simple_table .width-50pc{width:50%}

.bce-table tr.nopad-right-overrider td{padding-right: 0!important;}

/* 3Column_Table_Container_w_captions ends */
.modal .bce-modal .bce-modal-close{border:0;background-color:transparent;position:absolute;right:20px;top:15px;}

.bce-card-collapsible[aria-expanded="false"] > span:last-child{display:none}
.bce-card-collapsible[aria-expanded="true"] > span{display:none}
.bce-card-collapsible[aria-expanded="true"] > span:last-child{display:block}
.card-collapsible-child .card-collapsible-child-content{opacity:0;max-height:0;overflow:hidden}
.card-collapsible-child.expanded .card-collapsible-child-content{opacity:1;max-height:1000px;overflow:hidden}
.card-collapsible-child.expanded .card-collapsible-child-close{display:block}
.card-collapsible-child.expanded > div{background-color:#003778}
.card-collapsible-child > div{overflow:hidden}
.card-collapsible-child .card-collapsible-child-close{display:none}

/*Profile*/
.bio-title-container{width:auto;max-width:350px}

/*Brand Logos*/
.rect_imgContainer{height:149px;width:185px;border:1px solid #E1E1E1;border-radius:10px}
.rect_imgContainer .md-Logo{display:flex;align-items:center;justify-content:center;height:100%;width:100%}
.rect_imgContainer .md-Logo img.imgLogo{height:auto;max-height:47px;width:auto;max-width:125px}
.rectangle-logo-with-description .rect_imgContainer .md-Logo img.imgLogo {height:auto;max-height:90px;width:auto;max-width:125px}

.imgLogo_cContainer img.imgLogoC{max-height:130px;border:1px solid #E1E1E1;background-color:#FFF}
/*radius for only circled images*/
.imgLogo_cContainer img.imgLogoC.CircleR{border-radius:50%}
.imgLogo_cContainer img.imgLogoC.hovershadow:hover,.imgLogo_cContainer img.imgLogoC.hovershadow:focus,.imgLogo_cContainer a img.imgLogoC:hover,.imgLogo_cContainer a img.imgLogoC:focus{box-shadow:0 6px 25px 0 rgba(0,0,0,0.12)}

@media screen and (max-width:767.98px) {
.imgLogo_cContainer img.imgLogoC{height:76px}
.imgLogo_cContainer img.imgLogoC.CircleR{height:76px;width:76px}
.imgLogo_cContainer img.imgLogoC.heightlg{height:91px}

.heading-with-description .description h3 {	line-height: 18px;}
.rectangle-logo-with-description {flex-direction:column;align-items:center;margin-bottom:20px}
.rectangle-logo-with-description .rect_imgContainer {margin-right:0;margin-bottom:20px;width:100%;height: 130px;}
.center-button-with-line-fluid-container .center-button-with-line-fluid .button-holder {white-space: normal;}
.center-button-with-line-fluid-container .center-button-with-line-fluid .button-holder button {line-height: 18px;}
.container-no-padding-both-sides {padding-left:0; padding-right:0}
.container-no-padding-both-sides .accordionContainer pad-30-left-right-xs {padding-left:30px; padding-right:30px}
.container-no-padding-both-sides .accordionContainer pad-15 {padding-left:30px; padding-right:30px}
}
@media (min-width: 768px) and (max-width: 991.98px) {
.imgLogo_cContainer img.imgLogoC{height:105px}
.imgLogo_cContainer img.imgLogoC.CircleR{height:105px;width:105px}
.imgLogo_cContainer img.imgLogoC.heightlg{height:110px}
.heading-with-description .description h3 {	line-height: 18px;}
}
@media (min-width: 992px) {
.imgLogo_cContainer img.imgLogoC.heightlg{height:110px}
.imgLogo_cContainer img.imgLogoC.CircleR{height:130px;width:130px}
.heading-with-description .description h3 {	line-height: 25px;}
}

@media screen and (max-width:767.98px) {
    .negmargin-15side{padding-right:22.5px;padding-left:22.5px}
    .logoIconblock .imgTxtcontainer{max-width:121px;margin-right:7.5px;margin-left:7.5px}
    .rect_imgContainer{height:98px;width:122px;border:1px solid #E1E1E1;border-radius:10px}
    .rect_imgContainer .md-Logo img.imgLogo{height:auto;max-height:30px;width:auto;max-width:90px}
    /*extra sizes for logos*/
    .rect_imgContainer .md-Logo img.imgLogo.size35-xs{height:40px;width:auto;max-width:90px}
    .rect_imgContainer .md-Logo img.imgLogo.size40-xs{height:40px;width:auto;max-width:90px}
    .noBorderRadius-xs{border-radius:0}
    .no-pad-left-xs-imp{padding-left:0 !important}
    .no-pad-right-xs-imp{padding-right:0 !important}
    .pagination-num-wide .prev-option .option-arrow{padding:10px}
    .pagination-num-wide .next-option .option-arrow{padding:10px}
    .pagination-num-wide ul.pagination li.active.text a, .pagination-num-wide ul.pagination li.text a{padding:6px 11px}
    .pagination-num-wide .pagination > li > a, .pagination-num-wide .pagination > li > span{padding:6px 11px}
    .pagination-num-wide ul.pagination li.text{padding:0 !important}
    .RightdownloadContainer.no-padleft-xs{padding-left:0px}

    /*For Rectangle with logo description*/
    .rectangle-logo-with-description .rect_imgContainer .md-Logo img.imgLogo {max-height:90px;max-width: 125px;}

    /*For Rectangle with logo description*/

}
@media (min-width:768px)   {
    .logoIconblock .imgTxtcontainer {
        margin-right: 30px;
    }
}

/*Annual report*/
.report-nav{display:flex;flex-flow:row wrap;justify-content:space-between;height:80px}
@media screen and (max-width:767.98px) {
.report-nav{padding-left:0!important;padding-right:0!important}
}

/*Events & Presentation*/
.date-round-container{height:66px;width:66px;border-radius:50%;background-color:#00549A;display:table}
.event-presentation-container{cursor: pointer;box-shadow:0 6px 25px 0 rgba(0,0,0,0.12);border:1px solid #e1e1e1;border-radius:10px}
.event-presentation-container:hover,.event-presentation-container:focus{box-shadow: 0 10px 20px 0 rgba(0,0,0,0.2);}
.date-round-container > div{display:table-cell;vertical-align:middle}
.event-presentation-details > span:first-child:after{content:"";display:inline-block;width:2px;height:20px;vertical-align:middle;background-color:#d4d4d4;margin:0 0 2px 15px}

/* Financial Reports */
.bce-select{padding-top:13px;padding-bottom:12px}

/* 10. Others */
.title_and_link_list li:not(:last-child){margin-bottom:10px}
.title_and_link_list li > div{padding-left:25px}
.title_and_link_list li > div .iconContainer{left:0;position:absolute}
.title_with_link_list li:not(:last-child){margin-bottom:10px}
.title_and_link_list_w_background li:not(:last-child){margin-bottom:10px}

.List_links_icons_compressed li:not(:last-child){margin-bottom:5px}
.List_links_icons_compressed li > div{padding-left:25px}
.List_links_icons_compressed li > div .iconContainer{left:0;position:absolute}

.iconLinkContainer{padding-left:25px;position:relative}
.iconLinkContainer.iconLinkContainer-right-align{padding-left:0}
.iconLinkContainer .iconContainer{left:0;position:absolute}
.iconLinkContainer.iconLinkContainer-right-align .iconContainer{position:relative;left:auto}
.mainNonFloatcontent a{
    text-decoration:underline;
}
/* Twitter Icon */
.icon-twitter:before{left:0;top:9px}

/*Cards Section */
.slidingCardsSection{background-color:#fff}

/*home page and investor slides animation*/
.slidingCardsSection{
    transition: opacity 0.3s ease;
    -webkit-transition: opacity 0.3s ease;
}
.slidingCardsSection:not(.loaded) {
    opacity: 0;
    visibility: hidden;
    height:500px;
}
.slidingCardsSection.loaded  {
    visibility: visible;
    opacity: 1;
    height:initial;
}
.slick-track .slick-slide{
    transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
}

.slidingCardsSection .slick-track .slick-slide{
    min-height:300px;
} 

/*Cards Slider*/
.slidingCardsContainer .slick-slide:not(:last-child) {
    margin-right : 15px;
}

.slidingCardsContainer .slick-slide {
    box-shadow: 0 6px 25px 0 rgba(0,0,0,0.12);
    border-radius: 10px;
    display : flex;
}
.slidingCardsContainer .slick-slide:hover, .slidingCardsContainer .slick-slide:focus{
    box-shadow: 0 10px 20px 0 rgba(0,0,0,0.2);
}

.slidingCardsContainer .slick-list {
    padding: 10px 30px 30px 30px;
    margin: auto -30px;
}
@media (max-width: 639.98px) {
.slidingCardsContainer .slick-dots{margin-top:5px}
}
@media (min-width:640px) and (max-width:991.98px) {
.slidingCardsContainer .slick-dots{margin-top:-10px}
}
@media (min-width:992px) {
.slidingCardsContainer .slick-dots{margin-top:-20px}
}

.slidingCardsContainer .slick-prev:hover,.slidingCardsContainer .slick-next:hover,.slidingCardsContainer .slick-prev:focus,.slidingCardsContainer .slick-next:focus,.slidingCardsContainer .slick-prev,.slidingCardsContainer .slick-next{opacity:1;}
.slidingCardsContainer .slick-prev:hover:before,.slidingCardsContainer .slick-next:hover:before,.slidingCardsContainer .slick-prev:focus:before,.slidingCardsContainer .slick-next:focus:before{opacity:.75;color:#00549A}
.slidingCardsContainer .slick-next.slick-disabled,.slidingCardsContainer .slick-prev.slick-disabled{opacity:0}

.slidingCardsContainer .slick-next,
.slidingCardsContainer .slick-prev {
    height: 100%;
    top: 0;
    border-radius: 0;
    width: 100px;
}

.slidingCardsContainer .slick-next {
    background: linear-gradient(to left, #FFF 50%, transparent 50%), radial-gradient(ellipse at 68% 48%, rgba(0,0,0,0.3), transparent 67%, transparent);
    box-shadow: 20px 0px 0px 0px #fff;
}

.slidingCardsContainer .slick-prev {
    background: linear-gradient(to right, #FFF 50%, transparent 50%), radial-gradient(ellipse at 40% 48%, rgba(0,0,0,0.3), transparent 67%, transparent);
    box-shadow: -20px 0px 0px 0px #fff;
}

.slick-prev:before, .slick-next:before {
    top: 50%;
}

.slidingCardsContainer .slick-prev:before, .slidingCardsContainer .slick-next:before {
    font-size: 27px;
    opacity: 1
}
.slidingCardsContainer .slick-next{right:-50px}
.slidingCardsContainer .slick-prev{left:-50px}
.slidingCardsContainer .slick-slide > div,.slidingCardsContainer .slick-slide .slickSlide,.slidingCardsContainer .slick-slide .slickSlide .cards{height:100%;width:100%;}
.slidingCardsContainer .slick-dots li button{background-color:transparent;border-color:#a4a4a4}
.slidingCardsContainer .slick-dots li button:focus{outline:-webkit-focus-ring-color auto 5px!important;outline:1px solid #2672cb!important;opacity:1}
.slidingCardsContainer .slick-dots li.slick-active button{background-color:#999}

.noCardSlide {
    display : flex;
}

.noCardSlide .slickSlide {
    flex : 1;
}

.noCardSlide.card-column-2 .slickSlide:first-child {
    margin-right : 8px;
}

.noCardSlide.card-column-2 .slickSlide:last-child {
    margin-left : 8px;
}

.noCardSlide.card-column-3 > .slickSlide:nth-child(3n+1) {
    margin-right: 8px;
}

.noCardSlide.card-column-3 > .slickSlide:nth-child(3n+2) {
    margin: 0 8px;
}

.noCardSlide.card-column-3 > .slickSlide:nth-child(3n+3) {
    margin-left: 8px;
}

.slidingCardsContainer .slick-slide[aria-hidden=true] {
    visibility: hidden;
}

/*Cards*/
.cards {
    position: relative;
    height: 100%
}
.cardsWithImage .cardsImage.v2 {
background-position:center;
background-size:cover;
}
.cardsWithImage .cardsImage{background-color:#babec2;height:176px;position:relative;overflow:hidden; border-radius: 8px 8px 0 0;}
.cardsWithImage .cardsImage img{margin:0;position:absolute;top:50%;left:50%;margin-right:-50%;transform:translate(-50%,-50%);max-height:176px;}
.cards .cardsContent{padding:30px}
.cards .cardsContent span{font-size:12px;line-height:18px}
.cards .cardsContent h3{font-size:18px;line-height:22px;font-weight:bold}
.cards.cardsWithImage .cardsContent p{color:#666}

.cardsWithImage.borderGrayLight6{
    border: 1px solid #E1E1E1;
}
@media (max-width: 767.98px) {
    .cardsWithImage .cardsImage{height:120px;}
    .cardsWithImage .cardsImage img{max-height:120px;}

    .cardsWithImage.borderGrayLight6.borderRadiusAll8{
        box-shadow: 0 4px 16px 0 rgba(0,0,0,0.12);
    }
}
.cards a:hover span,
.cards a:hover p,
.cards a:hover i,
.cards a:hover i:before,
.cards a:focus span,
.cards a:focus p,
.cards a:focus i,
.cards a:focus i:before {
    text-decoration: none;
    /* display: inline-block; */
}

.slick-slide a:hover {
    text-decoration: none;
}

.slick-slide a:hover h3 {
    text-decoration: underline;
}

.slidingCardsContainer {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
}

/*Filter Expandle Container*/
.filter-expandable-container .i-icon:hover, .filter-expandable-container .i-icon:focus {
    color: #a1a5a6;
    text-decoration: none
}

.filter-expandable-container .accordion-accessible-toggle[aria-expanded="true"] .show-advance-text {display: none;}
.filter-expandable-container .accordion-accessible-toggle[aria-expanded="true"] .hide-advance-text {display: block;}
.filter-expandable-container .accordion-accessible-toggle[aria-expanded="false"] .hide-advance-text {display: none;}
.filter-expandable-container .accordion-accessible-toggle[aria-expanded="false"] .show-advance-text {display: block;}

/* RightAlignVertical_Social_Links */
.RightAlignVertical_Social_Links .social_icons_wrapper ul.social_icons_cont li .icon_background{background-color:#BABEC2}
.RightAlignVertical_Social_Links .social_icons_wrapper ul.social_icons_cont li a:hover .icon_background,.RightAlignVertical_Social_Links .social_icons_wrapper ul.social_icons_cont li a:focus .icon_background{background-color:#999}
.RightAlignVertical_Social_Links .social_icons_wrapper .tooltip-inner{padding:15px}
.RightAlignVertical_Social_Links .social_icons_wrapper .tooltip.bs-tooltip-left .arrow::before{border-width:10px;border-left-color:#fff;top:50%;transform:translateY(-50%)}
.RightAlignVertical_Social_Links .social_icons_wrapper .tooltip.bs-tooltip-left .arrow{top:0;margin-top:0}
.RightAlignVertical_Social_Links .social_icons_wrapper .tooltip{width:150px;max-width:315px}

/* RightAlignVertical_Social_Links ends*/

/* 5Column_Table_Container_input */

.bce-table.fiveColumn_Table_Container_input{min-width:820px}
.bce-table.fiveColumn_Table_Container_input tbody td:first-child, .bce-table.fiveColumn_Table_Container_input thead th:first-child{width:6%;min-width:50px}
.bce-table.fiveColumn_Table_Container_input tbody td:nth-child(2), .bce-table.fiveColumn_Table_Container_input thead th:nth-child(2){width:29%;min-width:240px}
.bce-table.fiveColumn_Table_Container_input tbody td:nth-child(3), .bce-table.fiveColumn_Table_Container_input thead th:nth-child(3){width:20%;min-width:165px}
.bce-table.fiveColumn_Table_Container_input tbody td:nth-child(4), .bce-table.fiveColumn_Table_Container_input thead th:nth-child(4){width:20%;min-width:175px}
.bce-table.fiveColumn_Table_Container_input tbody td:nth-child(5), .bce-table.fiveColumn_Table_Container_input thead th:nth-child(5){width:20%;min-width:190px}

.form-control.v2{height:44px;width:55px;padding:12px}
.form-control.v3{height:44px;width:70px;padding:12px}
.form-control.v4{height:44px;width:137px;padding:0 12px 0 65px}

.form-control.v4-modified{height:44px;width:137px;padding:12px}

.bce-table.fiveColumn_Table_Container_input .inputContainer:not(:last-child){margin-right:10px}

/* 5Column_Table_Container_input ends*/

/*Search filter section*/
.search-filter-section .form-group-label .v4-modified,
.search-filter-section .filter-drop-down .form-control-select-box {width: 280px;}
.search-filter-section .form-input-text-holder input::placeholder {font-size:14px;}

@media (min-width:768px) and (max-width: 991.98px) {
    .search-filter-section .filter-drop-down {margin-left: 20px;}
}

/*Search filter section ends*/

/*Cards breakpoints*/
@media (max-width: 639.98px) {
    .slidingCardsContainer{padding:0}
    .cards .cardsContent h3{font-size:14px}
    .slidingCardsContainer .slick-list{margin-left:0;overflow:visible}
    .slidingCardsContainer .slick-slide:first-child{margin-left:0}
    .slidingCardsSection .container.liquid-container{padding:30px}
}

a.threeColumn-pad-15>.box-shadow-gray:hover, a.threeColumn-pad-15>.box-shadow-gray:focus{
    box-shadow: 0 10px 20px 0 rgba(0,0,0,0.2);
}
/* 11. Media Queries */

/*Multi line banner text style*/
.multiline-bannertext{
    top:0;
    display:flex;
    align-items:center;
    width:100%;
    max-width:446px;
    z-index: 15;
}

/* Big Cards Clickable */
.bigCards_w_Image:not(.v2){height:360px;margin:0;overflow:hidden;border:1px solid #E1E1E1;text-decoration:none!important}
.bigCards_w_Image:hover,.bigCards_w_Image:focus{box-shadow:0 6px 25px 0 rgba(0,0,0,0.12)}
.bigCards_w_Image:not(.v2) img.simpleCenter{position:absolute;height:100%;width:auto;max-width:100%;min-width:592px}
.bigCards_w_Image:not(.v2) img.simpleCenter{top:50%;left:50%;transform:translate(-50%,-50%)}
.bigCards_w_Image .bottomText{width:100%}

.bigCards_w_Image.v2.cover-bg {background-size: cover !important; }
.bigCards_w_Image.v2 {height:422px;margin:0;overflow:hidden;border:1px solid #E1E1E1;text-decoration:none; }
.bigCards_w_Image.v2 img.simpleCenter{position:absolute;height:auto; min-height:100%;width:100%;}
.bigCards_w_Image .bottomText{width:100%;}
.bigCards_w_Image.v2 .bottomText .subtext_cont{width: 100%;margin: 0 auto; max-width:510px;}

@media (max-width: 767.98px) {
    .bigCards_w_Image.v2 {height:360px;}
    .parentImgCont.size440.bgsize:not([class*="bgpos"]){
        background-position: center;}

}

/*Tablet specific*/
@media (min-width: 768px) and (max-width: 991.98px) {
    .bigCards_w_Image.v2 {height:360px;}
    .bigCards_w_Image.v2 .bottomText{padding-bottom: 50px;}
    .bigCards_w_Image.v2 .bottomText h2{line-height:26px;}
    .bigCards_w_Image.v2 .bottomText .subtext_cont{width: 60%;}
}

/*Desktop*/
@media (min-width: 992px) {
    .bigCards_w_Image.v2 .bottomText h2{line-height:38px;}
    .bigCards_w_Image.v2 .bottomText{padding-bottom: 80px;}
    .bigCards_w_Image.v2 .bottomText .subtext_cont{width: 60%;}
}

/*Greater than Mobile*/
@media (min-width: 768px) {
.bigCards_w_Image{border-radius:10px}
.parentImgCont.size440.home_v2.centered_view_mup{background-position: center; }

.parentImgCont.size440.bgsize{
     background-position: center;}
}
.parentImgCont.size440{height:440px;overflow:hidden}

.parentImgCont.size440.bgsize{background-size:cover !important;}

.parentImgCont.size440.home_v2{background-size:cover; }

/*width based on the image height ratio 440*/
.parentImgCont img.banner-crop-img.size440{min-width:1064px}
.txtSize100{font-size:100px}
.full_floatContainer{display:flex}
.full_floatContainer .floatCont_30 {display: flex;align-items: center; /*overflow:hidden;*/}
.full_floatContainer .floatCont_30 .txtContMini{background-color:#FFF;border:1px solid #E1E1E1}
.playIconhover{color:#86898C;z-index:99999}
.playIconhover:focus,.playIconhover:hover{color:#a5a5a5}
.icnBlock{width:95px;height:95px;background:#fff;border-radius:50%}
.icnBlock span.icon2:before{top:-.27em;left:-.01em}
.threeFcontainer{display:flex;flex-flow:wrap}
.fImagecontainer{display:flex;justify-content:center;align-items:center}
.fImagecontainer .textSideContainer{margin-left:13px}
.threeFcontainer .fImagecontainer.innerCont{flex-shrink:0;width:100%}
.fImagecontainer img.logofsize{max-height:80px;width:auto}

/*custom breakpoint for french pagination xs bootstrap*/
@media (max-width: 576px) {
/*container pad for french pagination*/
    .en-n-fr.container.liquid-container{
        padding-left:15px; padding-right:15px;
    }
    .en-n-fr .pagination-num-wide .pagination > li.PagedList-skipToFirst > a,  .en-n-fr .pagination-num-wide .pagination > li.PagedList-skipToLast > a, .fr .pagination-num-wide .pagination > li.PagedList-ellipses > a ,  .en-n-fr .pagination-num-wide .pagination > li.PagedList-ellipses,  .en-n-fr  .pagination-num-wide ul.pagination li.text.PagedList-skipToLast a{
        padding-left:0px; padding-right:0px;
    }
    .en-n-fr .pagination-num-wide .pagination > li  > a{
        padding-left:5px; padding-right:5px;
    }

/*container pad for french pagination*/
}

.en-n-fr .pagination-num-wide .pagination > li:not(.PagedList-ellipses)  > a{
    min-width: 32px;
    text-align: center;
}
.en-n-fr .pagination-num-wide .pagination > li.active  > a{
        padding-left:5px; padding-right:5px;
}
.pagination-num-wide .pagination > li.PagedList-ellipses > a, .pagination-num-wide .pagination > li.PagedList-ellipses{
     padding-left:0px; padding-right:0px;
}

@media (max-width: 767.98px) {
    @-moz-document url-prefix() {
     #flexible-content{
          flex: 1;
        }
    }
   .full_floatContainer{width:100%;flex-direction:column-reverse}
    .vGalleryBg{position:absolute;overflow:hidden;width:100%;padding-left:30px;padding-right:30px;min-height:129px}
    .vGalleryBg .relBlock{border-top-left-radius:10px;border-top-right-radius:10px;border:1px solid #E1E1E1}
    .vGalleryBg.height129-xs{height:129px}
    .vGalleryBg.height129-xs img.shadowImageLg{width:100%;height:auto;border-top-left-radius:10px;border-top-right-radius:10px;box-shadow:0 6px 25px 0 rgba(0,0,0,0.12)}
    .full_floatContainer .floatCont_30 .txtContMini{background-color:#FFF;border:1px solid #E1E1E1;border-bottom-left-radius:10px;border-bottom-right-radius:10px;box-shadow:0 6px 25px 0 rgba(0,0,0,0.12);width:100%}
    .bigCards_w_Image:not(.v2) img.simpleCenter{width:100vw;height:auto;position:absolute}

    .full_floatContainer .floatCont_70{height:120px}
    .icnBlock{width:65px;height:65px;background:#fff;border-radius:50%}
    .icnBlock span.icon2{font-size:70px}
    .threeFcontainer{display:flex;flex-flow:column}
    .fImagecontainer img.logofsize{max-height:67px;height:100%}
    .threeFcontainer .fImagecontainer:not(:first-child){padding-top:20px;padding-bottom:20px;border-top:1px solid #E1E1E1}
    .threeFcontainer .fImagecontainer:first-child{padding-bottom:20px;padding-top:0}
    .threeFcontainer .fImagecontainer:last-child{padding-top:20px;padding-bottom:0}
/*.threeFcontainer .fImagecontainer .textSideContainer p{font-size:10px}*/

}

/* IE10+ CSS styles go here */
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) and (max-width: 767.98px)  {
   .fImagecontainer{min-height:67px;height:100%;width:100%;flex-basis:auto}
}

@media (min-width: 768px) {
   .full_floatContainer{width:100%; padding-top:0px;}
.full_floatContainer.rightViewSide-sm-up{display:flex;flex-direction:row-reverse}
.full_floatContainer .floatCont_30 .txtContMini.width304-sUp{width:304px}
.full_floatContainer .floatCont_30 .txtContMini.height330-sUp{height:330px}
.full_floatContainer .floatCont_70{flex:1}
.vGalleryBg{position:absolute;overflow:hidden;min-height:390px}
.z9999-Sup{z-index:99999}
.vGalleryBg img.shadowImageLg{height:auto}
.full_floatContainer .floatCont_30 .txtContMini{border-radius:10px;box-shadow:0 6px 25px 0 rgba(0,0,0,0.12)}

    /*For Home video*/
    .full_floatContainer .floatCont_30 .txtContMini.width456-sUp {
        width: 456px;
    }

    .z100-Sup {
        z-index: 100;
    }
}

/*Tablet specific*/
@media (min-width: 768px) and (max-width: 991.98px) {
    .vGalleryBg.height390-sm,.vGalleryBg.height390-sm img.shadowImageLg{height:390px}
    .threeFcontainer .fImagecontainer:not(:first-child){padding-left:30px;padding-right:30px;border-left:1px solid #E1E1E1}
    .threeFcontainer .fImagecontainer:first-child{padding-right:30px;padding-left:0}
    .threeFcontainer .fImagecontainer:last-child{padding-left:30px;padding-right:0}
    .threeFcontainer .fImagecontainer{flex:1;flex-shrink:0;width:100%}
    .threeFcontainer .fImagecontainer.innerCont{flex:1 1 auto;min-height:1px}
    .fImagecontainer img.logofsize{max-height:67px;height:auto}
    /*.threeFcontainer .fImagecontainer .textSideContainer p{font-size:10px}*/
    .homeCustomBanner.height433-sm{max-height:433px;}

    /*For home video*/
    .vGalleryBg.height570-sm, .vGalleryBg.height570-sm img.shadowImageLg {
        height: 570px
    }

    .full_floatContainer .floatCont_30 .txtContMini.height482-sUp {
        max-height: 312px;
    }

    /*Mid Banner with Logo Responsive image*/
    .mid-banner-with-logos .shadowImageLg {
        max-width: 136%;
        transform: translate(-10%, 10px);
        height: 478px;
    }
    /*Mid Banner with Logo Responsive image ends*/

    /*Search filter for input*/
    .search-filter-section .form-group-label .v4-modified {
        width: 230px;
    }
    /*Search filter for input*/

    /*Link-list-with-heading-group-container*/
    .link-list-with-heading-group-container {
        display:flex;
    }

    .link-list-with-heading-group-container .changeOrder {
        order: 4;
        margin-top: -110px;
    }
    /*Link-list-with-heading-group-container*/

    /*Title with color bullet*/
    .title-with-color-bullet .bullet-title { max-width: 120px!important;}
    /*Title with color bullet*/

    /*custom home banner*/
    .homeCustomBanner img {
        max-width: 130%;
    }
        /*small images below the homepage*/
    .img-canada-top100{width:54px; height:54.77px!important;}
    .img-young-people{width:54px; height:54.77px!important;}
    .img-montreal-top-employer{width:54px; height:54.77px!important;}
    .img-diversity-employer{width: 60.24px;	height: 49.68px !important;}
    .img-greenest-employer{width: 58.6px;	height: 38.88px !important;}

    /*For message error*/
    .error-message-container .msg-container p { width: 70%;margin: 0 auto;}

    /*video container download link*/
    /*.video-container .fcic-box-link-div{right:5px;}*/

}

/* IE10+ CSS styles go here */
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) and   (min-width: 768px) and (max-width: 991.98px) {
   .fImagecontainer img.logofsize{max-height:67px;height:auto;width:100%}
}
/*Desktop*/
@media (min-width: 992px) {
    .full_floatContainer{padding-top:45px;}
    .homeCustomContent .full_floatContainer {padding-top: 0px;}
    .vGalleryBg.height420-md,.vGalleryBg.height420-md img.shadowImageLg{height:420px}
    .vGalleryBg.height420-md img.shadowImageLg{width:100vw;height:auto;min-height:440px;bottom:0}
    .threeFcontainer .fImagecontainer:not(:first-child){padding-left:45px;padding-right:45px;border-left:1px solid #E1E1E1}
    .threeFcontainer .fImagecontainer:first-child{padding-right:45px}
    .threeFcontainer .fImagecontainer:last-child{padding-left:45px}
    .threeFcontainer .fImagecontainer{flex:1;flex-shrink:0;width:100%}
    .threeFcontainer .fImagecontainer.innerCont{flex:1 1 auto;min-height:1px}
    .fImagecontainer img.logofsize{max-height:67px;height:auto}
    /*.threeFcontainer .fImagecontainer .textSideContainer p{font-size:10px}*/
    /*For home video*/
    .vGalleryLg.height665-md, .vGalleryLg.height665-md img.shadowImageLg {
        height: 665px
    }

    .full_floatContainer .floatCont_30 .txtContMini.height482-sUp {
        max-height: 520px;
    }

    .homeCustomBanner img.banner-crop-img {
        width: 100%;
    }
}

/* IE10+ CSS styles go here */
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) and  (min-width: 992px) {
    .fImagecontainer img.logofsize{max-height:67px;height:auto;width:100%}
}

/*custom breakpoints for lg banner*/
@media (max-width: 1059.98px) {
    /*class for center cropping on mobile*/
    .parentImgCont img.banner-crop-img.size440.centerView{top:50%;left:50%;transform:translate(-50%,-50%)}
}
@media (min-width: 1060px) {
   .parentImgCont img.banner-crop-img.size440{width:100vw;height:auto;min-height:440px;position:absolute;bottom:0}
}

/*Banner customized till 1200.98px*/
@media (min-width: 320px) and (max-width: 1199.98px) {
    .parentImgCont{width:100%;min-height:300px;margin:0;overflow:hidden}
    .parentImgCont .ent-float-text-banner{top:auto;left:auto;transform:none}
    .parentImgCont img.banner-crop-img{position:absolute;height:100%;width:auto;max-width:100%}
    .parentImgCont.size300{height:300px}
    .parentImgCont.size440{height:440px}
    .parentImgCont.size430{height:430px}
    /*for image ratio resize the image to 300 and find the width*/
    /*width based on the image height ratio 300*/
    .parentImgCont img.banner-crop-img.size300{min-width:1200px}
    .parentImgCont img.banner-crop-img.size430{min-width:1440px}

    /*class for showing right side on mobile and Tablet*/
    .parentImgCont img.banner-crop-img.rightView{right:0}
    /*class for center cropping on mobile*/
    .parentImgCont img.banner-crop-img.centerView:not(.size440){top:50%;left:50%;transform:translate(-50%,-50%)}
    /*class for right cropping on mobile*/
    .parentImgCont img.banner-crop-img.leftView{left:0}
}
@media(min-width:1200px){
    /*width based on the image height ratio 300*/
    .parentImgCont img.banner-crop-img.size300{width:100vw;
    height: auto; min-height:300px }
}

/*custom sliding table*/
@media (max-width: 320.98px) {
    .scrollableContainer > .bce-table.three-column{min-width:535px}
    .scrollableContainer > .bce-table.five-column{min-width:768px}
}
@media (max-width: 639.98px) {
    .scrollableContainer > .bce-table{min-width:640px}
    .scrollableContainer > .bce-table.three-column {min-width:450px}
    .scrollableContainer > .width-1150 {min-width:1150px!important;}
    .scrollableContainer > .width-800 {min-width:800px!important;}

    /* Simple five column table */
    .simple-five-column-table .scrollableContainer > .bce-table {min-width: 900px;}
    /* Simple five column table */

    /* Special six column table */
    .special-six-column-table .scrollableContainer > .bce-table {min-width: 100px; width: 350%;}
    _:-ms-fullscreen, :root .special-six-column-table .scrollableContainer > .bce-table {min-width: 100px; width: 350%; max-width:1500px;} /*For IE*/
    /* Special six column table */

    /*Override Global nav in bce for ellipsis, long text title
    Do not Delete*/
    .global-navigation.bce .connector .connector-active-lob-title .custom-width {max-width: 70%;}
    /*Override Global nav in bce for ellipsis, long text title
    Do not Delete*/

}

@media (min-width: 639.98px) and (max-width:767.98px) {
    /* Special six column table */
    .special-six-column-table .scrollableContainer > .bce-table {min-width: 300px; width: 200%;}
    _:-ms-fullscreen, :root .special-six-column-table .scrollableContainer > .bce-table {min-width: 300px; width: 200%; max-width:1500px;} /*For IE*/
    /* Special six column table */
}

/*Mobile specific*/
@media (max-width: 767.98px) {
     /*Fonts*/
    .txtDefault-xs{font-family:"Helvetica",Arial,sans-serif}
    .max-height-225-xs{max-height:225px}

    .txtSize60-xs{
        font-size:60px;
    }
    .width-54perc-m{width:54%}
    .width-46perc-m{width:46%}
    .executive-team-cont .col-12{margin-top:30px}
    .height-300-xs{height:300px}
    .height-380-xs{height:380px}
    .bgGray19-xs{background-color: #f4f4f4}
    .border-lightGray-bottom-xs {border:none;border-bottom: 1px solid #d4d4d4;}
    .no-border-xs{border:none}
    .negative-margin-12-left-xs{margin-left:-12px}
    .max-width-260px-xs{max-width:260px}
    .min-width-unset-xs{min-width:unset}
    .max-width-unset-xs{max-width:unset}
    .col1-xs{width:100%}

    /*spacers*/
    .spacer5-xs{height:5px}
    .spacer25-xs{height:25px}

    /*padding*/
    .pad-25-bottom-xs-imp{padding-bottom:25px}

    .txtLeft-xs{text-align:left;}

    .column-with-bio:nth-child(2n+1) .executive-team-member{margin-right:11.25px}
    .column-with-bio:nth-child(2n-2) .executive-team-member{margin-left:3.75px;margin-right:7.5px}
    .column-with-bio:nth-child(2n-1) .executive-team-member{margin-left:7.5px;margin-right:3.75px}
    .column-with-bio:nth-child(2n) .executive-team-member{margin-left:11.25px;margin-right:0}
    .executive-team-member .detailsContainer > div{font-size:14px}
    .column-with-bio-cont.container{padding-left:22px}

    .executive-team-cont > .executive-team-member:first-child,.executive-team-cont > .executive-team-member:nth-of-type(2n+1){padding-right:7.5px}
    .executive-team-cont .executive-team-member:nth-child(2n){padding-left:7.5px}
    .RightdownloadContainer{padding-left:0px;}

   .date-round-container{height:56px;width:56px}

    .multiline-bannertext {
        width:85%;
    }

    .multiline-bannertext{
        justify-content:center;
        text-align: center;
        max-width: initial;
    }
    /*.parentImgCont img.banner-crop-img.rightView {
        right: initial;
    }*/

    .d-xs-none {display: none;}
    .d-xs-block {display:block!important;}
    /*For Icon size mobile*/
    .txtSize70-xs {font-size: 70px;}

    /*InfoBlock_Table_3Column_w_Modal*/
    .scrollableContainer{width:100%;overflow:hidden;overflow-x:scroll;position:relative; scrollbar-color:#00549A #a1a5a6; scrollbar-width:thin; scrollbar-highlight-color:#00549A;}
    .scrollableContainerShadow{position:relative}
    .left.scrollableContainerShadow:before{width:46px;-webkit-transition:width .5s;transition:width .5s}
    .scrollableContainerShadow:before{width:0;pointer-events:none;content:"";position:absolute;top:0;bottom:0;left:0;z-index:1;background:-moz-linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);background:-webkit-linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);background:linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);-webkit-transition:width .1s;transition:width .1s}
    .right.scrollableContainerShadow:after{width:46px;-webkit-transition:width .5s;transition:width .5s}
    .scrollableContainerShadow:after{width:0;pointer-events:none;content:"";position:absolute;top:0;bottom:0;right:0;z-index:1;background:-moz-linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);background:-webkit-linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);background:linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);-webkit-transition:width .1s;transition:width .1s}
    .scrollableContainer > table{min-width:580px}
    .scrollableContainer > .width-1150 {min-width:1150px!important;}
    .scrollableContainer > .width-800 {min-width:800px!important;}
    .scrollableContainer::-webkit-scrollbar-track{background-color:#BABEC2}
    .scrollableContainer::-webkit-scrollbar{height:8px;background-color:#F5F5F5}
    .scrollableContainer::-webkit-scrollbar-thumb{background-color:#00549A}

    /* Simple five column table */
    .simple-five-column-table .scrollableContainer > table {min-width: 1000px;}
    /* Simple five column table */

     table.InfoBlock_Table_3Column_w_Modal thead tr th,table.InfoBlock_Table_3Column_w_Modal tbody tr td{padding:15px}
    .InfoBlock_Table_3Column_w_Modal thead th,.InfoBlock_Table_3Column_w_Modal tbody td{width:18%}
    .InfoBlock_Table_3Column_w_Modal thead th:first-child,.InfoBlock_Table_3Column_w_Modal tbody td:first-child{width:28%}
    /*InfoBlock_Table_3Column_w_Modal ends*/
    .card-collapsible-child{height:auto;min-height:230px;}

    /*Events & Presentation*/
    .event-presentation-details > span:first-child:after{content:"";display:none;width:0;height:0;background-color:none}
    .event-presentation-details:after{content:"";display:block;width:100%;height:1px;background-color:#E1E1E1}
    .event-presentation-container{border:none;border-radius:0}

    /* MainContent_TitleWithSubtext */
    .MainContent_TitleWithSubtext .mediaInquiriesContainer > div{margin-bottom:15px}
    /* MainContent_TitleWithSubtext ends*/

    /* RightAlignVertical_Social_Links */
    .RightAlignVertical_Social_Links .social_icons_wrapper ul.social_icons_cont{display:flex}
    .RightAlignVertical_Social_Links .social_icons_wrapper ul.social_icons_cont li{margin-right:10px}
    /* RightAlignVertical_Social_Links ends*/

    #custom_dropdown { width : 100%; }

    .icon-back-to-top:before{top:15px}

    /* Date Picker Start */
    .date-picker{padding-right:15px}
    .date-picker-box.margin-30-right .ui-datepicker-trigger{margin-right:0;margin-top:-15px}
    .ui-datepicker-trigger::after{right:-8px}
    /* Date Picker end */
    .bce-table.Column_Table_Container_w_captions.three-to-one-column{width:100%;min-width:444px}
    .bce-table.Column_Table_Container_w_captions.three-to-one-column th, .bce-table.Column_Table_Container_w_captions.three-to-one-column td{width:25% !important}

    /*Mobile Style of SubNav Floating */
    #magic-line{display:none}
    .subnav-wrap{border:1px solid #D4D4D4;margin-bottom:30px;border-radius:10px;padding-top:25px;padding-right:0}
    .subnavgroup{padding:0 30px}
    .subnavgroup li:nth-last-child(2) a{padding-bottom:11px}
    /*Mobile Style of SubNav Floating */

    .tooltip-modal{padding-right:0!important}
    .tooltip-modal.modal .modal-dialog.modal-md.bell-modal-md{margin:0 auto;width:92%;bottom:unset;display:flex;align-items:center;justify-content:center;position:relative;top:-100%;top:-100%;height:100%}
    .tooltip-modal .modal-content{box-shadow:0 14px 36px 0 rgba(0,0,0,0.3)}
    .bce-table.EditableTableContainer.five-column{min-width:629px}
    .bce-table.Editable_6ColumnEditableTable_view.six-column{min-width:629px}

    /* Three column lists */
    .three-column-lists > div:nth-child(3n+1){padding-right:0;}
    .three-column-lists > div:nth-child(3n+2){padding:0;}
    .three-column-lists > div:nth-child(3n+3){padding-left:0;}

    /* Card Slider */
    .noCardSlide{flex-direction:column;}
    .noCardSlide.card-column-2 .slickSlide{width:100%;flex:none;}
    .noCardSlide.card-column-3 > .slickSlide:nth-child(3n+2){margin:0 0 15px;}
    .slidingCardsContainer .slick-slide[aria-hidden=true]{visibility:inherit;}
    .noCardSlide.card-column-2 .slickSlide:first-child,.noCardSlide.card-column-3 > .slickSlide:nth-child(3n+1){margin-right:0;margin-bottom:15px;}
    .noCardSlide.card-column-2 .slickSlide:last-child,.noCardSlide.card-column-3 > .slickSlide:nth-child(3n+3){margin-left:0;}

    /* accordions */
    .accordionContainer:not(.borderRoundXs){border-radius:0;border-left:none;border-right:none;margin:0 -30px;}
    .accordionContainer:not(.borderRoundXs) ul li{padding:15px 30px}
    .accordionContainer:not(.borderRoundXs) ul li .accordionButton.open a[aria-expanded="true"] span:first-child{font-weight:900}

    /* accordions end */
    .relative-xs{position:relative;}
    .noradius-xs{border-radius:0;}
    .noshadow-xs{box-shadow:0;}
    .width-100-percent-xs{width:100%;}

    /*class for showing right side of the banner on mobile*/
    .parentImgContModified img.banner-crop-img.rightView-xs{right:0}
    .parentImgContModified img.banner-crop-img.rightView-0-xs{top:50%;right:0;transform:translate(0%,-50%)}
    .parentImgContModified img.banner-crop-img.rightView-5-xs{top:50%;right:5%;transform:translate(5%,-50%)}
    .parentImgContModified img.banner-crop-img.rightView-10-xs{top:50%;right:10%;transform:translate(10%,-50%)}
    .parentImgContModified img.banner-crop-img.rightView-15-xs{top:50%;right:15%;transform:translate(15%,-50%)}
    .parentImgContModified img.banner-crop-img.rightView-20-xs{top:50%;right:20%;transform:translate(20%,-50%)}
    .parentImgContModified img.banner-crop-img.rightView-25-xs{top:50%;right:25%;transform:translate(25%,-50%)}
    .parentImgContModified img.banner-crop-img.rightView-30-xs{top:50%;right:50%;max-width:140%;transform:translate(30%,0%)}
    .parentImgContModified img.banner-crop-img.rightView-35-xs{top:50%;right:35%;transform:translate(35%,-50%)}
    .parentImgContModified img.banner-crop-img.rightView-40-xs{top:50%;right:40%;transform:translate(40%,-50%)}
    .parentImgContModified img.banner-crop-img.rightView-45-xs{top:50%;right:45%;transform:translate(45%,-50%)}
    .parentImgContModified img.banner-crop-img.rightView-50-xs{top:50%;right:50%;transform:translate(50%,-50%)}

    .four-column-bullet-graph-container .title {text-align:left;}
    .four-column-bullet-graph-container.pad-25 {padding: 25px 25px 25px 0;}
}

/*CUSTOM FOR MOBILE SPECIFIC MID BANNER IMAGE*/
/*Mid Banner with Logo Responsive image*/

@media (min-width:460px) and (max-width:767.98px) {
    .mid-banner-with-logos {display:flex;flex-direction:column-reverse;}
    .mid-banner-with-logos .txtContMini {position:static!important;}
    .mid-banner-with-logos .shadowImageLg {height: 500px;transform: translate(-39%, 10px);}
    .mid-banner-with-logos .mid-banner-logos .imgLogo_cContainer img {height: 50px;}
    .mid-banner-with-logos .txtContMini h2 {line-height: 26px; max-width: 60%;}
    .mid-banner-with-logos .mid-banner-logos .imgLogo_cContainer img {height: 50px;}
    .mid-banner-with-logos .txtContMini h2 {line-height: 26px;}

}

@media screen and (max-width:459.98px) {

    .mid-banner-with-logos {display:flex;flex-direction:column-reverse;}
    .mid-banner-with-logos .txtContMini {position:static!important;}
    .mid-banner-with-logos .shadowImageLg {height: 324px;transform: translate(-41%, 10px)}
    .mid-banner-with-logos .mid-banner-logos .imgLogo_cContainer img {height: 40px;}
    .mid-banner-with-logos .txtContMini {max-width: 100%;}
    .mid-banner-with-logos .txtContMini h2 {line-height: 26px;max-width:80%;}
    .col-xs-2{width:50%}
    .col-xs-4{width:50%; flex:0 0 50%;max-width:50%;}

}

/*Mid Banner with Logo Responsive image*/
/*CUSTOM FOR MOBILE SPECIFIC MID BANNER IMAGE*/

/*home page invest*/
.invest-info-block .icon2.icon-Latest_annual_documents:before {
    top:-2px;
}

/*home page three col*/
.three-banner-col-con .col-img-head{background-size:cover;background-position:50% 50%;border-radius:10px}
.three-banner-col-con .col-img-head.img-video-btn-cont a.playIconhover_v2{width:100%;height:100%;display:flex;justify-content:center;align-items:center}
.img-mar-neg-7half{margin:0 -7.5px}
.one-img-mid-banner{display:flex;flex-flow:wrap;justify-content: center;}
.one-of-three-side-col .imgLogo_cContainer img{height:auto;width:100%}

.subRectCont > div{width:100%}
.subRectCont.v2 p{line-height:18px}
/*responsibility overview*/
.float-simple-banner{background-size:cover;background-position:50% 50%}
.bg-cover-center{background-size:cover;background-position:50% 50%}
.float-simple-banner.size300{height:300px}

/*Mobile*/
@media (max-width: 767.98px) {
    .invest-list-v1 ul > li{padding-top:15px}

/* three column cont*/
   .three-banner-col-con .one-of-three-side-col:not(:first-child){padding-top:45px}
    .three-banner-col-con .one-of-three-side-col:first-child{padding-top:0}
    .three-banner-col-con .one-of-three-side-col{padding-bottom:0}
    .three-banner-col-con .col-img-head{height:150px;width:auto}
    .three-banner-col-con .col-img-head.img-video-btn-cont .icon-play_hover_multi .icon2.path1,.three-banner-col-con .col-img-head.img-video-btn-cont .icon-play_hover_multi .icon2.path2{font-size:63px}

    .img-mar-neg-var {
            margin: 0 -30px;
    }
    .one-img-mid-banner .imgLogo_cContainer{padding-left:12.5px;padding-right:12.5px}
    .one-img-mid-banner .imgLogo_cContainer{flex-basis:29.5%;max-width:29.5%;}

/*background y axis classes*/
    .bgpos.topView-0-xs{background-position:50% 0}
    .bgpos.midView-0-xs{background-position:50% 50%}
    .bgpos.bottomView-0-xs{background-position:50% 0}

/*responsibility overview*/
    .m-fixHeight-banner{height:460px}
    .float-simple-banner.bgpos{min-height:200px;height:300px;margin-bottom:160px;background-repeat:no-repeat}
    .float-simple-banner .multiline-bannertext.v3{height:auto;width:calc(100% - 30px);max-width:100%;top:auto;bottom:-156px}
    .float-simple-banner .multiline-bannertext.v3 .subRectCont.v2{min-height:188px}
}

/*Tablet specific*/
@media (min-width: 768px) and (max-width: 991.98px) {
   .invest-title-cont{display:flex}
    .invest-title-cont .invest-cont-sub{align-self:center}
    .cont-line-lists{display:flex}
    .line-1-list,.line-2-list{width:50%}
    .line-1-list .invest-list-v1{flex:1}
    .line-2-list .invest-list-v1{flex:1}
    .invest-list-v1 ul > li:not(:first-child){padding-top:20px}
    .invest-list-v1 ul > li{padding-left:10px}

/* three column cont*/
   .three-banner-col-con .row{margin-left:-15px;margin-right:-15px}
    .three-banner-col-con .one-of-three-side-col{padding-left:15px;padding-right:15px}
    .three-banner-col-con .col-img-head{height:150px;width:auto}
    .three-banner-col-con .col-img-head.img-video-btn-cont .icon-play_hover_multi .icon2.path1,.three-banner-col-con .col-img-head.img-video-btn-cont .icon-play_hover_multi .icon2.path2{font-size:63px}
    .img-mar-neg-var {
            margin: 0 -16%;
    }
    .one-img-mid-banner .imgLogo_cContainer{padding-left:10px;padding-right:10px}
    .one-img-mid-banner .imgLogo_cContainer{flex-basis:27%;max-width:27%}

/*responsibility overview*/
    .subRectCont.v2{width:330px;margin-top:0}
}
/*Desktop*/
@media (min-width: 992px) {
    .invest-list-v1 ul{display:flex;padding-left:10px}
    .invest-list-v1 ul > li{display:inline;display:inline-block;zoom:1;flex:1;padding-left:10px}
    .line-1-list .invest-list-v1{padding-top:5px}
    .line-2-list .invest-list-v1{padding-top:10px}
    .invest-list-v1 ul > li{padding-left:10px}

/* three column cont*/
  .three-banner-col-con .row{margin-left:-15px;margin-right:-15px}
    .three-banner-col-con .one-of-three-side-col{padding-left:15px;padding-right:15px}
    .three-banner-col-con .col-img-head{height:195px;width:auto}
    .three-banner-col-con .col-img-head.img-video-btn-cont .icon-play_hover_multi .icon2.path1,.three-banner-col-con .col-img-head.img-video-btn-cont .icon-play_hover_multi .icon2.path2{font-size:82px}
    .img-mar-neg-var {
            margin: 0 -14%;
    }
    .one-img-mid-banner .imgLogo_cContainer{padding-left:12.5px;padding-right:12.5px}
    .one-img-mid-banner .imgLogo_cContainer{flex-basis:28%;max-width:28%}

/*responsibility overview*/
    .subRectCont.v2{margin-top:0}
}

/*Greater than Mobile*/
@media (min-width: 768px) {
   .height-250{height:250px}
   .card-collapsible-child.expanded {z-index: 2}
   .card-collapsible-child.expanded .card-collapsible-child-accordion {bottom: calc((100% - 15px) * -1)}
   .card-collapsible-child.expanded h3 {width: calc(50% - 30px)}
   .card-collapsible-child.expanded .card-collapsible-child-content {max-height: 300px;}

    /* 2MultiInfoBlock_With_Graph */
    .MultiInfoBlock_With_Graph > div:nth-child(odd){padding-right:7.5px}
    .MultiInfoBlock_With_Graph > div:nth-child(even){padding-left:7.5px}
    .MultiInfoBlock_With_Graph > div > div{border-radius:10px;height:100%}
    /* 2MultiInfoBlock_With_Graph Ends */

    /* MainContent_TitleWithSubtext */
    .MainContent_TitleWithSubtext .mediaInquiriesContainer > div:nth-child(odd){padding-right:7.5px}
    .MainContent_TitleWithSubtext .mediaInquiriesContainer > div:nth-child(even){padding-left:7.5px}
    /* MainContent_TitleWithSubtext ends*/

    /* RightAlignVertical_Social_Links */
    .RightAlignVertical_Social_Links{position:relative}
    .RightAlignVertical_Social_Links .social_icons_wrapper{position:static ;z-index:2;left:auto;right:auto}
    .RightAlignVertical_Social_Links .social_icons_wrapper ul.social_icons_cont li{display:block;margin-bottom:10px}
    .RightAlignVertical_Social_Links .social_icons_wrapper{transition:opacity .2s ease-out}
    /* RightAlignVertical_Social_Links ends*/

    .pad-top-10-mup{
            padding-top: 10px;
    }
}

/*For Tablet and Mobile*/
@media screen and (max-width: 991.98px) {
    .column-sm {
        flex-direction: column
    }

    .txtleft-sm-xs{text-align:left; }

    .width-220px-sm{width: 220px}

    .column-with-bio:nth-child(3n+1) .executive-team-member{margin-right:11.25px}
    .column-with-bio:nth-child(3n-2) .executive-team-member{margin-left:3.75px;margin-right:7.5px}
    .column-with-bio:nth-child(3n-1) .executive-team-member{margin-left:7.5px;margin-right:3.75px}
    .column-with-bio:nth-child(3n) .executive-team-member{margin-left:11.25px;margin-right:0}
    .column-with-bio-cont.container{padding-left:26px}

    /*table*/
    .scrollableContainer-sm{width:100%;overflow:hidden;overflow-x:scroll;position:relative; scrollbar-color:#00549A #a1a5a6; scrollbar-width:thin; scrollbar-highlight-color:#00549A;}
    .scrollableContainerShadow-sm{position:relative}
    .left.scrollableContainerShadow-sm:before{width:46px;-webkit-transition:width .5s;transition:width .5s}
    .scrollableContainerShadow-sm:before{width:0;pointer-events:none;content:"";position:absolute;top:0;bottom:0;left:0;z-index:1;background:-moz-linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);background:-webkit-linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);background:linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);-webkit-transition:width .1s;transition:width .1s}
    .right.scrollableContainerShadow-sm:after{width:46px;-webkit-transition:width .5s;transition:width .5s}
    .scrollableContainerShadow-sm:after{width:0;pointer-events:none;content:"";position:absolute;top:0;bottom:0;right:0;z-index:1;background:-moz-linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);background:-webkit-linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);background:linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);-webkit-transition:width .1s;transition:width .1s}
    .scrollableContainer-sm::-webkit-scrollbar-track{background-color:#BABEC2}
    .scrollableContainer-sm::-webkit-scrollbar{height:8px;background-color:#F5F5F5}
    .scrollableContainer-sm::-webkit-scrollbar-thumb{background-color:#00549A}

    /* EditableTableContainer */
   .bce-table.EditableTableContainer.five-column th,.bce-table.EditableTableContainer.five-column td{width:23%!important}
    .bce-table.EditableTableContainer.five-column th:first-child,.bce-table.EditableTableContainer.five-column td:first-child{width:8%!important}
  /* EditableTableContainer ends*/

        /* Editable_6ColumnEditableTable_view */

    .bce-table.Editable_6ColumnEditableTable_view.six-column th:nth-child(4), .bce-table.Editable_6ColumnEditableTable_view.six-column td:nth-child(4){width:18%}
    .bce-table.Editable_6ColumnEditableTable_view.six-column th:nth-child(5), .bce-table.Editable_6ColumnEditableTable_view.six-column td:nth-child(5){width:18%}
    .bce-table.Editable_6ColumnEditableTable_view.six-column th:first-child, .bce-table.Editable_6ColumnEditableTable_view.six-column td:first-child{width:8%}

    /* Editable_6ColumnEditableTable_view ends*/

    /**/
    .bce-table.Column_Table_Container_w_captions.five-column-auto{min-width:589px}
    .slidingCardsContainer .slick-next {right : -30px;}
    .slidingCardsContainer .slick-prev {left: -30px;}
}

/*Tablet specific*/
@media (min-width: 768px) and (max-width: 991.98px) {
    /*New Header*/
    .spacer30-m { height: 30px}

    .executive-team-cont .executive-team-member:nth-child(3n+1){padding-right:10px}
    .executive-team-cont > .executive-team-member:first-child,.executive-team-cont > .executive-team-member:nth-of-type(3n+1){padding-left:0}
    .executive-team-cont .executive-team-member:nth-child(3n-1){padding-left:5px;padding-right:5px}
    .executive-team-cont .executive-team-member:nth-child(3n){padding-left:10px}
    .executive-team-cont > .executive-team-member:nth-of-type(3n){padding-right:0}
    .card-collapsible-cont > .card-collapsible-child:first-child,.card-collapsible-cont > .card-collapsible-child:nth-of-type(2n+1){padding-right:7.5px}
    .card-collapsible-cont .card-collapsible-child:nth-child(2n){padding-left:7.5px}
    .card-collapsible-child.expanded > div {height: calc(200% + 15px);width: calc(200% + 15px);}
    .card-collapsible-child.expanded:nth-of-type(2n) > div {position:relative;left:calc(-100% - 15px)}
    .card-collapsible-child.expanded:nth-of-type(2n) > div .card-collapsible-child-accordion {bottom: 30px}
    .card-collapsible-child.expanded:nth-child(2n+1):nth-last-child(-n+2) > div, .card-collapsible-child:nth-child(2n+1):nth-last-child(-n+2) ~ .card-collapsible-child.expanded > div {position: relative;top: calc(-100% - 15px);}
    .card-collapsible-child.expanded:nth-child(2n+1):nth-last-child(-n+2) > div .card-collapsible-child-accordion, .card-collapsible-child:nth-child(2n+1):nth-last-child(-n+2) ~ .card-collapsible-child.expanded > div .card-collapsible-child-accordion {bottom: 30px}
    .services-profile-cont .services-profile:nth-child(3n+1){padding-right:10px}
    .services-profile-cont > .services-profile:first-child,.services-profile-cont > .services-profile:nth-of-type(3n+1){padding-left:0}
    .services-profile-cont .services-profile:nth-child(3n-1){padding-left:5px;padding-right:5px}
    .services-profile-cont .services-profile:nth-child(3n){padding-left:10px}
    .services-profile-cont > .services-profile:nth-of-type(3n){padding-right:0}
    .RightdownloadContainer.no-padleft-sm{padding-left:0px}

    /*Events & Presentation*/
     .event-presentation-details.more-links-on-right > span:first-child:after{content:"";display:none;width:0;height:0;background-color:none}
    .event-presentation-details.more-links-on-right > span:first-child{display:block;margin-bottom:15px}

    .bce-table.Column_Table_Container_w_captions.three-to-one-column th,.bce-table.Column_Table_Container_w_captions.three-to-one-column td{width: 27%}
    .bce-table.Column_Table_Container_w_captions.three-to-one-column th:last-child,.bce-table.Column_Table_Container_w_captions.three-to-one-column td:last-child{width: 19%}

    .margin-20-left-sm{margin-left: 20px}
    .pad-20-sm{padding:20px}

    .txtSize75-sm{
        font-size:75px;
    }
    .RightAlignVertical_Social_Links .stickyItemWrapper.social_icons_wrapper.pos-fixed-top-imp  ul.social_icons_cont{ margin-top:45px;}
    .RightAlignVertical_Social_Links .social_icons_wrapper ul.social_icons_cont{right:0;position:absolute; margin-top:45px;}

    /*Column table with top label*/
    .column-table-with-top-label .scrollableContainer-sm table {
        min-width:1200px;
    }
    /*Column table with top label*/
}
/*Desktop*/
@media (min-width: 992px) {
    .card-collapsible-cont .card-collapsible-child:nth-child(3n+1){padding-right:10px}
    .card-collapsible-cont > .card-collapsible-child:first-child,.card-collapsible-cont > .card-collapsible-child:nth-of-type(3n+1){padding-left:0}
    .card-collapsible-cont .card-collapsible-child:nth-child(3n-1){padding-left:5px;padding-right:5px}
    .card-collapsible-cont .card-collapsible-child:nth-child(3n){padding-left:10px}
    .card-collapsible-cont > .card-collapsible-child:nth-of-type(3n){padding-right:0}
    .card-collapsible-child.expanded > div {height: calc(200% + 15px);width: calc(200% + 15px);}
    .card-collapsible-child.expanded:nth-of-type(3n) > div {position:relative;left:calc(-100% - 15px)}
    .card-collapsible-child.expanded:nth-of-type(3n) > div .card-collapsible-child-accordion {bottom: 30px}
    .card-collapsible-child.expanded:nth-child(3n+1):nth-last-child(-n+3) > div, .card-collapsible-child:nth-child(3n+1):nth-last-child(-n+3) ~ .card-collapsible-child.expanded > div {position: relative;top: calc(-100% - 15px);}
    .card-collapsible-child.expanded:nth-child(3n+1):nth-last-child(-n+3) > div .card-collapsible-child-accordion, .card-collapsible-child:nth-child(3n+1):nth-last-child(-n+3) ~ .card-collapsible-child.expanded > div .card-collapsible-child-accordion {bottom: 30px}
    .services-profile-cont > .services-profile:first-child,.services-profile-cont > .services-profile:nth-of-type(4n+1){padding-left:0}
    .services-profile-cont .services-profile:nth-child(4n+1){padding-right:10px}
    .services-profile-cont .services-profile:nth-child(4n-2){padding-left:5px;padding-right:5px}
    .services-profile-cont .services-profile:nth-child(4n-1){padding-left:5px;padding-right:5px}
    .services-profile-cont .services-profile:nth-child(4n){padding-left:10px;padding-right:0}

    .linkArrowRight{margin-bottom:5px}
    .services-profile-cont .services-profile:nth-child(4n){padding-left:10px;padding-right:0}
    .column-with-bio:first-child > .executive-team-member,.column-with-bio:nth-of-type(4n+1) > .executive-team-member{margin-left:0}
    .column-with-bio:nth-child(4n+1) .executive-team-member{margin-right:11.25px}
    .column-with-bio:nth-child(4n-2) .executive-team-member{margin-left:3.75px;margin-right:7.5px}
    .column-with-bio:nth-child(4n-1) .executive-team-member{margin-left:7.5px;margin-right:3.75px}
    .column-with-bio:nth-child(4n) .executive-team-member{margin-left:11.25px;margin-right:0}

    .bce-table.Column_Table_Container_w_captions.three-to-one-column th:last-child {padding: 10px 15px !important}

    .mid-banner-with-logos .shadowImageLg {
        max-width: 100%;
    }
    .RightAlignVertical_Social_Links .social_icons_wrapper ul.social_icons_cont{right:0;position:absolute; margin-top:30px;}

}

.card-collapsible-child .card-collapsible-child-accordion, .card-collapsible-child .card-collapsible-child-content{transition:opacity .3s ease-in-out}
/*table custom for header */
.bce-table tbody tr.table-header {
    border-bottom: 5px solid #d4d4d4!important;
}

/* For Custom Profile Form */
/*Tablet only*/
@media (min-width: 768px) and (max-width: 991.98px) {
    /*containers*/
    .width-96-sm {width: 96px;}
    .width-120-sm {width: 120px;}
    .width-280-sm {width: 280px;}
    .width-312-sm {width: 312px;}
    .width-206-sm {width: 206px;}
    .width-324-sm {	width: 324px;}
    .width-430-sm {	width: 430px;}

    .supplier-form .middle-align-self-sm {align-self:center;}

}
/*Desktop only*/
@media (min-width: 992px) {
    .width-96-md {width: 96px;}
    .width-126-md {width: 126px;}
    .width-280-md {width: 280px;}
    .width-312-md {width: 312px;}
    .width-203-md {width: 203px;}
    .width-358-md {width: 358px;}
    .width-430-md {width: 430px;}
    .max-width-580-md {max-width: 580px;}
}

@media (max-width: 991.98px) {
    .supplier-form .graphical_ctrl_checkbox .ctrl_element:after {
        left: 7px;
        top: 3px;
        width: 8px;
        height: 14px;
    }
}

@media screen and (max-width: 767.98px) {

    .supplier-form .middle-align-self-sm {align-self:center;}
    /*containers for suppliers page only with check box*/
    .col-xs-12 {flex:0 0 100%; max-width:100%;}
    .col-xs-2-with-fixed-width {flex: 0 0 20%;max-width: 80px;margin-right: 20px;}
    .col-xs-7 {flex: 0 0 73%;max-width: 73%;}

}

.supplier-form label{
    color:#111;
}
.supplier-form .graphical_ctrl_checkbox .ctrl_element:after {
    left: 7px;
    top: 3px;
    width: 8px;
    height: 14px;
}

.supplier-form .title-radio-button { margin: 0 30px auto 0;}

/* For Custom Profile Form */
/*Start of migrated carousel banner for homepage banners*/
#slider-rotating-carousel-component {
    position: relative;
}

/*fix for the  slider showing multiple images*/
.slider-rotating-carousel-component .slider-rotating-carousel {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease;
    -webkit-transition: opacity 0.3s ease;
    max-height:445px;
}
.slider-rotating-carousel-component .slider-rotating-carousel.slick-initialized {
    visibility: visible;
    opacity: 1;
    max-height:initial;
}

.slider-rotating-carousel-banner.slider-rotating-carousel-height{height:440px;overflow:hidden;}
.slider-rotating-content-component-wrap{text-align:center;z-index:1;padding:30px 15px 15px;}

/* Carousel styles - START */
.slider-rotating-carousel-buttons{position:absolute;bottom:15px;left:15px;height:34px;border-radius:16px;background-color:#fff;list-style:none;display:flex;flex-direction:row;box-shadow:0 2px 8px 0 rgba(0,0,0,0.2);margin:0;padding:4px 2px;}
.slider-rotating-carousel-button{position:relative;border-radius:50%;border:none;height:26px;width:26px;background-color:#fff;color:#003778;font-size:14px;line-height:16px;text-align:center;margin:0 2px;padding:0;}
.slider-rotating-carousel-button:hover{text-decoration:none;cursor:pointer;}
.slider-rotating-carousel-pause:hover, #slider-rotating-carousel-component :hover, .slider-rotating-carousel-pause-bg:hover, .slider-rotating-carousel-pause-bg::before:hover, .slider-rotating-carousel-pause-bg::before:hover { cursor:pointer; }
.slider-rotating-carousel-button > *{pointer-events:none;}
.slider-rotating-carousel-buttons > li.slick-active > .slider-rotating-carousel-button{color:#fff;background-color:#00549A;}
.slider-rotating-carousel-pause{height:38px;width:38px;border-radius:50%;border:none;background-color:transparent;position:absolute;bottom:13px;right:15px;z-index:10;padding:0; cursor: pointer;}
.slider-rotating-carousel-pause-bg{height:34px;width:34px;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);border-radius:50%;background-color:#FFF;box-shadow:0 2px 8px 0 rgba(0,0,0,0.2);pointer-events:none;}
svg.slider-rotating-carousel-progress{overflow:visible;position:absolute;top:0;left:0;pointer-events:none;transform:rotate(-90deg);}
svg.slider-rotating-carousel-progress circle.slider-rotating-carousel-progress_initial{display:none;}
svg.slider-rotating-carousel-progress circle{stroke:#0075FF;stroke-width:3px;stroke-dasharray:125;stroke-dashoffset:0;fill:rgba(225,255,255,0);}
.slider-rotating-carousel-pause[data-pressed="false"] .slider-rotating-carousel-pause-bg:before,.slider-rotating-carousel-pause[data-pressed="false"] .slider-rotating-carousel-pause-bg:after{content:"";display:block;position:absolute;top:50%;left:calc(50% - 3px);transform:translate(-50%,-50%);height:10px;width:0;border:1px solid #003778;z-index:1;}
.slider-rotating-carousel-pause[data-pressed="false"] .slider-rotating-carousel-pause-bg:before{left:calc(50% + 3px);}
.slider-rotating-carousel-pause[data-pressed="true"] .slider-rotating-carousel-pause-bg:before{content:"";display:block;position:absolute;top:50%;left:calc(50% + 1px);transform:translate(-50%,-50%);width:0;height:0;border-top:5px solid transparent;border-bottom:5px solid transparent;border-left:11px solid #003777;z-index:1;}
  /* Carousel styles - END */

  /* Custom banner styles - START */

  @media (max-width: 639px) {
.slider-rotating-content-component-wrap{height:auto;}
  }

  @media (min-width: 640px) {
.slider-rotating-content-component-wrap{padding-top:15px;flex-grow:1;width:40%;padding-right:20px;padding-left:0;z-index:1;display:flex;flex-direction:column;justify-content:center;text-align:left;}
  }

  @media (min-width: 767.98px) {
/* Carousel styles - START */
.slider-rotating-carousel-buttons{bottom:30px;right:85px;left:auto;}
.slider-rotating-carousel-pause{bottom:28px;right:30px;}
  }

  @media (min-width: 1240px) {
/* Carousel styles - START */
.slider-rotating-carousel-buttons{transform:translateX(600px);right:calc(50% + 55px);left:auto;}
.slider-rotating-carousel-pause{transform:translateX(600px);right:50%;left:auto;}
/* Carousel styles - END */
.slider-rotating-content-component-wrap{width:40%;padding-right:60px;}
}

/*Carousel style overwritten - same height shadow box*/
#slider-rotating-carousel-component{background:#FFF;}
#slider-rotating-carousel-component .slick-track{display:table;}
#slider-rotating-carousel-component .slick-slide{display:table-cell!important;vertical-align:middle;float:none;position:relative;}
#slider-rotating-carousel-component .slick-slide:after{content:"";position:absolute;top:0;right:0;bottom:0;left:0;box-shadow:inset 0 0 80px 20px rgba(0,0,0,.05);}
/*Adding carousel with tabs style*/
#slider-rotating-carousel-component + .nav-tabs-se{background:linear-gradient(to bottom,rgba(0,0,0,0.04) 0 100%);border-bottom:1px solid #d4d4d4;background-color:#FFF;}
#slider-rotating-carousel-component + .nav-tabs-se a.rsx-tabs-tab.rsx-active{position:relative;bottom:-1px;z-index:1;}
#slider-rotating-carousel-component + .nav-tabs-se .rsx-tabs:not(.rsx-tabs_vertical) .rsx-tabs-tab.rsx-active .rsx-tabs-tab-top{background-color:#fff;display:table-cell;float:none;vertical-align:middle;}
.slider-rotating-carousel-pause:hover, .slider-rotating-carousel-buttons .slider-rotating-carousel-button:hover{-webkit-appearance:button; cursor:pointer;}
/*End of migrated carousel banner for homepage banners*/

/*customize rotating homepage banner v2 */
.align-b-self{align-self:flex-end;margin:0 auto}
.subRectCont{display:flex;display:-webkit-flex;flex-flow:wrap;height:auto;border:1px solid #E1E1E1;border-radius:10px;background-color:#FFF;box-shadow:0 6px 25px 0 rgba(0,0,0,0.12);padding:30px}

@media all and (-ms-high-contrast: none),(-ms-high-contrast: active) and (min-width: 768px) {
    .subRectCont{flex-direction:column}
}

/*mobile only*/
@media (max-width: 767.98px) {
    .subRectCont{min-width:290px;
                 /*max-width:80%;*/
                 max-width:500px;
                 box-sizing:border-box;
                 min-height:220px
    }
    .txt_h1_spl{letter-spacing:-.4px;line-height:28px}
    .multiline-bannertext.v2{
        height:auto;
        /*margin-top: 165px;*/
        width:calc(100% - 30px);
        /*margin-left: -7px;*/
        max-width:100%;
        /*to increase auto to top*/
        top:auto;
        bottom:90px;
        /*to increase auto to top*/
    }
    .parentImgCont.size440.home_v2.bgpos{min-height:200px;height:203px;margin-bottom:273px;background-position:85%;background-repeat:no-repeat;position:inherit}
    .slider-rotating-carousel-pause,.slider-rotating-carousel-buttons{bottom:30px}
    .liquid-container.container.m-15{padding-left:15px;padding-right:15px}
}

/*tablet and desktop*/
@media (min-width: 768px) {
    .multiline-bannertext.v2{max-width:360px}
    .subRectCont{width:360px;box-sizing:border-box;/*min-height: 277px;*/ height:auto;margin-top:-20px}
    .txt_h1_spl{letter-spacing:-.5px;line-height:38px}
}

/* For Custom Profile Form ERROR */

.alert-list .error a:hover {color:#bd2025;text-decoration:none;}
@media (max-width: 767.98px) {
    /*borders*/
    .no-border-xs{border-radius:0;border:none}
    .border-top-bottom-xs{border-left:none;border-right:none;border-top:1px solid #D4D4D4;border-bottom:1px solid #D4D4D4}
    .border-d4-top-xs{border-left:none;border-right:none;border-top:1px solid #D4D4D4;border-bottom:none}
    .border-d4-bottom-xs{border-left:none;border-right:none;border-top:none;border-bottom:1px solid #D4D4D4}
    .no-border-bottom-xs{border-bottom:0;border-bottom-left-radius:0;border-bottom-right-radius:0}
    .line-height-22-xs{line-height:22px}
    .txtSize30.line-height-22-xs{margin-left:3px;}
}
/* For Custom Profile Form ERROR */

@media print {
    .no-print,.no-print *{display:none!important}
    .stickyItemWrapper,footer_grey,.subnav-scroll,.banner-crop-img,.footer_grey_wrapper,.connector-active-lob,.banner-crop-img,.backtotop_tablet_mobile_wrapper,ul.connector-areas,.federal-bar,.connector-search-wrap,#connector-search-button,.global-navigation .connector-nav-open-button, .icon.icon-exapnd-outline-circled{display:none!important}
    .size300,.size440{height:auto!important}
    .connector-brand a:before{font-size:40px!important}
    .txtWhite{color:#000!important}
    .bgBlueGradient{background-color:#fff!important}
    .accordion-wrap .collapse-accordion-accessible-toggle{display:block!important}
    .accordion-group .accordionContent{display:block!important}
    .card-collapsible-child{height:400px}
    .card-collapsible-child.expanded div{height:calc(200% + 15px);width:calc(200% + 15px);display:block}
    .card-collapsible-child .card-collapsible-child-content{opacity:1;max-height:250px;overflow:hidden;display:block}
    .card-collapsible-child .card-collapsible-child-accordion{display:none!important}

    .parentImgCont{
        min-height:60px !important;
    }
    .parentImgCont.size300 img{display:none !important; position:absolute;

    }
    .relative.parentImgCont .position-absolute {
    position:relative !important;
    bottom:0;
    }
    .graph-container{display:none!important}
    .connector{padding-bottom:30px;background:#fff!important}
    .btn-default{color:#003778!important}
    .print-txt-white {color:#fff!important;}
    .d-flex-print {display:flex!important;}
    main > .liquid-container.bgGray19 {
        background:#fff;
    }

    .global-navigation .connector-brand-home, .global-navigation.bce .connector-brand a:before{
        color:#00549a;
    }
    .spacer45{
        height:30px;
    }
    .spacer30{
        height:20px;
    }
    #maincontent > div > div.container.liquid-container > div > div > div.col-12.col-sm-3.col-md-4.relative{
         display:none;
     }

    #maincontent > div > div.container.liquid-container > div > div > div.col-12.col-sm-9.col-md-8.pad-45-right.no-pad-xs{
        width:100% !important;
        flex:1 !important;
        max-width:100% !important;
    }
/*tables*/
.scrollableContainerShadow .bce-table th, .bce-table td{
    padding:12px 0 12px 0 !important;
}

}

/*Added this media print and screens for mobile of News release listings
    mark up. Do not delete
*/
@media print and (min-width: 320px) and (max-width: 767px) {
    .news-title-print h1 {
        margin-bottom:20px;
    }
}
/*Added this media print and screens for mobile of News release listings
    mark up. Do not delete
*/

/*Big Table with Several Columns*/
    _:-ms-fullscreen, :root .big-table-with-twelve-columns{overflow:visible;} /*For IE*/
    .big-table-with-twelve-columns:-moz-read-only {overflow:unset;} /*For Firefox*/
    .big-table-with-twelve-columns {display:flex;width:100%;overflow:hidden;}
    .big-table-with-twelve-columns .table-labels-vertical table {min-width: 88px;}
    .big-table-with-twelve-columns .table-labels-vertical {max-width: 88px;}
    .big-table-with-twelve-columns .table-labels-vertical table thead {border-right: 1px solid #111!important}
    .big-table-with-twelve-columns .table-labels-vertical table th, .big-table-with-twelve-columns .table-labels-vertical table td {margin:0 auto;}
    .big-table-with-twelve-columns .scrollableContainer-big-table table th,.big-table-with-twelve-columns .scrollableContainer-big-table table td {justify-content:center;}
    .scrollableContainer-big-table table td.justify-end {justify-content:flex-end;}

/*Big Table with Several Columns*/

/*Scrollable container for Big Table*/
@media (min-width: 992px) and (max-width:1239px) {
    .scrollableContainer-big-table{width:100%;overflow:hidden;overflow-x:scroll;position:relative; scrollbar-color:#00549A #a1a5a6; scrollbar-width:thin; scrollbar-highlight-color:#00549A;}
    .scrollableContainerShadow-big-table{position:relative}
    .left.scrollableContainerShadow-big-table:before{width:46px;-webkit-transition:width .5s;transition:width .5s}
    .scrollableContainerShadow-big-table:before{width:0;pointer-events:none;content:"";position:absolute;top:0;bottom:0;left:0;z-index:1;background:-moz-linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);background:-webkit-linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);background:linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);-webkit-transition:width .1s;transition:width .1s}
    .right.scrollableContainerShadow-big-table:after{width:46px;-webkit-transition:width .5s;transition:width .5s}
    .scrollableContainerShadow-big-table:after{width:0;pointer-events:none;content:"";position:absolute;top:0;bottom:0;right:0;z-index:1;background:-moz-linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);background:-webkit-linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);background:linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);-webkit-transition:width .1s;transition:width .1s}
    .scrollableContainer-big-table::-webkit-scrollbar-track{background-color:#BABEC2}
    .scrollableContainer-big-table::-webkit-scrollbar{height:8px;background-color:#F5F5F5}
    .scrollableContainer-big-table::-webkit-scrollbar-thumb{background-color:#00549A}
    .big-table-with-twelve-columns .scrollableContainer-big-table table {width:110%;}

}

@media (min-width: 768px) and (max-width: 991.98px) {
    .big-table-with-twelve-columns .scrollableContainerShadow-big-table {width: 108%;}
    .big-table-with-twelve-columns .scrollableContainerShadow-big-table table {width:146%;}
    .big-table-with-twelve-columns .table-labels-vertical {max-width: 88px;}
}

@media (min-width: 560px) and (max-width: 767.98px) {
    .big-table-with-twelve-columns .table-labels-vertical table {min-width:70px;}
    .big-table-with-twelve-columns .scrollableContainerShadow-big-table table {width:180%; min-width:100px;}
}

@media (min-width: 416px) and (max-width: 559.98px) {
    .big-table-with-twelve-columns .scrollableContainerShadow-big-table table {width:300%; min-width:50px;}
    .big-table-with-twelve-columns .table-labels-vertical table {min-width:70px;}
}

@media screen and (max-width: 415px) {
    .big-table-with-twelve-columns .scrollableContainerShadow-big-table table {width:400%; min-width:50px;}
    .big-table-with-twelve-columns .table-labels-vertical table {min-width:70px;}
}
/*Scrollable container for Big Table ends*/

/*SEVEN COLUMN table with Right top Label*/

/*SEVEN COLUMN table with Right top Label - Specific column and row sized for & column table*/
.col-1-for-7-columns-only {
    width: 14.285714285714285714285714285714%;
    max-width: 14.285714285714285714285714285714% !important;
    flex: 0 0 14.285714285714285714285714285714%;
}
.col-2-for-7-columns-only {
    flex: 0 0 28.571428571428571428571428571428%;
    width: 28.571428571428571428571428571428%;
    max-width: 28.571428571428571428571428571428% !important;
}
.col-3-for-7-columns-only {
    flex: 0 0 42.857142857142857142857142857142%;
    width: 42.857142857142857142857142857142%;
    max-width: 42.857142857142857142857142857142% !important;
}
.col-4-for-7-columns-only {
    flex: 0 0 57.142857142857142857142857142856%;
    width: 57.142857142857142857142857142856%;
    max-width: 57.142857142857142857142857142856% !important;
}
.col-5-for-7-columns-only {
    flex: 0 0 71.42857142857142857142857142857%;
    width: 71.42857142857142857142857142857%;
    max-width: 71.42857142857142857142857142857% !important;
}
.col-6-for-7-columns-only {
    flex: 0 0 85.714285714285714285714285714284%;
    width: 85.714285714285714285714285714284%;
    max-width: 85.714285714285714285714285714284% !important;
}
.col-7-for-7-columns-only {
    flex: 0 0 99.999999999999999999999999999998%;
    width: 99.999999999999999999999999999998%;
    max-width: 99.999999999999999999999999999998% !important;
}
/*SEVEN COLUMN table with Right top Label - Specific column and row sized for & column table ends*/

.seven-column-table-with-right-top-label .no-border {border: 0 !important;}
.seven-column-table-with-right-top-label .borderGrayLight6 {border: 1px solid #d4d4d4!important;}
table .table-gray-column-heading th:not(:last-child) {border-right: 1px solid #d4d4d4!important;}

/*Seven Coulmn table with Right top Label ends*/

/* for js validate */
#errorContainer {
    display: none;
    overflow: auto;
}
.error-label-message {
    margin-top:10px;
    margin-bottom: -10px;
    display: block;
    color: #bd2025;
    font-size: 12px;
}

.error-label-message:before {
    content: "\e60a";
    font-family: 'bell-icon';
    position: relative;
    top: 0px;
    font-size: 16px;
    padding-right:10px;
}

.supplier-form .form-control {
    font-size:14px;
}

#signin_errors ul {
    color:#000;
    padding-bottom: 0;
    margin-bottom: 0;
}

#signin_errors ol li,#signin_errors ol li label {
    color:#000;
}

/* for js validate */

.two-column-with-banner-image .imgLogo_cContainer img {
    height: auto;
    width:100%;
}

.two-column-with-banner-image .tcbi-banner {
    border-radius: 10px;
}

.two-column-with-banner-image .container-floating-play-button {
    position: relative;
    text-align: center;
}

.two-column-with-banner-image .playIconhover {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.two-column-with-banner-image .playIconhover_v2 {
    position: absolute;
    top: 52%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.vGalleryBgUpdate {
    position:absolute;
    overflow:hidden;
    min-height:430px;
}
.parentImgContModified {
    width: 100%;
    min-height: 430px;
    margin: 0;
    overflow: hidden;
}

    .parentImgContModified.size430 {
        height: 430px;
    }

    .parentImgContModified img.banner-crop-img.centerView:not(.size430) {
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
    }

    .parentImgContModified img.banner-crop-img.size430 {
        min-width: 1440px;
    }

    .parentImgContModified img.banner-crop-img {
        position: absolute;
        height: auto;
        width: auto;
        max-width: 100%;
    }

@media (min-width: 992px) {
    .two-column-with-banner-image .mid-banner-logos .imgLogo_cContainer div.container-img {
        min-width: 63px;
    }
}

@media (min-width: 320px) and (max-width:767.98px) {
    .two-column-with-banner-image .imgLogo_cContainer img {
        height: auto;
        width:100%;
    }
    .parentImgContModified img.banner-crop-img.centerView:not(.size430) {
        top: 70%;
        left: 50%;
        transform: translate(-70%,-50%);
    }

    .two-column-with-banner-image .mid-banner-logos .imgLogo_cContainer div.container-img {
        min-width: 38px;
    }

}

@media (min-width: 768px) and (max-width: 991.98px){
    .two-column-with-banner-image .imgLogo_cContainer img {
        height: auto;
        width: 100%;
    }

    .two-column-with-banner-image .mid-banner-logos .imgLogo_cContainer div.container-img {
        min-width: 50px;
    }
}

/* Four column bullet graph*/
.four-column-bullet-graph-container {display: flex;flex-direction: column;}
.four-column-bullet-graph-container ul {list-style:none;padding-left:0;align-items:flex-start;}
.four-column-bullet-graph {margin-bottom:40px;}
.border-lightGray-left{border-left: 1px solid #d4d4d4;}

    /*Title with color bullet*/
    .title-with-color-bullet {width:100%;
                              display:flex;
                              flex-direction:column;
                              height: auto;
                              min-height:54px;
                              justify-content:space-between;}
    .title-with-color-bullet .color-bullet-group .color-bullet {width:10px;height:10px;margin-right: 4px;}
    .color-bullet-group-column .color-bullet {width:16px;height:16px;margin-right: 16px;}
    .color-bullet {width:16px;height:16px;}
    .title-with-color-bullet .color-bullet-group {display:flex;flex-direction:row;}
    .color-bullet-group-column  {display:flex;flex-direction:column;}
    .title-with-color-bullet .bullet-title {margin-bottom:10px;max-width:180px;}
    .bgBlueAqua {background-color: #0075FF;}
    .bgLightGray20 {background-color: #C2CEDF;}
    /*Title with color bullet ends*/

    /*Four column bullet graph legend*/
    .four-column-bullet-graph-legend {width: 100%;max-width: 170px;align-self: flex-end;margin-top: -177px}
    .four-column-bullet-graph-legend .color-bullet-with-name p {margin:0!important;}
    .four-column-bullet-graph-legend .color-bullet-with-name {margin-bottom:8px;}
    /*Four column bullet graph legend*/

/* Four column bullet graph ends*/

/*Five column modified*/
@media (min-width: 768px) and (max-width: 991.98px) {
    .five-column-modified {width:115%;}
}
/*Five column modified end*/

/*Icon list column*/
.icon-list-column-container {padding: 45px 0 45px 0;}
.icon-list-column-container ul {list-style:none;}
.icon-list-column-group .icon-with-title p.caption {width:100%;max-width:124px; margin-bottom:0; }
.icon-list-column-group .icon-with-title .icon-container-74 {height:74px; width:74px; display:flex;  align-items:center}
.icon-list-column-group .icon-with-title .icon {color:#00549a;}
.line-height-22px-xs {line-height:22px;}

    /*Icon with caption and round background */
      .icon-with-caption-and-round-background {width:100%; padding:10px;border-radius:37px;}
      .icon-with-caption-and-round-background .icon {color:#378E42;}
    /*Icon with caption and round background ends*/

/*Responsive for icon with caption and round background*/
@media screen and (max-width:767.98px) {
    .icon-with-caption-and-round-background {border-radius:10px;}
    .icon-list-column-container {padding: 30px 0 30px 0;}
    .icon-list-neg-marg{
        margin-bottom:0px;
    }
    .spacer10-xs{
        height:10px;
    }
}
/*Responsive for icon with caption and round background*/

@media (min-width: 768px) and (max-width: 991.98px){

    .icon-list-column-group .icon-with-title:first-child {
        padding-left:0;
        padding-right:12.5px;
    }
    .icon-list-column-group .icon-with-title:last-child {
        padding-left:12.5px;
        padding-right:0;
    }
    .icon-list-column-group .icon-with-title {
        padding-left:12.5px;
        padding-right:12.5px;
    }
      .icon-with-caption-and-round-background {width:80%;}
    .icon-list-column-group .icon-with-title .caption {width:100%;max-width:100px!important;margin-top:20px;}
    .four-column-bullet-graph-legend{
        padding-bottom:40px;
    }
    .four-column-bullet-graph-group {
        margin-bottom:0;

    }
}

/*Desktop*/
@media (min-width: 992px) {
    /*.icon-list-neg-marg{
        margin-left:-15px;
        margin-right:-15px;*/

    .icon-list-neg-marg{
        margin-bottom:0px;
    }
    .icon-list-column-group .icon-with-title {
        padding-left:15px;
        padding-right:15px;
    }

    .icon-list-column-group .icon-with-title:first-child {
        padding-left:0;
        padding-right:12.5px;
    }
    .icon-list-column-group .icon-with-title:last-child {
        padding-left:12.5px;
        padding-right:0;
    }
    .icon-list-column-container {padding: 45px 0 50px 0;}

    .four-column-bullet-graph-container{
        max-width:80%;
        margin:0 auto;
    }
    .four-column-bullet-graph-legend{
        padding-bottom:40px;
    }
}

/*Icon list column*/

/*For text orientation*/
.text-vertical {
    writing-mode: vertical-lr;
    -ms-writing-mode: tb-rl;
    transform: rotate(180deg);
}
/*For text orientation*/

/*Special Table with heading*/
.special-table-heading thead th:not(:last-child) {border-right:1px solid #111!important;}
.special-table-heading thead{border:1px solid #00549a}
.special-table-heading tbody{border:1px solid #d4d4d4;border-top:none}
.special-table-heading td{font-weight:400;padding:14px 22px 14px 22px;border:none;}
.special-table-heading th {font-weight:400;padding: 10px 20px;border:none;}
.special-table-heading td{min-height:50px; 	border-right: 1px solid #D4D4D4;}
.special-table-heading tbody th {border-right: 1px solid #D4D4D4;}
.special-table-heading tbody tr:not(:last-child){border-bottom:1px solid #d4d4d4}
.special-table-heading tbody tr:nth-child(odd), .special-table-heading tr td.bgwhite{background-color:#fff}
.special-table-heading tr td span{flex-basis: 100%}
.special-table-heading td {background-color:#fff!important; color:#555!important;}
.special-table-heading .width-8-pc {width:8%; max-width:8%}
.special-table-heading .width-2-pc {width:2%;max-width:2%;}
.special-table-heading .width-4-pc {width:4%;max-width:4%;}
.special-table-heading .width-20-pc {width:20%;max-width:20%;}
.special-table-heading .width-10-pc {width:10%;max-width:10%;}
.special-table-heading .table-heading-row {background-color:#00549a; color:#fff;}
.special-table-heading .table-heading-side-row, .special-table-heading .table-heading-side-row .pad-1 {padding:1px 0;}

    /*Special table heading for side*/
    .special-table-heading-container .special-table-heading-side {min-width: 65px;}
    .special-table-heading-side .table-body {background-color:#f6f6f6;border-right:none!important;
    border-left:1px solid #d4d4d4!important;}
    .special-table-heading-side .table-body .table-body-side-row {align-items:flex-end; padding:18px 0;width:100%;max-width:200px;}
    .special-table-heading-side .table-body:first-child {border-top:1px solid #d4d4d4!important;}
    .special-table-heading-side .table-body{border-bottom:1px solid #d4d4d4!important;}
    .col-05 {flex: 0 0 4.3333333333%;max-width: 4.3333333333%;}
    .col-1-5 {flex: 0 0 16.6666666666%;max-width: 16.6666666666%;}
    /*Special table heading for side*/

    /*Color with background group*/
     .number-with-color-background-group {display:flex;}
     .number-with-color-background-group  .number-with-color-background {margin-right:2px;}
     .number-with-color-background {height:auto; width:100%; min-width:20px; border-radius: 2px; display:flex; justify-content:center;}
     .number-with-color-background span {text-align:center;}
     .color-bullet-table-legend {margin-left: 60px; margin-bottom:20px;}
     .color-bullet-table-legend .color-bullet{margin-right:10px;}
     .color-bullet-table-legend .color-bullet-with-name{margin-right:20px;}
     .bgOrange {background-color:#F36E23;}
     .bgDarkRed {background-color:#8F1838;}
     .bgRed3 {background-color:#BD2025;}
     .bgGreen {background-color:#339043}
     .bgRedOrange {background-color:#F0412A;}
    /*Color with background group*/

    /*For Responsive*/

    @media (min-width: 992px) and (max-width: 1250px) {
        .special-table-heading-container .scrollableContainerShadow-huge-table table {width:200%;}
        .special-table-heading th {padding: 10px 20px;}

        /*For message error*/
        .error-message-container .msg-container p { width: 70%;margin: 0 auto;}
    }

    @media (min-width: 768px) and (max-width: 991.98px) {
        .special-table-heading-container .scrollableContainerShadow-huge-table table {width:250%;}
        .special-table-heading th {padding: 10px 20px;}
    }

    @media (min-width: 560px) and (max-width: 767.98px) {
        .special-table-heading-container .scrollableContainerShadow-huge-table table {width:300%; min-width:50px;}
        .special-table-heading th {padding: 10px 20px;}

    }

    @media (min-width: 460px) and (max-width: 559.98px) {
        .special-table-heading-container .scrollableContainerShadow-huge-table table {width:400%; min-width:50px;}

    }

    @media screen and (max-width: 459.98px) {
        .special-table-heading-container .scrollableContainerShadow-huge-table table {width:520%; min-width:50px;}
        .special-table-heading td{padding:10px 20px;}
        .special-table-heading th {padding: 5px 20px;}
        .color-bullet-table-legend {flex-wrap:wrap;}
        .color-bullet-table-legend .color-bullet-with-name{margin-bottom:10px;}
    }

    /*For Responsive*/

/*Special Table with heading*/

/*Scrollable container for Big Table*/

.scrollableContainer-huge-table{width:100%;overflow:hidden;overflow-x:scroll;position:relative; scrollbar-color:#00549A #a1a5a6; scrollbar-width:thin; scrollbar-highlight-color:#00549A;}
.scrollableContainerShadow-huge-table{position:relative; min-width:200px;}
.left.scrollableContainerShadow-huge-table:before{width:46px;-webkit-transition:width .5s;transition:width .5s}
.scrollableContainerShadow-huge-table:before{width:0;pointer-events:none;content:"";position:absolute;top:0;bottom:0;left:0;z-index:1;background:-moz-linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);background:-webkit-linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);background:linear-gradient(90deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);-webkit-transition:width .1s;transition:width .1s}
.right.scrollableContainerShadow-huge-table:after{width:46px;-webkit-transition:width .5s;transition:width .5s}
.scrollableContainerShadow-huge-table:after{width:0;pointer-events:none;content:"";position:absolute;top:0;bottom:0;right:0;z-index:1;background:-moz-linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);background:-webkit-linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);background:linear-gradient(270deg,rgba(0,0,0,0.29) 0%,rgba(0,0,0,0) 100%);-webkit-transition:width .1s;transition:width .1s}
.scrollableContainer-huge-table::-webkit-scrollbar-track{background-color:#BABEC2}
.scrollableContainer-huge-table::-webkit-scrollbar{height:8px;background-color:#F5F5F5}
.scrollableContainer-huge-table::-webkit-scrollbar-thumb{background-color:#00549A}
.scrollableContainer-huge-table table {width:158%;}

/*For Mozilla only*/
@-moz-document url-prefix() {
    @media (min-width: 320px) and (max-width: 1250px) {
        .scrollableContainerShadow-huge-table {
            position: relative;
            min-width: 200px;
            max-width: 1500px;
        }

    }

}
/*For Mozilla only*/

/*Special Table with heading*/

/*For Removing On-hover underline on Icons*/
.removeIconTextUnderlineOnHover:hover i, .removeIconTextUnderlineOnHover:focus i,
.removeIconTextUnderlineOnHover:hover span.iconContainer, .removeIconTextUnderlineOnHover:focus span.iconContainer {
    display: inline-block;
    text-decoration: none
}

a:hover span.iconContainer, a:focus span.iconContainer {display: inline-block;
text-decoration: none;}

a:hover span.iconContainer.displayInherit, a:focus span.iconContainer.displayInherit {display: inherit;text-decoration: none;}

/*For Removing On-hover underline on Icons ends*/

/*Image Zooming and text color change hover*/
.zoom-image-text-color-hover img { transition: all 0.6s cubic-bezier(0.42, 0, 0, 0.96);}
.zoom-image-text-color-hover:hover {text-decoration:none;}
.zoom-image-text-color-hover:hover img {transform: scale(1.05);}
.zoom-image-text-color-hover:hover h3 {color:#00549a;}
.zoom-image-text-color-hover:hover p {color:#00549a; text-decoration:underline;}
/*Image Zooming and text color change hover ends*/

/*Box Shadows*/
.box-shadow-none {box-shadow: none;}
.box-shadow-image img{
    max-height: 70vh;
}
.box-shadow-image img {box-shadow: 0 0 30px rgba(0,0,0,0.3);}
/*Box Shadows*/

/* for button to be text */
.btn-link {
    border: 0;
    background: none;
    color: #00549a;
    text-decoration: underline;
    cursor: pointer;
    margin-right: 30px;
}

.btn-link:hover {
    color: #003778;
}

@media (max-width: 767.98px) and (min-width: 320px) {
    .btn-link {
        margin-right: 0px;
        margin-top: 20px;
    }

    .supplier-form .form-control, .supplier-form .form-label-wrap {
        font-size: 16px;
    }
}

/* form radio */
.graphical_ctrl input:focus ~ .ctrl_element {
    background: #fff;
    border: 6px solid #003778;
}

.graphical_ctrl input:focus ~ .ctrl_element:after {
    left: 0px;
    top: 0px;
    height: 12px;
    width: 12px;
    border-radius: 50%;
    background: #fff;
}

/* adding shadow for container */
.containerLinknoHover  {
    box-shadow: 0 6px 25px 0 rgba(0,0,0,0.12);
}

/* new icon to align the text properly */
.align-icon-text span {
    float: left;
}

.align-icon-text div {
    padding-left: 25px;
}

/*For overriding the "Simplelits Expanding" class hovering effect on link with icon
    on core. Do not Delete
*/
ul.simplelist.expanded li a:hover {
    text-indent:0px;
}
ul.simplelist.expanded div {
    text-indent: 0px;
    margin-left: 0px;
}
/*For overriding the "Simplelits Expanding" class hovering effect on link with icon
    on core. Do not Delete
*/

/*For overriding the "video-modal modal-body" class fixed issue for scroll when
    resizing the height of the window. Do not Delete */

.video-modal .modal-dialog-video-content .modal-body.mdl-dlg-video-body {
    overflow-y:hidden!important;
}

/*For overriding the "video-modal modal-body" class fixed issue for scroll when
    resizing the height of the window. Do not Delete */

/*For logos in mid banner homepage do not delete */
.mid-banner-logos .flex-20-pc {
    flex-basis: 20%;
    max-width: 20%;
    padding-left: 15px;
    padding-right: 15px;
}

/*For logos in mid banner homepage do not delete */

/*START*/
/*Do not delete, Used in center button with gray line.
    For IE media query, TABLET and up*/
@media (min-width:768.67px) {
    _:-ms-fullscreen, :root .center-button-with-line-fluid-container .center-button-with-line-fluid
    .gray-line-holder {
        width: 50%;
    }  /*IE 10 and above*/

    _:-ms-lang(x), .ie10up .center-button-with-line-fluid-container .center-button-with-line-fluid
    .gray-line-holder {
        width: 50%;
    } /*IE 11 and above*/

    _:-ms-fullscreen, :root .center-button-with-line-fluid-container .center-button-with-line-fluid
    .gray-line-holder .gray-line {
        width: 100%;
    }
    /*IE 10 and above*/

    _:-ms-lang(x), .ie10up .center-button-with-line-fluid-container .center-button-with-line-fluid
    .gray-line-holder .gray-line {
        width: 100%;
    }
    /*IE 11 and above*/
}
/*END*/

/*START*/
/*Do not delete, Used in Scrolable huge table, TABLET to DESKTOP for IE
*/

@media (min-width: 1249.98px) and (max-width: 1600px) {
    _:-ms-fullscreen, :root :lang(fr) .special-table-heading-container .scrollableContainerShadow-huge-table table {
        width: 200%;
    }

    _:-ms-lang(x), .ie10up :lang(fr) .special-table-heading-container .scrollableContainerShadow-huge-table table {
        width: 200%;
    }

}

@media (min-width: 992px) and (max-width: 1250px) {
    _:-ms-fullscreen, :root :lang(fr) .special-table-heading-container .scrollableContainerShadow-huge-table table {
        width: 265%;
    }

    _:-ms-lang(x), .ie10up :lang(fr) .special-table-heading-container .scrollableContainerShadow-huge-table table {
        width: 265%;
    }
}

@media (min-width: 768px) and (max-width: 991.98px) {
    _:-ms-fullscreen, :root :lang(fr) .special-table-heading-container .scrollableContainerShadow-huge-table table {
        width: 400%;
    }
    /*IE 10 and above*/

    _:-ms-lang(x), .ie10up :lang(fr) .special-table-heading-container .scrollableContainerShadow-huge-table table {
        width: 400%;
    }
    /*IE 11 and above*/
}

@media (min-width: 560px) and (max-width: 767.98px) {
    _:-ms-fullscreen, :root :lang(fr) .special-table-heading-container .scrollableContainerShadow-huge-table table {
        width: 500%;
        min-width: 100px;
    }

    _:-ms-lang(x), .ie10up :lang(fr) .special-table-heading-container .scrollableContainerShadow-huge-table table {
        width: 500%;
        min-width: 100px;
    }
}

@media (min-width: 416px) and (max-width: 559.98px) {
    _:-ms-fullscreen, :root :lang(fr) .special-table-heading-container .scrollableContainerShadow-huge-table table {
        width: 600%;
        min-width: 50px;
    }

    _:-ms-lang(x), .ie10up :lang(fr) .special-table-heading-container .scrollableContainerShadow-huge-table table {
        width: 600%;
        min-width: 50px;
    }
}

@media screen and (max-width: 415px) {
    _:-ms-fullscreen, :root :lang(fr) .special-table-heading-container .scrollableContainerShadow-huge-table table {
        width: 600%;
        min-width: 50px;
    }

    _:-ms-lang(x), .ie10up :lang(fr) .special-table-heading-container .scrollableContainerShadow-huge-table table {
        width: 600%;
        min-width: 50px;
    }
}

/*END*/

/*START*/
/*Do not delete, Used in Parent container of Slick slider, hiding it before on load*/
.slider-wrapper-init {
    max-height: 0;
    overflow: hidden;
}
/*END*/

/* datepicker custom css */
#datepicker-start-date {
    background-color: #fff;
    cursor: pointer;
}

#datepicker-start-date:-moz-read-only { /* For Firefox */
    background-color: #fff;
}

#datepicker-start-date:read-only {
    background-color: #fff;
}

#datepicker-end-date {
    background-color: #fff;
    cursor: pointer;
}

#datepicker-end-date:-moz-read-only { /* For Firefox */
    background-color: #fff;
}

#datepicker-end-date:read-only {
    background-color: #fff;
}

.ui-datepicker-trigger {
    cursor: pointer;
    text-decoration: none;
}

.form-control-select + span.icon-select-trigger {
    top:2px;
}
/* datepicker custom css */

/* Custom CLASS to FIX finacial table with three column */
@media (max-width: 639.98px) {
    .scrollableContainer > .bce-table.three-column.three-column-V2 {min-width:535px}
}

/* Custom CLASS to FIX stock info table with three column */
@media (max-width: 639.98px) {
    .scrollableContainer > .bce-table.three-column.three-column-V3 {min-width:640px}
}

/* Spacer adjustment,60 desktop and tablet, mobile 40 */
/* mobile only - spacer*/
@media screen and (max-width:767.98px) {
    .spacer40-xs {
        height: 40px;
    }
}

/* two column banner image with center text for both column */
.two-col-img-banner .bigCards_w_Image.v2 {
    height:360px;
}

@media (min-width: 768px) {
    .two-col-img-banner .bigCards_w_Image.v2 .bottomText {
        padding-bottom: 40px;
    }
}

/* Five column, with two header top */
@media (max-width: 992px) {
    .five-column-two-header-top td, .five-column-two-header-top th, .five-column-two-header-top th.vPadding24-imp {
        padding: 5px !important;
    }
}

@media (max-width: 768px) {
    .five-column-two-header-top td, .five-column-two-header-top th, .five-column-two-header-top th.vPadding24-imp {
        padding: 10px !important;
    }
}

/* Five column, with two header top */
@media (max-width: 992px) {
    .five-column-five-header-top td, .five-column-five-header-top th, .five-column-five-header-top th.vPadding24-imp {
        padding: 5px !important;
    }
}

@media (max-width: 768px) {
    .five-column-five-header-top td, .five-column-five-header-top th, .five-column-five-header-top th.vPadding24-imp {
        padding: 5px !important;
    }
}

/* Special six column table Do not delete */
.special-six-column-table .table-labels-vertical {min-width:88px;}
.special-six-column-table .table-labels-vertical table {max-width:88px;}
.special-six-column-table .table-labels-vertical table thead {border-right: 1px solid #111!important;}
/* Special six column table Do not delete */

/* For Column 5 tables only Do not delete */
.width-20-pc {flex:20%;max-width:20%}
/* For Column 5 tables only Do not delete */

/*Override Global nav in bce for ellipsis, long text title
    Do not Delete
*/
.global-navigation.bce .connector .connector-active-lob-title .overflow-ellipsis {
    width: 88%;
}

@media (max-width:380px) {
    .global-navigation.bce .connector .connector-active-lob-title .overflow-ellipsis {
        width: 130px;
    }
}

@media (max-width: 991.98px) {
    .global-navigation.bce .connector-active-lob-title {
        margin-right: 65px;
    }
}
/*Override Global nav in bce for ellipsis, long text title
    Do not Delete
*/

/* Fix issue with datepicker arrow so its clickable in IE and Mobile/Tablet Devices FOR CALENDAR PICKER ONLY */
/*
select.ui-datepicker-month {
    background-image: url('../../../../assets/img_tmp/BCE/up-down-dropdown.png');
    background-position: calc(100% - .4\rem), 100% 0;
    background-size: 10px;
    background-repeat: no-repeat;
}

select.ui-datepicker-year {
    background-image: url('../../../../assets/img_tmp/BCE/up-down-dropdown.png');
    background-position: calc(100% - .4\rem), 100% 0;
    background-size: 10px;
    background-repeat: no-repeat;
}
*/

select.ui-datepicker-month, select.ui-datepicker-year {
    padding-right: 10px;
    position: relative;
    display: inline-block;

}

select.ui-datepicker-month + span {
    left: 56%;
    transform: translate(-40px, -50%);
    z-index: 9999;
    position: absolute;
    margin: 0 auto;
    right: 40%;

    -webkit-user-select:none;
        -webkit-touch-callout:none;
             -moz-user-select:none;
             -ms-user-select:none;
             user-select:none;
}