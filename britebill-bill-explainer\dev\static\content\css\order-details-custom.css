.visible-md, .visible-sm, .visible-xs {
    display: none;
}

.review-page > main > .bgGray19 > .container.liquid-container:first-child {
    padding-right: 0px;
    padding-left: 0px;
}

.no-shadow {
    box-shadow: none;
}

.header-shadow {
    box-shadow: 0px 0px 50px 0px rgba(0, 0, 0, 0.2);
}

.responsive-simplified-header .icon-chevron-left:before {
    top: 0.15em;
}

.responsive-simplified-header .icon-chevron-left {
    margin-right: 2px;
}

.overflow-hidden {
    overflow: hidden;
}

.borderRadius-10 {
    border-radius: 10px;
}

.borderRadius-right-10 {
    border-bottom-right-radius: 10px;
    border-top-right-radius: 10px;
}

.borderRadius-left-10 {
    border-bottom-left-radius: 10px;
    border-top-left-radius: 10px;
}

.row.section {
    flex-wrap: wrap;
    display: flex;
    margin: 0px;
}

.row.section > .column:first-child {
    border-right-color: #d4d4d4;
    border-right-style: solid;
    border-right-width: 1px;
}

.step {
    padding-bottom: 30px;
    position: relative;
    color: #808080;
    min-height: 1em;
}

.step:last-child {
    padding-bottom: 0px !important;
}

.step > div:first-child {
    position: static;
    height: 0;
}

.step > div:not(:first-child) {
    padding-left: 30px;
    margin-left: 24px;
}

.step.step-done .circle,
.step.step-active .circle {
    background-color: #003778;
    line-height: 24px;
    height: 24px;
    width: 24px;
    left: 0px;
    top: -2px;
}

.circle {
    background-color: #e1e1e1;
    border-radius: 100%;
    text-align: center;
    position: relative;
    color: #ffffff;
    line-height: 14px;
    height: 14px;
    width: 14px;
    z-index: 1;
    left: 5px;
    top: 4px;
}

.circle > .icon {
    position: relative;
    top: -2px;
}

.step.step-done .caption,
.step.step-active .caption {
    font-weight: 700;
}

.caption {
    line-height: 22px;
    color: #111111;
    font-size: 18px;
}

.sub-caption {
    line-height: 18px;
    margin-top: 15px;
    color: #555555;
    font-size: 14px;
}

.step:after {
    content: "";
    background-color: #e1e1e1;
    position: absolute;
    display: block;
    height: 100%;
    width: 2px;
    z-index: 0;
    left: 11px;
    top: 7px;
}

.step.step-done:after {
    background-color: #003778;
    width: 4px;
    left: 10px;
}

.step:last-child:after {
    display: none;
}

.letterSpacing-narrow {
    letter-spacing: -0.5px;
}

.flex-v-center {
    display: inline-flex;
    align-items: center;
}

.flex-h-center {
    justify-content: center;
}

.account-mobility.row {
    margin-right: 0px;
    margin-left: 0px;
}

.account-mobility.row > .col-sm-6:first-child > .bgWhite.borderGrayLight6 {
    margin-right: 7.5px;
}

.account-mobility.row > .col-sm-6:last-child > .bgWhite.borderGrayLight6 {
    margin-left: 7.5px;
}

.social-links.right {
    float: right;
}

.btn {
    padding-bottom: 8px;
    padding-top: 8px;
}

.txtYellow2 {
    color: #F9AC28;
}

.txtGreen2 {
    color: #378E42;
}

.mobility-width {
    width: 84%;
}

.section-box {
    border: 1px solid #e1e1e1;
    border-radius: 10px;
    overflow: hidden;
}

.section-box > div:nth-child(even) {
    border-bottom: 1px solid #e1e1e1;
    border-top: 1px solid #e1e1e1;
}

.section-box .price {
    position: relative;
    left: -2px;
}

.section-box .price > sup:first-child {
    top: -0.60em;
    right: 1px;
}

.section-box .price > sup:last-child {
    top: -0.60em;
    left: 2px;
}

.section-box .share-group-table {
    border: 1px solid #e1e1e1;
    border-radius: 10px;
    overflow: hidden;
}

.section-box .share-group-body:first-child {
    border-right: 1px solid #e1e1e1;
}

.section-box .share-group-icon {
    text-align: center;
    position: relative;
    width: 45px;
    left: -1px;
}

.section-box .form-control {
    height: 50px;
}

.section-box form.flex-grid-no-spacing {
    margin-right: 55px;
    margin-left: 55px;
}

.section-box .container-flex-box-wrap.form-group span,
.section-box .container-flex-box-wrap.form-group label {
    display: inline-block;
    line-height: 14px;
}

.section-box .container-flex-box-wrap.form-group img {
    height: auto;
    width: 38px;
}

.section-box .share-group-content .offset {
    position: relative;
    left: -5px;
}

.section-box .terms-scroll {
    height: 55px;
}

#compare-package .modal-dialog .container.liquid-container {
    width: 100%;
}

#compare-package .modal-header {
    border-bottom-width: 0px;
    height: auto;
}

#compare-package .modal-title.bellSlimBlack {
    font-family: "bellslim_font_black", Helvetica, Arial, sans-serif;
}

#compare-package .close {
    position: relative;
    padding-bottom: 11px;
    padding-top: 11px;
    left: -1px;
}

#compare-package .current-solution > section:first-child ,
#compare-package .future-solution > section:first-child {
    margin-bottom: 59px;
}

#compare-package .current-solution > section:last-child ,
#compare-package .future-solution > section:last-child {
    position: absolute;
    height: 59px;
    width: 100%;
    bottom: 0;
}

#compare-package .bellSlimHeavy.item-price {
    font-family: "bellslim_font_heavy", Helvetica, Arial, sans-serif;
    letter-spacing: -1px;
}

#compare-package .bellSlimHeavy.item-price > sup:first-child {
    right: 1px;
}

#compare-package .bellSlimHeavy.item-price > sup:last-child {
    left: 2px;
}

.txtSize10 { font-size: 10px !important; }
.txtSize11 { font-size: 11px !important; }
.txtSize12 { font-size: 12px !important; }
.txtSize13 { font-size: 13px !important; }
.txtSize14 { font-size: 14px !important; }
.txtSize15 { font-size: 15px !important; }
.txtSize16 { font-size: 16px !important; }
.txtSize17 { font-size: 17px !important; }
.txtSize18 { font-size: 18px !important; }
.txtSize19 { font-size: 19px !important; }
.txtSize20 { font-size: 20px !important; }
.txtSize21 { font-size: 21px !important; }
.txtSize22 { font-size: 22px !important; }
.txtSize23 { font-size: 23px !important; }
.txtSize24 { font-size: 24px !important; }
.txtSize25 { font-size: 25px !important; }
.txtSize26 { font-size: 26px !important; }
.txtSize27 { font-size: 27px !important; }
.txtSize28 { font-size: 28px !important; }
.txtSize29 { font-size: 29px !important; }
.txtSize30 { font-size: 30px !important; }
.txtSize55 { font-size: 55px !important; }

.lineHeight-10 { line-height: 10px !important; }
.lineHeight-11 { line-height: 11px !important; }
.lineHeight-12 { line-height: 12px !important; }
.lineHeight-13 { line-height: 13px !important; }
.lineHeight-14 { line-height: 14px !important; }
.lineHeight-15 { line-height: 15px !important; }
.lineHeight-16 { line-height: 16px !important; }
.lineHeight-17 { line-height: 17px !important; }
.lineHeight-18 { line-height: 18px !important; }
.lineHeight-19 { line-height: 19px !important; }
.lineHeight-20 { line-height: 20px !important; }
.lineHeight-21 { line-height: 21px !important; }
.lineHeight-22 { line-height: 22px !important; }
.lineHeight-23 { line-height: 23px !important; }
.lineHeight-24 { line-height: 24px !important; }
.lineHeight-25 { line-height: 25px !important; }
.lineHeight-26 { line-height: 26px !important; }
.lineHeight-27 { line-height: 27px !important; }
.lineHeight-28 { line-height: 28px !important; }
.lineHeight-29 { line-height: 29px !important; }
.lineHeight-30 { line-height: 30px !important; }
.lineHeight-31 { line-height: 31px !important; }
.lineHeight-32 { line-height: 32px !important; }
.lineHeight-33 { line-height: 33px !important; }
.lineHeight-34 { line-height: 34px !important; }
.lineHeight-35 { line-height: 35px !important; }
.lineHeight-36 { line-height: 36px !important; }
.lineHeight-37 { line-height: 37px !important; }
.lineHeight-38 { line-height: 38px !important; }
.lineHeight-39 { line-height: 39px !important; }
.lineHeight-40 { line-height: 40px !important; }

@media (min-width:992px) {
    .hidden-md {
        display: none;
    }
}

@media (min-width:768px) and (max-width:991.98px) { /* [T] */
    .flex-sm {
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
    }
    
    .flex-v-center-sm {
        display: inline-flex;
        align-items: center;
    }
    
    .flex-h-center-sm {
        justify-content: center;
    }

    .inlineBlock-sm {
        display: inline-block;
    }
    
    .inline-sm {
        display: inline;
    }

    .pad-h-30-sm {
        padding-right: 30px;
        padding-left: 30px;
    }

    .step.step-active {
        padding-bottom: 15px;
    }

    .social-links.left-sm {
        float: left;
    }

    .margin-10-right-sm {
        margin-right: 10px;
    }

    .mobility-width { width: 88%; }

    .txtSize10-sm { font-size: 10px !important; }
    .txtSize11-sm { font-size: 11px !important; }
    .txtSize12-sm { font-size: 12px !important; }
    .txtSize13-sm { font-size: 13px !important; }
    .txtSize14-sm { font-size: 14px !important; }
    .txtSize15-sm { font-size: 15px !important; }
    .txtSize16-sm { font-size: 16px !important; }
    .txtSize17-sm { font-size: 17px !important; }
    .txtSize18-sm { font-size: 18px !important; }
    .txtSize19-sm { font-size: 19px !important; }
    .txtSize20-sm { font-size: 20px !important; }
    .txtSize21-sm { font-size: 21px !important; }
    .txtSize22-sm { font-size: 22px !important; }
    .txtSize23-sm { font-size: 23px !important; }
    .txtSize24-sm { font-size: 24px !important; }
    .txtSize25-sm { font-size: 25px !important; }
    .txtSize26-sm { font-size: 26px !important; }
    .txtSize27-sm { font-size: 27px !important; }
    .txtSize28-sm { font-size: 28px !important; }
    .txtSize29-sm { font-size: 29px !important; }
    .txtSize30-sm { font-size: 30px !important; }
    .txtSize55-sm { font-size: 55px !important; }

    .lineHeight-10-sm { line-height: 10px !important; }
    .lineHeight-11-sm { line-height: 11px !important; }
    .lineHeight-12-sm { line-height: 12px !important; }
    .lineHeight-13-sm { line-height: 13px !important; }
    .lineHeight-14-sm { line-height: 14px !important; }
    .lineHeight-15-sm { line-height: 15px !important; }
    .lineHeight-16-sm { line-height: 16px !important; }
    .lineHeight-17-sm { line-height: 17px !important; }
    .lineHeight-18-sm { line-height: 18px !important; }
    .lineHeight-19-sm { line-height: 19px !important; }
    .lineHeight-20-sm { line-height: 20px !important; }
    .lineHeight-21-sm { line-height: 21px !important; }
    .lineHeight-22-sm { line-height: 22px !important; }
    .lineHeight-23-sm { line-height: 23px !important; }
    .lineHeight-24-sm { line-height: 24px !important; }
    .lineHeight-25-sm { line-height: 25px !important; }
    .lineHeight-26-sm { line-height: 26px !important; }
    .lineHeight-27-sm { line-height: 27px !important; }
    .lineHeight-28-sm { line-height: 28px !important; }
    .lineHeight-29-sm { line-height: 29px !important; }
    .lineHeight-30-sm { line-height: 30px !important; }
    .lineHeight-31-sm { line-height: 31px !important; }
    .lineHeight-32-sm { line-height: 32px !important; }
    .lineHeight-33-sm { line-height: 33px !important; }
    .lineHeight-34-sm { line-height: 34px !important; }
    .lineHeight-35-sm { line-height: 35px !important; }
    .lineHeight-36-sm { line-height: 36px !important; }
    .lineHeight-37-sm { line-height: 37px !important; }
    .lineHeight-38-sm { line-height: 38px !important; }
    .lineHeight-39-sm { line-height: 39px !important; }
    .lineHeight-40-sm { line-height: 40px !important; }
}

@media (max-width: 767.98px) { /* [M] */
    .visible-xs {
        display: block;
    }

    .block-xs {
        display: block;
    }

    .borderRadius-10-xs {
        border-radius: 10px;
    }

    .flex-v-center-sm {
        display: inline-flex;
        align-items: center;
    }
    
    .flex-h-center-sm {
        justify-content: center;
    }
    
    .no-pad-xs {
        padding: 0 !important;
    }

    .pad-20-left-xs {
        padding-left: 20px;
    }

    .pad-h-0-xs {
        padding-left: 0px !important;
        padding-right: 0px !important;
    }
    
    .mar-h-15-xs {
        margin-left: 15px;
        margin-right: 15px;
    }

    .straight-border-xs {
        border-radius: 0px;
    }

    .no-side-borders-xs {
        border-left: none;
        border-right: none;
    }
    
    .simplified-header {
        height: 55px;
    }

    .row.section > .column:first-child {
        border-bottom-color: #d4d4d4;
        border-bottom-style: solid;
        border-bottom-width: 1px;
        border-right-width: 0px;
    }

    .responsive-simplified-header .icon-chevron-left:before {
        top: 0.1em;
    }

    .social-links.right {
        float: none;
    }

    .step.step-active {
        padding-bottom: 15px;
    }

    .step {
        padding-bottom: 30px;
    }

    .step > div:not(:first-child) {
        padding-left: 15px;
    }

    .mobility-width { width: 88%; }

    .section-box .share-group-content {
        padding-right: 15px;
        padding-left: 15px;
    }

    .section-box .terms-scroll {
        height: 105px;
    }
    
    .section-box .accordion-content {
        width: 80%;
    }

    #compare-package .modal-header {
        padding-bottom: 27px;
        padding-top: 27px;
    }

    #compare-package .close {
        padding: 11px;
        left: -9px;
        top: 3px;
    }

    .section-box .share-group-table {
        border: none;
    }

    .section-box .share-group-body {
        border: 1px solid #e1e1e1;
        border-radius: 10px;
        overflow: hidden;
    }

    .section-box .share-group-snap {
        display: list-item;
    }

    .section-box form.flex-grid-no-spacing {
        margin-right: 0px;
        margin-left: 0px;
    }

    .txtSize10-xs { font-size: 10px !important; }
    .txtSize11-xs { font-size: 11px !important; }
    .txtSize12-xs { font-size: 12px !important; }
    .txtSize13-xs { font-size: 13px !important; }
    .txtSize14-xs { font-size: 14px !important; }
    .txtSize15-xs { font-size: 15px !important; }
    .txtSize16-xs { font-size: 16px !important; }
    .txtSize17-xs { font-size: 17px !important; }
    .txtSize18-xs { font-size: 18px !important; }
    .txtSize19-xs { font-size: 19px !important; }
    .txtSize20-xs { font-size: 20px !important; }
    .txtSize21-xs { font-size: 21px !important; }
    .txtSize22-xs { font-size: 22px !important; }
    .txtSize23-xs { font-size: 23px !important; }
    .txtSize24-xs { font-size: 24px !important; }
    .txtSize25-xs { font-size: 25px !important; }
    .txtSize26-xs { font-size: 26px !important; }
    .txtSize27-xs { font-size: 27px !important; }
    .txtSize28-xs { font-size: 28px !important; }
    .txtSize29-xs { font-size: 29px !important; }
    .txtSize30-xs { font-size: 30px !important; }
    .txtSize55-xs { font-size: 55px !important; }

    .lineHeight-10-xs { line-height: 10px !important; }
    .lineHeight-11-xs { line-height: 11px !important; }
    .lineHeight-12-xs { line-height: 12px !important; }
    .lineHeight-13-xs { line-height: 13px !important; }
    .lineHeight-14-xs { line-height: 14px !important; }
    .lineHeight-15-xs { line-height: 15px !important; }
    .lineHeight-16-xs { line-height: 16px !important; }
    .lineHeight-17-xs { line-height: 17px !important; }
    .lineHeight-18-xs { line-height: 18px !important; }
    .lineHeight-19-xs { line-height: 19px !important; }
    .lineHeight-20-xs { line-height: 20px !important; }
    .lineHeight-21-xs { line-height: 21px !important; }
    .lineHeight-22-xs { line-height: 22px !important; }
    .lineHeight-23-xs { line-height: 23px !important; }
    .lineHeight-24-xs { line-height: 24px !important; }
    .lineHeight-25-xs { line-height: 25px !important; }
    .lineHeight-26-xs { line-height: 26px !important; }
    .lineHeight-27-xs { line-height: 27px !important; }
    .lineHeight-28-xs { line-height: 28px !important; }
    .lineHeight-29-xs { line-height: 29px !important; }
    .lineHeight-30-xs { line-height: 30px !important; }
    .lineHeight-31-xs { line-height: 31px !important; }
    .lineHeight-32-xs { line-height: 32px !important; }
    .lineHeight-33-xs { line-height: 33px !important; }
    .lineHeight-34-xs { line-height: 34px !important; }
    .lineHeight-35-xs { line-height: 35px !important; }
    .lineHeight-36-xs { line-height: 36px !important; }
    .lineHeight-37-xs { line-height: 37px !important; }
    .lineHeight-38-xs { line-height: 38px !important; }
    .lineHeight-39-xs { line-height: 39px !important; }
    .lineHeight-40-xs { line-height: 40px !important; }
}