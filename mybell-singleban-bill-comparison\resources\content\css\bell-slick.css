﻿
/*Slick Banner*/
/* Slider */
.slick-slider {
    position: relative;
    display: block;
    box-sizing: border-box;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -khtml-user-select: none;
    -ms-touch-action: pan-y;
    touch-action: pan-y;
    -webkit-tap-highlight-color: transparent;
}

.slick-list {
    position: relative;
    display: block;
    overflow: hidden;
    margin: 0;
    padding: 0;
}

    .slick-list:focus {
        outline: none;
    }

    .slick-list.dragging {
        cursor: pointer;
        cursor: hand;
    }

.slick-slider .slick-track,
.slick-slider .slick-list {
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    -o-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}

.slick-track {
    position: relative;
    top: 0;
    left: 0;
    display: block;
}

    .slick-track:before,
    .slick-track:after {
        display: table;
        content: '';
    }

    .slick-track:after {
        clear: both;
    }

.slick-loading .slick-track {
    visibility: hidden;
}

[dir='rtl'] .slick-slide {
    float: right;
}

.slick-slide img {
    display: block;
}

.slick-slide.slick-loading img {
    display: none;
}

.slick-slide.dragging img {
    pointer-events: none;
}

.slick-initialized .slick-slide {
    float: left;
    height: 100%;
    min-height: 1px;
    display: block;
    padding: 0;
}

.slick-loading .slick-slide {
    visibility: hidden;
}

.slick-vertical .slick-slide {
    display: block;
    height: auto;
    border: 1px solid transparent;
}

.slick-arrow.slick-hidden {
    display: none;
}

/* Arrows */
.slick-prev:hover,
.slick-prev:focus,
.slick-next:hover,
.slick-next:focus {
    color: transparent;
    background: transparent;
}

    .slick-prev:hover:before,
    .slick-prev:focus:before,
    .slick-next:hover:before,
    .slick-next:focus:before {
        opacity: 1;
    }

.slick-prev.slick-disabled:before,
.slick-next.slick-disabled:before {
    opacity: .25;
}

.slick-prev {
    left: -25px;
}

[dir='rtl'] .slick-prev {
    right: -25px;
    left: auto;
}

    [dir='rtl'] .slick-prev:before {
        content: '→';
    }

.slick-next {
    right: -25px;
}

[dir='rtl'] .slick-next {
    right: auto;
    left: -25px;
}

.slick-next:before {
    content: '→';
}

[dir='rtl'] .slick-next:before {
    content: '←';
}

/* Dots */
.slick-dots {
    position: relative;
    display: block;
    width: 100%;
    padding: 0;
    margin: 0 auto;
    list-style: none;
    text-align: center;
}

    .slick-dots li {
        position: relative;
        display: inline;
        padding: 0;
        cursor: pointer;
    }

        .slick-dots li button {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            padding: 0;
            border: 2px solid #999;
            margin: auto 4px;
            opacity: .5;
            font-size: 0;
            line-height: 0;
            cursor: pointer;
            color: transparent;
        }

            .slick-dots li button:hover:before,
            .slick-dots li button:focus:before {
                opacity: 1;
            }

            .slick-dots li button:before {
                font-family: 'slick';
                font-size: 6px;
                line-height: 20px;
                position: absolute;
                top: 0;
                left: 0;
                width: 20px;
                height: 20px;
                content: '�';
                text-align: center;
                opacity: .25;
                color: transparent;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }

        .slick-dots li.slick-active button {
            background: #555;
            opacity: 1;
        }


/*Custom CSS*/
.banner .container {
    position: inherit;
}

.slick-prev, .slick-next {
    font-size: 0;
    line-height: 0;
    top: calc(50% - 25px);
    display: block;
    margin-top: 0px;
    padding: 0;
    cursor: pointer;
    color: transparent;
    border: none;
    background: transparent;
    position: absolute;
    opacity: 0;
    box-shadow: 0 0 0 8px #e1e1e1;
    background-color: #fff;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    z-index: 19;
}

.banner:hover .slick-prev, .banner:hover .slick-next {
    opacity: 1;
}

.slick-prev:hover, .slick-next:hover, .slick-prev:focus, .slick-next:focus {
    background: #003676;
    box-shadow: 0 0 0 8px #e1e1e1;
}

    .slick-prev:hover::before, .slick-next:hover::before, .slick-next:focus::before, .slick-prev:focus::before {
        color: #fff;
    }

.slick-prev:before, .slick-next:before {
    top: calc(50% - 11px);
    margin:0 !important;
    position: absolute;
    line-height: 1;
    opacity: .75;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: '\e012';
    font-family: 'bell-icon';
    display: inline-block;
    font-size: 22px;
    color: #00549a;
    transition: all .25s cubic-bezier(.55,0,.1,1);
}

.slick-next:before {
    margin-right:-5px;
}

.slick-prev:before {
    margin-right:2px;
}


.slick-next:before {
    right: 12px;
}

.slick-prev:before {
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
    left: 12px;
}


.slick-prev:hover:before, .slick-next:hover:before {
    color:#00549a;

}

.banner-wrapper {
    display: table;
    table-layout: fixed;
    height: 500px;
}

.banner-content-message h2 {
    margin: 0;
}

.slickSlide {
    position: relative;
}

/*Slideshow Banner*/

.slideshow-banner .slick-dots li button:before {
    content: none;
    color: #111;
}


.slideshow-banner .slick-dots li.slick-active {
    border: 2px solid #003676;
    border-radius: 24px;
}

    .slideshow-banner .slick-dots li.slick-active button {
        background: none;
    }

.slideshow-banner .slick-dots li button {
    width: auto;
    height: 30px;
    padding: 5px;
    border: none;
    font-size: 16px;
    color: #111;
}

.progress-bar-wrapper:last-child > .progress {
    margin-bottom: 0;
}

.box-center img {
    margin: 0 auto;
}

.full-banner-img-center .banner-wrapper {
    width: 100%;
}

.slider-nav {
    max-width: 520px;
    height: 50px;
    margin: auto;
    margin-bottom: -50px;
    z-index: 500;
}

    .slider-nav .slick-track {
        height: 60px;
    }

.div-slider-nav {
    height: 40px;
    margin-right: 1px !important;
    margin-left: auto;
    margin-right: auto;
    display: inline;
    outline: 0;
}

.p-slider-nav {
    max-width: 150px;
    color: #003778;
    background-color: transparent;
    font-size: 18px;
    padding: 7px 0;
    text-align: center;
    cursor: pointer;
    margin: 0 auto;
}

.active-div-slider-nav .p-slider-nav {
    border: 2px solid #003778;
    border-radius: 24px;
}

.slider-for {
    max-width: 1200px;
    margin: auto;
}

    .slider-for .banner-wrapper {
        margin: auto;
    }

    .slider-for .banner-wrapper-content {
        width: 90%;
    }

    .slider-for .banner-content-message {
        right: 60px;
        width: 100%;
    }


.slider-box-row {
    margin-left: 0;
    margin-bottom: 20px;
}

    .slider-box-row h3, .slider-box-row h2 {
        margin-left: 20px;
        text-align: left;
        margin-top: 0px;
        margin-bottom: 5px;
    }

    .slider-box-row h3 {
        color: #111111;
    }

    .slider-box-row h2 {
        color: #00549A;
    }

.slider-for .progress {
    border-radius: 0;
    height: 10px;
}

.slider-for .progress-bar {
    background-color: #00549a;
}

.progress-bar-text {
    color: #111111;
    display: inline-block;
    margin-bottom: 6px;
}

#sb-250mbps {
    transition-duration: 1s;
}

#sb-50mbps {
    transition-duration: 2s;
}

#sb-10mbps {
    transition-duration: 4s;
}

.bgBlue-glow {
    background: #00549a; /* For browsers that do not support gradients */
    background: -webkit-radial-gradient(farthest-corner at 120px 250px, #449ae2 -40%, #00549a 60%); /* Safari 5.1 6.0 */
    background: -o-radial-gradient(farthest-corner at 120px 250px, #449ae2 -40%, #00549a 60%); /* For Opera 11.6 12.0 */
    background: -moz-radial-gradient(farthest-corner at 120px 250px, #449ae2 -40%, #00549a 60%); /* For Firefox 3.6 15 */
    background: radial-gradient(farthest-corner at 120px 250px, #fff -40%, #00549a 60%); /* Standard syntax 449ae2*/
}

.text-below-slider-image {
    position: absolute;
    top: 85%;
    left: 50%;
    margin-left: -160px;
}

.banner-content-message p {
    font-size: 14px;
    line-height: 20px;
    margin-top: 10px;
    color: #555;
}

.banner-img {
    display: table-cell;
    vertical-align: middle;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

.banner-content-message.box-left {
    left: 50px;
}

.banner-content-message.box-right {
    right: 50px;
}

.banner-img.box-left {
    left: 0;
}

.banner-img.box-right {
    right: 0;
}

/*Full banner image center*/

.full-banner-img-center .banner-img {
    display: block;
    vertical-align: middle;
    position: absolute;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.full-banner-img-center .banner-content-message {
    position: relative;
    background: none;
    border: none;
    max-width: 100%;
    padding: 30px;
    text-align: center;
    top: 0;
    -webkit-transform: translatey(0%);
    -ms-transform: translatey(0%);
    transform: translatey(0%);
}

@media (min-width: 1000px) {
    .banner-content-message {
        position: absolute;
        max-width: 450px;
        background-color: #fff;
        border: 1px solid #d4d4d4;
        padding: 40px;
        top: 50%;
        -webkit-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
        z-index: 15;
    }
}

@media (min-width: 640px) and (max-width: 999.98px) {
    .slider-nav {
        background-color: #003778;
        max-width: 999px;
        margin-bottom: 20px;
    }

    .div-slider-nav {
        width: 100% !important;
    }

    .p-slider-nav {
        padding-top: 10px;
        color: #fff;
        width: 100%;
        margin: auto;
    }

    .active-div-slider-nav .p-slider-nav {
        border: 0;
    }

    .active-nav-slide {
        width: 100%;
        border-radius: 0;
        display: block;
    }

    .div-slider-nav.slick-slide {
        display: none;
    }

        .div-slider-nav.slick-slide.active-div-slider-nav {
            width: 100% !important;
            display: block !important;
            margin: auto;
        }

    .slider-for .banner-content-message {
        right: 5%;
        width: 100%;
    }



    .slick-slider {
        margin-bottom: 0px;
    }

    .banner-wrapper {
        height: 325px;
    }

    .banner-img img {
        width: auto;
        height: 325px;
    }

    .slider-for .banner-img img {
        width: auto;
        height: 270px;
        vertical-align: middle;
    }

    .banner-content-message h2 {
        font-size: 24px;
        line-height: 26px;
    }

    .full-banner-img-center .banner-img {
        display: block;
        vertical-align: middle;
        position: absolute;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        max-width: 100%;
        height: auto;
    }

    .full-banner-img-center .banner-content-message {
        position: relative;
        background: none;
        border: none;
        max-width: 100%;
        padding: 30px;
        text-align: center;
        top: 0;
        -webkit-transform: translatey(0%);
        -ms-transform: translatey(0%);
        transform: translatey(0%);
    }

    .full-banner .banner-img img {
        display: block;
        max-width: 100%;
        height: 325px;
    }

    .banner-content-message {
        position: absolute;
        max-width: 300px;
        background-color: #fff;
        margin-left: auto;
        margin-right: 0;
        border: 1px solid #d4d4d4;
        padding: 40px;
        text-align: center;
        top: 50%;
        -webkit-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
        z-index: 99;
    }
        .banner-content-message.box-left {
            left: 0;
        }

        .banner-content-message.box-right {
            right: 0;
        }

    .slick-prev {
        left: -30px;
        z-index: 19;
    }

    .slick-next {
        right: -30px;
        z-index: 19;
    }

    .slick-prev, .slick-next {
        box-shadow: none;
    }

    .slider-for .slick-prev {
        left: 0px;
        z-index: 19;
    }

    .slider-for .slick-next {
        right: 0px;
        z-index: 19;
    }
}

@media (min-width:320px) and (max-width:639.98px) {
    .banner-img {
        margin: 0 auto;
        position: relative;
        left: 50%;
        transform: translateX(-50%) translateY(0%);
        -webkit-transform: translateX(-50%) translateY(0%);
        -ms-transform: translateX(-50%) translateY(0%);
    }

    .full-banner-img-center .banner-img {
        display: block;
        min-width: 300px;
    }

    .text-below-slider-image {
        top: 95%;
        text-align: center;
    }

    .banner-img.box-left {
        top: 30%;
        max-width: 60%;
        left: 50%;
    }

    .banner-img.box-right {
        top: 40%;
    }



    .banner-wrapper {
        height: 325px;
        margin: 0 auto;
        display: block;
    }

    .tab-content .banner-wrapper {
        height: 325px;
    }

    .slick-slide {
        height: 375px;
    }

    .banner-content-message h2 {
        letter-spacing: -.4px;
        font-size: 20px;
        text-align: center;
        font-weight: 500;
    }

    .full-banner-img-center .banner-content-message {
        position: relative;
        background: none;
        border: none;
        max-width: 100%;
        padding: 30px;
        text-align: center;
        top: 0;
        -webkit-transform: translatey(0%);
        -ms-transform: translatey(0%);
        transform: translatey(0%);
    }

    .full-banner .banner-img {
        bottom: 0;
        position: absolute;
        margin: 0 auto;
        left: 50%;
        transform: translate(-50%, -0%);
        z-index: -1
    }

        .full-banner .banner-img img {
            display: block;
            max-width: 450px;
            height: auto;
        }

    .banner-img img {
        display: block;
        max-width: 100%;
        height: auto;
    }

    .banner-content-message h2 {
        letter-spacing: -.4px;
        font-size: 20px;
        text-align: center;
        font-weight: 500;
        line-height: 22px;
    }

    .banner-content-message {
        max-width: 400px;
        line-height: 1.5;
        text-align: center;
        margin: 0 auto;
        padding: 20px 0px 0 0;
    }

    .slick-prev, .slick-next {
        box-shadow: none;
    }

    .slick-prev {
        left: -10px;
        z-index: 99;
    }

    .slick-next {
        right: -10px;
        z-index: 99;
    }

    .slider-nav {
        margin-bottom: 0px;
    }

    .slider-for .banner-wrapper {
        display: block;
    }

    .slider-for .banner-content-message {
        width: 250px;
        margin: 20px auto 0;
    }

    .slider-for .slick-dots {
        display: block;
    }

        .slider-for .slick-dots li button {
            border-color: #00549a;
            opacity: 1;
        }

        .slider-for .slick-dots li.slick-active button {
            background: #00549a;
        }

    .slider-for .slick-slide {
        height: 375px;
    }

    .hidden-mobile {
        display: none !important;
    }
}

@media (max-width: 999.98px) {
    .generic-banner .slick-prev, .generic-banner .slick-next, .slideshow-banner .slick-prev, .slideshow-banner .slick-next {
        background: none;
        box-shadow: none;
    }

    .slick-prev:before, .slick-next:before {
        color: #0e5ba1;
    }

    .slick-prev, .slick-next {
        opacity: 1;
        width: 50px;
        height: 50px;
        background-color:white;
        box-shadow: 0 14px 36px 0 rgb(0 0 0 / 30%);

    }

    .slick-prev:hover, .slick-next:hover {
        background-color: transparent;
        box-shadow: 0 14px 36px 0 rgb(0 0 0 / 30%);
        border:1px solid #0e5ba1;
        padding-top: 0;
    }

    .slick-prev:focus, .slick-next:focus, .slick-next:focus:before, .slick-prev:focus:before {
        background-color: transparent;
        box-shadow: 0 14px 36px 0 rgb(0 0 0 / 30%);
        border:1px solid #0e5ba1;
    }
}

@media (min-width: 1240px) {
    .generic-banner .slick-prev, .generic-banner .slick-next, .slideshow-banner .slick-prev, .slideshow-banner .slick-next {
        background: none;
        box-shadow: none;
    }

    .slick-prev:before, .slick-next:before {
        color: #0e5ba1;
    }

    .slick-prev, .slick-next {
        opacity: 1;
        width: 50px;
        height: 50px;
        background-color:white;
        box-shadow: 0 14px 36px 0 rgb(0 0 0 / 30%);
    }

        .slick-prev:hover, .slick-next:hover {
            background-color: transparent;
            box-shadow: 0 14px 36px 0 rgb(0 0 0 / 30%);
            border:1px solid #0e5ba1;
        }

        .slick-prev:focus, .slick-next:focus, .slick-next:focus:before, .slick-prev:focus:before {
            background-color: transparent;
            box-shadow: 0 14px 36px 0 rgb(0 0 0 / 30%);
            border:1px solid #0e5ba1;
        }
}


/*Slick Banner END*/


/*Slick Trends*/
.trends .slick-slider {
    height: auto;
    position: relative;
}

.trends .slider .slick-prev:hover, .trends .slider .slick-next:hover, .trends .slider .slick-prev:focus, .trends .slider .slick-next:focus, .trends .slider .slick-prev, .trends .slider .slick-next {
    opacity: 1;
    background: transparent;
    box-shadow: none;
}

.section-trend-slider .slick-prev:hover:before, .section-trend-slider .slick-next:hover:before, .section-trend-slider .slick-prev:active:before, .section-trend-slider .slick-next:active:before, .section-trend-slider .slick-prev:active, .section-trend-slider .slick-next:active, .section-trend-slider .slick-prev:hover, .section-trend-slider .slick-prev:focus, .section-trend-slider .slick-next:hover, .section-trend-slider .slick-next:focus, .section-trend-slider .slick-prev:before, .section-trend-slider .slick-next:before {
    color: #00549a;
}

.section-trend-slider .slick-track {
    display: flex;
}

.section-trend-slider .slickSlide {
    display: flex;
    height: auto;
}

.text-no-underline a {
    text-decoration: none;
}

    .text-no-underline a:before, .text-no-underline a:after, .text-no-underline a:hover:before, .text-no-underline a:hover:after {
        text-decoration: none;
    }


.trend-banner {
    width: 33.33%;
}

.trend-two-column-pad {
    padding: 45px 30px;
}

.trend-two-column-cont-pad {
    padding: 0 15px;
}

.container-flex-box {
    display: flex;
    display: -webkit-flex;
}

.justify-center {
    -webkit-justify-content: center;
    justify-content: center;
}

.align-items-center {
    align-items: center;
    -webkit-align-items: center;
}

.container, .pad-responsive, .col, [class^="col-"], [class*=" col-"] {
    transition: height .5s cubic-bezier(.55, 0, .1, 1), padding .5s cubic-bezier(.55, 0, .1, 1), margin .5s cubic-bezier(.55, 0, .1, 1);
}

.crop-img-center {
    margin: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-right: -50%;
    transform: translate(-50%, -50%);
}

.overflow-hidden {
    overflow: hidden;
}

.slider-one-view .trend-right-cont {
    min-height: 282px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

    .slider-one-view .trend-right-cont .icon {
        align-self: flex-start;
    }

.trends .slider .slick-prev:before, .trends .slider .slick-next:before {
    font-weight: bold;
    font-size: 24px;
}

.spacer25 {
    height: 25px;
}

.img-cover {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: 50%;
}

.trends .slick-dots {
    bottom: 5px;
    margin-left: -4px;
}

.trends .slick-prev, .trends .slick-next {
    font-size: 0;
    line-height: 0;
    position: absolute;
    margin-top: -10px;
    padding: 0;
    cursor: pointer;
    color: transparent;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border-top-left-radius: 50%;
    border-top-right-radius: 50%;
    border-bottom-right-radius: 50%;
    border-bottom-left-radius: 50%;
}

    .trends .slick-prev:before, .trends .slick-next:before {
        content: '\e012';
        font-family: 'bell-icon';
        display: inline-block;
        font-size: 22px;
        color: #00549a;
        transition: all .25s cubic-bezier(.55,0,.1,1);
        position: absolute;
    }

    .trends .slick-prev:before {
        -webkit-transform: rotate(180deg);
        -ms-transform: rotate(180deg);
        transform: rotate(180deg);
        top: 14px;
        left: 12px;
    }

    .trends .slick-next:before {
        top: 14px;
        right: 12px;
    }

.trends .slick-list {
    overflow: hidden;
}

/*Slick Trends*/

@media (min-width: 1000px) {

    .slider-two-slides .slick-track {
        width: 100% !important;
    }
}

@media (max-width: 999.98px) {
    .slick-prev:before, .slick-next:before {
        color: #0e5ba1;
    }
}

@media (max-width: 999.98px) and (min-width: 992px) {
    /* .slick-track{
        height:376px;
    } */

    .infoblock-slider-t{
        height:376px;
    }

    .infoblock-slider-ts{
        height:292px;
    }

    .slick-bggray-adjustment{
        margin-top:-25px;
    }

    .infoblock-slider-ts .cardslide {
        height: 292px;
    }
    /* .infoblock-slider-ts .slick-dots {
        margin-top:-80px;
    } */
}

@media (min-width: 768px) and (max-width: 999.98px) {
    .slider-two-slides {
        padding-left: 30px;
        padding-right: 30px;
    }

    .slick-prev {
        z-index: 19;
    }
}

@media (min-width: 768px) {
    .trends .generic-banner-with-slider {
        padding-left: 30px;
        padding-right: 30px;
        padding-bottom: 55px;
    }

    .trend-right-cont {
        width: 66.67%;
    }

    .trends .slick-next-with-slider, .trends .slick-next {
        right: -15px;
        top: calc(50% - 30px);
    }

    .trends .slick-prev-with-slider, .trends .slick-prev {
        left: -15px;
        top: calc(50% - 30px);
    }

    .trends .slick-dots-with-slider, .trends .slick-dots {
        width: calc(100% - 60px);
    }
}

@media (max-width: 767.98px) {
    .no-pad-xs {
        padding: 0;
    }

    .pad-40-bottom-xs {
        padding-bottom: 40px;
    }

    .pad-60-right-xs {
        padding-right: 60px;
    }

    .pad-60-left-xs {
        padding-left: 60px;
    }

    .pad-25-top-xs {
        padding-top: 25px;
    }

    .line-height-34-xs {
        line-height: 34px;
    }

    .trends .slick-next {
        right: 0;
        top: calc(50% - 17px);
    }

    .trends .slick-prev {
        left: 0;
        top: calc(50% - 17px);
    }

    .trends .slick-dots {
        bottom: 20px;
        z-index: 1;
        width: 100%;
    }

    .trends .slick-slider {
        padding-bottom: 30px;
        background-color: #fff;
    }
}
