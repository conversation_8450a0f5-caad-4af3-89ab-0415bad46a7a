.v-resp-header {
    height: 75px;
    background-color: #2D2D2D;
}

.txt-overflow-ellipsis {
    white-space: nowrap;
}

.container12 {
    max-width: 1199px;
    margin: 0 auto;
    padding: 0;
}


.bg-gray-ba {
    background-color: #babec2;
}

.bg-purewhite {
    background-color: #fff;
}


.bg-timberwolf-vm {
    background-color: #d7d7d7;
}

/*progress steps*/
.vsteps-views{height:107px;}
.progressive-steps .icon-circle-xsmall { color: #fff; display: inline-block; position: relative; border: 2px solid #D7D7D7; border-radius: 50%; }
.progressive-steps .icon-circle-xsmall.icon-circle_solid { background: #333; }
.progressive-steps .icon-circle-xsmall { height: 13px; width: 13px; }

.progressive-steps.complete .icon-circle-xsmall, .progressive-steps.active .icon-circle-xsmall { height: 24px; width: 24px; border: 5px solid #fff; }
.progressive-steps.complete .icon-circle-xsmall { background: #fff; }
.progressive-steps.complete .icon-check-mark:before { font-size: 7px; top: -2px; position: relative; }
.progressive-steps.next .icon-circle-xsmall { height: 30px; width: 30px; }
.progressive-steps .icon.icon-check::before { font-size: 11px; }
.progressive-steps .sm-tick-icon .icon-check::before { font-size: 15px; color: #fff; }  
.progressive-steps-line {position: relative;top: 17px;margin: 0 100px;height: 2px;}
.progressive-steps-line.one-step { margin: 0 200px; }
.progressive-steps-line.two-steps { margin: 0 25%; }
.progressive-steps-line.three-steps { margin: 0 17%; }
.progressive-steps-line.four-steps { margin: 0 12.5%; }
.progressive-steps-line.five-steps { margin: 0 10%; }
.progressive-steps-line.six-steps { margin: 0 8%; } 
  
.progressive-mobile-progressbar { display: none; width: 100%; margin: 0px 0 0 0; }
.progressive-mobile-bar-left { background-color: #fff; height: 4px; }
.progressive-mobile-bar-right { background-color: #babec2; height: 2px; margin-top: 1px; }
.progressive-steps.left-side .icon-circle-xsmall { position: relative; left: -8px; top: -10px; height: 15px; width: 15px; overflow: hidden; }
.progressive-steps.right-side .icon-circle-xsmall { position: relative; right: -8px; top: -9px; height: 15px; width: 15px; overflow: hidden; }
.progressive-steps.active.mobile .icon-circle-xsmall { position: relative; top: -16px; }
 
/*progress for 6 steps only */
.progressive-steps-progress-1-of-6 { position: relative; top: 15px; margin: 0 8%; width: 0; height: 2px; }
.progressive-steps-progress-2-of-6 { position: relative; top: 15px; margin: 0 8%; width: calc(100% / 6); height: 2px; }
.progressive-steps-progress-3-of-6 { position: relative; top: 15px; margin: 0 8%; width: calc(100% / 3); height: 2px; }
.progressive-steps-progress-4-of-6 { position: relative; top: 15px; margin: 0 8%; width: calc(100% / 2); height: 2px; }
.progressive-steps-progress-5-of-6 { position: relative; top: 15px; margin: 0 8%; width: calc(100% / 1.5); height: 2px; }
.progressive-steps-progress-6-of-6 { position: relative; top: 15px; margin: 0 8%;  width: auto; height: 2px; }

/*progress for 5 steps only */
.progressive-steps-progress-1-of-5 { position: relative; top: 15px; margin: 0 10%; width: 0; height: 2px; }
.progressive-steps-progress-2-of-5 { position: relative; top: 15px; margin: 0 10%; width: calc(100% / 5); height: 2px; }
.progressive-steps-progress-3-of-5 { position: relative; top: 15px; margin: 0 10%; width: calc(100% / 2.5); height: 2px; }
.progressive-steps-progress-4-of-5 { position: relative; top: 15px; margin: 0 10%; width: calc(100% / 1.66); height: 2px; }
.progressive-steps-progress-5-of-5 { position: relative; top: 15px; margin: 0 10%; width: auto; height: 2px; } 

/*progress for 4 steps only */
.progressive-steps-progress-1-of-4 { position: relative; top: 15px; margin: 0 12.5%; width: 0; height: 2px; }
.progressive-steps-progress-2-of-4  { position: relative; top: 15px; margin: 0 12.5%; width: calc(100% / 3.9); height: 2px; }
.progressive-steps-progress-3-of-4  { position: relative; top: 15px; margin: 0 12.5%; width: calc(100% / 2); height: 2px; }
.progressive-steps-progress-4-of-4  { position: relative; top: 15px; margin: 0 12.5%; width: auto; height: 2px; } 


/*progress for 3 steps only */
.progressive-steps-progress-1-of-3 { position: relative; top: 15px; margin: 0 17%; width: 0; height: 2px; }
.progressive-steps-progress-2-of-3  { position: relative; top: 15px; margin: 0 17%; width: calc(100% / 3); height: 2px; }
.progressive-steps-progress-3-of-3  { position: relative; top: 15px; margin: 0 17%; width: auto; height: 2px; } 

/*progress for 2 steps only */
.progressive-steps-progress-1-of-2 { position: relative; top: 15px; margin: 0 25%; width: 0; height: 2px; } 
.progressive-steps-progress-2-of-2  { position: relative; top: 15px; margin: 0 25%; width: auto; height: 2px; } 




@media screen and (max-width:767.98px) {
    .v-resp-header {
        height: 54px;
    }
}

@media screen and (min-width:1240px) {
    .container12.liquid-container12, .container12 {
        width: 1200px;
        padding: 0;
    }
}

