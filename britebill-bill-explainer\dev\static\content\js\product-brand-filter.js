(function ($) {

    var $brandFilters, $brandFiltersContent, $brands, $moreButton;  

    function init(){
        $brandFilters = $(".prod-filter"),
        $brandFiltersContent = $brandFilters.find(".prod-filter-content"),
        $brands = $brandFilters.find(".prod-filter-brands"),    
        $moreButton = $brandFilters.find(".prod-filter-more-btn");

        $brands.on("click", "button", onBrandClicked);

        $moreButton.on("click", function(){
            $brands.find(".d-none").removeClass("d-none");
            $moreButton.parent().addClass("d-none");
        });
    }

    function onBrandClicked(e){

        var $activeBrand = $(e.target);

        var activeBrandPosition = Math.ceil($activeBrand.position().left);
        var brandFiltersPosition =  $brandFilters.scrollLeft();
        var scrollToPosition = brandFiltersPosition + activeBrandPosition - $(window).width() / 2 + $activeBrand.width();

        $brands.children().each(function(){
            $(this).attr("aria-pressed", false);
        });

        $activeBrand.attr("aria-pressed", true);        
        $brandFilters.animate({scrollLeft: scrollToPosition}, "slow"); 
    }

    $(document).ready(init);

})(jQuery);