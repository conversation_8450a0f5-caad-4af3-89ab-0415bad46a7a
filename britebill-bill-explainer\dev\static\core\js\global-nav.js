//v1.1
//Updated 2019 Aug 1
var GlobalNav = {
    $GNactiveLob: $('.connector-active-lob'),

    init: function () {
        try {
            this.$GNactiveLob.animate({
                scrollLeft: Math.ceil($('div.connector-active-lob li.active').offset().left)
            }, 'slow');
        }

        catch (err) { }

    },
    saveLanguage: function ($language) {
        var language = $language.data("alternative");
        var vanityurl = $language.data("vanityurl");
        this.setLanguageRegionCookieValue(language, null);
        var langRegex = /(language=\w+)|(lang=\w+)/i;

        if (vanityurl && vanityurl.length > 0) {
            if (langRegex.test(vanityurl)) {
                window.location = vanityurl.replace(langRegex, "lang=" + language);
            } else {
                window.location = vanityurl;
            }
        } else if (langRegex.test(location.href)) {
            location.href = location.href.replace(langRegex, "lang=" + language);
        } else {
            window.location.reload();
        }
    },
    saveProvince: function (province) {
        var language = $(".js-current-language").data("language");
        this.setLanguageRegionCookieValue(language, province);
        this.paramURLCheck("prov=", province);
        window.location.reload();
    },
    paramURLCheck: function (text, value) {
        var urlParam = window.location.search.split('&');
        var index = -1;
        var paramIndex = $(urlParam).each(function (listIndex) {
            if (this.indexOf(text) !== -1) {
                index = listIndex;
            }
        });
        if (index !== -1) {
            var pramString = text;
            if (index == 0) {
                pramString = "?" + text;
            }
            urlParam[index] = pramString + value;
            window.history.pushState("{\"location\":\"" + urlParam.join('&') + "\"}", urlParam.join('&'), urlParam.join('&'));
        }
    },
    setLanguageRegionCookieValue: function (lang, region, larsegmenttype, bIgnorePreviousCookie) {
        var geminiCookieName = "gemini";
        var cookieVal = bIgnorePreviousCookie == true ? null : this.getCookie(geminiCookieName);
        var geminiCookieVal = this.getGeminiString(lang, region, cookieVal, larsegmenttype);
        var hostname = window.location.hostname;
        var domain = ".bell.ca";
        this.setCookie(geminiCookieName, geminiCookieVal, 90, domain);
    },
    getGeminiString: function (lang, region, cval, larsegmenttype) {
        var retVal = "region=" + region + "|language=" + lang + "|province=" + region + "|LarSegmentType=" + larsegmenttype;
        if (cval != null) {
            var strSpl = cval.split('|');
            if (region != null && region.length != 0) {
                strSpl[0] = "region=" + region;
                strSpl[2] = "province=" + region;
            }
            if (lang != null && lang.length != 0) {
                strSpl[1] = "language=" + lang;
            }
            if (larsegmenttype != null && larsegmenttype != undefined && larsegmenttype.length != 0) {
                strSpl[3] = "LarSegmentType=" + larsegmenttype;
            }
            retVal = strSpl[0] + "|" + strSpl[1] + "|" + strSpl[2] + "|" + strSpl[3];
        }

        return retVal;
    },

    setCookie: function (name, value, days, domain) {
        var expires = "", date = new Date();
        if (!days || isNaN(days)) {
            days = 365;
        }

        date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
        expires = "; expires=" + date.toGMTString();
        var setCookieVal = name + "=" + value + expires + "; path=/";

        if (domain) {
            setCookieVal += "; domain=" + domain;
        }

        document.cookie = setCookieVal;
    },

    getCookie: function (name) {
        var value = "; " + document.cookie;
        var parts = value.split("; " + name + "=");
        if (parts.length == 2) {
            return parts.pop().split(";").shift();
        }
    }
}

$(document).ready(function () {
    GlobalNav.init();

    $('body').append('<div class="screen" aria-hidden="true"></div>');

    //Start Federal Bar Events
    $(document).on('click', '.federal-bar-store-locator a', function (e) {
        $('li.connector-area').removeClass('active').find('.menu-flyout').removeClass('menu-flyout-visible').removeClass('menu-flyout-has-been-expanded');
        $('.federal-bar-link-provinces').removeClass('active');
        $('.federal-bar-store-locator-popup').addClass('federal-bar-links');
        $(this).parent().toggleClass('active');
        //e.stopPropagation();
    });

    $(document).on('click', '.show-store-locator', function (event) {
        $this = $(this);
        $this.closest('.federal-bar-store-locator-popup').toggleClass('federal-bar-links');
        $this.closest('.federal-bar-store-locator-popup').find('#searchField').focus();
        //event.stopPropagation();
    });

    $(document).on('click', '.federal-bar-store-locator-popup', function (e) {
        return false;
    });

    $(document).on('click', '.js-current-language', function () {
        GlobalNav.saveLanguage($(this));
    });

    $(document).on("change", ".js-province-mobile", function () {
        GlobalNav.saveProvince(this.value);
    });

    $(document).on('click', '.checkboxes a.province-item', function (e, data) {
        GlobalNav.saveProvince($(this).find("input").val());
    });

    $(document).on('click', 'label.label', function (event) {
        var $this = $(this);
        if ($this.hasClass('active')) {
            $this.find('.store-locator-filter-checkbox').prop('checked', false);
        } else {
            $this.find('.store-locator-filter-checkbox').prop('checked', true);
        }
        $this.toggleClass('active').toggleClass('focused');
        event.stopPropagation();
        return false;
    });

    $(document).on('focusin', '.store-locator-filter-checkbox', function () {
        $(this).closest('.label').addClass('focused');
    });

    $(document).on('focusout', '.store-locator-filter-checkbox', function () {
        $(this).closest('.label').removeClass('focused');
    });

    $(document).on('click', '.connector-cart-button', function () {
        $(this).closest('.shopping-cart-button').toggleClass('active');
    });
    $(document).on('click', '.shopping-cart-popup', function () {
        return false;
    });

    $(document).on('click', '.footer-header-current-province', function (e) {
        $('li.connector-area').removeClass('active').find('.menu-flyout').removeClass('menu-flyout-visible').removeClass('menu-flyout-has-been-expanded');
        $('.federal-bar-store-locator').removeClass('active');
        $('.federal-bar-store-locator-popup').addClass('federal-bar-links');
        $('.federal-bar-link-provinces').toggleClass('active');
        //e.stopPropagation();
    });

    $(document).on('keyup', '.ui-autocomplete-input', function () {
        var $this = $(this);
        if ($this.val().length > 0) {
            $('button[type="reset"]').addClass('active');
        } else {
            $('button[type="reset"]').removeClass('active');
        }
    });

    $(document).on('click', 'button[type="reset"]', function () {
        $('button[type="reset"]').removeClass('active');
    });

    $(document).on('click', '#connector-search-button', function (e) {
        $this = $(this);
        $this.toggleClass('active');
        $this.closest('.connector').toggleClass('connector-search-active');
        $('.connector-search-wrap').toggleClass('active');
        $('.connector-search-wrap input').focus();
    });
    //End of Federal Bar Events


    $(document).on('click', 'li.connector-area', function (event) {
        //if (window.matchMedia("(max-width: 991.98px)").matches) {
        $('.federal-bar-store-locator').removeClass('active');
        $('.federal-bar-link-provinces').removeClass('active');
        $('.federal-bar-store-locator-popup').addClass('federal-bar-links');
        var $this = $(this);
        if ($this.hasClass('active') && $this.find('.menu-flyout').hasClass('menu-flyout-visible')) {
            $this.removeClass('bce-no-flyout-open').removeClass('active').find('.menu-flyout').removeClass('menu-flyout-visible').removeClass('menu-flyout-has-been-expanded');
            $('.menu-flyout-item-active').removeClass('menu-flyout-item-active');
        } else {
            $('li.connector-area').removeClass('bce-no-flyout-open').removeClass('active').find('.menu-flyout').removeClass('menu-flyout-visible').removeClass('menu-flyout-has-been-expanded');
            $('.menu-flyout-item-active').removeClass('menu-flyout-item-active');
            $this.addClass("active");
            $this.find('.menu-flyout').addClass('menu-flyout-visible');
        }
        //}
        event.stopPropagation();
    });
    $(document).on('click', '.sub-nav-root > li', function (event) {
        var $this = $(this), subNavGroupEl;

        if ($this.hasClass('menu-flyout-item-active')) {
            if (window.matchMedia("(max-width: 991.98px)").matches) {
                $('.sub-nav-root > li').removeClass('menu-flyout-item-active');
            }
        } else {
            $('.sub-nav-root > li').removeClass('menu-flyout-item-active');
            $this.addClass("menu-flyout-item-active");
            if (!$this.hasClass('no-sub-nav')) {

                if (!$this.closest('.menu-flyout-visible').hasClass('menu-flyout-has-been-expanded')) {
                    $this.closest('.menu-flyout-visible').addClass('menu-flyout-has-been-expanded');
                    if (window.matchMedia("(min-width: 992px)").matches) {
                        subNavGroupEl = $this.find('.sub-nav-group');
                        subNavGroupEl.first().width(0).animate({ "width": subNavGroupEl.hasClass('has-two-columns') ? '180%' : (subNavGroupEl.hasClass('sub-nav-large') ? '320%' : '100%') }, 225, function () {});
                    }
                }
            }
        }
        event.stopPropagation();
    });
    $(document).on('mouseover', '.sub-nav-root > li', function (event) {
        if (window.matchMedia("(min-width: 992px)").matches) {
            $(this).trigger('click');
        }
        event.stopPropagation();
    });

    $(document).on('click', '.sub-nav-level4 > li', function (event) {
        var $this = $(this);
        if ($this.hasClass('menu-flyout-item-active')) {
            if (window.matchMedia("(max-width: 991.98px)").matches) {
                $('.sub-nav-level4 > li').removeClass('menu-flyout-item-active');
            }
        } else {
            $('.sub-nav-level4 > li').removeClass('menu-flyout-item-active');
            $this.addClass("menu-flyout-item-active");
            if (!$this.hasClass('no-sub-nav')) {

                if (!$this.closest('.menu-flyout-visible').hasClass('menu-flyout-has-been-expanded')) {
                    $this.closest('.menu-flyout-visible').addClass('menu-flyout-has-been-expanded');
                    if (window.matchMedia("(min-width: 992px)").matches) {
                        $this.find('.sub-nav-group').width(0).animate({ "width": "100%" }, 225, function () { });
                    }
                }
            }
        }
        event.stopPropagation();
    });

    $(document).on('mouseover', '.sub-nav-level4 > li', function (event) {
        if (window.matchMedia("(min-width: 992px)").matches) {
            $(this).trigger('click');
        }
        event.stopPropagation();
    });

    $(document).on('click', '.connector-nav-open-button, .screen', function () {
        $this = $('.connector-nav-open-button');
        $('body').toggleClass('connector-active');
        $this.toggleClass('active');
        if ($this.hasClass('active')) {
            $this.attr('title', 'Close Mobile Navigation');
        } else {
            $this.attr('title', 'Open Mobile Navigation');
        }
    });

    $(document).on('click', function (e) {
        if (window.matchMedia("(min-width: 992px)").matches) {
            $('li.connector-area').removeClass('active').find('.menu-flyout').removeClass('menu-flyout-visible').removeClass('menu-flyout-has-been-expanded');
            $('.federal-bar-store-locator').removeClass('active');
            $('.federal-bar-link-provinces').removeClass('active');
            $('.federal-bar-store-locator-popup').addClass('federal-bar-links');
            $('.shopping-cart-button').removeClass('active');
        }
    });


    //Secondary Navigation

    $(document).on('focusin', '.global-navigation .secondary-nav-lob', function () {
        $(this).closest('li').find('.secondary-nav-dropdown').show();
    });

    $(document).on('focusout', '.global-navigation .secondary-nav-dropdown a:last-child', function () {
        $(this).closest('li').find('.secondary-nav-dropdown').hide();
    });

    $(document).on('mousemove', function () {
        $('body').find('.secondary-nav-dropdown').hide();
    });



})