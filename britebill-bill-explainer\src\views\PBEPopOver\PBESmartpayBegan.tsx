import * as React from "react";
import { InjectedIntlProps, injectIntl } from "react-intl";
import { <PERSON><PERSON><PERSON>eader, PBEFooter } from "singleban-components";
import { IPBE } from "../../models";
import { CURRENCY_OPTIONS, DATE_OPTIONS, modalOpenedOmniture } from "../../utils/Utility";

interface Props {
    pbe: IPBE;
    isPBEModalLinkDisabled: boolean;
}

const PBESmartpayBegan = (props: Props & InjectedIntlProps) => {
    const { intl: { formatMessage, formatNumber, formatDate }, pbe } = props;
    const title = formatMessage({ id: "PBE_SMARTPAY_BEGAN_TITLE" });
    const description = formatMessage({ id: "PBE_SMARTPAY_BEGAN_DESC" }, {
        startDate: formatDate(new Date(pbe?.pbeDataBag?.startDate), DATE_OPTIONS),
        endDate: formatDate(new Date(pbe?.pbeDataBag?.endDate), DATE_OPTIONS)
    });
    const imageClassName = "icon-07_bill_circle";
    const PBEFooterItems = [{
        ctaLink: formatMessage({ id: "SMARTPAY_BEGAN_SEE_AGREEMENT_LINK" }, {
            acctNo: pbe?.pbeDataBag?.encryptAcctNo,
            subNo: pbe?.pbeDataBag?.subNo,
        }),
        iconClassName: "icon-07_bill",
        titleKey: formatMessage({ id: "SMARTPAY_BEGAN_SEE_AGREEMENT_TITLE" }),
        ctaTitleKey: formatMessage({ id: "SMARTPAY_BEGAN_SEE_AGREEMENT_SUBTITLE" }),
        isFirstRow: true,
        id: "pbe-smartpay-began-see-agreement"
    }];

    React.useEffect(() => {
        modalOpenedOmniture(props?.pbe?.triggerPbeOmniture, title, description);
    },[title, description]);
    return (
        <>
            <PBEHeader descriptionKey={description} titleKey={title} iconClassName={imageClassName} />
            <div className="margin-b-30 margin-h-xs-15 margin-h-30">
                <div className="box-round-grey pad-xs-15 pad-20">
                    <div className="d-block relative">
                        <div className="d-flex">
                            <span className="margin-r-xs-15 margin-r-20">
                                <span className="surtitle-black">{formatMessage({ id: "SMARTPAY_DEVICE_PURCHASE_PRICE" })}</span>
                                <span className="sr-only">&nbsp;</span>
                            </span>
                            <span className="d-flex container-flex-grow-fill justify-end">
                                <span className="d-inline noTxt txtRight">
                                    <span className="surtitle-black text-nowrap txtSize14 line-height-18" aria-hidden="true">{formatNumber(pbe?.pbeDataBag?.devicePurchasePrice, CURRENCY_OPTIONS)}</span>
                                    <span className="sr-only">{formatNumber(pbe?.pbeDataBag?.devicePurchasePrice, CURRENCY_OPTIONS)}</span>
                                </span>
                            </span>
                        </div>
                        <div className="d-flex margin-t-10">
                            <span className="margin-r-xs-15 margin-r-20">
                                <span className="surtitle-black">{formatMessage({ id: "SMARTPAY_AGREEMENT_CREDIT" })}</span>
                                <span className="sr-only">&nbsp;</span>
                            </span>
                            <span className="d-flex container-flex-grow-fill justify-end">
                                <span className="d-inline noTxt txtRight">
                                    <span className="surtitle-black text-nowrap txtSize14 line-height-18" aria-hidden="true">{formatNumber(pbe?.pbeDataBag?.agreementCredit * -1, CURRENCY_OPTIONS)}</span>
                                    <span className="sr-only">{formatNumber(pbe?.pbeDataBag?.agreementCredit * -1, CURRENCY_OPTIONS)}</span>
                                </span>
                            </span>
                        </div>
                    </div>
                    <hr className="margin-v-15 border-lightGray" aria-hidden="true" />
                    <div className="d-block relative">
                        <div className="d-flex">
                            <span className="margin-r-xs-15 margin-r-20">
                                <span>{formatMessage({ id: "SMARTPAY_REDUCED_DEVICE_PRICE" })}</span>
                                <span className="sr-only">&nbsp;</span>
                            </span>
                            <span className="d-flex container-flex-grow-fill justify-end">
                                <span className="d-inline noTxt txtRight">
                                    <span className="text-nowrap txtSize14 line-height-18" aria-hidden="true">{formatNumber(pbe?.pbeDataBag?.reducedDevicePrice, CURRENCY_OPTIONS)}</span>
                                    <span className="sr-only">{formatNumber(pbe?.pbeDataBag?.reducedDevicePrice, CURRENCY_OPTIONS)}</span>
                                </span>
                            </span>
                        </div>
                        <div className="d-flex margin-t-10">
                            <span className="margin-r-xs-15 margin-r-20">
                                <span>{formatMessage({ id: "SMARTPAY_TAXES" })}</span>
                                <span className="sr-only">&nbsp;</span>
                            </span>
                            <span className="d-flex container-flex-grow-fill justify-end">
                                <span className="d-inline noTxt txtRight">
                                    <span className="text-nowrap txtSize14 line-height-18" aria-hidden="true">{formatNumber(pbe?.pbeDataBag?.deviceTax, CURRENCY_OPTIONS)}</span>
                                    <span className="sr-only">{formatNumber(pbe?.pbeDataBag?.deviceTax, CURRENCY_OPTIONS)}</span>
                                </span>
                            </span>
                        </div>
                    </div>
                    <hr className="margin-v-15 border-lightGray" aria-hidden="true" />
                    <div className="d-block relative">
                        <div className="d-flex">
                            <span className="margin-r-xs-15 margin-r-20">
                                <span>{formatMessage({ id: "SMARTPAY_TOTAL_DEVICE_PRICE" })}</span>
                                <span className="sr-only">&nbsp;</span>
                            </span>
                            <span className="d-flex container-flex-grow-fill justify-end">
                                <span className="d-inline noTxt txtRight">
                                    <span className="text-nowrap txtSize14 line-height-18" aria-hidden="true">{formatNumber(pbe?.pbeDataBag?.totalDevicePrice, CURRENCY_OPTIONS)}</span>
                                    <span className="sr-only">{formatNumber(pbe?.pbeDataBag?.totalDevicePrice, CURRENCY_OPTIONS)}</span>
                                </span>
                            </span>
                        </div>
                    </div>
                    <hr className="margin-v-15 border-lightGray" aria-hidden="true" />
                    <div className="d-block relative">
                        <div className="d-flex">
                            <span className="margin-r-xs-15 margin-r-20">
                                <span>{formatMessage({ id: "SMARTPAY_DOWN_PAYMENT" })}</span>
                                <span className="sr-only">&nbsp;</span>
                            </span>
                            <span className="d-flex container-flex-grow-fill justify-end">
                                <span className="d-inline noTxt txtRight">
                                    <span className="text-nowrap txtSize14 line-height-18" aria-hidden="true">{formatNumber(pbe?.pbeDataBag?.downPayment * -1, CURRENCY_OPTIONS)}</span>
                                    <span className="sr-only">{formatNumber(pbe?.pbeDataBag?.downPayment * -1, CURRENCY_OPTIONS)}</span>
                                </span>
                            </span>
                        </div>
                        <div className="d-flex margin-t-10">
                            <span className="margin-r-xs-0 margin-r-20">
                                <span className="surtitle-black">{formatMessage({ id: "SMARTPAY_DEVICE_RETURN_OPTION" })}</span>
                                <span className="sr-only">&nbsp;</span>
                            </span>
                            <span className="d-flex container-flex-grow-fill justify-end">
                                <span className="d-inline noTxt txtRight">
                                    <span className="surtitle-black text-nowrap txtSize14 line-height-18" aria-hidden="true">{formatNumber(pbe?.pbeDataBag?.deviceReturnOption * -1, CURRENCY_OPTIONS)}</span>
                                    <span className="sr-only">{formatNumber(pbe?.pbeDataBag?.deviceReturnOption * -1, CURRENCY_OPTIONS)}</span>
                                </span>
                            </span>
                        </div>
                    </div>
                    <hr className="margin-v-15 border-lightGray" aria-hidden="true" />
                    <div className="d-block relative">
                        <div className="d-flex">
                            <span className="margin-r-xs-15 margin-r-20">
                                <span className="surtitle-black">{formatMessage({ id: "SMARTPAY_FINANCED_AMOUNT" })}</span>
                                <span className="sr-only">&nbsp;</span>
                            </span>
                            <span className="d-flex container-flex-grow-fill justify-end">
                                <span className="d-inline noTxt txtRight">
                                    <span className="surtitle-black text-nowrap txtSize14 line-height-18" aria-hidden="true">{formatNumber(pbe?.pbeDataBag?.financedAmount, CURRENCY_OPTIONS)}</span>
                                    <span className="sr-only">{formatNumber(pbe?.pbeDataBag?.financedAmount, CURRENCY_OPTIONS)}</span>
                                </span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            {pbe?.pbeDataBag?.isSubscriberCancelled ? null : <PBEFooter footerItems={PBEFooterItems} isPBEModalLinkDisabled={props.isPBEModalLinkDisabled} />}
        </>
    );
};


export default (injectIntl(PBESmartpayBegan));