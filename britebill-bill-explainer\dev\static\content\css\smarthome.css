.modified-tooltip .tooltip {
    max-width: 225px;
}

.modified-tooltip .tooltip-inner {
    padding-right: 15px;
    padding-left: 30px;
    line-height: 18px;
}

table.table-td-50 td {
    height: 50px;
}

.long-text-tag-center{
    width: 100%;
    text-align: center;
}

.min-height-80{min-height:80px}

.width-260 {
    width: 260px;
}
.width-290 {
    width: 290px;
}
.height-10 {
    height: 10px;
}
.height-225, textarea.form-control.height-225 {
    height: 225px;
}
.height-135, textarea.form-control.height-135 {
    height: 135px;
}

.txtSize36 {
    font-size: 36px;
}

.txtSize27 {
    font-size: 27px;
}

.borderRadiusAll4 {
    border-radius: 4px;
}

.min-height-465 {
    min-height: 465px;
}

.subtitle-2-reg-2 {
    color: #111;
    letter-spacing: normal;
    line-height: 22px;
    font-size: 18px;
    font-weight: 400;
}

.small-title-2 {
    font-family: "bellslim_font_black", Helvetica, Arial, sans-serif;
    font-weight: 400;
    letter-spacing: -.4px;
    font-size: 24px;
    line-height: 26px;
    color: #111;
}

.bg-image-cover-no-repeat-bottom {
    background-size: cover;
    background-position: bottom;
    background-repeat: no-repeat;
}

.min-height-260{
    min-height: 260px;
}

.height-440 {
    height: 440px;
}
.line-height-25 {
    line-height: 25px
}

.sub-banner-large .banner-image > img, .primary-offers .graphic-product-card .offer-image > img, .huge-offer .image-banner > img, .banner-image.banner-image-2 > img, .img-responsive {
    max-width: 100%;
    flex-shrink: 0;
}

.sticky-first-column, .sticky-first-column-modal {
    padding: 15px 20px;
}

.table-scrollable-wrapper tbody td {
    padding: 0;
}

.table-scrollable-wrapper tbody td div.inner-content {
    padding: 15px 20px;
}

.txtSize8 {
    font-size: 8px;
}

.max-width-unset {
    max-width: unset;
    max-width: none;
}

.icon-dl-app-height {
    height: 36px;
}

.sticky-first-column-cont {
    width: 33%;
    min-width: 33%;
}

.table-options th {
    width: 50%;
}

.sticky-first-column-cont > div:nth-child(odd) .sticky-first-column {
    background-color: #f4f4f4;
}

.absolute-bottom {
    position: absolute;
    bottom: 0;
}

.bundle-and-save-image {
    height: 127px;
}

.margin-neg-20-l {
    margin-left: -20px;
}

.topCenter-centered {
    top: 0;
    left: 50%;
    transform: translate(-50%,-50%);
}

.vaa-plus-choice {
    height: 72px;
    width: 198px;
}

/*Radio Button*/
.radio-container {
    border: 1px solid #D4D4D4;
    background-color: #FFFFFF;
    box-shadow: 0 2px 3px 0 rgba(0,0,0,0.2);
    padding: 24px;
    border-radius: 4px;
}

.radio-container {
    padding: 15px;
    /*height: 54px;*/
    display: flex;
    align-items: center;
}

.graphical_ctrl input:checked ~ span {
    color: #111;
}

.ctrl_radioBtn .ctrl_element:after {
    left: 5px;
    top: 5px;
    height: 13px;
    width: 13px;
    border-radius: 50%;
    background: #fff;
}

.ctrl_element {
    position: absolute;
    top: -3px;
    left: 0;
    height: 25px;
    width: 25px;
    background: #fff;
    border: 1px solid #ccc;
}

.v-top {
    top: 50%;
    transform: translateY(-70%);
}

.v-center {
    top: 50%;
    transform: translateY(-50%);
}

.graphical_ctrl input:checked ~ * {
    color: #003778;
}

.graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element, .graphical_ctrl input[type="radio"]:focus ~ .ctrl_element {
    outline: none;
}

.focused-element {
    outline-width: 2px;
    outline-style: solid;
    outline-color: #4d90fe;
    outline-offset: 2px;
}

.checkbox-container.checked-border, .radio-container.checked-border {
    border: 2px solid #003778;
}

.display-radio-container {
    display: none;
}

.border-2-left-gray2 {
    border-left: 2px solid #e1e1e1;
}

.spacer74 {
    height: 74px;
}

.form-group.error .error-description {
    display: flex;
}

.form-group .error-description {
    display: none;
}

.label-required::after{
    content:'*';
    display:inline-block;
    margin-right:.25em;
}

.sup-align-top-smaller {
    font-size: 0.65em;
    line-height: 0;
    position: relative;
    top: -.5em;
    vertical-align: baseline;
}

.price_inactive {
    text-decoration: line-through;
    color: #BABEC2;
}

.product-badge-clearance, .product-badge-sale {
    padding: 5px;
    background-color: #00549a;
    border-radius: 50%;
    color: #fff;
    line-height: 1;
    text-align: center;
    width: 60px;
    height: 60px;
    position: absolute;
    display: flex;
    box-shadow: 0 0 30px rgba(0,0,0,0.3);
}

.product-badge-clearance, .product-badge-sale {
    top: 0;
    left: 0;
}

    .product-badge-clearance span {
        font-size: 8px;
        font-weight: bold;
        text-transform: uppercase;
    }

    .product-badge-sale span {
        font-size: 14px;
        font-weight: bold;
        text-transform: uppercase;
    }


@media (min-width: 1200px) {
    .pad-h-lg-60{
        padding-left: 60px;
        padding-right: 60px;
    }
}


@media (min-width: 768px) and (max-width: 991.98px) {
    .banner-crop-img.rightView-0-sm {
        position: absolute;
        height: 100%;
        width: auto;
        top: 50%;
        right: 0%;
        transform: translate(0%,-50%);
    }

    .banner-crop-img.rightView-10-sm {
        position: absolute;
        height: 100%;
        width: auto;
        top: 50%;
        right: -10%;
        transform: translate(0%,-50%);
    }

    .banner-crop-img.leftView-10-sm {
        position: absolute;
        height: 100%;
        width: auto;
        top: 50%;
        left: 10%;
        transform: translate(-10%,-50%)
    }

    .banner-crop-img.leftView-10-sm.getty-image {
        width: 100%
    }

    .bundle-and-save-image {
        height: 155px;
    }

    .vaa-plus-choice {
        width: 179px;
    }

    .margin-neg-15-l-sm {
        margin-left: -15px;
    }

    .img-increase-size-30-sm{
        position: absolute;
        max-width: calc(100% + 30px);
    }

    .centerView-sm {
        left: 50%;
        transform: translateX(-50%);
    }

    .absolute-sm {
        position: absolute;
    }

    .hero-banner-13 .img-container {
        height: 280px;
        width: 100%;
    }
}

@media (min-width: 992px) {
    .long-text-md {
        color: #2b2b2b;
        font-size: 16px;
        line-height: 25px;
        margin-bottom: 10px;
    }

    .banner-crop-img.rightView-0-md {
        position: absolute;
        height: 100%;
        width: auto;
        top: 50%;
        right: 0%;
        transform: translate(0%,-50%);
    }

    .column-spacer-30-md {
        margin: 0 -15px;
    }

    .column-spacer-30-md > div {
        padding-left: 15px;
        padding-right: 15px;
    }

    .protect-home-banner {
        height: 500px;
    }

    .protect-banner .protect-home-banner img {
        left: -131px;
        top: 0px;
        height: 500px;
    }

    .hero-banner-13 img {
        height: 435px;
    }
}


@media (max-width: 991.98px) {

    .protect-home-banner {
        height: 260px;
    }

    .protect-banner .protect-home-banner img {
        top: -16px;
        position: relative;
    }

    .hero-background-manage {
        background-position: 70%;
    }

    .subtitle-2-reg-2 {
        line-height: 18px;
        font-size: 14px;
        color: #555555;
    }

    .hero-banner-13 img {
        height: 100%;
    }

}


@media (min-width: 768px) {
    .width-175-sm {
        width: 175px;
    }

    .width-280-sm {
        width: 280px;
    }
    .width-410-sm {
        width: 410px;
    }

    .width-435-sm {
        width: 435px;
    }

    .width-600-sm {
        width: 600px;
    }

    .height-135-sm, textarea.form-control.height-225 {
        height: 135px;
    }

    .height-145-sm {
        height: 145px;
    }

    .min-height-305-sm {
        min-height: 305px;
    }

    .button-link {
        border-radius: 20px;
        font-size: 15px;
        height: 35px;
        line-height: 17px;
        text-align: center;
        cursor: pointer;
        padding: 7px 28px;
        white-space: nowrap;
        color: #fff;
        background-color: #003778;
        border: 2px solid #003778;
        margin-left: auto;
    }

        .button-link:hover, .button-link:focus {
            color: #fff;
            background-color: #00549a;
            border-color: #00549a;
        }

    .banner-crop-leftView {
        position: absolute;
        width: auto;
        top: 40%;
        left: 50%;
        transform: translate(-25%, -50%);
    }

    .responsive-hd-divider {
        border-top: 0;
        border-bottom: 0;
        border-left: 1px solid #003778;
    }

    .max-width-308-sm {
        max-width: 308px;
    }

    .max-width-300-sm {
        max-width: 300px;
    }
}


@media (max-width: 767.98px) {
    .width-60-xs{
        width: 60px;
    }
    .banner-crop-leftView {
        margin-left: 60%;
        transform: translateX(-50%);
        min-width: 485px;
        width: 100%;
        height: auto;
        bottom: 0;
        position: absolute;
    }

    .sticky-first-column-cont {
        min-width: 144px;
    }

    .sticky-first-column-cont > div:not(:first-child) div.same-height {
        padding: 15px 10px 15px 20px;
    }

    /* Custom table with scroll */

        .table-scrollable-wrapper::-webkit-scrollbar {
            height: 8px;
        }

        .table-scrollable-wrapper::-webkit-scrollbar-track {
            background: #e1e1e1;
            height: 8px;
        }

        .table-scrollable-wrapper::-webkit-scrollbar-thumb {
            height: 8px;
            background: #003778
        }

        .table-options th {
            min-width: 195px;
        }


    .ctrl_radioBtn .ctrl_element {
        top: 50%;
        left: 0;
    }

    .column-spacer-15 > div {
        padding-left: 0;
        padding-right: 0;
    }

    .protect-banner-image {
        width: 500px;
        height: auto;
        left: 50%;
        transform: translate(-49%, 5%);
    }

    .protect-banner {
        min-height: 440px;
    }

    .hero-banner-12 {
        height: 292px;
    }

    .hero-background-manage {
        background-position: 21%;
    }

    .manage-mobile-custom-size {
        height: 339px;
        width: 90.6%;
        margin-bottom: -35px;
    }

    .manage-mobile-custom {        
        top: -23px;
    }

    .bundle-and-save-image-container {
        height: 182px;
    }

    .bundle-and-save-image {
        height: 158px;
    }

    .min-height-xs-294 {
        height: 294px;
    }

    .height-220-xs {
        height: 220px;
    }

    .box-shadow-xs, .box-shadow-round-xs {
        box-shadow: 0 6px 25px 0 rgba(0,0,0,.12);
    }

    .height-295-xs{
        height: 295px;
    }

    .height-270-xs{
        height: 270px;
    }

    .margin-neg-30-l-xs{
        margin-left: -30px;
    }

    .margin-neg-20-t-xs {
        margin-top: -20px;
    }

    .centerView-xs {
        left: 50%;
        transform: translateX(-50%);
    }

    .hero-banner-13 img{
        height: 195px;
    }

    .button-link {
        color: #00549a;
        text-decoration: underline;
    }

        .button-link:hover, .button-link:focus {
            text-decoration: none;
        }
}

@media (max-width: 991.98px) and (min-width: 768px) and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    /* IE10+ CSS styles go here */
    .img-increase-size-30-sm {
        transform: translate(-50%, -50%)
    }
}

@media (min-width: 992px) and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    /* IE10+ CSS styles go here */
    .hero-banner-13 .img-container{
        left: 0;
    }
}

@media (max-width: 991.98px) and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    /* IE10+ CSS styles go here */
    .hero-banner-13 img {
        top: 0;
    }
}


