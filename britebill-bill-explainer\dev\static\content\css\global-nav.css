header {
    background: #00549a;
}

.global-navigation #connector-search [type="search"]::-ms-clear {
    display: none;
}

.global-navigation ::-webkit-search-cancel-button {
    -webkit-appearance: none;
}

.global-navigation .container {
    width: 960px;
}

.global-navigation a:focus,
.global-navigation .btn:focus,
.global-navigation li.ui-menu-item:focus {
    outline: -webkit-focus-ring-color auto 5px;
}

.global-navigation .caret {
    border-top: medium none;
}

.global-navigation .connector a, .global-navigation .connector a:link, .global-navigation .connector a:visited, .global-navigation .connector a:hover, .global-navigation .connector a:active {
    text-decoration: none;
}

/* Star skip to main content*/

.global-navigation .skip-to-main-link:focus {
    top: 0;
}

.global-navigation a.skip-to-main-link {
    color: #ffffff;
}

.global-navigation .skip-to-main-link {
    display: inline-block;
    padding: 9px 12px;
    position: absolute;
    top: -50px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    text-decoration: none;
    border-bottom-right-radius: 8px;
    transition: top 0.3s ease-out;
    z-index: 3000;
    color: #fff;
    text-transform: uppercase;
    font-size: 11px;
    background: #2d2e33;
}

/* End skip to main content*/

/* Start Federal Bar Area */
.global-navigation .federal-bar {
    background: #2d2e33;
    height: 33px;
    padding: 9px 0;
    display: block;
}

.global-navigation .federal-bar-links a,
.global-navigation .federal-bar-links a:link,
.global-navigation .federal-bar-links a:visited {
    color: #babec2;
    text-decoration: none;
}

    .global-navigation .federal-bar-links a.active,
    .global-navigation .federal-bar-links a.active:link,
    .global-navigation .federal-bar-links a.active:visited,
    .global-navigation .federal-bar-links a:hover,
    .global-navigation .federal-bar-links a:focus {
        color: #fff;
        text-decoration: none;
    }

.global-navigation .federal-bar-store-locator {
    display: inline-block;
    position: relative;
}

.global-navigation .store-locator-section-links li .label-text {
    font-size: 13px;
    color: #555555;
    text-decoration: none;
    white-space: nowrap;
}

    .global-navigation .store-locator-section-links li .label-text:hover {
        color: #555555;
    }

.global-navigation .store-locator-section-links li:not(:first-child) {
    padding-top: 10px;
}

.global-navigation .federal-bar-store-locator-popup.federal-bar-links {
    width: 260px;
}

.global-navigation .federal-bar-store-locator-popup {
    display: none;
    position: absolute;
    right: -65px;
    background: #fff;
    z-index: 100;
    padding: 20px;
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.5);
    top: 30px;
    width: 360px;
    text-transform: none;
}

.global-navigation
.federal-bar-store-locator.active
.federal-bar-store-locator-popup {
    display: block;
}

.global-navigation
.federal-bar-store-locator
.federal-bar-store-locator-popup.federal-bar-links.caret::after {
    left: calc(50% + 30px);
}

.global-navigation
.federal-bar-store-locator-popup.federal-bar-links
.store-locator-section {
    display: none;
}

.global-navigation .federal-bar-store-locator-popup .store-locator-section {
    display: block;
}

.global-navigation
.federal-bar-store-locator-popup.federal-bar-links
.store-locator-section-links {
    display: block;
}

.global-navigation
.federal-bar-store-locator-popup
.store-locator-section-links {
    display: none;
}

.global-navigation .federal-bar ul,
.global-navigation .connector ul {
    padding: 0;
    margin: 0;
}

    .global-navigation .federal-bar ul > li,
    .global-navigation .connector ul > li {
        list-style-type: none;
    }

.global-navigation .federal-bar-mobile .custom-select-trigger > .icon {
    -webkit-transform: translateY(-55%) rotate(90deg);
    -ms-transform: translateY(-55%) rotate(90deg);
    transform: translateY(-55%) rotate(90deg);
    color: #97989c;
}

.global-navigation .checkboxes_absolute .checkbox {
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 3px;
    box-shadow: inset 1px 1px 0 0 rgba(0, 0, 0, 0.1);
    display: inline-block;
    width: 22px;
    height: 22px;
    border: 1px solid #ccc;
    background-color: #fff;
    transition: background-color 10ms cubic-bezier(0.17, 0.67, 0.83, 0.67);
}

.global-navigation .radios .label,
.checkboxes .label {
    margin-left: 0;
    position: relative;
    color: #212121;
    font-weight: normal;
    vertical-align: top;
    cursor: default;
}


.global-navigation label {
    margin: 0;
}

.global-navigation .radios_absolute.radios .label-text,
.checkboxes_absolute.checkboxes .label-text {
    padding-left: 35px;
    -webkit-transform: translateY(2px);
    -ms-transform: translateY(2px);
    transform: translateY(2px);
}

.global-navigation div.checkboxes label.active .checkbox {
    background-color: #003778;
    border-color: #003778;
}

.global-navigation .radios .label.focused .radio,
.global-navigation .checkboxes .label.focused .checkbox {
    outline: 0;
    box-shadow: 0 0 3px 2px rgba(178, 209, 228, 1);
}

.global-navigation .checkboxes .label:not(.disabled) {
    cursor: pointer;
}

.global-navigation .federal-bar-store-locator-popup a.button {
    box-sizing: border-box;
    display: inline-block;
    position: relative;
    margin: 15px 0;
    padding: 10px 36px;
    vertical-align: middle;
    background-color: #003778;
    font-size: 16px;
    line-height: 1;
    text-align: center;
    text-decoration: none !important;
    color: #fff;
    border: 2px solid #003778;
    border-radius: 24px;
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.55, 0, 0.1, 1);
    width: 100%;
}

    .global-navigation .federal-bar-store-locator-popup a.button:hover,
    .federal-bar-store-locator-popup a.button:focus {
        background-color: #00549a;
        border-color: #00549a;
    }

.global-navigation .federal-bar-link-provinces {
    position: absolute;
    top: 30px;
    right: -17px;
    z-index: 100;
    width: 250px;
    display: none;
    background-color: white;
    padding: 15px 10px;
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.3);
}

    .global-navigation .federal-bar-link-provinces.caret:after {
        transform: translateX(53px) translateY(-100%);
        -webkit-transform: translateX(53px) translateY(-100%);
        -ms-transform: translateX(53px) translateY(-100%);
    }

.global-navigation #connector-search {
    width: 210px;
}

    .global-navigation #connector-search [type="reset"],
    #connector-search [type="submit"],
    #connector-search #voice_search {
        position: absolute;
        right: 5px;
        left: auto;
        top: 0;
        padding: 0;
        border: 0;
        background: none;
    }

    .global-navigation #connector-search [type="reset"] {
        right: 40px;
        width: 30px;
        display: none;
    }

        .global-navigation #connector-search [type="reset"] .icon {
            opacity: .5;
            font-size: 18px;
        }

        .global-navigation #connector-search [type="reset"].active {
            display: block;
        }

.global-navigation .header-preferences {
    display: inline-block;
    margin-left: 15px;
}

.global-navigation .footer-header-preferences-buttons {
    position: relative;
}

    .global-navigation .footer-header-preferences-buttons select {
        left: 0;
        opacity: 0;
        position: absolute;
        top: 0;
        width: 25px;
        display: none;
    }

.global-navigation .federal-bar-link-provinces .label {
    text-transform: initial;
    padding: 5px 5px 3px 7px;
    text-transform: none;
}

.global-navigation .radios:not(.radios_absolute) .label-text,
.global-navigation .checkboxes:not(.checkboxes_absolute) .label-text {
    -webkit-transform: translateY(-5px);
    -ms-transform: translateY(-5px);
    transform: translateY(-5px);
    padding-left: 4px;
}

.global-navigation .federal-bar-link-provinces .label .label-text {
    font-size: 13px;
    color: #555555;
}

.global-navigation .federal-bar-link-provinces .label.active .label-text {
    color: #00549a;
    font-weight: bold;
}

.global-navigation .radios .label-text,
.global-navigation .checkboxes .label-text {
    display: inline-block;
    line-height: 1;
}

.global-navigation .federal-bar-link-provinces .label:hover,
.global-navigation .federal-bar-link-provinces .label:focus {
    background: #e1e1e1;
    border-radius: 3px;
}

.global-navigation .federal-bar-link-provinces .checkbox {
    border: none;
    background-color: transparent;
    box-shadow: none;
}

    .global-navigation .federal-bar-link-provinces .checkbox:after {
        color: #00549a;
        background: none;
        font-size: 12px;
        font-weight: bold;
    }

.global-navigation .radios label.active .radio:after, .global-navigation .checkboxes label.active .checkbox:after {
    opacity: 1;
    height: 10px;
    width: 10px;
}

.global-navigation .radio:after, .global-navigation .checkbox:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
    opacity: 0;
    transition: opacity 10ms cubic-bezier(.17, .67, .83, .67);
    background-color: #fff;
    border-radius: 50%;
}

.global-navigation .checkbox:after {
    font-family: 'bell-icon';
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: '\e603';
    color: #fff;
    background-color: transparent;
    border-radius: inherit;
    font-size: 11px;
}

.global-navigation .radio,
.global-navigation .checkbox {
    display: inline-block;
    position: relative;
    width: 22px;
    height: 22px;
    border: 1px solid #ccc;
    background-color: #fff;
    border-radius: 50%;
    transition: background-color 10ms cubic-bezier(0.17, 0.67, 0.83, 0.67);
}

/* End of Federal Bar Area */
/* Start of Connector */
.global-navigation .connector {
    position: relative;
    background: #00549a;
}

.global-navigation .radios input[type="radio"],
.global-navigation .checkboxes input[type="checkbox"] {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none;
}

/* End of Connector */
/* Start Right Connecter Area */
.global-navigation .connector-cart-button, .global-navigation .connector-brand {
    font-family: 'bell-icon';
    font-style: normal;
    speak: none;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.global-navigation .connector-brand, .global-navigation .connector-brand-show {
    padding-left: 0;
    margin-left: 0;
}

.global-navigation .connector-area {
    background: #00549a;
    border-bottom: 1px solid #003778;
}

.global-navigation .connector .connector-brand a {
    color: #fff;
    top: 4px;
}

.global-navigation .connector-brand a:before {
    content: '\e600';
}

.global-navigation .connector-area.active div > a {
    color: #fff;
}

.global-navigation .connector-search-wrap {
    float: left;
    margin-top: 0px;
    margin-right: 10px;
    position: relative;
}

.global-navigation .hideAutocomplete.active:focus {
    opacity: 1;
}

.global-navigation .connector-login-button:focus {
    color: #fff !important;
    border-color: #fff !important;
    background-color: #3376ae;
}

.global-navigation .connector-cart-button {
    padding: 0;
    margin: 0;
    font-size: 27px;
    bottom: 0;
    color: #ffffff;
}

    .global-navigation .connector-cart-button:hover {
        color: #c2cedf;
    }

.global-navigation #connector-search [type="reset"],
.global-navigation #connector-search [type="submit"] {
    cursor: pointer;
}

    .global-navigation #connector-search [type="submit"]:after {
        content: "\e615";
        -webkit-transform: translateX(-50%) translateY(-50%);
        -ms-transform: translateX(-50%) translateY(-50%);
        transform: translateX(-50%) translateY(-50%);
        font-size: 18px;
        color: #003778;
    }

    .global-navigation #connector-search [type="reset"]:after,
    #connector-search [type="submit"]:after {
        font-family: "bell-icon";
        line-height: 1;
    }

    .global-navigation #connector-search [type="reset"]:after,
    #connector-search [type="reset"]:before,
    #connector-search [type="submit"]:after {
        display: block;
        position: absolute;
        top: 50%;
        left: 50%;
    }

    .global-navigation #connector-search [type="reset"] .icon {
        font-size: 14px;
        color: #111;
    }

    .global-navigation #connector-search [type="reset"]:focus .icon {
        opacity: 1;
    }

/* End Right Connecter Area */

.global-navigation #connector-search [type="search"] {
    display: inline-block;
    border-radius: 18px;
    box-shadow: inset 2px 0 3px -1px rgba(0, 0, 0, 0.46);
}

.global-navigation input[type="search"]::-webkit-input-placeholder {
    color: #999999;
}

.global-navigation input[type="search"]::-moz-placeholder {
    color: #999999;
}

.global-navigation input[type="search"]:-ms-input-placeholder {
    color: #999999;
}

.global-navigation input[type="search"]:-moz-placeholder {
    color: #999999;
}

.global-navigation .connector-area div > a {
    color: #c2cedf;
    display: inline-block;
    margin-right: 15px;
    margin-left: 10px;
    top: -1px;
    position: relative;
}

    .global-navigation .connector-area div > a:hover {
        color: #ffffff;
    }

    .global-navigation .connector-area div > a span {
        font-size: 26px;
        letter-spacing: -1px;
    }

.global-navigation .shopping-cart-button.active .shopping-cart-popup {
    display: block;
}

.global-navigation .shopping-cart-popup {
    right: -15px;
    top: 40px;
}

.global-navigation .popup.caret:after {
    border-width: 12px;
    left: calc(50% + 87px);
}

.global-navigation .federal-bar-link-small-business, .global-navigation .federal-bar-link-enterprise, .global-navigation .shopping-cart-popup, .global-navigation .login-footer-popup {
    position: absolute;
    top: 45px;
    z-index: 100;
    width: 230px;
    display: none;
    background-color: white;
    padding: 20px;
    box-shadow: 0 0 40px rgba(0,0,0, .3);
}

.global-navigation .caret, .federal-bar-link-provinces, .global-navigation .federal-bar-store-locator-popup, .global-navigation .connector-logged-in-modal.active, .global-navigation .connector-login-modal.active {
    height: auto;
}

.global-navigation .shopping-cart-popup .icon-circle-large {
    left: 50%;
    transform: translateX(-50%);
}


.global-navigation a,
.global-navigation a:hover {
    color: #00549a;
}

body.voice-search-enabled #connector-search #voice_search {
    display: block;
}

.global-navigation #connector-search [type="submit"] {
    width: 30px;
}

.global-navigation #connector-search #voice_search {
    right: 30px;
}

    .global-navigation #connector-search #voice_search:after {
        content: "?";
        -webkit-transform: translateX(-50%) translateY(-50%);
        -ms-transform: translateX(-50%) translateY(-50%);
        transform: translateX(-50%) translateY(-50%);
        font-size: 18px;
        color: #003778;
    }

.global-navigation #connector-search [type="search"] {
    position: relative;
    width: 100%;
    padding-right: 70px;
    padding-left: 15px;
    border: 0;
    background-color: #fff;
    color: #111;
}

.global-navigation #connector-search [type="search"], .global-navigation #connector-search [type="reset"], .global-navigation #connector-search [type="submit"] {
    height: 36px;
}

.global-navigation .connector-login-button {
    max-width: 230px;
    border: 2px solid #ffffff;
    color: #ffffff;
    padding: 8px 20px;
    font-size: 14px;
}

    .global-navigation .connector-login-button:hover {
        background-color: #3376ae;
        color: #ffffff;
    }

.global-navigation .footer-login-btn, .global-navigation .connector-login-button {
    white-space: normal;
}

.global-navigation .menu-flyout-visible .sub-nav-level-1 {
    font-size: 15px;
}

.global-navigation .menu-flyout-visible .sub-nav-item ul > li a {
    font-size: 13px;
}

.global-navigation .menu-flyout-visible .sub-nav-level-1:after {
    content: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAANCAYAAACUwi84AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyRpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoTWFjaW50b3NoKSIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDozQTdGOTE3RDZFMDkxMUU4ODU1MkE2RDY0QTMwNUEyMyIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDozQTdGOTE3RTZFMDkxMUU4ODU1MkE2RDY0QTMwNUEyMyI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjNBN0Y5MTdCNkUwOTExRTg4NTUyQTZENjRBMzA1QTIzIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjNBN0Y5MTdDNkUwOTExRTg4NTUyQTZENjRBMzA1QTIzIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Vo8LxgAAAZJJREFUeNpMUEsvA2EUvfP55q1GR6tECYJ6rTyasJCQSCxEIiQeYWHDmv9gKSzEokIkrCQWVlJBjNZKKhYI2oSUeCeD6mg7046vXnGX955z7jmHmlw7hFdNbwYKkCwyfi2eBIqCv0HRuOGe2zzdn/ee+qLxZEMWT4Np/gOkTJPhGQxqWIXl3YuNSEyvEjlMQN8oZBVZf3djsTu/LPft6vzetrB95ovpqRoLz3wpoWTKBC1hHNSX2buKyh3GTfApZ0W58H4kjBKBxYDSMnoyBWShuF25rc4Kx2Pw/KFgSQkqxF91RkvvKPA0BtnCQqFNDFcVyMdHty9Dj6FnKYKgAxnkBYMR2AggW2BR4PJpOKpqINozobbQOoXTkdO5WToD1gPhVcUX6uFkAfraXGN2C+dBCFEgCQzsnNwtp4+0hYOBVtdYnsR7Ih8JQMSl5D26XvTuhYZwFg+D7ZXjDonzqNE4USb1vsf0uq1AeIRUDf1tFRNOWZh50wjzp2/M0tjf2VQ6y3NYc1qFafU98cX8nU8BBgAJEJrPXAOh1QAAAABJRU5ErkJggg==);
}

.global-navigation .menu-flyout {
    display: none;
}

    .global-navigation .menu-flyout.menu-flyout-visible {
        display: block;
    }

.global-navigation .menu-flyout-visible .sub-nav-root ul.sub-nav-group {
    display: none;
}

.global-navigation .bellSlimSemibold-Nav {
    border-right: solid 1px #004978;
    border-left: solid 1px #004978;
    margin-left: 4px;
    margin-top: 3px;
    padding-left: 9px;
    letter-spacing: -1px;
}

.global-navigation .connector-settings-mobile > li {
    background: #00549a;
    border-bottom: 1px solid #003778;
    position: relative;
}

    .global-navigation .connector-settings-mobile > li > div > .icon {
        position: absolute;
        color: #fff;
        font-size: 22px;
        top: 9px;
        left: 18px;
        cursor: pointer;
        pointer-events: none;
    }

    .global-navigation .connector-settings-mobile > li > div > a {
        display: block;
        padding: 10px 20px 11px 49px;
        font-size: 17px;
    }

.global-navigation .preferences-section {
    font-size: 12px;
    text-transform: uppercase;
}

.global-navigation .federal-bar-mobile .custom-select-trigger-label {
    margin: 0 25px 0 0;
}



/* Start Logged In */

.global-navigation .connector-area a.active span {
    font-family: bellslim_semiboldregular;
    color: #fff;
}

    .global-navigation .connector-area a.active span::after {
        background-color: #fff;
        bottom: -2px;
        content: "";
        display: block;
        height: 2px;
        left: 0;
        position: absolute;
        right: 0;
    }

/*.global-navigation .connector-active-secondary-nav li a:focus ~ .secondary-nav-dropdown,*/
.global-navigation .connector-active-secondary-nav li:hover .secondary-nav-dropdown {
    display: block !important;
}

.global-navigation .connector-active-secondary-nav li.active a.trigger-dropdown span:after {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    right: 0;
    bottom: -7px;
    height: 2px;
    background-color: #fff;
}

.global-navigation .connector-active-secondary-nav a:focus {
    outline: 1px solid #2672cb;
}
/* End Logged In */

.global-navigation #initial-lang-region, .global-navigation #initial-lang-reigon-backdrop {
    display: none;
    position: fixed;
}

.global-navigation .menu-flyout-overlay {
    background-color: transparent;
    left: 0;
    width: 100vw;
    height: 100vh;
    position: fixed;
    z-index: 19;
    display: none;
}

@media (min-width: 1240px) {
    .global-navigation .container {
        max-width: 1200px;
        width: 100%;
    }

    .global-navigation .connector-area div > a {
        margin: 0 20px;
    }

    .global-navigation #connector-search {
        width: 300px;
    }
}

@media (min-width: 992px) {
    .global-navigation .connector-mobile-bar, .global-navigation .connector-settings-mobile, .global-navigation .connector-nav-close-button, .global-navigation .federal-bar-link-provinces {
        display: none;
    }

        .global-navigation .federal-bar-link-provinces.active {
            display: block;
        }

    .global-navigation .shopping-cart-button {
        padding-right: 0;
        padding-left: 10px;
        margin-top: -2px;
        margin-left: 6px;
        font-size: 27px;
        bottom: -3px;
    }

    .global-navigation .menu-flyout-visible + .menu-flyout-overlay {
        display: block;
    }

    .global-navigation #connector-search [type="reset"] {
        right: 35px;
    }

    .global-navigation .federal-bar-mobile {
        display: none;
    }

    /* Start Menu */
    .global-navigation .connector-mobile-bar {
        border: none !important;
        position: relative;
        height: 55px;
    }

    .global-navigation .connector-area {
        height: auto;
        border-bottom: none;
    }

    .global-navigation .connector-area {
        position: relative !important;
        height: 54px;
        display: inline-block;
        overflow: inherit;
        max-height: 1000px;
        border-bottom: none;
    }

    .global-navigation .connector-brand {
        margin-right: 28px;
        font-size: 37px;
        padding: inherit;
        height: 54px;
    }

        .global-navigation .connector-brand > a {
            position: relative;
        }

    .global-navigation .connector-brand-home {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0,0,0,0);
        border: 0;
    }

    .global-navigation .connector-areas {
        display: inline-block;
    }

    .global-navigation .connector-settings {
        float: right;
        margin-top: 20px;
    }

    .global-navigation .connector-nav {
        width: auto;
        position: static;
        float: left;
        margin-top: 13px;
        background: none;
        -webkit-transform: none;
        -ms-transform: none;
        transform: none;
        overflow: visible;
    }

        .global-navigation .connector-nav > ul {
            font-size: 0;
        }

    .global-navigation .connector-cart-button, .global-navigation .connector-login-button, .global-navigation .connector-log-out-button {
        float: left;
    }

    .global-navigation .menu-flyout-visible .sub-nav-item ul > li {
        margin-bottom: 15px;
    }

    .global-navigation .menu-flyout {
        opacity: 0;
        height: 0;
        max-height: 0;
        overflow: hidden;
        position: absolute;
        z-index: -1;
        -webkit-transition: opacity 0.25s;
        -o-transition: opacity 0.25s;
        transition: opacity 0.25s;
    }

        .global-navigation .menu-flyout.menu-flyout-visible {
            opacity: 1;
            position: absolute;
            z-index: 20;
            background-color: #f0f0f0;
            width: 214px;
            padding-top: 0;
            padding-bottom: 0;
            -webkit-box-shadow: 0 3px 16px 2px rgba(0, 0, 0, 0.23);
            box-shadow: 0 3px 16px 2px rgba(0, 0, 0, 0.23);
            left: 50%;
            margin-left: -107px;
            top: 55px;
            height: auto;
            max-height: none;
            overflow: visible;
        }

    .global-navigation .menu-flyout-visible .sub-nav-root {
        padding-top: 20px;
    }

    .global-navigation .menu-flyout-visible .menu-flyout-root {
        position: relative;
        min-height: 300px;
    }

        .global-navigation .menu-flyout-visible .menu-flyout-root:after {
            bottom: 100%;
            left: 50%;
            border: solid transparent;
            content: " ";
            height: 0;
            width: 0;
            position: absolute;
            pointer-events: none;
            border-color: rgba(255, 255, 255, 0);
            border-bottom-color: #f0f0f0;
            border-width: 14px;
            margin-left: -14px;
        }



    .global-navigation .menu-flyout-visible .sub-nav-root > li:last-child {
        padding-bottom: 40px;
    }

    .global-navigation .menu-flyout-visible .sub-nav-header {
        padding: 29px 30px 0 30px;
        font-weight: bold;
        color: #111;
        display: flex;
        font-size: 12px;
        margin: 0;
    }
        
    .global-navigation .menu-flyout-visible .sub-nav-desktop {
        display: block;
    }

    .global-navigation .menu-flyout-visible .sub-nav-mobile {
        display: none;
    }

    .global-navigation .menu-flyout-visible .sub-nav-level-1 {
        padding: 10px 18px;
        display: block;
        border-left: 4px solid #f0f0f0;
        position: relative;
        line-height: normal;
    }

    .global-navigation .menu-flyout.menu-flyout-visible li.menu-flyout-item-active > .sub-nav-level-1 {
        border-left-color: #00549a;
        background-color: white;
        font-weight: bold;
        -webkit-box-shadow: -1px 4px 8px -4px rgba(0,0,0,0.1);
        box-shadow: -1px 4px 8px -4px rgba(0,0,0,0.1);
    }

    .global-navigation .menu-flyout-visible .no-sub-nav .sub-nav-level-1 {
        color: #00549a;
    }

    .global-navigation .menu-flyout-visible .sub-nav-group {
        width: 180%;
        background-color: white;
        z-index: -1;
        -webkit-box-shadow: 0px 3px 16px 2px rgba(0,0,0,0.23);
        box-shadow: 0px 3px 16px 2px rgba(0,0,0,0.23);
        opacity: 0;
        -webkit-transition: opacity .225s;
        -o-transition: opacity .225s;
        transition: opacity .225s;
    }

    .global-navigation
    .menu-flyout.menu-flyout-visible
    li.menu-flyout-item-active
    .sub-nav-group {
        position: absolute;
        left: 214px;
        top: 0;
        height: 100%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        opacity: 1;
    }

    .global-navigation .menu-flyout-visible .sub-nav-large {
        width: 230%;
    }

    .global-navigation .menu-flyout-visible .no-sub-nav .sub-nav-level-1::after {
        content: none;
    }

    .global-navigation .menu-flyout-visible .sub-nav-item ul {
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        padding-left: 30px;
        padding-right: 30px;
        height: 100%;
        padding-top: 30px;
    }

    .global-navigation .menu-flyout-visible .sub-nav-item .sub-nav-links-two-columns, .global-navigation .menu-flyout-visible .sub-nav-item .sub-nav-links-three-columns {
        -webkit-column-gap: 30px;
        column-gap: 30px;
        -webkit-column-fill: auto;
        column-fill: auto;
        height: 280px;
    }

    .global-navigation .menu-flyout-visible .sub-nav-links-two-columns {
        columns: 2;
        -webkit-columns: 2;
        -moz-columns: 2;
        list-style-position: inside;
    }

    .global-navigation .menu-flyout-visible .sub-nav-item {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        width: 0;
        overflow-y: hidden;
        -webkit-transition: opacity .225s, -webkit-transform .225s;
        transition: opacity .225s, -webkit-transform .225s;
        -o-transition: opacity .225s, transform .225s;
        transition: opacity .225s, transform .225s;
        transition: opacity .225s, transform .225s, -webkit-transform .225s;
    }

    .global-navigation .menu-flyout-visible .sub-nav-level-1:after {
        color: #00549a;
        position: absolute;
        top: 50%;
        margin-top: -6px;
        right: 15px;
    }

    .global-navigation .menu-flyout.menu-flyout-visible li.menu-flyout-item-active > .sub-nav-level-1:after {
        content: "";
    }

    .global-navigation .menu-flyout-visible .sub-nav-item:not(:last-child) ul {
        border-right: 1px solid #e2e2e2;
    }

    .global-navigation .menu-flyout-visible .sub-nav-item ul > li a {
        display: block;
        color: #555;
        line-height: normal;
    }

        .global-navigation .menu-flyout-visible .sub-nav-item ul > li a:hover {
            color: #00549a;
            text-decoration: underline;
        }

    .global-navigation .menu-flyout-visible .sub-nav-item .sub-nav-level4 {
        padding-top: 20px;
    }
    /* End Menu */
}

@media (max-width: 991.98px) {
    .global-navigation .connector-nav.nav-right {
        right: 0;
    }

    .global-navigation #connector-search [type="search"], .global-navigation #connector-search [type="reset"], .global-navigation #connector-search [type="submit"] {
        height: 55px;
    }

    .global-navigation .connector-mobile-bar .ui-autocomplete-input::-webkit-input-placeholder {
        color: #000;
    }

    .global-navigation .connector-mobile-bar .ui-autocomplete-input::-moz-placeholder {
        color: #000;
    }

    .global-navigation .connector-mobile-bar .ui-autocomplete-input:-ms-input-placeholder {
        color: #000;
    }

    .global-navigation .connector-mobile-bar .ui-autocomplete-input:-moz-placeholder {
        color: #000;
    }

    .global-navigation a,
    .global-navigation a:hover {
        color: #ffffff;
    }

    .global-navigation .connector > .container {
        background-color: #00549a;
        z-index: 1100;
        position: relative;
    }

    .global-navigation .container {
        width: 100%;
        max-width: 100%;
        margin: 0;
        padding: 0;
    }

    .global-navigation .federal-bar {
        display: none;
    }

    .global-navigation .connector-mobile-bar {
        margin: 0;
        position: relative;
        border-bottom: 1px solid #003778 !important;
        height: 55px;
    }

    .global-navigation .connector-active-lob-title {
        position: relative;
        font-size: 19px;
        line-height: 1;
        letter-spacing: -.8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-left: 68px;
        margin-right: 100px;
        padding-top: 10px;
        padding-bottom: 10px;
        color: #c2cedf;
    }

    .global-navigation .connector-brand {
        position: absolute;
        top: 0;
        left: 15px;
        font-size: 0;
        text-decoration: none;
        text-decoration: none;
        border-bottom: none;
        padding: 0;
    }

        .global-navigation .connector-brand:after {
            content: '\e600';
            font-size: 26px;
            line-height: 2.1;
        }

    .global-navigation #connector-search {
        position: relative;
        width: 100%;
        display: block;
    }

        .global-navigation #connector-search [type="search"] {
            border-radius: 0;
            box-shadow: none;
        }

    .global-navigation #connector-search-button {
        display: block;
        position: absolute;
        top: 0;
        right: 50px;
        border: 0;
        background: none;
        font-size: 19px;
        font-style: normal;
        speak: none;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        color: #fff;
        cursor: pointer;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin: 8px 5px;
        transition: background-color .25s cubic-bezier(.55,0,.1,1);
        padding: 0;
    }

        .global-navigation #connector-search-button.active {
            background-color: #002c6b;
        }

    .global-navigation .connector-settings .connector-brand, .global-navigation .connector-nav-open-button {
        display: block !important;
    }

    .global-navigation .connector-nav-open-button {
        border: 0;
        color: #fff;
        background: none;
        font-size: 20px;
        position: absolute;
        right: 0;
        top: 0;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        margin: 8px 5px;
        padding: 0;
        cursor: pointer;
        z-index: 60;
    }

        .global-navigation .connector-nav-open-button.active {
            background-color: #002c6b;
        }

        .global-navigation .connector-nav-open-button .icon-plus {
            display: none;
        }

        .global-navigation .connector-nav-open-button .icon-mobile-menu {
            display: inline-block;
        }

        .global-navigation .connector-nav-open-button.active .icon-plus {
            display: inline-block;
        }

        .global-navigation .connector-nav-open-button .icon-plus:before {
            display: inline-block;
            -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
        }

        .global-navigation .connector-nav-open-button.active .icon-mobile-menu {
            display: none;
        }

    .global-navigation .connector-login-button {
        display: none;
    }

    .screen {
        position: fixed;
        z-index: 1000;
        top: 0;
        left: 0;
        bottom: 100%;
        right: 0;
        opacity: 0;
        background-color: rgba(0,0,0,.6);
    }

    .connector-active .screen {
        bottom: 0;
        opacity: 1;
    }

    .global-navigation .connector-nav {
        display: none;
        position: fixed;
        top: 55px;
        bottom: 0;
        background: #2d2e33;
        width: 300px;
        -webkit-transform: translateX(-300px);
        -ms-transform: translateX(-300px);
        transform: translateX(-300px);
        z-index: 999999;
        overflow: auto;
    }

    .connector-active .global-navigation .connector-nav {
        -webkit-transform: none;
        -ms-transform: none;
        transform: none;
        display: block;
    }

    /* Start Shadow */
    .global-navigation .connector-area.connector-area_first > div:first-child {
        -webkit-box-shadow: inset 0px 11px 17px 0px rgba(0, 0, 0, 0.25);
        -moz-box-shadow: inset 0px 11px 17px 0px rgba(0, 0, 0, 0.25);
        box-shadow: inset 0px 11px 17px 0px rgba(0, 0, 0, 0.25);
    }
    /* End Shadow */

    .global-navigation #connector-search [type="reset"],
    .global-navigation #connector-search #voice_search {
        right: 8px;
    }

    .global-navigation #connector-search [type="submit"]:after {
        color: #00549a;
        font-size: 16px;
    }

    .global-navigation .connector-search-wrap {
        position: absolute;
        width: 100%;
        z-index: 55;
        top: 55px;
        left: 0;
        display: none;
    }

        .global-navigation .connector-search-wrap.active {
            display: block;
        }

    .global-navigation .connector-areas li > div,
    .global-navigation .connector-settings-mobile li > div {
        padding: 1px;
        width: 100%;
    }

    .global-navigation .federal-bar-mobile li > div {
        width: auto;
    }

    .global-navigation .connector.connector-search-active .connector-nav {
        top: 111px;
    }

    .global-navigation .connector-area div > a {
        margin: 0;
        color: #fff;
    }

    .global-navigation .connector-area.active div > a::after,
    .global-navigation
    .connector-area.active
    .menu-flyout-visible
    .menu-flyout-item-active
    .sub-nav-level-1:after {
        top: 48px;
        left: 28px;
        border: solid transparent;
        content: " ";
        height: 0;
        width: 0;
        position: absolute;
        z-index: 11;
        pointer-events: none;
        border-left: 13px solid transparent;
        border-right: 13px solid transparent;
        border-top: 11px solid #00549a;
    }

    .global-navigation
    .connector-area.active
    .menu-flyout-visible
    .menu-flyout-item-active
    .sub-nav-level-1:after {
        transform: none;
        border-top: 11px solid #003778;
    }

    .global-navigation .connector-area div > a span {
        font-size: 20px;
    }

    .global-navigation .connector-single-link a > h3 {
        font-size: 18px;
        font-family: sans-serif;
        font-weight: normal;
        letter-spacing: normal;
        line-height: 1.1;
    }

    .global-navigation .connector-single-link a {
        padding: 17px 40px 6px 24px;
        display: block;
        position: relative;
    }

    .global-navigation .connector-area div > a {
        position: relative;
        font-family: "bell-slim";
        letter-spacing: 0.4px;
        display: block;
        padding: 11px 35px 6px 14px;
        font-size: 20px;
        margin-bottom: 1px;
    }

    .global-navigation .connector-area_find-store:after {
        font-family: "bell-icon";
        content: "\e620";
        color: #fff;
        font-size: 18px;
        font-style: normal;
        speak: none;
        font-weight: normal;
        font-variant: normal;
        position: absolute;
        text-transform: none;
        line-height: 1;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        top: 26px;
        -webkit-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
        right: 12px;
        opacity: 1;
        transition: opacity 0.3s cubic-bezier(0.55, 0, 0.1, 1), transform 0.2s cubic-bezier(0.55, 0, 0.1, 1);
        cursor: pointer;
        pointer-events: none;
    }

    .global-navigation .menu-flyout-visible .sub-nav-desktop {
        display: none;
    }

    .global-navigation .menu-flyout-visible .sub-nav-mobile {
        display: block;
    }

    .global-navigation .menu-flyout.menu-flyout-visible {
        display: block;
        position: relative;
        background-color: #003778;
    }

    .global-navigation .menu-flyout-visible .sub-nav-level-1 {
        font-size: 18px;
        padding: 14px 40px 9px 24px;
        display: block;
        position: relative;
    }

        .global-navigation .menu-flyout-visible .sub-nav-level-1:after,
        .sub-nav-root .sub-nav-group .sub-nav-item li a::after {
            font-family: "bell-icon";
            content: "\e012";
            color: #fff;
            font-size: 13px;
            font-style: normal;
            speak: none;
            font-weight: normal;
            font-variant: normal;
            text-transform: none;
            line-height: 1;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 14px;
        }

    .global-navigation
    .menu-flyout-visible
    li:not(.no-sub-nav)
    .sub-nav-level-1:after {
        transform: translateY(-50%) rotate(90deg);
    }

    .global-navigation
    .menu-flyout-visible
    .menu-flyout-item-active
    .sub-nav-level-1:after {
        content: " ";
    }

    .global-navigation
    .menu-flyout-visible
    .menu-flyout-item-active
    ul.sub-nav-group {
        display: block;
        background: #002c6b;
    }

    .global-navigation .connector-areas > li:first-child,
    .menu-flyout-root > ul > li:first-child,
    .menu-flyout-root > ul > li > ul > li:first-child {
        -webkit-box-shadow: inset 0 11px 17px 0 rgba(0, 0, 0, 0);
        -moz-box-shadow: inset 0 11px 17px 0 rgba(0, 0, 0, 0);
        box-shadow: inset 0 11px 17px 0 rgba(0, 0, 0, 0);
    }

    .global-navigation .sub-nav-root li:first-child > a {
        -webkit-box-shadow: inset 0 11px 17px 0 rgba(0, 0, 0, 0.11);
        -moz-box-shadow: inset 0 11px 17px 0 rgba(0, 0, 0, 0.11);
        box-shadow: inset 0 11px 17px 0 rgba(0, 0, 0, 0.11);
    }

    .global-navigation .menu-flyout .sub-nav-root > li > ul > li > ul > li > a {
        box-shadow: none;
    }

    .global-navigation .sub-nav-group > li,
    .global-navigation .sub-nav-group > li > a,
    .global-navigation .sub-nav-group > li > ul > li {
        border-bottom: 1px solid #002b65;
    }

    .global-navigation .sub-nav-root .sub-nav-group .sub-nav-item ul a {
        color: #c2cedf;
        text-decoration: none;
    }

    .global-navigation .sub-nav-root .sub-nav-group .sub-nav-item li a:hover {
        color: #ffffff;
    }

    .global-navigation .sub-nav-root .sub-nav-group .sub-nav-item a,
    .sub-nav-root .sub-nav-group a {
        font-size: 18px;
        padding: 14px 40px 9px 34px;
        position: relative;
        display: block;
    }

    .global-navigation .connector-area {
        position: relative;
    }

        .global-navigation .connector-area:after, .global-navigation .connector-lob:after, .global-navigation .connector-lob > ul > li:after, .global-navigation .connector-lob > ul > li > ul > li:after {
            font-family: 'bell-icon';
            content: "\e012";
            color: #fff;
            font-size: 13px;
            font-style: normal;
            speak: none;
            font-weight: normal;
            font-variant: normal;
            text-transform: none;
            line-height: 1;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            position: absolute;
            top: 50%;
            -webkit-transform: translateY(-50%);
            -ms-transform: translateY(-50%);
            transform: translateY(-50%);
            right: 15px;
            opacity: 1;
        }

        .global-navigation .connector-area:not(.connector-area_first).active:after {
            opacity: 0;
        }

        .global-navigation .connector-area.connector-area_first.active:after {
            opacity: 1;
            transform: rotate(90deg);
        }

        .global-navigation .connector-area:after {
            top: 19px;
            transform: rotate(90deg);
            cursor: pointer;
        }

    .global-navigation ul.federal-bar-mobile {
        background-color: #2d2e33;
        padding-top: 8px;
        padding-bottom: 60px;
    }

    .global-navigation .federal-bar-mobile > li a:link,
    .global-navigation .federal-bar-mobile > li a:visited,
    .global-navigation .federal-bar-mobile > li a:hover,
    .global-navigation .federal-bar-mobile > li a:active {
        display: block;
        padding: 13px 15px 14px 14px;
        font-size: 12px;
        text-transform: uppercase;
        color: #97989c;
        position: relative;
    }

    .global-navigation .federal-bar-mobile .custom-select-trigger {
        border: none;
        padding: 13px 15px 14px 14px;
        color: #97989c;
    }

    .global-navigation .custom-select-trigger > .icon {
        position: absolute;
        top: 50%;
        right: 16px;
    }

    .global-navigation select:focus ~ .custom-select-trigger.bg-transparent {
        outline: -webkit-focus-ring-color auto 5px;
    }

    .global-navigation #connector-search [type="reset"] {
        width: 30px;
        right: 40px;
    }

    .global-navigation #connector-search [type="submit"] {
        width: 45px;
        right: 0px;
    }

    .global-navigation .sub-nav-group {
        max-width: 100%;
    }
}

@media (min-width: 768px) {
    .global-navigation #initial-lang-region {
        width: 480px;
        background-color: #fff;
        left: 50%;
        transform: translate(-50%, 0);
        top: 50%;
        z-index: 5000;
    }
}

@media (max-width: 767.98px) {
    .global-navigation .connector-nav {
        width: 100%;
    }
}
