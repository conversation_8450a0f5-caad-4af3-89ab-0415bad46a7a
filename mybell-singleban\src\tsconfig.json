{"compilerOptions": {"target": "es5", "module": "commonjs", "moduleResolution": "node", "lib": ["dom", "es6"], "sourceMap": true, "allowSyntheticDefaultImports": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "removeComments": false, "declaration": false, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "noImplicitReturns": false, "noUnusedLocals": true, "pretty": true, "strictNullChecks": true, "importHelpers": true, "jsx": "react", "types": ["node"], "baseUrl": "../node_modules/@types", "outDir": "../dist", "paths": {"redux": ["node_modules/redux"]}}}