﻿/*START BELL ICON FONTS*/
@font-face{font-family:'bell-icon';src:url(../fonts/mybell-icons.eot?#iefix) format("embedded-opentype"),url(../fonts/mybell-icons.woff) format("woff"),url(../fonts/mybell-icons.ttf) format("truetype"),url(../fonts/mybell-icons.svg) format("svg");font-weight:400;font-style:normal}
@font-face{font-family:'bell-icon-outline';src:url(../fonts/mybell-icons.eot?iw8dli);src:url(../fonts/mybell-icons.eot?#iefixiw8dli) format("embedded-opentype"),url(../fonts/mybell-icons.ttf?iw8dli) format("truetype"),url(../fonts/mybell-icons.woff?iw8dli) format("woff"),url(../fonts/mybell-icons.svg?iw8dli#shop-icons) format("svg");font-weight:400;font-style:normal}
@font-face{font-family:'bell-icon2';src:url(../fonts/mybell-icons.eot?#iefix) format("embedded-opentype"),url(../fonts/mybell-icons.woff) format("woff"),url(../fonts/mybell-icons.ttf) format("truetype"),url(../fonts/mybell-icons.svg) format("svg");font-weight:400;font-style:normal}
@font-face{font-family:'bell-icon3';src:url(../fonts/mybell-icons.eot?#iefix) format("embedded-opentype"),url(../fonts/mybell-icons.woff) format("woff"),url(../fonts/mybell-icons.ttf) format("truetype"),url(../fonts/mybell-icons.svg) format("svg");font-weight:400;font-style:normal;font-display:block;}
@font-face{font-family:'bell-icon';src:url(../fonts/mybell-icons.eot?#iefix) format("embedded-opentype"),url(../fonts/mybell-icons.woff) format("woff"),url(../fonts/mybell-icons.ttf) format("truetype"),url(../fonts/mybell-icons.svg) format("svg");font-weight:400;font-style:normal}
@font-face{font-family:'bell-icon-outline';src:url(../fonts/mybell-icons.eot?iw8dli);src:url(../fonts/mybell-icons.eot?#iefixiw8dli) format("embedded-opentype"),url(../fonts/mybell-icons.ttf?iw8dli) format("truetype"),url(../fonts/mybell-icons.woff?iw8dli) format("woff"),url(../fonts/mybell-icons.svg?iw8dli#shop-icons) format("svg");font-weight:400;font-style:normal}
@font-face{font-family:'bell-icon2';src:url(../fonts/mybell-icons.eot?#iefix) format("embedded-opentype"),url(../fonts/mybell-icons.woff) format("woff"),url(../fonts/mybell-icons.ttf) format("truetype"),url(../fonts/mybell-icons.svg) format("svg");font-weight:400;font-style:normal}
@font-face{font-family:'bell-icon3';src:url(../fonts/mybell-icons.eot?#iefix) format("embedded-opentype"),url(../fonts/mybell-icons.woff) format("woff"),url(../fonts/mybell-icons.ttf) format("truetype"),url(../fonts/mybell-icons.svg) format("svg");font-weight:400;font-style:normal;font-display:block;}

.icon, .icon2, .icon3, .icon-o {
    font-family: 'bell-icon';
    font-style: normal;
    speak: none;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}
.icon:before, .icon2:before{font-family:'bell-icon';position:relative;top:0px;}
.icon3,.icon3:before{font-family:'bell-icon3';position:static}
.icon-o:before{font-family:'bell-icon-outline'}

.icon-chevron:before,.icon-chevron-up:before,.icon-chevron-right:before,.icon-chevron-down:before,.icon-chevron-left:before {content:"\e012";
display:inline-block}
.icon-chevron-up:before {-webkit-transform:rotate(-90deg);
-ms-transform:rotate(-90deg);
transform:rotate(-90deg);
-webkit-transform-origin:45% 40%;
-ms-transform-origin:45% 40%;
transform-origin:45% 40%}
.icon-chevron-down:before {-webkit-transform:rotate(90deg);
-ms-transform:rotate(90deg);
transform:rotate(90deg)}
.icon-chevron-left:before {-webkit-transform:rotate(180deg);
-ms-transform:rotate(180deg);
transform:rotate(180deg)}
.icon-plus:before {
    content: "\e007";
    /* top: 3px !important; */
}
.icon-bell-logo:before {
    content: "\e600";
}
.icon-magnifying-glass:before {
    content: "\e615";
    top: 0 !important;
}
.icon-laptop-nopad:before {
    content: "\e616";
}
.icon-mobile-menu:before {
    content: "\e618";
}
.icon-chevron-bold:before {
    content: "\e61d";
}
.icon-search:before {
    content: "\e919";
}
.icon-chevron-left2:before {
    content: "\ea04";
}
.icon-BCE_Logo:before {
    content: "\eaa0";
}
.icon-Chevron_down:before {
    content: "\eaa3";
}
.icon-Chevron_right:before {
    content: "\eaa4";
}
.icon-RightArrow:before {
    content: "\eaa5";
}
.icon-close:before {
    content: "\eaa2";
}
.icon-cart:before {
    content: "\e617";
    top: 3px !important;
}
.icon-silhouette:before {
    content: "\e616";
    top: 3px !important;
}
.icon-check-light:before {
    content: "\e603";
}
.icon-BCESearch:before {
    content: "\eaa1";
}
.icon-small_icon_arrow_pill:before {
    content: "\e908";
}
.icon-17-16_shield_wot:before {
    content: "\e906";
}
.icon-20-21_voice_wot:before {
    content: "\e907";
}
.icon-14-06_international_wot:before {
    content: "\e901";
}
.icon-07-07_unlimited_wot:before {
    content: "\e902";
}
.icon-15-01_phonecall_wot:before {
    content: "\e903";
}
.icon-exapnd-outline-circled:before {
    content: "\e90c";
}
.icon-collapse-outline-circled:before {
    content: "\e90b";
}
.icon-small_icon_checkmark_outline:before {
    content: "\e90a";
}
.icon-small_icon_call:before {
    content: "\e909";
}
.icon-15-18_voicemail_ot:before {
    content: "\e905";
}
.icon-15-13_phone_care_ot:before {
    content: "\e904";
}
.icon-15-07_call_line_id_display_ot:before {
    content: "\e90d";
}
.icon-o-chat-bubble:before {
    content: "\e604";
}
.icon-o-handset:before {
    content: "\e610";
}
.icon-o-headphones:before {
    content: "\e611";
}
.icon-o-location:before {
    content: "\e599";
}
.icon-home:before {
    content: "\e61c";
}
.icon-back-to-top:before {
    content: "\e925";
}
.icon-facebook:before {
    content: "\e619";
}
.icon-twitter:before {
    content: "\e612";
}
.icon-blog-en:before {
    content: "\e90e";
}
.icon-youtube_bg:before {
    content: "\eafd";
}
.icon-linkedin_bg:before {
    content: "\eafb";
}
.icon-o-clock:before {
    content: "\e606";
}
.icon-close1:before {
    content: "\e624";
}
.icon-15-08_call_pick_up_bg:before {
    content: "\e90f";
}
.icon-01-08_ID_bg:before {
    content: "\e910";
}
.icon-01-07_group_partipants_bg:before {
    content: "\e911";
}
.icon-15-18_voicemail_bg:before {
    content: "\e912";
}
.icon-15-32_three_way_calling_bg:before {
    content: "\e913";
}
.icon-15-05_call_forward_bg:before {
    content: "\e914";
}
.icon-15-25_call_waiting_bg:before {
    content: "\e915";
}
.icon-15-36_last_number_redial_bg:before {
    content: "\e916";
}
.icon-18-22_voice_dialing_bg:before {
    content: "\e917";
}
.icon-15-42_call_privacy_bg:before {
    content: "\e918";
}
.icon-15-43_call_control_bg:before {
    content: "\e91a";
}
.icon-15-09_call_transfer_bg:before {
    content: "\e91b";
}
.icon-15-03_home_wireless_phone_bg:before {
    content: "\e91c";
}
.icon-01-13_user_profile_bg:before {
    content: "\e91d";
}
.icon-09-02_laptop_bg:before {
    content: "\e91e";
}
.icon-18-23_bell_calling_card_bg:before {
    content: "\e923";
}
.icon-18-24_family_contact_card_bg:before {
    content: "\e924";
}
.icon-04-02_neighbourhood_bg:before {
    content: "\e927";
}
.icon-close-solid:before {
    content: "\e60c";
}
.icon-voice-search:before {
    content: "\e91f";
}
.icon-select-trigger:before {
    content: "\e601"
}
.icon-small_icon_select_trigger_half:before {
    content: "\e920";
}
.icon-checkmark-circled:before {
    content: "\e921";
}
.icon-exclamation-circled:before {
    content: "\e922";
}
.icon-blog-fr:before {
    content: "\e926";
}
.icon-location-pin:before {
    content: "\e940";
}
.icon-o-whats-on:before {
    content: "\e807";
}
.icon-youtube:before {
    content: "\e928";
}
.icon-linked-in-logo:before {
    content: "\e929";
}
.icon-contact-us-solid:before {
    content: "\e941";
}
.icon-call-display:before {
    content: "\e956";
}
.icon-bellaliant_calling_card_bl_wot:before {
    content: "\e92d";
}
.icon-bellaliant_calling_card_bl_bg:before {
    content: "\e92e";
}
.icon-aliant_family_contact_card_bl_wot:before {
    content: "\e92f";
}
.icon-aliant_family_contact_card_bl_bg:before {
    content: "\e930";
}
.icon-small_icon_collapse_bg:before {
    content: "\e931";
}
.icon-small_icon_expand_bg:before {
    content: "\e932";
}
/* Update Jan 16, 2020 */
.icon-Small_Cart_with_arrow:before {
    content: "\e933";
}
.icon-o-locationNoPad:before {
    content: "\e934";
}
.icon-FireTV .path1:before {
    content: "\e935";
    color: #999;
}
.icon-FireTV .path2:before {
    content: "\e936";
    color: #231f20;
    margin-left: -1.861328125em;
}
.icon-Chromecast:before {
    content: "\e937";
}
.icon-AppleTV:before {
    content: "\e938";
}
.icon-Apple:before {
    content: "\e939";
}
.icon-android-TV .path1:before {
    content: "\e93a";
    color: #231f20;
}
.icon-android-TV .path2:before {
    content: "\e93b";
    color: #8a8b8a;
    margin-left: -6.375em;
}
.icon-Android:before {
    content: "\e93c";
}
/* Update Jan 21, 2020 */
.icon-icn_OneApp:before {
    content: "\e93d";
}
.icon-icn_GreatValue:before {
    content: "\e93e";
}
.icon-icn_AnyDevice:before {
    content: "\e93f";
}
.icon-icn_AllYourTV:before {
    content: "\e940";
}
.icon-star:before {
    content: "\e941";
}
/* Update Jan 22, 2020 */
.icon-Small_Check_availability:before {
    content: "\e942";
}
.icon-checkmark:before {
    content: "\e943";
}
/* Update Jan 28, 2020 */
.icon-small_log_in:before {
    content: "\e944";
}
.icon-4k_tv:before {
    content: "\e945";
}
/* Update Jan 29, 2020 */
.icon-Request_a_callback:before {
    content: "\e948";
}
.icon-Big-expand:before {
    content: "\e949";
}
.icon-Big-collapse:before {
    content: "\e94a";
}
.icon-Call_trace:before {
    content: "\e94b";
}
.icon-Call_block:before {
    content: "\e94c";
}
.icon-Temporarily_suspend:before {
    content: "\e94d";
}
/* Update Jan 30, 2020 */
.icon-Call_trace_bl_bg:before {
    content: "\e94e";
}
.icon-Call_block_bl_bg:before {
    content: "\e94f";
}
.icon-Temporarily_suspend_bl_bg:before {
    content: "\e950";
}
.icon-tvpay_perview_bl:before {
    content: "\e951";
}
/* Update Feb 03, 2020 */
.icon-tvpay_perview:before {
    content: "\e947";
}
.icon-find_channel:before {
    content: "\e952";
}
.icon-download_bl_bg:before {
    content: "\e953";
}
/* Update Feb 05, 2020 */
.icon-chat_round:before {
    content: "\e946";
}
/* Update Feb 06, 2020 */
.icon-info:before {
    content: "\e954";
}
.icon-i:before {
    content: "\e60a";
}
/* Update Feb 13, 2020 */
.icon-tv_app_icon:before {
    content: "\e955";
}
.icon-TUTORIAL:before {
    content: "\e957";
}
.icon-tablet_mobile_play:before {
    content: "\e958";
}
.icon-choice_of_4_packages:before {
    content: "\e959";
}
.icon-movies_bl_bg:before {
    content: "\e95a";
}
.icon-tomorrow_tech_or_future_bl_bg:before {
    content: "\e95b";
}
.icon-4k_whole_home_pvr:before {
    content: "\e95c";
}
.icon-wireless_tv:before {
    content: "\e95d";
}
.icon-tv_package_or_guide:before {
    content: "\e95e";
}
.icon-mobile_tv:before {
    content: "\e95f";
}
.icon-quality_tv:before {
    content: "\e960";
}
.icon-calendar:before {
    content: "\e961";
}
/* Update Feb 20, 2020 */
.icon-4k_whole_home_bg:before {
    content: "\e962";
}
.icon-Close2x:before {
    content: "\e963";
}
.icon-tomorrow_tect .path1:before {
    content: "\e964";
    color: #231f20;
}
.icon-tomorrow_tect .path2:before {
    content: "\e965";
    color: #fff;
    margin-left: -1em;
}
.icon-movies_bl .path1:before {
    content: "\e966";
    color: #231f20;
}
.icon-movies_bl .path2:before {
    content: "\e967";
    color: #fff;
    margin-left: -1em;
}
.icon-4k_tv_bl_bg .path1:before {
    content: "\e968";
    color: #231f20;
}
.icon-4k_tv_bl_bg .path2:before {
    content: "\e969";
    color: #fff;
    margin-left: -1em;
}
/* Update Feb 21, 2020 */
.icon-Small_Location:before {
    content: "\e96a";
}
.icon-Big_info:before {
    content: "\e96b";
}
.icon-call_display_bg:before {
    content: "\e96c";
}
/* Update Feb 21, 2020 */
.icon-tv_internet_mobile:before {
    content: "\e96d";
}
.icon-smart_touch_guide:before {
    content: "\e96e";
}
.icon-record_unit:before {
    content: "\e96f";
}
.icon-remote:before {
    content: "\e970";
}
.icon-quality_tv_wot:before {
    content: "\e971";
}
.icon-data_descending:before {
    content: "\e972";
}
.icon-wi_fi:before {
    content: "\e973";
}
.icon-12_search:before {
    content: "\e974";
}
.icon-external-link:before {
    content: "\eae1";
}
.icon-speech_bubble:before {
    content: "\e975";
}
.icon-satellite:before {
    content: "\e976";
}
.icon-payper_viewbl_bg:before {
    content: "\e977";
}
.icon-youtube_o:before {
    content: "\e978";
}
.icon-icon-dnld-speed:before {
    content: "\e979";
}
.icon-tv_internet_bg:before {
    content: "\e97a";
}
.icon-tomorrowtech_bl_wot:before {
    content: "\e97b";
}
.icon-wifi_bl_wot:before {
    content: "\e97c";
}
.icon-stopwatch_bl_wot:before {
    content: "\e97d";
}
.icon-stopwatch_bl_bg:before {
    content: "\e97e";
}
.icon-details-more-fill:before {
    content: "\ea01";
}
.icon-play_hover_multi .path1:before {
    content: "\e97f";
    color: #000;
    opacity: 0.5978;
}

.icon-play_hover_multi .path2:before {
    content: "\e980";
    color: #fff;
    margin-left: -1em;
}
.icon-email:before {
    content: "\e93f";
}
.icon-cc-card:before {
    content: "\e965";
}
.icon-info-bg:before {
    content: "\e60d";
}
.icon-data-3:before {
    content: "\e929";
}
.icon-i-icon:before {
    content: "\e92e";
}
.icon-error:before {
    content: "\e60a";
}
.icon-phone:before{
    content:"\e92f";
}
