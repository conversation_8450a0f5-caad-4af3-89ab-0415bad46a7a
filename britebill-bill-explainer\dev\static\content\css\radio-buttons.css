.graphical_ctrl {
    position: relative;
    padding-left: 35px;
}

.graphical_ctrl input {
    position: absolute;
    width: 48px;
    z-index: -1;
    height: 48px;
    opacity: 0;
    top: -16px;
    left: -9px;
}

.border-radius-3 {
    border-radius: 3px;
}

.ctrl_radioBtn .ctrl_element {
    border-radius: 50%;
}

.ctrl_element {
    position: absolute;
    top: -3px;
    left: 0;
    height: 25px;
    width: 25px;
    background: #fff;
    /* box-shadow: inset 0 1px 1px 1px rgba(0, 0, 1, .15); */
    border: 1px solid #ccc;
}

.graphical_ctrl input:checked ~ .ctrl_element {
    background: #cc0000;
    border: 1px solid #cc0000;
}

.graphical_ctrl.ctrl_radioBtn input:checked ~ .ctrl_element {
    background: #fff;
    border: 1px solid #c9c9c9;
}

.ctrl_element:after {
    content: '';
    position: absolute;
    display: none;
}

.graphical_ctrl input:checked ~ .ctrl_element:after {
    display: block;
}

.ctrl_radioBtn .ctrl_element:after {
    left: 5px;
    top: 5px;
    height: 13px;
    width: 13px;
    border-radius: 50%;
    background: #cc0000;
}

.graphical_ctrl input:disabled ~ .ctrl_element, .graphical_ctrl input:checked:disabled ~ .ctrl_element {
    background: #e6e6e6;
    opacity: 0.6;
    border: 1px solid #e6e6e6;
    pointer-events: none;
}

.ctrl_radioBtn input:disabled ~ .ctrl_element:after {
    background: #7b7b7b;
    pointer-events: none;
    cursor: not-allowed;
}
