$(function () {
    $(document).on('hide.bs.collapse', '[data-expand-type="single"]', function (e) {
        var $this = $(this);
        var $accordionWrap = $this.closest('.accordion-wrap');
        var $collapseTrigger = $accordionWrap.find('.collapse-trigger');
        var expandIcon = $collapseTrigger.data('icon-expand');
        var collapseIcon = $collapseTrigger.data('icon-collapse');
        
        $accordionWrap.find('.icon:not([data-icon="xs"])').first().removeClass(collapseIcon).addClass(expandIcon);
    });
    
    $('.modal-trigger').modal('show');
    
    var $body = $('body');
        
    if ($body.data('js-click-on-space-delegate-initialized') !== true) {
        $body.data('js-click-on-space-delegate-initialized', true).on('keydown', '.js-click-on-space-delegate', function (e) {
            var $this;
        
            if (32 === (e.which || e.keyCode || 0)) {
                e.preventDefault();
                e.stopPropagation();
        
                $this = $(this);
                if ($this.is('a')) {
                    this.click();                   
                } else {
                    $this.click();
                }
            }
        });
    }

});


//Please do add this in our custom for modal trapping

function modaltrappingTempSuspend() {

    var modalSuspendVirginTrapping = $(".modal.modal-suspend-virgin");

    modalSuspendVirginTrapping.on('shown.bs.modal', function (e) {
        overwriteTabIndexAndAriaHiddenDifferentHierarchy(modalSuspendVirginTrapping);
    });

    modalSuspendVirginTrapping.on('hide.bs.modal', function (e) {
        revertTabIndexAndAriaHiddenDifferentHierarchy(modalSuspendVirginTrapping);

    });
}


$(document).ready(function () {
    modaltrappingTempSuspend();
});
