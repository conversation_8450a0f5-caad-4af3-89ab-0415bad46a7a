$(document).ready(function () {
    ActualTabs.init();
    $(document).on('click', 'a.self-close-modal-link', function (e) {
        var $this = $(this);
        $this.closest(".modal").modal('toggle');
        if ($this.hasClass("techspecs-modal-link")) {
            equalizeTechSpecsHeight();
        }
    });
    $(document).on('click', '.compare-modal-link', function (e) {
        equalizeCompareSolutionHeight();
    });
    $(document).on('click', '#send-code', function () {
        $('.show-msg').removeClass('d-none');
        $('.show-msg').attr('aria-hidden', 'false');
        $(this).addClass('d-none');
    });
    $("#validate-code").validate({
        unhighlight: function (element) {
            $(element).parents('.form-group').find('.validation-message').hide();
            $(element).parents('.form-group').removeClass("error");
        },
        highlight: function (element) {
            $(element).parents('.form-group').addClass("error");
            $(element).parents('.form-group').find('.validation-message').css('display','flex');
        },
        errorPlacement: function (error, element) {
            $(element).parents('.form-group').find('.error-caption').text($(error).text());
        }
    }); 
/*code for accordian terms*/
    $('.accordion-heading a').click(function (e) {
        var $this = $(this);
        var $ariaExpanded = $this.attr('aria-expanded') == "false" ? "true" :"false";
        var $thisAreaExp = $($this.attr('href'));
        $thisAreaExp.attr('aria-expanded', $ariaExpanded);
        assignAriaLabel($this);
    });    
});

function assignAriaLabel(obj) {
    if ($(obj).attr("aria-expanded") == "false" || $(obj).attr("aria-expanded") == undefined) {
        if ($(obj).attr("data-show-text") != undefined) {
            $(obj).attr("aria-label", $(obj).attr("data-show-text"));
            $(obj).text($(obj).attr("data-show-text"));
        }
    } else {
        if ($(obj).attr("data-hide-text") != undefined) {
            $(obj).attr("aria-label", $(obj).attr("data-hide-text"));
            $(obj).text($(obj).attr("data-hide-text"));
        }
    }
}

function equalizeCompareSolutionHeight() {
    var compareSolutionHeading, compareSolutionPhone, compareSolutionDevice, compareSolutionRateplan, compareSolutionAddons, compareSolutionTotal;

    compareSolutionHeading = $(".compare-modal-heading");
    compareSolutionPhone = $(".compare-modal-phone");
    compareSolutionDevice = $(".compare-modal-device");
    compareSolutionRateplan = $(".compare-modal-rateplan");
    compareSolutionAddons = $(".compare-modal-addons");
    compareSolutionTotal = $(".compare-modal-total-monthly");

    equalizeHeight(compareSolutionHeading);
    equalizeHeight(compareSolutionPhone);
    equalizeHeight(compareSolutionDevice);
    equalizeHeight(compareSolutionRateplan);
    equalizeHeight(compareSolutionAddons);
    equalizeHeight(compareSolutionTotal);

    $(window).resize(debounce(function () {
        equalizeHeight(compareSolutionHeading);
        equalizeHeight(compareSolutionPhone);
        equalizeHeight(compareSolutionDevice);
        equalizeHeight(compareSolutionRateplan);
        equalizeHeight(compareSolutionAddons);
        equalizeHeight(compareSolutionTotal);
    }));
}

function equalizeTechSpecsHeight() {

    var techSpecsRow1, techSpecsRow2, techSpecsRow3, techSpecsRow4, techSpecsRow5, techSpecsRow6; 

    techSpecsRow1 = $(".technical-specifications-row1");
    techSpecsRow2 = $(".technical-specifications-row2");
    techSpecsRow3 = $(".technical-specifications-row3");
    techSpecsRow4 = $(".technical-specifications-row4");
    techSpecsRow5 = $(".technical-specifications-row5");
    techSpecsRow6 = $(".technical-specifications-row6");

    equalizeHeight(techSpecsRow1);
    equalizeHeight(techSpecsRow2);
    equalizeHeight(techSpecsRow3);
    equalizeHeight(techSpecsRow4);
    equalizeHeight(techSpecsRow5);
    equalizeHeight(techSpecsRow6);

    $(window).resize(debounce(function () {
        equalizeHeight(techSpecsRow1);
        equalizeHeight(techSpecsRow2);
        equalizeHeight(techSpecsRow3);
        equalizeHeight(techSpecsRow4);
        equalizeHeight(techSpecsRow5);
        equalizeHeight(techSpecsRow6);
    }));
}

function equalizeHeight(collection) {
    var mql = window.matchMedia('(min-width: 768px)');

    if (mql.matches) {
        var tallest = 0;
        collection.each(function () {
            var $this = $(this);
            $this.removeAttr("style");
            if ($this.outerHeight() > tallest) {
                tallest = $this.outerHeight();
            }
        }).each(function () {
                $(this).outerHeight(tallest);
            });
    } else {
        collection.each(function () {
            $(this).removeAttr("style");
        });
    }
}

function debounce(func, wait, immediate) { var timeout; return function () { var context = this, args = arguments; var later = function () { timeout = null; if (!immediate) func.apply(context, args) }; var callNow = immediate && !timeout; clearTimeout(timeout); timeout = setTimeout(later, wait); if (callNow) func.apply(context, args) } };            

// START Tab Control (tabs DO NOT cause page redirect)
var KEYS = {
    space: 32,
    enter: 13,
    left: 37,
    right: 39,
    up: 38,
    down: 40,
    home: 36,
    end: 35,
    esc: 27
}, ActualTabs;
ActualTabs = {
    options: {
        tabSelector: '.actual-tabs-controller-js[role=tablist] [role=tab]'
    },
    init: function (config) {
        var extendedOptions = $.extend(this.options, config),
            $tabs = $(extendedOptions.tabSelector),
            $tabList = $tabs.first().parent().closest('[role=tablist]');

        $tabList.data('actualtabs-options', JSON.stringify(extendedOptions));

        this.initTabEvents($tabs);
    }, initTabEvents: function (tabs) {
        var $tabs = tabs ? $(tabs) : $(tabs.tabSelector);

        // toggle attributes and class when a tab is clicked
        $tabs.on('click', this._tabClickListener);

        // automatic tabs automatically change tab when arrow keys are pressed. consider supporting manual tabs in the future if necessary
        $tabs.on('keydown', this._tabKeydownListener);
    }, cleanTabEvents: function (tabs) {
        var $tabs = tabs ? $(tabs) : $(tabs.tabSelector);

        $tabs.off('click', this._tabClickListener);
        $tabs.off('keydown', this._tabKeydownListener);
    }, reinit: function (tabs) {
        var $tabs = $(tabs);

        this.cleanTabEvents($tabs);
        this.initTabEvents($tabs);
    }, _tabClickListener: function () {
        var clickedTab = $(this),
            tabList = clickedTab.parent().closest('.actual-tabs-controller-js'),
            tabs,
            scrollTop,
            tabPanelContainer,
            tabPanels,
            i,
            len;

        if (tabList.hasClass('manual-tabs-js')) {
            // support manual activation in the future if necessary
        } else {
            // toggle attribute and class
            tabs = tabList.find('[role=tab]')
            tabs.attr({
                'aria-selected': 'false',
                'tabindex': '-1'
            }).removeClass('active');
            clickedTab.attr({
                'aria-selected': 'true',
                'tabindex': '0'
            }).addClass('active').filter('a').removeAttr('tabindex');

            // scroll into view horizontally
            scrollTop = $(window).scrollTop();
            clickedTab[0].scrollIntoView();
            $(window).scrollTop(scrollTop);

            // set focus if necessary. this is the case if active tab is changed using left/right/home/<USER>
            if (document.activeElement === this || $(document.activeElement).closest('.actual-tabs-controller-js')[0] === tabList[0]) {
                clickedTab.focus();
            }

            // control tab panel switching if necessary. don't do this for carousels by setting data-carousel-tablist=true
            if (tabList.data('carousel-tablist') !== true) {
                tabPanelContainer = $(tabList.data('tab-panels-container'));
                if (tabPanelContainer.length > 0) {
                    tabPanels = tabPanelContainer.find('[role=tabpanel]').filter(function () { return $(this).parent().closest('[role=tabpanel]', tabPanelContainer[0]).length === 0; });

                    for (i = 0, len = tabs.length; i < len; i++) {
                        if (tabs[i] === this) {
                            tabPanels.eq(i).attr({
                                'aria-hidden': 'false',
                                'tabindex': 0
                            });
                        } else {
                            tabPanels.eq(i).attr({
                                'aria-hidden': 'true',
                                'tabindex': -1
                            });
                        }
                    }
                }
            }
        }
    }, _tabKeydownListener: function (e) {
        var key = e.which || e.keyCode || 0,
            tabList = $(this).parent().closest('.actual-tabs-controller-js'),
            isVertical = tabList.attr('aria-orientation') === 'vertical', // if tabs are in vertical arrangement, aria-orientation=vertical must be set
            tabs = tabList.find('[role=tab]'),
            index = 0,
            len = tabs.length;

        for (; index < len; index++) {
            if (this === tabs[index]) {
                break;
            }
        }

        if (key === KEYS.home) {
            index = 0;
        } else if (key === KEYS.end) {
            index = len - 1;
        } else {
            // left & right is for horizontal tabs. up & down is for vertical tabs
            if (!isVertical && key === KEYS.left || isVertical && key === KEYS.up) {
                if (index === 0) {
                    index = len - 1;
                } else {
                    index--;
                }
            } else if (!isVertical && key === KEYS.right || isVertical && key === KEYS.down) {
                if (index === len - 1) {
                    index = 0;
                } else {
                    index++;
                }
            } else {
                return;
            }
        }

        e.preventDefault();
        e.stopPropagation();
        tabs.eq(index).trigger('click');
    }
};
// END Tab Control (tabs DO NOT cause page redirect)



