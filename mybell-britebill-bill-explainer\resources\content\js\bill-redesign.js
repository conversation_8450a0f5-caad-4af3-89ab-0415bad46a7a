
// TreeGrid - start
function TreeGrid(treegridElem, doAllowRowFocus, doStartRowFocus) {
	function initAttributes() {

        //Polyfill IE- forEach
        if (window.NodeList && !NodeList.prototype.forEach) {
            NodeList.prototype.forEach = Array.prototype.forEach;
        }

        // process non-collapsible parent rows
        treegridElem.querySelectorAll('.not-collapsible-row').forEach(function (row) {
            row.setAttribute('aria-expanded', 'true');
        });

        // Make sure focusable elements are not in the tab order
        // They will be added back in for the active row
        setTabIndexOfFocusableElems(treegridElem, -1);

        // Add tabindex="0" to first row, "-1" to other rows
        // We will use the roving tabindex method since aria-activedescendant
        // does not work in IE
        var rows = getAllRows();
        var index = rows.length;
        var startRowIndex = doStartRowFocus ? 0 : -1;

        while (index--) {
            if (doAllowRowFocus) {
                rows[index].tabIndex = index === startRowIndex ? 0 : -1;
            }
            else {
                setTabIndexForCellsInRow(rows[index], -1);
                moveAriaExpandedToFirstCell(rows[index]);
            }
        }

        if (doStartRowFocus) {
            return;
        }

        // Start with cell focus
        var firstCell = getNavigableCols(rows[0])[0];
        setTabIndexForCell(firstCell); 

    }

    function setTabIndexForCell(cell, tabIndex) {
        var focusable = getFocusableElems(cell)[0] || cell;
        focusable.tabIndex = tabIndex;
    }

    function setTabIndexForCellsInRow(row, tabIndex) {
        var cells = getNavigableCols(row);
        var cellIndex = cells.length;
        while (cellIndex--) {
            setTabIndexForCell(cells[cellIndex], tabIndex);
        }
    }

    function getAllRows() {
        var nodeList = treegridElem.querySelectorAll('tbody > tr');
        return Array.prototype.slice.call(nodeList);
    }

    function getFocusableElems(root) {
        // textarea not supported as a cell widget as it's multiple lines
        // and needs up/down keys
        // These should all be descendants of a cell
        var nodeList = root.querySelectorAll('a,button,input,td>[tabindex]');
        return Array.prototype.slice.call(nodeList);
    }

    function setTabIndexOfFocusableElems(root, tabIndex) {
        var focusableElems = getFocusableElems(root);
        var index = focusableElems.length;
        while (index--) {
            focusableElems[index].tabIndex = tabIndex;
        }
    }

    function getAllNavigableRows() {
        var nodeList = treegridElem.querySelectorAll('tbody > tr:not([class~="hidden"])');
        // Convert to array so that we can use array methods on it
        return Array.prototype.slice.call(nodeList);
    }

    function getNavigableCols(currentRow) {
        var nodeList = currentRow.getElementsByTagName('td');
        return Array.prototype.slice.call(nodeList);
    }

    function restrictIndex(index, numItems) {
        if (index < 0) {
            return 0;
        }
        return index >= numItems ? index - 1 : index;
    }

    function focus(elem) {
        elem.tabIndex = 0; // Ensure focusable
        elem.focus();
    }

    function focusCell(cell) {
        // Check for focusable child such as link or textbox
        // and use that if available
        var focusableChildren = getFocusableElems(cell);
        focus(focusableChildren[0] || cell);
    }

    // Restore tabIndex to what it should be when focus switches from
    // one treegrid item to another
    function onFocusIn(event) {
        var newTreeGridFocus =
            event.target !== window && treegridElem.contains(event.target) &&
            event.target;

        // The last row we considered focused
        var oldCurrentRow = enableTabbingInActiveRowDescendants.tabbingRow;
        if (oldCurrentRow) {
            enableTabbingInActiveRowDescendants(false, oldCurrentRow);
        }
        if (doAllowRowFocus && onFocusIn.prevTreeGridFocus &&
            onFocusIn.prevTreeGridFocus.localName === 'td') {
            // Was focused on td, remove tabIndex so that it's not focused on click
            onFocusIn.prevTreeGridFocus.removeAttribute('tabindex');
        }

        if (newTreeGridFocus) {
            // Stayed in treegrid
            if (oldCurrentRow) {
                // There will be a different current row that will be
                // the tabbable one
                oldCurrentRow.tabIndex = -1;
            }

            // The new row
            var currentRow = getRowWithFocus();
            if (currentRow) {
                currentRow.tabIndex = 0;
                // Items within current row are also tabbable
                enableTabbingInActiveRowDescendants(true, currentRow);
            }
        }

        onFocusIn.prevTreeGridFocus = newTreeGridFocus;
    }

    // Set whether interactive elements within a row are tabbable
    function enableTabbingInActiveRowDescendants(isTabbingOn, row) {
        if (row) {
            setTabIndexOfFocusableElems(row, isTabbingOn ? 0 : -1);
            if (isTabbingOn) {
                enableTabbingInActiveRowDescendants.tabbingRow = row;
            }
            else {
                if (enableTabbingInActiveRowDescendants.tabbingRow === row) {
                    enableTabbingInActiveRowDescendants.tabbingRow = null;
                }
            }
        }
    }

    // The row with focus is the row that either has focus or an element
    // inside of it has focus
    function getRowWithFocus() {
        return getContainingRow(document.activeElement);
    }

    function getContainingRow(start) {
        var possibleRow = start;
        if (treegridElem.contains(possibleRow)) {
            while (possibleRow !== treegridElem) {
                if (possibleRow.localName === 'tr') {
                    return possibleRow;
                }
                possibleRow = possibleRow.parentElement;
            }
        }
    }

    function isRowFocused() {
        return getRowWithFocus() === document.activeElement;
    }

    // Note: contenteditable not currently supported
    function isEditableFocused() {
        var focusedElem = document.activeElement;
        return focusedElem.localName === 'input';
    }

    function getColWithFocus(currentRow) {
        if (currentRow) {
            var possibleCol = document.activeElement;
            if (currentRow.contains(possibleCol)) {
                while (possibleCol !== currentRow) {
                    if (possibleCol.localName === 'td') {
                        return possibleCol;
                    }
                    possibleCol = possibleCol.parentElement;
                }
            }
        }
    }

    function getLevel(row) {
        return row && parseInt(row.getAttribute('aria-level'));
    }

    // Move backwards (direction = -1) or forwards (direction = 1)
    // If we also need to move down/up a level, requireLevelChange = true
    // When
    function moveByRow(direction, requireLevelChange) {
        var currentRow = getRowWithFocus();
        var requiredLevel = requireLevelChange && currentRow &&
            getLevel(currentRow) + direction;
        var rows = getAllNavigableRows();
        var numRows = rows.length;
        var rowIndex = currentRow ? rows.indexOf(currentRow) : -1;
        // When moving down a level, only allow moving to next row as the
        // first child will never be farther than that
        var maxDistance = requireLevelChange && direction === 1 ? 1 : NaN;

        // Move in direction until required level is found
        do {
            if (maxDistance-- === 0) {
                return; // Failed to find required level, return without focus change
            }
            rowIndex = restrictIndex(rowIndex + direction, numRows);
        }
        while (requiredLevel && requiredLevel !== getLevel(rows[rowIndex]));

        if (!focusSameColInDifferentRow(currentRow, rows[rowIndex])) {
            focus(rows[rowIndex]);
        }
    }

    function focusSameColInDifferentRow(fromRow, toRow) {
        var currentCol = getColWithFocus(fromRow);
        if (!currentCol) {
            return;
        }

        var fromCols = getNavigableCols(fromRow);
        var currentColIndex = fromCols.indexOf(currentCol);

        if (currentColIndex < 0) {
            return;
        }

        var toCols = getNavigableCols(toRow);
        // Focus the first focusable element inside the <td>
        focusCell(toCols[currentColIndex]);
        return true;
    }

    function moveToExtreme(direction) {
        var currentRow = getRowWithFocus();
        if (!currentRow) {
            return;
        }
        var currentCol = getColWithFocus(currentRow);
        if (currentCol) {
            moveToExtremeCol(direction, currentRow);
        }
        else {
            // Move to first/last row
            moveToExtremeRow(direction);
        }
    }

    function moveByCol(direction) {
        var currentRow = getRowWithFocus();
        if (!currentRow) {
            return;
        }
        var cols = getNavigableCols(currentRow);
        var numCols = cols.length;
        var currentCol = getColWithFocus(currentRow);
        var currentColIndex = cols.indexOf(currentCol);
        // First right arrow moves to first column
        var newColIndex = (currentCol || direction < 0) ? currentColIndex +
            direction : 0;
        // Moving past beginning focuses row
        if (doAllowRowFocus && newColIndex < 0) {
            focus(currentRow);
            return;
        }
        newColIndex = restrictIndex(newColIndex, numCols);
        focusCell(cols[newColIndex]);
    }

    function moveToExtremeCol(direction, currentRow) {
        // Move to first/last col
        var cols = getNavigableCols(currentRow);
        var desiredColIndex = direction < 0 ? 0 : cols.length - 1;
        focusCell(cols[desiredColIndex]);
    }

    function moveToExtremeRow(direction) {
        var rows = getAllNavigableRows();
        var newRow = rows[direction > 0 ? rows.length - 1 : 0];
        if (!focusSameColInDifferentRow(getRowWithFocus(), newRow)) {
            focus(newRow);
        }
    }

    function doPrimaryAction() {
        var currentRow = getRowWithFocus();
        if (!currentRow) {
            return;
        }

        //// If row has focus, open message
        //if (currentRow === document.activeElement) {
        //    alert('Message from ' + currentRow.children[2].innerText + ':\n\n' +
        //        currentRow.children[1].innerText);
        //    return;
        //}

        // If first col has focused, toggle expand/collapse
        toggleExpanded(currentRow);
    }

    function toggleExpanded(row) {
        var cols = getNavigableCols(row);
        var currentCol = getColWithFocus(row);
        if (currentCol === cols[3] && isExpandable(row)) {
            changeExpanded(!isExpanded(row), row);
        }
    }


    //function getAllnotCollapsibleRows() {

    //    var rows = treegridElem.querySelectorAll('tbody > tr.not-collapsible-row');

    //    alert(rows.length);

    //}


    function changeExpanded(doExpand, row) {
        var currentRow = row || getRowWithFocus();

        //if (currentRow.classList.contains("not-collapsible-row")) {
        //    return;
        //}

        if (!currentRow) {
            return;
        }
        var currentLevel = getLevel(currentRow);
        var rows = getAllRows();
        var currentRowIndex = rows.indexOf(currentRow);
        var didChange;
        var doExpandLevel = [];
        doExpandLevel[currentLevel + 1] = doExpand;

        var noOfNotCollapsibleRows = [];


       
        while (++currentRowIndex < rows.length) {
            var nextRow = rows[currentRowIndex];
            var rowLevel = getLevel(nextRow);
            if (rowLevel <= currentLevel) {
                break; // Next row is not a level down from current row
            }
            // Only expand the next level if this level is expanded
            // and previous level is expanded
            doExpandLevel[rowLevel + 1] =
                doExpandLevel[rowLevel] &&
                isExpanded(nextRow);
            var willHideRow = !doExpandLevel[rowLevel];
            var isRowHidden = nextRow.classList.contains('hidden');

            //if (nextRow.classList.contains("not-collapsible-row")) {
            //    noOfNotCollapsibleRows.push(nextRow);
            //}

            if (willHideRow !== isRowHidden) {
                if (willHideRow) {
                    nextRow.classList.add('hidden');
                }
                else {
                    nextRow.classList.remove('hidden');
                }
                didChange = true;
            }
        }

        //alert(noOfNotCollapsibleRows.join(","));

        if (didChange) {
            setAriaExpanded(currentRow, doExpand);
            return true;
        }


    }

    // Mirror aria-expanded from the row to the first cell in that row
    // (TBD is this a good idea? How else will screen reader user hear
    // that the cell represents the opportunity to collapse/expand rows?)
    function moveAriaExpandedToFirstCell(row) {
        var expandedValue = row.getAttribute('aria-expanded');
        var firstCell = getNavigableCols(row)[0];
        if (expandedValue) {
            firstCell.setAttribute('aria-expanded', expandedValue);
            row.removeAttribute('aria-expanded');
        }
    }

    function getAriaExpandedElem(row) {
        return doAllowRowFocus ? row : getNavigableCols(row)[0];
    }

	function isCollapsible(row) {
		return !row.classList.contains('not-collapsible-row');
	}

    function setAriaExpanded(row, doExpand) {
        var elem = getAriaExpandedElem(row);
        elem.setAttribute('aria-expanded', doExpand);
    }

	function isExpandable(row) {
        var  elem = getAriaExpandedElem(row);

		return isCollapsible(row) && elem.hasAttribute('aria-expanded');
    }

    function isExpanded(row) {
        var elem = getAriaExpandedElem(row);
        return elem.getAttribute('aria-expanded') === 'true';
    }

    function onKeyDown(event) {
        var ENTER = 13;
        var UP = 38;
        var DOWN = 40;
        var LEFT = 37;
        var RIGHT = 39;
        var HOME = 36;
        var END = 35;
        var CTRL_HOME = -HOME;
        var CTRL_END = -END;

        var numModifiersPressed = event.ctrlKey + event.altKey + event.shiftKey +
            event.metaKey;

        var key = event.keyCode;

        if (numModifiersPressed === 1 && event.ctrlKey) {
            key = -key; // Treat as negative key value when ctrl pressed
        }
        else if (numModifiersPressed) {
            return;
        }

        switch (key) {
            case DOWN:
                moveByRow(1);
                break;
            case UP:
                moveByRow(-1);
                break;
            case LEFT:
                if (isEditableFocused()) {
                    return;  // Leave key for editable area
                }
				if (isRowFocused()) {
					isCollapsible(getRowWithFocus()) && changeExpanded(false) || moveByRow(-1, true);
                }
                else {
                    moveByCol(-1);
                }
                break;
            case RIGHT:
                if (isEditableFocused()) {
                    return;  // Leave key for editable area
                }

                // If row: try to expand
                // If col or can't expand, move column to right
                if (!isRowFocused() || !changeExpanded(true)) {
                    moveByCol(1);
                }
                break;
            case CTRL_HOME:
                moveToExtremeRow(-1);
                break;
            case HOME:
                if (isEditableFocused()) {
                    return;  // Leave key for editable area
                }
                moveToExtreme(-1);
                break;
            case CTRL_END:
                moveToExtremeRow(1);
                break;
            case END:
                if (isEditableFocused()) {
                    return;  // Leave key for editable area
                }
                moveToExtreme(1);
                break;
            case ENTER:
                doPrimaryAction();
                break;
            default:
                return;
        }

        // Important: don't use key for anything else, such as scrolling
        event.preventDefault();
    }

    // Toggle row expansion if the click is over the expando triangle
    // Since the triangle is a pseudo element we can't bind an event listener
    // to it. Another option is to have an actual element with role="presentation"
    function onClick(event) {
        var target = event.target;
        if (target.localName !== 'td') {
            return;
        }

        var row = getContainingRow(event.target);
        if (!isExpandable(row)) {
            return;
        }

        // Determine if mouse coordinate is just to the left of the start of text
        var range = document.createRange();
        range.selectNodeContents(target.firstChild);
        var left = range.getBoundingClientRect().left;
        var EXPANDO_WIDTH = 20;

        if (event.clientX < left && event.clientX > left - EXPANDO_WIDTH) {
            changeExpanded(!isExpanded(row), row);
        }
    }

    // Double click on row toggles expansion
    function onDoubleClick(event) {
        var row = getContainingRow(event.target);
        if (row) {
            if (isExpandable(row)) {
                changeExpanded(!isExpanded(row), row);
            }
            event.preventDefault();
        }
    }

    function onRowClick(event) {
        var row = getContainingRow(event.target);
        if (row) {
            if (isExpandable(row)) {
                changeExpanded(!isExpanded(row), row);
            }
            event.preventDefault();
        }
    }

    initAttributes();
    treegridElem.addEventListener('keydown', onKeyDown);
    treegridElem.addEventListener('click', onRowClick);
    treegridElem.addEventListener('dblclick', onRowClick);
    // Polyfill for focusin necessary for Firefox < 52
    window.addEventListener(window.onfocusin ? 'focusin' : 'focus',
        onFocusIn, true);
}

document.addEventListener('DOMContentLoaded', function () {
    if ($('#billing-treegrid').length) {
        var doAllowRowFocus = true;
        var doStartRowFocus = true;
        TreeGrid(document.getElementById('billing-treegrid'), doAllowRowFocus, doStartRowFocus);
    }
});


// Switch between dynamic title of accordion
$(document).on('show.bs.collapse hide.bs.collapse', '[data-title-type="dynamic"]', function (e) {
    if ($(this).is(e.target)) {
        var title = $(this).closest('.accordion-wrap').find('.accordion-title');
        var expandTitle = title.data('expand-title');
        var collapseTitle = title.data('collapse-title');
        if (e.type === 'hide') {
            title.html(expandTitle);
        }
        else if (e.type === 'show') {
            title.html(collapseTitle);
        }
    }
});

// START document-ready
(function ($) {
	// toggle visibility of the sr-only and injectable elements used by the grid for accessibility
	$('.billing-treegrid').on('mouseenter focusin', function () {
		$('#sr-only-expand-text, #sr-only-not-collapsible-text').removeClass('d-none');
	}).on('mouseleave focusout', function () {
		var $this = $(this);

		if (this.contains(document.activeElement) || $this.has(':hover').length > 0) {
			return;
		}

		$('#sr-only-expand-text, #sr-only-not-collapsible-text').addClass('d-none');
	});

    //Append "Expand your details" to row on hover/focus
	$(".billing-treegrid-row-services").on("mouseenter focus", function () {
		var $this = $(this),
			$service = $this.find('.billing-treegrid-services-title');
		if ($this.is("[aria-expanded='true']")) return;

		$('#injectible-expand-text').removeClass('d-none').insertAfter($service);
		$service.closest('[role=row]').attr('aria-describedby', 'sr-only-expand-text');
    }).on("mouseleave blur", function () {
		$('main').append($('#injectible-expand-text').addClass('d-none'));
		$(this).find('.billing-treegrid-services-title').closest('[role=row]').removeAttr('aria-describedby');
    });

    //not collapsible row
	$(".not-collapsible-row").attr('aria-describedby', 'sr-only-not-collapsible-text');


    //set event handler for tooltip enter keypress
    tooltipKeypress();

    // users should be able to activate "buttons" using the space key. this is for anchor tags so enter key is already supported
    $('a[role=button]:not(.click-on-space)').on('keypress', function (e) {
        if (KEYS.space === (e.which || e.keyCode || 0)) {
            e.preventDefault();
            e.stopPropagation();
            $(this).trigger("click");
        }
    });
    //Toggle Message function
    $('#message-button-1').click(function (e) {
        //console.log("test");
        $('.message').toggleClass("overflow-information");
        var messagebutton = $(this);
        var collapsetitle = messagebutton.find('.message-title').data('collapse-title');
        var expandtitle = messagebutton.find('.message-title').data('expand-title');
        var expandIcon = messagebutton.data('icon-expand');
        var collapseIcon = messagebutton.data('icon-collapse'); 

        if (expandIcon && collapseIcon) {
            if (messagebutton.attr('aria-expanded') !== "true") {
                messagebutton.find('.message-title').html(collapsetitle);
                messagebutton.find('.icon').addClass(collapseIcon).removeClass(expandIcon);
                messagebutton.attr('aria-expanded', 'true');
                
            }
            else {
                messagebutton.find('.message-title').html(expandtitle);
                messagebutton.find('.icon').removeClass(collapseIcon).addClass(expandIcon);
                messagebutton.attr('aria-expanded', 'false');
                
            }
        }
    });

})(jQuery);

// supports dismissing of tooltip when ESC is pressed while trigger button is focused. 
// also supports showing of tooltip upon pressing SPACE / ENTER on the button trigger(dismisses on blur or ESC)
function onKeydownTooltip(e) {
    var keyCode = e.keyCode, $el;

    if (27 === keyCode) {
        $(this).tooltip('hide');
    }
    else if (32 === keyCode || 13 === keyCode) {
        e.preventDefault();
        e.stopPropagation();
        $el = $(this);
        $el.tooltip('show');
    }
}

// Tooltip modal keypress on mobile view
function tooltipKeypress() {
    $('.tooltip-interactive').on('keypress', function (e) {
        if (e.keyCode == 13 || e.keyCode == 32) {
            $(this).trigger('click');
        }
    });
};

// Close tooltip when escape key is pressed
$('[data-toggle="tooltip"]').on('keydown', function (event) {
    if (event.key === "Escape") {
        $(this).tooltip('hide');
    }
});


var KEYS = {
    space: 32,
    enter: 13,
    left: 37,
    right: 39,
    up: 38,
    down: 40,
    home: 36,
    end: 35,
    esc: 27
}, ActualTabs;


// START Tab Control (tabs DO NOT cause page redirect)
ActualTabs = {
    options: {
        tabSelector: '.actual-tabs-controller-js[role=tablist] [role=tab]'
    },
    init: function (config) {
        var extendedOptions = $.extend(this.options, config),
            $tabs = $(extendedOptions.tabSelector),
            $tabList = $tabs.first().parent().closest('[role=tablist]');

        $tabList.data('actualtabs-options', JSON.stringify(extendedOptions));

        this.initTabEvents($tabs);
    }, initTabEvents: function (tabs) {
        var $tabs = tabs ? $(tabs) : $(tabs.tabSelector);

        // toggle attributes and class when a tab is clicked
        $tabs.on('click', this._tabClickListener);

        // automatic tabs automatically change tab when arrow keys are pressed. consider supporting manual tabs in the future if necessary
        $tabs.on('keydown', this._tabKeydownListener);
    }, cleanTabEvents: function (tabs) {
        var $tabs = tabs ? $(tabs) : $(tabs.tabSelector);

        $tabs.off('click', this._tabClickListener);
        $tabs.off('keydown', this._tabKeydownListener);
    }, reinit: function (tabs) {
        var $tabs = $(tabs);

        this.cleanTabEvents($tabs);
        this.initTabEvents($tabs);
    }, _tabClickListener: function () {
        var clickedTab = $(this),
            tabList = clickedTab.parent().closest('.actual-tabs-controller-js'),
            tabs,
            scrollTop,
            tabPanelContainer,
            tabPanels,
            i,
            len;

        if (tabList.hasClass('manual-tabs-js')) {
            // support manual activation in the future if necessary
        } else {
            // toggle attribute and class
            tabs = tabList.find('[role=tab]')
            tabs.attr({
                'aria-selected': 'false',
                'tabindex': '-1'
            }).removeClass('active');
            clickedTab.attr({
                'aria-selected': 'true',
                'tabindex': '0'
            }).addClass('active').filter('a').removeAttr('tabindex');

            // scroll into view horizontally
            scrollTop = $(window).scrollTop();
            // Remove the line of code below as it causes flickering issue on IE
            //clickedTab[0].scrollIntoView();
            $(window).scrollTop(scrollTop);

            // set focus if necessary. this is the case if active tab is changed using left/right/home/<USER>
            if (document.activeElement === this || $(document.activeElement).closest('.actual-tabs-controller-js')[0] === tabList[0]) {
                clickedTab.focus();
            }

            // control tab panel switching if necessary. don't do this for carousels by setting data-carousel-tablist=true
            if (tabList.data('carousel-tablist') !== true) {
                tabPanelContainer = $(tabList.data('tab-panels-container'));
                if (tabPanelContainer.length > 0) {
                    tabPanels = tabPanelContainer.find('[role=tabpanel]').filter(function () { return $(this).parent().closest('[role=tabpanel]', tabPanelContainer[0]).length === 0; });

                    for (i = 0, len = tabs.length; i < len; i++) {
                        if (tabs[i] === this) {
                            tabPanels.eq(i).attr({
                                'tabpanel-selected': 'true',
                                'tabindex': 0
                            });
                        } else {
                            tabPanels.eq(i).attr({
                                'tabpanel-selected': 'false',
                                'tabindex': -1
                            });
                        }
                    }
                }
            }
        }
    }, _tabKeydownListener: function (e) {
        var key = e.which || e.keyCode || 0,
            tabList = $(this).parent().closest('.actual-tabs-controller-js'),
            isVertical = tabList.attr('aria-orientation') === 'vertical', // if tabs are in vertical arrangement, aria-orientation=vertical must be set
            tabs = tabList.find('[role=tab]'),
            index = 0,
            len = tabs.length;

        for (; index < len; index++) {
            if (this === tabs[index]) {
                break;
            }
        }

        if (key === KEYS.home) {
            index = 0;
        } else if (key === KEYS.end) {
            index = len - 1;
        } else {
            // left & right is for horizontal tabs. up & down is for vertical tabs
            if (!isVertical && key === KEYS.left || isVertical && key === KEYS.up) {
                if (index === 0) {
                    index = len - 1;
                } else {
                    index--;
                }
            } else if (!isVertical && key === KEYS.right || isVertical && key === KEYS.down) {
                if (index === len - 1) {
                    index = 0;
                } else {
                    index++;
                }
            } else {
                return;
            }
        }

        e.preventDefault();
        e.stopPropagation();
        tabs.eq(index).trigger('click');
    }
};
// END Tab Control (tabs DO NOT cause page redirect)

// END global and constants
// START document-ready
(function ($) {
    // initialize actual tabs
    ActualTabs.init();

    // header-tab-control is only for group of links that looks like tabs but DO NOT function as tabs
    // set scrollLeft of header-tab-control to make sure the active item is visible in case there's overflow, we'll center it if possible to make it easier to see that the area is scrollable
    $('.header-tab-control').each(function () {
        var scrollableEl = $(this),
            activeEl = scrollableEl.find('li a.active, li a[aria-current]:not([aria-current=false])').first(),
            listEl = activeEl.parent().closest('ul');

        if (activeEl.is(':not([aria-current])')) {
            activeEl.attr('aria-current', 'page');
        }

        scrollableEl.scrollLeft(activeEl.offset().left - listEl.offset().left - listEl.outerWidth() / 2 + activeEl.outerWidth() / 2);
    });

    // users should be able to activate "buttons" using the space key. this is for anchor tags so enter key is already supported
    $('a[role=button]:not(.click-on-space)').on('keypress', function (e) {
        if (KEYS.space === (e.which || e.keyCode || 0)) {
            e.preventDefault();
            e.stopPropagation();
            $(this).trigger("click");
        }
    });

    // select elements now use bell-blue color if it has a selected value and the selected option is also in that color    
    $('select.colored-selected').each(function () {
        // if the colored-selected class is found, automatically set the other class and attributes
        var el = $(this),
            options = el.find('option'),
            selected = options.eq(this.selectedIndex);

        options.not('[selected]').removeClass('selected');
        selected.addClass('selected');

        options.filter('[value=""]').addClass('no-value');

        if (selected.hasClass('no-value')) {
            el.removeClass('has-selected');
        } else {
            el.addClass('has-selected');
        }
    }).on('change', function () {
        // add a change event listener to toggle the classes and attributes accordingly
        var el = $(this),
            selected = el.find('option').eq(this.selectedIndex);

        el.find('option.selected').removeClass('selected').removeAttr('selected');
        if (selected.hasClass('no-value')) {
            el.removeClass('has-selected');
        } else {
            el.addClass('has-selected');
        }
        selected.addClass('selected').attr('selected', 'selected');
    });

    // modal accessibility fix
    $('.modal').on('shown.bs.modal', function () {
        overwriteTabIndexAndAriaHiddenDifferentHierarchy($(this));
    }).on('hidden.bs.modal', function () {
        var el = $(this),
            dismissFocusElSelector = el.data('dismiss-focus-target');
        revertTabIndexAndAriaHiddenDifferentHierarchy(el);
        // if data-dimiss-focus-target is set, we'll set the focus to it once the modal has been closed
        if (undefined !== dismissFocusElSelector) {
            $(dismissFocusElSelector).focus();
        }
    });
    //Check if element has sroll data-toggle="modal"
    $('[data-toggle="modal"]').on('click', function () {
        if ($('.table-scrollable-wrapper').length > 0) checkScrollableTable();
    })

    $('.tooltip-static').each(function() {
        $(this).tooltip({
            container: $(this)
        });
    });

    $('.tooltip-static').on('shown.bs.tooltip', function () {
        $('.tooltip').removeAttr('tabindex');
    })

})(jQuery);
// END document-ready

//Pagination Count
$(document).ready(function () {
    var CLASS_DISABLED = "disabled",
        CLASS_ACTIVE = "active",
        CLASS_SIBLING_ACTIVE = "active-sibling",
        DATA_KEY = "pagination";
    //Count  OL items 

    $(".pagination").each(initPagination);

    function initPagination() {
        var $this = $(this);

        $this.data(DATA_KEY, $this.find("li").index(".active"));

        $this.find(".bill-prev").on("click", navigateSinglePage);
        $this.find(".bill-next").on("click", navigateSinglePage);
        $this.find("li").on("click", function () {
            var $parent = $(this).closest(".pagination");
            $parent.data(DATA_KEY, $parent.find("li").index(this));
            changePage.apply($parent);
        });

    }

    function navigateSinglePage() {
        if (!$(this).hasClass(CLASS_DISABLED)) {
            var $parent = $(this).closest(".pagination"),
                currActive = parseInt($parent.data("pagination"), 10);
            currActive += 1 * ($(this).hasClass("bill-prev") ? -1 : 1);
            $parent.data(DATA_KEY, currActive);
            changePage.apply($parent);

        }

    }

    function changePage(currActive) {
        var $list = $(this).find("li"),
            currActive = parseInt($(this).data(DATA_KEY), 10);

        $list.filter("." + CLASS_ACTIVE).removeClass(CLASS_ACTIVE);
        $list.filter("." + CLASS_SIBLING_ACTIVE).removeClass(CLASS_SIBLING_ACTIVE);

        $list.eq(currActive).addClass(CLASS_ACTIVE);

        if (currActive === 0) {
            $(this).find(".bill-prev").addClass(CLASS_DISABLED);
        } else {
            $list.eq(currActive - 1).addClass(CLASS_SIBLING_ACTIVE);
            $(this).find(".bill-prev").removeClass(CLASS_DISABLED);
        }

        if (currActive == ($list.length - 1)) {
            $(this).find(".bill-next").addClass(CLASS_DISABLED);
        } else {
            $(this).find(".bill-next").removeClass(CLASS_DISABLED);
        }
    }

});
