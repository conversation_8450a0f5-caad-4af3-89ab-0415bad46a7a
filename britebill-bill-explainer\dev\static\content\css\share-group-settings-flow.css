/*This copied from Shop form Global Header*/
/* START the header/nav and footer were not completely rewritten yet but several styles were added to make them look as close to the mockups as possible. these can be removed once those have been updated already */

footer .btn-site-feedback.btn-primary:not(:disabled):not(.disabled):focus {
    background-color: #ccd7e4;
}

/* mobile and tablet */
@media (max-width: 991.98px) {
    .global-navigation .aliant-home .bellSlimSemibold-Nav {
        font-size: 14px;
    }

    .sub-nav-header {
        color: #fff;
        font-size: 12px;
        padding: 12px 50px 12px 30px;
        margin: 0;
    }
}

/* desktop and larger */
@media (min-width: 992px) {
    .global-navigation .connector .connector-brand.aliant a {
        font-size: 26px;
        top: 0;
    }

    .global-navigation .connector .menu-flyout-visible .sub-nav-group.has-two-columns {
        width: 180%;
    }

    .nav-links-two-columns {
        column-count: 2;
        column-gap: 30px;
        column-fill: auto;
    }

        .nav-links-two-columns li {
            display: inline-block;
            vertical-align: top;
        }

    .global-navigation .connector .menu-flyout-visible .sub-nav-item .sub-nav-level4.nav-links-two-columns {
        padding-right: 30px;
    }

        .global-navigation .connector .menu-flyout-visible .sub-nav-item .sub-nav-level4.nav-links-two-columns li > a {
            padding-right: 20px;
        }

    .global-navigation .connector .menu-flyout-visible .sub-nav-group.sub-nav-large {
        width: 320%;
    }

    .global-navigation .connector .menu-flyout-visible .menu-flyout-root {
        min-height: 300px;
    }

    .sub-nav-item {
        overflow-y: hidden;
    }
}

/* END header and/or footer styles */




/*Data Manager Settings*/

.display-flex {
  display: flex;
}
.display-flex:not(.dm-schedule-details).hide {
  display: none;
}
.dsm-name-1 {
  height: 22px;
  width: 53px;
  color: #111111;
  font-family: Arial;
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 22px;
}
.dsm-modal-footer,
.dsm-modal-footer-2 {
  background-color: #f0f0f0;
  justify-content: flex-start;
}
.dsm-custom-margin,
.dsm-custom-margin-2 {
  margin-right: 30px !important;
}
.dsm-spacer-1 {
  width: 100px;
}
.dsm-spacer-2 {
  width: 36px;
}
.dsm-spacer-3 {
  width: 31px;
  margin-right: 15px;
}
.data-blocking-tempor {
  color: #111111;
  font-size: 24px;
  font-weight: 900;
  letter-spacing: -0.4px;
  line-height: 26px;
}

.dsm-modal-dialog {
  box-shadow: 0 14px 36px 0 rgba(0, 0, 0, 0.3);
}

.dsm-radiogroup input:checked ~ span {
  position: absolute;
  left: 0;
  height: 24px;
  width: 24px;
  background: #fff;
  box-shadow: inset 0 0px 3px 0 rgba(0, 0, 0, 0.2);
  border: 1px solid #ccc;
  border-radius: 50%;
  cursor: pointer;
  pointer-events: none;
  background: #003778;
  border: 1px solid #003778;
}
.dsm-radiogroup input:checked ~ span:after {
  display: block;
  left: 5px;
  top: 5px;
  height: 12px;
  width: 12px;
  border-radius: 50%;
  background: #fff;
  content: "";
  position: absolute;
}
.dsm-radiogroup input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.dsm-radiogroup-input {
  height: 24px;
  width: 24px;
  position: relative;
  pointer-events: none;
}
.dsm-hr {
  border-color: #d4d4d4;
}
.dsm-radiogroup span {
  position: absolute;
  left: 0;
  height: 24px;
  width: 24px;
  background: #fff;
  box-shadow: inset 0 0px 3px 0 rgba(0, 0, 0, 0.2);
  border: 1px solid #ccc;
  border-radius: 50%;
  cursor: pointer;
  pointer-events: none;
}
.dsm-radiogroup label {
  cursor: pointer;
  padding-left: 40px;
  margin-left: -40px;
  line-height: 24px;
}
.by-deleting-your-my-b {
  color: #111111;
  font-family: Arial;
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22px;
}
.dsm-txtSize36 {
  font-size: 36px;
}

.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #999999;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: #00549a;
}

input:focus + .slider {
  box-shadow: 0 0 1px #00549a;
}

input:checked + .slider:before {
  -webkit-transform: translateX(20px);
  -ms-transform: translateX(20px);
  transform: translateX(20px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

.dsm-flex-grow-1 {
  flex-grow: 1;
}

.dsm-check-label {
  color: #111111;
  font-family: Arial;
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 22px;
}
.dsm-dot {
  display: flex;
}
.dsm-dot > div > span,
.dsm-dot > a > span {
  height: 31px;
  width: 31px;
  background-color: #8cb8d6;
  border-radius: 50%;
  display: inline-block;
  border-color: #d4d4d4;
}
.dsm-dot > div,
.dsm-dot > a {
  position: relative;
  margin-right: 15px;
  height: 31px;
  cursor: pointer;
}
.dsm-dot > div > div,
.dsm-dot > a > div {
  height: 31px;
  width: 31px;
  position: absolute;
  top: 0px;
  display: flex;
}
.dsm-dot p,
.dsm-dot > a > div > span {
  color: white;
  width: 31px;
  text-align: center;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 16px;
  margin: auto;
}
.dsm-time-label {
  height: 18px;
  color: #111111;
  font-family: Arial;
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 18px;
}
.dsm-time-label.txtRed {
  color: #bd2025;
}
.dsm-eg-time {
  height: 14px;
  width: 110px;
  color: #555555;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 0;
  line-height: 14px;
}
.dsm-input-1,
.dsm-input-2 {
  height: 44px;
  max-width: 110px;
  border: 2px solid #d4d4d4;
  border-radius: 4px;
  padding: 15px;
  font-size: 13.333px;
}
.dsm-input-2 {
  max-width: 260px;
  width: 100%;
}
.dsm-info-icon {
  font-size: 16px;
  color: #999999;
  margin-right: 5px;
}
.dsm-info-icon.txtRed {
  color: #bd2025;
}
.dsm-applied-to-the-next {
  color: #555555;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 0;
  line-height: 14px;
}
.dsm-select-group {
  position: relative;
  display: inline-block;
}
.dsm-select-group select {
  width: 250px;
  height: 44px;
  border: 2px solid #d4d4d4;
  border-radius: 4px;
  padding-left: 15px;
  color: #555555;
  font-family: Arial;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 18px;

  box-shadow: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  cursor: pointer;
  padding-right: 38px;
}

.dsm-select-group.dsm-select-group-2 select {
  width: 315px;
}
.dsm-select-group span {
  position: absolute;
  right: 15px;
  top: 15px;
  pointer-events: none;
}
.dsm-remove-schedule {
  color: #00549a;
  font-family: Arial;
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 18px;
}
.dsm-custom-dot > a > span {
  background-color: white;
  border: 1px solid #d4d4d4;
}

.dsm-custom-dot > a > div > span {
  color: black;
}

.dsm-custom-dot .dsm-selected > span {
  background-color: #003778;
}

.dsm-custom-dot .dsm-selected > div > span {
  color: white;
}

.dsm-this-overlaps-div {
  padding: 15px;
  background: #f4f4f4;
  width: 250px;
  margin-bottom: 30px;
}
.pad-54-right {
  padding-right: 54px;
}
.dsm-flex-end {
  justify-content: flex-end;
}
.dsm-no-schedule-span {
  color: #999999;
}
.dsm-dot .dsm-custom-hr:last-child .dsm-custom-hr {
  border: 1px solid #e1e1e1;
}
.dsm-dot.hide {
  display: none;
}
.dsm-daily-schedule-div > div:last-child .dsm-custom-hr {
  margin-bottom: 0px;
}

.dsm-no-schedule-border {
  border-bottom: 1px solid #e1e1e1;
}

@media (max-width: 767px) {
  .dsm-dot > a {
    margin-right: 10px;
  }
  .pad-54-right {
    padding-right: 0px;
  }
  .dm-schedule-details {
    height: 442px;
    transition: height 0.5s;
  }
  .dm-start-time-div,
  .dsm-weekly-schedule-div-1 {
    flex-wrap: wrap;
  }
  .dm-start-time-div > div,
  .dsm-weekly-schedule-div-1 > div {
    width: 100%;
    margin-right: 0px;
  }
  .dsm-weekly-schedule-div-1 > div:not(:last-child) {
    margin-bottom: 15px;
  }
  .dm-start-time-div > div:not(:last-child) {
    margin-bottom: 20px;
  }
  .dsm-input-1,
  .dsm-input-2,
  .dsm-select-group select,
  .dsm-select-group.dsm-select-group-2 select,
  .dsm-select-group {
    width: 100%;
    max-width: 100%;
  }
  .display-none-max-767 {
    display: none;
  }
  .dsm-spacer-1,
  .dsm-spacer-2,
  .dm-spacer-3 {
    display: none;
  }
  .dsm-modal-footer {
    background: white;
    padding: 15px;
    padding-bottom: 30px;
    padding-top: 0px;
  }
  .dsm-custom-margin {
    margin-right: 15px !important;
  }
  .dsm-noMargin-xs {
    margin: 0px;
  }
  .dsm-custom-border {
    border: none;
    padding: 0px;
    border-bottom: 1px solid #d4d4d4;
    padding-bottom: 30px;
    border-radius: 0px;
  }
  .dsm-custom-border-2 {
    border: none;
    padding: 0px;
    border-top: 1px solid #d4d4d4;
    border-radius: 0px;
    padding-top: 30px;
  }
  .dsm-flex-wrap-xs {
    flex-wrap: wrap;
  }
  .dsm-flex-wrap-xs > * {
    width: 100%;
  }
  .dsm-margin-10-bottom-xs {
    margin-bottom: 10px;
  }
}
/*End of Data Manager Settings*/


/*Data Manager*/

.dm-spacer-3 {
  width: 40px;
  margin-right: 15px;
}
.dm-edit-button,
.dm-remove-button {
  background: transparent;
  border: none;
  padding: 0px;
  cursor: pointer;
}
.dm-remove-button.margin-15-right {
  margin-right: 15px;
}

.dm-schedule-details {
  height: 356px;
  transition: height 0.5s;
}

.dm-schedule-details.hide {
  height: 0px;
  overflow: hidden;
  margin: 0px;
  margin-bottom: -15px;
}
.dm-schedule-details:last-child {
  margin-top: 30px;
}

.dm-schedule-details.hide:last-child {
  margin: 0px;
}
.dm-start-time-div .form-group {
    margin-bottom: 0px;
}

.date-picker-box {
    width: 150px;
}
.date-picker-box input {
    border-radius: 4px;
}

.ui-datepicker-trigger-dm {
    cursor: pointer;
    text-decoration: none;
    background: none;
    border: none;
    position: absolute;
    left: 110px;
    bottom: 10px;
}

    .ui-datepicker-trigger-dm::before {
        font-family: "icon-datamanager";
        content: "\ea0d";
        font-size: 24px;
        background-color: transparent;
        color: #00549a;
        height: 24px;
    }

/*temporary fix*/
.date-picker {
    opacity: 0.5 !important;
    background-color: #ffffff !important;
}

.manage-your-data-box-pad {
    padding: 46.5px 21px 46.5px 21px;
}

.dm-group-heading {
    border-radius: 10px 10px 0 0;
}

.data-status-icon {
    height: 12px;
    width: 12.71px;
    border-radius: 50%;
}

.data-status-icon.active {
    background-color: #339043;
}

.data-status-icon.blocked {
    background-color: #BD2025;
}

.data-status-icon.pending {
    background-color: #F9AC28;
}

.margin-t-7 {
    margin-top: 7px;
}

.margin-l-61 {
    margin-left: 61px;
}

.margin-r-9 {
    margin-right: 9px;
}

.margin-t-8 {
    margin-top: 8px;
}

.hr-darker-grey {
    border-color: #979797;
}

.light-grey-icon {
    color: #999999;
}

.overflow-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dm-banner hr {
    width: 58px;
}

.dm_ctrl_element {
    top: -8px !important
}

.unblock-data-img-container {
    width: 40px;
    height: 40px;
    padding: 5px 12px 5px 12px;
}

    .unblock-data-img-container img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

.dm-modal-footer {
    justify-content: flex-start;
}

.dm-select-group {
    position: relative;
    display: inline-block;
}

    .dm-select-group span {
        position: absolute;
        right: 15px;
        top: 15px;
        pointer-events: none;
    }

.dm-select-group select {
    /* for Firefox */
    -moz-appearance: none;
    /* for Chrome */
    -webkit-appearance: none;
}

    /* For IE10 */
    .dm-select-group select::-ms-expand {
        display: none;
    }

.dm-select-group select {
    border-radius: 4px;
}

.dm-radiogroup input:checked ~ span {
    position: absolute;
    left: 0;
    height: 24px;
    width: 24px;
    background: #fff;
    box-shadow: inset 0 0px 3px 0 rgba(0, 0, 0, 0.2);
    border: 1px solid #ccc;
    border-radius: 50%;
    cursor: pointer;
    pointer-events: none;
    background: #003778;
    border: 1px solid #003778;
}

    .dm-radiogroup input:checked ~ span:after {
        display: block;
        left: 5px;
        top: 5px;
        height: 12px;
        width: 12px;
        border-radius: 50%;
        background: #fff;
        content: "";
        position: absolute;
    }

.dm-radiogroup input[type="radio"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.dm-radiogroup-input {
    height: 24px;
    width: 24px;
    position: relative;
    pointer-events: none;
}

.dm-radiogroup span {
    position: absolute;
    left: 0;
    height: 24px;
    width: 24px;
    background: #fff;
    box-shadow: inset 0 0px 3px 0 rgba(0, 0, 0, 0.2);
    border: 1px solid #ccc;
    border-radius: 50%;
    cursor: pointer;
    pointer-events: none;
}

.dm-radiogroup label {
    cursor: pointer;
    padding-left: 40px;
    margin-left: -40px;
    line-height: 24px;
}

.dm-input-date-start {
    height: 44px;
    max-width: 150px;
    border: 2px solid #d4d4d4;
    border-radius: 4px;
    padding: 15px;
    font-size: 13.333px;
}

.dm-header-75 {
    height: 75px;
    display: flex;
    align-items: center;
}

input[type=date]::-webkit-clear-button, /* blue cross */
input[type=date]::-webkit-inner-spin-button, /* up */
input[type=date]::-webkit-outer-spin-button, /* down */
input[type=date]::-webkit-calendar-picker-indicator /* datepicker*/ {
    display: none;
}


/*Table*/

/*Allows horizontal scrolling*/
.scrollableContainer {
    overflow-x: auto;
}
.dm-table thead {
    width: 1169px;
}
.dm-table tbody {
    width: 1169px;
}
/*end*/

.dm-table thead th {
    vertical-align: middle;
    border-bottom: none;
    font-weight: normal;
}
.dm-full-width {
    width: 100%;
}
.dm-table thead th {
    width: 20.8%;
    box-shadow: inset -1px 0 0 0 #003778;
}
.dm-table thead th:nth-child(2) {
    width: 37.6%;
}
.dm-table tr td {
    width: 20.8%;
    display: flex;
    align-items: center;
}
    .dm-table tr td:nth-child(2) {
        width: 37.6%;
        padding: 0 15px 0 15px;
    }
.dm-table tbody tr:nth-child(odd) {
    background-color: #F4F4F4;
}
.table-bordered {
    border: none;
}
.table-bordered th {
    border: none;
}
.table-bordered td {
    border: 1px solid #d4d4d4 !important;
}
.dm-table td, .table th {
    padding: 20px 15px 20px 15px;
    vertical-align: middle;
}

.page-number-nav {
    height: 30px;
    width: 30px;
    border-radius: 50%;
}

.page-number-active {
    background-color: #00549a;
    color: #ffffff;
    font-weight: bold;
}

a.disabled {
    pointer-events: none;
    color: #BABEC2;
}

.record-display-amount select {
    -moz-appearance: none;
    -webkit-appearance: none;
    appearance: none;
}
.dm-form-control {
    width: 70px;
    border: 2px solid #D4D4D4;
    border-radius: 4px;
    background-image: url(../../content/assets/selector_down_blue.svg);
    background-size: .6em;
    background-position: calc(100% - 0.8em) center;
    background-repeat: no-repeat;
}


.dm-btn {
    border: 1px solid #D4D4D4;
    border-radius: 15px;
    background-color: #FFFFFF;
    color: #111111;
    padding: 7px 15px;
}

    .dm-btn.active {
        background-color: #003778;
        color: #FFFFFF;
    }
   



/*NAV*/
.pad-b-12 {
    padding-bottom: 12px;
}

.margin-r-18 {
    margin-right: 18px;
}

.txtSize7 {
    font-size: 7px;
}

.sub-nav-container .icon-Chevron_down {
    left: 11px;
}

.sub-nav-container .nav-item.active span:first-child {
    color: white !important;
    border-bottom: 2px solid #fff;
    padding-bottom: 4px;
}

.sub-nav-container-second-level {
    background-color: #00215E;
}

.sub-nav-container-second-level a:hover {
    text-decoration: none !important;
}

    .sub-nav-container-second-level .nav-item:not(:last-child) {
        margin-right: 20px;
    }

    .sub-nav-container-second-level .nav-item.active span {
        color: white !important;
        border-bottom: 1px solid #fff;
        padding-top: 4px;
        padding-bottom: 4px;
    }

.disabled-edit {
    color: #BABEC2;
}

input[type=checkbox][disabled] + .slider {
    opacity: 0.5;
}

.max-width-286 {
    max-width: 286px;
}

.accordPanel.seeMore[aria-expanded="false"]:after {
    content: "See More";
}

.accordPanel.seeMore[aria-expanded="true"]:after {
    content: "See less";
}


.dm-hr-line {
    border-color: #D4D4D4;
}

.txtSize23 {
    font-size: 23px;
}

.spacer3 {
    height: 3px;
    background-color: #ffffff;
}

.hr-line {
    content: "";
    display: block;
    border-bottom: 1px solid #D4D4D4;
    height: 0px;
    width: 820px;
    position: relative;
    left: -20px;
}

.contain-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    overflow: hidden;
}

.txtGray20 {
    color: #babec2;
}

a:hover.hover_no_underline {
    text-decoration: none;
}

/*temp NAV fix*/
.bell-icon-div {
    display: flex;
    align-items: center;
    height: 100%;
}
.bell-icon-div p {
    margin-bottom: 0;
}
.bell-icon-div span {
    font-size: 34px;
}
.mobility-velma {
    border-left: 1px solid #00215E;
}
.pad-l-7 {
    padding-left: 7px;
}
.margin-r-7 {
    margin-right: 7px;
}
.sub-nav-container-second-level {
    overflow: hidden;
}
.sub-nav-container-second-level ul {
    align-items: center;
}



/*Media Queries*/

@media screen and (min-width: 1134px) {
    #banner-lg {
        display: none;
    }

    #banner-xl {
        display: block !important;
    }
}

    @media screen and (min-width: 992px) {
        #banner-xl {
            display: none;
        }

        #banner-md {
            display: none;
        }

        #banner-xs {
            display: none;
        }

        .pad-b-md-37 {
            padding-bottom: 37px;
        }

        .global-navigation .aliant .connector-brand {
            font-size: 38px;
        }
    }

    @media screen and (min-width: 768px) {
        .bottom-7 {
            bottom: 7px;
        }

        .pad-t-sm-lg-37 {
            padding-top: 37px;
        }

        .dm-select-group-sub {
            width: 173px;
        }

        .dm-select-group-date {
            width: 110px;
        }
    }


    @media screen and (min-width: 768px) and (max-width: 991.98px) {
        #banner-xl {
            display: none;
        }

        #banner-lg {
            display: none;
        }

        #banner-xs {
            display: none;
        }

        .manage-your-data-box-pad {
            padding: 30.5px 64px 30.5px 64px;
        }

        .margin-l-sm-7-5 {
            margin-left: 7.5px;
        }

        .margin-r-sm-7-5 {
            margin-right: 7.5px;
        }

        .margin-l-sm-80 {
            margin-left: 80px;
        }

        .pad-r-sm-21-5 {
            padding-right: 21.5px;
        }

        .block-data-label {
            max-width: 200px;
        }

        .dm-select-group-date {
            width: 173px;
        }

        .page-nav-container {
            display: block !important;
        }

        .hidden-sm {
            display: none;
        }
    }

    @media (min-width: 320px) and (max-width: 767.98px) {
        .dm-height-54-xs {
            height: 54px;
        }

        .dm-display-flex {
            display: flex;
            align-items: center;
            height: 100%;
        }

        #banner-xl {
            display: none;
        }

        #banner-lg {
            display: none;
        }

        #banner-md {
            display: none;
        }

        .manage-your-data-box-pad {
            padding: 30px 20px 30px 20px;
        }

        .block-data-label {
            max-width: 150px;
        }

        .margin-l-xs-80 {
            margin-left: 80px;
        }

        .pad-t-xs-7 {
            padding-top: 7px;
        }

        .wider-than-parent-xs {
            width: 100vw;
            position: relative;
            left: calc(-50vw + 50%);
        }

        .border-top-none-xs {
            border-top: none;
        }

        .block-type li {
            margin-bottom: 15px;
        }

        .filter-to-date {
            width: 69px;
        }

        .width-xs-70 {
            width: 70%;
        }

        .date-picker-box {
            width: 100%;
        }

        .ui-datepicker-trigger-dm {
            left: 160px;
        }

        .no-pad-sides-xs {
            padding-left: 0 !important;
            padding-right: 0 !important;
        }

        .surtitle-gray-xs {
            font-size: 14px;
            color: #555555;
        }

        .manage-your-data-box-pad {
            padding: 20px 15px 20px 15px;
        }

        .txtLeft-xs {
            text-align: left;
        }

        .hr_BlueExtraDark3 {
            border-color: #003778;
        }
    }

    @media (max-width: 767.98px) {
        .left.scrollableContainerShadow:before {
            width: 46px;
            -webkit-transition: width .5s;
            transition: width .5s;
        }
    }

    @media (max-width: 767.98px) {
        .scrollableContainerShadow {
            position: relative;
        }
    }


