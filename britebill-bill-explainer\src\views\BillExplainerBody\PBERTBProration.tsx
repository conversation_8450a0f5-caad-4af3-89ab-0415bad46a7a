import * as React from "react";
import { IBillExplainerBody } from "./IBillExplainerBody";
import { FormattedHTMLMessage, InjectedIntlProps, injectIntl } from "react-intl";
import { CURRENCY_OPTIONS, DATE_OPTIONS_WITHOUT_YEAR, FormattedFromToDate, FormattedFromToDateSR, debounce, getLobData, isNullUndefinedOrEmpty, removeHoursFromDate } from "../../utils/Utility";
import { ICSMTransaction, IChargeItem, PBEPosition } from "../../models";
import { pbeSplitChartRemade as generatePBEBarsAndLabels, initializePBESplitChart } from '../../utils/PBEBarsLabels';

interface IBillActivitiesProps {
    chargeItems: IChargeItem[];
    transactions: ICSMTransaction[];
    startDate: string;
    endDate: string;
    titleKey: string;
    showLegends: boolean;
}

interface IDataSplitTransaction {
    label: string;
    date: string;
    date_label: string;
}
interface IDataSplitChargeItem {
    label: string;
    sub_label: string;
    startDate: string;
    endDate: string;
    position: PBEPosition;
    itemLegend: string;
    alwaysLabel: boolean;
    legend: string;
    label_position: PBEPosition;
}

interface IDataSplitChart {
    startDate: string;
    endDate: string;
    transactions: IDataSplitTransaction[];
    chargeItems: IDataSplitChargeItem[];
    UseLegendsForDiagram: boolean;
}

const mapPositionFromResponse = (position: string): PBEPosition => {
    switch (position) {
        case "PREVIOUS_UPPER":
        case "CURRENT_UPPER":
            return "upper";
        case "PREVIOUS_BELOW":
        case "PREVIOUS":
        case "CURRENT":
        case "CURRENT_BELOW":
            return "below";
        default:
            return "";
    }
}

const compareDate = (date1Str: string, date2Str: string) => {
    const date1 = new Date(date1Str);
    const date2 = new Date(date2Str);
    date1.setHours(0, 0, 0, 0);
    date2.setHours(0, 0, 0, 0);
    const time1 = date1.getTime();
    const time2 = date2.getTime();
    if (time1 < time2) {
        return "<";
    } else if (time1 > time2) {
        return ">";
    } else { // same
        return "=";
    }
}

const BillActivities = ({ chargeItems, transactions, startDate, endDate, intl, titleKey, showLegends }: IBillActivitiesProps & InjectedIntlProps) => {
    const { formatMessage, formatDate, formatNumber } = intl;
    // const chargesForPeriod = chargeItems?.filter(c => {
    //     const chargeStartDate = new Date(c?.startDate);
    //     const chargeEndDate = new Date(c?.endDate);
    //     return chargeStartDate >= new Date(startDate) && chargeEndDate <= new Date(endDate);
    // }) ?? [];
    // const transactionsForPeriod = transactions?.filter(t => {
    //     const trxEffDate = new Date(t.trxEffDate);
    //     return trxEffDate >= new Date(startDate) && trxEffDate <= new Date(endDate);
    // }) ?? [];

    const chargesForPeriod = chargeItems?.filter(c => {
        const startDateAheadOrEqual = compareDate(c?.startDate, startDate) == ">" || compareDate(c?.startDate, startDate) == "=";
        const endDateBeforeOrEqual = compareDate(c?.endDate, endDate) == "<" || compareDate(c?.endDate, endDate) == "=";
        return startDateAheadOrEqual && endDateBeforeOrEqual;
    });
    const transactionsForPeriod = transactions?.filter(t => {
        const startDateAheadOrEqual = compareDate(t.trxEffDate, startDate) == ">" || compareDate(t.trxEffDate, startDate) == "=";
        const endDateBeforeOrEqual = compareDate(t.trxEffDate, endDate) == "<" || compareDate(t.trxEffDate, endDate) == "=";
        return startDateAheadOrEqual && endDateBeforeOrEqual;
    }) ?? [];

    const dataSplitChart: IDataSplitChart = {
        startDate: removeHoursFromDate(startDate),
        endDate: removeHoursFromDate(endDate),
        transactions: transactionsForPeriod.map((t) => ({
            label: t.trxDescKey && formatMessage({ id: t.trxDescKey }),
            date: removeHoursFromDate(t.trxEffDate),
            date_label: formatDate(new Date(removeHoursFromDate(t.trxEffDate)), DATE_OPTIONS_WITHOUT_YEAR)
        })),
        chargeItems: chargesForPeriod.map((c) => {
            const hasLegends = !isNullUndefinedOrEmpty(c.legend);
            return ({
                alwaysLabel: c.alwaysLabel,
                endDate: removeHoursFromDate(c.endDate),
                startDate: removeHoursFromDate(c.startDate),
                itemLegend: formatMessage({ id: c.itemLegend }),
                label: isNullUndefinedOrEmpty(c.itemDescKey) ? "" : formatMessage({ id: hasLegends ? c.itemDetailedDescKey : c.itemDescKey }, { amount: formatNumber(Math.abs(c?.amount), CURRENCY_OPTIONS) }),
                sub_label: (isNullUndefinedOrEmpty(c.itemDescSubKey) || hasLegends) ? "" : formatMessage({ id: c.itemDescSubKey }, { days: c.prorationDays, amount: formatNumber(c.amount, CURRENCY_OPTIONS) }),
                legend: isNullUndefinedOrEmpty(c.legend) ? "" : c.legend,
                position: mapPositionFromResponse(c.position),
                label_position: isNullUndefinedOrEmpty(c.itemDescKey) ? "" : c.labelPosition
            })
        }),
        UseLegendsForDiagram: showLegends
    };

    return (
        <div className="flex1">
            <div className="txtCenter margin-b-15">
                <h4 className="subtitle-2 d-flex justify-center align-items-end same-height" data-same-height-index="1">
                    {formatMessage({ id: titleKey })}
                </h4>
                <div className="txtSize12">
                    <span aria-hidden="true"><FormattedFromToDate startDate={removeHoursFromDate(startDate)} endDate={removeHoursFromDate(endDate)} /></span>
                    <span className="sr-only"><FormattedFromToDateSR startDate={removeHoursFromDate(startDate)} endDate={removeHoursFromDate(endDate)} /></span>
                </div>
            </div>
            <div className="pbe-js-chart bgGray19" data-split-chart={JSON.stringify(dataSplitChart)}>
                <div className="pbe-chart-container" aria-hidden="true"></div>
                <div className="pbe-labels-container" aria-hidden="true"></div>
            </div>
        </div>
    );
}

const PBERTBProration = (props: IBillExplainerBody & InjectedIntlProps) => {
    const { intl: { formatMessage, formatNumber } } = props;

    // const description = formatMessage({ id: props?.descriptionKey }, { service: formatMessage({ id: getLobData(props?.subscriberDetails?.subscriberType).lobName }) });
    const description = !!props?.descriptionKey && formatMessage({ id: props?.descriptionKey }, { service: formatMessage({ id: getLobData(props?.subscriberDetails?.subscriberType).lobName }) });
    const chargeActivitiesWithDescription: IChargeItem[] = (props?.chargeItems ?? []).filter((c) => !isNullUndefinedOrEmpty(c?.itemDetailedDescKey));
    const showPreviousBill = !isNullUndefinedOrEmpty(props?.previousPeriodEndDate) && !isNullUndefinedOrEmpty(props?.previousPeriodStartDate);
    const showCurrentBill = !isNullUndefinedOrEmpty(props?.currentPeriodStartDate) && !isNullUndefinedOrEmpty(props?.currentPeriodEndDate);
    const detailedDescription = formatMessage({ id: props?.detailedDescKey });
    const showDetailedDescription = detailedDescription !== props?.detailedDescKey;
    const chargeActivitiesWithLegends: IChargeItem[] = (props?.chargeItems ?? []).filter((c) => !isNullUndefinedOrEmpty(c?.legend));
    const showLegends = props?.useLegendsForDiagram && chargeActivitiesWithDescription?.length > 0;
    const showChargesList = !showLegends && chargeActivitiesWithDescription?.length > 0;

    React.useEffect(() => {
        const onWindowResize = debounce(generatePBEBarsAndLabels, 200);
        window.addEventListener("resize", onWindowResize);
        initializePBESplitChart();
        return () => {
            window.removeEventListener("resize", onWindowResize);
        }
    }, [props?.intl?.locale])

    return (
        <div className="container pad-b-45">
            <div className="pbe-chart pbe-new-activation-chart pbe-chart-scrollable col-sm-10 margin-auto pad-30 pad-xs-0 borderRadiusAll10 border-gray2 no-borders-xs pad-h-xs-0 max-width-708">
                <div className="max-width-735 w-100">
                    <h3 className="subtitle-2 margin-b-15">
                        {description}
                    </h3>
                    {showDetailedDescription ? <p className="margin-b-10">{detailedDescription}</p> : null}
                    {showChargesList ?
                        <ul className="pbe-charges-list margin-b-45 margin-b-xs-30 margin-l-15 pad-l-0">
                            {chargeActivitiesWithDescription.map((c, index) =>
                                <li key={c?.itemDetailedDescKey + index}>
                                    <FormattedHTMLMessage id={c?.itemDetailedDescKey} values={{ amount: formatNumber(Math.abs(c?.amount), CURRENCY_OPTIONS) }} />
                                </li>
                            )}
                        </ul> : null
                    }
                </div>
                <div className="pbe-inner-scrollable" role="region">
                    <div className="pbe-wrapper d-flex same-height-wrap">
                        {showPreviousBill ? <BillActivities startDate={props?.previousPeriodStartDate} endDate={props?.previousPeriodEndDate} chargeItems={props?.chargeItems} transactions={props?.transactions} intl={props?.intl} titleKey="PREVIOUS_BILL" showLegends={showLegends} /> : null}
                        {showCurrentBill ? <BillActivities startDate={props?.currentPeriodStartDate} endDate={props?.currentPeriodEndDate} chargeItems={props?.chargeItems} transactions={props?.transactions} intl={props?.intl} titleKey="CURRENT_BILL" showLegends={showLegends} /> : null}
                    </div>
                </div>
                {showLegends ? <div className="pad-t-30" role="list">
                    {chargeActivitiesWithLegends.map((c, index) => {
                        const backgroundColorClass = formatMessage({ id: c?.itemLegend });
                        return <div key={c?.itemDetailedDescKey + index} className={`d-flex line-height-18 ${index !== 0 ? "margin-t-7" : ""}`} role="listitem">
                            <div aria-hidden="true">
                                <span className={`d-inline-block legend txtSize12 line-height-18 ${backgroundColorClass}`}>{c?.legend}</span>
                            </div>
                            <div aria-hidden="true" className="pad-l-5 pad-t-1 pad-b-2">=</div>
                            <div className="pad-l-5 pad-t-1 pad-b-2">
                                <span className="sr-only">{c?.legend}&nbsp;=&nbsp;</span>
                                <FormattedHTMLMessage id={c?.itemDetailedDescKey} values={{ amount: formatNumber(Math.abs(c?.amount), CURRENCY_OPTIONS) }} />
                            </div>
                        </div>
                    }
                    )}
                </div> : null}
            </div>
        </div>
    );
};

export default injectIntl(PBERTBProration);