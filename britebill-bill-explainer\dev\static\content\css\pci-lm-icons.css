@font-face{font-family:'pci-lucky';src:url(../fonts/pci-lm-icons.eot?#iefix) format("embedded-opentype"),url(../fonts/pci-lm-icons.woff) format("woff"),url(../fonts/pci-lm-icons.ttf) format("truetype"),url(../fonts/pci-lm-icons.svg) format("svg");font-weight:400;font-style:normal}

.icon-pci{font-style:normal;speak:none;font-weight:400;font-variant:normal;text-transform:none;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}
.icon-pci, .icon-pci:before {
    font-family: 'pci-lucky';
    position: static;
}

.icon-lm-logo:before {
    content: "\e920";
}
/*Info icon paths*/
.icon-info .path1:before {
    content: "\e928";
    color: #40b5e5;
}
.icon-info .path2:before {
    content: "\e929";
    color: #002e73;
    margin-left: -1em;
}
.icon-info .path3:before {
    content: "\e92a";
    color: #002e73;
    margin-left: -1em;
}
/*Warning icon paths*/
.icon-warning-yellow .path1:before {
    content: "\e92f";
    color: #ffd669;
}
.icon-warning-yellow .path2:before {
    content: "\e930";
    color: #002e73;
    margin-left: -1em;
}
.icon-warning-yellow .path3:before {
    content: "\e931";
    color: #002e73;
    margin-left: -1em;
}
/*Error icon paths*/
.icon-error-red .path1:before {
    content: "\e92f";
    color: #d42121;
}
.icon-error-red .path2:before {
    content: "\e930";
    color: #fff;
    margin-left: -1em;
}
.icon-error-red .path3:before {
    content: "\e931";
    color: #fff;
    margin-left: -1em;
}

.icon-success:before {
    content: "\e92e";
}
.icon-down_arrow:before {
    content: "\e90b";
}
.icon-Bottom_arrow-small:before {
    content: "\e933";
}
.icon-close-big:before {
    content: "\e900";
}