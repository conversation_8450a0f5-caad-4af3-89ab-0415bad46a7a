!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],e):e(t.<PERSON>actRouter={},t.React)}(this,function(t,e){"use strict";e=e&&e.hasOwnProperty("default")?e.default:e;"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;function n(t,e){return t(e={exports:{}},e.exports),e.exports}function r(t){return function(){return t}}var o=function(){};o.thatReturns=r,o.thatReturnsFalse=r(!1),o.thatReturnsTrue=r(!0),o.thatReturnsNull=r(null),o.thatReturnsThis=function(){return this},o.thatReturnsArgument=function(t){return t};var i=o,a=function(t){};var c=function(t,e,n,r,o,i,c,u){if(a(e),!t){var s;if(void 0===e)s=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var p=[n,r,o,i,c,u],l=0;(s=new Error(e.replace(/%s/g,function(){return p[l++]}))).name="Invariant Violation"}throw s.framesToPop=1,s}},u=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable;(function(){try{if(!Object.assign)return!1;var t=new String("abc");if(t[5]="de","5"===Object.getOwnPropertyNames(t)[0])return!1;for(var e={},n=0;n<10;n++)e["_"+String.fromCharCode(n)]=n;if("0123456789"!==Object.getOwnPropertyNames(e).map(function(t){return e[t]}).join(""))return!1;var r={};return"abcdefghijklmnopqrst".split("").forEach(function(t){r[t]=t}),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},r)).join("")}catch(t){return!1}})()&&Object.assign;var l="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",h=n(function(t){t.exports=function(){function t(t,e,n,r,o,i){i!==l&&c(!1,"Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types")}function e(){return t}t.isRequired=t;var n={array:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e};return n.checkPropTypes=i,n.PropTypes=n,n}()}),f=function(){},d=function(t,e,n,r,o,i,a,c){if(!t){var u;if(void 0===e)u=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var s=[n,r,o,i,a,c],p=0;(u=new Error(e.replace(/%s/g,function(){return s[p++]}))).name="Invariant Violation"}throw u.framesToPop=1,u}};function y(t){return"/"===t.charAt(0)}function m(t,e){for(var n=e,r=n+1,o=t.length;r<o;n+=1,r+=1)t[n]=t[r];t.pop()}var v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};var g=function(t){var e=t.pathname,n=t.search,r=t.hash,o=e||"/";return n&&"?"!==n&&(o+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(o+="#"===r.charAt(0)?r:"#"+r),o},b=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},x=function(t,e,n,r){var o=void 0;"string"==typeof t?(o=function(t){var e=t||"/",n="",r="",o=e.indexOf("#");-1!==o&&(r=e.substr(o),e=e.substr(0,o));var i=e.indexOf("?");return-1!==i&&(n=e.substr(i),e=e.substr(0,i)),{pathname:e,search:"?"===n?"":n,hash:"#"===r?"":r}}(t)).state=e:(void 0===(o=b({},t)).pathname&&(o.pathname=""),o.search?"?"!==o.search.charAt(0)&&(o.search="?"+o.search):o.search="",o.hash?"#"!==o.hash.charAt(0)&&(o.hash="#"+o.hash):o.hash="",void 0!==e&&void 0===o.state&&(o.state=e));try{o.pathname=decodeURI(o.pathname)}catch(t){throw t instanceof URIError?new URIError('Pathname "'+o.pathname+'" could not be decoded. This is likely caused by an invalid percent-encoding.'):t}return n&&(o.key=n),r?o.pathname?"/"!==o.pathname.charAt(0)&&(o.pathname=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=t&&t.split("/")||[],r=e&&e.split("/")||[],o=t&&y(t),i=e&&y(e),a=o||i;if(t&&y(t)?r=n:n.length&&(r.pop(),r=r.concat(n)),!r.length)return"/";var c=void 0;if(r.length){var u=r[r.length-1];c="."===u||".."===u||""===u}else c=!1;for(var s=0,p=r.length;p>=0;p--){var l=r[p];"."===l?m(r,p):".."===l?(m(r,p),s++):s&&(m(r,p),s--)}if(!a)for(;s--;s)r.unshift("..");!a||""===r[0]||r[0]&&y(r[0])||r.unshift("");var h=r.join("/");return c&&"/"!==h.substr(-1)&&(h+="/"),h}(o.pathname,r.pathname)):o.pathname=r.pathname:o.pathname||(o.pathname="/"),o},w=function(t,e){return t.pathname===e.pathname&&t.search===e.search&&t.hash===e.hash&&t.key===e.key&&function t(e,n){if(e===n)return!0;if(null==e||null==n)return!1;if(Array.isArray(e))return Array.isArray(n)&&e.length===n.length&&e.every(function(e,r){return t(e,n[r])});var r=void 0===e?"undefined":v(e);if(r!==(void 0===n?"undefined":v(n)))return!1;if("object"===r){var o=e.valueOf(),i=n.valueOf();if(o!==e||i!==n)return t(o,i);var a=Object.keys(e),c=Object.keys(n);return a.length===c.length&&a.every(function(r){return t(e[r],n[r])})}return!1}(t.state,e.state)},O=("undefined"==typeof window||!window.document||window.document.createElement,"function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t}),R=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},P=function(t,e,n){return Math.min(Math.max(t,e),n)},j=function(){var t,e,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=n.getUserConfirmation,o=n.initialEntries,i=void 0===o?["/"]:o,a=n.initialIndex,c=void 0===a?0:a,u=n.keyLength,s=void 0===u?6:u,p=(t=null,e=[],{setPrompt:function(e){return f(null==t,"A history supports only one prompt at a time"),t=e,function(){t===e&&(t=null)}},confirmTransitionTo:function(e,n,r,o){if(null!=t){var i="function"==typeof t?t(e,n):t;"string"==typeof i?"function"==typeof r?r(i,o):(f(!1,"A history needs a getUserConfirmation function in order to use a prompt message"),o(!0)):o(!1!==i)}else o(!0)},appendListener:function(t){var n=!0,r=function(){n&&t.apply(void 0,arguments)};return e.push(r),function(){n=!1,e=e.filter(function(t){return t!==r})}},notifyListeners:function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];e.forEach(function(t){return t.apply(void 0,n)})}}),l=function(t){R(b,t),b.length=b.entries.length,p.notifyListeners(b.location,b.action)},h=function(){return Math.random().toString(36).substr(2,s)},d=P(c,0,i.length-1),y=i.map(function(t){return x(t,void 0,"string"==typeof t?h():t.key||h())}),m=g,v=function(t){var e=P(b.index+t,0,b.entries.length-1),n=b.entries[e];p.confirmTransitionTo(n,"POP",r,function(t){t?l({action:"POP",location:n,index:e}):l()})},b={length:y.length,action:"POP",location:y[d],index:d,entries:y,createHref:m,push:function(t,e){f(!("object"===(void 0===t?"undefined":O(t))&&void 0!==t.state&&void 0!==e),"You should avoid providing a 2nd state argument to push when the 1st argument is a location-like object that already has state; it is ignored");var n=x(t,e,h(),b.location);p.confirmTransitionTo(n,"PUSH",r,function(t){if(t){var e=b.index+1,r=b.entries.slice(0);r.length>e?r.splice(e,r.length-e,n):r.push(n),l({action:"PUSH",location:n,index:e,entries:r})}})},replace:function(t,e){f(!("object"===(void 0===t?"undefined":O(t))&&void 0!==t.state&&void 0!==e),"You should avoid providing a 2nd state argument to replace when the 1st argument is a location-like object that already has state; it is ignored");var n=x(t,e,h(),b.location);p.confirmTransitionTo(n,"REPLACE",r,function(t){t&&(b.entries[b.index]=n,l({action:"REPLACE",location:n}))})},go:v,goBack:function(){return v(-1)},goForward:function(){return v(1)},canGo:function(t){var e=b.index+t;return e>=0&&e<b.entries.length},block:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return p.setPrompt(t)},listen:function(t){return p.appendListener(t)}};return b},E=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},C=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},T=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)},k=function(t,e){var n={};for(var r in t)e.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n},S=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e},A=function(t){function n(){var e,r;E(this,n);for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=r=S(this,t.call.apply(t,[this].concat(i))),r.state={match:r.computeMatch(r.props.history.location.pathname)},S(r,e)}return T(n,t),n.prototype.getChildContext=function(){return{router:C({},this.context.router,{history:this.props.history,route:{location:this.props.history.location,match:this.state.match}})}},n.prototype.computeMatch=function(t){return{path:"/",url:"/",params:{},isExact:"/"===t}},n.prototype.componentWillMount=function(){var t=this,n=this.props,r=n.children,o=n.history;null!=r&&1!==e.Children.count(r)&&d(!1),this.unlisten=o.listen(function(){t.setState({match:t.computeMatch(o.location.pathname)})})},n.prototype.componentWillReceiveProps=function(t){},n.prototype.componentWillUnmount=function(){this.unlisten()},n.prototype.render=function(){var t=this.props.children;return t?e.Children.only(t):null},n}(e.Component);A.contextTypes={router:h.object},A.childContextTypes={router:h.object.isRequired};var M=function(t){function n(){var e,r;E(this,n);for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=r=S(this,t.call.apply(t,[this].concat(i))),r.history=j(r.props),S(r,e)}return T(n,t),n.prototype.componentWillMount=function(){},n.prototype.render=function(){return e.createElement(A,{history:this.history,children:this.props.children})},n}(e.Component),q=function(t){function e(){return E(this,e),S(this,t.apply(this,arguments))}return T(e,t),e.prototype.enable=function(t){this.unblock&&this.unblock(),this.unblock=this.context.router.history.block(t)},e.prototype.disable=function(){this.unblock&&(this.unblock(),this.unblock=null)},e.prototype.componentWillMount=function(){this.context.router||d(!1),this.props.when&&this.enable(this.props.message)},e.prototype.componentWillReceiveProps=function(t){t.when?this.props.when&&this.props.message===t.message||this.enable(t.message):this.disable()},e.prototype.componentWillUnmount=function(){this.disable()},e.prototype.render=function(){return null},e}(e.Component);q.defaultProps={when:!0},q.contextTypes={router:h.shape({history:h.shape({block:h.func.isRequired}).isRequired}).isRequired};var U=Array.isArray||function(t){return"[object Array]"==Object.prototype.toString.call(t)},_=z,W=$,I=function(t,e){return F($(t,e))},L=F,N=G,H=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");function $(t,e){for(var n,r=[],o=0,i=0,a="",c=e&&e.delimiter||"/";null!=(n=H.exec(t));){var u=n[0],s=n[1],p=n.index;if(a+=t.slice(i,p),i=p+u.length,s)a+=s[1];else{var l=t[i],h=n[2],f=n[3],d=n[4],y=n[5],m=n[6],v=n[7];a&&(r.push(a),a="");var g=null!=h&&null!=l&&l!==h,b="+"===m||"*"===m,x="?"===m||"*"===m,w=n[2]||c,O=d||y;r.push({name:f||o++,prefix:h||"",delimiter:w,optional:x,repeat:b,partial:g,asterisk:!!v,pattern:O?V(O):v?".*":"[^"+B(w)+"]+?"})}}return i<t.length&&(a+=t.substr(i)),a&&r.push(a),r}function D(t){return encodeURI(t).replace(/[\/?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}function F(t){for(var e=new Array(t.length),n=0;n<t.length;n++)"object"==typeof t[n]&&(e[n]=new RegExp("^(?:"+t[n].pattern+")$"));return function(n,r){for(var o="",i=n||{},a=(r||{}).pretty?D:encodeURIComponent,c=0;c<t.length;c++){var u=t[c];if("string"!=typeof u){var s,p=i[u.name];if(null==p){if(u.optional){u.partial&&(o+=u.prefix);continue}throw new TypeError('Expected "'+u.name+'" to be defined')}if(U(p)){if(!u.repeat)throw new TypeError('Expected "'+u.name+'" to not repeat, but received `'+JSON.stringify(p)+"`");if(0===p.length){if(u.optional)continue;throw new TypeError('Expected "'+u.name+'" to not be empty')}for(var l=0;l<p.length;l++){if(s=a(p[l]),!e[c].test(s))throw new TypeError('Expected all "'+u.name+'" to match "'+u.pattern+'", but received `'+JSON.stringify(s)+"`");o+=(0===l?u.prefix:u.delimiter)+s}}else{if(s=u.asterisk?encodeURI(p).replace(/[?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()}):a(p),!e[c].test(s))throw new TypeError('Expected "'+u.name+'" to match "'+u.pattern+'", but received "'+s+'"');o+=u.prefix+s}}else o+=u}return o}}function B(t){return t.replace(/([.+*?=^!:${}()[\]|\/\\])/g,"\\$1")}function V(t){return t.replace(/([=!:$\/()])/g,"\\$1")}function Y(t,e){return t.keys=e,t}function J(t){return t.sensitive?"":"i"}function G(t,e,n){U(e)||(n=e||n,e=[]);for(var r=(n=n||{}).strict,o=!1!==n.end,i="",a=0;a<t.length;a++){var c=t[a];if("string"==typeof c)i+=B(c);else{var u=B(c.prefix),s="(?:"+c.pattern+")";e.push(c),c.repeat&&(s+="(?:"+u+s+")*"),i+=s=c.optional?c.partial?u+"("+s+")?":"(?:"+u+"("+s+"))?":u+"("+s+")"}}var p=B(n.delimiter||"/"),l=i.slice(-p.length)===p;return r||(i=(l?i.slice(0,-p.length):i)+"(?:"+p+"(?=$))?"),i+=o?"$":r&&l?"":"(?="+p+"|$)",Y(new RegExp("^"+i,J(n)),e)}function z(t,e,n){return U(e)||(n=e||n,e=[]),n=n||{},t instanceof RegExp?function(t,e){var n=t.source.match(/\((?!\?)/g);if(n)for(var r=0;r<n.length;r++)e.push({name:r,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,asterisk:!1,pattern:null});return Y(t,e)}(t,e):U(t)?function(t,e,n){for(var r=[],o=0;o<t.length;o++)r.push(z(t[o],e,n).source);return Y(new RegExp("(?:"+r.join("|")+")",J(n)),e)}(t,e,n):function(t,e,n){return G($(t,n),e,n)}(t,e,n)}_.parse=W,_.compile=I,_.tokensToFunction=L,_.tokensToRegExp=N;var K={},Q=0,X=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"/",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return"/"===t?t:function(t){var e=t,n=K[e]||(K[e]={});if(n[t])return n[t];var r=_.compile(t);return Q<1e4&&(n[t]=r,Q++),r}(t)(e,{pretty:!0})},Z=function(t){function e(){return E(this,e),S(this,t.apply(this,arguments))}return T(e,t),e.prototype.isStatic=function(){return this.context.router&&this.context.router.staticContext},e.prototype.componentWillMount=function(){this.context.router||d(!1),this.isStatic()&&this.perform()},e.prototype.componentDidMount=function(){this.isStatic()||this.perform()},e.prototype.componentDidUpdate=function(t){var e=x(t.to),n=x(this.props.to);w(e,n)||this.perform()},e.prototype.computeTo=function(t){var e=t.computedMatch,n=t.to;return e?"string"==typeof n?X(n,e.params):C({},n,{pathname:X(n.pathname,e.params)}):n},e.prototype.perform=function(){var t=this.context.router.history,e=this.props.push,n=this.computeTo(this.props);e?t.push(n):t.replace(n)},e.prototype.render=function(){return null},e}(e.Component);Z.defaultProps={push:!1},Z.contextTypes={router:h.shape({history:h.shape({push:h.func.isRequired,replace:h.func.isRequired}).isRequired,staticContext:h.object}).isRequired};var tt={},et=0,nt=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments[2];"string"==typeof e&&(e={path:e});var r=e,o=r.path,i=r.exact,a=void 0!==i&&i,c=r.strict,u=void 0!==c&&c,s=r.sensitive,p=void 0!==s&&s;if(null==o)return n;var l=function(t,e){var n=""+e.end+e.strict+e.sensitive,r=tt[n]||(tt[n]={});if(r[t])return r[t];var o=[],i={re:_(t,o,e),keys:o};return et<1e4&&(r[t]=i,et++),i}(o,{end:a,strict:u,sensitive:p}),h=l.re,f=l.keys,d=h.exec(t);if(!d)return null;var y=d[0],m=d.slice(1),v=t===y;return a&&!v?null:{path:o,url:"/"===o&&""===y?"/":y,isExact:v,params:f.reduce(function(t,e,n){return t[e.name]=m[n],t},{})}},rt=function(t){function n(){var e,r;E(this,n);for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=r=S(this,t.call.apply(t,[this].concat(i))),r.state={match:r.computeMatch(r.props,r.context.router)},S(r,e)}return T(n,t),n.prototype.getChildContext=function(){return{router:C({},this.context.router,{route:{location:this.props.location||this.context.router.route.location,match:this.state.match}})}},n.prototype.computeMatch=function(t,e){var n=t.computedMatch,r=t.location,o=t.path,i=t.strict,a=t.exact,c=t.sensitive;if(n)return n;e||d(!1);var u=e.route,s=(r||u.location).pathname;return nt(s,{path:o,strict:i,exact:a,sensitive:c},u.match)},n.prototype.componentWillMount=function(){},n.prototype.componentWillReceiveProps=function(t,e){this.setState({match:this.computeMatch(t,e.router)})},n.prototype.render=function(){var t=this.state.match,n=this.props,r=n.children,o=n.component,i=n.render,a=this.context.router,c=a.history,u=a.route,s=a.staticContext,p={match:t,location:this.props.location||u.location,history:c,staticContext:s};return o?t?e.createElement(o,p):null:i?t?i(p):null:"function"==typeof r?r(p):r&&!function(t){return 0===e.Children.count(t)}(r)?e.Children.only(r):null},n}(e.Component);rt.contextTypes={router:h.shape({history:h.object.isRequired,route:h.object.isRequired,staticContext:h.object})},rt.childContextTypes={router:h.object.isRequired};var ot=function(t){return"/"===t.charAt(0)?t:"/"+t},it=function(t,e){return t?C({},e,{pathname:ot(t)+e.pathname}):e},at=function(t){return"string"==typeof t?t:g(t)},ct=function(t){return function(){d(!1)}},ut=function(){},st=function(t){function n(){var e,r;E(this,n);for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=r=S(this,t.call.apply(t,[this].concat(i))),r.createHref=function(t){return ot(r.props.basename+at(t))},r.handlePush=function(t){var e=r.props,n=e.basename,o=e.context;o.action="PUSH",o.location=it(n,x(t)),o.url=at(o.location)},r.handleReplace=function(t){var e=r.props,n=e.basename,o=e.context;o.action="REPLACE",o.location=it(n,x(t)),o.url=at(o.location)},r.handleListen=function(){return ut},r.handleBlock=function(){return ut},S(r,e)}return T(n,t),n.prototype.getChildContext=function(){return{router:{staticContext:this.props.context}}},n.prototype.componentWillMount=function(){},n.prototype.render=function(){var t=this.props,n=t.basename,r=(t.context,t.location),o=k(t,["basename","context","location"]),i={createHref:this.createHref,action:"POP",location:function(t,e){if(!t)return e;var n=ot(t);return 0!==e.pathname.indexOf(n)?e:C({},e,{pathname:e.pathname.substr(n.length)})}(n,x(r)),push:this.handlePush,replace:this.handleReplace,go:ct(),goBack:ct(),goForward:ct(),listen:this.handleListen,block:this.handleBlock};return e.createElement(A,C({},o,{history:i}))},n}(e.Component);st.defaultProps={basename:"",location:"/"},st.childContextTypes={router:h.object.isRequired};var pt=function(t){function n(){return E(this,n),S(this,t.apply(this,arguments))}return T(n,t),n.prototype.componentWillMount=function(){this.context.router||d(!1)},n.prototype.componentWillReceiveProps=function(t){},n.prototype.render=function(){var t=this.context.router.route,n=this.props.children,r=this.props.location||t.location,o=void 0,i=void 0;return e.Children.forEach(n,function(n){if(null==o&&e.isValidElement(n)){var a=n.props,c=a.path,u=a.exact,s=a.strict,p=a.sensitive,l=a.from,h=c||l;i=n,o=nt(r.pathname,{path:h,exact:u,strict:s,sensitive:p},t.match)}}),o?e.cloneElement(i,{location:r,computedMatch:o}):null},n}(e.Component);pt.contextTypes={router:h.shape({route:h.object.isRequired}).isRequired};var lt=n(function(t,e){var n,r,o,i,a,c,u,s;t.exports=(n={childContextTypes:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},r={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},o=Object.defineProperty,i=Object.getOwnPropertyNames,a=Object.getOwnPropertySymbols,c=Object.getOwnPropertyDescriptor,u=Object.getPrototypeOf,s=u&&u(Object),function t(e,p,l){if("string"!=typeof p){if(s){var h=u(p);h&&h!==s&&t(e,h,l)}var f=i(p);a&&(f=f.concat(a(p)));for(var d=0;d<f.length;++d){var y=f[d];if(!(n[y]||r[y]||l&&l[y])){var m=c(p,y);try{o(e,y,m)}catch(t){}}}return e}return e})});t.MemoryRouter=M,t.Prompt=q,t.Redirect=Z,t.Route=rt,t.Router=A,t.StaticRouter=st,t.Switch=pt,t.generatePath=X,t.matchPath=nt,t.withRouter=function(t){var n=function(n){var r=n.wrappedComponentRef,o=k(n,["wrappedComponentRef"]);return e.createElement(rt,{children:function(n){return e.createElement(t,C({},o,n,{ref:r}))}})};return n.displayName="withRouter("+(t.displayName||t.name)+")",n.WrappedComponent=t,lt(n,t)},Object.defineProperty(t,"__esModule",{value:!0})});
