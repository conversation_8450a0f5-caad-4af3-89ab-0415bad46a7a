/* This css file is for apple pages */

/* Start Utilities / helpers */

body main.apple {
    line-height: 1.42857143;
    overflow-x: scroll;
}


/*#region Position Helpers*/
.top-0 {top: 0}

.right-0 {right: 0}

.left-0 {left: 0}

/*#endregion */

/*#region Typography Helpers*/
.txtSize9 {font-size: 9px;}
.txtSize12 {font-size: 12px;}
.txtSize19 {font-size: 19px !important;}
.txtSize21 {font-size: 21px;}
.txtSize28 {font-size: 28px;}
.txtSize48 {font-size: 48px;}

.font-weight700 {font-weight: 700;}
.apple-line-height {line-height: 1.42857143;}
.lineHeight-29 {line-height: 29px;}
.lineHeight-52 {line-height: 52px;}


.apple-typography {
    color: #6A6A6A;
    font-size: 30px;
    line-height: 1.0625em;
    letter-spacing: -.04em;
    font-family: "SF Pro Display","SF Pro Icons","Helvetica Neue","Helvetica","Arial","sans-serif" !important;
    font-weight: 600;
}

.apple-typography-2 {
    font-size: 24px !important;
    color: #000 !important;
}

.apple-typography-3 {
    font-size: 16px !important;
    color: #61CDF4 !important;
}

.apple-typography-4 {
    font-family:Arial !important;
    font-size:1.2em;
}

.apple-typography-5 {
    line-height: 1.125;
    font-weight: 600;
    letter-spacing: 0em;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", "Helvetica", "Arial", sans-serif, "SF Pro Icons";
    color: #1d1d1f;
}

.apple-typography-6 {
    font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif;
    font-weight: 500;
    line-height: 1.235;
}

.apple-typography-7 {
    font-size: 14px;
    display: block;
    line-height: 24px;
    font-family: "Lucida Sans Unicode","Lucida Grande",Verdana,Arial,Helvetica,sans-serif;
}

.apple-typography-8 {
    font-size: 12px;
    font-family: "Lucida Sans Unicode","Lucida Grande",Verdana,Arial,Helvetica,sans-serif;
    color: #acacac !important;
    line-height: 24px;
}

.apple-typography-9 {
    color: #000 !important;
    text-decoration: underline;
}

.apple-typography-10 {
    font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif;
    line-height: 17.1429px;
    font-size: 12px;
    display: block;
    color: #555 !important;
}

.apple-typography-11 {
    color: #00549a !important;
    text-decoration: underline;
}

.apple-typography-12 {
    font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif;
    font-weight: 400;
    font-size: 12px;
    line-height: 16.0005px;
}

.apple-typography-tout {
    color: #555;
    padding-top: 8px;
    font-size: 19px;
    line-height: 1.25;
    font-weight: 600;
    letter-spacing: .012em;
    font-family: "SF Pro Display","SF Pro Icons","Helvetica Neue","Helvetica","Arial",sans-serif;
}

.apple-typography-manifesto {
    font-size: 28px;
    font-weight: 600;
    letter-spacing: .004em;
    line-height: 1.25;
}

.apple-sup {
    font-size: .5em;
    line-height: 0;
    position: relative;
    top: -.8em;
    vertical-align: baseline;
}

.apple-sup-2 {
    position: relative;
    font-size: 0.6em;
    vertical-align: baseline;
    top: -0.5em;
}

.apple-suptitle {
    font-size: 15px;
    top: -12px;
}

.apple-legal {
    font-size: 10px;
    background-color: #e5e6e8;
    line-height: 19px;
    padding: 10px 20% 33px 20%;
    color: grey;
}

.apple-typography-caption {
    font-size: 32px;
    font-weight: 600;
    letter-spacing: 0.004em;
    line-height: 36px;
}

.apple-text-light-blue {color: #61CDF4 !important;}
.apple-text-light-grey {color: #86868b;}
.apple-text-light-grey-2 {color: #a1a1a6;}

.apple-text-rose-gold {color: #97604a !important;}

.apple-text-black,
a.apple-text-black:hover {color: #515154;}

.apple-text-black-2 {color:#000;}
.apple-text-black-3 {color: #1d1d1f;}
.apple-text-black-4 {color: #555 !important;}

/*#endregion */

/*#region Dimension Helpers*/
.min-width-1230 {
    min-width: 1230px;
}

.max-width-400 {max-width: 400px;}
.max-width-480 {max-width: 480px;}

.width-80p {width: 80%;}
.width-70p {width: 70%;}
.width-55p {width: 55%;}


.height-28 {height: 28px;}
.height-35 {height: 35px;}

.bigcustom-bgRoseGold-apple-width {
    width: 100%;
}

/*#endregion */

/*#region Spacings Helpers*/

.margin-b-1em {
    margin-bottom: 1em;
}
/*#endregion */

/*#region Background Helpers*/
.light-grey-bottom-gradient {
    background: #ffffff;
    background: -moz-linear-gradient(top, #ffffff 0%, #fafafa 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%,#fafafa 100%);
    background: linear-gradient(to bottom, #ffffff 0%,#fafafa 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#fafafa',GradientType=0 );
}

.bgBlackLight-apple {background-color: #121212}
.bgRoseGold-apple {background-color: #fdede4;}
.bgLightGray-apple {background-color: #f0f0f0}
.bgPlatinumGray-apple {background-color: #e7e7e7}


/*#endregion */


/*** START Custom ***/
.border-gray {
    border-color: #eee;
}

.border-black {
    border-color: #555 !important;
}

.apple-devider {
    border-bottom: 1px solid #d6d6d6;
}

.apple-button-container {
    width: 100%;
    height: 4vh;
    z-index: 11
}

.apple-button-container.bottom-12-percent {
    bottom: 12%;
}

.apple-button-container.bottom-8-percent {
    bottom: 8%;
}


/** START COMPARE APPLE WATCH **/
.apple-hidden-button {
    width: 7.3%;
    height: 5%;
}

/*first set of bottons*/
.apple-watch-btn-8 {
    height: 4%;
    bottom: 8%;
    left: 16.5%;
}

.apple-watch-se-btn-8 {
    height: 4%;
    bottom: 8%;
    left: 55.5%;
}

/*second set of bottons*/
.apple-watch-btn-13 {
    bottom: 13.5%;
    left: 34.5%;
}

.apple-watch-se-btn-13 {
    bottom: 13.5%;
    left: 56%;
}

.apple-watch-btn-13-fr {
    bottom: 12.5%;
    left: 34.5%;
}

.apple-watch-se-btn-13-fr {
    bottom: 12.5%;
    left: 56%;
}
/** END COMPARE APPLE WATCH **/

/* End Utilities / helpers */


/* Start select style*/
.apple-select-form {
    position: relative;
    width: 100%;
    transition: border-color 200ms ease;
    border-bottom: solid 1px #d6d6d6;
    transition: border-color 200ms ease;
}

.apple-select-form select{
    line-height: 1.3529611765;
    font-weight: 600;
    letter-spacing: 0em;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", "Helvetica", "Arial", sans-serif, "SF Pro Icons";
    height: auto;
    border:none;
    border-radius: 5px;
    padding-bottom: 11px;
    padding-left: 0px;
    padding-top: 11px;
}

.apple-select-form select:focus {
    box-shadow: 0 0 0 4px rgba(131, 192, 253, 0.5);
}

.apple-select-form select::-ms-expand {
    display: none !important;
}

.apple-select-form select {
    -moz-appearance: none;
    -webkit-appearance: none;
}

.apple-select-form optgroup {
    font-family: inherit;
    font-weight:inherit;
}

.apple-select-form option{
    font-weight:inherit;
}

.apple-select-icon {
    top: 50%;
    transform: translateY(-50%);
    color: #0066cc;
    right: 0px;
    pointer-events: none;
}

.apple-select-form::before{
    content: "";
    width: 25px;
    position: absolute;
    background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, white 50%, white 100%);
    top: 0;
    height: 100%;
    right: 5px;
    z-index: 0;
    pointer-events: none;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
}
/* End select style*/

/* Start color swatches */
.apple-swatch {
    display: inline-block;
    margin: 0 9px 0 0;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    box-shadow: inset 0 0 1px 0 rgba(0, 0, 0, 0.2);
    display: block;
}
.apple-swatch:last-child{
    margin-right:0px;
}
.apple-swatch::after {
    border-radius: inherit;
    box-shadow: inset 0 2px 1.5px rgba(0, 0, 0, 0.1);
    content: "";
    display: block;
    height: inherit;
    position: absolute;
    width: inherit;
}
/* End color swatches */


/* Start button styles */
.btn-transparent {
    background-color: transparent;
    border-radius: 5px;
    border: 2px solid #adadad;
    color: #adadad;
    box-sizing: border-box;
    display: inline-block;
    margin: 15px 0;
    padding: 10px 36px;
    line-height: 1;
    text-align: center;
    text-decoration: none !important;
    cursor: pointer;
}

.btn-transparent:hover {
    color: #adadad;
}

.btn-compare-iphone {
    position: absolute;
    left: 38.5%;
    top: 88.96%;
    width: 26.83%;
    height: 3.18%;
    z-index: 2;
}

.btn-compare-iphone-xs {
    position: absolute;
    left: 20.5%;
    top: 91%;
    width: 55%;
    height: 5%;
    z-index: 2;
}
.apple-btn {
    border-radius: 18px;
    font-size: 11px;
    line-height: 1.235;
    padding: 8px 11px 8px 11px;
}
.apple-btn-outline-dark {
    background-color: transparent;
    border: 1px solid #1d1d1f;
    color: #1d1d1f;
}
.apple-btn-outline-dark:hover {
    border: 1px solid #6e6e73;
    color: #6e6e73;
}


/* End button styles */

/* Start compare iphone */
.iphone-12-pro, .iphone-12 {
    width: 80px;
}

.iphone-12-pro-max {
    width: 87px;
}

.iphone-12-mini {
    width: 72px;
}

.iphone-11-pro {
    width: 81px;
}

.iphone-11-pro-max {
    width: 86px;
}

.iphone-11 {
    width: 83px;
}

.iphone-se-2nd, .iphone-8, .iphone-6 {
    width: 65px;
}

.iphone-xs {
    width: 71px;
}

.iphone-xs-max {
    width: 77px;
}

.iphone-xr, .iphone-8-plus, .iphone-7-plus, .iphone-6s-plus, .iphone-6-plus {
    width: 74px;
}

.iphone-x {
    width: 75px;
}

.iphone-7, .iphone-6s {
    width: 64px;
}

.iphone-se-1st {
    width: 55px;
}
/* End compare iphone */

.apple-watch-buy-now-btn{
    height: .28%;
    width:7.3%;
    bottom:2.8%;
    left:57.8%;
}
.apple-watch-compare-btn{
    height: .29%;
    width:7.8%;
    bottom:6.83%;
    left:46%;
}

.absolute.apple-ipad-buy-now-fr {
    top: 6.8%;    
}
/*Mobile Only*/
@media (min-width:300px) and (max-width: 767px) {
    .apple-txtsize-xs-18 {
        font-size: 18px !important;
        margin: 0 auto;
    }

    .apple-txtsize-xs-16 {
        font-size: 16px !important;
        margin: 0 auto;
    }

    .apple-typography-xs-7 {
        font-size: 14px;
        display: block;
        text-align: left !important;
        line-height: 24px;
        font-family: "Lucida Sans Unicode","Lucida Grande",Verdana,Arial,Helvetica,sans-serif;
    }

    .apple-ipad-buy-now-1 {
        height: 0.4%;
        width: 8.5%;
        top: 7.2%;
        left: 11.5% !important;
    }

    .apple-ipad-buy-now-2 {
        height: 0.4%;
        width: 8.5%;
        top: 7.2%;
        left: 28.5% !important;
    }

    .apple-ipad-buy-now-3 {
        background-color:#ffffff;
        height: 1.4%;
        width: 12%;
        top: 7.2%;
        left: 44.5% !important;
    }

    .apple-ipad-buy-now-4 {
        height: 0.4%;
        width: 8.5%;
        top: 7.2%;
        right: 28.5% !important;
    }

    .apple-ipad-buy-now-5 {
        height: 0.4%;
        width: 8.5%;
        top: 7.2%;
        right: 11.5% !important;
    }
    .column-spacer-xs-20{
        margin-left:-5px;
        margin-right:-5px;
    }
    .column-spacer-xs-20 > div{
        padding-left:10px;
        padding-right:10px;
    }

}

@media (max-width: 767px) {
    .container-height-xs {
        max-height: 992px;
        overflow: auto;
    }
    .width-xs-290 {
        width: 290px;
    }
    
    .apple-custom-header {
        text-align: center;
    }

    .absolute-xs {
        position: absolute;
    }

}

/*Tablet Only*/
@media (min-width: 768px) and (max-width: 991px){

    .apple-txtsize-sm-18 {
        font-size: 18px !important;
        margin: 0 auto;
    }

    .apple-ipad-buy-now-1 {
        height: 0.4%;
        width: 8.5%;
        top: 7.2%;
        left: 11.5% !important;
    }

    .apple-ipad-buy-now-2 {
        height: 0.4%;
        width: 8.5%;
        top: 7.2%;
        left: 28.5% !important;
    }

    .apple-ipad-buy-now-3 {
        background-color: #ffffff;
        height: 1.4%;
        width: 12%;
        top: 7.2%;
        left: 44.5% !important;
    }

    .apple-ipad-buy-now-4 {
        height: 0.4%;
        width: 8.5%;
        top: 7.2%;
        right: 28.5% !important;
    }

    .apple-ipad-buy-now-5 {
        height: 0.4%;
        width: 8.5%;
        top: 7.2%;
        right: 11.5% !important;
    }
    
    .iPhone12-details-container {max-width: 680px;}

}

@media (min-width: 768px) {
    .max-width-sm-600 {
        max-width: 600px;
    }
    .height-sm-0{
        height: 0px;
    }
    /* Start compare iphone */
    .iphone-12-pro, .iphone-12 {
        width: 144px;
    }

    .iphone-12-pro-max {
        width: 157px;
    }

    .iphone-12-mini {
        width: 129px;
    }

    .iphone-11-pro {
        width: 147px;
    }

    .iphone-11-pro-max {
        width: 155px;
    }

    .iphone-11 {
        width: 149px;
    }

    .iphone-se-2nd, .iphone-8, .iphone-7, .iphone-6s {
        width: 116px;
    }

    .iphone-xs {
        width: 129px;
    }

    .iphone-xs-max {
        width: 138px;
    }

    .iphone-xr, .iphone-8-plus, .iphone-7-plus, .iphone-6s-plus {
        width: 133px;
    }

    .iphone-x, .iphone-6-plus {
        width: 134px;
    }

    .iphone-6 {
        width: 117px;
    }

    .iphone-se-1st {
        width: 99px;
    }
    /* End compare iphone */
    .apple-select-form {
        border: solid 1px #d6d6d6;
        border-radius: 12px;
    }
    .apple-select-form select{
        border-radius: 12px;
        padding:16px;
    }
    .apple-select-form:hover {
        border: solid 1px #666;
    }
    .apple-select-icon {
        right: 15px;
    }
    .apple-select-form::before {
        width: 60px;
    }
    .apple-swatch {
        width: 20px;
        height: 20px;
    }

    .apple-btn {
        border-radius: 18px;
        font-size: 17px;
        line-height: 1.235;
        padding: 8px 17px 8px 17px;
    }
    .apple-typography-5 {
        line-height: 1.0834933333;
    }

    .apple-watch-buy-now-btn{
        height: .28%;
        width:7.3%;
        bottom:2.8%;
        left:57.3%;
    }
    .apple-watch-compare-btn{
        height: .29%;
        width:7.2%;
        bottom:6.83%;
        left:46.4%;
    }
    .column-spacer-sm-30 {
        margin: 0 -15px;
    }
    .column-spacer-sm-30 > div {
        padding: 0 15px;
    }
    

}

@media (min-width: 992px) {
    .max-width-md-960 {
        max-width: 960px;
    }

    .width-md-52p {
        width: 52%;
    }

    .width-md-64p {
        width: 64%;
    }

    .pad-h-md-20 {
        padding-left: 20px;
        padding-right: 20px;
    }
    .apple-watch-buy-now-btn{
        height: .28%;
        width:7.3%;
        bottom:2.8%;
        left:57.8%;
    }
    .apple-watch-compare-btn {
        height: .29%;
        width: 7.7%;
        bottom: 6.83%;
        left: 46.1%;
    }
    .apple-ipad-buy-now-1 { 
        height: 0.4%;
        width:8.5%;
        top:7.2%;
        left: 11.5% !important;
    }
    .apple-ipad-buy-now-2 {
        height: 0.4%;
        width: 8.5%;
        top: 7.2%;
        left: 28.5% !important;
    }
    .apple-ipad-buy-now-3 {
        background-color: #ffffff;
        height: 1.4%;
        width: 12%;
        top: 7.2%;
        left: 44.5% !important;
    }
    .apple-ipad-buy-now-4 {
        height: 0.4%;
        width: 8.5%;
        top: 7.2%;
        right: 28.5% !important; 
    }
    .apple-ipad-buy-now-5 {
        height: 0.4%;
        width: 8.5%;
        top: 7.2%;
        right: 11.5% !important;
    }
        
}

@media (min-width: 991.98px) {
    .pad-h-md-40 {
        padding-left: 40px;
        padding-right: 40px;
    }
    .pad-v-lg-60 {
        padding-top: 60px;
        padding-bottom: 60px;
    }

    .pad-t-lg-60 {
        padding-top: 60px;
    }

    .pad-t-lg-40 {
        padding-top: 40px;
    }

    .margin-v-lg-60 {
        margin-top: 60px;
        margin-bottom: 60px;
    }

    .apple-typography-lg {
        font-family: "Lucida Sans Unicode","Lucida Grande",Verdana,Arial,Helvetica,sans-serif;
        font-size: 1.357em;
        letter-spacing: .029em;
        line-height: 1.8;
        text-align: center;
        color: #595859;
        font-weight: normal;
    }
}

@media (max-width: 999px) {
    .apple-md-100p {
        max-width: 100% !important;
        padding: 0px;
    }

    .apple-flex-dir-column-md {
        flex-direction: column;
    }
}

@media (min-width: 1000px) {
    
    .max-width-135-lg {max-width: 135px;}

    .rsx-width {
        width: 50% !important;
        margin: 0 auto;
        display: flex;
    }

    .rsx-txt-size {
        font-size: 28px !important;
        margin: 0 auto;
    }
}


@media only screen and (min-width: 1240px) {
    .max-width-lg-1200 {
        max-width: 1200px
    }

    .pad-h-lg-40 {
        padding-left: 40px;
        padding-right: 40px;
    }

    .pad-h-lg-60 {
        padding-left: 60px;
        padding-right: 60px;
    }
    .pad-t-lg-140{
        padding-top:140px;
    }
    .pad-t-lg-180{
        padding-top:180px;
    }

    .apple-eight-text-width {
        width: 1200px;
    }

    .rsx-txt-size {
        font-size: 28px !important;
    }

    .rsx-width {
        width: 50% !important;
        margin: 0 auto;
    }

    .txtSize17-lg {
        font-size: 17px;
    }

    .txtSize19-lg {
        font-size: 19px;
    }

    .txtSize64-lg {
        font-size: 64px;
    }

    .margin-b-lg-20 {
        margin-bottom: 20px;
    }

    /* Start compare iphone */
    .iphone-12-pro, .iphone-12 {
        width: 171px;
    }

    .iphone-12-pro-max, .iphone-11-pro-max {
        width: 185px;
    }

    .iphone-12-mini {
        width: 154px;
    }

    .iphone-11-pro {
        width: 175px;
    }

    .iphone-11 {
        width: 172px;
    }

    .iphone-se-2nd, .iphone-8, .iphone-6 {
        width: 138px;
    }

    .iphone-xs {
        width: 153px;
    }

    .iphone-xs-max {
        width: 165px;
    }

    .iphone-xr {
        width: 160px;
    }

    .iphone-x, .iphone-6-plus {
        width: 161px;
    }

    .iphone-8-plus, .iphone-6s-plus {
        width: 158px;
    }

    .iphone-7-plus {
        width: 159px;
    }

    .iphone-7 {
        width: 139px;
    }

    .iphone-6s {
        width: 137px;
    }

    .iphone-se-1st {
        width: 119px;
    }
    /* End compare iphone */
    .apple-swatch {
        width: 22px;
        height: 22px;
    }
    .apple-typography-5 {
        line-height: 0.74em;
    }
    .margin-b-lg-20 {
        margin-bottom: 20px;
    }
    .margin-b-lg-30 {
        margin-bottom: 30px;
    }
    .margin-b-lg-35 {
        margin-bottom: 35px;
    }
    .margin-b-lg-45 {
        margin-bottom: 45px;
    }
    .margin-b-lg-60{
        margin-bottom:60px;
    }
    .margin-b-lg-70{
        margin-bottom:70px;
    }
    .margin-b-lg-55{
        margin-bottom:55px;
    }
    .apple-watch-buy-now-btn{
        height: .28%;
        width:7.7%;
        bottom:2.8%;
        left:58%;
    }
    .apple-watch-compare-btn{
        height: .29%;
        width:8%;
        bottom:6.83%;
        left:46%;
    }
    .width-lg-695{
        width: 695px;
    }
}

@media (max-width: 519px){
    .apple-container.container {
        padding-left:15px;
        padding-right: 15px;
    }

    .apple-container.container.no-pad-xs {
        padding-left: 0;
        padding-right: 0;
    }
}

@media (min-width: 520px){
    .apple-container.container {
        max-width: 480px;
        padding-left:0;
        padding-right: 0;
    }
}

@media (min-width: 640px){
    .apple-container.container {
        max-width: 600px;
        padding-left:0;
        padding-right: 0;
    }
}

@media (min-width: 1000px){
    .apple-container.container {
        max-width: 960px;
        padding-left:0;
        padding-right: 0;
    }
    .apple-container.container.width-md-70-percent{
        width: 70%;
        max-width: 1200px;
    }
    

}

@media (min-width: 1240px){
    .apple-container.container {
        max-width: 1200px;
        padding-left:0;
        padding-right: 0;
    }

    .iPhone-left-container {
        padding-left: 60px;
    }
}

@media (min-width: 1280px) {
    .apple-width-lg-90 {
        width: 90% !important;
    }
}
