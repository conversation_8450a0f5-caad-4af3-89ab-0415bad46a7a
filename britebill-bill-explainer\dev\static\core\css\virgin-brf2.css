﻿/* BRF - v3.0
Latest update 2019 November 25

Rules
1 Please keep the fonts grouped together at the top of the file.
2 Please keep the media queries grouped at the bottom of the file.
Virgin font*/
/*VirginUltra font*/
@font-face {
    font-family: 'VMUltramagneticNormalRegular';
    src: url('../fonts/vmultramagnetic-normal-webfont.eot');
    src: url('../fonts/vmultramagnetic-normal-webfont.eot?#iefix') format('embedded-opentype'),url('../fonts/vmultramagnetic-normal-webfont.ttf') format('truetype')
}

@font-face {
    font-family: 'BigUltramagneticBold';
    src: url('../fonts/vmultramagnetic-bold-webfont.eot');
    src: url('../fonts/vmultramagnetic-bold-webfont.eot?#iefix') format('embedded-opentype'),url('../fonts/vmultramagnetic-bold-webfont.ttf') format('truetype')
}

@font-face {
    font-family: 'virgin-icons';
    src: url('../fonts/virgin-icons.eot?qkura8');
    src: url('../fonts/virgin-icons.eot?qkura8#iefix') format('embedded-opentype'), url('../fonts/virgin-icons.ttf?qkura8') format('truetype'), url('../fonts/virgin-icons.woff?qkura8') format('woff'), url('../fonts/virgin-icons.svg?qkura8#icomoon') format('svg');
    font-weight: normal;
    font-style: normal
}

.virginUltra {
    font-family: "BigUltramagneticBold", Arial, Helvetica, sans-serif;
    font-weight: normal
}

.virginUltraReg {
    font-family: "VMUltramagneticNormalRegular", Arial, Helvetica, sans-serif;
    font-weight: normal
}
.sans-serif{font-family:Arial, Helvetica, sans-serif;letter-spacing:0}

body{color:#555;background-color:#e1e1e1;overflow-x:hidden;font-family: Helvetica,Arial,sans-serif;font-size:14px}
main{overflow-x:hidden}


.big-title,.title,.small-title,.small-title-2{font-family: "VMUltramagneticNormalRegular", Arial, Helvetica, sans-serif;font-weight: normal}
.big-title{font-size:41px;font-weight:normal;line-height:1}
.title{font-size:28px;font-weight:normal;line-height:1}
.small-title-1{font-size:26px;font-weight:normal;line-height:1}
.small-title-2{font-size:22px;font-weight:normal;line-height:1}
.subtitle{font-size:18px;font-weight:bold;line-height:1}
.surtitle{font-size:14px;font-weight:bold;line-height:1}
.small-copy{font-size:12px;font-weight:normal;line-height:1}
.tiny-copy{font-size:10px;font-weight:normal;line-height:1}


/*START UTILITY CLASS*/
.col-lg-1,.col-lg-10,.col-lg-11,.col-lg-12,.col-lg-2,.col-lg-3,.col-lg-4,.col-lg-5,.col-lg-6,.col-lg-7,.col-lg-8,.col-lg-9,.col-md-1,.col-md-10,.col-md-11,.col-md-12,.col-md-2,.col-md-3,.col-md-4,.col-md-5,.col-md-6,.col-md-7,.col-md-8,.col-md-9,.col-sm-1,.col-sm-10,.col-sm-11,.col-sm-12,.col-sm-2,.col-sm-3,.col-sm-4,.col-sm-5,.col-sm-6,.col-sm-7,.col-sm-8,.col-sm-9,.col-xs-1,.col-xs-10,.col-xs-11,.col-xs-12,.col-xs-2,.col-xs-3,.col-xs-4,.col-xs-5,.col-xs-6,.col-xs-7,.col-xs-8,.col-xs-9{padding-right:0;padding-left:0}
ol,ul{padding-left:17px}
.table-cell{display:table-cell!important;float:none!important}
.table{display:table!important;margin-bottom:0;table-layout:fixed;width:100%}

/*Replacement of panel body*/
.card-body{padding:20px 40px}
.img-responsive{display:block;max-width:100%;height:auto}
  
/*Replacement of well class that was removed in bootstrap*/
.grid-container{min-height:20px;padding:20px 35px;margin-bottom:20px;background-color:#f5f5f5;border:1px solid #e3e3e3;border-radius:4px;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.05);box-shadow:inset 0 1px 1px rgba(0,0,0,.05)}
.grid-container-sm{padding:9px;border-radius:3px}

/*START SHOW/HIDE PASSWORD BUTTOn*/
.maskUnMaskPwsBtn{right:12px;top:7px;border:medium none;background:#bbbec3;width:auto;height:25px;color:#000;border-radius:5px;font-size:11px}
/*END SHOW/HIDE PASSWORD BUTTOn*/

.scrollToTop.mobile{opacity:.9;right:12px;bottom:12px;display:none;width:50px;height:50px;position:fixed;padding-top:7px;border-radius:50%;z-index:99999;-webkit-box-shadow:0 0 26px -6px rgba(0,0,0,0.75);-moz-box-shadow:0 0 26px -6px rgba(0,0,0,0.75);box-shadow:0 0 26px -6px rgba(0,0,0,0.75)}

/*END UTILITY CLASS*/

/* icon helper classes */
 /* REMOVED ******* No Icon Helper classes required yet */
/*End*/

/* Some common circle  icon presets */

/*links*/
a{color:#2390B9;text-decoration:none}
a.txtWhite:hover,a.txtWhite:focus{color:#fff}
a:hover,a:focus{color:#2390B9;text-decoration:underline}
a:hover.txtNoUnderline,a:focus.txtNoUnderline{text-decoration:none}
.tabOuter_container a:hover.txtNoUnderline,.tabOuter_container a:focus.txtNoUnderline{color:#fff}
a:focus,li.ui-menu-item:focus{outline-width:2px; outline-style:solid; outline-color:#4d90fe!important;outline-offset:2px}
a:link .icon:before,a:visited .icon:before,a:hover .icon:before,a:focus .icon:before a:active .icon:before{text-decoration:none;display:inline-block}
a.error:link,a.error:visited,a.error:hover,a.error:active{color:#BD2025}

a.skip-to-main-link:focus{outline:2px solid #2672cb}

/*START PROGRESS STEPS*/
 
/*START PROGRESS STEPS*/
 /* Removed as progressive steps are not required yet */
/*END PROGRESS STEPS*/

/*END PROGRESS STEPS*/

/*START MODALS*/
.modal-content{border-radius:0;-webkit-box-shadow:0 0 30px rgba(0,0,0,0.3);-moz-box-shadow:0 0 30px rgba(0,0,0,0.3);box-shadow:0 0 30px rgba(0,0,0,0.3)}
.modal-header .close{margin-top:-7px;margin-right:-15px}
.modal-lg .modal-header .close{margin-top:-7px;margin-right:-17px}
.modal-lg.virgin-modal-lg .modal-header .close{padding:15px;margin:-5px -15px -15px -18px}
.modal .modal-close_cros{border:0;background-color:transparent;width:40px;height:40px}
.modal .modal-md .close{padding:15px;margin:-7px -15px -15px -18px}
.modal.modal-tooltip .modal-body,.modal-header,.modal-footer{padding:15px 30px}
.modal.modal-tooltip{z-index:99999}
.modal.modal-tooltip .modal-body{padding:0 40px 40px}
.modal-body{padding:30px}
.modalSelectNav{width:90%;margin:auto}
.close{opacity:1}
.close:hover,.close:focus{opacity:1}
.modal-footer{text-align:left;border-top:none}
.modal-footer .btn + .btn{margin-bottom:0;margin-left:0}
button.close:focus{border:1px dotted}
.modal-content{border:0}
.modal-title.line-height-20{line-height:23px;margin-top:0px}
.modal-title{line-height:26px;}
.unfocus,.unfocus:focus{border:0;outline:0}
.modal .modal-md .close{margin-top:-20px}
.modal-header-gray{height:74px;background-color:#e1e1e1}
.modal-header-blue{height:74px;background-color:#E10A0A}
.modal-dialog{width:645px;max-width:100%}
.modal.modal-tooltip .modal-content{border:0}
.modal-open .modal{overflow:hidden}
/*fix for view portview width issue on mobile*/
.modal{width:calc(var(--vw, 1vw) * 100)}

/*MODAL WINDOW VERTICAL CENTERING*/
.modal{text-align:center;padding:0;z-index:9999999}
.modal:before{content:'';display:inline-block;height:100%;vertical-align:middle;margin-right:0}
.modal-dialog{display:inline-block;text-align:left;vertical-align:middle;margin: auto;}
/*MODAL WINDOW VERTICAL CENTERING*/
.modal.modal-tooltip .modal-content{border:0}
/*END MODALS*/

/*START TOOLTIP*/
.tooltip{width:315px;position:absolute!important}
.tooltip-inner{max-width:315px;padding:40px;color:#555;font-size:14px;text-align:left;background-color:#FFF;border-radius:0;
               box-shadow: 0 0 11px 0 rgba(0,0,0,0.27);
               -webkit-box-shadow: 0 0 11px 0 rgba(0,0,0,0.27);-moz-box-shadow: 0 0 11px 0 rgba(0,0,0,0.27);}
.tooltip-lg .tooltip{width:700px}
.tooltip-lg .tooltip .tooltip-inner{max-width:700px;padding:30px}
.tooltip.show{opacity:1}
.tooltip .arrow{height:30px}
.tooltip.bs-tooltip-right{padding:0 5px;margin-left:20px}
.tooltip.bs-tooltip-right .arrow{margin-top:-25px}
.tooltip.bs-tooltip-right .arrow{-webkit-filter: drop-shadow(-6px 0px 3px rgba(0,0,0,0.12)); 
    -moz-filter: drop-shadow(-6px 0px 3px rgba(0,0,0,0.12));
    filter: drop-shadow(-6px 0px 3px rgba(0,0,0,0.12));}
.tooltip.bs-tooltip-right .arrow::before{border-width:25px 25px 25px 0;border-right-color:#fff}
.tooltip.bs-tooltip-left{padding:0 5px;margin-right:20px}
.tooltip.bs-tooltip-left .arrow{margin-top:-25px}
.tooltip.bs-tooltip-left .arrow::before{border-width:25px 0 25px 25px;border-left-color:#fff}
.tooltip.bs-tooltip-left .arrow{-webkit-filter: drop-shadow(4px -1px 2px rgba(0,0,0,0.17)); 
    -moz-filter: drop-shadow(4px -1px 2px rgba(0,0,0,0.17));
    filter: drop-shadow(4px -1px 2px rgba(0,0,0,0.17));}

.tooltip.bs-tooltip-top{padding:0 5px;margin-bottom:20px}
.tooltip.bs-tooltip-top .arrow{margin-left:-35px;margin-bottom:-25px}
.tooltip.bs-tooltip-top .arrow::before{border-width:25px 25px 0;border-top-color:#fff}
.tooltip.bs-tooltip-top .arrow{-webkit-filter: drop-shadow(0px 9px 5px rgba(0,0,0,0.12)); 
    -moz-filter: drop-shadow(0px 9px 5px rgba(0,0,0,0.12));
    filter: drop-shadow(0px 9px 5px rgba(0,0,0,0.12));}
.tooltip.bs-tooltip-bottom{padding:0 5px;margin-top:20px}
.tooltip.bs-tooltip-bottom .arrow{margin-left:-35px;margin-top:-25px}
.tooltip.bs-tooltip-bottom .arrow::before{border-width:0 25px 25px;border-bottom-color:#fff}
.tooltip.in{filter:alpha(opacity=100);opacity:1}
.tooltip.bs-tooltip-bottom .arrow{-webkit-filter:drop-shadow(0px -8px 5px rgba(0,0,0,0.12)); 
    -moz-filter: drop-shadow(0px -8px 5px rgba(0,0,0,0.12));
    filter: drop-shadow(0px -8px 5px rgba(0,0,0,0.12));}
/*END TOOLTIP*/


/* IE10+ CSS styles for tooltip*/
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) { 
    .tooltip.bs-tooltip-right .arrow{ 
       box-shadow: -15px 15px 30px rgba(0,0,0,0.42); 
    }
    .tooltip.bs-tooltip-left .arrow{ 
       box-shadow: 15px 15px 30px rgba(0,0,0,0.42); 
    }
    .tooltip.bs-tooltip-top .arrow{ 
       box-shadow: 20px 10px 30px rgba(0,0,0,0.42); 
    }
    .tooltip.bs-tooltip-bottom .arrow{ 
       box-shadow: 20px -5px 30px rgba(0,0,0,0.42); 
    }
}

/* START CHECKBOX SWITCH */.form-switch-container{width:120px;border:1px solid #D7D7D7;height:30px;border-radius:15px;font-size:12px}
.form-switch-toggle > span,.form-switch-toggle > span{color:#fff}
.form-switch-toggle span span,.form-switch-toggle label,.form-switch-toggle span span,.form-switch-toggle label{color:#fff}
.form-switch-toggle span span,.form-switch-toggle input:checked ~ span span:first-child,.form-switch-toggle.form-switch-candy label{color:#E10A0A}
.form-switch-toggle input ~ span span:first-child,.form-switch-toggle input:checked ~ span span:nth-child(2),.form-switch-candy input:checked + label{color:#fff}
.form-switch-toggle a,.form-switch-toggle span span{display:none}
.form-switch-toggle{display:block;cursor:pointer;height:30px;position:relative;overflow:visible;padding:0;margin-left:0;margin-bottom:0}
.form-switch-toggle *{box-sizing:border-box}
.form-switch-toggle a{display:block;transition:all .3s ease-out 0}
.form-switch-toggle label,.form-switch-toggle > span{line-height:30px;vertical-align:middle}
.form-switch-toggle label{font-weight:700;margin-bottom:2px;max-width:100%;color:#555}
.form-switch-toggle input{position:absolute;opacity:0;z-index:5}
.form-switch-toggle input:checked ~ a{right:2%}
.form-switch-toggle > span{position:absolute;left:-100px;width:100%;margin:0;padding-right:100px;text-align:left}
.form-switch-toggle > span span{position:absolute;left:2px;z-index:5;display:block;width:50%;margin-left:100px;text-align:center}
.form-switch-toggle > span span:last-child{left:50%}
.form-switch-toggle a{position:absolute;right:48%;top:48%;z-index:4;display:block;width:50%;height:24px;border-radius:12px;padding:0;transform:translateY(-48%)}
.form-blue-onoffswitch{position:relative;width:70px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none}
.form-blue-onoffswitch-checkbox{position:absolute;height:28px;width:70px;opacity:0}
.form-blue-onoffswitch-label{display:block;overflow:hidden;cursor:pointer;border-radius:50px}
.form-blue-onoffswitch-inner{display:block;width:200%;margin-left:-100%}
.form-blue-onoffswitch-inner:before,.form-blue-onoffswitch-inner:after{display:block;float:left;width:50%;height:30px;padding:0;line-height:30px;font-size:12px;color:#fff;font-family:Trebuchet,Arial,sans-serif;font-weight:700;box-sizing:border-box}
.form-blue-onoffswitch-inner:before{content:"YES";padding-left:13px;background-color:#E10A0A;color:#FFF}
.form-blue-onoffswitch-inner:after{content:"NO";padding-right:14px;background-color:#BD2025;color:#FFF;text-align:right}
.form-blue-onoffswitch-switch{display:block;width:24px;margin:3px;background:#D7D7D7;position:absolute;top:0;bottom:0;border-radius:50px;transition:all .3s ease-in 0}
.form-blue-onoffswitch-checkbox:checked + .form-blue-onoffswitch-label .form-blue-onoffswitch-inner{margin-left:0}
.form-blue-onoffswitch-checkbox:checked + .form-blue-onoffswitch-label .form-blue-onoffswitch-switch{right:0;background:#fff}
/* END CHECKBOX SWITCH*/


/*START RANGE SLIDER*/.rangeslider,.rangeslider__fill{display:block;-moz-box-shadow:inset 0 1px 3px rgba(0,0,0,0.3);-webkit-box-shadow:inset 0 1px 3px rgba(0,0,0,0.3);box-shadow:inset 0 1px 3px rgba(0,0,0,0.3);-moz-border-radius:10px;-webkit-border-radius:10px;border-radius:10px}
.rangeslider{background:#fff;position:relative;-moz-box-shadow:inset 0 1px 3px 0 rgba(0,0,0,0.2);-webkit-box-shadow:inset 0 1px 3px 0 rgba(0,0,0,0.2);box-shadow:inset 0 1px 3px 0 rgba(0,0,0,0.2)}
.rangeslider--horizontal{height:10px;width:100%}
.rangeslider--vertical{width:20px;min-height:150px;max-height:100%}
.rangeslider--disabled{filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=40);opacity:.4}
.rangeslider__fill{background:#E10A0A;position:absolute}
.rangeslider--horizontal .rangeslider__fill{top:0;height:100%}
.rangeslider--vertical .rangeslider__fill{bottom:0;width:100%}
.rangeslider__handle{background:#fff;cursor:pointer;display:inline-block;width:24px;height:24px;position:absolute;-moz-box-shadow:inset 0 1px 3px 0 rgba(0,0,0,0.14);-webkit-box-shadow:inset 0 1px 3px 0 rgba(0,0,0,0.14);box-shadow:inset 0 1px 3px 0 rgba(0,0,0,0.14);-moz-border-radius:50%;-webkit-border-radius:50%;border-radius:50%;background:#BD2025}
.rangeslider__handle:after{content:"";display:block;width:12px;height:12px;margin:auto;position:absolute;top:0;right:0;bottom:0;left:0;-moz-border-radius:50%;-webkit-border-radius:50%;border-radius:50%;background:#fff}
.rangeslider__handle:active,.rangeslider--active .rangeslider__handle{background-image:url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4gPHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IGlkPSJncmFkIiBncmFkaWVudFVuaXRzPSJvYmplY3RCb3VuZGluZ0JveCIgeDE9IjAuNSIgeTE9IjAuMCIgeDI9IjAuNSIgeTI9IjEuMCI+PHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzAwMDAwMCIgc3RvcC1vcGFjaXR5PSIwLjEiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMwMDAwMDAiIHN0b3Atb3BhY2l0eT0iMC4xMiIvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZCkiIC8+PC9zdmc+IA==);background-size:100%;background-image:-webkit-gradient(linear,50% 0%,50% 100%,color-stop(0%,rgba(0,0,0,0.1)),color-stop(100%,rgba(0,0,0,0.12)));background-image:-moz-linear-gradient(rgba(0,0,0,0.1),rgba(0,0,0,0.12));background-image:-webkit-linear-gradient(rgba(0,0,0,0.1),rgba(0,0,0,0.12));background-image:linear-gradient(rgba(0,0,0,0.1),rgba(0,0,0,0.12))}
.rangeslider--horizontal .rangeslider__handle{top:-8px;touch-action:pan-y;-ms-touch-action:pan-y}
.rangeslider--vertical .rangeslider__handle{left:-10px;touch-action:pan-x;-ms-touch-action:pan-x}
input[type="range"]:focus + .rangeslider .rangeslider__handle{-moz-box-shadow:0 0 8px rgba(255,0,255,0.9);-webkit-box-shadow:0 0 8px rgba(255,0,255,0.9);box-shadow:0 0 8px rgba(255,0,255,0.9)}
.image-bottom-right{bottom:0;right:0}
/*END RANGE SLIDER*/


/*START FORM CONTROLS*/.form-control{border-radius:0;color:#333}
.form-control-blue.form-control{color:#fff}
.has-error .form-control:focus{border-color:#BD2025}
.has-error .form-control{border-color:#BD2025}
label{font-weight:400}
.radio{margin-bottom:15px}
.has-error .checkbox,.has-error .checkbox-inline,.has-error .control-label,.has-error .help-block,.has-error .radio,.has-error .radio-inline,.has-error.checkbox label,.has-error.checkbox-inline label,.has-error.radio label,.has-error.radio-inline label{color:#BD2025}
.help-block{color:#333}
.variableWidthInput{width:100%}
.variableWidthInput2{width:90%}
.form-control:focus{border-color:#BD2025}
.form-control.form-control-gray{background-color:#f4f4f4}
.form-control.form-control-blue{background-color:#E10A0A}
.form-control{display:block;width:100%;height:44px;background-color:#fff;background-image:none;border:2px solid #D7D7D7;padding-left:15px;padding-right:15px;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0);box-shadow:inset 0 1px 1px rgba(0,0,0,.0)}

/*Prepare the select element*/
select{padding:9px;border:1px solid #ccc}

/*IE 10 version fixes*/
select::-ms-expand{display:none}
select::-ms-value{background:none;color:#5a5a5a}
/*IE 10 version fixes*/
.form-control-select{box-shadow:none;-webkit-appearance:none;-moz-appearance:none;cursor:pointer;padding-right:38px}

/*Select box wrapper element*/
.form-control-select-box{position:relative;display:inline-block}
/*Overtlay icon for custom select box*/
.form-control-select-box.select-box-gray:after{background-color:#f0f0f0}
.form-control-select-box.select-box-blue:after{background-color:#E10A0A;color:#fff}
.form-group.error .form-control-select-box:after{color:#bd2025}
.form-control-select-box:after{font-family:"virgin-icon";content:"\e9bd";font-size:18px;background-color:#fff;color:#E10A0A;right:2px;top:2px;padding:11px 14px 10px 0;height:44px;position:absolute;pointer-events:none}
TEXTAREA.form-control{min-height:100px;padding-top:10px;padding-bottom:10px}
.form-label{color:#000;display:inline-block;font-weight:700;margin-bottom:10px}
.form-group.error,.form-group.error .form-label,.form-group.error .form-control{color:#BD2025;border-color:#BD2025;transition:border-color .5s cubic-bezier(.55,0,.1,1),color .5s cubic-bezier(.55,0,.1,1)}
.form-inline .form-group,.form-inline .form-control{display:inline-block;width:auto;vertical-align:middle}
.form-control-validated-wrap.success,.form-control-validated-wrap.error{padding-right:60px;display:inline-block;position:relative}
.form-control-validated-wrap .icon{position:absolute;right:0;height:40px;width:40px;top:5px;border:2px solid #555;border-radius:50%;display:none}
.form-control-validated-wrap.success .icon{border-color:#378E42;display:block}
.form-control-validated-wrap.error .icon{border-color:#BD2025;display:block}
.form-control-validated-wrap .icon:before{position:absolute;right:8px;top:9px;font-size:19px}
.form-control-validated-wrap.success .icon:before{color:#378E42}
.form-control-validated-wrap.error .icon:before{color:#BD2025}
.form-padding [class^="col-md"],.form-padding [class*=" col-md"]{padding:0 20px}
.form-padding [class^="col-md"] [class^="col-sm"],.form-padding [class*=" col-md"] [class*=" col-sm"]{padding:0 20px}
.form-item-note-error{padding-top:5px;padding-bottom:10px}
.disabled,.disabled label,.disabled input{opacity:.5;cursor:default}
.error{color:#BD2025;border-color:#BD2025}
.graybg.form-control-select{background-color:#f4f4f4}


@media (max-width: 519.98px) {
    .form-label{margin-bottom:10px}
    .form-control-validated-wrap.success,.form-control-validated-wrap.error{padding-right:0}
    .form-control-validated-wrap.success .form-control,.form-control-validated-wrap.error .form-control{padding-right:50px}
    .form-control-validated-wrap .icon{top:10px;right:10px;height:30px;width:30px}
    .form-control-validated-wrap .icon:before{right:5px;top:5px;font-size:16px;font-weight:700}
    .video-group div{padding-left:0}
}

@media (min-width: 520px) {
    .form-group{display:table;width:100%}
    .form-group > .row{display:table-row}
    .form-group > .form-label-col,.form-group > .form-control-col,.form-group > .row > .form-label-col,.form-group > .row > .form-control-col{display:table-cell;float:none;vertical-align:middle}
    .form-group > .form-label-col[class^="col-"],.form-group > .row > .form-label-col[class^="col-"]{text-align:right;padding-right:40px}
    .form-label-col .form-label{margin-bottom:0}
}
.form-input-group{position:relative}
.form-input-group input{padding-right:46px}
.form-input-group i{position:absolute;right:0;padding:4px 4px 0 0;pointer-events:none;font-size:42px}

/* Steps */
.form-steps{counter-reset:form-steps;list-style:none;margin-top:0;transition:margin .5s cubic-bezier(.55,0,.1,1);padding-left:0;border:1px solid #D7D7D7}
.form-steps > li{background-color:#fff;transition:background-color .5s cubic-bezier(.55,0,.1,1),color .5s cubic-bezier(.55,0,.1,1);font-family:'bell-slim',Helvetica,Arial,sans-serif;font-size:22px;font-weight:700}
.form-steps > li[class*="col-"]{padding:20px}
.form-steps > li:before{counter-increment:form-steps;content:counter(form-steps) '. '}
.form-steps > li.active{background-color:#E10A0A;color:#fff}

/* fix vertical alignment of text inside the select element */
.form-control-select{padding-bottom:9px;padding-top:11px;padding-left:15px;height:44px;line-height:18px;font-size:14px}
/* default select font color for IE Edge */
select.form-control::-ms-value{color:#333}
/* default seect arrow styles use a next sibling span for the arrow styles are copied from bell.css pseudoelement arrow */
.form-control-select + span {font-family: "virgin-icon";font-size: 18px;background-color: transparent;right: 2px;top: 2px;padding: 11px 14px 10px 0;height: 44px;position: absolute;pointer-events: none;color: #E10A0A;padding: 10px 9px 6px 0;height: 41px;top: 0}

/* blue select font color for IE Edge */
.form-control-select-box.select-box-blue .form-control-select::-ms-value{color:#fff}
/* blue select arrow color */
.form-control-select-box.select-box-blue .form-control-select + span{color:#fff}
/* error select font color for IE Edge */
.form-group.error select.form-control::-ms-value{color:#bd2025}
/* error select arrow color */
.form-group.error .form-control-select-box .form-control-select + span{color:#bd2025}
/* remove bell.css pseudoelement arrow */
.form-control-select-box:after{display:none}
@media (max-width: 991.98px) {
.form-control-select-box:after{font-size:16px;padding:11px 20px 10px 0}
.form-steps > li + li[class*="col-"]{border-top-width:1px}
.form-steps > li + li[class*="col-"]:not(.active){border-top-width:1px}
}

@media (min-width: 992px) {
.form-steps > li + li[class*="col-"]:not(.active){border-left-width:1px}
}

/* Required */
.form-required:before{content:'*';display:inline-block;margin-right:.25em;font-weight:700;color:#BD2025}

/* Date */
@media (max-width: 519.98px) {
.form-control-date a{display:block;margin-top:10px}
}
@media (min-width: 520px) {
.form-control-date input[type="text"]{width:50%}
.form-control-date a{position:absolute;bottom:55px;left:calc(50% + 15px)}
}

/* Edit */
.form-review-edit{position:absolute;top:18px;right:0;transition:transform .5s cubic-bezier(.55,0,.1,1);width:45px;height:45px;border-radius:50%;background-color:#E10A0A;font-size:54px;color:#fff}
.form-review-edit i,.form-review-edit i:before{display:block}
.form-review-edit i{position:relative;width:100%;height:100%}
.form-review-edit i:before{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%);font-size:48px;color:#fff}
@media (max-width: 519.98px) {
.form-review-edit{-webkit-transform:translate(-30px,25%);-ms-transform:translate(-30px,25%);transform:translate(-30px,25%)}
}
@media (min-width: 520px) and (max-width: 639.98px),(min-width: 992px) and (max-width: 1199.98px) {
.form-review-edit{-webkit-transform:translate(-20px,25%);-ms-transform:translate(-20px,25%);transform:translate(-20px,25%)}
}
@media (min-width: 640px) and (max-width: 991.98px),(min-width: 1200px) {
.form-review-edit{-webkit-transform:translate(-40px,25%);-ms-transform:translate(-40px,25%);transform:translate(-40px,25%)}
}
/*END FORM CONTROLS*/

/*START CUSTOM SCROLLBAR*/
.scrollbar-content{padding:0 10px 0 30px}
.scrollAdjust::-webkit-scrollbar{width:8px}
.scrollAdjust::-webkit-scrollbar-track{-webkit-border-radius:8px;border-radius:8px;background:#e1e1e1}
.scrollAdjust::-webkit-scrollbar-thumb{height:40px;-webkit-border-radius:8px;border-radius:8px;background:#BD2025}
.scrollAdjust{overflow:scroll;overflow-x:hidden}
/*END CUSTOM SCROLLBAR*/

/*START FORMS NOTIFICATION*/.notification{position:relative;padding-left:55px}
.notification span.icon:nth-of-type(1){content:"";display:block;position:absolute;width:40px;height:40px;border:2px solid #0066a4;border-radius:50%;left:0;top:-10px}
.notification span.icon.icon_hcontrast{border-color:#2390B9}
.notification.notification_multiline span.icon{top:auto}
.notification span.icon.icon_hcontrast:before{color:#2390B9}
.notification span.icon:before{position:absolute;left:8px;top:8px;font-size:20px;line-height:1;color:#0066a4}
.notification.success span.icon{border-color:#2C9E25}
.notification.success span.icon:before{color:#2C9E25}
.notification.warning span.icon{border-color:#E99E00}
.notification.warning .icon:before{color:#E99E00}
.notification.error{color:inherit}
.notification.error span.icon{border-color:#BD2025}
.notification.error span.icon:before{color:#BD2025}
/*END FORMS NOTIFICATION*/

/*START BUTTONS */
.btn{border-radius: 3px; font-size: 16px; font-weight: bold; padding: 11px 26px; /* 4px less on each side because of borders */ cursor: pointer; font-family: Arial, Helvetica, sans-serif;}
.btn-primary,.btn-primary:active,.btn-primary:focus{background: #E10A0A; border: 2px solid #E10A0A; color: #fff;}
.btn-primary:hover,.btn-primary:active:focus{color:#FFF;border-color:#000;background-color:#000}
.btn-primary.disabled,.btn-primary.disabled:hover,.btn-primary.disabled:focus{color:#9C9C9C;background-color:transparent;border:2px solid #9C9C9C;cursor:default}
.btn-default,.btn-default:active,.btn-default:focus{background: #E10A0A; border: 2px solid #E10A0A; color: #fff;}
.btn-default:hover,.btn-default:active:focus{color:#FFF;border-color:#000;background-color:#000}
.btn-default.disabled,.btn-default.disabled:hover,.btn-default.disabled:focus{color:#9C9C9C;background-color:transparent;border:2px solid #9C9C9C;cursor:default}
.btn-secondary,.btn-secondary:active,.btn-secondary:focus{background: transparent; border: 2px solid #000; color: #000;}
.btn-secondary:hover,.btn-secondary:active:focus{color:#E10A0A;border-color:#E10A0A;background-color:transparent}
.btn-secondary.disabled,.btn-secondary.disabled:hover,.btn-secondary.disabled:focus{color:#9C9C9C;background-color:transparent;border:2px solid #9C9C9C;cursor:default}
.btn-primary-inverted,.btn-primary-inverted:active,.btn-primary-inverted:focus{background: #FFF; border: 2px solid #FFF; color: #000;}
.btn-primary-inverted:hover,.btn-primary-inverted:active:focus{color:#FFF;border-color:#000;background-color:#000}
.btn-primary-inverted.disabled,.btn-primary-inverted.disabled:hover,.btn-primary-inverted.disabled:focus{color:#9C9C9C;background-color:transparent;border:2px solid #9C9C9C;cursor:default}
.btn-secondary-inverted,.btn-secondary-inverted:active,.btn-secondary-inverted:focus{background: transparent; border: 2px solid #FFF; color: #FFF;}
.btn-secondary-inverted:hover,.btn-secondary-inverted:active:focus{color:#000;border-color:#000;background-color:transparent}
.bgBlack .btn-secondary-inverted:hover,.bgBlack .btn-secondary-inverted:active:focus{color:#E10A0A;border-color:#E10A0A;background-color:transparent}
.btn-secondary-inverted.disabled,.btn-secondary-inverted.disabled:hover,.btn-secondary-inverted.disabled:focus{color:#9C9C9C;background-color:transparent;border:2px solid #9C9C9C;cursor:default}

/*button added for widget*/
.btn-error{color:#fff;background-color:#BD2025;border:2px solid #BD2025;font-size:18px;padding:12px 32px;text-align:center;cursor:pointer}
.btn-error:hover,.btn-error:focus{color:#fff}
/*END BUTTONS */


/*START FOOTER*/
hr{border-color:#555555}
 
  .simplified-footer {
    padding-left: 30px;
    padding-right: 30px;
  }
  .simplified-footer .footer-links li {
    border-right: 1px solid #333333;
    padding-left: 20px;
    line-height: 1;
  }
  .simplified-footer .footer-links li:first-child {
    padding-left: 0px;
  }
  .simplified-footer .footer-links li:last-child {
    border-right: none;
  }
  @media screen and (max-width: 767.98px) {
    .simplified-footer .footer-links li {
      padding: 0 !important;
      border-right: none;
      margin-bottom: 15px;
    }
    .simplified-footer .footer-links li a {
      margin: 0 !important;
    }
  }
/*END FOOTER*/

.hidden-tooltip-target-xs{display:inline-block}
.hidden-tooltip-target-lg{display:none}
.modal.modal-tooltip .modal-header{border-bottom:0}

/*START Accordion*/
/*accordion with scroll*/
.accordion-scrollable-2[aria-expanded="false"]{display:block;height:55px!important;overflow-y:auto}
.accordion-scrollable-2[aria-expanded="true"]{display:block;height:200px!important;overflow-y:auto}
.accordion-scrollable-2{display:block!important;height:55px;overflow-y:auto;-moz-transition:height .5s ease;-webkit-transition:height .5s ease;-o-transition:height .5s ease;transition:height .5s ease}
.accordion-group a:hover,.accordionButton a:hover,.accordion-group a:focus,.accordionButton a:focus{text-decoration:none}
.accordion-group a:not(.txtNoUnderline):hover div:nth-child(2),.accordionButton a:not(.txtNoUnderline):hover div:nth-child(2){text-decoration:underline}
.accordion-group a:not(.txtNoUnderline):focus div:nth-child(2),.accordionButton a:not(.txtNoUnderline):focus div:nth-child(2){text-decoration:underline}
.expand-info-toggle[aria-expanded="true"] .icon-expand-small,.expand-info-toggle[aria-expanded="false"] .icon-collapse-small{display:none}
.expand-info-toggle[aria-expanded="true"] .icon-collapse-small,.expand-info-toggle[aria-expanded="false"] .icon-expand-small{display:inline-block}

/*Simple accordion expandcollapse icon toggle*/
.accordionButton.open .icon-exapnd-outline-circled:before{content:"\e90e"}
.icon-expand-bold.icon-collapse-outline-circled:before{content:"\e96b"}

/*ShowHide Accordion Pure CSS*/
.new-list-item{display:none}
.show-list-item{display:none}
.hide-list-item:target + .show-list-item{display:inline}
.hide-list-item:target{display:none}
.hide-list-item:target ~ .new-list-item{display:block}
.read-more-toggle[aria-expanded=false] .read-more-show-text,.read-more-toggle[aria-expanded=true] .read-more-hide-text{display:inline}
.read-more-toggle[aria-expanded=false] .read-more-hide-text,.read-more-toggle[aria-expanded=true] .read-more-show-text{display:none}
.read-more-toggle[aria-expanded=false] ~ .read-more-target{display:none}
.read-more-toggle[aria-expanded=true] ~ .read-more-target{display:block}
/*End Accordion*/

/*START pagination*/
.pagination > li > a,.pagination > li > span{border-radius:50%;border:0;background:none}
.pagination > .active > a,.pagination > .active > a:focus,.pagination > .active > a:hover,.pagination > .active > span,.pagination > .active > span:focus,.pagination > .active > span:hover{background-color:#E10A0A;border-color:#E10A0A;color:#fff}
.pagination > li:first-child > a,.pagination > li:first-child > span,.pagination > li:last-child > a,.pagination > li:last-child > span{border-top-left-radius:50%;border-bottom-left-radius:50%;background:none}
.pagination > li:first-child > a:focus,.pagination > li:first-child > span:focus,.pagination > li:last-child > a:focus,.pagination > li:last-child > span:focus{background:none}
.pagination > li:first-child > a:hover,.pagination > li:first-child > span:hover,.pagination > li:last-child > a:hover,.pagination > li:last-child > span:hover{background:none}
.pagination > li > a:hover,.pagination > li > span:hover,.pagination > li > a:focus,.pagination > li > span:focus{z-index:2;color:#23527c;background-color:#eee;border-color:#ddd}
.pagination > .active > a,.pagination > .active > a:focus,.pagination > .active > a:hover,.pagination > .active > span,.pagination > .active > span:focus,.pagination > .active > span:hover{cursor:default}
.pagination > li > a,.pagination > li > span{position:relative;float:left;padding:6px 12px;line-height:1.42857143;color:#337ab7;text-decoration:none}
/*END Pagination*/

/*wide Pagination*/
.pagination-num-wide .disable a{color:#BABEC2}
.pagination-num-wide li.active a{background-color: #E10A0A;}
.pagination-num-wide li.active a:focus, .pagination-num-wide li.active a:hover{background-color: #3376ae;  cursor: pointer;}
.pagination-num-wide ul.pagination li:not(.active) a:focus, .pagination-num-wide ul.pagination li:not(.active) a:hover{background-color: #eee}
@media (min-width: 320px) and (max-width:767.98px) {
    .pagination-num-wide{margin-left:-15px;margin-right:-15px}
    .pagination-num-wide .prev-option .option-arrow{padding:15px}
    .pagination-num-wide .next-option .option-arrow{padding:15px}
    .pagination-num-wide li:not(:first-child){     padding-left: 5px;}
}
/*wide Pagination end*/

/*Price format input*/
.input-symbol:after{position:absolute;top:40px;left:40px;content:"$"}
.price-format-input.form-control{padding-left:30px}
/*END Price format input*/

/*Simple header*/
.simplified-header {
    background: #e10a0a none repeat scroll 0 0;
    box-shadow: 0 0 50px 0 rgba(0, 0, 0, 0.2);
    height: 75px;
    letter-spacing: 0.5px;
    position: relative;
    text-align: center;
    z-index: 999;
  }
  .simplified-header .page-heading {
    left: 0;
    right: 0;
    z-index: 0;
    margin: 0 auto;
    width: 75%;
  }
  .simplified-header .page-back-button,
  .simplified-header .page-right-button {
    z-index: 1;
  }
  .simplified-header .responsive-simplified-header-back {
    text-decoration: none;
  }

/*END Simplified header*/


/*Federal Bar*/

/*End federal bar*/


/*Custom selectbox*/

/*End Custom Select box*/

/*Custom hamburger select form mobile devices*/
select.custom-selection{-webkit-appearance:none;-moz-appearance:none;cursor:pointer;color:#fff;background-color:#E10A0A;padding:15px;padding-top:10px;padding-bottom:10px;height:50px;font-size:15px}
select.custom-selection::-ms-value{color:#fff}
option.tab_selection{background-color:#fff;color:#555;font-size:14px}
div.selection-box{position:relative}
div.selection-box:after{font-family:"virgin-icon";content:'\e9bd';font-size:21px;background-color:#BD2025;color:#fff;right:0;top:1px;padding:10px 18px;height:48px;position:absolute;pointer-events:none}
div.selection-box:before{content:'';position:absolute;pointer-events:none;display:block}
div.selection-box.search-arrow-down:after{content:"\e9bd"}
/*END custom hamburger select form mobile devices END*/
 
/*Start 5050 Columns content containers*/

/*Start 5050 Columns contemt containers*/

/*Start Search Bar */
/* Search Form */

/* search bar autocomplete*/

/*END  Search Bar */

/*Start  Custom radio button and checkboxes*/
.graphical_ctrl{position:relative;padding-left:35px}
.graphical_ctrl input{position:absolute;width:48px;z-index:-1;height:48px;opacity:0;top:-16px;left:-9px}
.ctrl_element{position:absolute;top:-3px;left:0;height:24px;width:24px;background:#fff; box-shadow: inset 0 0px 3px 0 rgba(0,0,0,0.2);border:1px solid #ccc}
.ctrl_radioBtn .ctrl_element{border-radius:50%}
.graphical_ctrl input:checked:focus ~ .ctrl_element{outline-width:1px;outline-style:dashed;outline-color:#4d90fe;box-shadow:0 0 3px 2px rgba(178,209,228,1)}
.graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element{outline-width:1px;outline-style:dashed;outline-color:#4d90fe}
.graphical_ctrl input:checked ~ .ctrl_element{background:#BD2025;border:1px solid #BD2025}
.graphical_ctrl input:checked:focus ~ .ctrl_element{background:#BD2025}
.graphical_ctrl input:disabled ~ .ctrl_element{background:#e6e6e6;opacity:.6;border:1px solid #e6e6e6;pointer-events:none}
.ctrl_element:after{content:'';position:absolute;display:none}
.graphical_ctrl input:checked ~ .ctrl_element:after{display:block}
.graphical_ctrl_checkbox .ctrl_element:after{left:7px;top:3px;width:6px;height:11px;border:solid #fff;border-width:0 2px 2px 0;display:inline-block;-webkit-transform:rotate(45deg);-moz-transform:rotate(45deg);-o-transform:rotate(45deg);transform:rotate(45deg)}
.graphical_ctrl_checkbox input:disabled ~ .ctrl_element:after{border-color:#7b7b7b}
.ctrl_radioBtn .ctrl_element:after{left:5px;top:5px;height:12px;width:12px;border-radius:50%;background:#fff}
.ctrl_radioBtn input:disabled ~ .ctrl_element:after{background:#7b7b7b}
.ctrl_lg .graphical_ctrl{padding-left:40px}
.ctrl_lg .ctrl_element{height:30px;width:30px;top:-5px}
.ctrl_lg .ctrl_radioBtn .ctrl_element:after{left:6px;top:6px;height:16px;width:16px}
.error-ctrl .ctrl_element{border:1px solid #BD2025}
.error-ctrl .graphical_ctrl input:checked ~ .ctrl_element{background-color:#BD2025;border:1px solid #BD2025}
.error-ctrl .error_radio_lg .ctrl_radioBtn .ctrl_element:after{top:6px;left:6px}
.error-ctrl .ctrl_radioBtn .ctrl_element:after{top:5px;left:5px}
.chk_radius{border-radius:3px}
.ctrl_lg .graphical_ctrl_checkbox .ctrl_element:after{height:15px;left:10px;top:4px;width:9px}
/*On dark Background*/
label.on-dark-bg{color:#fff}
.graphical_ctrl_checkbox.on-dark-bg .ctrl_element:after{border:solid #fff;border-width:0 2px 2px 0;display:inline-block;-webkit-transform:rotate(45deg);-moz-transform:rotate(45deg);-o-transform:rotate(45deg);transform:rotate(45deg)}
.graphical_ctrl.on-dark-bg input:checked ~ .ctrl_element:after{border:solid #BD2025;border-width:0 2px 2px 0}
.graphical_ctrl.on-dark-bg input:checked ~ .ctrl_element{background:#fff;border:1px solid #BD2025}
.selected-rectangle {
    font-weight: bold;
    color: #BD2025;
} 
@media (max-width: 991.98px) {    
    /*for custom radio button and checkboxs starts*/
    /*graphical_ctrl{padding-left:35px}*/
.ctrl_element{height:24px;width:24px;top:-3px}
.error-ctrl .ctrl_radioBtn .ctrl_element:after,.error-ctrl .error_radio_lg .ctrl_radioBtn .ctrl_element:after{top:6px;left:6px}
.ctrl_radioBtn .ctrl_element:after{left:5px;top:5px;height:12px;width:12px}
.graphical_ctrl_checkbox .ctrl_element:after{left:11px;top:6px;width:8px;height:14px}
    /*for custom radio button and checkboxs ends*/
}

@media (min-width: 320px) and (max-width: 767.98px) {
    .ctrl_radioBtn .ctrl_element {
        top: 14px;
        left: 15px;
    }
     /*boxes for radio buttons*/
    .rectangle {
        padding: 15px 50px;
        border: 1px solid #D7D7D7;
        background-color: #FFFFFF;
        box-shadow: 0 2px 3px 0 rgba(0,0,0,0.1);
    }

    .selected-rectangle {
        border: 2px solid #E10A0A;
    }
}
/*END  Custom radio button and checkboxes*/


/*LOADER*/
.loading-indicator-circle{display:inline-block;width:37px;height:37px;margin-right:10px;vertical-align:middle;-webkit-animation:spin 1.1s linear infinite;-moz-animation:spin 1.1s linear infinite;animation:spin 1.1s linear infinite}
@-moz-keyframes spin {
100%{-moz-transform:rotate(360deg)}
}
@-webkit-keyframes spin {
100%{-webkit-transform:rotate(360deg)}
}
@keyframes spin {
100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}
}
.loader-fixed{width:300px;left:50%;margin-left:-150px;position:fixed;top:45%;padding:20px;z-index:99999;-webkit-box-shadow:0 0 40px rgba(0,0,0,0.4);-moz-box-shadow:0 0 40px rgba(0,0,0,0.4);box-shadow:0 0 40px rgba(0,0,0,0.4)}
.page-loader-msg{display:block;float:right;align-self:center;width:210px}
.loaderOverlayBackground{background:#000 none repeat scroll 0 0;display:none;height:100%;left:0;opacity:.7;position:fixed;top:0;width:100%;z-index:55555}
.pagLoader{display:none}
.loader-container .centerElement{margin:0 auto;display:table}
.loading-mbm-spinner{background-image:url(../../content/img/loader_inner.png);background-repeat:no-repeat;display:inline-block;width:70px;height:70px;vertical-align:middle;-webkit-animation:spin 2s linear infinite;-moz-animation:spin 2s linear infinite;animation:spin 2s linear infinite}
.loaderMBMBG{background-image:url(../../content/img/loader_inner_outer.png);background-repeat:no-repeat;display:inline-block;width:75px;height:75px;top:17px;left:113px;position:absolute}
.mbmLoaderFix{opacity:.9;width:300px;left:50%;margin-left:-150px;position:fixed;top:45%;padding:20px;z-index:99999}
/* End of loader centered within a container */

/* SMALL Loader centered within a container */
.loader-fixed.small-loader{text-align:center;width:auto;margin-left:-50px;max-width:120px;padding:20px 10px;box-shadow:none}
.loading-indicator-circle.square45{width:45px;height:45px}
/* End of SMALL Loader centered within a container */
/*END  LOADER*/

/*BRF Tabs*/
ul.tabs{display:table;table-layout:fixed;padding-left:0;margin-bottom:0;list-style:none}
ul.tabs li.active_tabs{background-color:#E10A0A;z-index:2}
ul.tabs li.active_tabs label{position:relative;top:-5px}
ul.tabs li.active_tabs .active-tab-top{background-color:#E10A0A;display:block;height:10px;left:0;opacity:1;position:absolute;top:-10px;width:100%;z-index:-1}
ul.tabs li.active_tabs::before{background:rgba(0,0,0,0) linear-gradient(94deg,#04225e 45%,rgba(4,34,94,0) 50%,rgba(4,34,94,0) 100%);content:"";height:100%;opacity:1;position:absolute;right:-10px;top:0;width:10px;position:absolute;top:0}
ul.tabs li.active_tabs.last-active-tab::before{background:rgba(0,0,0,0) linear-gradient(95deg,#04225e 40%,rgba(4,34,94,0) 35%,rgba(4,34,94,0) 100%);opacity:.35}
.active_tabs::after{content:"";position:absolute;bottom:-15px;border-width:15px 15px 0;border-style:solid;border-color:#E10A0A transparent;display:block;width:0;left:0;right:0;margin-left:auto;margin-right:auto}
ul.tabs li{border-right:1px solid #092442;text-align:center}
ul.tabs li.last-active-tab{border-right:0 solid transparent}
ul.tabs li{cursor:pointer;padding:20px;background-color:#BD2025;color:#fff;font-size:16px;display:table-cell;vertical-align:middle;float:none;position:relative}
ul.tabs li > i{font-size:55px;vertical-align:middle;margin:0 auto;display:table}
ul.tabs.tabs_vertical li a{color:#c2cedf;word-break:normal}
ul.tabs.tabs_vertical li.active_tabs a{color:#fff}
ul.tabs.tabs_vertical li{display:block;padding:20px 40px;text-align:left;font-weight:400;font-size:24px;letter-spacing:-.6px;line-height:1.4;margin:0;font-family:"bellslim_mediumregular",Helvetica,Arial,sans-serif;border-bottom:1px solid #01215e}
ul.tabs.tabs_vertical li:before,ul.tabs.tabs_vertical li.active_tabs:before{font-family:'virgin-icon';color:#fff;content:"\e012";position:absolute;right:20px;top:50%;-webkit-transform:translateY(-50%);-ms-transform:translateY(-50%);transform:translateY(-50%);font-size:18px;background:none;height:inherit;width:inherit}
ul.tabs.tabs_vertical li.active_tabs.active_tabs::after{border-color:transparent}
.tabH{display:inline-block}
.tab_container{border-top:none;clear:both}
.tab-content{display:none}
.tab-content.first{display:block}

/*BRF Tabs small*/
ul.tabs-small li{padding:0;height:65px;font-size:15px}
@media (min-width: 768px) {
ul.tabs-small li.active_tabs,ul.tabs-small li.active_tabs .active-tab-top{padding-bottom:10px}
}


/* Start of Tabs 2  with accessibility */
.tabs2 .tabs{display:table;table-layout:fixed;padding-left:0;margin-bottom:0;list-style:none}
.tabs2 #tabs2-without-icon.tabs.tabs-small [role="tab"]{padding:0;height:65px;font-size:15px}
.tabs2 .tabs .active_tabs[role="tab"]::before{background:rgba(0,0,0,0) linear-gradient(94deg,#04225e 45%,rgba(4,34,94,0) 50%,rgba(4,34,94,0) 100%);content:"";height:100%;opacity:1;position:absolute;right:-10px;top:0;width:10px}
.tabs2 .tabs .active_tabs[role="tab"] .active-tab-top{background-color:#E10A0A;display:block;height:10px;left:0;opacity:1;position:absolute;top:-10px;width:100%;z-index:-1}
.tabs2 .tabs [role="tab"]{cursor:pointer;padding:20px;background-color:#BD2025;color:#fff;font-size:16px;display:initial;vertical-align:bottom;float:none;position:relative;border:none;flex:1;border-right:1px solid #092442;text-align:center}
.tabs2 .tabs [role="tab"] i{font-size:55px;vertical-align:middle;margin:0 auto;display:table}
.tabs2 .tabs .active_tabs{background-color:#E10A0A;z-index:2}
/* End of Tabs 2 */

/*Alert Message Box*/
.alert-msg-middle {
    width: 85%
}
/*END Alert Message Box*/

/*Features Checkboxes*/
.box-shadow-1 {
    box-shadow: 0 2px 3px 0 rgba(0,0,0,0.1);
    -webkit-box-shadow: 0 2px 3px 0 rgba(0,0,0,0.1);
}

.check-select-cta {
    padding-left: 35px
}

.checkbox-selection ~ .ctrl_element {
    top: -5px
}

@media (max-width: 991.98px) {
    .check-select-cta {
        padding-left: 45px
    }
}
/*END Features Checkboxes*/



/*Message Boxes Start*/
.message-box-wrapper {
    position: fixed;
    top: 130px;
    left: 50%;
    z-index: 999;
}

.message-box {
    position: relative;
    left: -50%;
    background: #fff;
    padding: 15px 15px 0px 15px;
    border: 0;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    border-bottom: 5px solid #fff;
    -webkit-box-shadow: 0px 0px 60px 0px rgba(0,0,0,0.3);
    -moz-box-shadow: 0px 0px 60px 0px rgba(0,0,0,0.3);
    box-shadow: 0px 0px 60px 0px rgba(0,0,0,0.3);
    min-width: 160px;
    max-width: 600px;
}

.message-box-warning {
    border-bottom: 5px solid #E99E00;
}

.message-box-success {
    border-bottom: 5px solid #2C9E25;
}

.message-box-close-button {
    position: absolute;
    top: 15px;
    margin-top: -5px;
    margin-right: -5px;
    right: 15px;
    font-size: 9px;
}

.message-box-icon {
    position: absolute;
}

.message-box-text {
    word-wrap: break-word;
    padding-left: 35px;
    padding-right: 10px;
    padding-bottom: 5px;
    padding-top: 5px;
}

@media (max-width: 767.98px) {
    .message-box-wrapper {
        margin-right: -100px;
    }
}
/*Message Boxes END*/


/* media queries keep at the bottom*/
@media screen and (min-width: 992px) and (max-width: 999.98px) {
    .back-to-top, .scrollToTop.mobile {
        display: none
    }
}

@media screen and (max-width:999.98px) {
    .virginUltra-sm {
        font-family: "BigUltramagneticBold", Arial, Helvetica, sans-serif;
        font-weight: normal
    }
    
    .virginUltraReg-sm {
        font-family: "VMUltramagneticNormalRegular", Arial, Helvetica, sans-serif;
        font-weight: normal
    }
}

@media screen and (max-width:991.98px) {
    .variableWidthButton {
        float: left;
        width: 100%
    }

    .modal:before, .modal-dialog {
        vertical-align: middle
    }

    .modal.modal-tooltip {
        padding-top: 0px;
    }
}

@media screen and (max-width:767.98px) {
    .modal.modal-tooltip {
        bottom: unset
    }

        .modal.modal-tooltip .tooltip-dialog {
            margin: auto 20px;
            -webkit-transform: translate(0%, -50%);
            transform: translate(0%, -50%);
            position: relative;
            bottom: 50%;
        }

            .modal.modal-tooltip .tooltip-dialog .modal-header {
                padding-bottom: 10px
            }

            .modal.modal-tooltip .tooltip-dialog button.close:focus {
                outline: 1px;
                outline-color: #E10A0A;
            }

            .modal.modal-tooltip .tooltip-dialog .close {
                padding: 15px;
                font-size: 12px
            }

    .modal-header-blue {
        height: 60px;
        background-color: #E10A0A;
        padding-top: 22px
    }

    .modal-header-gray {
        height: 60px;
        background-color: #e1e1e1;
        padding-top: 22px
    }

    .sans-serif-xs {
        font-family: Arial, Helvetica, sans-serif;
        letter-spacing: 0
    }

    .flipFloatLR {
        float: right
    }

    .modal:before, .modal-dialog {
        vertical-align: middle
    }

    .modal.modal-tooltip {
        padding-top: 0px;
    }

    .mobile-progressbar {
        display: block
    }

    .modal .modal-dialog.modal-lg.virgin-modal-lg {
        margin: 0px;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100%;
        background-color: #f0f0f0;
        width: 100%
    }

    .modal .modal-dialog.modal-lg-2.virgin-modal-lg {
        background-color: #fff
    }

    .modal .modal-dialog.center-screen.modal-md.virgin-modal-md {
        margin: 0 auto;
        position: relative;
        width: 92%;
    }

    .modal .modal-dialog.modal-md.virgin-modal-md {
        margin: 0;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        top: auto;
        height: auto;
        background-color: transparent;
    }

    .modal-dialog.modal-md.virgin-modal-md.bgGray19 {
        background-color: #f4f4f4
    }

    .virgin-modal-lg .modal-content, .virgin-modal-md .modal-content {
        border-radius: 10px;
        -webkit-box-shadow: 0 0 0px rgba(0,0,0,0);
        -moz-box-shadow: 0 0 0px rgba(0,0,0,0);
        box-shadow: 0 0 0px rgba(0,0,0,0);
    }

    .hidden-tooltip-target-xs {
        display: none
    }

    .hidden-tooltip-target-lg {
        display: inline-block
    }

    .primary-cta {
        display: none
    }
}

@media (max-width:639.98px) {
    .form-control-select-box:after {
        font-size: 16px;
        padding: 11px 6px 10px 0;
    }

    .mobile-progressbar {
        display: block
    }
}

@media (min-width:640px) {
    .footerIcon, .footerList a, .footerList span {
        display: inline-block;
        padding-right: 25px
    }

    .footerIcon {
        float: right
    }
}

@media screen and (max-width:640.98px) {
    .card-body {
        padding: 20px
    }
}

@media (min-width:520px) {
    .footerIcon, .footerList a, .footerList span {
        display: block;
        text-align: center;
        padding: 0 0 15px 0;
        margin: auto
    }

    .footerIcon {
        text-align: center;
        float: none
    }

    .mobile-progressbar {
        width: 100% !important
    }

    .secondary-cta-hidden-xs {
        display: block
    }
}

@media screen and (max-width:520.98px) {
    .card-body {
        padding: 20px 20px
    }

    .variableWidthButton {
        float: none;
        width: 100%
    }

    .modal.modal-tooltip {
        position: fixed;
        padding-top: 0px;
        width: 100%;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%)
    }

    .modal-scroll-area {
        padding: 0
    }

    .secondary-cta-hidden-xs {
        display: none !important
    }

    .primary-cta {
        display: block !important
    }
}

@media (max-width:519.98px) {
    .icon-circle-large:before {
        font-size: 52px
    }

    .footerIcon, .footerList a, .footerList span {
        display: block;
        text-align: center;
        padding: 0 0 10px 0;
        margin: auto
    }

    .footerIcon {
        text-align: center;
        float: none
    }

    .hidden-xs {
        display: none
    }

    .mobile-progressbar {
        display: block
    }
}

@media (min-width:992px) {
    .footerIcon, .footerList a, .footerList span {
        display: inline-block;
        padding-right: 25px
    }

    .footerIcon {
        float: right
    }

    .modal-scroll-area {
        padding: 30px 40px 0 30px
    }
}

@media (min-width:1200px) {
    .variableWidthInput {
        width: 160px
    }

    .variableWidthInput2 {
        width: 160px
    }

    .flipFloatLR {
        float: left
    }

    .flipTxtAlignRL {
        text-align: right
    }

    .modal .modal-lg.virgin-modal-lg {
        width: 1190px;
        margin-left: -9px
    }

    .modal-scroll-area {
        padding: 30px 40px 0 30px
    }
}

@media (max-width:1199.98px) {
    .modal.modal-tooltip {
        position: fixed;
        width: 100%;
        height: 100%;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        overflow: hidden;
    }
}

@media (min-width:1200px) {
    .container {
        padding-right: 0px;
        padding-left: 0px
    }

    .footerIcon, .footerList a, .footerList span {
        display: inline-block;
        padding-right: 25px
    }

    .footerIcon {
        float: right
    }

    .modal .modal-lg.virgin-modal-lg {
        width: 1200px;
        margin-left: -9px;
        max-width: 100%
    }

    .modal-scroll-area {
        padding: 30px 40px 0 30px
    }
}

@media (max-width:639.98px) {
    .vPadding20-left-xs {
        padding-left: 20px;
    }
}

/* Start For Datepicker*/
.ui-widget-header {
    background: #E10A0A !important;
}

.ui-widget-header {
    color: #fff !important;
    font-weight: normal !important;
}

    .ui-widget-header a {
        color: #ffffff !important;
    }

.icon-reversed {
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
    display: inline-block;
    margin-left: 3px;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default, .ui-button, html .ui-button.ui-state-disabled:hover, html .ui-button.ui-state-disabled:active {
    background: #fff;
    border: 0;
}

    .ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active, a.ui-button:active, .ui-button:active, .ui-button.ui-state-active:hover {
        background: #E10A0A;
        color: #fff;
    }

.ui-datepicker .ui-datepicker-prev {
    /*top: 4px !important;*/
    top: 50% !important;
    transform: translateY(-50%) !important;
    left: 8px !important;
    cursor: pointer;
}

.ui-datepicker .ui-datepicker-next {
    /*top: 4px !important;*/
    top: 50% !important;
    transform: translateY(-50%) !important;
    cursor: pointer;
}

.ui-datepicker .ui-datepicker-next-hover {
    right: 2px !important;
}

.ui-state-hover, .ui-widget-content .ui-state-hover {
    background: #E10A0A !important;
    font-weight: normal;
    color: #ffffff !important;
}

.ui-datepicker a:focus, .ui-datepicker a:hover {
    text-decoration: none !important;
}

.ui-datepicker th {
    color: #555;
    background-color: #f5f5f5;
    border-bottom: 0px !important;
    font-weight: normal !important;
    padding: .1em .3em !important;
}

.tooltip-datepicker .tooltip-inner {
    padding: 1px !important;
}

.ui-datepicker.ui-widget.ui-widget-content {
    border: 1px solid #D7D7D7 !important;
    box-shadow: 0 0 21px 0 rgba(0,0,0,0.2);
    background: #fff !important;
}

.ui-datepicker .ui-datepicker-header {
    border-radius: 0px !important;
}

.ui-datepicker td span, .ui-datepicker td a {
    text-align: center;
}

.ui-datepicker .ui-datepicker-next {
    /*right: 0px !important;*/
    right: 8px !important;
}

.ui-datepicker-calendar thead tr th:first-child, .ui-datepicker-calendar tbody {
    padding-left: 15px !important;
}

    .ui-datepicker-calendar tbody tr td:first-child {
        padding-left: 15px !important;
    }

    .ui-datepicker-calendar tbody tr td:last-child {
        padding-right: 15px !important;
    }

.ui-datepicker-calendar thead tr th:last-child, .ui-datepicker-calendar tbody {
    padding-right: 15px !important;
}

.ui-datepicker {
    padding: 0px !important;
}

.datePicker {
    padding-top: 27px;
}

.ui-datepicker table {
    background-color: #fff !important;
    margin: 0;
    padding: 4px 0;
}

.ui-icon, .ui-widget-content .ui-datepicker-next .ui-icon, .ui-widget-content .ui-datepicker-prev .ui-icon {
    background-image: none;
    color: transparent;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.ui-datepicker .ui-datepicker-prev span, .ui-datepicker .ui-datepicker-next span {
    /*margin-left: -14px;*/
    margin-left: 0;
    left: 50%;
    transform: translateX(-50%);
}

.ui-datepicker .ui-datepicker-prev span {
    margin-left: -2px;
}

select.ui-datepicker-month,
select.ui-datepicker-year {
    background: #fff /*#dcdcdc*/;
    /* custom dropdown arrow styles below */
    padding-bottom: 9px;
    padding-top: 11px;
    height: 44px;
    line-height: 18px;
    font-size: 14px;
    box-shadow: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
    padding-right: 20px;
    margin: 1px !important;
}

    select.ui-datepicker-month:focus,
    select.ui-datepicker-year:focus {
        border-color: #66afe9;
        outline: 0;
        -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), inset 0 0 3px rgba(102,175,233,.6);
        box-shadow: inset 0 1px 1px rgba(0,0,0,.075), inset 0 0 3px rgba(102,175,233,.6);
    }

    /* custom dropdown arrow styles below */
    select.ui-datepicker-month + span,
    select.ui-datepicker-year + span {
        font-family: "virgin-icon";
        font-size: 18px;
        background-color: transparent;
        top: 50%;
        height: auto;
        position: absolute;
        display: block; /* need to be block level for pointer-events none to work on IE and Edge */
        pointer-events: none;
        color: #E10A0A;
        padding: 0;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    select.ui-datepicker-month + span {
        left: 50%;
        transform: translate(-20px, -50%);
    }

    select.ui-datepicker-year + span {
        left: 90%;
        transform: translate(-40px, -50%);
    }

.ui-datepicker-current.ui-state-default {
    opacity: 1;
}

.ui-state-hover,
.ui-widget-content .ui-state-hover,
.ui-widget-header .ui-state-hover,
.ui-state-focus,
.ui-widget-content .ui-state-focus,
.ui-widget-header .ui-state-focus,
.ui-button:hover,
.ui-button:focus {
    border: none;
}

/*Next Prev icon*/
.ui-datepicker .ui-icon {
    text-indent: inherit;
}

.ui-icon::after, .ui-widget-content .ui-datepicker-next .ui-icon::after {
    font-family: 'virgin-icon' !important;
    content: "\e9a9";
    font-size: 14px;
    background-color: transparent;
    color: #fff;
    right: 0px;
    top: -2px;
    padding: 0;
    height: 24px;
    position: absolute;
    pointer-events: none;
}

.ui-icon::after, .ui-widget-content .ui-datepicker-prev .ui-icon::after {
    font-family: 'virgin-icon' !important;
    content: "\e9be";
    font-size: 14px;
    background-color: transparent;
    color: #fff;
    right: 0px;
    top: -2px;
    padding: 0;
    height: 24px;
    position: absolute;
    pointer-events: none;
}

/*Calendar icon*/
.date-picker {padding-bottom: 9px;padding-top: 11px;padding-left: 15px;height: 44px;line-height: 18px;font-size: 14px}

.ui-datepicker-trigger {
    background: none;
    border: none;
    position: absolute;
    top: 50%;
    transform: translateY(10%);
    width: 24px;
    height: 24px;
    right: 30px;
}

.date-picker-box.margin-30-right .ui-datepicker-trigger {
    margin-right: 22px;
}

.date-picker-box .ui-datepicker-trigger img {
    display: none;
}

.ui-datepicker-trigger::after {
    font-family: "virgin-icon";
    content: '\e91c';
    font-size: 24px;
    background-color: transparent;
    color: #E10A0A;
    right: 0px;
    top: 0;
    height: 24px;
    position: absolute;
    transform: translateY(-40%)
}

.date-picker-box input[disabled] ~ .ui-datepicker-trigger img, .date-picker-box input::-ms-clear {
    display: none;
}

.date-picker-box input[disabled] ~ .ui-datepicker-trigger::after {
    color: transparent;
    content: '';
}
/* END For Datepicker*/

/* START For Video Gallery Component*/
.video-group span {
    display: block;
    padding: 10px;
    font-size: 12px;
}

.video-group .iframe-container {
    position: relative;
    height: 120px;
    background-position: center !important;
    background-size: cover !important;
    background-repeat: no-repeat;
    width: 100%;
    cursor: pointer;
}

.video-group div.iframe-container .play-icon {
    background-repeat: no-repeat;
    width: 60px;
    height: 60px;
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 48px;
    color: #000;
    z-index: 2;
    opacity: 0.8;
    -webkit-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
}

.video-group img,
.video-group iframe {
    width: 100%;
    height: 100%;
    display: block;
    position: relative;
}

.video-group img {
    z-index: 1;
}

.video-group iframe {
    z-index: 3;
}

.video-main #main-iframe {
    width: 100%;
    height: 550px;
    background-color: #000;
}

.video-tag {
    position: absolute;
    bottom: 0;
    left: 0;
    padding: 1px;
    background: #E10A0A;
    z-index: 1;
    color: white;
}

.video-group div.iframe-container.active::before {
    content: '';
    position: absolute;
    z-index: 2;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    box-shadow: inset 0 0 0 5px #E10A0A;
}
/* END For Video Gallery Component*/


/*Flex boxes automated padding control 2up, 3up, 4up, 5up, and 6up*/
.flex-2up, .flex-3up, .flex-4up {
    margin-left: -15px;
    margin-right: -15px
}

.col-lg-5th {
    width: 20%;
    float: left
}

@media (max-width:767.98px) {
    .flex-5up .col-lg-5th.col-xs-12 {
        width: 100%;
        padding-left: 0;
        padding-right: 0;
    }
   
}

@media (max-width:991.98px) {
    .col-lg-5th.col-sm-6 {
        width: 50%;
    }
    .flex-6up .col-md-2:nth-of-type(2n+1) {
        padding-left: 0
    }
     .flex-6up .col-md-2:nth-of-type(2n) {
        padding-right: 0
    }
}

@media (min-width:992px) {
    .flex-5up .col-lg-5th:nth-of-type(5n+1) {
        padding-left: 0
    }

    .flex-5up .col-lg-5th:nth-of-type(5n) {
        padding-right: 0
    }
    
    .flex-6up .col-lg-2:nth-of-type(6n+1) {
        padding-left: 0
    }
    
    .flex-6up .col-lg-2:nth-of-type(6n) {
        padding-right: 0
    }
}
/*END Flex boxes automated padding control 2up, 3up, 4up, 5up, and 6up*/

/*to overide out of the box bootstrap*/
.modal-backdrop {
    z-index: 1100
}

@media (max-width:519.98px) { 
}

@media (max-width:520.98px) { 
}

@media (min-width:520px) {
    .container {
        width: 480px
    }
}

@media (min-width:768px) {
    .container {
        width: 600px
    }

    .modal-backdrop.in {
        opacity: 0.5
    }
}

@media (min-width:992px) {
    .container {
        width: 980px
    }

    .modal-backdrop.in {
        opacity: 0.5
    }
}

@media (min-width:1200px) {
    .container {
        width: 980px
    }

    .modal-backdrop.in {
        opacity: 0.5
    }
}

@media (min-width:1240px) {
    .container {
        width: 1200px
    }

    .modal-backdrop.in {
        opacity: 0.5
    }
}
/*Bridges the gap between Bootsrap and Bell media quieries*/
@media screen and (min-width: 992px) and (max-width: 999.98px) {
}

/*START Assets bell packages*/



/*Internet Packages*/


/*Image and Text Box*/

.text-and-image > [class^="col-"], .text-and-image > [class*=" col-"] {
    display: table-cell;
    float: none;
    vertical-align: middle;
}

/*Channel Logo Tabs*/



/*picture-with-content*/


/*Asset bell package generic*/



/*END Assets bell packages*/

/*START Bell TV Receivers*/



/*END Bell TV Receivers*/

/*START Responsive Tables*/


/* Icons */
.virgin-icon {
    font-style: normal;
    speak: none;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.virgin-icon:before {
    font-family: 'virgin-icons';
    position: relative;
}

.icon-4G:before {
    content: "\e904";
}

.icon-911:before {
    content: "\e905";
}

.icon-add_ons:before {
    content: "\e906";
}

.icon-android:before {
    content: "\e907";
}

.icon-angel:before {
    content: "\e908";
}

.icon-antenna:before {
    content: "\e909";
}

.icon-apple:before {
    content: "\e90a";
}

.icon-atom:before {
    content: "\e90b";
}

.icon-bag:before {
    content: "\e90c";
}

.icon-battery:before {
    content: "\e90d";
}

.icon-battery_charged:before {
    content: "\e90e";
}

.icon-bbm:before {
    content: "\e90f";
}

.icon-beats:before {
    content: "\e910";
}

.icon-big_X:before {
    content: "\e911";
}

.icon-bill:before {
    content: "\e912";
}

.icon-bill1:before {
    content: "\e913";
}

.icon-bill2:before {
    content: "\e914";
}

.icon-bill3:before {
    content: "\e915";
}

.icon-blackberry:before {
    content: "\e916";
}

.icon-bluetooth:before {
    content: "\e917";
}

.icon-bridge:before {
    content: "\e918";
}

.icon-brokescreen:before {
    content: "\e919";
}

.icon-bulb:before {
    content: "\e91a";
}

.icon-calculator:before {
    content: "\e91b";
}

.icon-calendar:before {
    content: "\e91c";
}

.icon-camera:before {
    content: "\e91d";
}

.icon-canada_leaf:before {
    content: "\e91e";
}

.icon-canada_shop:before {
    content: "\e91f";
}

.icon-card:before {
    content: "\e920";
}

.icon-charger:before {
    content: "\e921";
}

.icon-checkmark:before {
    content: "\e922";
}

.icon-chip:before {
    content: "\e923";
}

.icon-clavardage_endirect:before {
    content: "\e924";
}

.icon-connectivity:before {
    content: "\e925";
}

.icon-controller:before {
    content: "\e926";
}

.icon-dashboard:before {
    content: "\e927";
}

.icon-dashboard2:before {
    content: "\e928";
}

.icon-down_arrow:before {
    content: "\e929";
}

.icon-drink:before {
    content: "\e92a";
}

.icon-earphones:before {
    content: "\e92b";
}

.icon-edit:before {
    content: "\e92c";
}

.icon-email:before {
    content: "\e92d";
}

.icon-expand_m:before {
    content: "\e92e";
}

.icon-eye:before {
    content: "\e92f";
}

.icon-eye2:before {
    content: "\e930";
}

.icon-eye3:before {
    content: "\e931";
}

.icon-flash:before {
    content: "\e932";
}

.icon-funky:before {
    content: "\e933";
}

.icon-gaming:before {
    content: "\e934";
}

.icon-glasses:before {
    content: "\e935";
}

.icon-graph:before {
    content: "\e936";
}

.icon-graph2:before {
    content: "\e937";
}

.icon-hand:before {
    content: "\e938";
}

.icon-hanger:before {
    content: "\e939";
}

.icon-HD:before {
    content: "\e93a";
}

.icon-headphones:before {
    content: "\e93b";
}

.icon-heart:before {
    content: "\e93c";
}

.icon-hearts:before {
    content: "\e93d";
}

.icon-home:before {
    content: "\e93e";
}

.icon-home_phone:before {
    content: "\e93f";
}

.icon-icon_1:before {
    content: "\e940";
}

.icon-icon_2:before {
    content: "\e941";
}

.icon-icon_3:before {
    content: "\e942";
}

.icon-ios:before {
    content: "\e943";
}

.icon-key:before {
    content: "\e944";
}

.icon-keyboard:before {
    content: "\e945";
}

.icon-laugh:before {
    content: "\e946";
}

.icon-link:before {
    content: "\e947";
}

.icon-link_1:before {
    content: "\e948";
}

.icon-lips:before {
    content: "\e949";
}

.icon-lips2:before {
    content: "\e94a";
}

.icon-live_chat:before {
    content: "\e94b";
}

.icon-lock:before {
    content: "\e94c";
}

.icon-love:before {
    content: "\e94d";
}

.icon-LTE:before {
    content: "\e94e";
}

.icon-magic:before {
    content: "\e94f";
}

.icon-messaging:before {
    content: "\e950";
}

.icon-mobile_account:before {
    content: "\e951";
}

.icon-mobile_fire:before {
    content: "\e952";
}

.icon-modem:before {
    content: "\e953";
}

.icon-modem_wifi:before {
    content: "\e954";
}

.icon-modem3:before {
    content: "\e955";
}

.icon-modem2:before {
    content: "\e956";
}

.icon-music:before {
    content: "\e957";
}

.icon-network:before {
    content: "\e958";
}

.icon-network2:before {
    content: "\e959";
}

.icon-network3:before {
    content: "\e95a";
}

.icon-object:before {
    content: "\e95b";
}

.icon-object_2:before {
    content: "\e95c";
}

.icon-object_3:before {
    content: "\e95d";
}

.icon-passport:before {
    content: "\e95e";
}

.icon-password:before {
    content: "\e95f";
}

.icon-pen:before {
    content: "\e960";
}

.icon-phone:before {
    content: "\e961";
}

.icon-phone_1:before {
    content: "\e962";
}

.icon-phone_case:before {
    content: "\e963";
}

.icon-phone_charging:before {
    content: "\e964";
}

.icon-phone2:before {
    content: "\e965";
}

.icon-phone3:before {
    content: "\e966";
}

.icon-pin:before {
    content: "\e967";
}

.icon-plus:before {
    content: "\e968";
}

.icon-Print:before {
    content: "\e969";
}

.icon-profile:before {
    content: "\e96a";
}

.icon-question:before {
    content: "\e96b";
}

.icon-RAM:before {
    content: "\e96c";
}

.icon-recording:before {
    content: "\e96d";
}

.icon-resizescreen:before {
    content: "\e96e";
}

.icon-router:before {
    content: "\e96f";
}

.icon-run:before {
    content: "\e970";
}

.icon-sad:before {
    content: "\e971";
}

.icon-savings:before {
    content: "\e972";
}

.icon-savings1:before {
    content: "\e973";
}

.icon-science:before {
    content: "\e974";
}

.icon-science_2:before {
    content: "\e975";
}

.icon-screaming:before {
    content: "\e976";
}

.icon-screens:before {
    content: "\e977";
}

.icon-search:before {
    content: "\e978";
}

.icon-settings:before {
    content: "\e979";
}

.icon-shining_love:before {
    content: "\e97a";
}

.icon-sick:before {
    content: "\e97b";
}

.icon-SIM:before {
    content: "\e97c";
}

.icon-sleepy:before {
    content: "\e97d";
}

.icon-smile:before {
    content: "\e97e";
}

.icon-smile2:before {
    content: "\e97f";
}

.icon-snapchat:before {
    content: "\e980";
}

.icon-snowflake:before {
    content: "\e981";
}

.icon-snowflake2:before {
    content: "\e982";
}

.icon-star:before {
    content: "\e983";
}

.icon-star_moving:before {
    content: "\e984";
}

.icon-star2:before {
    content: "\e985";
}

.icon-star3:before {
    content: "\e986";
}

.icon-strength:before {
    content: "\e987";
}

.icon-tablet:before {
    content: "\e988";
}

.icon-TL:before {
    content: "\e989";
}

.icon-text:before {
    content: "\e98a";
}

.icon-text_canada:before {
    content: "\e98b";
}

.icon-text_internet:before {
    content: "\e98c";
}

.icon-text_world:before {
    content: "\e98d";
}

.icon-text_world1:before {
    content: "\e98e";
}

.icon-tool:before {
    content: "\e98f";
}

.icon-tools:before {
    content: "\e990";
}

.icon-touch_screen:before {
    content: "\e991";
}

.icon-touch_screen2:before {
    content: "\e992";
}

.icon-trash:before {
    content: "\e993";
}

.icon-travelling:before {
    content: "\e994";
}

.icon-trophe:before {
    content: "\e995";
}

.icon-truck:before {
    content: "\e996";
}

.icon-truck2:before {
    content: "\e997";
}

.icon-tv:before {
    content: "\e998";
}

.icon-tv_wifi:before {
    content: "\e999";
}

.icon-unite:before {
    content: "\e99a";
}

.icon-Unlink:before {
    content: "\e99b";
}

.icon-unlock:before {
    content: "\e99c";
}

.icon-up_arrow:before {
    content: "\e99d";
}

.icon-down_arrow1:before {
    content: "\e9bc";
}

.icon-vibrate:before {
    content: "\e99e";
}

.icon-VIP:before {
    content: "\e99f";
}

.icon-wallet:before {
    content: "\e9a0";
}

.icon-windows:before {
    content: "\e9a1";
}

.icon-world:before {
    content: "\e9a2";
}

.icon-x_box:before {
    content: "\e9a3";
}

.icon-instagram:before {
    content: "\e9a4";
}

.icon-facebook:before {
    content: "\e9a5";
}

.icon-google_plus:before {
    content: "\e9a6";
}

.icon-twitter:before {
    content: "\e9a7";
}

.icon-youtube:before {
    content: "\e9a8";
}

.icon-Down_arrow1:before {
    content: "\e9bd";
}

.icon-Right_arrow:before {
    content: "\e9a9";
}

.icon-Left_arrow:before {
    content: "\e9be";
}

.icon-Big_collapse:before {
    content: "\e9aa";
}

.icon-Big_expand:before {
    content: "\e9ab";
}

.icon-collapse_m:before {
    content: "\e9ac";
}

.icon-plus2:before {
    content: "\e9ad";
}

/* Icon with color path */
.icon-Collapse .path1:before {
    content: "\e9ae";
    color: #2390b9;
}

.icon-Collapse .path2:before {
    content: "\e9af";
    color: #fff;
    margin-left: -1em;
}

.icon-Expand .path1:before {
    content: "\e9b0";
    color: #2390b9;
}

.icon-Expand .path2:before {
    content: "\e9b1";
    color: #fff;
    margin-left: -1em;
}

.icon-warning .path1:before {
    content: "\e9b2";
    color: #c00;
}

.icon-warning .path2:before {
    content: "\e9b3";
    color: #fff;
    margin-left: -1em;
}

.icon-delete_x .path1:before {
    content: "\e9b4";
    color: #999;
}

.icon-delete_x .path2:before {
    content: "\e9b5";
    color: #fff;
    margin-left: -1em;
}

.icon-question_bg .path1:before {
    content: "\e9b6";
    color: #2390b9;
}

.icon-question_bg .path2:before {
    content: "\e9b7";
    color: #fff;
    margin-left: -1em;
}

.icon-Big_check_confirm .path1:before {
    content: "\e9b8";
    color: #2c9e25;
}

.icon-Big_check_confirm .path2:before {
    content: "\e9b9";
    color: #fff;
    margin-left: -1em;
}

.icon-BIG_WARNING .path1:before {
    content: "\e900";
    color: #e99e00;
}

.icon-BIG_WARNING .path2:before {
    content: "\e901";
    color: #fff;
    margin-left: -1em;
}

.icon-Big_info .path1:before {
    content: "\e902";
    color: #2390b9;
}

.icon-Big_info .path2:before {
    content: "\e9ba";
    color: #fff;
    margin-left: -1em;
}

.icon-info .path1:before {
    content: "\e903";
    color: #a4a6a7;
}

.icon-info .path2:before {
    content: "\e9bb";
    color: #fff;
    margin-left: -1em;
}

.icon-Payment .path1:before {
    content: "\e9bf";
    color: #f4f4f4;
}

.icon-Payment .path2:before {
    content: "\e9c0";
    color: #191617;
    margin-left: -3.2890625em;
}

.icon-Payment .path3:before {
    content: "\e9c1";
    color: #0a5296;
    margin-left: -3.2890625em;
}

.icon-Payment .path4:before {
    content: "\e9c2";
    color: #f6a500;
    margin-left: -3.2890625em;
}

.icon-Payment .path5:before {
    content: "\e9c3";
    color: #c1c1c1;
    margin-left: -3.2890625em;
}

.icon-Payment .path6:before {
    content: "\e9c4";
    color: #ff0016;
    margin-left: -3.2890625em;
}

.icon-Payment .path7:before {
    content: "\e9c5";
    color: #ffa916;
    margin-left: -3.2890625em;
}

.icon-Payment .path8:before {
    content: "\e9c6";
    color: #ff6a00;
    margin-left: -3.2890625em;
}

.icon-Payment-cards .path1:before {
    content: "\e9c7";
    color: #f4f4f4;
}

.icon-Payment-cards .path2:before {
    content: "\e9c8";
    color: #0a5296;
    margin-left: -5.2890625em;
}

.icon-Payment-cards .path3:before {
    content: "\e9c9";
    color: #f6a500;
    margin-left: -5.2890625em;
}

.icon-Payment-cards .path4:before {
    content: "\e9ca";
    color: #192169;
    margin-left: -5.2890625em;
}

.icon-Payment-cards .path5:before {
    content: "\e9cb";
    color: #ff0016;
    margin-left: -5.2890625em;
}

.icon-Payment-cards .path6:before {
    content: "\e9cc";
    color: #ffa916;
    margin-left: -5.2890625em;
}

.icon-Payment-cards .path7:before {
    content: "\e9cd";
    color: #ff6a00;
    margin-left: -5.2890625em;
}

.icon-Payment-cards .path8:before {
    content: "\e9ce";
    color: #00adef;
    margin-left: -5.2890625em;
}

.icon-Payment-cards .path9:before {
    content: "\e9cf";
    color: #fff;
    margin-left: -5.2890625em;
}

/* TABLE WITH VISIBLE AND HIDDEN COLUMNS */

/* TABLE WITH EXPANDING ROWS */

/* Loader */


/*Table*/

/*Information overlay*/


/*chartRow*/

/*headerRow*/


/*Footer*/


/*Data rows*/

/*END Responsive Tables*/

/*START Asset Links*/


/*END Asset Links*/


/*START footer grey Navigation */

.footer_grey .footer_grey_wrapper {
    background-color: #f4f4f4;
}

.footer_grey .footer_container {
    padding-top: 45px;
    padding-bottom: 45px;
}

/*footer skip to main content*/
.footer_grey .footer_skip_to_main_link, .simple-footer .footer_skip_to_main_link {
    display: inline-block;
    padding: 7px 12px;
    position: absolute;
    left: -300px;
    text-decoration: underline;
    border-bottom-right-radius: 8px;
    transition: left .3s ease-out;
    background-color: #fff;
    z-index: 3000;
    font-size: 13px;
    color: #E10A0A;
}

    .footer_grey .footer_skip_to_main_link:focus, .simple-footer .footer_skip_to_main_link:focus {
        left: 0;
    }

/*Bread Crumbs*/
    .bread_crumbs_wrapper ol.bread_crumbs_container > li.bread_crumbs_item {
        display: inline-block;
    }

.bread_crumbs_wrapper ol.bread_crumbs_container li.bread_crumbs_item > i {
    margin: 0 10px;
}

.bread_crumbs_wrapper ol.bread_crumbs_container li.bread_crumbs_item:last-child > i {
    display: none;
}

.bread_crumbs_wrapper ol.bread_crumbs_container li.bread_crumbs_item.active > a {
    color: #111111;
    text-decoration: none;
    cursor: default;
}
/*Bread Crumbs*/

/*4 Columns Links List*/


/*4 Columns Links List*/

/*Email Links List*/

/*Email Links List*/

/*Site Links List*/

/*Site Links List*/

/*Social Icon List*/
.social_icons_wrapper ul.social_icons_cont {
    text-align: left;
}

    .social_icons_wrapper ul.social_icons_cont li {
        display: inline-block;
    }

    .social_icons_wrapper ul.social_icons_cont a {
        display: table-cell;
        vertical-align: middle;
        text-align: center;
    }

    .social_icons_wrapper ul.social_icons_cont li .icon_background {
        background-color: #E10A0A;
        color: #fff;
        height: 36px;
        width: 36px;
        border-radius: 50%;
        display: inline-block;
        text-align: center;
    }

        .social_icons_wrapper ul.social_icons_cont li .icon_background > i:before {
            top: 25%;
            left: 0;
        }
/*Social Links Icon List*/

/*Back top top button*/
.backtotop_tablet_mobile_wrapper .backtotop_tablet_mobile .scrollToTop.mobile {
    opacity: 1;
    bottom: 15px;
    right: 15px;
    width: auto;
    height: auto;
    position: fixed;
    padding-top: 0;
    border-radius: 0;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.backtotop_tablet_mobile_wrapper .backtotop_tablet_mobile .icon_background {
    height: 49px;
    width: 49px;
    background-color: rgba(255,255,255,0.5);
    box-shadow: 0 1px 29px 0 rgba(0,0,0,0.25);
    border-radius: 50%;
    display: inline-block;
    text-align: center;
}

    .backtotop_tablet_mobile_wrapper .backtotop_tablet_mobile .icon_background > i:before {
        top: 25%;
        left: 0;
    }
/*Back top top button*/

/*footer Media Queries*/
@media (min-width: 992px) {
    /*footer Social Links*/
    .social_icons_wrapper ul.social_icons_cont {
        text-align: right;
    }
}

@media (max-width: 991.98px) {
    .footer_grey .footer_container {
        padding-top: 30px;
        padding-bottom: 30px;
    }
}

@media (min-width: 768px) and (max-width: 991.98px) {
    /*4 Columns Links List*/
    .fourcol_links_list_wrapper ul.fourcol_ll_container {
        columns: 3;
        -webkit-columns: 3;
        min-height: 1px;
    }
    /*4 Columns Links List*/

    /*Email Links List*/
    .email_list_wrapper ul.email_list_cont > li.email_list_column {
        width: calc(33.33% - 25px);
        margin-top: 30px;
    }

        .email_list_wrapper ul.email_list_cont > li.email_list_column:nth-child(-n+3) {
            margin-top: 0;
        }

        .email_list_wrapper ul.email_list_cont > li.email_list_column:nth-child(3) {
            margin-right: 0;
        }
    /*Email Links List*/
}

@media (max-width: 767.98px) {
    /*4 Columns Links List*/
    .fourcol_links_list_wrapper ul.fourcol_ll_container {
        columns: 1;
    }
    /*4 Columns Links List*/

    /*Email Links List*/
    .email_list_wrapper ul.email_list_cont > li.email_list_column {
        width: 100%;
        margin-top: 30px;
        margin-right: 0;
    }

        .email_list_wrapper ul.email_list_cont > li.email_list_column:first-child {
            margin-top: 0;
        }
    /*Email Links List*/

    /*Site Links List*/
    .site_links_list_wrapper ul.site_links_list_cont li > a {
        margin: 0 5px;
    }

    .site_links_list_wrapper ul.site_links_list_cont li:first-child > a {
        margin-left: 0;
    }

    .site_links_list_wrapper ul.site_links_list_cont li:last-child > a {
        margin-right: 0;
    }
    /*Site Links List*/
}

/*END Footer Grey Navigation*/
 
/*for mobile devices to disable zooming in at the element*/
@media screen and (-webkit-min-device-pixel-ratio:0) and (max-device-width:767px) { 
    select.mos_zoom,
    textarea.mos_zoom,
    input.mos_zoom{
        font-size: 16px; 
    }
    .form-control-select, .date-picker{
        font-size: 16px; 
    }
}

@media screen and (max-width: 767.98px) {
.message-block .icon-width-40,.message-block .content-width,.upgrade-my-device .icon-width-40,.md-icon-info-block-full .icon-width-40,.small-icon-info-block-half-full .icon-width-40{width:100%}
.container-fullWidth-xs{width:100%}
.steps-textsize{font-size:12px}
}
@media (min-width: 320px) and (max-width: 991.98px) {
.container, .container.liquid-container{width:100%;max-width:100%}
.container{padding-left:30px;padding-right:30px}
}
@media (min-width: 992px) and (max-width: 1239.98px) {
.container{padding-left:45px;padding-right:45px}
}
@media (min-width:992px) {
.container, .container.liquid-container{width:100%;max-width:100%}
}
@media (min-width:1200px) {
.container, .container.liquid-container{width:100%;max-width:100%}
}
@media (min-width:1240px) {
.container, .container.liquid-container{width:1200px}
}

/*BRF3 New Styleguide container*/
@media (min-width: 320px) and (max-width: 767.98px) { 
.brfpad .container, .brfpad .container.liquid-container{padding-left:15px;padding-right:15px}
}
@media (min-width: 768px) and (max-width: 991.98px) { 
.brfpad .container, .brfpad .container.liquid-container{padding-left:30px;padding-right:30px}
}
@media (min-width: 992px) and (max-width: 1239.98px) {
.brfpad .container, .brfpad .container.liquid-container{padding-left:16px;padding-right:16px}
}


/*fix for firefox input elements which show no text at all using native bootstrap*/
.firefoxFix{height:38px;padding:6px 12px}

a.btn:focus {
    text-decoration: none;
}

input.searchBox:focus, .search-bar-footer input.ui-autocomplete-input:focus{
    border:2px solid #BD2025; 
        -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6);
}

body.is_tabbing *:focus {
  outline: 2px solid #4d90fe !important; /* for non-webkit browsers */
  outline: 2px auto -webkit-focus-ring-color !important; 
}
body:not(.is_tabbing) button:focus,
body:not(.is_tabbing) input:focus,
body:not(.is_tabbing) select:focus,
body:not(.is_tabbing) a:focus {
  outline: none;
} 