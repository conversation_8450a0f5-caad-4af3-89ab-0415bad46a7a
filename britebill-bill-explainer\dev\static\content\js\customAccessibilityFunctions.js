﻿//*-------------------------------
//Custom Functions for Accessibility Fixes
//--------------------------------*/
//Version date Aug, 31, 2021 3:11pm

//Global Variables
var focusableEl = "a[href]:not([aria-disabled=true]),a[onclick]:not([aria-disabled=true]),button:not(disabled):not([aria-disabled=true]),[tabindex=0]";

//---------------------- START - Utiliy Functions -----------------------//

//This function sets the other sections' tabindex to -1 and aria-hidden to true when the passed modal is opened to trap keyboard and screenreader focus.
function overwriteTabIndexAndAriaHiddenDifferentHierarchy(currEl, tempMoveToProperPos) {
    var parent, index, originClass;

    // if tempMoveToProperPos === true, the modal will temporarily be moved and become an immediate child of the document body
    if (tempMoveToProperPos === true) {
        index = currEl.index();
        if (index > 0) { // has previous sibling, mark it
            originClass = 'origin-' + (new Date()).getTime();
            currEl.prev().addClass(originClass);
            currEl.data('originprev', originClass).appendTo('body');
        } else { // remember parent
            originClass = 'origin-' + (new Date()).getTime();
            currEl.parent().addClass(originClass);
            currEl.data('originparent', originClass).appendTo('body');
        }
    }

    // process the current element's siblings
    currEl.siblings().each(function () {
        var el = $(this), tabindex = el.attr('tabindex'), ariaHidden = el.attr('aria-hidden');

        if (null != tabindex && "" !== tabindex) {
            el.data('oldtabindex', tabindex);
        }
        el.attr('tabindex', -1);

        if (null != ariaHidden && "" !== ariaHidden) {
            el.data('oldariahidden', ariaHidden);
        }
        el.attr('aria-hidden', true);

        el.find('a,area,input,select,textarea,button,iframe,[tabindex],[contentEditable=true]').each(function () {
            el = $(this), tabindex = el.attr('tabindex');

            if (null != tabindex && "" !== tabindex) {
                el.data('oldtabindex', tabindex);
            }
            el.attr('tabindex', -1);
        });
    });

    // use recursion to process each ancestor until the body root is reached
    parent = currEl.parent();
    if (parent.length > 0 && !parent.is('body')) {
        overwriteTabIndexAndAriaHiddenDifferentHierarchy(currEl.parent());
    }
}

//This function reverts the other sections' tabindex and aria-hidden to their original values when the passed modal is closed to remove keyboard and screenreader focus trapping.
function revertTabIndexAndAriaHiddenDifferentHierarchy(currEl) {
    var parent, origParentClass, origParent, origPrevSibClass, origPrevSib;

    // process the current element's siblings
    currEl.siblings().each(function () {
        var el = $(this), tabindex = el.data('oldtabindex'), ariaHidden = el.attr('oldariahidden');

        if (null != tabindex) {
            el.attr('tabindex', tabindex);
            el.removeData('oldtabindex');
        } else {
            el.removeAttr('tabindex');
        }

        if (null != ariaHidden) {
            el.attr('aria-hidden', ariaHidden);
            el.removeData('oldariahidden');
        } else {
            el.removeAttr('aria-hidden');
        }

        el.find('a,area,input,select,textarea,button,iframe,[tabindex],[contentEditable=true]').each(function () {
            el = $(this), tabindex = el.data('oldtabindex');

            if (null != tabindex) {
                el.attr('tabindex', tabindex);
                el.removeData('oldtabindex');
            } else {
                el.removeAttr('tabindex');
            }
        });
    });

    // use recursion to process each ancestor until the body root is reached
    parent = currEl.parent();
    if (parent.length > 0 && !parent.is('body')) {
        revertTabIndexAndAriaHiddenDifferentHierarchy(currEl.parent());
    }

    // this returns the modal to its original position if it was temporarily moved by overwriteTabIndexAndAriaHiddenDifferentHierarchy
    origParentClass = currEl.data('originparent');
    if (origParentClass) {
        origParent = $('.' + origParentClass);
        currEl.prependTo(origParent);
        origParent.removeClass(origParentClass);
        currEl.removeData('originparent');
    } else {
        origPrevSibClass = currEl.data('originprev');
        if (origPrevSibClass) {
            origPrevSib = $('.' + origPrevSibClass);
            currEl.insertAfter(origPrevSib);
            origPrevSib.removeClass(origPrevSibClass);
            currEl.removeData('originprev');
        }
    }
}

//Function that toggles element aria-expanded
function toggleAriaExpanded(triggerEl) {
    var expandedAttr = (triggerEl.attr("aria-expanded") == "true");
    if (expandedAttr === undefined || expandedAttr == null) {
        expandedAttr = false;
    }
    triggerEl.attr("aria-expanded", !expandedAttr);
}

//Function that triggers element click 
function clickPreventDefault(e, elem) {
    var jQueryEl = $(elem);

    e.preventDefault();
    e.stopPropagation();

    if (jQueryEl.is('a[href]:not([onclick]')) {
        elem.click();
    } else {
        jQueryEl.click();
    }
}

//check if the language use in a page
function getIsFrench() {
    var language = $('html').attr('lang');

    if (language === undefined) {
        language = 'en';
    } else {
        language = language.substring(0, 2).toLowerCase();
    }
    return language === 'fr';
}

// Returns the string with the first letter capitalized, return empty string if the passed variable is undefined
function firstToCap(s) {
    return s === undefined ? "" : s.charAt(0).toUpperCase() + s.slice(1);
}

// function to get the id of the passed element. if no id is set, a new one is generated, set, and returned
function getSetId(el) {
    var triggerId = el.id;

    if (!triggerId) {
        triggerId = Math.random().toString(36).substring(2) + (new Date()).getTime().toString(36);
        el.id = triggerId;
    }

    return triggerId;
}

//----------------------END - Utiliy Functions -----------------------//

//---------------------- START - Page/Component Specific Functions -----------------------//

// START - Global Navigation Accessibility functions
function changeLinkToButton(links) {
    $.each(links, function () {
        var targetEl = $(this),
            href = targetEl.attr("href"),
            btnMenu = targetEl.siblings(".connector-lob-dropdown-mobile"),
            btnMenuId = getSetId(btnMenu);

        if (href == undefined || href == null) {
            targetEl.attr("href", "javascript:void(0)");
        }

        if (targetEl.siblings(".connector-lob-dropdown-mobile").length > 0) {
            targetEl.attr({
                "role": "button",
                "aria-expanded": targetEl.parent().hasClass("active"),
                "aria-controls": btnMenuId
            });
            btnMenu.attr("id", btnMenuId);
        }
    });

    links.not(".initialized").addClass('initialized').on("click", function (e) {
        if ($(window).width() < 1000) {
            $('.accss-styles-init .button-on-mobile > a').not(this).not(".no-flyout-menu").attr("aria-expanded", "false");
            toggleAriaExpanded($(this));
        }
    }).on("keydown", function (e) {
        if (32 === (e.which || e.keyCode || 0)) {
            clickPreventDefault(e, this);
        }
    });
}

function revertButtonToLink(links) {
    $.each(links, function () {
        var targetEl = $(this),
            isHrefAvail = targetEl.hasClass("connector-lob-no-href"),
            btnMenu = targetEl.siblings(".connector-lob-dropdown-mobile");

        if (isHrefAvail) {
            targetEl.removeAttr("href");
        }
        targetEl.removeAttr("role aria-expanded aria-controls");
        btnMenu.removeAttr("id");
    });
}

function applyNavFocusTrapping(targetPanel, trigger) {
    //applying trap focus on the mobile menu panel
    targetPanel.toggleClass("hidden-on-mobile");
    if (!targetPanel.hasClass("hidden-on-mobile")) {
        if (!targetPanel.hasClass("mobNav-trap-focus-init")) {
            targetPanel.addClass("mobNav-trap-focus-init");
            overwriteTabIndexAndAriaHiddenDifferentHierarchy(targetPanel);
            trigger.siblings().addBack().parent().addBack().removeAttr("aria-hidden tabindex");
            trigger.attr({
                'aria-label': (getIsFrench() ? 'Fermer le menu de navigation' : 'Close Mobile Nav'),
                'title': (getIsFrench() ? 'Fermer le menu de navigation' : 'Close Mobile Nav')
            });
        }
    } else {
        if (targetPanel.hasClass("mobNav-trap-focus-init")) {
            targetPanel.removeClass("mobNav-trap-focus-init");
            revertTabIndexAndAriaHiddenDifferentHierarchy(targetPanel);
            trigger.siblings().addBack().parent().addBack().removeAttr("aria-hidden tabindex");
            trigger.attr({
                'aria-label': (getIsFrench() ? 'Ourvrir le menu de navigation' : 'Open Mobile Nav'),
                'title': (getIsFrench() ? 'Ourvrir le menu de navigation' : 'Open Mobile Nav')
            });
        }
    }
    toggleAriaExpanded(trigger);
}

function checkNavIfNotDesktop() {
    var isNavResponsive = ($('.accss-styles-init header.gn-non-responsive').length === 0),
        windowWidth = $(window).outerWidth(),
        linksToChange = $('.accss-styles-init .button-on-mobile > a'),
        mobilePaneltrigger = $('.accss-styles-init header .connector-nav-open-button'),
        targetPanel = $("#" + mobilePaneltrigger.attr("aria-controls")),
        $menuWithFlyOut = $(".accss-styles-init header .show-flyout-onfocus-virgin");

    if (!isNavResponsive) {
        return false;
    }

    if (windowWidth < 1000) {
        changeLinkToButton(linksToChange);
        $menuWithFlyOut.attr('aria-expanded', ($menuWithFlyOut.parents('.connector-area').hasClass('active'))).attr('role', 'button');
        if (!targetPanel.hasClass("hidden-on-mobile") && !targetPanel.hasClass("mobNav-trap-focus-init")) {
            targetPanel.addClass("mobNav-trap-focus-init");
            overwriteTabIndexAndAriaHiddenDifferentHierarchy(targetPanel);
            mobilePaneltrigger.siblings().addBack().parent().addBack().removeAttr("aria-hidden tabindex");
        }
    } else {
        revertButtonToLink(linksToChange);
        $menuWithFlyOut.attr('aria-expanded', false).removeAttr('role');

        if ($menuWithFlyOut.is($(document.activeElement))) {
            $menuWithFlyOut.addClass('blur-from-resize').blur();
        }

        if (targetPanel.hasClass("mobNav-trap-focus-init")) {
            targetPanel.removeClass("mobNav-trap-focus-init");
            revertTabIndexAndAriaHiddenDifferentHierarchy(targetPanel);
            mobilePaneltrigger.siblings().addBack().parent().addBack().removeAttr("aria-hidden tabindex");
        }
    }
}
// END - Global Navigation Accessibility functions

if ('function' == typeof $.fn.datepicker) {
    //Define functions for datepicker accessibility if the datepicker js is available
    if ('function' !== typeof initDatePickerAccessibility) {
        //This function initialize the datepicker for a specific input and initialize function that makes the datepicker accessible
        window.initDatePickerAccessibility = function initDatePickerAccessibility(datePickerOptions, inputTrigger) {

            var isFrench = getIsFrench(),
                closeText = isFrench ? 'Fermer' : 'Close';

            // localization - datepicker js regional for french should be present           
            $.datepicker.setDefaults($.datepicker.regional[isFrench ? 'fr' : '']);

            var input = inputTrigger
                , showOn = datePickerOptions["showOn"] || 'focus'
                , passedShowOn
                , implementedShowOn
                , dpOptions;

            /* jquery-ui supports 'focus', 'button', or 'both' for showOn value (focus refers to input focus).
             * however, since we are moving the focus inside the datepicker and trapping it there whenever it is shown, it's not smart to do this on focus because it can be disorienting.
             * so, instead of focus (which i will be blocking and default to button when set as data-show-on option), we'll enable showing it on click (or ENTER keypress) instead by setting the data-show-on attribute to 'click' or 'both'.
             * in detail, if data-show-on equals:
             * 'button' - default value. datepicker is shown only upon clicking the button trigger
             * 'click' - datepicker is shown only upon clicking the input or pressing ENTER or SPACE while it is focused
             * 'both' - datepicker is shown in both 'button' and 'click' scenarios
             * 'focus' - this option will be forced to change to click option
             * any other value - invalid option so nothing will happen
             */
            switch (showOn) {
                case 'button':
                    passedShowOn = 'button';
                    implementedShowOn = 'button';
                    break;
                case 'both':
                    passedShowOn = 'button';
                    implementedShowOn = 'button and click';
                    break;
                case 'click':
                    passedShowOn = 'none'; // passing none or any invalid string will cause the datepicker to not show on anything
                    implementedShowOn = 'click';
                    break;
                case 'focus':
                    passedShowOn = 'none';
                    implementedShowOn = 'click';
                    break;
                default: // invalid
                    passedShowOn = 'none';
                    implementedShowOn = 'none';
                    break;
            }

            //Setting the default datepicker options for fixing its accessibility issues
            var addedFunctions = {
                showOn: passedShowOn,
                closeText: closeText,
                onClose: function () {
                    var input = $(this);
                    setTimeout(function () {
                        var calendarDiv = $('#ui-datepicker-div'),
                            trigger = $('#' + calendarDiv.data('trigger-id'));

                        input.off('keydown', preventSpaceKeydown);
                        trigger.attr("aria-expanded", "false").focus();
                        calendarDiv.off('keydown');

                        revertTabIndexAndAriaHiddenDifferentHierarchy(calendarDiv);
                    }, 0);
                },
                onChangeMonthYear: function () {
                    var input = $(this);
                    input.attr("disabled", true);
                    setTimeout(function () {
                        if ($.datepicker._curInst !== null) {
                            setPrimaryFocus();
                            addCalendarTableLabel();
                            addCalendarDatesLabel();
                            setupPrevNextButtons();
                            setupMonthYearDropdowns();
                            setupTodayButton();
                        }
                        input.removeAttr("disabled");
                    }, 0);
                }
            };
            //combine the define options and the default options but the priority will be the defaul options
            dpOptions = $.extend({}, datePickerOptions, addedFunctions);

            // initialize the datepicker
            // instead of using the onSelect option, we'll use change event listener because jquery-ui doesn't fire the change event if an onSelect option is passed. using the change event, we can support multiple listeners
            input.datepicker(dpOptions).on("change", function () {
                setHighlightedState($('.ui-state-hover')[0], $('#ui-datepicker-div')[0]);
            });

            //add class to the body indicating that datepicker was initialized
            $('body:not(.initialized)').addClass('datepicker-initialized');

            // initialize the generated show calendar button
            initializeShowCalendarTrigger(implementedShowOn, input);

            //This is for the select option when it is shown
            // jquery-ui is recreating the trigger button when an option gets changed so let's listen to an event that can be triggered to signify this
            input.on('optionchange', function () {
                initializeShowCalendarTrigger(implementedShowOn, input);
            });
            // let's perform a similar check on mousedown (do not change this to click or mouseup and do not make the called function asynchronous) for cases wherein the optionchange event was not triggered after changing the option
            input.parent().on('mousedown', '.ui-datepicker-trigger:not(.initialized)', function () {
                initializeShowCalendarTrigger(implementedShowOn, input);
            });
        }
    }

    if ('function' !== typeof initializeShowCalendarTrigger) {
        // this function initializes the show calendar trigger (either button click or input click or both) and associates it with the calendar upon showing since we only have one calendar instance
        window.initializeShowCalendarTrigger = function (implementedShowOn, input) {
            var buttonTrigger,
                labelId,
                initButton = false,
                initInput = false,
                buttonImageOnly = input.datepicker('option', 'buttonImageOnly');

            switch (implementedShowOn) {
                case 'none':
                    return;
                case 'button':
                    initButton = true;
                    break;
                case 'click':
                    initInput = true;
                    break;
                case 'button and click':
                    initButton = true;
                    initInput = true;
                    break;

            }

            // improve/tweak jquery's implementation of showing/hiding of datepicker by clicking the button
            if (initButton && !buttonImageOnly) {
                buttonTrigger = input.next('.ui-datepicker-trigger:not(.initialized)');
                if (buttonTrigger.length > 0) {
                    // Add aria-describedby to the button referring to the input's label, adding alt attribute for the button image
                    buttonTrigger.attr('aria-label', getIsFrench() ? 'Montrer le calendrier' : 'Show calendar');
                    buttonTrigger.find('img').attr('alt', getIsFrench() ? 'Montrer le calendrier' : 'Show calendar').removeAttr('title');
                    labelId = getInputLabelId(input);
                    if (labelId !== null) {
                        buttonTrigger.attr('aria-describedby', labelId);
                    }

                    buttonTrigger.on('click', function () {
                        // $.datepicker._datepickerShowing is updated before click fires so we check the inverted value
                        if (!$.datepicker._datepickerShowing && this === $.datepicker._curInst.trigger[0]) {
                            $.datepicker.dpDiv.data('trigger-id', getSetId(this));
                            input.datepicker('hide');
                        } else {
                            input.attr("disabled", true);
                            onShowCalendar.call(this);
                            input.removeAttr("disabled");
                        }
                    });

                    buttonTrigger.addClass('initialized');
                }
            }

            // implement showing/hiding of datepicker by clicking the input or pressing ENTER or SPACE while it is focused
            if (initInput && !input.hasClass('initialized')) {
                input.on('click', function () {
                    if ($.datepicker._datepickerShowing && this === $.datepicker._lastInput) {
                        $.datepicker.dpDiv.data('trigger-id', getSetId(this));
                        input.datepicker('hide');
                    } else {
                        input.datepicker('show');
                        onShowCalendar.call(this);
                    }
                }).on('keypress', function (e) {
                    var key = e.which || e.keyCode || 0;

                    if (13 === key || 32 === key) {
                        e.preventDefault();
                        e.stopPropagation();
                        input.click();
                    }
                });

                input.addClass('initialized');
            }

            //hiding of datepicker upon input click when button is the trigger and not the input
            if (!initInput && initButton && !input.hasClass('initialized')) {
                input.on('click', function () {
                    if ($.datepicker._curInst !== null) {
                        $.datepicker._curInst.input.datepicker('hide');
                        setTimeout(function () {
                            input.focus();
                        }, 0);
                    }
                })

                input.addClass('initialized');
            }
        }
    }

    if ('function' !== typeof onShowCalendar) {
        // this function sets up the calendar everytime it is shown
        window.onShowCalendar = function () {
            var triggerId = getSetId(this);

            $.datepicker._curInst.input.on('keydown', preventSpaceKeydown);
            //Setting the aria-expanded for the trigger of the datepicker
            $('#' + triggerId).attr("aria-expanded", "true");

            setTimeout(function () {
                var calendarDivEl = $('#ui-datepicker-div'),
                    calendarDiv = calendarDivEl[0],
                    highlightedDate;

                //setting the primary focus for the datepicker                 
                setPrimaryFocus();

                // set reference to button trigger on the calendar
                calendarDivEl
                    .data('trigger-id', triggerId)
                    .attr({
                        'role': 'application',
                        'aria-label': getIsFrench() ? 'S�lecteur de date de calendrier' : 'Calendar date picker'
                    });

                // set label for the table of the calendar
                addCalendarTableLabel();

                // set aria-label of each date(number) of the calendar
                addCalendarDatesLabel();

                // set various attributes of the previous and next buttons of the calendar and attach click event handlers
                setupPrevNextButtons();

                // set labels for month and year dropdowns
                setupMonthYearDropdowns();

                // set today button if present
                setupTodayButton();

                // call function to override keydown events inside the calendar div
                initCalendarNavigation(calendarDiv);

                // hide other elements from screenreaders whenever the calendar is shown
                overwriteTabIndexAndAriaHiddenDifferentHierarchy(calendarDivEl);
            }, 0);
        }
    }

    if ('function' !== typeof setPrimaryFocus) {
        // add an aria-label to the date link indicating the currently focused date
        window.setPrimaryFocus = function () {
            var calendarDivEl = $("#ui-datepicker-div"),
                highlightedDate = $('.ui-state-active');

            //priority of focus is selected date followed by current date followed by the first date of currently visible month
            if (highlightedDate.length === 0) {
                highlightedDate = $('.ui-datepicker-today a');
                if (highlightedDate.length === 0) {
                    highlightedDate = calendarDivEl.find('td:not(.ui-state-disabled)').first().find(".ui-state-default");
                }
            }
            highlightedDate.focus();
            setHighlightedState(highlightedDate, calendarDivEl[0]);
        }
    }

    if ('function' !== typeof addCalendarTableLabel) {
        // add an aria-label to the date link indicating the currently focused date
        window.addCalendarTableLabel = function () {
            var datePickDiv = $("#ui-datepicker-div")[0],
                calendarTableTitle = $(".ui-datepicker-title", datePickDiv),
                calendarTable = $(".ui-datepicker-calendar", datePickDiv),
                monthEl = $('.ui-datepicker-month', datePickDiv),
                yearEl = $('.ui-datepicker-year', datePickDiv),
                monthName = monthEl.find('option:selected').text(),
                year = yearEl.find('option:selected').text(),
                titleID = getSetId(calendarTableTitle);

            if (monthName.length === 0 && year.length === 0) {
                calendarTableTitle.attr("id", titleID);
                calendarTable.attr("aria-labelledby", titleID);
            } else {
                calendarTable.attr("aria-label", monthName + " " + year);
            }
        }
    }

    if ('function' !== typeof addCalendarDatesLabel) {
        // add an aria-label to the date link indicating the currently focused date
        window.addCalendarDatesLabel = function () {
            var cleanUps = $('.amaze-date'),
                datePickDiv = $("#ui-datepicker-div")[0],
                calendarDiv = $(datePickDiv),
                triggerEl,
                inputEl,
                dateFormat,
                shortYearCutoff,
                settings;

            // get date format and settings
            triggerEl = $('#' + calendarDiv.data('trigger-id'));
            inputEl = $.datepicker._curInst.input;
            dateFormat = "dd M yy"; // this will be the default format of the dates label
            shortYearCutoff = inputEl.datepicker('option', 'shortYearCutoff');
            shortYearCutoff = (typeof shortYearCutoff !== "string" ? shortYearCutoff : new Date().getFullYear() % 100 + parseInt(shortYearCutoff, 10));
            settings = {
                shortYearCutoff: shortYearCutoff,
                dayNamesShort: inputEl.datepicker('option', 'dayNamesShort'),
                dayNames: inputEl.datepicker('option', 'dayNames'),
                monthNamesShort: inputEl.datepicker('option', 'monthNamesShort'),
                monthNames: inputEl.datepicker('option', 'monthNames')
            };

            $(cleanUps).each(function (clean) {
                // each(cleanUps, function (clean) {
                clean.parentNode.removeChild(clean);
            });

            $('a.ui-state-default', datePickDiv)
                .attr({
                    'role': 'button',
                    'href': 'javascript:void(0)'
                }).each(function (index, date) {
                    var currentRow = $(date).closest('tr'),
                        currentTds = $('td', currentRow),
                        currentIndex = $.inArray(date.parentNode, currentTds),
                        headThs = $('thead tr th', datePickDiv),
                        dayIndex = headThs[currentIndex],
                        daySpan = $('span', dayIndex)[0],
                        monthEl = $('.ui-datepicker-month', datePickDiv),
                        yearEl = $('.ui-datepicker-year', datePickDiv),
                        monthName = monthEl.find('option:selected').text(),
                        year = yearEl.find('option:selected').text(),
                        number = date.innerHTML,
                        currentMonth = ($.inArray(monthName, settings.monthNamesShort) + 1),
                        formattedDate;

                    if (monthName.length === 0) {
                        monthName = monthEl.text();
                    }

                    if (year.length === 0) {
                        year = yearEl.text();
                    }

                    if (!monthName || !number || !year) {
                        return;
                    }

                    if (currentMonth === 0) {
                        currentMonth = ($.inArray(monthName, settings.monthNames) + 1)
                    }

                    // support formatting based on "dd M yy" option
                    formattedDate = $.datepicker.formatDate(dateFormat, new Date(currentMonth + '-' + date.innerHTML + '-' + year), settings);
                    // AT Reads: {date} {month} {year} {day:optional} ex. "18 December 2014 Thursday"
                    date.setAttribute('aria-label', formattedDate + (daySpan ? ' ' + daySpan.title : ''));
                }).on('click', function () {
                    //updates the button label based on the selected date
                    updateBtnLabel($(this));
                });

            //set aria disabled for the disabled elements and aria selected for the selected element
            $('.ui-state-disabled', datePickDiv).attr('aria-disabled', 'true');
            setSelectedState($('a.ui-state-default.ui-state-active', datePickDiv));
        }
    }

    if ('function' !== typeof setupPrevNextButtons) {
        // this function sets various attributes of the previous and next buttons of the calendar and attaches click event handlers
        window.setupPrevNextButtons = function () {
            var calendarDivEl = $('#ui-datepicker-div'),
                prevEl = calendarDivEl.find('.ui-datepicker-prev'),
                nextEl = calendarDivEl.find('.ui-datepicker-next'),
                prevNextButtons = prevEl.add(nextEl);

            // set-up calendar previous and next buttons
            prevNextButtons.attr({
                'role': 'button',
                'href': 'javascript:void(0)'
            }).removeAttr('title aria-disabled');

            if (prevEl.hasClass('ui-state-disabled')) {
                prevEl.attr('aria-disabled', 'true');
            }

            if (nextEl.hasClass('ui-state-disabled')) {
                nextEl.attr('aria-disabled', 'true');
            }

            setLabelPrevNextButtons(prevNextButtons);

            // delegation won't work here for whatever reason, so we are
            // forced to attach individual click listeners to the prev /
            // next month buttons each time they are added to the DOM
            prevEl.on('click', handlePrevBtnClicks);
            nextEl.on('click', handleNextBtnClicks);
        }
    }

    if ('function' !== typeof setLabelPrevNextButtons) {
        // append the corresponding value to the label of the next month and previous month buttons
        window.setLabelPrevNextButtons = function (buttons) {
            var isFrench = getIsFrench(),
                localizationDict = $.datepicker.regional[isFrench ? 'fr' : ''],
                monthNames = localizationDict.monthNames,
                monthNamesShort = localizationDict.monthNamesShort,
                calendarDivEl = $('#ui-datepicker-div'),
                monthEl = calendarDivEl.find('.ui-datepicker-month'),
                yearEl = calendarDivEl.find('.ui-datepicker-year'),
                currentMonth,
                currentYear,
                monthIndex;

            if (monthEl.is('select')) {
                currentMonth = monthEl.find('option:selected').text();
                monthIndex = $.inArray(currentMonth, monthNamesShort);
            } else {
                currentMonth = monthEl.text();
                monthIndex = $.inArray(currentMonth, monthNames);
            }

            if (yearEl.is('select')) {
                currentYear = yearEl.find('option:selected').text();
            } else {
                currentYear = yearEl.text();
            }

            currentYear = encodeURI(currentYear);

            (buttons || calendarDivEl.find('.ui-datepicker-prev, .ui-datepicker-next')).each(function () {
                var buttonEl = $(this),
                    buttonText,
                    isNext = buttonEl.hasClass('ui-datepicker-next'),
                    newIndex = isNext ? monthIndex + 1 : monthIndex - 1,
                    newYear = currentYear;

                if (isNext && monthIndex === 11) {
                    newYear = parseInt(currentYear, 10) + 1;
                    newIndex = 0;
                } else if (!isNext && monthIndex === 0) {
                    newYear = parseInt(currentYear, 10) - 1;
                    newIndex = monthNames.length - 1;
                }

                if (isFrench) {
                    buttonText = isNext
                        ? 'Le mois prochain, ' + firstToCap(monthNames[newIndex]) + ' ' + newYear
                        : 'Le mois pr�c�dent, ' + firstToCap(monthNames[newIndex]) + ' ' + newYear;
                } else {
                    buttonText = isNext
                        ? 'Next month, ' + firstToCap(monthNames[newIndex]) + ' ' + newYear
                        : 'Previous month, ' + firstToCap(monthNames[newIndex]) + ' ' + newYear;
                }

                buttonEl.find('.ui-icon').html(buttonText);
            });
        }
    }

    if ('function' !== typeof setupMonthYearDropdowns) {
        // this function sets the aria-label attribute and custom arrows of the month and year selectors if enabled
        window.setupMonthYearDropdowns = function () {
            var calendarDivEl = $('#ui-datepicker-div'),
                monthDropdown = calendarDivEl.find('select.ui-datepicker-month'),
                yearDropdown = calendarDivEl.find('select.ui-datepicker-year'),
                monthSelectorLabel = 'month selector',
                yearSelectorLabel = 'year selector';

            if (getIsFrench()) {
                monthSelectorLabel = 's�lecteur de mois';
                yearSelectorLabel = "s�lecteur d'ann�e";
            }

            monthDropdown.attr('aria-label', monthSelectorLabel).on('change', function () {
                setTimeout(function () {
                    $('.ui-datepicker-month').focus();
                }, 0);
            });
            yearDropdown.attr('aria-label', yearSelectorLabel).on('change', function () {
                setTimeout(function () {
                    $('.ui-datepicker-year').focus();
                }, 0);
            });

            if (monthDropdown.next('.custom-dropdown-arrow').length === 0) {
                monthDropdown.after('<span class="icon icon-select-trigger"></span>');
            }

            if (yearDropdown.next('.custom-dropdown-arrow').length === 0) {
                yearDropdown.after('<span class="icon icon-select-trigger"></span>');
            }

        }
    }

    if ('function' !== typeof setupTodayButton) {
        // this function sets the text of the current date button and modifies its behavior to select the date instead of just highlighting it
        window.setupTodayButton = function () {
            var calendarDivEl = $('#ui-datepicker-div'),
                todayButton = calendarDivEl.find('.ui-datepicker-current'),
                todayLabel = getIsFrench() ? "Aujourd'hui" : "Today";

            todayButton.text(todayLabel).click(function () {
                setTimeout(function () {
                    var activeDate = $('.ui-state-highlight') || $('.ui-state-active');
                    if (activeDate.length > 0) {
                        activeDate.click();
                    }
                }, 0);
            });
        }
    }

    if ('function' !== typeof initCalendarNavigation) {
        // this function overrides the keydown events inside the calendar div
        window.initCalendarNavigation = function (calendarDiv) {
            $(calendarDiv).on('keydown', function (e) {
                var key = e.which || e.keyCode || 0,
                    target = e.target,
                    targetEl = $(target),
                    newTarget,
                    highlightedDate = getHighlightedDate(calendarDiv),
                    triggerEl,
                    inputEl,
                    activeDate,
                    firstOfMonth,
                    daysOfMonth,
                    lastDay;

                if (!highlightedDate) {
                    highlightedDate = $('.ui-state-active');
                    if (highlightedDate.length === 0) {
                        highlightedDate = $('.ui-state-default:not(.ui-datepicker-close, .ui-datepicker-current)');
                    }
                    setHighlightedState(highlightedDate[0], calendarDiv);
                }

                if (27 === key) { // ESC
                    triggerEl = $('#' + $(this).data('trigger-id'));
                    inputEl = triggerEl.prev().addBack('input');
                    inputEl.datepicker('hide');
                    return;
                } else if (9 === key && e.shiftKey) { // SHIFT + TAB
                    e.preventDefault();
                    if (targetEl.not('.ui-datepicker-close, .ui-datepicker-current').hasClass('ui-state-default')) { // date values
                        newTarget = $('.ui-datepicker-next');
                        if (newTarget.hasClass('ui-state-disabled')) {
                            newTarget = $('.ui-datepicker-prev');
                            if (!newTarget.hasClass('ui-state-disabled')) {
                                newTarget.focus();
                            }
                        } else {
                            newTarget.focus();
                        }
                    } else if (targetEl.hasClass('ui-datepicker-next')) { // next button
                        newTarget = $('.ui-datepicker-year');
                        if (newTarget.is('select')) {
                            newTarget.focus();
                        } else {
                            newTarget = $('.ui-datepicker-month');
                            if (newTarget.is('select')) {
                                newTarget.focus();
                            } else {
                                newTarget = $('.ui-datepicker-prev');
                                if (newTarget.hasClass('ui-state-disabled')) {
                                    activeDate = $('.ui-state-highlight') || $('.ui-state-active');
                                    if (activeDate.length > 0) {
                                        activeDate.focus();
                                    }
                                } else {
                                    newTarget.focus();
                                }
                            }
                        }
                    } else if (targetEl.hasClass('ui-datepicker-year')) { // year dropdown
                        newTarget = $('.ui-datepicker-month');
                        if (newTarget.is('select')) {
                            newTarget.focus();
                        } else {
                            $('.ui-datepicker-prev').focus();
                        }
                    } else if (targetEl.hasClass('ui-datepicker-month')) { // month dropdown
                        $('.ui-datepicker-prev').focus();
                    } else if (targetEl.hasClass('ui-datepicker-prev')) { // prev button
                        newTarget = $('.ui-datepicker-close');
                        if (newTarget.length > 0) {
                            newTarget.focus();
                        } else {
                            activeDate = $('.ui-state-highlight') || $('.ui-state-active');
                            if (activeDate.length > 0) {
                                activeDate.focus();
                            } else {
                                $('.ui-datepicker-next').focus();
                            }
                        }
                    } else if (targetEl.hasClass('ui-datepicker-close')) { // close button
                        $('.ui-datepicker-current').focus();
                    } else if (targetEl.hasClass('ui-datepicker-current')) { // current button
                        activeDate = $('.ui-state-highlight') || $('.ui-state-active');
                        if (activeDate.length > 0) {
                            activeDate.focus();
                        }
                    }
                } else if (key === 9) { // TAB
                    e.preventDefault();
                    if (targetEl.not('.ui-datepicker-close, .ui-datepicker-current').hasClass('ui-state-default')) { // date values
                        newTarget = $('.ui-datepicker-current');
                        if (newTarget.length > 0) {
                            newTarget.focus();
                        } else {
                            newTarget = $('.ui-datepicker-prev');
                            if (newTarget.hasClass('ui-state-disabled')) {
                                if (!$('.ui-datepicker-next').hasClass('ui-state-disabled')) {
                                    $('.ui-datepicker-next').focus();
                                }
                            } else {
                                newTarget.focus();
                            }
                        }
                    } else if (targetEl.hasClass('ui-datepicker-current')) { // today button
                        $('.ui-datepicker-close').focus();
                    } else if (targetEl.hasClass('ui-datepicker-close')) { // close button
                        $('.ui-datepicker-prev').focus();
                    } else if (targetEl.hasClass('ui-datepicker-prev')) { // prev button
                        newTarget = $('.ui-datepicker-month');
                        if (newTarget.is('select')) {
                            newTarget.focus();
                        } else {
                            newTarget = $('.ui-datepicker-year');
                            if (newTarget.is('select')) {
                                newTarget.focus();
                            } else {
                                newTarget = $('.ui-datepicker-next');
                                if (newTarget.hasClass('ui-state-disabled')) {
                                    activeDate = $('.ui-state-highlight') || $('.ui-state-active');
                                    if (activeDate.length > 0) {
                                        activeDate.focus();
                                    }
                                } else {
                                    newTarget.focus();
                                }
                            }
                        }
                    } else if (targetEl.hasClass('ui-datepicker-month')) { // month dropdown
                        newTarget = $('.ui-datepicker-year');
                        if (newTarget.is('select')) {
                            newTarget.focus();
                        } else {
                            $('.ui-datepicker-next').focus();
                        }
                    } else if (targetEl.hasClass('ui-datepicker-year')) { // year dropdown
                        $('.ui-datepicker-next').focus();
                    } else if (targetEl.hasClass('ui-datepicker-next')) { // next button
                        activeDate = $('.ui-state-highlight') || $('.ui-state-active');
                        if (activeDate.length > 0) {
                            activeDate.focus();
                        } else {
                            $('.ui-datepicker-prev').focus();
                        }
                    }
                } else if (key === 37) { // LEFT arrow key
                    if (!targetEl.hasClass('ui-datepicker-close') && !targetEl.hasClass('ui-datepicker-current') && targetEl.hasClass('ui-state-default')) {
                        movePreviousDay(target);
                    }

                    if (targetEl.is(':not(select)')) {
                        e.preventDefault();
                    }
                } else if (key === 39) { // RIGHT arrow key
                    if (!targetEl.hasClass('ui-datepicker-close') && !targetEl.hasClass('ui-datepicker-current') && targetEl.hasClass('ui-state-default')) {
                        moveNextDay(target);
                    }

                    if (targetEl.is(':not(select)')) {
                        e.preventDefault();
                    }
                } else if (key === 38) { // UP arrow key
                    if (!targetEl.hasClass('ui-datepicker-close') && !targetEl.hasClass('ui-datepicker-current') && targetEl.hasClass('ui-state-default')) {
                        moveUpHandler(target, calendarDiv);
                    }

                    if (targetEl.is(':not(select)')) {
                        e.preventDefault();
                    }
                } else if (key === 40) { // DOWN arrow key
                    if (!targetEl.hasClass('ui-datepicker-close') && !targetEl.hasClass('ui-datepicker-current') && targetEl.hasClass('ui-state-default')) {
                        moveDownHandler(target, calendarDiv);
                    }

                    if (targetEl.is(':not(select)')) {
                        e.preventDefault();
                    }
                } else if (32 === key) { // SPACE
                    if (targetEl.hasClass('ui-datepicker-prev') || targetEl.hasClass('ui-datepicker-next') || (!targetEl.hasClass('ui-datepicker-close') && !targetEl.hasClass('ui-datepicker-current') && targetEl.hasClass('ui-state-default'))) {
                        e.preventDefault();
                        target.click();
                    }
                } else if (33 === key) { // PAGE UP
                    if (!targetEl.hasClass('ui-datepicker-close') && !targetEl.hasClass('ui-datepicker-current') && targetEl.hasClass('ui-state-default')) {
                        moveNextOrPrevMonth(target, 'prev');
                    }

                    if (targetEl.is(':not(select)')) {
                        e.preventDefault();
                    }
                } else if (34 === key) { // PAGE DOWN
                    if (!targetEl.hasClass('ui-datepicker-close') && !targetEl.hasClass('ui-datepicker-current') && targetEl.hasClass('ui-state-default')) {
                        moveNextOrPrevMonth(target, 'next');
                    }

                    if (targetEl.is(':not(select)')) {
                        e.preventDefault();
                    }
                } else if (36 === key) { // HOME
                    if (!targetEl.hasClass('ui-datepicker-close') && !targetEl.hasClass('ui-datepicker-current') && targetEl.hasClass('ui-state-default')) {
                        firstOfMonth = targetEl.closest('tbody').find('td:not(.ui-datepicker-unselectable) > .ui-state-default')[0];
                        if (firstOfMonth) {
                            firstOfMonth.focus();
                            setHighlightedState(firstOfMonth, $('#ui-datepicker-div')[0]);
                        }
                    }

                    if (targetEl.is(':not(select)')) {
                        e.preventDefault();
                    }
                } else if (35 === key) { // END
                    if (!targetEl.hasClass('ui-datepicker-close') && !targetEl.hasClass('ui-datepicker-current') && targetEl.hasClass('ui-state-default')) {
                        daysOfMonth = targetEl.closest('tbody').find('td:not(.ui-datepicker-unselectable) > .ui-state-default');
                        lastDay = daysOfMonth[daysOfMonth.length - 1];
                        if (lastDay) {
                            lastDay.focus();
                            setHighlightedState(lastDay, $('#ui-datepicker-div')[0]);
                        }
                    }

                    if (targetEl.is(':not(select)')) {
                        e.preventDefault();
                    }
                }
            });
        }

    }

    ////////////////////////////////
    //// UTILITY-LIKE Functions ///
    //////////////////////////////
    if ('function' !== typeof moveNextOrPrevMonth) {
        window.moveNextOrPrevMonth = function (currentDate, dir) {
            var button = (dir === 'next')
                ? $('.ui-datepicker-next')[0]
                : $('.ui-datepicker-prev')[0];

            if (!button) {
                return;
            }

            var ENABLED_SELECTOR = '#ui-datepicker-div tbody td:not(.ui-state-disabled)';
            var $currentCells = $(ENABLED_SELECTOR);
            var currentIdx = $.inArray(currentDate.parentNode, $currentCells);

            button.click();
            setTimeout(function () {
                var $newCells = $(ENABLED_SELECTOR);
                var newTd = $newCells[currentIdx];
                var newAnchor = newTd && $(newTd).find('a')[0];

                while (!newAnchor) {
                    currentIdx--;
                    newTd = $newCells[currentIdx];
                    newAnchor = newTd && $(newTd).find('a')[0];
                }

                setHighlightedState(newAnchor, $('#ui-datepicker-div')[0]);
                newAnchor.focus();

            }, 0);

        }
    }

    ///////////////// PREV-NEXT CLICK FUNCTIONS /////////////////
    if ('function' !== typeof handleNextBtnClicks) {
        window.handleNextBtnClicks = function () {
            setTimeout(function () {
                prepHighlightedDate();
                var nextBtn = $('.ui-datepicker-next');
                if (nextBtn.hasClass("ui-state-disabled")) {
                    getHighlightedDate($('#ui-datepicker-div')[0]).focus();
                } else {
                    nextBtn.focus();
                }
            }, 0);
        }
    }

    if ('function' !== typeof handlePrevBtnClicks) {
        window.handlePrevBtnClicks = function () {
            setTimeout(function () {
                prepHighlightedDate();
                var prevBtn = $('.ui-datepicker-prev');
                if (prevBtn.hasClass("ui-state-disabled")) {
                    getHighlightedDate($('#ui-datepicker-div')[0]).focus();
                } else {
                    prevBtn.focus();
                }
            }, 0);
        }
    }

    ///////////////// PREVIOUS FUNCTIONS /////////////////
    if ('function' !== typeof movePreviousDay) {
        window.movePreviousDay = function (dateLink) {
            var container = $("#ui-datepicker-div")[0];
            if (!dateLink) {
                return;
            }
            var td = $(dateLink).closest('td');
            if (!td) {
                return;
            }

            var prevTd = $(td).prevAll(':not(.ui-datepicker-unselectable)').first(),
                prevDateLink = $('a.ui-state-default', prevTd)[0];

            if (prevTd && prevDateLink) {
                setHighlightedState(prevDateLink, container);
                prevDateLink.focus();
            } else {
                movePreviousWeek(dateLink);
            }
        }
    }

    if ('function' !== typeof movePreviousWeek) {
        window.movePreviousWeek = function (target) {
            var container = $("#ui-datepicker-div")[0];
            if (!target) {
                return;
            }
            var currentRow = $(target).closest('tr');
            if (!currentRow) {
                return;
            }
            var previousRow = $(currentRow).prev();

            if (!previousRow || previousRow.length === 0) {
                // there is not previous row, so we go to previous month...
                movePreviousMonth();
            } else {
                var prevRowDates = $('td a.ui-state-default', previousRow);
                var prevRowDate = prevRowDates[prevRowDates.length - 1];

                if (prevRowDate) {
                    setTimeout(function () {
                        setHighlightedState(prevRowDate, container);
                        prevRowDate.focus();
                    }, 0);
                }
            }
        }
    }

    if ('function' !== typeof movePreviousMonth) {
        window.movePreviousMonth = function () {
            var prevLink = $('.ui-datepicker-prev')[0];
            var container = $("#ui-datepicker-div")[0];
            prevLink.click();
            // focus last day of new month
            setTimeout(function () {
                var trs = $('tr', container),
                    lastRowTdLinks = $('td a.ui-state-default', trs[trs.length - 1]),
                    lastDate = lastRowTdLinks[lastRowTdLinks.length - 1];

                if (lastDate != undefined) {
                    setHighlightedState(lastDate, container);
                    lastDate.focus();
                }

            }, 0);
        }
    }

    ///////////////// NEXT FUNCTIONS /////////////////
    if ('function' !== typeof moveNextDay) {
        window.moveNextDay = function (dateLink) {
            var container = $("#ui-datepicker-div")[0];
            if (!dateLink) {
                return;
            }
            var td = $(dateLink).closest('td');
            if (!td) {
                return;
            }
            var nextTd = $(td).nextAll(':not(.ui-datepicker-unselectable)').first(),
                nextDateLink = $('a.ui-state-default', nextTd)[0];

            if (nextTd && nextDateLink) {
                setHighlightedState(nextDateLink, container);
                nextDateLink.focus(); // the next day (same row)
            } else {
                moveNextWeek(dateLink);
            }
        }
    }

    if ('function' !== typeof moveNextWeek) {
        window.moveNextWeek = function (target) {
            var container = $("#ui-datepicker-div")[0];
            if (!target) {
                return;
            }
            var currentRow = $(target).closest('tr'),
                nextRow = $(currentRow).next();

            if (!nextRow || nextRow.length === 0) {
                moveNextMonth();
            } else {
                var nextRowFirstDate = $('a.ui-state-default', nextRow)[0];
                if (nextRowFirstDate) {
                    setHighlightedState(nextRowFirstDate, container);
                    nextRowFirstDate.focus();
                }
            }
        }
    }

    if ('function' !== typeof moveNextMonth) {
        window.moveNextMonth = function () {
            nextMon = $('.ui-datepicker-next')[0];
            var container = $("#ui-datepicker-div")[0];
            nextMon.click();
            // focus the first day of the new month
            setTimeout(function () {
                var firstDate = $('a.ui-state-default', container)[0];

                if (firstDate != undefined) {
                    setHighlightedState(firstDate, container);
                    firstDate.focus();
                }

            }, 0);
        }
    }

    if ('function' !== typeof moveUpHandler) {
        /////////// UP KEY FUNCTION ///////////
        window.moveUpHandler = function (target, cont) {
            var prevLink = $('.ui-datepicker-prev')[0];
            var rowContext = $(target).closest('tr');
            if (!rowContext) {
                return;
            }
            var rowTds = $('td', rowContext),
                rowLinks = $('a.ui-state-default', rowContext),
                targetIndex = $.inArray(target, rowLinks),
                prevRow = $(rowContext).prev(),
                prevRowTds = $('td', prevRow),
                parallel = prevRowTds[targetIndex],
                linkCheck = $('a.ui-state-default', parallel)[0];

            if (prevRow && parallel && linkCheck) {
                // there is a previous row, a td at the same index
                // of the target AND theres a link in that td
                setHighlightedState(linkCheck, cont);
                linkCheck.focus();
            } else {
                // we're either on the first row of a month, or we're on the
                // second and there is not a date link directly above the target
                if (!$(prevLink).hasClass("ui-state-disabled")) {
                    prevLink.click();
                    setTimeout(function () {
                        var newRows = $('tr', cont),
                            lastRow = newRows[newRows.length - 1],
                            lastRowTds = $('td', lastRow),
                            tdParallelIndex = $.inArray(target.parentNode, rowTds),
                            newParallel = lastRowTds[tdParallelIndex],
                            newCheck = $('a.ui-state-default', newParallel)[0];

                        if (lastRow && newParallel && newCheck) {
                            setHighlightedState(newCheck, cont);
                            newCheck.focus();
                        } else {
                            // theres no date link on the last week (row) of the new month
                            // meaning its an empty cell, so we'll try the 2nd to last week
                            var secondLastRow = newRows[newRows.length - 2],
                                secondTds = $('td', secondLastRow),
                                targetTd = secondTds[tdParallelIndex],
                                linkCheck = $('a.ui-state-default', targetTd)[0];

                            if (linkCheck) {
                                setHighlightedState(linkCheck, cont);
                                linkCheck.focus();
                            }

                        }
                    }, 0);
                }
            }
        }
    }

    if ('function' !== typeof moveDownHandler) {
        //////////////// DOWN KEY FUNCTION ////////////////
        window.moveDownHandler = function (target, cont) {
            var nextLink = $('.ui-datepicker-next')[0];
            var targetRow = $(target).closest('tr');
            if (!targetRow) {
                return;
            }
            var targetCells = $('td', targetRow),
                cellIndex = $.inArray(target.parentNode, targetCells), // the td (parent of target) index
                nextRow = $(targetRow).next(),
                nextRowCells = $('td', nextRow),
                nextWeekTd = nextRowCells[cellIndex],
                nextWeekCheck = $('a.ui-state-default', nextWeekTd)[0];

            if (nextRow && nextWeekTd && nextWeekCheck) {
                // theres a next row, a TD at the same index of `target`,
                // and theres an anchor within that td
                setHighlightedState(nextWeekCheck, cont);
                nextWeekCheck.focus();
            } else {
                if (!$(nextLink).hasClass("ui-state-disabled")) {
                    nextLink.click();
                    setTimeout(function () {
                        var nextMonthTrs = $('tbody tr', cont),
                            firstTds = $('td', nextMonthTrs[0]),
                            firstParallel = firstTds[cellIndex],
                            firstCheck = $('a.ui-state-default', firstParallel)[0];

                        if (firstParallel && firstCheck) {
                            setHighlightedState(firstCheck, cont);
                            firstCheck.focus();
                        } else {
                            // lets try the second row b/c we didnt find a
                            // date link in the first row at the target's index
                            var secondRow = nextMonthTrs[1],
                                secondTds = $('td', secondRow),
                                secondRowTd = secondTds[cellIndex],
                                secondCheck = $('a.ui-state-default', secondRowTd)[0];

                            if (secondRow && secondCheck) {
                                setHighlightedState(secondCheck, cont);
                                secondCheck.focus();
                            }
                        }
                    }, 0);
                }

            }
        }
    }

    if ('function' !== typeof prepHighlightedDate) {
        // Set the highlighted class to date elements, upon changing months
        window.prepHighlightedDate = function () {
            var highlight;
            var cage = $("#ui-datepicker-div")[0];
            highlight = $('.ui-state-highlight', cage)[0] ||
                $('td:not(.ui-datepicker-unselectable) > .ui-state-default', cage)[0];
            if (highlight && cage) {
                setHighlightedState(highlight, cage);
            }
        }

    }

    if ('function' !== typeof setHighlightedState) {
        // Set the highlighted class to date elements, when focus is recieved
        window.setHighlightedState = function (newHighlight, container) {
            $(getHighlightedDate(container)).removeClass('ui-state-highlight');
            $(newHighlight).addClass('ui-state-highlight');
        }
    }

    if ('function' !== typeof preventSpaceKeydown) {
        // this blocks the space (32) key
        window.preventSpaceKeydown = function (e) {
            var key = e.which || e.keyCode || 0;

            if (32 === key) {
                e.preventDefault();
                e.stopPropagation();
            }
        }
    }

    if ('function' !== typeof setSelectedState) {
        // Set the highlighted class to date elements, when focus is recieved
        window.setSelectedState = function (newHighlight, container) {
            $('.ui-state-active').removeAttr('aria-selected');
            $(newHighlight).attr('aria-selected', 'true');
        }
    }

    if ('function' !== typeof updateBtnLabel) {
        // Set the highlighted class to date elements, when focus is recieved
        window.updateBtnLabel = function (currentDate) {
            var targetBtn = $.datepicker._curInst.input.next('button.ui-datepicker-trigger'),
                selectedText = getIsFrench() ? ', Date s�lectionn�e: ' : ', Selected Date: ',
                label = currentDate.attr("aria-label").split(" ");
            if (targetBtn.length > 0) {
                targetBtn.attr("aria-label", targetBtn.attr("aria-label").split(",")[0] + selectedText + [label[0], label[1], label[2]].join(" "));
            }
        }
    }

    // grabs the current date based on the highlight class
    function getHighlightedDate(container) {
        return $('.ui-state-highlight', container)[0];
    }

    // returns the id of the passed input's label. if the label has no id, a new one is set and returned
    function getInputLabelId(input) {
        var inputLabel = input.attr('aria-labelledby'), id;

        if (inputLabel != null && inputLabel.length > 0) {
            return inputLabel;
        }

        inputLabel = input.closest('label');
        if (inputLabel.length === 0) {
            inputLabel = $('label[for=' + input.attr('id') + ']');
        }

        if (inputLabel.length > 0) {
            id = inputLabel.attr('id');
            if (id != null && id.length > 0) {
                return id;
            }

            id = 'label-' + (new Date()).getTime();
            inputLabel.attr('id', id);
            return id;
        }

        return null;
    }
}

//---------------------- END - Page/Component Specific Functions -----------------------//

//Function that contains all the function that needs to be initialize on after widget loads
var loadingInterval;
function initAfterWidgetLoading() {
    loadingInterval = setInterval(function () {
        var loaderConfig = window.sk_trackingConfig,
            loader = loaderConfig != undefined ? $(loaderConfig.spinnerSelector) : $("#brfLoadingIndicator");

        if (loader.length < 1) {
            clearInterval(loadingInterval);
        }

        if (!loader.is(":visible")) {
            clearInterval(loadingInterval);

            //Initialize tooltip after loading
            $('.initialize-after-load.tooltip-interactive:not(.tooltip-initialized)').each(function () {
                var $this = $(this);
                $this.tooltip({ container: $this });
                $this.addClass("tooltip-initialized");
            });

            setElementsClassesOnReady();
            triggerMobileAccordion();
            addColorContrastClass();
            replaceDuplicateIDs();
            tooltipOverviewPage();
            tooltipMyPlanPage();
            updateRadioGroup();
            moveLinkOverride();
            modifyPNPModal();
            removeTagRole();
        }
    }, 1000);
}

//Function that contains all the function that needs to be initialize on page load
function initOnPageLoad() {

    checkNavIfNotDesktop();
}

$(document).ready(function () {

    //Function that contains all the function that needs to be initialize on page load
    initOnPageLoad();
    initAfterWidgetLoading();


    //----------------------CUSTOM EVENT HANDLER FOR NAV----------------------//
    //Adding aria expanded attribute to the connector flyout link for desktop
    $('.accss-styles-init header .show-flyout-onfocus-virgin').not('.focus-initialized').addClass('focus-initialized').on('focus', function (e) {
        var $flyOutTriggerLink = $(this);

        setTimeout(function () {
            var windowWidth = $(window).outerWidth(),
                isNavResponsive = ($('.accss-styles-init header.gn-non-responsive').length === 0);

            if (windowWidth >= 1000 || !isNavResponsive) {
                $flyOutTriggerLink.attr('aria-expanded', true);
            }
        }, 0);
    }).not('.blur-initialized').addClass('blur-initialized').on('blur', function (e) {
        var $flyOutTriggerLink = $(this),
            $nextEl = $(e.relatedTarget === null ? document.activeElement : e.relatedTarget);

        setTimeout(function () {
            var windowWidth = $(window).outerWidth(),
                isNavResponsive = ($('.accss-styles-init header.gn-non-responsive').length === 0);

            if ((!$nextEl.parents('.connector-lob-flyout').length > 0 && windowWidth >= 1000) || !isNavResponsive) {
                $flyOutTriggerLink.attr('aria-expanded', false);
            }
        }, 0);
    });

    //Adding aria expanded attribute to the connector flyout link upon closing the flyout for desktop
    $('.accss-styles-init header .show-flyout-onfocus-virgin ~ .connector-lob-flyout a').not('.focus-initialized').addClass('focus-initialized').on('focus', function (e) {
        var $flyOutTriggerLink = $(this).parents('.connector-lob-flyout').siblings('.show-flyout-onfocus-virgin');
        setTimeout(function () {
            var windowWidth = $(window).outerWidth(),
                isNavResponsive = ($('.accss-styles-init header.gn-non-responsive').length === 0);

            if (windowWidth >= 1000 || !isNavResponsive) {
                $flyOutTriggerLink.attr('aria-expanded', true);
            }
        }, 0);
    }).not('.blur-initialized').addClass('blur-initialized').on('blur', function (e) {
        var $flyOutTriggerLink = $(this).parents('.connector-lob-flyout').siblings('.show-flyout-onfocus-virgin'),
            $nextEl = $(e.relatedTarget === null ? document.activeElement : e.relatedTarget);
        setTimeout(function () {
            var windowWidth = $(window).outerWidth(),
                isNavResponsive = ($('.accss-styles-init header.gn-non-responsive').length === 0);

            if ((!$nextEl.parents('.connector-lob-flyout').length > 0 && windowWidth >= 1000) || !isNavResponsive) {
                $flyOutTriggerLink.attr('aria-expanded', false);
            }
        }, 0);
    });

    //Adding aria expanded attribute to the connector flyout link on mobile
    $('.accss-styles-init header .show-flyout-onfocus-virgin').not('.click-initialized').addClass('click-initialized').on('click touch', function (e) {
        var $flyOutTriggerLink = $(this),
            windowWidth = $(window).outerWidth(),
            isNavResponsive = ($('.accss-styles-init header.gn-non-responsive').length === 0);

        if (windowWidth < 1000 && isNavResponsive) {
            $flyOutTriggerLink.siblings('.connector-lob-flyout').find('.connector-lob-flyout-content li.connector-lob > a[role="button"]').not(".no-flyout-menu").attr('aria-expanded', false);
            toggleAriaExpanded($flyOutTriggerLink);
        }
    }).not('.keydown-initialized').addClass('keydown-initialized').on('keydown', function (e) {
        if (32 === (e.which || e.keyCode || 0)) {
            clickPreventDefault(e, this);
        }
    });

    //Adding aria expanded attribute to trigger-popup in the global nav
    $('.accss-styles-init header .login-register-button').on('click touch', function (e) {
        var loginBtn = $(this);
        toggleAriaExpanded(loginBtn);
        //setTimeout(function () {
        //    loginBtn.next(".connector-login-modal").find(focusableEl).first().focus();
        //}, 0)

    });

    $('.accss-styles-init header .connector-login-modal > a').not('.blur-initialized').addClass('blur-initialized').on('blur', function (e) {
        var $nextEl = $(e.relatedTarget === null ? document.activeElement : e.relatedTarget);

        if (!$nextEl.parents('.connector-login-modal').length > 0) {
            $('.connector-login-modal').removeClass('show active');
            $('.login-register-button').attr("aria-expanded", "false");
        }
    });

    //$('.accss-styles-init header .connector-login-modal > a').on('keydown', function (e) {
    //    var key = e.which || e.keyCode || 0,
    //        activeLink = $(this);

    //    e.preventDefault();
    //    switch (key) {
    //        case 38:
    //            if (activeLink.prev().length > 0) {
    //                activeLink.prev("a").focus();
    //            }
    //            break;
    //        case 40:
    //            if (activeLink.next().length > 0) {
    //                activeLink.next("a").focus();
    //            }
    //            break;
    //        case 13: 
    //        case 32:
    //            activeLink.click();
    //            break;
    //        default:
    //    }
    //});    
    //End - Adding aria expanded attribute to trigger-popup in the global nav

    //Adding aria expanded attribute to secondary-nav-lob in the global nav    
    $('.accss-styles-init header .connector-active-secondary-nav li').on('mouseenter', function (e) {
        var activeDropdown = $('.dropdown-active'),
            dropdownTrigger = $(this).find(".secondary-nav-lob").not(".no-flyout-menu");

        activeDropdown.attr("aria-expanded", "false").removeClass("dropdown-active");
        dropdownTrigger
            .addClass("dropdown-active")
            .attr("aria-expanded", "true");
    }).on('mouseleave', function (e) {
        var activeEl = $(e.relatedTarget === null ? document.activeElement : e.relatedTarget);
        if (!activeEl.parents(".secondary-nav-dropdown").length > 0) {
            var dropdownTrigger = $(this).find(".secondary-nav-lob").not(".no-flyout-menu");
            dropdownTrigger
                .removeClass("dropdown-active")
                .attr("aria-expanded", "false");
        }
    });

    //Adding esc keyboard event for the secondary nav links and secondary nav dropdown links 
    $('.accss-styles-init header .secondary-nav-lob:not(".no-flyout-menu"), .accss-styles-init header .secondary-nav-dropdown a.services-selection').not('.keydown-initialized').addClass('keydown-initialized').on('keydown', function (e) {
        var $dropdownTrigger = $(this),
            key = e.which || e.keyCode || 0;

        if ($dropdownTrigger.parents('.secondary-nav-dropdown').length > 0) {
            $dropdownTrigger = $dropdownTrigger.parents('.secondary-nav-dropdown').siblings('.trigger-dropdown');
        }

        switch (key) {
            case 27:
                $dropdownTrigger
                    .removeClass("dropdown-active")
                    .attr("aria-expanded", "false")
                    .next(".secondary-nav-dropdown")
                    .hide();
                break;
            default:
        }
    });

    $('.accss-styles-init header .secondary-nav-lob:not(".no-flyout-menu")').on('focus', function (e) {
        var dropdownTrigger = $(this);
        $('.dropdown-active').removeClass("dropdown-active");
        dropdownTrigger
            .addClass("dropdown-active")
            .attr("aria-expanded", "true");
    }).on('blur', function (e) {
        var activeEl = $(e.relatedTarget === null ? document.activeElement : e.relatedTarget),
            dropdownTrigger = $(this);
        if (!activeEl.parents(".secondary-nav-dropdown").length > 0) {
            dropdownTrigger
                .removeClass("dropdown-active")
                .attr("aria-expanded", "false")
                .next(".secondary-nav-dropdown")
                .hide();
        }
    });

    $('.accss-styles-init header .services-selection').on('blur', function (e) {
        var activeEl = $(e.relatedTarget === null ? document.activeElement : e.relatedTarget);
        if (!activeEl.parents(".secondary-nav-dropdown").length > 0) {
            var trigger = $(this).parents(".secondary-nav-dropdown").prev(".secondary-nav-lob");
            trigger
                .removeClass("dropdown-active")
                .attr("aria-expanded", "false")
                .next(".secondary-nav-dropdown")
                .hide();
        }
    });
    //End - Adding aria expanded attribute to secondary-nav-lob in the global nav 

    //Showing/Hiding Mobile Global Nav Panel
    $('.accss-styles-init header .connector-nav-open-button').on('click', function (e) {
        var trigger = $(this),
            targetPanel = $("#" + trigger.attr("aria-controls"));
        applyNavFocusTrapping(targetPanel, trigger);
    });
    //End - Showing/Hiding Mobile Global Nav Panel

    //----------------------END CUSTOM EVENT HANDLER FOR NAV----------------------//

    //----------------------DOCUMENT/WINDOW EVENT HANDLER----------------------//
    $(document).on('click touch', function (event) {
        var tagetPopup = $('.accss-styles-init header .connector-login-modal').not(this),
            mobNavTrigger = $('.accss-styles-init header .connector-nav-open-button'),
            mobNavPanel = $("#" + mobNavTrigger.attr("aria-controls"));

        //toggle aria when clicking outside connector login modal
        if (!$(event.target).parents().addBack().is('.login-register-button') && tagetPopup.is(":visible")) {
            toggleAriaExpanded(tagetPopup.prev());
            tagetPopup.removeClass('show active');
        }

        //Remove mobile nav trapping when click on the overlay        
        if (!$(event.target).parents().addBack().is('.header-outline-override')) {
            if ($(event.target).hasClass("screen")) {
                applyNavFocusTrapping(mobNavPanel, mobNavTrigger);
            }
        }

        //hide show-flyout-onfocus-virgin flyout        
        if (!$(event.target).parents().addBack().is('.show-flyout-onfocus-virgin')) {
            $('.connector-area.hover').removeClass('hover');
        }
    });


    var resizeTimeout;
    $(window).resize(function (e) {
        //checking for the screensize of tablet and Mobile
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(checkNavIfNotDesktop, 200);
    });
    //----------------------DOCUMENT/WINDOW EVENT HANDLER----------------------//
    // ---------- ACCSS 1988, 1989, 2405 SET alt="" --------------------------//
    $('.accss-styles-init .slick-slide img:not([alt])').attr('alt', '');

    // ---------- DCX  111 --------------------------//
    $('.myusage .chart_cols').attr('tabindex', '-1');
    $('.myusage .six-month-trend-bar').children('div').attr('tabindex', '-1');

    // ---------- DCX  49 --------------------------//
    $('.vPadding07 .cut_txt.btn-primary').addClass('accss-header-btn-pad');

    // ---------- ACCSS 1983 Aria-Selected --------------------------//
    $(".slick-slide").focusin(function () {
        $(this).siblings().attr('aria-selected', false);
        $(this).attr('aria-selected', true);

    });

});

//----------------------UTILITY EVENT HANDLER----------------------//
function utilityKeyEvents() {
    // attach a keydown listener to allow firing of click events using the ENTER key for elements that uses this generic class
    $('.click-on-enter:not(.click-on-enter-initialized)')
        .addClass('click-on-enter-initialized')
        .on('keydown', function (e) {
            if (13 === (e.which || e.keyCode || 0)) {
                clickPreventDefault(e, this);
            }
        });

    // attach a keydown listener to allow firing of click events using the SPACE key for elements that uses this generic class
    $('.click-on-space:not(.click-on-space-initialized)')
        .addClass('click-on-space-initialized')
        .on('keydown', function (e) {
            if (32 === (e.which || e.keyCode || 0)) {
                clickPreventDefault(e, this);
            }
        });

    // attach a keydown listener to allow firing of click events using the ENTER or SPACE key for elements that uses this generic class.
    $('.click-on-enter-or-space:not(.click-on-enter-or-space-initialized)')
        .addClass('click-on-enter-or-space-initialized')
        .on('keydown', function (e) {
            var key = e.which || e.keyCode || 0;

            if (13 === key || 32 === key) {
                clickPreventDefault(e, this);

            }
        });
}
//----------------------UTILITY EVENT HANDLER----------------------//

//---------------------- START - Functions for setting classes, aria-label and alt attributes of elements from different pages -----------------------//

//ACCSS-1430
//function for setting the classes/attributes for specific elements on on page load
var loadingIntervalCheckElement;
function setElementsClassesOnReady() {
    loadingIntervalCheckElement = setInterval(function () {

        var sliderElement = $('.accss-styles-init .myaccount-change-feature .wss-slick-slider-data.slick-initialized');

        if (sliderElement.length > 0) {
            clearInterval(loadingIntervalCheckElement);
            //ACCSS-1430 - remove and replace attribtues
            var sliderRadioGroup = $(".accss-styles-init .myaccount-change-feature .wss-slick-slider-data .slick-track"),
                sliderRadioItem = $(".accss-styles-init .myaccount-change-feature .wss-slick-slider-data .slick-track .slick-slide");

            sliderRadioGroup.removeAttr("role", "listbox");
            sliderRadioGroup.attr("role", "radiogroup");
            sliderRadioItem.removeAttr("role", "option");

            //ACCSS-1430 - remove and replace attribtues using slick function           
            sliderElement.on('setPosition', function (event, slick) {
                setTimeout(function () {
                    sliderRadioGroup.removeAttr("role", "listbox");
                    sliderRadioGroup.attr("role", "radiogroup");
                    sliderRadioItem.removeAttr("role", "option");
                }, 1000);
            });
        }
    }, 1000);
}

//ACCSS-1018 - add target classs for the accordion
//Accordion expand/collapse
//Added defect - ACCSS-1027
function addAriaExpandedSpecificPage() {
    var $accordionGroup = $('.accss-styles-init .accordion-group'),
        $accordionToggle = $accordionGroup.find('.accordion-heading > div.accordion-toggle'),
        $accordionContent = $accordionGroup.find('.accordionContent');
    if ($accordionContent.hasClass('slideUp')) {
        $accordionContent.attr('aria-hidden', 'true');
        $accordionContent.find('button').attr('tabindex', '-1');
    }

    $accordionToggle.attr('role', 'button');
    $accordionToggle.on("click", function () {
        var $this = $(this),
            $toggleIcon = $this.find('.icon-plus-solid').first();

        if ($toggleIcon.hasClass("icon-collapse-outline-circled")) {
            $this.attr('aria-expanded', "false");
            $accordionContent.attr('aria-hidden', 'true');
            $accordionContent.find('button').attr('tabindex', '-1');
        } else if ($toggleIcon.not("icon-collapse-outline-circled")) {
            $this.attr('aria-expanded', "true");
            $accordionContent.removeAttr('aria-hidden');
            $accordionContent.find('button').removeAttr('tabindex');
        }
    });
}

/* ACCSS-2869 */
function updateRadioGroup() {
    setTimeout(function () {
        var featureRadioGroup = $(".myaccount-feature-selection #tab1 .slick-track");

        if (featureRadioGroup.length) {
            featureRadioGroup.attr("aria-labelledby", "radio-slick-legend");
            featureRadioGroup.find(".col-box").attr("aria-hidden", "false");
            featureRadioGroup.prepend("<legend id=\"radio-slick-legend\" class=\"sr-only\">Select One</legend>");
        }
    }, 2000);
}

function moveLinkOverride() {
    var tabContainer = $(".tab_container.tab_container_accordion.col1.bgWhite");

    if (tabContainer.length) {
        tabContainer.removeClass("accss-link-override");
        tabContainer.find("#tab1").addClass("accss-link-override");
        tabContainer.find("#tab2").addClass("accss-link-override");
        tabContainer.find("#tab3").addClass("accss-link-override");
        tabContainer.find("#tab4").addClass("accss-link-override");
        tabContainer.find("#tab5").addClass("accss-link-override");
        tabContainer.find("#tab6").addClass("accss-link-override");
    }
}

/* ACCVIRDCX-20 */
function removeTagRole() {
    if ($("#inqC2CImgContainer_AnchoredV > input[type=\"image\"]").length) {
        $("#inqC2CImgContainer_AnchoredV > input[type=\"image\"]").removeAttr("role");
    }

    setTimeout(function () {
        if ($(".myaccount-feature-selection .wss-slick-slider-data.col-eq.slick-initialized.slick-slider").length) {
            $(".myaccount-feature-selection .wss-slick-slider-data.col-eq.slick-initialized.slick-slider .slick-prev.slick-arrow").removeAttr("role");
            $(".myaccount-feature-selection .wss-slick-slider-data.col-eq.slick-initialized.slick-slider .slick-next.slick-arrow").removeAttr("role");
        }
    }, 500);
}

/* ACCVIRDCX-75 */
function replaceDuplicateIDs() {
    var duplicateArray = ["btnLinkData_SO_DVMVC3", "notificationBox_sm_learnMore", "flexDataPricingLB_closetier", "add_data_flex_overage", "parentClassData2", "btn_ManageUsage", "lightboxSize"];

    for (var x = 0; x < duplicateArray.length; x++) {
        var duplicateElement = $("[id=" + duplicateArray[x] + "]");

        if (duplicateElement.length) {
            if ($(duplicateElement[0]).is(":hidden") == true && $(duplicateElement[1]).is(":hidden") == true) {
                $(duplicateElement[1]).attr("id", duplicateElement[1].id + "_alt");
            } else {
                for (var y = 0; y < duplicateElement.length; y++) {
                    if ($(duplicateElement[y]).is(":hidden") == true) {
                        $(duplicateElement[y]).attr("id", duplicateElement[y].id + "_alt");
                    }
                }
            }
        }
    }
}

/* ACCVIRDCX-17 */
function tooltipOverviewPage() {
    if ($("#mob-overview-plan-addons .icon-info-solid.tooltip-interactive").length) {
        $("#mob-overview-plan-addons .icon-info-solid.tooltip-interactive").each(function () {
            var targetTooltip = this;
            var targetOffset = $(targetTooltip).offset();

            var observer = new WebKitMutationObserver(function (mutations) {
                mutations.forEach(function (mutation) {
                    if ($(targetTooltip).find(".clipped").length && $(targetTooltip).find(".tooltip .tooltip-inner").length) {
                        if ($(targetTooltip).find(".clipped").attr("aria-hidden") == undefined) {
                            $(targetTooltip).find(".clipped").attr("aria-hidden", "true");
                        }

                        if (!$(targetTooltip).find(".tooltip .tooltip-inner .sr-only").length) {
                            $(targetTooltip).find(".tooltip .tooltip-inner").append(" <span class=\"sr-only\">tooltip</span>");
                        }

                        $(targetTooltip).keydown(function (e) {
                            if (e.keyCode == 13 || e.keyCode == 32) {
                                e.preventDefault();
                                if (!$("#" + $(targetTooltip).attr("aria-describedby")).length) {
                                    $(targetTooltip).tooltip().mouseover();

                                    $(targetTooltip).attr("aria-expanded", "true");
                                } else {
                                    $("#" + $(targetTooltip).attr("aria-describedby")).tooltip("hide");

                                    $(targetTooltip).attr("aria-expanded", "false");
                                }
                            } else if (e.keyCode == 27) {
                                if ($("#" + $(targetTooltip).attr("aria-describedby")).length) {
                                    $("#" + $(targetTooltip).attr("aria-describedby")).tooltip("hide");

                                    $(targetTooltip).attr("aria-expanded", "false");
                                }
                            }
                        });

                        $(targetTooltip).focusout(function () {
                            $("#" + $(targetTooltip).attr("aria-describedby")).tooltip("hide");
                        });
                    }
                });
            });

            observer.observe(targetTooltip, { attributes: true, childList: true, characterData: true, subtree: true });

            if ($(targetTooltip).attr("aria-expanded") == undefined) {
                $(targetTooltip).attr("aria-expanded", (($("#" + $(targetTooltip).attr("aria-describedby")).length)) ? "true" : "false");
            }

            $(targetTooltip).on("focusin mouseover", function () {
                $(targetTooltip).attr("aria-expanded", "true");
            });

            $(targetTooltip).on("focusout mouseout", function () {
                $(targetTooltip).attr("aria-expanded", "false");
            });
        });
    }
}

/* ACCVIRDCX-101 */
function tooltipMyPlanPage() {
    var duplicateArray = ["close_1", "tooltip_0", "tooltip_1", "tooltipModalMobileWSS-2"];

    for (var x = 0; x < duplicateArray.length; x++) {
        var duplicateElement = $("[id=" + duplicateArray[x] + "]");

        if (duplicateElement.length) {
            if ($(duplicateElement[0]).is(":hidden") == true && $(duplicateElement[1]).is(":hidden") == true) {
                $(duplicateElement[1]).attr("id", duplicateElement[1].id + "_alt");
            } else {
                for (var y = 0; y < duplicateElement.length; y++) {
                    if ($(duplicateElement[y]).is(":hidden") == true) {
                        $(duplicateElement[y]).attr("id", duplicateElement[y].id + "_alt");
                    }
                }
            }
        }
    }

    $("#myaccount-mobility-service .icon-info-solid.tooltip-interactive").each(function () {
        var targetTooltip = this;
        var e = $(this);

        if (e.attr("role") == "tooltip") {
            e.attr("role", "button");
        }

        if (($("#" + $(targetTooltip).attr("aria-describedby")).length)) {
            $(targetTooltip).attr("aria-expanded", "true");
        } else {
            $(targetTooltip).attr("aria-expanded", "false");
        }

        $(targetTooltip).on("focusin mouseover", function () {
            $(targetTooltip).attr("aria-expanded", "true");
        });

        $(targetTooltip).on("focusout mouseout", function () {
            $(targetTooltip).attr("aria-expanded", "false");
        });

        $(targetTooltip).keydown(function (e) {
            if (e.keyCode == 13 || e.keyCode == 32) {
                e.preventDefault();
                if (!$("#" + $(targetTooltip).attr("aria-describedby")).length) {
                    $(targetTooltip).tooltip().mouseover();

                    $(targetTooltip).attr("aria-expanded", "true");
                } else {
                    $("#" + $(targetTooltip).attr("aria-describedby")).tooltip("hide");

                    $(targetTooltip).attr("aria-expanded", "false");
                }
            } else if (e.keyCode == 27) {
                if ($("#" + $(targetTooltip).attr("aria-describedby")).length) {
                    $("#" + $(targetTooltip).attr("aria-describedby")).tooltip("hide");

                    $(targetTooltip).attr("aria-expanded", "false");
                }
            }
        });

        var observer = new WebKitMutationObserver(function (mutations) {
            mutations.forEach(function (mutation) {
                if (!$(targetTooltip).find(".tooltip .tooltip-inner .sr-only").length) {
                    $(targetTooltip).find(".tooltip .tooltip-inner").append(" <span class=\"sr-only\">tooltip</span>");
                }
            });
        });

        observer.observe(targetTooltip, { attributes: true, childList: true, characterData: true, subtree: true });
    });
}

/* ACCVIRDCX-127 */
function modifyPNPModal() {
    function callbackFunction(records) {
        records.forEach(function (record) {
            var list = record.addedNodes;
            var i = list.length - 1;

            for (; i > -1; i--) {
                if (list[i].nodeName === "DIV" && $(list[i]).attr("role") == "dialog") {
                    console.log(list[i]);

                    var targetElement = list[i];

                    if ($(targetElement).find("#DataUsageCalculatorDayMonthBox").length) {
                        $(targetElement).find("#DataUsageCalculatorDayMonthBox").focusin(function () {
                            $(targetElement).find(".switch-container .switch-toggle").addClass("active-focus");
                        });

                        $(targetElement).find("#DataUsageCalculatorDayMonthBox").focusout(function () {
                            $(targetElement).find(".switch-container .switch-toggle").removeClass("active-focus");
                        });
                    }

                    if ($(targetElement).find("#Remove_0").length) {
                        $(targetElement).find("#Remove_0").attr("tabindex", "0");
                    }
                }
            }
        });
    }

    var observer = new MutationObserver(callbackFunction);
    var targetNode = document.body;

    observer.observe(targetNode, { childList: true, subtree: true });
}

function triggerMobileAccordion() {
    $("#hide-payment-detail-xs, #payment-detail-xs").click(function () {
        if ($("#hide-payment-detail-xs").attr("aria-expanded") == "true" && $("#payment-detail-xs").attr("aria-expanded") == "true") {
            $("#hide-payment-detail-xs").attr("aria-expanded", "false");
            $("#payment-detail-xs").attr("aria-expanded", "false");
        } else {
            $("#hide-payment-detail-xs").attr("aria-expanded", "true");
            $("#payment-detail-xs").attr("aria-expanded", "true");
        }
    });
}

function addColorContrastClass() {
    $("#viewBillDetails_SO_TCC1.icon.more-link").addClass("links-blue-on-bg-gray");
    $("#More.icon.more-link").addClass("links-blue-on-bg-gray");

    // ACCSS-1031 - Color contrast for reset filter link
    $('#usageDetailTabPanelDisplay a.reset_filters').addClass('links-blue-on-bg-white');
}

function accordionToggle() {
    $('.accss-show-hide-toggle').on('click', function () {
        var accssIcon;
        if ($(this).hasClass("toggleClose")) {
            $(this).removeClass("toggleClose").addClass("toggleOpen").children("span:not('.accss-icon')").html($(this).attr("data-hide-text"));
            accssIcon = $(this).children("i, .accss-icon")
            accssIcon.removeAttr('class').addClass(accssIcon.attr("data-alter-class"));
            $("#" + $(this).attr("data-href")).slideDown();
        } else {
            $(this).removeClass("toggleOpen").addClass("toggleClose").children("span:not('.accss-icon')").html($(this).attr("data-show-text"));
            accssIcon = $(this).children("i, .accss-icon");
            accssIcon.removeAttr('class').addClass(accssIcon.attr("data-new-class"));
            $("#" + $(this).attr("data-href")).slideUp();
        }
    });
}

function personalizationTilesLabel() {
    var $contentCont, $detailsLink, $detailsTag, $detailsTitle, $ctaLink, $ctaTag;
    $.each($('.personalization-slider-container .personalization-tile-content-container'), function () {
        $contentCont = $(this);
        $detailsLink = $contentCont.find('.personalization-details-link');
        $detailsTag = $contentCont.find('.personalization-tag').text();
        $detailsTitle = $contentCont.find('.personalization-title').text();

        $detailsLink.attr('aria-label', $detailsTag + ' ' + $detailsTitle + ' ' + $detailsLink.text());
        $detailsLink.removeAttr('aria-describedby');

        $ctaLink = $contentCont.find('.personalization-tile-cta-container a');
        $ctaTag = $contentCont.find('.personalization-tag .category').text();
        if ($contentCont.hasClass('personalization-tile-content-container double-tile')) {
            $ctaLink.attr('aria-label', $detailsTag + ' ' + $detailsTitle + ' ' + $ctaLink.text());
        } else if ($('.personalization-tile-cta-container').find('custom-separator').length > 0) {
            $ctaLink.attr('aria-label', $ctaLink.text() + ' for ' + $ctaTag);
        } else {
            $ctaLink.attr('aria-label', $ctaLink.text() + ' for ' + $ctaTag);
        }
        $ctaLink.removeAttr('aria-describedby');
        // change i tag to span tag - specifically on personalization tiles
        $ctaLinkIcon = $ctaLink.find('i');
        $ctaLinkIcon.replaceWith($('<span class="' + $ctaLinkIcon.attr('class') + '" aria-hidden="true"></span>'));
    });

    //Landmark - ACCVIRDCX-81
    $('.PersonalizationTilesContainer').each(function () {
        var $this = $(this);
        $this.attr('role', 'region');
        if ($this.find('#title-support-slider').length > 0) {
            $this.attr('aria-labelledby', 'title-support-slider');
        } else {
            $this.attr('aria-label', 'offers');
        }

        //My Support Articles Heading - ACCVIRDCX-191
        $('#title-support-slider').attr('role', 'heading');
        $('#title-support-slider').attr('aria-level', '2');

        //Remove Role - ACCVIRDCX-76
        $('.slick-track').removeAttr('role');
        $('.slick-slide').removeAttr('role');
    });

    //Bill Overview Take a Tour Header - ACCVIRDCX-86
    $('.guidedTour').each(function () {
        $('.tour-title').attr('role', 'heading');
        $('.tour-title').attr('aria-level', '2');
    });

    //Personalization Tiles Modal Focus Outline - Offer Zone Cancel Link
    //$('.offer-zone .personalization-modal-container').each(function () {
    //    $('a').attr('href', '#');
    //});
}

function widgetPersonalizationListboxLabel() {
    var $accssOffer = $('.accss-offer-container .slick-track'),
        $tag = $accssOffer.find('.accss-offer-tag').first().text();
    $accssOffer.attr('aria-label', $tag);
}

/* ACCSS-3195 and ACCSS-3198 */
function fixMyProfileTooltipMargin() {
    $('#editPassword_toolTipNewPassword, #editRecoveryMobile_ToolTip, #editRecoveryEmail_tooltip').on('hover focus', function () {
        var tooltip_id = $(this).attr('aria-describedby');
        $('#' + tooltip_id).addClass('accss-tooltip');
    });
}

/* ACCVIRDCX-156 */
function myProfileModalTrapping($modalTarget, $modalTrigger) {
    $modalTrigger.on('click', function () {
        overwriteTabIndexAndAriaHiddenDifferentHierarchy($modalTarget);
        $modalTarget.find('.close:visible').first().focus();
    });
    $modalTarget.find('[data-dismiss="modal"]').on('click', function () {
        revertTabIndexAndAriaHiddenDifferentHierarchy($modalTarget);
        $modalTrigger.focus();
    });
}

function myProfileTooltips() {
    $("#myProfile_editPassword_toolTip, #myProfile_editdigitalpin_toolTip, #myProfile_editRecoveryMobile_toolTip, #myProfile_editRecoveryEmail_toolTip, #myProfile_editSecretQuestion_toolTip").on('focus hover', function () {
        $(this).tooltip('show');
        $(this).attr('aria-live', 'polite');
    });
}

$(window).load(function () {
    setTimeout(function () {
        utilityKeyEvents();
        accordionToggle();
        addAriaExpandedSpecificPage();
        personalizationTilesLabel();
        widgetPersonalizationListboxLabel();
        fixMyProfileTooltipMargin();
        myProfileModalTrapping($("#logoutPopup"), $("#logoutCTAHeader"));
        myProfileModalTrapping($("#popUp"), $("#mrktPrfAddEmail"));
        myProfileTooltips();
    }, 3000);
});
