/* To be moved to allbrowser */ 

.lineheight18{line-height:18px}
.lineheight26{line-height:26px}
.letter-spacing-n1p33{letter-spacing:-1.33px}
.vPadding100{padding: 100px 0}
.box-shadow-gray{box-shadow:0 6px 25px 0 rgba(0,0,0,0.12)}
a:hover .txtOnlyDecoration_hover, a:focus .txtOnlyDecoration_hover {text-decoration: underline}

/* To be moved to allbrowser ends */
/* To be moved to global-nav */
.hoverBorderUnderlineWhite:hover {
    border-bottom: 2px solid #fff
}
.global-navigation .menu-flyout-visible .sub-nav-group.sub-nav-large {width: 230%;}
.social_icons_wrapper ul.social_icons_cont a:hover, .social_icons_wrapper ul.social_icons_cont a:focus{text-decoration:none}
.global-navigation .connector-area div > a {font-weight:500}
.connector-active-lob a > span {
    line-height: 1.1;
    margin-bottom: 0;
    margin-top: -4px;
}
/* To be moved to global-nav ends */ 

/* To be moved to bell */
.social_icons_wrapper ul.social_icons_cont li .icon_background{
    display:flex;
    align-items:center;
    justify-content: center;
}

.social_icons_wrapper .icon_background .icon:before{top:1px;left:0}
/* To be moved to bell ends*/

.iconContainer {
    height: 85px;
    width: 85px;
    border: 2px solid #fff;
}

.bottom-0{bottom:0}
.resourceCentreSearchBar{
    max-width: 400px;
    width: 80%;
}

/* Banner */
.bannerContainer2 {
    height: 250px;
    width: 100%;
    margin: 0;
    overflow: hidden;
    margin-top: -45px;
    /*box-shadow: inset 0 0 80px 30px rgba(0,0,0,0.05);*/
}

    .bannerContainer2 img {
        height: 100%;
        max-width: none;
    }

img.coverBanner{
    min-width: 1200px !important;
    height: auto !important;
    max-width: 100% !important;
}

.max-width-70p {
    max-width: 70%
}

.innerShadowContainer {
    box-shadow: inset 0 0 80px 30px rgba(0,0,0,0.05);
    width: 100%;
    height: 100%;
    top:0;
    z-index: 3;
}

/* Banner ends*/

/* Icon Alignment  */
.iconContainer2 .icon:before {
    top: 0
}

/*.iconContainer .icon2:before {
    top: -5px
}*/

.iconContainer3 {
    position: absolute;
    right: 20px
}

    .iconContainer3 .icon2 {
        top: -4px;
        left: 6px;
        position: absolute;
        transform: rotate(180deg);
        font-size: 5px;
    }


.iconContainer7 .icon2:before {
    top: -5px
}

/*a span.icon:before {
    top: 1px;
}*/
/* Icon Alignment ends */


/* Video */
.videoContainer {
    height: 675px;
    width: 100%;
    margin: auto;
    overflow: hidden;
    margin-top: -45px;
    /*background: linear-gradient(0deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%);*/
    border: 1px solid #d4d4d4;
}

    .videoContainer .embed-responsive {
        width: 100%;
        height: 100%;
    }

    .playIconRound {
        height: 100px;
        width: 100px;
        opacity: 0.3;
        background-color: #111111;
        border-radius: 50%;
    }

.playIconhover:hover .playIconRound {
    opacity: 0.6;
}

/* Video */

/* IFrame */
.iframeContainer{
    min-height: 175px;
    overflow: hidden;
}

.iframeContainer .embed-responsive .embed-responsive-item{
    position: relative;
}
/* IFrame */

/* Custom Dropdown */
.dropdown_label{
    cursor: pointer;
}

.custom_dropdown {
    position: relative;
    width: 100%;
}

    .custom_dropdown > button {
        border-radius: 0;
        font-size: 14px;
        text-align: left;
        border: 2px solid #D4D4D4;
        background-color: #f4f4f4;
        width: 100%;
        position: relative;
        color: #555;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

        .custom_dropdown > button:after {
            font-family: 'bell-icon';
            content: "\e9c4";
            position: absolute;
            transform: translateY(-50%) rotate(180deg);
            font-size: 5px;
            color: #00549a;
            right: 13px;
            height: 100%;
        }

        .custom_dropdown > button:focus {
            border-color: #003778;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }

    .custom_dropdown .dropdownList {
        padding: 0;
        background: #fff;
        border: 1px solid #D4D4D4;
        border-top: 0;
        box-shadow: 5px 15px 25px 3px rgba(0,0,0,0.2);
        border-top: 0;
        overflow-y: auto;
        position: absolute;
        margin: 0;
        width: inherit;
        z-index: 5;
        padding: 10px 10px 15px 10px;
        top:1px;
    }
        .custom_dropdown .dropdownList li[role="option"] {
            padding: 5px 10px;
            cursor: pointer;
            margin-bottom: 5px;
        }

            .custom_dropdown .dropdownList li[role="option"]:hover, .custom_dropdown .dropdownList li[role="option"].focused {
                border-radius: 3px;
                background-color: #E1E1E1;
            }

.custom_dropdown .dropdown_item {
    padding: 5px;
    cursor: pointer;
}


.custom_dropdown .dropdown_item:hover, .custom_dropdown .dropdown_item:focus {
    border-radius: 3px;
    background-color: #E1E1E1;
}

.dropdown_item .ctrl_element {
    top: -2px;
}

.graphical_ctrl.active {
    font-weight: bold;
    color: #003778;
}

.custom_dropdown .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element {
    outline-width: 2px;
    outline-style: solid;
    outline-color: #4d90fe !important;
    outline-offset: 2px;
}

.custom_dropdown .graphical_ctrl_checkbox .ctrl_element:after {
    left: 7px;
    top: 3px;
    border-width: 0 3px 3px 0;
    width: 8px;
    height: 14px;
}

#custom_dropdown_list li:not(:last-child) {
    margin-bottom: 5px
}

.dropdown_buttons [role="button"]:focus {
    outline-width: 2px;
    outline-style: solid;
    outline-color: #4d90fe !important;
    outline-offset: 2px;
}

.dropdownContainer > div:not(:last-child) {
    margin-right: 15px
}

.dropdownContainer > div:nth-last-child(2) {
    margin-right: 0;
}

@media (max-width: 1440.98px) and (min-width: 320px) {
    .parentImgCont{min-height:300px}
    .parentImgCon2t{min-height:475px}
    .parentImgCont, .parentImgCont2 {
        width: 100%;
        margin: 0;
        overflow: hidden;
    }
        .size300{height:300px}
        .size475{height:475px}
        img.banner-crop-img.centerView {
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);
        }
        img.banner-crop-img.size600 {
            min-width: 1440px;
        }
        img.banner-crop-img {
            position: absolute;
            height: 100%;
            width: auto;
            max-width: 100%;
            min-width:1400px;
        }
}

/* Banner ends*/  

.dropdownContainer > div:not(:last-child){margin-right:15px}
.width-dropdown2{width:160px}
.width-dropdown3{width:160px}
.width-dropdown{width:290px}

/* Tiles */

.tilesContainer{margin:0 -7.5px;}
.tilesContainer > div{padding:0 7.5px;}
.tilesContainer > div > a:hover,.tilesContainer > div > a:focus{z-index:2}

.tilesItem {
    box-shadow: 0 0px 30px 0 rgba(0,0,0,0.12);
}
a:focus .tilesItem, a:hover .tilesItem {
    box-shadow: 0 20px 25px 0 rgba(0,0,0,0.2);
} 

.tilesItem .tilesImage{
    height: 175px;
    position: relative;
}
.tilesItem .tilesImage img {
    margin: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    height:175px;
    margin-top:-1px;
}

.tilesMarker {
    padding: 2px 6px;
    bottom: 15px;
    left: 30px;
    border-radius: 2px;
    text-transform:uppercase;
}
.tilesContent h3, .tilesContent .h3 {
    letter-spacing:0;
    line-height:24px
}

/*use true bold expand/collapse icon*/ 
.icon-exapnd-outline-circled:before,
.accordion-toggle[aria-expanded="false"] > span.icon-exapnd-outline-circled:before{
    content: "\e96c";
    top:-3px;
}
.icon-collapse-outline-circled:before,
.accordion-toggle[aria-expanded="true"] > span.icon-exapnd-outline-circled:before{
    content: "\e96b";
    top:-3px;
}

/* Tiles end */

@media (max-width:991.98px){
    .width-dropdown2 {
        width: 140px
    }

    .width-dropdown {
        width: 210px
    }
}



































/* Slider */
.height180 {
    height: 180px
}

.border-none-top {
    border-top: none
}

.lineHeight25 {
    line-height: 25px
}

.generic-slider .slick-list.draggable:before {
    background: linear-gradient(to left, #FFF 50%, transparent 50%), radial-gradient(ellipse at 68% 48%, rgba(0,0,0,0.3), transparent 67%, transparent);
    box-shadow: 20px 0px 0px 0px #fff;
}

.rsc-center-content {
    height: calc(100% - 180px);
    padding: 25px 30px;
    min-height: 180px;
    border-bottom-left-radius:10px;
    border-bottom-right-radius:10px;
}

.resource-center-explore img {
    margin: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-right: -50%;
    transform: translate(-50%, -50%);
    height: 100%;
}
.resource-center-explore, .height180.borderGrayLight6{
    border-top-left-radius:10px;
    border-top-right-radius:10px;
}


.generic-slider .slick-track {
    display: flex;
}

.generic-slider.slick-initialized .slick-slide {
    height: auto;
    margin: 10px 0;
}

    .generic-slider.slick-initialized .slick-slide > div {
        height: 100%;
    }

.generic-slider.slick-slider {
    height: auto;
    margin: 0 auto;
    padding-left: 30px;
    padding-right: 30px;
}

.generic-slider .slick-prev,
.generic-slider .slick-next {
    opacity: 1;
    background-color: transparent;
    box-shadow: none;
}

.generic-slider .slick-dots {
    width: calc(100% - 60px);
    margin-top: 20px;
}

.generic-slider .box-shadow {
    box-shadow: 0 0px 15px 0 rgba(0,0,0,0.12);
}

.generic-slider .slick-prev,
.generic-slider .slick-next {
    opacity: 1;
    background-color: transparent;
    box-shadow: none;
    /*top: 45%;*/
    top: 0;
    height: calc(100% - 20px);
    width: 120px;
    display: flex;
    align-items: center;
}

/* Image Controller end */

/* Media Query Section */

@media (min-width: 1440px) {

    .generic-slider.slidingCardsSection button.slick-arrow { 
        background: none;
        box-shadow: none;
    }

        .generic-slider .slick-prev:hover:before,
        .generic-slider .slick-next:hover:before,
        .generic-slider .slick-prev:active:before,
        .generic-slider .slick-next:active:before,
        .generic-slider .slick-prev:active,
        .generic-slider .slick-next:active,
        .generic-slider .slick-prev:hover,
        .generic-slider .slick-prev:focus,
        .generic-slider .slick-next:hover,
        .generic-slider .slick-next:focus,
        .generic-slider .slick-prev:before,
        .generic-slider .slick-next:before {
            color: #00549a;
        }

    .generic-slider .slick-prev.slick-disabled:before,
    .generic-slider .slick-next.slick-disabled:before {
        opacity: 1;
        color: #999;
    }

.generic-slider .slick-prev {
    left: -18px;
    background: linear-gradient(to right, #FFF 50%, transparent 50%), radial-gradient(ellipse at 40% 48%, rgba(0,0,0,0.3), transparent 67%, transparent);
}

.generic-slider .slick-next {
    right: -18px;
    background: linear-gradient(to left, #FFF 50%, transparent 50%), radial-gradient(ellipse at 60% 48%, rgba(0,0,0,0.3), transparent 67%, transparent);
}

    .generic-slider .slick-prev:before,
    .generic-slider .slick-next:before {
        font-size: 30px;
        /*top: 10px;*/
        top: initial;
    }

.generic-slider .slickSlide {
    display: flex;
    height: 100%;
}

.generic-slider .links {
    position: absolute;
    bottom: 30px;
}
/* Slider ends*/

.searchBarContainer{
    flex-basis:313px;
    margin-left: auto;
    margin-top:20px
}

.searchBox {
    position: relative;
    padding: 9px 50px 9px 20px;
    margin: 0;
    background-color: #fff;
    color: #111;
    height: 37px;
    border-radius: 2px;
    display: inline-block;
    border: 1px solid #d4d4d4;
    box-shadow: inset 0.7px 0.7px 3px 0 rgba(0, 0, 0, 0.24);
    border-radius:20px;
}

.searchBar button {
    background-color: transparent;
    border: none;
}

.searchButtonContainer{
    top:-30px;
    right:0;
    float:right;
}


.max-width-70p{max-width:70%}

.iconContainer2 .icon:before{top:0}
.iconContainer .icon2:before{top:-5px}
.iconContainer3{position:absolute;right:20px}
.iconContainer3 .icon2{
    top: -2px;
    position: absolute;
    transform: rotate(180deg);
    font-size: 5px;
}

@media (min-width: 992px) {
}

@media (max-width: 991.98px) {
    .min-width-dropdown2{min-width:140px}
    .min-width-dropdown{min-width:210px}
}

@media (min-width: 768px) {

}

@media (min-width: 768px) and (max-width: 991.98px) {
    .width-dropdown {width: 210px;}
    .width-dropdown2{width:140px;}
}

@media (max-width: 767.98px) {
    .fullWidth-xs{width:100%}
    .max-width-100p-xs{max-width:100%}
    .dropdownContainer > div:not(:last-child){margin-bottom:10px;margin-right:0}

    .searchBarContainer {
        flex-basis: unset;
        margin-left: unset;
        width: 100%;
        margin-top: 20px
    }
}
/*Cards Section */
.slidingCardsSection {
    background-color: #fff
}

/*Cards Slider*/
.slidingCardsContainer .slick-slide:not(:last-child) {
    margin-right: 15px;
}

.slidingCardsContainer .slick-slide {
    box-shadow: 0 0px 15px 0 rgba(0,0,0,0.12);
    border-radius: 10px;
    display: flex;
}

.slidingCardsContainer .slick-list {
    padding: 10px 0;
}

.slidingCardsContainer .slick-prev:hover, .slidingCardsContainer .slick-next:hover, .slidingCardsContainer .slick-prev:focus, .slidingCardsContainer .slick-next:focus, .slidingCardsContainer .slick-prev, .slidingCardsContainer .slick-next {
    opacity: 1;
}

    .slidingCardsContainer .slick-prev:hover:before, .slidingCardsContainer .slick-next:hover:before, .slidingCardsContainer .slick-prev:focus:before, .slidingCardsContainer .slick-next:focus:before {
        opacity: .75;
        color: #00549A
    }

    .slidingCardsContainer .slick-next.slick-disabled, .slidingCardsContainer .slick-prev.slick-disabled {
        opacity: 0
    }

.slidingCardsContainer .slick-next,
.slidingCardsContainer .slick-prev {
    height: 100%;
    top: 0;
    border-radius: 0;
    width: 100px;
}

.slidingCardsContainer .slick-next {
    background: linear-gradient(to left, #FFF 50%, transparent 50%), radial-gradient(ellipse at 68% 48%, rgba(0,0,0,0.3), transparent 67%, transparent);
    box-shadow: 20px 0px 0px 0px #fff;
}

.slidingCardsContainer .slick-prev {
    background: linear-gradient(to right, #FFF 50%, transparent 50%), radial-gradient(ellipse at 40% 48%, rgba(0,0,0,0.3), transparent 67%, transparent);
    box-shadow: -20px 0px 0px 0px #fff;
}

.slick-prev:before, .slick-next:before {
    top: 50%;
}

.slidingCardsContainer .slick-prev:before, .slidingCardsContainer .slick-next:before {
    font-size: 27px;
    opacity: 1
}

.slidingCardsContainer .slick-next {
    right: -50px
}

.slidingCardsContainer .slick-prev {
    left: -50px
}

.slidingCardsContainer .slick-slide > div, .slidingCardsContainer .slick-slide .slickSlide, .slidingCardsContainer .slick-slide .slickSlide .cards {
    height: 100%;
    width: 100%;
}

.slidingCardsContainer .slick-dots li button {
    background-color: transparent;
    border-color: #a4a4a4
}

    .slidingCardsContainer .slick-dots li button:focus {
        outline: -webkit-focus-ring-color auto 5px !important;
        outline: 1px solid #2672cb !important;
        opacity: 1
    }

.slidingCardsContainer .slick-dots li.slick-active button {
    background-color: #999
}

.noCardSlide {
    display: flex;
}

    .noCardSlide .slickSlide {
        flex: 1;
    }

    .noCardSlide.card-column-2 .slickSlide:first-child {
        margin-right: 8px;
    }

    .noCardSlide.card-column-2 .slickSlide:last-child {
        margin-left: 8px;
    }

    .noCardSlide.card-column-3 > .slickSlide:nth-child(3n+1) {
        margin-right: 8px;
    }

    .noCardSlide.card-column-3 > .slickSlide:nth-child(3n+2) {
        margin: 0 8px;
    }

    .noCardSlide.card-column-3 > .slickSlide:nth-child(3n+3) {
        margin-left: 8px;
    }

.slidingCardsContainer .slick-slide[aria-hidden=true] {
    visibility: hidden;
}

/*Cards*/
.cards {
    position: relative;
    height: 100%
}

.cardsWithImage .cardsImage {
    background-color: #babec2;
    height: 176px;
    position: relative;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
}

    .cardsWithImage .cardsImage img {
        margin: 0;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%)
    }

    /* Social Icons */ 
    .txtContainer {
        width: 100%;
    }

    .txtContainer > div{
        max-width: 100%;
        padding: 0 15px;
    }

    .socialLinksContainer .tooltip.bs-tooltip-bottom {
        margin-top: 25px;
    }
    /* Social Icons ends */

    /* Video */
    .videoContainer{
        min-height: 165px;
        height: 398px;
        margin-top: -30px;
    }
    /* Video ends */

    .line-height-26-xs{
        line-height: 26px;
    }

.cards .cardsContent {
    padding: 30px
}

    .cards .cardsContent span {
        font-size: 12px;
        line-height: 18px
    }

    .cards .cardsContent h3 {
        font-size: 18px;
        line-height: 22px;
        font-weight: bold
    }

    .spotlightContainer {
        overflow:hidden
    }
}



























