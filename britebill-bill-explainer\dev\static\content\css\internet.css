/*START Checkbox Focus Outline*/
.focus_outline .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element,
.focus-checkbox_container.focused-outline {
    outline: none !important;
    box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}

.focus_outline_blue .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element,
body.is_tabbing .focus_outline_blue *:focus {
    outline: none !important;
    box-shadow: 0 0 0px 3px #00549a, 0 0 2px 3px #00549a, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}

.focus_outline_gray .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element,
body.is_tabbing .focus_outline_gray *:focus {
    outline: none !important;
    box-shadow: 0 0 0px 3px #f4f4f4, 0 0 2px 3px #f4f4f4, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}
/*END Checkbox Focus Outline*/

/* START Helper Class */
.height-190{height:190px}
.height-440{height: 440px}
.height-152 {height: 152px}
.min-height-460{min-height:460px}
.max-width-100 {max-width: 100%}
.max-width-unset{max-width:unset;max-width:none}
.width-150{width:150px}
.width-190{width:190px}
.width-515{width: 515px;}
.line-height-22{line-height:22px}
.border-radius-5{border-radius:5px}
.border-none {border:none}
.left-0{left:0;}
.right-0{right:0;}
.txtSize46 {font-size:46px}
.txtSize74 {font-size:74px}
.vSpacer4 {width: 4px}
.max-width-300 {
    max-width: 300px;
}
.max-width-170 {
    max-width: 170px;
}
.max-width-200 {
    max-width: 200px;
}

.margin-neg-l-5{margin-left:-5px;}

.bg-blue-spotlight-radial-gradiant {
    background: radial-gradient(circle, #1CBCF4 0%, #0E75CD 45.47%, #024791 69.57%, #012F6A 100%)
}
/*.white-spotlight-radial-gradiant {radial-gradient(closest-corner at 35% 50%, #1CBCF4 0%, #0E75CD 39.47%, #00549A 100%)}*/
.bg-spotlight-corner-radial-gradiant{background: radial-gradient(closest-corner at 35% 50%, #1CBCF4 0%, #0E75CD 39.47%, #00549A 100%)}

.background-size-cover{background-size:cover}
.background-position-center{background-position:center}
.background-no-repeat{background-repeat:no-repeat}
.background-size-hero-banner-12 {background-size: 1247px 100%;}
.background-position-hero-banner-12 {background-position: 75% 50%}

.focus-h-center{left:50%;transform:translateX(-50%)}
 
.subtitle{
    font-size: 20px;
    line-height: 22px;
    font-weight: 700;   
    color: #111;
}

.long-article {
    font-size: 16px;
    line-height: 25px;
    color: #2B2B2B
}

.z-index-10 {
    z-index: 10;
}

.bg-bell-dark-blue {
    background-color: #002A4D;
}

.vCenter, .graphical_ctrl input.vCenter {
    top: 50%;
    transform: translateY(-50%);
}

.topCenter-centered {
    top: 0;
    left: 50%;
    transform: translate(-50%,-50%);
}

.leftCenter-centered {
    top: 50%;
    left: 0;
    transform: translate(-50%,-50%);
}

.graphical_ctrl_checkbox input[type="checkbox"]:checked ~ span {
    color: #003778;
    font-weight: bold;
}

    /*.graphical_ctrl_checkbox input[type="checkbox"]:checked ~ .ctrl_element:after {
        top: 2px;
        left: 8px;
    }*/

    .graphical_ctrl_checkbox input[type="checkbox"]:focus ~ .ctrl_element {
        outline-width: 2px;
        outline-style: solid;
        outline-color: #4d90fe;
        outline-offset: 2px;
    }

/* Custom nav for student identification */
.global-navigation .connector-brand.home-sm:after {
    font-size: 34px;
}

.banner .banner-image {
    height: 430px;
}

.hero-banner-11 {
    height: 440px;
}

.hero-banner-12 {    
    height: 460px;
}

.hero-banner-13 {
    height: 540px;
}

    .hero-banner-13 .banner-image {
        height: 320px;
    }

.hero-banner-14 {
    height: 570px;
}

    .hero-banner-14 .banner-image {
        height: 325px;
    }
        .hero-banner-14 .banner-image img {
            bottom: 10px;
        }

.hero-background-bell {
    background-image: url('../../../../assets/img_tmp/Internet/internet_banner_info.png');
}

/*.column-spacer-15 {
    margin-left: -7.5px;
    margin-right: -7.5px
}

    .column-spacer-15 > div {
        padding-left: 7.5px;
        padding-right: 7.5px;
    }*/

.multiInfoBlock [class^="col-"] > div{
    min-height: 470px;
}

.background-img-responsive {
    margin-top: -20%;
}

/* Custom table with scroll */
.scrollableTableColumnWidth th:not(.sr-only){
    width: 33.333333333333333%;
    min-width: 110px;
}


.table-scrollable-wrapper tbody td div.inner-content {
    padding: 12px 20px;
}

.table-scrollable-wrapper tbody td {
    padding: 0;
}

.sticky-first-column-cont {
    width: 25%;
    min-width: 25%;
}

.sticky-first-column-cont-modal {
    width: 33.33%;
    min-width: 33.33%;
}

.table-scrollable-wrapper tbody td div.inner-content {
    padding: 12px 20px;
}

.table-scrollable-wrapper tbody td {
    padding: 0;
}

.sticky-first-column, .sticky-first-column-modal {
    padding: 12px 20px;
}

.sticky-first-column-cont > div div.same-height {
    min-height: 50px;
}

.sticky-first-column-modal{
    min-height: 60px;
}

.responsive-hd-divider {
    border-top: 1px solid #003778;
}

.width-300 {
    width: 300px;
}

.tabpanel-container div[role="tabpanel"][aria-hidden="true"]:not(.slick-slide) {
    display: none
}

.step-container .step-scroll .step-group .step-list.active .step-title {
    color: #111;
}

.scrollable-img {
    width: 472px;
}

.store-icon-size {
    width: 120px;
    height: 40px;
}

.store-icon-size2 {
    width: 135px;
    height: 40px;
}
/*Remove for defect BTC-5406*/
/*.store-icon-size:last-child {
    width: 135px;
}*/

.img-positioned-top-border-center {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translate(-50%, -50%);
}

.wifi-pod-detail {
    position: absolute;
    width: 100%;
    top: 0px;
    left: 50%;
    transform: translate(-22%, -120%);
    z-index: 2;
}

.wifi-pod-price-box {
    width: 378px;
    height: 256px;
}

.wifi-pod-model {
    width: 478px;
    height: 348px;
}

.wifi-tooltip.tooltip-interactive .tooltip-inner {
    max-width: 250px;
}

.wifi-tooltip.tooltip-interactive .tooltip {
    width: 250px;
}

.max-width-410{
    max-width: 410px;
}

.max-width-430 {
    max-width: 430px;
}

.max-height-275 {
    max-height: 275px;
}

.wifi-pod-4-new {
    height: 46px;
    position: absolute;
    bottom: 5px;
}

.border-radius-5 {
    border-radius: 5px;
}

.focus-checkbox_container .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element {
    outline: none;
}

.focus-checkbox_container .graphical_ctrl input:checked ~ span {
    color: #2b2b2b;
}

.focus-checkbox_container.checked-border {
    border: 2px solid #00549A;
}

.graphical_ctrl input:checked + *
/* Custom animation for fibre glow*/
.fibre-glow {
    z-index: 5;
}
    .fibre-glow.glow1 {
        opacity: 0;
        transform: scale(0.6) rotate(-15deg);
        animation: pulse 1.5s, scale-rotate 1.5s;
        animation-delay: 3.2s;
        animation-iteration-count: infinite;
        animation-timing-function: ease-in-out;
        bottom: 19%;
        left: 16%;
        width: 40px;
    }

    .fibre-glow.light1 {
        opacity: 0;
        transform: rotate(-20deg);
        animation: pulse 1.5s, rotate-light 1.5s;
        animation-delay: 3.2s;
        animation-iteration-count: infinite;
        animation-timing-function: ease-in-out;
        bottom: 19%;
        left: 16%;
        width: 40px;
    }

    .fibre-glow.glow2 {
        opacity: 0;
        transform: scale(0.6) rotate(-15deg);
        animation: pulse 1.5s, scale-rotate 1.5s;
        animation-iteration-count: infinite;
        animation-timing-function: ease-in-out;
        bottom: 21%;
        left: 30%;
        width: 40px;
    }

    .fibre-glow.light2 {
        opacity: 0;
        transform: rotate(-20deg);
        animation: pulse 1.5s, rotate-light 1.5s;
        animation-iteration-count: infinite;
        animation-timing-function: ease-in-out;
        bottom: 21%;
        left: 30%;
        width: 40px;
    }

    .fibre-glow.glow3 {
        opacity: 0;
        transform: scale(0.6) rotate(-15deg);
        animation: pulse 1.5s, scale-rotate 1.5s;
        animation-delay: 2.4s;
        animation-iteration-count: infinite;
        animation-timing-function: ease-in-out;
        bottom: 18.5%;
        left: 40%;
        width: 25px;
    }

    .fibre-glow.light3 {
        opacity: 0;
        transform: rotate(-20deg);
        animation: pulse 1.5s, rotate-light 1.5s;
        animation-delay: 2.4s;
        animation-iteration-count: infinite;
        animation-timing-function: ease-in-out;
        bottom: 18.5%;
        left: 40%;
        width: 25px;
    }


    .fibre-glow.glow4 {
        opacity: 0;
        transform: scale(0.6) rotate(-15deg);
        animation: pulse 1.5s, scale-rotate 1.5s;
        animation-delay: 0.8s;
        animation-iteration-count: infinite;
        animation-timing-function: ease-in-out;
        bottom: 26%;
        left: 51.5%;
        width: 20px;
    }

    .fibre-glow.light4 {
        opacity: 0;
        transform: rotate(-20deg);
        animation: pulse 1.5s, rotate-light 1.5s;
        animation-delay: 0.8s;
        animation-iteration-count: infinite;
        animation-timing-function: ease-in-out;
        bottom: 26%;
        left: 51.5%;
        width: 20px;
    }

    .fibre-glow.glow5 {
        opacity: 0;
        transform: scale(0.6) rotate(-15deg);
        animation: pulse 1.5s, scale-rotate 1.5s;
        animation-delay: 4s;
        animation-iteration-count: infinite;
        animation-timing-function: ease-in-out;
        bottom: 26%;
        left: 57.2%;
        width: 20px;
    }

    .fibre-glow.light5 {
        opacity: 0;
        transform: rotate(-20deg);
        animation: pulse 1.5s, rotate-light 1.5s;
        animation-delay: 4s;
        animation-iteration-count: infinite;
        animation-timing-function: ease-in-out;
        bottom: 26%;
        left: 57.2%;
        width: 20px;
    }


    .fibre-glow.glow6 {
        opacity: 0;
        transform: scale(0.6) rotate(-15deg);
        animation: pulse 1.5s, scale-rotate 1.5s;
        animation-delay: 1.6s;
        animation-iteration-count: infinite;
        animation-timing-function: ease-in-out;
        bottom: 9%;
        left: 89.5%;
        width: 50px;
    }

    .fibre-glow.light6 {
        opacity: 0;
        transform: rotate(-20deg);
        animation: pulse 1.5s, rotate-light 1.5s;
        animation-delay: 1.6s;
        animation-iteration-count: infinite;
        animation-timing-function: ease-in-out;
        bottom: 9%;
        left: 89.5%;
        width: 50px;
    }

.slick-dots li button:before {
    opacity: 1;
}

.slick-prev,
.slick-next {
    background: #fff;
    border: 1px solid #E1E1E1;
    box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
    margin: 1px;
    margin-top: -16px;
    opacity: 1;
    transform: translateY(-50%);
}

.slick-prev {
    left: -15px;
}

.slick-next {
    right: -15px;
}

    .slick-prev:before,
    .slick-next:before {
        opacity: 1;
    }

    .slick-next:before {
        top: 12px;
    }

    .slick-prev:hover,
    .slick-prev:focus,
    .slick-next:hover,
    .slick-next:focus {
        background: #fff;
        border: 2px solid #00549A;
        box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
        color: #00549a;
        margin: 0;
        margin-top: -17px;
    }

    .slick-prev:hover:before,
    .slick-prev:focus:before,
    .slick-next:hover:before,
    .slick-next:focus:before {
        color: #00549a;
        opacity: 1;
    }

    .step-container {
        /* Based on mockups */
        width: 960px;
        margin: auto;
    }
        
.step-container .step-scroll {
    border-left: 1px solid #e1e1e1;
    position: relative;
}

.step-container .step-scroll .step-group .step-list .step-desc {
    color: #555;
}

.step-container .step-scroll .step-group .step-list.active .step-title {
    color: #111;
}
        
.step-container .step-scroll .scroll-line {
    position: absolute;
    width: 4px;
    top: 0;
    background-color: #00549A;
    list-style: none;
}
        
.scrollable-steps {
    display: flex;
    flex-shrink: 0;
}

.scrollable-steps > div:first-child{
    width: 472px;
}
.scrollable-steps > div:last-child {
    width: calc(100% - 435px);
}

.pods-horizontal-line:before, .label-3radios:before {
    content: "";
    display: block;
    background: #00549A;
    border-radius: 100%;
    width: 10px;
    height: 10px;
    position: absolute;
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
}

.label-3radios:after {
    content: "";
    opacity: 1;
    display: block;
    background: #00549A;
    width: 1px;
    height: 95px;
    /*bottom: calc(-135% - 3.5px);*/
    position: absolute;
    margin-top: 5px;
    transition: height 1s;
}

.label-500mbps:after {
    content: "";
    opacity: 1;
    display: block;
    background: #00549A;
    width: 1px;
    height: 75px;
    /*bottom: calc(-135% - 3px);*/
    top: 7px;
    left: 184px;
    position: absolute;
    margin-top: 5px;
    transition: height 1s;
}

/*.label-500mbps .txtBold:after{
    position: absolute;
    bottom: -143px;
    left: -379px;
    content: '';
    height: 59px;
    width: 100%;
    border-right: 2px solid #00549A;
    -webkit-transform: skew(-122deg);
    -moz-transform: skew(-122deg);
    transform: skew(-122deg);
}*/

.pods-horizontal-line::after {
    position: absolute;
    top: 87px;
    left: 231px;
    content: '';
    height: 59px;
    border-right: 2px solid #00549A;
    -webkit-transform: skew(-122deg);
    -moz-transform: skew(-122deg);
    transform: skew(-122deg);
}

.package-list-detail {
    column-count: 2;
    -webkit-column-count: 2;
    -moz-column-count: 2;
    /* -moz-column-fill: auto;
    column-fill: auto; */
}
.render-bullet{
    list-style-position: outside;
    padding-left: 15px;
}

.warning-icon-size {
    width: 60px;
    height: 60px;
}

.icon-circle-large-expired { 
    display: inline-block;
    position: relative;
    border: 2px solid #00549a;
    border-radius: 50%; 
    width: 72px;
    height: 72px;
}

.icon-circle-large-expired:before {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%,-50%);
    -ms-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    font-size: 72px
}

/*.sticky-first-column-cont > div:not(first-child) div.same-height {
    border-right: 1px solid #d4d4d4;
}*/


.dash-list-style li:before{
    content: "-";
    margin-right: 5px;
}

.list-margin-10 li:not(:last-child) {
    margin-bottom: 10px;
}

@supports (-moz-appearance:none) {
    .sticky-first-column-cont > div:first-child div.same-height {
        margin-top: -1px;
    }
}

/* START Custom styles for tooltip */
.tooltip-email .tooltip-inner {
    /*max-width: 210px;*/
    padding: 15px;
    box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
    -webkit-box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
    -moz-box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
}

.tooltip-email .tooltip.bs-tooltip-right .arrow::before {
    border-width: 12px 15px 12px 0px;
}

.tooltip-email .tooltip.bs-tooltip-left .arrow::before {
    border-width: 12px 0px 12px 15px;
}

.tooltip-email .tooltip.bs-tooltip-right .arrow {
    margin-top: 5px;
}

.tooltip-email .tooltip.bs-tooltip-left .arrow {
    margin-top: 5px;
}

.tooltip-email .tooltip.bs-tooltip-right {
    margin-left: 15px;
}
/* END Custom styles for tooltip */

/* START Custom slick dots for Infoblock carousel */
.infoblock-slider .slick-dots li.slick-active button {
    background-color: #555555;
}

.infoblock-slider .slick-dots li button {
    border: 1px solid #555555;
    opacity: 1;
}

.infoblock-slider .slick-dots button {
    background-color: #ffffff;
}

.infoblock-slider .slick-prev {
    left: -30px;
}

.infoblock-slider .slick-next {
    right: -30px;
}
/* END Custom slick dots for Infoblock carousel */

@keyframes wifi-animation-secFadeInOut {
    0% {
        opacity: 0;
    }

    15% {
        opacity: 1;
    }

    55% {
        opacity: 0;
    }

    100% {
        opacity: 0;
    }
}

.wifi-animation img {
    animation: wifi-animation-secFadeInOut 12s ease-in-out infinite 0s;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
}

    .wifi-animation img:first-child {
        position: relative;
    }

    .wifi-animation img {
        width: 100%;
        height: 100%;
    }

    .wifi-animation img:nth-of-type(2) {
        animation-delay: 3s;
    }

    .wifi-animation img:nth-of-type(3) {
        animation-delay: 6s;
    }

    .wifi-animation img:nth-of-type(4) {
        animation-delay: 9s;
    }

.wifi-signal {
    border-radius: 50%;
    width: 50%;
    padding-bottom: 50%;
    background-color: #00549a;
    opacity: .1;
    position: absolute;
    animation: signalAnimation 1.5s ease-out;
    animation-iteration-count: infinite;
    transform-origin: center center;
}

.wifi-signal_1 {
    top: -1.5%;
    left: 11.7%;
}

.wifi-signal_2 {
    top: 53.5%;
    left: -0.3%;
}

.wifi-signal_3 {
    top: 28.5%;
    left: 46.7%;
}

@keyframes signalAnimation {
    0% {
        transform: scale(0.1);
    }

    100% {
        transform: scale(1);
    }
}

.dimension-50 {
    height: 50px;
    width: 50px;
}

.flexShrink-0{
    flex-shrink: 0;
}
/* Start of Power Fibre Modal */

.power-fibre-learn-more .img-fttn {
    margin-left: 53%;
    transform: translateX(-50%);
    max-width: 153%;
}

.power-fibre-learn-more .img-ftth {
    max-width: 100%;
    margin: 0 auto;
}

.power-fibre-learn-more .mx-auto{
    width:224px;
}
/* End of Power Fibre Modal */

/* Custom height for table td */
.table-td-50 td {
    height: 50px;
}


.modal.scrollable-body.show {
    z-index: 2003;
}

.modal.scrollable-body.dual-modal.show {
    z-index: 2001;
}

.modal-backdrop:nth-child(odd) {
    z-index: 2000;
}

.infoblock-l .infoblock-l-height .img-scaled{
    transform: scale(1);
}
.modal-with-scrollable-table .modal-body.pad-h-15.pad-b-0{
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 0px;
}
.modal-with-scrollable-table table{
    min-width: 465px;
}
.modal-with-scrollable-table table tbody td{
    padding:10px 20px;
}
.banner .banner-image3{
    min-height: 200px;
}
.margin-neg-5-l{
    margin-left: -5px;
}
.background-img-responsive-3 {
    position:absolute;
    bottom:0;
}
@media (min-width: 992px) {
    /* START Helper Class */
    .banner-2{position:absolute;right:-80px}
    .max-width-255{
        width: 255px;
        max-width: 255px;
    }
    .width-md-315 {
        width: 315px;
    }
    .width-md-350{
        width:350px;
    }
    .width-md-400{
        width:400px;
    }
    /* END Helper Class */
    /* START Custom Class */
    .column-spacer-md-20 {
        margin-left: -10px;
        margin-right: -10px
    }

        .column-spacer-md-20 > div {
            padding-left: 10px;
            padding-right: 10px;
        }

    .banner .banner-image img {
        left: -425px;
        top: 60px;
        height: 500px;
    }
    /* END Custom Class */

    /* Custom table with scroll */
    .table-options {
        width: 100%;
    }

    /* START Custom nav for student identification */
    .global-navigation .connector-brand.home-lg {
        margin-left: 5px;

    }
    /* END Custom nav for student identification */

   /* START Custom slick for Infoblock carousel */
    .infoblock-slider.slick-initialized .slick-slide {
        padding: 30px;
        max-width: 310px;
    }

    .infoblock-slider .slick-list {
        padding-bottom: 25px;
    }


    /* START Custom Arrows */
    .infoblock-slider .slick-arrow.slick-disabled {
        display: none;
    }

    .infoblock-slider .slick-prev, .infoblock-slider .slick-next {
        background: #fff;
        border: 1px solid #E1E1E1;
        box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
        margin: 0px 15px;
        margin-top: -15px;
        opacity: 1;
        transform: translateY(-50%);
    }
        .infoblock-slider .slick-next:before {
            top: 12px;
        }

        .infoblock-slider .slick-prev:hover,
        .infoblock-slider .slick-prev:focus,
        .infoblock-slider .slick-next:hover,
        .infoblock-slider .slick-next:focus {
            background: #fff;
            border: 1px solid #00549A;
            box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
            color: #00549a;
        }

            .infoblock-slider .slick-prev:hover:before,
            .infoblock-slider .slick-prev:focus:before,
            .infoblock-slider .slick-next:hover:before,
            .infoblock-slider .slick-next:focus:before {
                color: #00549a;
                opacity: 1;
            }

    .infoblock-slider .slick-arrow.slick-disabled {
        display: none;
    }

    .infoblock-slider .slick-prev:focus, .infoblock-slider .slick-next:focus, .infoblock-slider .slick-next:focus:before, .infoblock-slider .slick-prev:focus:before {
        background: #fff;
        color: #00549a;
    }
    /* END Custom Arrows */

   /* END Custom slick for Infoblock carousel */

    .pods-horizontal-line:before {
        right: 195px;
    }

    .label-3radios:before {
        right: 141px;
        top: 80px;
    }

    .pods-horizontal-line:before {
        top: 140px;
    }

    .label-3radios:after {
        right: 146px;
        top: -13px;
    }
    /* END Custom slick dots for Infoblock carousel */
    /* Start of Bell Wifi 3 columns */
    .wifi-app-3-col > div:first-child {
        right: 71%;
        bottom:0;
    }
        .wifi-app-3-col > div:first-child img{
            width:230px;
        }
    .wifi-app-3-col > div:last-child {
        left: 73%;
        top: 60px;
    }
        .wifi-app-3-col > div:last-child img{
            width:230px;
        }
    
    /* End of Bell Wifi 3 columns */
    .herobanner .img-responsive{
        position: absolute;
    }
    .infoblock-l .infoblock-l-height{
        height: 650px;
    }
    .infoblock-l .infoblock-l-height.infoblock-suburb-glow2{
        height: 620px;
    }

    .infoblock-l .infoblock-l-height .img-scaled{
        transform: scale(1.06);
    }
    .width-md-795{
        width: 795px;
    }
    .banner .banner-image3{
        min-height: 440px;
    }
    .banner .banner-image3 img{
        top:0;
        right:10px;
        /*max-width:505px;*/
    }
    .margin-neg-sm-10-b{
        margin-bottom:-10px;
    }
    .hero-banner-14 .position-md-absolute{
        top: 235px;
    }
    .position-md-absolute{
        position: absolute;
    }
}

/* desktop and larger */
@media (min-width: 1200px) {
    .banner-2{position:absolute;right:0}
    /* START Custom slick for Infoblock carousel */
    .infoblock-slider.slick-initialized .slick-slide {
        max-width: 390px;
    }
    /* END Custom slick for Infoblock carousel */
}

@media (max-width: 991.98px) {
    .box-shadow-round-sm {
        box-shadow: 0 6px 25px 0 rgba(0,0,0,0.12);
    }
    .multiInfoBlock [class^="col-"] > div {
        min-height: 520px;
    }

    .hero-banner-12 {   
        height: 646px;
    }

    .background-position-hero-banner-12 {
        background-position: 94% 115%
    }

    .background-size-hero-banner-12 {
        background-size: 192% 75%;
    }

    .margin-neg-50-l-sm {
        margin-left: -50px;
    }

    .mw-none-sm {
        max-width: none;
    }

    .focus-h-sm-center {
        left: 50%;
        transform: translateX(-50%);
    }
    .line-height-18-sm {
        line-height: 18px;
    }
    .line-height-26-sm {
        line-height: 26px;
    }

    .infoblock-slider .slick-list {
        overflow: visible;
        padding-bottom: 5px;
    }

    /* Mobile Slider */
    .slick-slider-mobile-js.slick-initialized {
        width: 100%;
        margin: 0 auto;
    }

        .slick-slider-mobile-js.slick-initialized .slick-list {
            margin: 0 -30px;
            padding: 0 30px;
        }

        .slick-slider-mobile-js.slick-initialized .slick-track {
            display: flex;
            margin-left: -22.5px;
            padding: 30px;
            width: 100%;
        }

            .slick-slider-mobile-js.slick-initialized .slick-track.margin-n30-left {
                margin-left: -30px;
            }

            .slick-slider-mobile-js.slick-initialized .slick-track.margin-n15-left {
                margin-left: -15px;
            }

        .slick-slider-mobile-js.slick-initialized .slick-slide {
            margin-right: 15px;
            height: auto;
        }

            .slick-slider-mobile-js.slick-initialized .slick-slide > div {
                height: 100%;
            }

            .slick-slider-mobile-js.slick-initialized .slick-slide .slickSlide {
                height: 100%;
            }

                .slick-slider-mobile-js.slick-initialized .slick-slide .slickSlide .two-column-card {
                    height: 100%;
                }

        .slick-slider-mobile-js.slick-initialized .slick-dots {
            margin-top: -5px;
        }



    .slick-container .slide-top-content {
        justify-content: center;
    }

    .slick-container .slide-top-content-info {
        padding-left: 10px;
    }

    .panel-img {
        padding: 30px 15px;
    }

    .slick-slider-mobile-js .slick-dots li button {
        border: 1px solid #555555;
        opacity: 1;
        background-color: #fff;
    }

    .slick-slider-mobile-js .slick-dots li.slick-active button {
        background-color: #555555;
    }

    .step-container {
        width: 100%;
    }

    .step-container .slick-slider-mobile-js.slick-initialized .slick-list {
        margin: 0 auto;
        padding: 0;
    }

    .step-container .slick-slider-mobile-js.slick-initialized .slick-slide {
        margin-right: 0;
    }

    .step-container .slick-slider-mobile-js.slick-initialized .slick-track {
        padding: 0;
        display: block;
        margin-left: auto;
    }

    .step-container .scrollable-steps .scroll-line.wifi-alt-line {
        display: none;
    }

    .step-container .slick-slider-mobile-js.slick-initialized .slick-dots {
        margin-top: -10px;
        clear: both;
    }

    .scrollable-steps > div:last-child {
        width: 100%;
    }

    .wifi-pod-detail {
        top: 0px;
        left: 50%;
        transform: translate(-31%, -130%);
    } 

    .label-500mbps:before {
        left: calc(39% - 20px);
    }

    .label-500mbps:before {
        top: 193px;
    }

    .label-500mbps .txtBold:after{
        bottom: -137px;
        left: -323px;
        height: 53px;
        -webkit-transform: skew(-119deg);
        -moz-transform: skew(-119deg);
        transform: skew(-119deg);
    }

    .subtitle-2-reg-3 {
        font-size: 14px;
        line-height: 18px;
    }

    .hero-banner-13.height-auto{
    height: auto;
    }
        .hero-banner-13.height-auto .banner-image2{
            height: auto;
        }

    .fibrebanner .banner-image{
        height: 245px;
    }

    .txtWrap-sm {
        white-space: normal;
    }
    .button-link-2 {
        font-weight: bold;
    }

    .wifi-pod-4-new {
        position: relative;
        bottom: 0px;
    }

}

@media (min-width: 768px) and (max-width: 991.98px)  {
    .banner-2{
        height:460px;
        position:relative;
        margin-top:-100px;
    }
    .banner-2 img{
        position: absolute;
        right: 0;
    }
    .banner .banner-image img {
        left: -350px;
        top: 80px;
        height: 465px;
    }

    .txtLeft-sm {
        text-align: left;
    }

    .txtSize50-sm {
        font-size: 50px;
    }
    .txtSize60-sm {
        font-size: 60px;
    }

    .infoblock-slider.slick-initialized .slick-slide {
        padding: 30px;
        max-width: 347px;
    }

    .background-img-responsive {
        margin-top: -27%;
    }

        .background-img-responsive.fibe-bg {
            left: -17%;
        }

            .background-img-responsive.fibe-bg img {
                max-width: 135%;
                width: 135%;
           }

    .wifi-pod-price-box {
        width: auto;
        height: 257px;
    }
    
    .wifi-pod-model {
        width: 405px;
        height: 100%;
    }

    .label-500mbps:after {
        left: 128px;
    }

    .pods-horizontal-line::after {
        left: 175px;
        height: 50px;
        -webkit-transform: skew(-118deg);
        -moz-transform: skew(-118deg);
        transform: skew(-118deg);
    }

    .pods-horizontal-line:before {
        /*top: 188px;*/
        top: 131px;
    }

    .pods-horizontal-line:before {
        /*left: calc(39% - 20px);*/
        left: 220px;
    }

    .label-3radios:before {
        top: 80px;
    }

    .label-3radios:before {
        right: 138px;
    }

    .label-3radios:after {
        right: 143px;
        top: -15px;
    }

    .scrollable-steps > div:first-child{
        width: 372px;
    }

    .scrollable-img {
        width: 394px;
        margin: 0 auto;
    }

    .package-list-detail {
        column-gap: 60px;
    }

    .package-list-detail.no-gap {
        column-gap: 0;
    }

    .sticky-first-column, .sticky-first-column-modal {
        padding: 10px 5px 12px 20px
    }
    .width-sm-270 {
        width: 270px;
    }
    .minwidth-sm-300{
        min-width: 300px;
    }
    /*Start of Wifi-app-3-col*/
    .wifi-app-3-col > div:first-child{
        right: 75%;
        bottom: 0;
    }
        .wifi-app-3-col > div:first-child img{
            width: 140px;
        }

    .wifi-app-3-col > div:last-child{
        left: 74.5%;
        top: 60px;
    }
        .wifi-app-3-col > div:last-child img{
            width: 150px;
        }
    /*End of Wifi-app-3-col*/

    .herobanner{
        height:440px;
    }
    .herobanner .img-responsive{
        margin-left: 50%;
        transform: translateX(-50%);
    }

    .infoblock-l .infoblock-l-height{
        height: 500px;
    }
    .infoblock-l .infoblock-l-height.infoblock-suburb-glow{
        height: 560px;
    }
    .infoblock-l .infoblock-l-height.infoblock-suburb-glow2{
        height: 540px;
    }

    .infoblock-l .infoblock-l-height .img-scaled{
        transform: scale(1.08);
    }

    .banner .banner-image3 img{
        left:0;
        right:0;
        max-width: 510px;
    }

}

/* @media (min-width: 768px) and (max-width: 816px)  {
    .label-500mbps .txtBold:after{
        bottom: -152px;
        left: -366px;
        height: 60px;
        -webkit-transform: skew(-120deg);
        -moz-transform: skew(-120deg);
        transform: skew(-120deg);
    }
} */

@media (min-width: 768px) {
    /* START Helper Class */
    .border-all-right-radius-10-sm {
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
    }
    /* END Helper Class */
    /* START Custom Class */
    .button-link {
        border-radius: 20px;
        font-size: 15px;
        height: 35px;
        line-height: 17px;
        text-align: center;
        cursor: pointer;
        padding: 7px 28px;
        white-space: nowrap;
        color: #fff;
        background-color: #003778;
        border: 2px solid #003778;
        margin-left: auto;
    }

        .button-link:hover, .button-link:focus {
            color: #fff;
            background-color: #00549a;
            border-color: #00549a;
        }

        .button-link:focus {
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }

    .banner > div {
        min-height: 440px;
    }
    /* END Custom Class */

    .responsive-hd-divider-2 {
        border-left: 1px solid #BABEC2;
    }

    .table-fixed-sm {
        table-layout: fixed;
    }

    .sticky-first-column-cont > div:first-child {
        border-right: 1px solid #003778;
    }

    .line-height-sm-26 {
        line-height: 26px
    }

    .max-width-280-sm {
        max-width: 280px;
    }

    /* Custom padding for btn-file */
    .btn.btn-file {
        padding: 7px 20px;
        max-width: 148px;
    }

    .responsive-hd-divider {
        border-top: 0;
        border-bottom: 0;
        border-left: 1px solid #003778
    }

    .hero-banner-13 {
        height: 770px;
    }

        .hero-banner-13 .banner-image {
            height: 700px;
        }
    
    .hero-banner-14 {
        height: 815px;
    }
        .hero-banner-14 .banner-image {
            height: 700px;
        }
            .hero-banner-14 .banner-image img{
                bottom: 40px;
            }

    .dimension-74 {
        height: 74px;
        width: 74px;
    }
    .dimension-72 {
        height: 72px;
        width: 72px;
    }

    .width-137 {
        width: 137px
    }

    .box-shadow-th-blue {
        box-shadow: inset 1px 0 0 0 #003778;
    }
    .modal-dialog.power-fibre-learn-more{
        width:708px;
    }
    /* Custom animation for fibre glow*/
    .fibre-glow.fiber-glow-sm.glow1 {
        bottom: 18%;
        left: 16.7%;
        width: 5%;
    }

    .fibre-glow.fiber-glow-sm.light1 {
        bottom: 18%;
        left: 16.7%;
        width: 5%;
    }

    .fibre-glow.fiber-glow-sm.glow2 {
        bottom: 21.5%;
        left: 32.3%;
        width: 5%;
    }

    .fibre-glow.fiber-glow-sm.light2 {
        bottom: 21.5%;
        left: 32.3%;
        width: 5%;
    }

    .fibre-glow.fiber-glow-sm.glow3 {
        bottom: 18%;
        left: 40%;
        width: 4%;
    }

    .fibre-glow.fiber-glow-sm.light3 {
        bottom: 18%;
        left: 40%;
        width: 4%;
    }


    .fibre-glow.fiber-glow-sm.glow4 {
        bottom: 25%;
        left: 51.3%;
        width: 3%;
    }

    .fibre-glow.fiber-glow-sm.light4 {
        bottom: 25%;
        left: 51.3%;
        width: 3%;
    }

    .fibre-glow.fiber-glow-sm.glow5 {
        bottom: 25%;
        left: 57.5%;
        width: 3%;
    }

    .fibre-glow.fiber-glow-sm.light5 {
        bottom: 25%;
        left: 57.5%;
        width: 3%;
    }


    .fibre-glow.fiber-glow-sm.glow6 {
        bottom: 10%;
        left: 90%;
        width: 5%;
    }

    .fibre-glow.fiber-glow-sm.light6 {
        bottom: 10%;
        left: 90%;
        width: 5%;
    }


    .tooltip-table .tooltip{
        width:635px;
        padding:0;
    }
    
    .tooltip-table .tooltip-inner{
        max-width: 635px;
        padding: 25px;
    }
    .wifi-app-3-col > div:nth-child(2){
        max-width:425px;
    }

    .table-options-sm-w-100 {
        width: 100%;
    }

    .max-width-450-sm {
        max-width: 450px;
    }
    .position-sm-absolute{
        position:absolute;
    }
    .height-sm-440{
        height:440px;
    }

    .vCenter-sm, .graphical_ctrl input.vCenter-sm {
        top: 50%;
        transform: translateY(-50%);
    }

}

@media (min-width: 992px) {
    .offer-tiles img{
        position: absolute;
        bottom:0;
        top:0;
        right:0;
        margin-top:auto;
        margin-bottom:auto;
    }
    .hero-banner-13 {
        height: 600px;
    }

        .hero-banner-13 .banner-image {
            height: 845px;
        }

            .hero-banner-13 .banner-image img {
                top: 40%;
            }
    
    .hero-banner-14 {
        height: 600px;
    }
        .hero-banner-14 .banner-image {
            height: 845px;
        }

            .hero-banner-14 .banner-image img {
                bottom: -230px;
            }

    .pad-v-md-20 {
        padding-top: 20px;
        padding-bottom: 20px;
    }

    .pad-t-md-45 {
        padding-top: 45px;
    }

    .pad-b-md-30 {
        padding-bottom: 30px;
    }

    .pad-b-md-60 {
        padding-bottom: 60px;
    }

    .txtCenter-md {
        text-align: center;
    }

    .margin-neg-85-l {
        margin-left: -85px;
    }
    .button-link-2 {
        border-radius: 20px;
        font-size: 15px;
        height: 35px;
        line-height: 17px;
        text-align: center;
        cursor: pointer;
        padding: 7px 28px;
        white-space: nowrap;
        color: #fff;
        background-color: #003778;
        border: 2px solid #003778;
        margin-left: auto;
    }

        .button-link-2:hover, .button-link-2:focus {
            color: #fff;
            background-color: #00549a;
            border-color: #00549a;
        }

        .button-link-2:focus {
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        a:hover.button-link-2 > .anchor-text,
        a:focus.button-link-2 > .anchor-text{
            text-decoration: none;
        }

}

@media (min-width: 1920px) {
    .container.infoblock-l > div{
        max-width: 1860px;
        margin:0 auto;
    }

    .background-img-responsive {
        margin-top: -410px;
    }

    .background-img-responsive > img {
        margin: auto;
    }
}
@media (min-width: 1440px) {
    .pad-h-xl-0 {
        padding-left: 0;
        padding-right: 0;
    }

    .pad-r-lg-15 {
        padding-right: 15px;
    }
    .pad-r-xl-0,
    .pad-r-xl-0.pad-r-60,
    .pad-r-xl-0.pad-r-45,
    .pad-r-xl-0.pad-r-15{
        padding-right: 0;
    }
    .pad-r-xl-25{
        padding-right: 25px;
    }
    .pad-r-xl-70, .title.pad-r-xl-70 {
        padding-right: 70px;
    }
    .pad-r-xl-60{
        padding-right: 60px;
    }

    .pad-l-xl-10 {
        padding-left: 10px;
    }

    .pad-l-xl-20 {
        padding-left: 20px;
    }

    .pad-l-xl-35 {
        padding-left: 35px;
    }
    .pad-l-xl-60 {
        padding-left: 60px;
    }
    .pad-t-xl-0 {
        padding-top: 0px;
    }

    .banner.margin-b-xl-30, .margin-b-xl-30 {
        margin-bottom: 30px;
    }

    .hero-banner-13 .banner-image {
        height: 1000px;
    }

    .hero-banner-13 .banner-image img {
        top: 50%
    }

    .height-xl-600 {
        height: 600px;
    }

    .fibrebanner .banner-image {
        height: 440px;
    }

    .pad-t-xl-10 {
        padding-top: 10px;
    }

    .pad-v-xl-25 {
        padding-top: 25px;
        padding-bottom: 25px;
    }

    .pad-t-xl-20 {
        padding-top: 20px;
    }

    .pad-t-xl-60 {
        padding-top: 60px;
    }

    .pad-b-xl-35 {
        padding-bottom: 35px;
    }

    .pad-b-xl-45 {
        padding-bottom: 45px;
    }

    .margin-h-xl-60 {
        margin-left: 60px;
        margin-right: 60px;
    }

    .margin-t-xl-110{
        margin-top: 110px;
    }

    .margin-r-xl-15{
        margin-right: 15px;
    }

    .width-xl-460{
        width: 460px;
    }
    /* Start of Bell Wifi 3 columns */
    .wifi-app-3-col > div:first-child {
        right: 70%;
        bottom:0;
    }
        .wifi-app-3-col > div:first-child img{
            width:285px;
        }
    .wifi-app-3-col > div:last-child {
        left: 73.5%;
        top: 60px;
    }
        .wifi-app-3-col > div:last-child img{
            width:285px;
        }
    /* End of Bell Wifi 3 columns */
    .container.infoblock-l.container-2{
        width: 100%;
        padding:0 30px;
    }
    .title-2{
        font-size: 40px;
        line-height: 46px;
    }
    .infoblock-l .infoblock-l-height,
    .infoblock-l .infoblock-l-height.infoblock-suburb-glow2{
        height: 800px;
    }
    .infoblock-l .infoblock-l-height .img-scaled{
        transform: scale(1.06);
    }
    .max-width-xl-260{
        max-width: 260px;
        width: 260px;
    }

}
@media (max-width: 767.98px) {
    .width-100-percent-xs {width: 100%;}
    .width-60-xs{width:60px;}
    .width-xs-215{width:215px;}
    .max-width-xs-300{width:300px;}
    .banner-2 {
        height: 360px;
        position: relative;
        margin-top: -80px;
    }

        .banner-2 img {
            position: absolute;
            right: -170px;
            height: 100%;
        }

    /* START Helper Class */
    .min-height-xs-0{min-height:0!important}
    .height-xs-135{height:135px}
    .height-xs-115{height:115px}
    .focus-h-xs-center{left:50%;transform:translateX(-50%)}
    .border-all-bottom-radius-10-xs {
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
    }
    .subtitle-2-reg-2 {
        line-height: 18px;
        font-size: 14px
    }
    .line-height-26-xs {
        line-height: 26px;
    }
    .line-height-18-xs {
        line-height: 18px;
    }
    .height-xs-114 {
        height: 114px
    }
    .width-125-xs{width:125px}

    .box-shadow-round-xs {
        box-shadow: 0 6px 25px 0 rgba(0,0,0,0.12);
    }

    .topCenter-centered-xs {
        top: 0;
        left: 50%;
        transform: translate(-50%,-50%);
    }

    .width-55-xs {
        width:55px;
    }


    /* END Helper Class */

    /* START Custom Class */
    .button-link {
        color: #00549a;
        text-decoration: underline;
    }

        .button-link:hover, .button-link:focus {
            text-decoration: none;   
        }

    .banner .banner-image {
        height: 310px;
    }

    .banner-image4 {
        max-height:340px;
    }

    /* END Custom Class */

    .hero-banner-12 {
        height: 655px;
    }

     /* Custom table with scroll */
     .table-scrollable-wrapper {
        border-right: 1px solid #d4d4d4;
    }
    .table-scrollable-wrapper::-webkit-scrollbar {
        height: 8px;
    }

    .table-scrollable-wrapper::-webkit-scrollbar-track {
        background: #e1e1e1;
        height: 8px;
    }

    .table-scrollable-wrapper::-webkit-scrollbar-thumb {
        height: 8px;
        background: #003778
    }

    .table-options {
        width: calc(767px - 112px);
    }

    .table-options th {
        min-width: 112px;
    }

    /*.sticky-first-column {
       padding: 15px 5px 15px 13px;
    }*/

    .sticky-first-column-cont > div:not(:first-child) div.same-height {
        padding: 15px 5px 15px 13px;
    }

    .sticky-first-column-cont {
        min-width: 112px;
    }
    
    .sticky-first-column-cont-modal {
        min-width: 115px;
    }

    .sticky-first-column-cont > div:first-child div.same-height {
        background-color: #003778;
        max-height: 60px;
    }

    .sticky-first-column-cont-modal > div:first-child div.same-height {
        background-color: #003778;
        height: 84px;
    }

    /*Custom padding for email info tooltip-modal mobile view*/
    .modal.modal-tooltip.modal-email-mobile .modal-body {
        padding: 15px 30px;
    }

    .scrollable-img{
        width: 100%;
    }

    .infoblock-slider.slick-initialized .slick-slide {
        padding: 30px;
        max-width: 260px;
    }

    .background-img-responsive .img-responsive {
        height: 345px;
    }

    .background-img-responsive-2 .img-responsive {
        height: 200px;
    }

    .background-img-responsive {
        margin-top: -30%;
    }

    .background-img-responsive .img-responsive {
        height: 250px;
    }

    .img-responsive.centerView-xs {
        margin-left: 49%;
        transform: translateX(-50%);
        max-width: 150%;
        width: 150%;
        height: 100%;
    }
    .img-responsive.centerView-xs-220 {
        margin-left: 50%;
        transform: translateX(-50%);
        max-width: 220%;
        width: 220%;
        height: 100%;
    }

    .margin-neg-10-t-xs {
        margin-top: -10px;
    }

    .wifi-pod-price-box, .wifi-pod-model {
        width: 100%;
        height: 100%;
    }

    .package-list-detail {
        column-count: 1;
        -webkit-column-count: 1;
        -moz-column-count: 1;
    }

    .content-expired-promotion {
        column-count: 2;
        -webkit-column-count: 2;
        -moz-column-count: 2;
    }

    .tablist-underlined [role=tab]:not(:last-child) {
        margin-right: 15px;
    }

    .bg-spotlight-corner-radial-gradiant {
        background: radial-gradient(closest-corner at 39% 42%, #1CBCF4 0%, #0E75CD 39.47%, #00549A 100%);
    }

    .sticky-first-column-modal {
        padding: 12px 15px;
    }

    .table-options-modal tr th{
        min-width: 167px;
        height: 84px;
    }

    .inverted.sticky-first-column-modal {
        height: 84px;
    }

    .herobanner .img-responsive{
        position: absolute;
    }

    .background-size-hero-banner-12 {
        background-size: 350% 55%;
    }

    .background-position-hero-banner-12 {
        background-position: 74% 100%;
    }
    .fibrebanner .banner-image{
        height: 215px;
    }
    .background-img-responsive-3{
        bottom: -180px;
        height: 435px;
        display: inline-block;
        min-width: 100%;
        margin-left: 50%;
        transform: translateX(-50%);
    }
    .background-img-responsive-3 .img-responsive.centerView-xs {
        margin-left: 50%;
        transform: translateX(-50%);
        width: auto !important;
        max-width: none;
        min-width: 100%;
    }

    .infoblock-l .infoblock-l-height.infoblock-suburb-glow{
        height: auto;
        margin-bottom: 180px;
    }

    .infoblock-l .infoblock-l-height.infoblock-suburb-glow2{
        height: auto;
        margin-bottom: 180px;
    }

    .wifi-pod-4-new {
        position: absolute;
        height: 50px;
        width: 140px;
        left: 0px;
        top: -20px;
    }

    .wifi-pod-container-xs {
        height: 50px;
    }

}

@media (min-width: 320px) and (max-width: 767.98px) {
    
    .store-icon-size {
        width: 120px;
    }

    .store-icon-size2 {
        width: 135px;
    }

    .width-300 {
        width: 100%;
    }

    .margin-neg-120 {
        margin-left: 0px;
    }

    .border-xs-none {
        border: none;
    }

    .wifi-pod-detail {
        position: relative;
        display: flex;
        top: 0;
        left: 0;
        transform: none;
        justify-content: center;
        width: 320px;
    }

    .pods-horizontal-line:before {
        bottom: 90px;
        left: 150px;
    }

    .label-3radios:before {
        top: 75px;
        right: 112px;
    }

    .label-3radios:after {
        right: 116px;
        top: -15px;
    }

    .pods-horizontal-line:after {
        top: 86px;
        left: 113px;
        height: 53px;
        -webkit-transform: skew(-121deg);
        -moz-transform: skew(-121deg);
        transform: skew(-121deg);
    }

    .label-500mbps:after {
        left: 70px;
    }

    .wifi-pod-model {
        width: 320px;
        height: 233px;
    }
    
    .banner .banner-image3 img{
        left:0;
        right:0;
        max-width:285px;
    }
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
    .modal-open .modal-dialog .modal-body.scrollAdjust.scroll-end:not(*:root) {
        margin-right: 0;
        padding-right: 15px;
    }
}

/* START Animation */
.left-glow-animation {
    position: absolute;
    top: -280px;
    -webkit-animation: left-animation 1s linear;
    animation: left-animation 1s linear;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-transform-origin: center center;
    -ms-transform-origin: center center;
    transform-origin: center center;
}
.right-glow-animation {
    position: absolute;
    bottom: -280px;
    -webkit-animation: right-animation 1s linear;
    animation: right-animation 1s linear;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-transform-origin: center center;
    -ms-transform-origin: center center;
    transform-origin: center center;
}

@keyframes left-animation {
  0%   {top: -280px;}   
  100% {top: 380px;}
}
@keyframes right-animation {
  0%   {bottom: -280px;}
  100% {bottom: 380px}
}

.animation-container-left{left:8%;}
.animation-container-right{right:8%;}

.animation-container-left .left-glow-animation {left:0;}
.animation-container-right .right-glow-animation{right:0;}

@media (max-width: 767.98px) {
    .animation-container-left{left:-70px;}
    .animation-container-right{right:-70px;}
}
/* END Animation */

@media (min-width: 1200px){
    .margin-neg-120 {
        margin-left: -100px;
    }

    .pad-l-xl-20 {
        padding-left: 40px;
    }
}

@media (min-width: 1240px) {
    /*Start Helper Class*/
    .margin-b-lg-15{
        margin-bottom: 15px;
    }
    .margin-b-lg-60{
        margin-bottom: 60px;
    }
    .pad-l-lg-60{
        padding-left: 60px;
    }
    .pad-r-lg-20{
        padding-right: 20px;
    }
    .pad-r-lg-40{
        padding-right: 40px;
    }
    .pad-r-lg-60{
        padding-right: 60px;
    }
    .pad-r-lg-150{
        padding-right: 150px;
    }
    /*End Helper Class*/
}

@media (min-width: 1330px) {
    .infoblock-slider .slick-prev {
        left: -80px;
    }

    .infoblock-slider .slick-next {
        right: -80px;
    }
}

@keyframes pulse {
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

@keyframes scale-rotate {
    50% {
        transform: scale(1);
    }

    100% {
        transform: scale(0.5) rotate(40deg);
    }
}

@keyframes rotate-light {
    0%,100% {
        transform: rotate(20deg);
    }
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
    #check-availability-2 .modal-body.scrollAdjust:not(*:root) {
        padding-right: 15px;
        margin-right: 0px;
    }
}

@media screen and (-ms-high-contrast: active) and (min-width: 768px), (-ms-high-contrast: none) and (min-width: 768px) {
    .render-bullet li:nth-child(n+3) {
        list-style-position: inside;
    }
}