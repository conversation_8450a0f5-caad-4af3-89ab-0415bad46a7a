import * as React from "react";
import { FormattedFromToDate, FormattedFromToDateSR, getBillPeriodByBillCloseDate, getCharge, getGroupedItems, getLobData, getPreviousData } from "../../utils/Utility";
import { IBillExplainerBody } from "./IBillExplainerBody";
import { fixPBELayout, refreshStickyNav, FormattedDollarsAmount, ExtractedFormattedMessage, CustomFormattedDateWithoutYearLong } from "singleban-components";
import { FormattedMessage } from "react-intl";
const PBENewActivation = (props: IBillExplainerBody) => {
    const { currentBillChgItems, partialChgCrdItems, refundItems } = getGroupedItems(props.chargeItems, props.cycleStartDate);
    if (refundItems.length >= 1) { // no refund section in this page
        refundItems.map((item) => {
            partialChgCrdItems.push(item);
        });
    }
    const partialChgCrdFilter = partialChgCrdItems.filter((charge) => {
        if (charge.amount > 0) {
            return charge;
        }
        return null;
    });

    const service = props.subscriberDetails?.subscriberType;
    const { lobName } = getLobData(service);
    const partialChgCrdItem = partialChgCrdItems?.length && partialChgCrdItems?.length >= 1 ? partialChgCrdFilter[0] : null;
    const newServiceChargePartial = getCharge(partialChgCrdItems);
    const newServiceChargeMonthly = getCharge(currentBillChgItems);
    let { serviceModifyDate, chargedDays, width } = getPreviousData(props.cycleStartDate, props.cycleEndDate, partialChgCrdItem);
    const { currentStartDate, currentEndDate, prevStartDate, prevEndDate } = getBillPeriodByBillCloseDate(props.billCloseDate);
    if(chargedDays === 0){
        width = 100;
    }
    // if (partialChgCrdItems?.length === 0 && props.pbeCategory === "NEW_ACTIVATION")
    // {
    //     const recurringChargesItems = getRecurringChargesItems(props.chargeItems);
    //     newServiceChargePartial = getCharge(recurringChargesItems);
    //     chargedDays = 30;
    //     width = 100;
    // }

    React.useEffect(() => {
        refreshStickyNav();
    });
    React.useEffect(() => {
        fixPBELayout(true);
    });
    return (
        <div className="container pad-b-45">
            <div className="pbe-chart pbe-new-activation-chart col-sm-10 margin-auto pad-30 pad-xs-0 borderRadiusAll10 border-gray2 no-borders-xs pad-h-xs-0">
                <h3 className="subtitle-2 margin-b-15">
                    <FormattedMessage id={lobName}>
                        {(serviceName: string) => <ExtractedFormattedMessage id="PAGE_NEW_ACTIVATION_HEADER3" values={{ service: serviceName }} />}
                    </FormattedMessage>
                </h3>
                <p className="margin-b-10">
                    {/* <FormattedHTMLMessage id={props.description} /> */}
                    {/* <ExtractedFormattedMessage id="PAGE_NEW_ACTIVATION_BODY1" /> */}
                </p>
                <ul className="margin-b-30 margin-l-15 pad-l-0">
                    <li>
                        <FormattedDollarsAmount amount={newServiceChargePartial} showHyphenIfAmountIsNull={false}>{(amount: string) => <ExtractedFormattedMessage id="PAGE_NEW_ACTIVATION_BODY1" values={{ amount: amount }} />}</FormattedDollarsAmount>
                    </li>
                    <li>
                        <FormattedDollarsAmount amount={newServiceChargeMonthly} showHyphenIfAmountIsNull={false}>{(amount: string) => <ExtractedFormattedMessage id="PAGE_NEW_ACTIVATION_BODY2" values={{ amount: amount }} />}</FormattedDollarsAmount>
                    </li>
                </ul>
                <div className="pbe-wrapper row pad-h-15 same-height-wrap">
                    <div className="pbe-col col-6 pad-4-right">
                        <div className="txtCenter margin-b-15">
                            <h3 className="subtitle-2 d-flex justify-center align-items-end same-height" data-same-height-index="1">
                                <ExtractedFormattedMessage id="PREVIOUS_BILL" />
                            </h3>
                            <div className="txtSize12">
                                <span aria-hidden="true"><FormattedFromToDate startDate={prevStartDate} endDate={prevEndDate} /></span>
                                <span className="sr-only"><FormattedFromToDateSR startDate={prevStartDate} endDate={prevEndDate} /></span>
                            </div>
                        </div>
                        <div className="sr-only">
                            <div>
                                <ExtractedFormattedMessage id="PAGE_NEW_ACTIVATION_BEFORE_ACTIVATION" />
                                <FormattedFromToDate startDate={prevStartDate} endDate={serviceModifyDate} />
                                {/* No service {Month day} to {Month day} */}
                            </div>
                            <div>
                                <ExtractedFormattedMessage id="PAGE_NEW_ACTIVATION_SERVICE_ACTIVATED" />
                                <CustomFormattedDateWithoutYearLong date={serviceModifyDate}>{(date: string) => date}</CustomFormattedDateWithoutYearLong>
                                {/* Service activated February 10 */}
                            </div>
                            <div>
                                <ExtractedFormattedMessage id="PAGE_NEW_ACTIVATION_NEW_SERVICE" />
                                <ExtractedFormattedMessage id="PAGE_NEW_ACTIVATION_NEW_SERVICE_BODY" values={{ days: chargedDays }} />
                                <FormattedDollarsAmount amount={newServiceChargePartial} showHyphenIfAmountIsNull={false}>{(amount: string) => amount}</FormattedDollarsAmount>
                                {/* New service (charge for 3 days) XX.XX dollars */}
                            </div>
                        </div>
                        <div className="js-split-wrapper relative invisible-force" data-split-divider-width="8" data-split-divider-margin="2" data-js-split-percentage={width}>
                            <div className="upper-line-container relative bgGray19 pad-t-20" aria-hidden="true">
                                <div className="pbe-line"></div>
                            </div>
                            <div className="bars-container single-bar bgGray19" aria-hidden="true">
                                <div className="d-flex height-31">
                                    <div className="split-bar-left overflowHidden">
                                        <div className="h-100 bg-stripe-blue"></div>
                                    </div>
                                    <div className="split-bar-divider"></div>
                                    <div className="split-bar-right overflowHidden">
                                        <div className="h-100 bgBlue"></div>
                                    </div>
                                </div>
                                <div className="split-divider-indicator pbe-prev-package-change-blue-line-inner">
                                    <div className="h-100"></div>
                                </div>
                            </div>
                            <div className="labels-container" aria-hidden="true">
                                <div className="label-group-top relative bgGray19">
                                    <div className="split-divider-label d-flex flex-row">
                                        <div className="w-100">
                                            <div className="surtitle-black">
                                                {/* Service activated */}
                                                <ExtractedFormattedMessage id="PAGE_NEW_ACTIVATION_SERVICE_ACTIVATED" />
                                            </div>
                                            <div>
                                                {/* February 10 */}
                                                <CustomFormattedDateWithoutYearLong date={serviceModifyDate}>{(date: string) => date}</CustomFormattedDateWithoutYearLong>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="label-group-bottom relative">
                                    <div className="split-origin-label d-flex">
                                        <div className="label-indicator-line-origin">
                                        </div>
                                        <div>
                                            <div className="surtitle-black">
                                                {/* No service */}
                                                <ExtractedFormattedMessage id="PAGE_NEW_ACTIVATION_BEFORE_ACTIVATION" />
                                            </div>
                                        </div>
                                    </div>
                                    <div className="split-right-label d-flex flex-column">
                                        <div className="label-indicator-line-bottom">
                                            <div></div>
                                        </div>
                                        <div className="w-100">
                                            <div className="surtitle-black">
                                                {/* New service */}
                                                <ExtractedFormattedMessage id="PAGE_NEW_ACTIVATION_NEW_SERVICE" />
                                            </div>
                                            <div className="small-text margin-3-top">
                                                {/* (charge for 3 days) */}
                                                <ExtractedFormattedMessage id="PAGE_NEW_ACTIVATION_NEW_SERVICE_BODY" values={{ days: chargedDays }} />
                                            </div>
                                            <div className="small-text">
                                                {/* <span>$</span>XX.XX */}
                                                <FormattedDollarsAmount amount={newServiceChargePartial} showHyphenIfAmountIsNull={false}>{(amount: string) => amount}</FormattedDollarsAmount>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="pbe-col col-6 pad-4-left">
                        <div className="txtCenter margin-b-15">
                            <h3 className="subtitle-2 d-flex justify-center align-items-end same-height" data-same-height-index="1">
                                <ExtractedFormattedMessage id="CURRENT_BILL" />
                            </h3>
                            <div className="txtSize12">
                                {/* <span aria-hidden="true">Feb 20 - Mar 19</span> <span className="sr-only">February 20 to March 19</span>
                                     */}
                                <span aria-hidden="true"><FormattedFromToDate startDate={currentStartDate} endDate={currentEndDate} /></span>
                                <span className="sr-only"><FormattedFromToDateSR startDate={currentStartDate} endDate={currentEndDate} /></span>
                            </div>
                        </div>
                        <div className="sr-only">
                            <div>
                                <ExtractedFormattedMessage id="PAGE_NEW_ACTIVATION_NEW_SERVICE_ADVANCE" />
                                <ExtractedFormattedMessage id="PAGE_NEW_ACTIVATION_NEW_SERVICE_ADVANCE_BODY" />
                                <FormattedDollarsAmount amount={newServiceChargeMonthly} showHyphenIfAmountIsNull={false}>{(amount: string) => amount}</FormattedDollarsAmount>
                                {/* New service (charged one month in advance) XX.XX dollars */}
                            </div>
                        </div>
                        <div className="js-split-wrapper relative invisible-force" data-split-divider-width="8" data-split-divider-margin="2" data-js-split-percentage="null">
                            <div className="upper-line-container relative bgGray19 pad-t-20" aria-hidden="true">
                                <div className="pbe-line"></div>
                            </div>
                            <div className="bars-container single-bar bgGray19" aria-hidden="true">
                                <div className="d-flex height-31">
                                    <div className="split-bar-left overflowHidden">
                                        <div className="h-100 bgGray19"></div>
                                    </div>
                                    <div className="split-bar-divider"></div>
                                    <div className="split-bar-right overflowHidden">
                                        <div className="h-100 bgBlue"></div>
                                    </div>
                                </div>
                            </div>
                            <div className="labels-container" aria-hidden="true">
                                <div className="label-group-top relative bgGray19">
                                </div>
                                <div className="label-group-bottom relative">
                                    <div className="split-right-label d-flex flex-column">
                                        <div className="label-indicator-line-bottom">
                                            <div></div>
                                        </div>
                                        <div className="w-100">
                                            <div className="surtitle-black">
                                                {/* New service */}
                                                <ExtractedFormattedMessage id="PAGE_NEW_ACTIVATION_NEW_SERVICE_ADVANCE" />
                                            </div>
                                            <div className="small-text margin-3-top">
                                                {/* (charged one month in advance) */}
                                                <ExtractedFormattedMessage id="PAGE_NEW_ACTIVATION_NEW_SERVICE_ADVANCE_BODY" />
                                            </div>
                                            <div className="small-text">
                                                {/* <span>$</span>XX.XX */}
                                                <FormattedDollarsAmount amount={newServiceChargeMonthly} showHyphenIfAmountIsNull={false}>{(amount: string) => amount}</FormattedDollarsAmount>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PBENewActivation;