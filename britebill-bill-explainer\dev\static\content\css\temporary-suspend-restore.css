﻿/* NOTE: use mobile-first media queries. place your styles under proper categories. */
/* START - Component Styles (can be moved to core) */
.standard-step-flow-header .standard-step-flow-header-upper {
    min-height: 54px;
}

.standard-step-flow-header-upper a:focus,
.standard-step-flow-header-upper button:focus {
    outline: none;
    box-shadow: 0 0 0 3px #00549a, 0 0 2px 3px #00549a, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}

.standard-step-flow-header .btn.btn-primary-white {
    padding: 7px 28px;
}

.standard-minimal-footer .legal-links ul li {
    margin-bottom: 0;
}

    .standard-minimal-footer .legal-links ul li:not(:first-child) {
        margin-top: 10px;
    }

.standard-minimal-footer .skip-to-main-link {
    display: none;
    padding: 7px 12px;
    position: absolute;
    left: -300px;
    text-decoration: underline;
    transition: left .3s ease-out;
    background-color: #e1e1e1;
    z-index: 3000;
    font-size: 13px;
    color: #00549a;
}

    .standard-minimal-footer .skip-to-main-link:focus {
        left: 0;
    }

.tsrs-main .accordionContainer > .accordion-wrap > div > a[aria-expanded="true"] > span:not(.icon) {
    color: #111111;
}

.tsrs-main .accordionContainer > .accordion-wrap > div > a[aria-expanded="false"] > span:not(.icon) {
    color: #00549a;
}

@media (min-width:768px) {
    .standard-minimal-footer .legal-links ul li {
        display: flex;
        align-items: center;
    }

        .standard-minimal-footer .legal-links ul li:not(:first-child) {
            margin-top: 0;
        }

        .standard-minimal-footer .legal-links ul li:not(:last-child):after {
            border-right: 1px solid #D4D4D4;
            content: '';
            display: inline-block;
            height: 12px;
            margin: 0 6px;
        }
}

@media (min-width:992px) {
    .standard-step-flow-header .standard-step-flow-header-upper {
        min-height: 75px;
    }

    .standard-minimal-footer .skip-to-main-link {
        display: block;
    }
}
/* END - Component Styles */
/* START - Helper Styles */
.txtSize12 {
    font-size: 12px;
}

.txtSize14 {
    font-size: 14px;
}

.txtSize77 {
    font-size: 77px;
}

.iconSize84 {
    font-size: 84px;
}

.txtGrey2{
    color: #7A7A7A;
}

.line-height-14 {
    line-height: 14px;
}

.line-height-18 {
    line-height: 18px;
}

.line-height-22 {
    line-height: 22px;
}
.line-height-26 {
    line-height: 26px;
}

.line-height-28 {
    line-height: 28px;
}

.margin-t-1 {
    margin-top: 1px;
}

.margin-t-24 {
    margin-top: 24px;
}

.bg-very-light-grey {
    background-color: #f4f4f4;
}

.bg-very-light-grey-v2 {
    background-color: #e1e1e1;
}

.bg-very-light-grey-v3 {
    background-color: #e4e4e4;
}

.border-gray3 {
    border: 1px solid #d7d7d7
}

.txtGreen2{
    color:#378E42;
}

.txtVeryLightGrey {
    color: #CBCBCB;
}

.txtCeladonBlue-bgGray {
    color: #0D5F7D;
}

.txtAquaBlue-bgGray {
    color: #016D98;
}

    .txtAquaBlue-bgGray:focus,
    .txtAquaBlue-bgGray:hover {
        color: #0D5F7D !important;
    }

.txtAquaBlue-bgWhite {
    color: #007cad;
}

    .txtAquaBlue-bgWhite:focus,
    .txtAquaBlue-bgWhite:hover {
        color: #0d5f7d;
    }

.lnkUnderline {
    text-decoration: underline;
}

    .lnkUnderline:hover,
    .lnkUnderline:focus {
        text-decoration: none;
    }

.borderRadiusAll4 {
    border-radius: 4px;
}

.fullPadding-t {
    padding-top: 100%;
}
.pad-h-30{
    padding-left:30px !important;
    padding-right:30px !important;
}

.pad-l-11{
    padding-left:11px;
}

.width-115 {
    width: 115px;
}
.width-90{
    width:90px;
}

.min-width-110 {
    min-width: 110px;
}

.min-height-150 {
    min-height: 150px;
}

.modal-header-border-radius-0 {
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
    border-radius: 0;
}

@media (min-width:768px) {
    .txtSize14-sm {
        font-size: 14px;
    }

    .txtSize18-sm {
        font-size: 18px;
    }

    .line-height-18-sm {
        line-height: 18px;
    }

    .line-height-22-sm {
        line-height: 22px;
    }

    .margin-t-0-sm {
        margin-top: 0;
    }

    .margin-t-5-sm {
        margin-top: 5px;
    }

    .two-column-arrow-divider {
        display: flex;
        flex-wrap: wrap;
    }

        .two-column-arrow-divider > div:first-child {
            border-bottom: 1px solid #e1e1e1;
            padding: 0 0 20px 0;
            display: flex;
            justify-content: center;
        }

            .two-column-arrow-divider > div:first-child:before {
                width: 20px;
                height: 20px;
                position: absolute;
                right: 50%;
                transform: rotate(135deg) translateX(-50%);
                border-bottom: none;
                border-left: none;
                background-color: #f4f4f4;
                top: 99%;
            }

        .two-column-arrow-divider > div:last-child {
            display: flex;
            justify-content: center;
            padding-top: 35px;
        }
}

/*  */
.two-column-arrow-divider > div:first-child {
    border: none;
    padding: 0 40px 0 0;
    display: flex;
}

    .two-column-arrow-divider > div:first-child:before {
        content: "";
        width: 20px;
        height: 20px;
        right: 4px;
        transform: rotate(45deg) translateY(-100%);
        border-bottom: none;
        border-left: none;
        background-color: #f4f4f4;
        top: 50%;
    }

.two-column-arrow-divider > div:last-child {
    padding: 0 0 0 40px;
}

.icon-text-container {
    min-width: 260px;
}

    .icon-text-container > div > span.icon {
        margin-top: 10px
    }



.modal-header-border-radius-0-sm {
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
    border-radius: 0;
}

/* END - Helper Styles */
.is_tabbing .tsrs-main .graphical_ctrl > input[type="radio"]:focus ~ .ctrl_element:before {
    content: " ";
    display: block;
    position: absolute;
    left: -1px;
    top: -1px;
    width: 24px;
    height: 24px;
    border-radius: 50% !important;
    background-color: transparent;
    box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
}

.tsrs-main .graphical_ctrl > input[type="radio"]:checked:focus ~ .ctrl_element {
    /* box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc !important; */
    outline: none !important;
}

.tsrs-main .ctrl_element {
    outline: none !important;
    top: 50%;
    margin-top: -12px;
}

.tsrs-main .graphical_ctrl > input[type="radio"]:checked ~ label {
    font-weight: bold;
}

.tsrs-main .graphical_ctrl > [type="radio"] {
    z-index: 1;
}

    .tsrs-main .graphical_ctrl > [type="radio"] + span {
        z-index: 0;
    }

.tsrs-main .box-shadow-bell-rdo {
    box-shadow: 0 6px 25px 0 rgba(225,225,225);
}


.tsrs-main .form-control-select + span {
    right: 0px;
}

.tsrs-main .graphical_ctrl.ctrl_radioBtn .radio-button-dark-border {
    box-shadow: inset 0 0px 3px 0 rgb(0 0 0 / 20%) !important;
    border: 1px solid #949596 !important;
}

/* START - Non-Component Focus Outline Styles */

footer .skip-to-main-link {
    display: inline-block;
    padding: 7px 12px;
    position: absolute;
    left: -300px;
    text-decoration: underline;
    border-bottom-right-radius: 8px;
    transition: left .3s ease-out;
    background-color: #e1e1e1;
    z-index: 3000;
    font-size: 13px;
    color: #00549a;
}

    footer .skip-to-main-link:focus {
        left: 0;
    }

.icon-small_icon_select_trigger_half:before {
    content: "\e920";
}

/* END - Non-Component Focus Outline Styles */
/*START - CSS For modals */
.tsrs-body .modal-header {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

/* tablet and larger */
@media (min-width: 768px) {
    .tsrs-body .modal-content {
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
    }

    .tsrs-body .modal-dialog .modal-content .modal-header .close:focus {
        box-shadow: 0 0 0 3px #f0f0f0, 0 0 2px 3px #fff, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
    }

    .tsrs-body .modal-body.scrollAdjust {
        overflow-y: auto;
    }
    .two-column-arrow-divider-md > div:first-child {
        border: none;
        padding: 0 0 0 0;
        display: flex;
    }

        .two-column-arrow-divider-md > div:first-child:before {
            content: "";
            width: 20px;
            height: 20px;
            position: absolute;
            right: 4px;
            transform: rotate(45deg) translateY(-100%);
            background-color: #F4F4F4;
            top: 50%;
            z-index:100;
        }

    .no-pad-md-top {
        padding-top: 0
    }

    .pad-h-md-15 {
        padding-right: 15px !important;
        padding-left: 15px !important;
    }
    .margin-h-7_half-sm {
        margin-right: 7.5px;
        margin-left: 7.5px;
    }

    .max-width-sm-283{
        max-width: 283px;
    }

/*END - CSS For modals */
}

@media(min-width: 320px) and (max-width: 767.98px) {
    .txtLeft-xs {
        text-align: left;
    }

    .col1-xs {
        width: 100%
    }

    .pad-t-xs-9 {
        padding-top: 9px;
    }

    .margin-14-bottom-xs {
        margin-bottom: 14px
    }

    .no-border-xs {
        border: none;
    }

    .two-column-arrow-divider,
    .two-column-arrow-divider-md {
        display: flex;
        flex-wrap: wrap;
    }

        .two-column-arrow-divider > div:first-child,
        .two-column-arrow-divider-md > div:first-child {
            padding: 0 0 20px 0;
            display: flex;
            justify-content: center;
        }

            .two-column-arrow-divider > div:first-child:before,
            .two-column-arrow-divider-md > div:first-child:before {
                content: "";
                width: 20px;
                height: 20px;
                position: absolute;
                right: 50%;
                transform: rotate(135deg) translateX(-50%);
                border-bottom: none;
                border-left: none;
                background-color: #f4f4f4;
                top: 97%;
                z-index: 100;
            }

        .two-column-arrow-divider > div:last-child,
        .two-column-arrow-divider-md > div:last-child {
            display: flex;
            justify-content: center;
            padding-top: 35px;
        }
}
/*END - CSS For modals */
@media (min-width: 768px) and (max-width: 991.98px) {
    .pad-h-sm-15 {
        padding-right: 15px !important;
        padding-left: 15px !important;
    }

    .line-height-md-38{
        line-height: 38px;
    }
    /* .block-sm{
        display: block;
    } */
    .no-pad-sm {
        padding: 0 !important;
    }
}


/* mobile */
@media screen and (max-width: 767.98px) {
    .no-border-h-xs {
        border-right: 0;
        border-left: 0;
    }

    .left-xs-0 {
        left: 0 !important;
    }

    .fullWidth-xs-100 {
        width: 100%;
    }
    .padding-h-xs-15{
        padding-left: 15px !important;
        padding-right: 15px !important;
    }
    .modal-header-border-radius-0-xs {
        border-bottom-left-radius: 0px;
        border-bottom-right-radius: 0px;
        border-radius:0;
    }
    .button-grid {
        display: grid;
    }
    .align-items-start-xs{
        align-items: flex-start !important;
    }
    .align-items-center-xs {
        align-items: flex-start !important;
    }
    .line-height-28-xs{
        line-height: 28px;
    }
    .flex-xs-column-reverse {
        flex-direction: column-reverse!important;
    }

    .img-mobile {
        min-width: 38px;
        height: 77px;
    }

    .flex-column-xs {
        flex-direction: column;
    }

}

.img-portrait-device{
    width: 90px;
    height: auto;
    display: inline-flex;
    justify-content: center;
    align-items:baseline;
}

.borderRadius10 {
    border-radius: 10px;
}

.w100 {
    width: 100%;
}