    (function ($) { 
    var resizeTimeoutFn;
    var slickOnMobile = $('.slick-slider-mobile-js').length || 0;
    var slickActive = false;
    // tv on demand slider
    
        

    // wait for bell.js to fire the lazy-loaded event then try to reposition the logos after a short delay (to allow the image to be loaded)
    $('.channel-card img').on('lazy-loaded', function () {
        var logo = $(this);

        setTimeout(function () {
            if (logo.height() > 0) {
                logo.addClass('lazy-loaded');
                repositionChannelLogos(logo);
            }
        }, 300);
    });

    // put resize event callback functions here
    $(window).on('resize', function () {
        if($('.sticky-element').has('scrollTrue')){
            $('.scrollableContainerShadow-table').css("height", "calc(100% - 17px)");
        }
        clearTimeout(resizeTimeoutFn);
        resizeTimeoutFn = setTimeout(function () {
            // call repositionChannelLogos upon window resize to reposition the logos accordingly
            $('.channel-card img.lazy-loaded').each(function () {
                repositionChannelLogos($(this));
            });

            // toggle mobile slider
            toggleMobileSlider();
        }, 200);

        var on_demand_sliders = $('.movie-slider');

        if(on_demand_sliders){

            if ($(window).width() < 992) {
                on_demand_sliders.not('.slick-initialized').slick();
            } 

            removeBtnFirstLastSlider(on_demand_sliders);
        }

        // Added condition for new construction homes sliders
        var construction_homes_sliders = $('.infoblock-slider');
        if (construction_homes_sliders) {

            removeBtnFirstLastSlider(construction_homes_sliders);
        }

    });

    $(".actual-tabs-controller-js").on("click", function (e) {
        if (window.matchMedia('(max-width: 767px)').matches) {
            reactivateMobileSliderInTabPanel($(e.target).closest('[aria-controls]').attr('aria-controls'));
        }
    });

    function repositionChannelLogos(logo) {
        var logoParent = logo.parent(),
            logoHeight,
            card,
            cardDesc,
            cardDetails,
            spaceAvailable,
            offsetTop;

        // apply the custom offset only on mobile
        if (window.matchMedia("(max-width: 767.98px)").matches) {
            // try to middle-align the heading and logo
            logoHeight = logo.height();
            card = logoParent.closest('.channel-card');
            cardDesc = card.find('.card-desc');
            cardDetails = card.find('.card-details');
            spaceAvailable = cardDesc.offset().top - card.offset().top;
            offsetTop = parseInt(cardDetails.css('padding-top'));

            // if the logo will exceed the available space, we'll just retain the default top offset
            if (logoHeight + offsetTop < spaceAvailable) {
                logoParent.css('margin-top', (spaceAvailable - logoHeight - offsetTop) / 2 + 'px');
            }
        } else {
            // remove the added margin on tablet and larger
            logoParent.css('margin-top', '');
        }
    }

    function reactivateMobileSliderInTabPanel(TabPanelSlider) {
        var mobileTabPanelSlider = TabPanelSlider || 'div[role="tabpanel"]';
        $('#' + mobileTabPanelSlider + ' .slick-slider-mobile-js.slick-initialized').slick('unslick');
        activateSlider('#' + mobileTabPanelSlider + ' .slick-slider-mobile-js');
    }

    function activateSlider(slider) {
        var mobileSlider = slider || '.slick-slider-mobile-js';
        $(mobileSlider).each(function (key, item) {
            if (!$(item).hasClass('slick-initialized')) {
                $(item).slick({
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    dots: true,
                    arrows: false,
                    waitForAnimate: false,
                    infinite: false,
                    speed: 500
                }).on('beforeChange', function (event, slick, currentSlide, nextSlide) {
                    var slickSlideCount = $(this).find('.slickSlide').length;
                    var $slider = $(this);
                    $slider.find('.slick-list').removeAttr('aria-live');
                    $slider.find('.slick-track').removeClass('margin-n30-left').removeClass('margin-n15-left');
                    if (!$slider.hasClass('full-width-slide')) {
                        if (nextSlide == 0) {
                            $slider.find('.slick-track').addClass('margin-n30-left');
                        } else if (nextSlide == (slickSlideCount - 1)) {
                            $slider.find('.slick-track').addClass('margin-n15-left');
                        }
                    }
                }).on('afterChange', function (event, slick, currentSlide, nextSlide) {
                    var $slider = $(this);
                    setTimeout(function () {
                        $slider.find('.slick-slide.slick-active').attr('role', 'option');
                        $slider.find('.slick-slide').attr('aria-hidden', 'true');
                        $slider.find('.slick-list').attr('aria-live', 'polite');
                        $slider.find('.slick-slide.slick-active').attr('aria-hidden', 'false');
                        $slider.find('.slick-slide.slick-active').attr('role', 'tabpanel');
                    }, 500);
                });
            }
        });
        $('.slick-slider-mobile-js:not(.full-width-slide)').find('.slick-track').addClass('margin-n30-left');
        $('.slick-slider-mobile-js').find('.slick-list').attr('aria-live', 'polite');
    }

    function removeBtnFirstLastSlider(sliders) {
        sliders.each(function(key, item){
            $(this).find('.slick-arrow.slick-disabled').removeAttr('style');
        });
    }

    function repositionImageLabels() {
        $('.panel-img').each(function () {
            var $this = $(this);
            var first_child = $this.find('.label-container:first-child > div').length;
            var last_child = $this.find('.label-container:last-child > div').length;
            if (first_child == 1 && last_child == 1) {
                $this.find('.label-container > div').css('margin-left','20%');
            } else {
                $this.find('.label-container > div').css('margin-left', '');
            }
        });
    }

    function toggleMobileSlider() {
        var sliderMobile;
        var on_demand_sliders = $('.movie-slider');
        var construction_homes_sliders = $('.infoblock-slider');

        if (window.matchMedia('(max-width: 767px)').matches) {
            
            if (slickOnMobile > 0 && !slickActive) {
                activateSlider();
                slickActive = true;
            }
            $('.panel-img').find('.label-container > div').css('margin-left', '');
        } else {
            
            if (slickOnMobile > 0 && slickActive) {
                sliderMobile = $('.slick-slider-mobile-js');
                sliderMobile.slick('unslick');
                slickActive = false;

                // tab events are being removed upon unslick so let's reinit
                ActualTabs.reinit(sliderMobile.find('[role=tab]'));
                removeBtnFirstLastSlider(on_demand_sliders);
                removeBtnFirstLastSlider(construction_homes_sliders);
            }
            repositionImageLabels();
        }
    }

    // initialize mobile slider if on mobile
    toggleMobileSlider();

    
})(jQuery);

var scrollableSteps = {
    init: function() {
        var self = this;
        $('.scrollable-steps .scroll-line').css('height', $('div[role=tabpanel] > .step-container .step-list.active').innerHeight());
        self.makeScrollStep();
        self.resizeWindowStep();
    }, 
    makeScrollStep: function() {
        $('.scrollable-steps .step-group .step-list').on('click', this._stepClickListener);
    },
    resizeWindowStep: function() {
        var self = this;
        var resizeTimeoutStep;
        $(window).resize(function () {
            clearTimeout(resizeTimeoutStep);
            resizeTimeoutStep = setTimeout(function () {
                $('.scrollable-steps .step-group .step-list').off('click', self._stepClickListener);
                if ($(window).width() > 767) {
                    $('.scrollable-steps .scroll-line').css('height', $('div[role=tabpanel] > .step-container .step-list.active').innerHeight());
                    self.makeScrollStep();
                }
            }, 300);
        });
    }, _stepClickListener: function () {
        var $el = $(this),
            stepActive = $el.attr('data-step'),
            parentEl = $el.closest('.scrollable-steps'),
            scrollLine = parentEl.find('.scroll-line');

        parentEl.find('.step-group .step-list').removeClass('active');
        $el.addClass('active');
        parentEl.find('.scrollable-img-contents .scrollable-img').removeClass('active');
        parentEl.find('.scrollable-img-contents .scrollable-img[data-step-img="' + stepActive + '"]').addClass('active');
        scrollLine.css('height', $el.innerHeight());
        scrollLine.animate({ top: $el.position().top }, 300);
    }
};
if($('.scrollable-steps').length > 0)
{
    scrollableSteps.init();
}
// on demand

function addModalAddOnTabsEventListener() {
 
    if ($('#selectLanguage').length > 0 || $('#selectLanguageFibeTV').length > 0 || $('#selectLanguageSatelliteTV').length > 0) {
        $('#selectLanguage,#selectLanguageSatelliteTV,#selectLanguageFibeTV').on('change', function () {
            var $el = $(this),
                siblingEl = $el.closest('.language-selection').next();

            siblingEl.children().hide();
            siblingEl.find('#' + $el.val()).show();
        });
    }
    
} 
addModalAddOnTabsEventListener();

function addModalPrintTVEventListener() {
    if ($('input[name="print-tv-packages"]').length > 0) {
        $('input[name="print-tv-packages"]').on('change', function (e) {
            if ($(this).closest('div').is(':last-child') && $(this).prop('checked')) {
                $(this).closest('div[role="radiogroup"]').next().show();
            }
            else {
                $(this).closest('div[role="radiogroup"]').next().hide();
            }
            e.stopImmediatePropagation();
        });
    }
}
addModalPrintTVEventListener();

var displayAccordionDescOnChannels = {
    init: function () {
        var self = this;
        self.addEventListener();
        self.addAriaHidden();
        self.onMobileResize();
    },
    addEventListener: function () {
        $('.expander-description-control').on('click', function (e) {
            var $this = $(this);

            if ($this.attr('aria-expanded') == 'false') {
                $this.prev().css('max-height', '10000px').attr('aria-hidden', 'false');
                $this.attr('aria-expanded', 'true');

            }
            else {
                $this.prev().removeAttr('style').attr('aria-hidden', 'true');
                $this.attr('aria-expanded', 'false');
            }
            e.stopImmediatePropagation();

        });
    },
    onMobileResize: function () {
        var self = this;
        $(window).resize(function () {
            self.addAriaHidden();
        });
    },
    addAriaHidden: function () {
        if (window.matchMedia('(max-width: 767px)').matches) {
            if ($('.expander-description[aria-hidden]').length <= 0) {
                $('.expander-description').attr('aria-hidden', 'true');
            }
        }
        else {
            $('.expander-description').removeAttr('style').removeAttr('aria-hidden');
        }
    }

};
if ($('.expander-description-control').length > 0) {
    displayAccordionDescOnChannels.init();
}

function addChannelTooltipModalEventListener() {
    var $elementTarget;
    if ($('.channel-minicard-img[tooltip-title]').length > 0) {
        $('.channel-minicard-img[tooltip-title]').on('click', function () {
            var $this = $(this);
            $elementTarget = $this;
            var modalContentToReplace = $('.modal.modal-tooltip-channel');
            modalContentToReplace.find('div[tooltip-title]').html($this.attr('tooltip-title'));
            modalContentToReplace.find('p[tooltip-content]').html($this.attr('tooltip-content'));
        });

        $('.channel-minicard-img[tooltip-title]').on('keypress', function (e) {
            if (e.keyCode == 13) {
                $(this).trigger('click');
            }
        });

        $('.modal.modal-tooltip-channel').on('hidden.bs.modal', function () {
            $elementTarget.focus();
            $elementTarget = null;
        });
    }
}
addChannelTooltipModalEventListener();

// adding modal fade when the second modal show
$(document).on('click',"div[data-target='#tooltipModalMobile1'],.dual-modal [data-toggle='modal']",function() {
    if($(".modal-backdrop").length > 1) {
        $($(".modal-backdrop")[1]).css('z-index', '2002');
    }
});

//$(document).on('click', "input[data-target='#modal-compare-hardware-options']", function () {
//    resetSameHeightElements($('div.table-scrollable-wrapper'));
//}); 


$('.same-height-modal-js').on('shown.bs.modal', function () {
    $(this).find('.same-height').each(function () {
        processSameHeightElements($(this));
    });

    if (window.matchMedia('(max-width: 991px)').matches) {
        $('.scrollableContainer-js').trigger('scroll');
    }
    else {
        $(this).find('[class^=scrollableContainerShadow]').removeClass('left').removeClass('right');
    }
    
});
    
function initializeScrollableTable() {
    
    $('.scrollableContainer-js').on('scroll', function () {
        var $this = $(this);
        var scrollPos = $this.scrollLeft();
        var width = $this.width();
        var scrollWidth = $this.get(0).scrollWidth;
        var container = $this.closest('[class^=scrollableContainerShadow]');

        if (scrollPos === 0) {
            container.removeClass('left');
        } else {
            container.addClass('left');
        }

        if (scrollPos + width === scrollWidth) {
            container.removeClass('right');
        } else {
            container.addClass('right');
        }
    });

    $(window).resize(function () {
        if (window.matchMedia('(max-width: 991px)').matches) {
            $('.scrollableContainer-js').trigger('scroll');
        }
        else {
            $('.same-height-modal [class^=scrollableContainerShadow]').removeClass('left').removeClass('right');
        }
    });
}

initializeScrollableTable();

 ////parallax skrollr  animation
 var skrollrStart = function () {
     var _skrollr = skrollr.get(); // determine if skrollr is already initialised

     if (window.matchMedia('(min-width: 992px)').matches) {
         if (_skrollr) {
             skrollr.init().refresh('.parallax-screens');
         } else {
             var s = skrollr.init({
                 forceHeight: false,
                 smoothScrolling: true,
                 mobileCheck: function () {
                     return (/Android|iPhone|iPad|iPod|BlackBerry/i).test(navigator.userAgent || navigator.vendor || window.opera);
                     return false;
                 }
             });

             if (s.isMobile()) {
                 s.destroy();
             }
         }
     } else {
         if (_skrollr) {
             skrollr.init().destroy();
         }
     }
 }

 //call initially on load
 $(window).on('load', function () {
     if ($('#parallax-screen-container').hasClass('parallax-screens-js')) {
         skrollrStart();
     }
 });

 //resize event callback function
 $(window).on('resize', function () {
     if ($('#parallax-screen-container').hasClass('parallax-screens-js')) {
         skrollrStart();
     }
 });

// START scroll counter number animation
(function ($) {
	$.fn.countTo = function (options) {
		options = options || {};

		return $(this).each(function () {
			// set options for current element
			var settings = $.extend({}, $.fn.countTo.defaults, {
				from:            $(this).data('from'),
				to:              $(this).data('to'),
				speed:           $(this).data('speed'),
				refreshInterval: $(this).data('refresh-interval'),
				decimals:        $(this).data('decimals')
			}, options);

			// how many times to update the value, and how much to increment the value on each update
			var loops = Math.ceil(settings.speed / settings.refreshInterval),
				increment = (settings.to - settings.from) / loops;

			// references & variables that will change with each update
			var self = this,
				$self = $(this),
				loopCount = 0,
				value = settings.from,
				data = $self.data('countTo') || {};

			$self.data('countTo', data);

			// if an existing interval can be found, clear it first
			if (data.interval) {
				clearInterval(data.interval);
			}
			data.interval = setInterval(updateTimer, settings.refreshInterval);

			// initialize the element with the starting value
			render(value);

			function updateTimer() {
				value += increment;
				loopCount++;

				render(value);

				if (typeof(settings.onUpdate) == 'function') {
					settings.onUpdate.call(self, value);
				}

				if (loopCount >= loops) {
					// remove the interval
					$self.removeData('countTo');
					clearInterval(data.interval);
					value = settings.to;

					if (typeof(settings.onComplete) == 'function') {
						settings.onComplete.call(self, value);
					}
				}
			}

			function render(value) {
				var formattedValue = settings.formatter.call(self, value, settings);
				$self.text(formattedValue);
			}
		});
	};

	$.fn.countTo.defaults = {
		from: 0,               // the number the element should start at
		to: 0,                 // the number the element should end at
		speed: 1000,           // how long it should take to count between the target numbers
		refreshInterval: 100,  // how often the element should be updated
		decimals: 0,           // the number of decimal places to show
		formatter: formatter,  // handler for formatting the value before rendering
		onUpdate: null,        // callback method for every time the element is updated
		onComplete: null       // callback method for when the element finishes updating
	};

	function formatter(value, settings) {
		return value.toFixed(settings.decimals);
	}
}(jQuery));

function triggerFunction(section, secAnimate, classToRemove) {
    let $window = $(window);
    
    if (window.matchMedia('(min-width: 768px)').matches) {
        $(".count-animate-js > *").css("opacity", "0");
        $('.tab-circle-animation-js').css({
            "transition": "transform 1s",
            "transform": "scale(0)"
        });
    }
    
    $window.scroll(function() {
        if(secAnimate.hasClass(classToRemove)) {
            let hT = section.offset().top,
                hH = section.outerHeight(),
                wH = $window.height(),
                wS = $(this).scrollTop();

        
            if (wS >= (hT + hH - wH)){
                
                if (section.attr("id") === "home-pvr") {
                    if (window.matchMedia('(min-width: 768px)').matches) {
                        $(".hiddenClass").removeClass("hiddenClass");

                        $(".count-animate-js > *").css({
                            "opacity": "1",
                            "transition-property": "opacity",
                            "transition-duration": "1.5s",
                            "transition-delay": "1s"
                        });

                        secAnimate.countTo();
                        secAnimate.removeClass(classToRemove);
                        //scrollAnimation = false;
                        console.log("Counting animation trigger");
                    }
                } else if (section.attr("id") === "three-column-section") {
                    secAnimate.css({ "transform": "translateY(-25%)", "transition": "all 1s" });
                    secAnimate.removeClass(classToRemove);
                    console.log("Three column section trigger");
                } else if (section.attr("id") === "remote-animate-section") {
                    $(".remote-waves > div").css("transform", "scale(1)");
                    secAnimate.removeClass(classToRemove);
                    console.log("Remote animation trigger");
                } else if (section.attr("id") === "high-res-section") {
                    secAnimate.css("transform", "scale(1)");
                    secAnimate.removeClass(classToRemove);
                    console.log("Tab circle animation trigger");
                }

                
                
            } 
        }
        
    });

};

triggerFunction($("#home-pvr"), $('.count-to-js'), 'no-animation');
triggerFunction($("#three-column-section"), $(".three-col-animation-js"), 'no-animation');
triggerFunction($("#remote-animate-section"), $('.remote-waves'), 'no-animation');
triggerFunction($("#high-res-section"), $('.tab-circle-animation-js'), 'no-animation');
$(window).trigger('resize');


//function selectCustomTrigger() {
//    $(".custom-select-trigger-label").text($(".custom-selected").find("select").children("option:selected").text());
//}
//selectCustomTrigger();

//Tab recall same-height function
$('.same-height-tab-js').on("click", function () {
    $(".same-height-tabpanel-js").find('.same-height').each(function () {
        processSameHeightElements($(this));
    });
});

//recall same-height function when load is finish
$(window).on('load', function () {
    processSameHeightElements();
});

//START Slider pause and play button
(function ($) {
    var autoplaySpeedPP,
        autoplayPP

    autoplaySpeedPP = 6000;
    autoplayPP = true;

    var progressInterval,
        percentComplete,
        progressStep = 50,
        progressIndicatorLength,
        progressIndicatorUnit,
        progressIndicatorTotal,
        progressIndicatorTotalRounded

    var playButtonLabel = "Pause rotation of banners";
    var pauseButtonLabel = "Resume rotation of banners";

    var $accessibilityLabel = $(".slider-rotating-carousel-pause").find(".sr-only");
    var progressIndicator = $(".slider-rotating-carousel-pause").find(".slider-rotating-carousel-progress > circle");

    progressIndicatorLength = typeof SVGElement.prototype.getTotalLength !== "undefined" ? Math.round(progressIndicator.get(0).getTotalLength()) : 125;
    progressIndicatorUnit = progressIndicatorLength / 100;

    progressIndicator.css({ "stroke-dasharray": progressIndicatorLength });

    resumeRotation();

    $accessibilityLabel.text(playButtonLabel);

    function pauseRotation() {
        if (Math.abs(progressIndicatorTotalRounded) < 1) {
            progressIndicator.addClass("slider-rotating-carousel-progress_initial");
        }
        $(".slider-rotating-carousel-pause").attr("data-pressed", true);
        if ($(".slider-rotating-carousel-pause").length > 0) {
            $(".slick-initialized").slick("slickPause");
        }
        $accessibilityLabel.text(pauseButtonLabel);
    }

    function resumeRotation() {
        $(".slider-rotating-carousel-pause").attr("data-pressed", false);
        if ($(".slider-rotating-carousel-pause").length > 0) {
            $(".slick-initialized").slick("slickPlay");
        }
        $accessibilityLabel.text(playButtonLabel);
    }

    $(".slider-rotating-carousel-pause").on("click tap", function () {
        var isPaused = $(this).attr("data-pressed") === "true";

        if (isPaused) {
            resumeRotation();
        } else {
            pauseRotation();
        }
    });

    var startAutoplay = function () {
        percentComplete = 0;
        progressIndicatorTotal = 0;
        updateProgressIndicator();
        progressInterval = setInterval(progressIntervalHandler, progressStep);
    };

    var updateProgressIndicator = function () {
        percentComplete += progressStep / autoplaySpeedPP * 100;
        progressIndicatorTotal = percentComplete * progressIndicatorUnit * -1 + 1;
        progressIndicatorTotalRounded = Math.round(progressIndicatorTotal * 10) / 10;
        progressIndicator.css({ "stroke-dashoffset": progressIndicatorTotalRounded });
    };

    var progressIntervalHandler = function () {
        if ($(".slider-rotating-carousel-pause").attr("data-pressed") === "false") {
            updateProgressIndicator();
            if (percentComplete >= 100) {
                $(".slider-with-data-options").slick('slickNext');
                resetAutoplayProgress();
            }
        }
    };
    $(".slider-with-data-options").on("beforeChange", function (event, slick, currentSlide, nextSlide) {
        resetAutoplayProgress();
    });

    var resetAutoplayProgress = function () {
        clearInterval(progressInterval);
        startAutoplay();
    };

    if (autoplayPP) {
        startAutoplay();
    }
})(jQuery);
//END Slider pause and play button


