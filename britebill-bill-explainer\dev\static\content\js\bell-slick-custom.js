// Custom code for trends
var Trends = {
    init: function () {
        Trends.addClassToSliderIfOne();
    },

    addClassToSliderIfOne: function () {
        $('.trends').each(function () {
            if ($(this).find('.slider .slickSlide').children().length === 1) {
                $(this).find('.slider').addClass('slider-one-view');
            }
        });

    }
}


$(document).ready(function () {

    $('.generic-banner').slick({

        autoplay: true,
        autoplaySpeed:5000,
        dots: true,
        arrows: true,
        slidesToShow: 1,
        slidesToScroll: 1,
        responsive: [
  {
      breakpoint: 1023,
      settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          infinite: true,
          dots: true,
          arrows: true
      }
  },
  {
      breakpoint: 600,
      settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          infinite: true,
          dots: true,
          arrows: true
      }
  },
  {
      breakpoint: 480,
      settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          infinite: true,
          dots: true,
          arrows: true
      }
  }
        ]
    });

    $('.slider-for').slick({
        slidesToShow: 1,
        slidesToScroll: 1,
        dots: true,
        asNavFor: '.slider-nav' [
    {
        breakpoint: 1023,
        settings: {
            slidesToShow: 1,
            slidesToScroll: 1,
            infinite: true,
            arrows: true
        }
    },
    {
        breakpoint: 600,
        settings: {
            slidesToShow: 1,
            slidesToScroll: 1,
            infinite: true,
            arrows: true
        }
    },
    {
        breakpoint: 480,
        settings: {
            slidesToShow: 1,
            slidesToScroll: 1,
            infinite: true,
            arrows: true
        }
    }
        ]
    });

    $('.slider-nav').slick({
        slidesToShow: 3,
        slidesToScroll: 1,
        asNavFor: '.slider-for',
        focusOnSelect: true
    });

    $('.slider-nav .div-slider-nav').eq(0).addClass('active-div-slider-nav');

    $('.slider-for').on('beforeChange', function (event, slick, currentSlide, nextSlide) {
        var mySlideNumber = nextSlide;
        $('.slider-nav .div-slider-nav').removeClass('active-div-slider-nav');
        $('.slider-nav .div-slider-nav').eq(mySlideNumber).addClass('active-div-slider-nav');
    });

    $('.progress .progress-bar').css("width", function() {
                    return $(this).attr("aria-valuenow") + "%";
                });

    /* FibeTV and SatelliteTV 'Tabs with slider' component*/
    $('.tv-slider-for').slick({
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: true,
        asNavFor: '.tv-slider-nav' [
    {
        breakpoint: 1023,
        settings: {
            slidesToShow: 1,
            slidesToScroll: 1,
            infinite: true,
            arrows: true
        }
    },
    {
        breakpoint: 600,
        settings: {
            slidesToShow: 1,
            slidesToScroll: 1,
            infinite: true,
            arrows: true
        }
    },
    {
        breakpoint: 480,
        settings: {
            slidesToShow: 1,
            slidesToScroll: 1,
            infinite: true,
            arrows: true
        }
    }
        ]
    });

    $('.tv-slider-nav').slick({
        slidesToShow: 6,
        slidesToScroll: 1,
        asNavFor: '.tv-slider-for',
        focusOnSelect: true
    });

    $('.tv-slider-nav .tv-div-slider-nav').eq(0).addClass('active-tv-div-slider-nav');

    $('.tv-slider-for').on('beforeChange', function (event, slick, currentSlide, nextSlide) {
        var mySlideNumber = nextSlide;
        $('.tv-slider-nav .tv-div-slider-nav').removeClass('active-tv-div-slider-nav');
        $('.tv-slider-nav .tv-div-slider-nav').eq(mySlideNumber).addClass('active-tv-div-slider-nav');
    });

    $('.slick-slide').attr({
        'aria-label': 'slide element'
    })


    /*Slick Trends*/
    //check if trend component has one or many slides
    $('.section-trend-slider').each(function () {
        var jQueryEl = $(this), trendSlideCounter = jQueryEl.find('.slickSlide').length, tempjQueryEl;

        //if many slides provoke slick js
        if (trendSlideCounter > 1) {
            //if it's just two slides remove some styling
            if (trendSlideCounter == 2) {
                jQueryEl.find('.slickSlide div').first().removeClass('pad-15-left');
                jQueryEl.find('.slickSlide:eq(1) div').removeClass('pad-15-right');
                jQueryEl.removeClass('generic-banner-with-slider');
                jQueryEl.addClass('slider-two-slides');
            }

            jQueryEl.slick({
                autoplay: false,
                dots: true,
                arrows: true,
                infinite: true,
                slidesToShow: 2,
                slidesToScroll: 1,
                responsive: [
                    {
                        breakpoint: 1000,
                        settings: {
                            slidesToShow: 1,
                            slidesToScroll: 1
                        }
                    }
                ]
            });
        }
        //if its just one slide remove some styling for it to have full width
        else if (trendSlideCounter == 1) {
            //jQueryEl.find('.slickSlide div').addClass('no-pad');
            jQueryEl.removeClass('generic-banner-with-slider');
            tempjQueryEl = jQueryEl.find('> div > div');
            //tempjQueryEl.find('> div:nth-child(2)').addClass('trend-right-cont');
        }
    });

    // Custom code for trends
    Trends.init();
     /*Slick Trends*/
});

function setPrevArrowId(slickElement, newId) {
    let prevBtn = $(slickElement).find(".slick-prev");
    prevBtn.attr("id", newId);
}

function setNextArrowId(slickElement, newId) {
    let nextBtn = $(slickElement).find(".slick-next");
    nextBtn.attr("id", newId);
}

function slickCustom_correctAnchors() {
    $('.cmp-slick').on('afterChange', function (event, slick, currentSlide, nextSlide) {
        if ($(this).find('.slick-track').length <= 0) {
            return; // make sure we have initialized working slick slider
        }

        let wSlide = $(slick.$slides[currentSlide]).width() + 2;    // we also add x border
        let wList = $(slick.$list).width();

        if (wList > 425) {
            return; // only happen to small devices
        }
        wList += 10; // we add the padding

        let diff = ((wList - wSlide) / 2) - 25;
        let lastIndex = slick.$slides.length - 1;
        let strack = slick.$slideTrack
        let transform = strack.css("transform");
        let matrix = transform.split(",");

        if (currentSlide === 0) {
            setTimeout(function () {
                strack.css("transform", "translate3d(" + (parseInt(matrix[4]) - diff) + "px,0px,0px)");
            }, 1);
        } else if (currentSlide === lastIndex) {
            setTimeout(function () {
                strack.css("transform", "translate3d(" + (parseInt(matrix[4]) + diff) + "px,0px,0px)");
            }, 1);
        }
    });
}

function slickCustom_init() {   // position slick slides to left aligned
    slickCustom_correctAnchors();
    $(".cmp-slick").each(function () {
        if ($(this).find('.slick-track').length <= 0) {
            return;
        }

        $(this).slick("slickPrev");
    })
}