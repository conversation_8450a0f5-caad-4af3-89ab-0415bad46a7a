/* Required for Show/Hide Password Button widget
---------------------------------------------- */
function maskUnmaskPws(obj) {
    if (obj.next('input').attr('type') == 'password') {
        //obj.html("HIDE");
        //obj.next("input").attr('type', 'text');	
        obj.parents(".vm-panel-body").find(".maskUnMaskPwsBtn").html("HIDE");
        obj.parents(".vm-panel-body").find(".maskUnMaskPwsBtn").next("input").attr('type', 'text');
        obj.next("input").focus();
    }
    else {
        //obj.html('SHOW');
        //obj.next("input").attr('type', 'password');
        obj.parents(".vm-panel-body").find(".maskUnMaskPwsBtn").html("SHOW");
        obj.parents(".vm-panel-body").find(".maskUnMaskPwsBtn").next("input").attr('type', 'password');
        obj.next("input").focus();
    }
}

function changeModalSubTab(btnClicked, tabNo) {
    var container = $(btnClicked).parents('.modal-content');
    $(container).find('.btn-modalSubNav').removeClass('active');
    $(btnClicked).addClass('active');
    $(container).find('.modalTab').addClass('hidden');
    $(container).find(tabNo).removeClass('hidden');
    $("img.lazy").lazyload({ effect: "fadeIn" });
}

function openRecoveryTab(radioClicked, tabToOpen) {
    $(document).find('.recoveryTab').addClass('hidden');
    $(tabToOpen).removeClass('hidden');
}
function changeRadioBg(event) {
    var e = event || windows.event,
        itemClicked = $(e.target);
    itemClicked.parents('.panel.bgWhite').find('.bgRadioPanel').removeClass('active');
    itemClicked.parents('.panel.bgWhite').find('.bgRadioPanel').find('input[type = radio]').prop("checked", false);
    itemClicked.parents('.bgRadioPanel').addClass('active');
    itemClicked.parents('.bgRadioPanel').find('input[type = radio]').prop("checked", true);
}
function openTabFromSelect(selectItem) {
    var mainTabToOpen = $(selectItem).val(),
        mainNavBtnToActivate = mainTabToOpen.substring(12, 13);
    tabParent = $(selectItem).parents('.modal-content');
    $(tabParent).find('.modalTab').addClass('hidden');
    $(mainTabToOpen).removeClass('hidden');
    $(tabParent).find('.btn-modalSubNav').removeClass('active');
    $(tabParent).find('.btn-modalSubNav').eq(mainNavBtnToActivate).addClass('active');
    $("img.lazy").lazyload({ effect: "fadeIn" });
}
function openSendCodeMessage(itemClicked) {
    if ($(itemClicked).parents('.recoveryTab').length > 0) {
        $(itemClicked).parents('.recoveryTab').find('.sendCodeMessage').removeClass('hidden');
    } else {
        $(itemClicked).parents('.panel-body').find('.sendCodeMessage').removeClass('hidden');
    }

}
function openSecretAnswer(itemClicked) {
    questionID = $(itemClicked).attr('id');
    questionChoice = $(itemClicked).val();
    if (questionID == 'secretQn1') {
        //alert('questionID == secretQn1');
        if (questionChoice == 'custom') {
            $(itemClicked).parent().find('#secretAnsForm2').removeClass('hidden');
            $(itemClicked).parent().find('#secretAnsForm1').addClass('hidden');
        } else {
            $(itemClicked).parent().find('#secretAnsForm1').removeClass('hidden');
            $(itemClicked).parent().find('#secretAnsForm2').addClass('hidden');
        }
    } else if (questionID == 'secretQn2') {
        if (questionChoice == 'custom') {
            $(itemClicked).parent().find('#secretAnsForm4').removeClass('hidden');
            $(itemClicked).parent().find('#secretAnsForm3').addClass('hidden');
        } else {
            $(itemClicked).parent().find('#secretAnsForm3').removeClass('hidden');
            $(itemClicked).parent().find('#secretAnsForm4').addClass('hidden');
        }
    } else if (questionID == 'secretQn3') {
        if (questionChoice == 'custom') {
            $(itemClicked).parent().find('#secretAnsForm6').removeClass('hidden');
            $(itemClicked).parent().find('#secretAnsForm5').addClass('hidden');
        } else {
            $(itemClicked).parent().find('#secretAnsForm5').removeClass('hidden');
            $(itemClicked).parent().find('#secretAnsForm6').addClass('hidden');
        }
    }
}
$('.bgRadioPanel').click(function (event) {
    e = event || window.event;
    $(e.currentTarget).find('input').trigger('click');
});
//Vertically center modal window and make content scrollable if modal is too long
//if ($(window).width() > 991) { //for desktop only
//    function setModalMaxHeight(element) {
//        this.$element = $(element);
//        this.$content = this.$element.find('.modal-content');
//        var borderWidth = this.$content.outerHeight() - this.$content.innerHeight();
//        var dialogMargin = $(window).width() < 768 ? 20 : 60;
//        var contentHeight = $(window).height() - (dialogMargin + borderWidth);
//        var headerHeight = this.$element.find('.modal-header').outerHeight() || 0;
//        var subHeight = this.$element.find('.modal-sub-header').outerHeight() + 1 || 0;
//        var footerHeight = this.$element.find('.modal-footer').outerHeight() || 0;
//        var maxHeight = contentHeight - (headerHeight + subHeight + footerHeight);



//        this.$element
//          .find('.modal-body').css({
//              'max-height': maxHeight,
//              'overflow-y': 'auto'
//          });
//    }

//    $('.modal').on('show.bs.modal', function () {
//        $(this).show();
//        setModalMaxHeight(this);
//    });

//    $(window).resize(function () {
//        if ($('.modal.in').length != 0) {
//            setModalMaxHeight($('.modal.in'));
//        }
//    });

//};

//added to give focus to the modal close button when the modal is launched from a link. Tabbing will then start from this button onward so the first tab will always bring you to the NEXT FOCUSABLE ELEMENT INSIDE THE MODAL
function setFocusTimeout(item) {
    var focusTimeout = window.setTimeout(focusOnCloseBtn, 500);
    function focusOnCloseBtn() {
        $($(item).attr('data-target')).find('.modal-header').find('button').focus();
        clearTimeout(focusTimeout);
    }
}

//LazyLoad
$(document).ready(function () {
    if (typeof $('img.lazy').lazyload !== 'undefined') {
        $("img.lazy").lazyload({ effect: "fadeIn" });
    }
    $('.modal').on('shown.bs.modal', function () {
        setTimeout(function () {
            $("img.lazy").lazyload({ effect: "fadeIn" });
        }, 100);
    });
});

//Tabs
$("ul.tabs li").click(function () {
    $(".tab-content").hide();
    var activeTab = $(this).attr("data-rel");
    $("#" + activeTab).fadeIn();
    $("ul.tabs li").removeClass("active_tabs");
    $(this).addClass("active_tabs");
    $(".tab_heading").removeClass("d_active");
    $(".tab_heading[data-rel^='" + activeTab + "']").addClass("d_active");
});
//Mobile hamburger selectbox tab container selection
$(".custom-selection").change(function () {
    var option_activeTab = $('option:selected', this).attr('data-rel');
    $(".tab-content").hide();
    $("#" + option_activeTab).show();
    $(".tab_heading").removeClass("option_active");
    $(this).addClass("option_active");
});

//Focuses modal close button when shown
$('.modal').on('shown.bs.modal', function () {
    $('.close').focus();
});




$(document).ready(function () {

        /* prevent focus from going outside of modal. this only works with proper markup:
         * <body>
         *   <header>
         *   </header>
         *   <main>
         *   </main>
         *   <div class="modal">
         *   </div>
         *   <footer>
         *   </footer>
         * </body>
        */
        $(".modal").on("shown.bs.modal", function () {
            var modal = $(this), arrEls = modal.parent().find('> :not(div.modal)'), focusCatcherElString = '<span class="modal-focus-catcher" aria-hidden="true" tabindex="0" style="position: fixed; top: 50%; left: 50%;"></span>';

            arrEls.each(function () {
                var el, ariaHidden;

                if ("SCRIPT" === this.nodeName || "STYLE" === this.nodeName || "LINK" === this.nodeName)
                    return;

                el = $(this);
                ariaHidden = el.attr('aria-hidden');

                if (ariaHidden != null && ariaHidden != "") {
                    el.data('old-aria-hidden', ariaHidden);
                }
                el.attr('aria-hidden', true);
            });

            modal.before(focusCatcherElString);
            modal.after(focusCatcherElString);
            modal.attr('aria-hidden', false);
        });

        $(".modal").on("hidden.bs.modal", function () {
            var modal = $(this), arrEls = $(this).parent().find('> :not(div.modal)');

            arrEls.each(function () {
                var el, ariaHidden;

                if ("SCRIPT" === this.nodeName || "STYLE" === this.nodeName || "LINK" === this.nodeName)
                    return;

                el = $(this);
                ariaHidden = el.data('old-aria-hidden');

                if (ariaHidden != null && ariaHidden != "") {
                    el.attr('aria-hidden', ariaHidden);
                } else {
                    el.removeAttr('aria-hidden');
                }
            });

            $('.modal-focus-catcher').remove();
            modal.attr('aria-hidden', true);
        });


    //Enables the continue button when user scrolls to the bottom or expands the T&C box
    $('.terms-scroll').scroll(function () {
        if ($(this).scrollTop() == $(this)[0].scrollHeight - $(this).height()) {
            $('.btn.btn-primary.disabled').removeClass('disabled');
        }
    });
    $('.accordion-toggle').click(function () {
        $('.btn.btn-primary.disabled').removeClass('disabled');
    });
    $(window).on('beforeunload', function () {
        $('.terms-scroll').scrollTop(0);
    });

    // START focusable tooltips for screen reader compatibility
    $('[data-toggle="tooltip"][data-tooltipnofocus!=true]:not(.tooltip-static)').on('shown.bs.tooltip', function () {
        $(this).find('.tooltip').attr('tabindex', 0);
    });
    // END focusable tooltips for screen reader compatibility


    // START Tooltip Auto Placement
    function fnTooltipPreAdjustment() {
        $(this).find('.tooltip.top, .tooltip.bottom').css('opacity', 0);
    }

    /* modified code to handle deeper nested elements. note that this supports absolute positioned tooltips only which is the default */
    function fnTooltipAdjustment() {
        var tooltip, parent, height, scrollTop, elementOffset, distance, height, tailHeight, marginTopDiff;

        tooltip = $(this).find('.tooltip.top');
        if (tooltip.length > 0) {
            // check top overflow
            parent = tooltip.parent();
            // tooltip height doesn't include the arrow and the arrow sometimes overlap the parent, so let's compute the height manually
            height = tooltip.height() + tooltip.find('.tooltip-arrow')[0].getBoundingClientRect().bottom - tooltip[0].getBoundingClientRect().bottom;
            scrollTop = $(window).scrollTop();
            elementOffset = parent.offset().top;
            distance = (elementOffset - scrollTop);

            if (height > distance) {
                setTimeout(function () {
                    if (tooltip.hasClass('top')) {
                        marginTopDiff = parseFloat(tooltip.css('margin-top'));
                        tooltip.removeClass('top').addClass('bottom');
                        marginTopDiff -= parseFloat(tooltip.css('margin-top'));
                        tailHeight = tooltip[0].getBoundingClientRect().top - tooltip.find('.tooltip-arrow')[0].getBoundingClientRect().top;
                        tooltip.css('top', tooltip.position().top + height + parent.height() + tailHeight + marginTopDiff + "px");
                    }
                    tooltip.css('opacity', 1);
                }, 0);
            } else {
                tooltip.css('opacity', 1);
            }
            return;
        }

        tooltip = $(this).find('.tooltip.bottom');
        if (tooltip.length > 0) {
            // check bottom overflow
            parent = tooltip.parent();
            // tooltip height doesn't include the arrow and the arrow sometimes overlap the parent, so let's compute the height manually
            height = tooltip.height() + tooltip[0].getBoundingClientRect().top - tooltip.find('.tooltip-arrow')[0].getBoundingClientRect().top;
            distance = $(window).height() - parent[0].getBoundingClientRect().bottom;

            if (height > distance) {
                setTimeout(function () {
                    if (tooltip.hasClass('bottom')) {
                        marginTopDiff = parseFloat(tooltip.css('margin-top'));
                        tooltip.removeClass('bottom').addClass('top');
                        marginTopDiff -= parseFloat(tooltip.css('margin-top'));
                        tailHeight = tooltip.find('.tooltip-arrow')[0].getBoundingClientRect().bottom - tooltip[0].getBoundingClientRect().bottom;
                        tooltip.css('top', tooltip.position().top - height - parent.height() - tailHeight + marginTopDiff + "px");
                    }
                    tooltip.css('opacity', 1);
                }, 0);
            } else {
                tooltip.css('opacity', 1);
            }
            return;
        }

        tooltip.css('opacity', 1);
    }

    $('[data-toggle="tooltip"]').on('inserted.bs.tooltip', function () {
        fnTooltipPreAdjustment.apply(this);
    });

    $('[data-toggle="tooltip"]').on('shown.bs.tooltip', function () {
        fnTooltipAdjustment.apply(this);
    });

    // handle onscroll adjustment
    $(window).scroll(function () {
        var timeout;

        this.clearTimeout(timeout);
        timeout = setTimeout(function () {
            $('.tooltip.in').each(function () {
                var triggerEl = $(this).parent().first();
                fnTooltipPreAdjustment.apply(triggerEl);
                fnTooltipAdjustment.apply(triggerEl);
            });
        }, 100);
    });
    // END Tooltip Auto Placement


    // Click functions for Message Boxes
    $('#message-box-warning-toggle').click(function () {
        $('.message-box-warning-script').fadeIn(1000);
        //Get the window height and width
        var winH = $(window).height();
        var winW = $(window).width();

        //if close button is clicked
        $('.close-button').click(function (e) {
            //Cancel the link behavior
            e.preventDefault();
            $('.message-box-warning-script').fadeOut(500);
        });
        setTimeout(function () {
            $('.message-box-warning-script').fadeOut(1000);
        }, 7000);
    });
    $('#message-box-success-toggle').click(function () {
        $('.message-box-success-script').fadeIn(1000);
        //Get the window height and width
        var winH = $(window).height();
        var winW = $(window).width();

        $('.close-button').click(function (e) {
            //Cancel the link behavior
            e.preventDefault();
            $('.message-box-success-script').fadeOut(500);
        });
        setTimeout(function () {
            $('.message-box-success-script').fadeOut(1000);
        }, 7000);
    });

        //Detects mobile devices and hides the "skip-to-main-link"
        if (/Android|webOS|iPhone|iPad|BlackBerry|Windows Phone|Opera Mini|IEMobile|Mobile/i.test(navigator.userAgent))
        $('.skip-to-main-link').hide();

    window.addEventListener('keydown', focusTabControlOnce);


    $('.connector-area.keyTrigger').keyup(function (e) {
        if (e.keyCode === 13) {
            $(this).addClass('hover');
            $(this).addClass('hoverEvent')          
            $(this).attr('aria-expanded', 'true'); 
            $('.connector-lob-flyout').css('display', 'block');
        }        
        event.preventDefault();
        return false;
    });

});



// Listen to tab events to enable outlines (accessibility improvement)
function focusTabControlOnce(e) {
    if (e.keyCode === 9) { 
        document.body.classList.add('is_tabbing');
        window.removeEventListener('keydown', focusTabControlOnce);
        window.addEventListener('mousedown', focusMouseDownControlOnce);
    }
}


function focusMouseDownControlOnce() {
    document.body.classList.remove('is_tabbing');
    window.removeEventListener('mousedown', focusMouseDownControlOnce);
    window.addEventListener('keydown', focusTabControlOnce);
}

//Dropdown menus
$('.trigger-dropdown').on('mouseover focus click touch', function () {
    $('.trigger-dropdown').next().hide();
    $(this).next().show();
});
$(document).on('mouseover focus click touch', function (event) {
    if (!$(event.target).parents().addBack().is('.trigger-dropdown')) {
        $('.connector-drop-down').not(this).hide();
    }
});
$('.connector-drop-down').on('mouseover focus click touch', function (event) {
    event.stopPropagation();
});


//Prevents url redirect on first tap on mobile devices
if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
    function stopEventOnce(event) {
        event.preventDefault();
        $(this).unbind('click', stopEventOnce);
        return false;
    }
    $(".trigger-dropdown").bind('click', stopEventOnce);
    $(".trigger-dropdown").on('focusout', function () {
        $(".trigger-dropdown").bind('click', stopEventOnce);
    });
}

//Features Boxes Checkbox active state
$(document).on('click', '.checkbox-selection', function () {
    if ($(this).prop("checked")) {
        $(this).parent().parent().parent().parent().removeClass("borderGrayLight6").addClass("borderBlack border-2px");
    } else {
        $(this).parent().parent().parent().parent().removeClass("borderBlack border-2px").addClass("borderGrayLight6 ");
    }
});