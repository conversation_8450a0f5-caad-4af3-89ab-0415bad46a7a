body {background-color: #fff;}

.left-0 {left: 0;}
.top-neg-1 {top: -1px;}
.line-height-18 {line-height: 18px;}
.txtSize36 { font-size: 36px;}
.height-55 {height: 55px;}
.max-width-100-percent {max-width: 100%;}
.width-465 { width: 465px;}
.width-80 {width: 80px;}
.height-20 {
    height: 20px;
}

.graphical_ctrl input:checked ~ .radio-text{font-weight: bold; color: #003778;}
.form-control-select + span.top-3 {top: 3px;}
.font-arial {font-family: Arial, Helvetica, sans-serif;letter-spacing: 0}


.border-black-round {
    border: 1px solid #111111;
    border-radius: 10px;
    padding: 2px 5px;
}

/*Cheque line*/
.cheque-line:after {
    content: "";
    opacity: 1;
    display: block;
    background: #111111;
    width: 1px;
    height: 20px;
    position: absolute;
    left: 50%;
    top: 19px;
}

.cheque-line-left:after {
    content: "";
    opacity: 1;
    display: block;
    width: 8px;
    background: none;
    border-bottom: 1px solid #111111;
    border-right: 1px solid #111111;
    height: 32px;
    position: absolute;
    left: 7px;
    bottom: -33px;
}

.cheque-line-right:after {
    content: "";
    opacity: 1;
    display: block;
    width: 8px;
    background: none;
    border-bottom: 1px solid #111111;
    border-left: 1px solid #111111;
    height: 32px;
    position: absolute;
    left: 20px;
    bottom: -33px;
}

/* Required for tooltip font-family */ 
.tooltip-inner{
    font-family: Arial, Helvetica, sans-serif;
}

@media (min-width: 992px) {
    .height-75-md {
        height: 75px;
    }
}
@media screen and (max-width: 991.98px) {
    .pci-header-title {
        font-family: "Helvetica", Arial, sans-serif;
        font-weight: bold;
    }
}
/*Greater than Mobile*/
@media (min-width: 768px) {
    .width-90-sm {
        width: 90px;
    }
    .width-135-sm {
        width: 135px;
    }
    .width-260-sm {
        width: 260px;
    }
    .width-300-sm {
        width: 300px;
    }
    .width-380-sm {
        width: 380px;
    }

    .cheque-line:after {
        left: 50%;
        top: 19px;
    }

    .cheque-line-left:after {
        left: 13px;
        bottom: -33px;
    }

    .cheque-line-right:after {
        left: 35px;
        bottom: -33px;
    }
}
@media screen and (max-width: 767.98px) {
    .width-42-xs {width:42px;max-width:42px;}
    .modal .modal-dialog.pci-dialog {
        height: auto;
        max-height: calc(100% - 45px);
        position: relative;
        top: 50%;
        left: 0;
        right: 0;
        width: 100%;
        transform: translate(0,-50%);
    }

    .pci-modal:before {
        display: none;
    }

    .ctrl_radioBtn .ctrl_element {
        top: -1px;
        left: 0;
    }
    .form-control-select.txtSize14-xs {
        font-size: 14px;
    }
    .form-control-select.pad-r-xs-35 {
        padding-right: 35px;
    }
    .pci-header-title {
        font-weight: normal;
    }
    .inlineBlock-xs{display: inline-block}
}

