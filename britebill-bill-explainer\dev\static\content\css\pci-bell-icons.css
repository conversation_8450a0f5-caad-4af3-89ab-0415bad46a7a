@font-face {
    font-family: 'bell-icon';
    src: url(../fonts/pci-bell-icons.eot?#iefix) format("embedded-opentype"),url(../fonts/pci-bell-icons.woff) format("woff"),url(../fonts/pci-bell-icons.ttf) format("truetype"),url(../fonts/pci-bell-icons.svg) format("svg");
    font-weight: 400;
    font-style: normal
}
@font-face {
    font-family: 'pci-bell';
    src: url(../fonts/pci-bell-icons.eot?#iefix) format("embedded-opentype"),url(../fonts/pci-bell-icons.woff) format("woff"),url(../fonts/pci-bell-icons.ttf) format("truetype"),url(../fonts/pci-bell-icons.svg) format("svg");
    font-weight: 400;
    font-style: normal
}

.icon-pci{font-style:normal;speak:none;font-weight:400;font-variant:normal;text-transform:none;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}
    .icon-pci, .icon-pci:before {
        font-family: 'pci-bell';
        position: static
    }

.icon-bell-logo:before {
    content: "\e600";
}
.icon-i:before {
    content: "\e60a";
}
.icon-checkmark-circled:before {
    content: "\e921";
}
.icon-exclamation-circled:before {
    content: "\e922";
}
.icon-info:before {
    content: "\e954";
}
.icon-Close2x:before {
    content: "\e963";
}
/*Add June 9 2020*/
.icon-small_icon_select_trigger_half:before {
    content: "\e920";
}
/*Add Aug 24 2020*/
.icon-close:before {
    content: "\eaa2";
}
