@font-face {
    font-family: 'ciam-lm-icons';
    src: url(../fonts/ciam-lm-icons.eot?#iefix) format("embedded-opentype"),url(../fonts/ciam-lm-icons.woff) format("woff"),url(../fonts/ciam-lm-icons.ttf) format("truetype"),url(../fonts/ciam-lm-icons.svg) format("svg");
    font-weight: 400;
    font-style: normal
}

.icon-ciam-lm,
.icon-ciam-lm [class^="icon-"],
.icon-ciam-lm [class*=" icon-"] {
    top: 0;
}

.icon-ciam-lm {
    font-family: 'ciam-lm-icons' !important;
    font-style: normal;
    speak: none;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

    .icon-ciam-lm:before {
        font-family: 'ciam-lm-icons' !important;
        position: static;
    }

.icon-ciam-lm-logo:before {
    content: "\e920";
}

.icon-checkmark-new:before {
    content: "\e901";
}

.icon-info .path1:before {
    content: "\e928";
    color: #40b5e5;
}

.icon-info .path2:before {
    content: "\e929";
    color: #002e73;
    margin-left: -1em;
}

.icon-info .path3:before {
    content: "\e92a";
    color: #002e73;
    margin-left: -1em;
}

.icon-error-red .path1:before {
    content: "\e92f";
    color: #d42121;
}

.icon-error-red .path2:before {
    content: "\e930";
    color: #fff;
    margin-left: -1em;
}

.icon-error-red .path3:before {
    content: "\e931";
    color: #fff;
    margin-left: -1em;
}

.icon-success:before {
    content: "\e92e";
}

.icon-no-success:before {
    content: "\e92e";
    color: #e1e1e1;
}

/* NOTE: The icons below are still unused at time of writing. If you use any of them, move it above this comment. TODO: remove unused icons prior to deployment */

.icon-checkmark:before {
    content: "\e906";
}

/*Warning icon paths*/
.icon-warning-yellow .path1:before {
    content: "\e92f";
    color: #ffd669;
}

.icon-warning-yellow .path2:before {
    content: "\e930";
    color: #002e73;
    margin-left: -1em;
}

.icon-warning-yellow .path3:before {
    content: "\e931";
    color: #002e73;
    margin-left: -1em;
}

.icon-down_arrow:before {
    content: "\e90b";
}

.icon-Bottom_arrow-small:before {
    content: "\e933";
}

.icon-close-big:before {
    content: "\e900";
}
