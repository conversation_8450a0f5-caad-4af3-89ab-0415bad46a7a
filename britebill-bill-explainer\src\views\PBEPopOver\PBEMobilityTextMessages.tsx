import * as React from "react";
import { InjectedIntlProps, injectIntl } from "react-intl";
import { P<PERSON>Header, PBEFooter } from "singleban-components";
import { IPBE } from "../../models";
import { CURRENCY_OPTIONS, modalOpenedOmniture } from "../../utils/Utility";

interface Props {
    pbe: IPBE;
    isPBEModalLinkDisabled: boolean;
}

const PBEMobilityTextMessages = (props: Props & InjectedIntlProps) => {
    const { intl: { formatMessage, formatNumber }, pbe } = props;
    const title = formatMessage({ id: "PBE_MOB_TEXT_MSG_TITLE" });
    const description = formatMessage({ id: "PBE_MOB_TEXT_MSG_DESC" }, {
        totalTextMessages: pbe?.pbeDataBag?.totalTextMessages,
        totalCharge: formatNumber(pbe?.pbeDataBag?.totalCharge, CURRENCY_OPTIONS)
    });
    const imageClassName = "icon-01_chat_texting_circle";
    const PBEFooterItems = [(pbe?.pbeDataBag?.showUsageLink ? {
        ctaLink: formatMessage({ id: "PBE_MOB_USAGE_LINK" }, {
            acctNo: pbe?.pbeDataBag?.encryptedBan,
            subNo: pbe?.pbeDataBag?.subNo,
            seqNo: pbe?.pbeDataBag?.seqNo
        }),
        iconClassName: "icon-10_usage",
        titleKey: formatMessage({ id: "PBE_MOB_TEXT_MSG_FOOTER_LINK_1_DESC" }),
        ctaTitleKey: formatMessage({ id: "HP_LONG_DISTANCE_FOOTER_SUBTITLE1" }),
        isFirstRow: true,
        id: "pbe-mob-text-message-usage"
    } : null), {
        ctaLink: formatMessage({ id: "PBE_MOB_RATE_PLAN_LINK" }, {
            acctNo: pbe?.pbeDataBag?.encryptedBan,
            subNo: pbe?.pbeDataBag?.subNo,
        }),
        iconClassName: "icon-09_unlimited",
        titleKey: formatMessage({ id: "PBE_MOB_TEXT_MSG_FOOTER_LINK_2_DESC" }),
        ctaTitleKey: formatMessage({ id: "PBE_MOB_TEXT_MSG_FOOTER_LINK_2_CTA" }),
        isFirstRow: false,
        id: "pbe-hp-text-message-upgrade"
    }];
    React.useEffect(() => {
        modalOpenedOmniture(props?.pbe?.triggerPbeOmniture, title, description);
    },[title, description]);

    return (
        <>
            <PBEHeader descriptionKey={description} titleKey={title} iconClassName={imageClassName} />
            <PBEFooter footerItems={PBEFooterItems} isPBEModalLinkDisabled={props.isPBEModalLinkDisabled}/>
        </>
    );
};


export default (injectIntl(PBEMobilityTextMessages));