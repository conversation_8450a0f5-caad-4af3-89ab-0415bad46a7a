/*! jquery.priceformat@1.0.2 by <PERSON> */
!function ($, a) { function b(b, c) { this.$el = $(b), this.options = $.extend({}, d, c); var e = this.$el.val(), f = this.$el.data("decimal-separator"), g = this.$el.data("thousands-separator"), h = "+"; 0 === e.length && (e = this.options.defaultValue), e.toString().length > 0 && (h = e.toString().charAt(0), "-" === h || "+" === h ? (e = e.toString().substr(1), c.allowSign || (h = "+")) : h = "+", isNaN(e) || (this.options.defaultValue = parseInt(e))), f !== a && (this.options.decimalSeparator = f), g !== a && (this.options.thousandsSeparator = g), this.init(h) } var c = "priceformat", d = { defaultValue: 0, decimalSeparator: ".", thousandsSeparator: null, allowSign: !1, displayPlusSign: !1 }, e = "plugin_" + c + "_price", f = "plugin_" + c + "_sign"; b.prototype.init = function (a) { var b = this; this.$el.data(e, b.options.defaultValue).data(f, a).attr("unselectable", "on").css("user-select", "none").on("selectstart", !1).on("keydown", function (a) { var c = $(this), d = c.data(e), g = a.keyCode || a.which; if (9 === g || 13 === g || a.altKey || a.ctrlKey); else { if (a.preventDefault(), 8 === g || 46 === g) d = parseInt(d / 10); else if (g >= 48 && 57 >= g) d = 10 * d + (g - 48); else { if (!(g >= 96 && 105 >= g)) return void (b.options.allowSign && (107 === g || a.shiftKey && 187 === g ? c.data(f, "+") : (189 === g || 109 === g) && c.data(f, "-"))); d = 10 * d + (g - 96) } c.data(e, d) } }).on("keyup", function () { var a = $(this), c = (a.data(e) / 100).toFixed(2), d = a.data(f); if ("." !== b.options.decimalSeparator && (c = c.replace(".", b.options.decimalSeparator)), null !== b.options.thousandsSeparator) { var g = c.toString().split(b.options.decimalSeparator); g[0] = g[0].replace(/\B(?=(\d{3})+(?!\d))/g, b.options.thousandsSeparator), c = g.join(b.options.decimalSeparator) } b.options.allowSign && ("-" === d ? c = "-" + c : b.options.displayPlusSign && (c = "+" + c)), a.val(c) }).keyup() }, $.fn[c] = function (a) { return this.each(function () { $.data(this, "plugin_" + c) || $.data(this, "plugin_" + c, new b(this, a)) }) }, $(document).ready(function () { $('input[data-format="price"]').priceformat() }) }(jQuery);

$(document).ready(function () {
    $('.price-format-input').priceformat({
        defaultValue: 0,
        decimalSeparator: '.',
        thousandsSeparator: ','
    });
})