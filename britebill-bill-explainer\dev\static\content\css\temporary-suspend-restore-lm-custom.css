/* NOTE: use mobile-first media queries. place your styles under proper categories.
   Custom Styles for Temporary Suspend & Restore Service - Lucky
*/

.btn-primary.txtSize14,
.btn-primary.txtSize14:active,
.btn-primary.txtSize14:focus,
.btn-secondary.txtSize14,
.btn-secondary.txtSize14:active,
.btn-secondary.txtSize14:focus {
	font-size: 14px;
}

.theme-tsrs .btn-primary,
.theme-tsrs .btn-secondary {
	box-shadow: 1.4px 1.4px 2.8px 0.2px rgba(0, 0, 0, 0.2);
}

	.theme-tsrs .btn-primary:not(:disabled):not(.disabled):active,
	.theme-tsrs .btn-primary:not(:disabled):not(.disabled).active {
		color: #002D72;
		background-color: #41b6e6;
		border-color: #41b6e6;
	}

	.theme-tsrs .btn-secondary:not(:disabled):not(.disabled):active,
	.theme-tsrs .btn-secondary:not(:disabled):not(.disabled).active {
		color: #002D72;
		background-color: #fff;
		border-color: #41B6E6;
	}

.theme-tsrs footer .skip-to-main-link {
	display: inline-block;
}

.theme-tsrs .lm-graphical-ctrl {
	min-height: 24px;
}

.theme-tsrs .lm-radio-element {
	border: 1px solid #949596;
	box-shadow: inset 0 1px 2px 1px rgba(0,0,0,0.1);
}

.theme-tsrs .form-group-box {
	border: 1px solid #D4D4D4;
	box-shadow: 0 2px 25px -4px #E1E1E1;
}

	.theme-tsrs .form-group-box.form-error,
	.theme-tsrs .form-error .form-group-box {
		border: 1px solid #D32020;
	}

.theme-tsrs .modal.in {
	background-color: rgba(0, 0, 0, 0.6);
}

.theme-tsrs .modal-header .close {
	margin-top: -1rem;
	position: relative;
}

.theme-tsrs .close:not(:disabled):not(.disabled):hover,
.theme-tsrs .close:not(:disabled):not(.disabled):focus {
	opacity: 1;
}

.aria-combobox-select-wrapper {
	position: relative;
}

	.aria-combobox-select-wrapper .txtDarkGrey {
		color: #222222;
	}

	.aria-combobox-select-wrapper .aria-combobox-select-trigger-value {
		border: 0px !important;
		color: #666666;
		position: relative;
		left: 0;
		top: -4px;
		float: left;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

    .aria-combobox-select-wrapper .aria-combobox-select-trigger {
        border: 2px solid #949596;
        height: 40px;
        min-width: 200px;
        width: 100%;
        border-radius: 2px;
        padding: 10px;
        font-family: "GTWalsheim-med", Helvetica, Arial, sans-serif;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        position: relative;
    }

        .aria-combobox-select-wrapper .aria-combobox-select-trigger:hover {
            border: 2px solid #949596;
        }

		.aria-combobox-select-wrapper .aria-combobox-select-trigger .aria-combobox-select-trigger-arrow {
			font-size: 6px;
			float: right;
			position: relative;
			top: 3px;
			right: 4px;
			padding: 0;
			color: #002D72;
		}

    .aria-combobox-select-wrapper .aria-combobox-select-listbox {
        position: absolute;
        left: 0;
        width: 100%;
        z-index: 1;
        background-color: white;
        border: #949596 2px solid;
        border-top: 0;
    }

		.aria-combobox-select-wrapper .aria-combobox-select-listbox li {
			padding: 5px;
			padding-left: 5px;
			text-align: left;
			color: #555555;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			margin: 10px;
			cursor: pointer;
		}

			.aria-combobox-select-wrapper .aria-combobox-select-listbox li:hover,
			.aria-combobox-select-listbox li.custom-highlight {
				background-color: #e6e6e6;
				border-radius: 3px;
			}

	.aria-combobox-select-wrapper .aria-combobox-select-trigger[aria-expanded=true] {
		border-bottom: 0;
	}

		.aria-combobox-select-wrapper .aria-combobox-select-trigger[aria-expanded=true] ~ .aria-combobox-select-listbox {
			border-top: 0;
		}

/* Helpers */

.txtSize44 {
	font-size: 44px;
}

.txtSize80 {
	font-size: 80px;
}

.txtSize94 {
	font-size: 94px;
}

.line-height-12 {
	line-height: 12px;
}

.line-height-17 {
	line-height: 17px;
}

.line-height-18 {
	line-height: 18px;
}

.line-height-20 {
	line-height: 20px;
}

.line-height-22 {
	line-height: 22px;
}

.line-height-24 {
	line-height: 24px;
}

.line-height-25 {
	line-height: 25px;
}

.line-height-26 {
	line-height: 26px;
}

.line-height-28 {
	line-height: 28px;
}

.line-height-30 {
	line-height: 30px;
}

.line-height-42 {
	line-height: 42px;
}

.pad-t-58 {
	padding-top: 58px;
}

.pad-v-8 {
	padding-top: 8px;
	padding-bottom: 8px;
}

.pad-h-13 {
	padding-left: 13px;
	padding-right: 13px;
}

.pad-h-28 {
	padding-left: 28px;
	padding-right: 28px;
}

.margin-t-3 {
	margin-top: 3px;
}

.margin-l-4 {
	margin-left: 4px;
}

.margin-r-12 {
	margin-right: 12px;
}

.margin-b-neg30 {
	margin-bottom: -30px;
}

.letter-spacing-neg4 {
	letter-spacing: -0.4px;
}

.bg-tsr-light-grey {
	background-color: #F4F4F4;
}

.borderRadiusAll4 {
	border-radius: 4px;
}

.borderRadiusAll7 {
	border-radius: 7px;
}

.borderRadius-R-7 {
	border-top-right-radius: 7px;
	border-bottom-right-radius: 7px;
}

.cursor-pointer {
	cursor: pointer;
}

.no-shadow-force {
	box-shadow: none !important;
}

.position-static-force {
	position: static !important;
}

.v-center-transform {
	top: 50%;
	transform: translateY(-50%);
}

.min-h-84px {
	min-height: 84px;
}

.tsrs-img-container-fixed-width {
    width: 115px;
}

.tsrs-img-container-fixed-height {
    height: 115px;
}

.theme-tsrs .container.liquid-container {
	padding-left: 15px;
	padding-right: 15px;
	margin-left: auto;
	margin-right: auto;
	max-width: 100%;
}

.txtUnderlineOnInteraction {
	text-decoration: none !important;
}

	.txtUnderlineOnInteraction:hover,
	.txtUnderlineOnInteraction:focus {
		text-decoration: underline !important;
	}

.txtNoUnderlineOnInteraction {
	text-decoration: underline !important;
}

	.txtNoUnderlineOnInteraction:hover,
	.txtNoUnderlineOnInteraction:focus {
		text-decoration: none !important;
	}

.txtWithIconUnderlineOnInteraction,
.txtWithIconUnderlineOnInteraction:hover,
.txtWithIconUnderlineOnInteraction:focus {
	text-decoration: none !important;
}

	.txtWithIconUnderlineOnInteraction:hover .interaction-decor-target,
	.txtWithIconUnderlineOnInteraction:focus .interaction-decor-target {
		text-decoration: underline !important;
	}

@media (max-width: 767.98px) {
	.theme-tsrs .lm-footer-list-inline:not(:last-of-type) {
		margin-bottom: 30px;
	}

	.margin-t-3-xs {
		margin-top: 3px;
	}

    .margin-t-6-xs {
        margin-top: 6px;
    }

    .margin-t-neg5-xs {
        margin-top: -5px;
    }

	.margin-r-neg2-xs {
		margin-right: -2px;
	}

	.line-height-18-xs {
		line-height: 18px;
	}

	.margin-r-neg5-xs {
		margin-right: -5px;
	}

	.pad-h-0-xs {
		padding-left: 0px !important;
		padding-right: 0px !important;
	}

	.pad-h-xs-0 {
		padding-left: 0 !important;
		padding-right: 0 !important;
	}

	.pad-3-top-xs {
		padding-top: 3px;
	}

	.pad-r-xs-65 {
		padding-right: 65px;
	}

    .tsrs-message-tile-xs {
        float: none;
        clear: both;
    }

	.theme-tsrs .modal-header .close {
		margin-top: -30px;
	}

	.theme-tsrs .modal-content {
		min-height: 0;
	}

	.theme-tsrs .modal-dialog {
		top: 0;
		position: absolute;
		right: 0;
		width: 100%;
	}

	.mobile-view .two-column-arrow-divider > div:first-child {
		border-bottom: 1px solid #d4d4d4;
		border-color: #d4d4d4;
		padding: 0 0 0 0;
		display: inline;
	}

		.mobile-view .two-column-arrow-divider > div:first-child:before {
			content: "";
			width: 20px;
			height: 20px;
			position: absolute;
			bottom: -24px;
			transform: rotate(45deg) translateY(-100%);
			background-color: #F4F4F4;
			border-right: 1px solid #d4d4d4;
			border-bottom: 1px solid #d4d4d4;
			left: 43%;
		}
}

@media (min-width: 768px) {
	.theme-tsrs .container.liquid-container {
		padding-left: 24px;
		padding-right: 24px;
	}

	.pad-v-0-sm {
		padding-top: 0;
		padding-bottom: 0;
	}

	.pad-h-0-sm {
		padding-left: 0;
		padding-right: 0;
	}

    .pad-h-15-sm {
        padding-left: 15px;
        padding-right: 15px;
    }

    .pad-h-27-sm {
        padding-left: 27px;
        padding-right: 27px;
    }

	.pad-h-30-sm {
		padding-left: 30px;
		padding-right: 30px;
	}

	.pad-b-30-sm {
		padding-bottom: 30px;
	}

	.pad-27-top-sm {
		padding-top: 27px;
	}

    .pad-r-10-sm {
        padding-right: 10px;
    }

	.pad-r-35-sm {
		padding-right: 35px;
	}

    .margin-t-7-sm {
        margin-top: 7px;
    }

	.margin-b-10-sm {
		margin-bottom: 10px;
	}

	.margin-b-15-sm {
		margin-bottom: 15px;
	}

	.margin-b-30-sm {
		margin-bottom: 30px;
	}

    .margin-l-7-sm {
        margin-left: 7px;
    }

	.margin-l-35-sm {
		margin-left: 35px;
	}

	.w-455px-sm {
		width: 455px !important;
	}

	.max-width-sm-280 {
		max-width: 280px;
	}

	.no-pad-r-sm-force {
		padding-right: 0 !important;
	}

	.letter-spacing-neg048-sm {
		letter-spacing: -0.48px;
	}

	.theme-tsrs .modal-dialog {
		min-width: 751px;
	}

	.two-column-arrow-divider > div:first-child {
		border-right: 1px solid #d4d4d4;
		border-color: #d4d4d4;
		padding: 0 0 0 0;
		display: flex;
	}

		.two-column-arrow-divider > div:first-child:before {
			content: "";
			width: 20px;
			height: 20px;
			position: absolute;
			right: 4px;
			transform: rotate(45deg) translateY(-100%);
			background-color: #F4F4F4;
			top: 50%;
			border-right: 1px solid #d4d4d4;
			border-top: 1px solid #d4d4d4;
		}

	.aria-combobox-select-wrapper .aria-combobox-select-trigger .aria-combobox-select-trigger-arrow {
		right: 8px;
	}
}

@media (min-width: 992px) {
	/* containers in desktop mockups are maxed-out at 970px so we can't make them fluid. intended? */
	.theme-tsrs .container.liquid-container {
		max-width: calc(970px + 48px);
	}

	.theme-tsrs header {
		box-shadow: 2.1px 2.1px 2.8px 0.2px rgba(0,0,0,0.2);
		position: relative;
	}

	.txtSize27-md {
		font-size: 27px;
	}

	.pad-v-5-md {
		padding-top: 5px;
		padding-bottom: 5px;
	}

	.pad-t-55-md {
		padding-top: 55px;
	}

	.pad-b-60-md {
		padding-bottom: 60px;
	}

	.margin-b-neg35-md {
		margin-bottom: -35px;
	}

	.margin-b-neg40-md {
		margin-bottom: -40px;
	}
}

/* override LM.css to fix alignment issue on modal */
@media screen and (max-width:1100px) {
	.theme-tsrs .modal:before {
		height: 100%;
	}
}

/* START messages */

.tsrs-message-options .txtUnderlineOnInteraction {
	text-decoration: none !important;
}

	.tsrs-message-options .txtUnderlineOnInteraction:hover,
	.tsrs-message-options .txtUnderlineOnInteraction:focus {
		text-decoration: underline !important;
	}

.tsrs-message-options .txtNoUnderlineOnInteraction {
	text-decoration: underline !important;
}

	.tsrs-message-options .txtNoUnderlineOnInteraction:hover,
	.tsrs-message-options .txtNoUnderlineOnInteraction:focus {
		text-decoration: none !important;
	}

.tsrs-message-options .d-flex {
	display: flex;
}

.tsrs-message-options .flex-grow-1 {
	flex-grow: 1;
}

.tsrs-message-options .bgWhite {
	background-color: #fff;
}

.tsrs-message-options .borderGrey {
	border: 1px solid #d4d4d4;
}

.tsrs-message-options .borderRadiusAll7 {
	border-radius: 7px;
}

.tsrs-message-options .txtDarkGrey {
	color: #222;
}

.tsrs-message-options .txtLuckyGrey {
	color: #555;
}

.tsrs-message-options .txtLuckyRed {
	color: #D32020;
}

.tsrs-message-options .txtLuckyGreen {
	color: #00AA55;
}

.tsrs-message-options a,
.tsrs-message-options a:focus,
.tsrs-message-options a:hover {
	color: #002D72;
	text-decoration: none;
}

.tsrs-message-options .txtUnderline {
	text-decoration: underline;
}

.tsrs-message-options .txtSize14 {
	font-size: 14px;
}

.tsrs-message-options .txtSize16 {
	font-size: 16px;
}

.tsrs-message-options .txtSize18 {
	font-size: 18px;
}

.tsrs-message-options .line-height-18 {
	line-height: 18px;
}

.tsrs-message-options .line-height-22 {
	line-height: 22px;
}

.tsrs-message-options .pad-30 {
	padding: 30px;
}

.tsrs-message-options .margin-r-11 {
	margin-right: 11px;
}

.tsrs-message-options .margin-t-8 {
	margin-top: 8px;
}

.tsrs-message-options .margin-t-3 {
	margin-top: 3px;
}

.tsrs-message-options .margin-t-19 {
	margin-top: 19px;
}

.tsrs-message-options .margin-b-0 {
	margin-bottom: 0px;
}

.tsrs-message-options a {
	position: relative;
}

	.tsrs-message-options a:focus {
		outline: none !important;
		box-shadow: none !important;
	}

		.tsrs-message-options a:focus::before {
			content: '';
			height: calc(100% + 6px);
			width: calc(100% + 6px);
			position: absolute;
			top: -3px;
			left: -3px;
			display: block;
			box-shadow: 0 0 3px 1px #000, 0 0 3px 2px #000;
			z-index: 1;
			pointer-events: none;
		}

	.tsrs-message-options a.btn-primary:focus::before {
		height: calc(100% + 10px);
		width: calc(100% + 10px);
		top: -5px;
		left: -5px;
	}

@media screen and (max-width: 767.98px) {
	.tsrs-message-options .pad-h-xs-15 {
		padding-left: 15px;
		padding-right: 15px;
	}

	.tsrs-message-options .margin-l-xs-5 {
		margin-left: 5px;
	}

	.tsrs-message-options .margin-t-xs-13 {
		margin-top: 13px;
	}

	.pad-h-0-xs {
		padding-left: 0px !important;
		padding-right: 0px !important;
	}
}

/* IE11-only */
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
	/* workaround for IE11 weird artifacts from focus outline while tabbing through consecutive controls */
	.tsrs-message-options a {
		outline: 7px solid transparent !important;
	}
}

/* END messages */

/* Focus Outline */

/* Focus Outline - fallback style */
.standard-outline-fallback *:focus {
	outline: solid 2px #000;
}

/* Focus Outline - header and footer */

header.standard-outline-header a:not(.skip-to-main-link),
footer.standard-outline-footer a:not(.skip-to-main-link) {
	position: relative;
}

header.standard-outline-header a:focus,
footer.standard-outline-footer a:focus {
	outline: none !important;
	box-shadow: none !important;
}

	header.standard-outline-header a:focus::after,
	footer.standard-outline-footer a:focus::after {
		content: '';
		height: calc(100% + 6px);
		width: calc(100% + 6px);
		position: absolute;
		top: -3px;
		left: -3px;
		display: block;
		box-shadow: 0 0 3px 1px #000, 0 0 3px 2px #000;
		z-index: 1;
		pointer-events: none;
	}

/* Focus Outline - others */

.standard-outline-relative a {
	position: relative;
}

.standard-outline-relative .btn-primary:active,
.standard-outline-relative .btn-primary-inverted:active,
.standard-outline-relative .btn-primary:focus,
.standard-outline-relative .btn-primary-inverted:focus,
.standard-outline-relative .btn-secondary:active,
.standard-outline-relative .btn-secondary-inverted:active,
.standard-outline-relative .btn-secondary:focus,
.standard-outline-relative .btn-secondary-inverted:focus,
.aria-combobox-select-listbox:focus {
	box-shadow: none !important;
}

.standard-outline-relative a:focus,
.standard-outline-relative button:focus,
.standard-outline-relative input[type="submit"]:focus,
.standard-outline-self-after:focus,
.aria-combobox-select-listbox:focus {
	outline: none;
}

	.standard-outline-relative a:focus::after,
	.standard-outline-relative button:focus::after,
	.standard-outline-relative input[type="submit"]:focus::after,
	.standard-outline-self-after:focus::after,
	.aria-combobox-select-listbox:focus::after {
		content: '';
		height: calc(100% + 6px);
		width: calc(100% + 6px);
		position: absolute;
		top: -3px;
		left: -3px;
		display: block;
		box-shadow: 0 0 3px 1px #000, 0 0 3px 2px #000;
		z-index: 1;
		pointer-events: none;
	}

	.aria-combobox-select-listbox:focus::after {
		height: calc(100% + 10px);
		width: calc(100% + 10px);
		top: -5px;
		left: -5px;
	}

.standard-outline-relative .btn-primary:focus::after,
.standard-outline-relative .btn-secondary:focus::after {
	height: calc(100% + 10px);
	width: calc(100% + 10px);
	top: -5px;
	left: -5px;
}

.standard-outline-form .lm-checkbox-input:focus,
.standard-outline-form .lm-checkbox-input:focus ~ .lm-radio-element {
	outline: none !important;
	box-shadow: none !important;
}

	.standard-outline-form .lm-checkbox-input:focus ~ .lm-radio-element::after {
		content: '';
		height: calc(100% + 6px);
		width: calc(100% + 6px);
		position: absolute;
		top: -3px;
		left: -3px;
		display: block;
		box-shadow: 0 0 3px 1px #000, 0 0 3px 2px #000;
		z-index: 1;
		pointer-events: none;
	}

/* use standard-outline-direct for links that wrap. note that uncommenting the commented-out styles will reduce the outline "padding" to 1px to improve visibility of wrapped text while focused */
.standard-outline-direct a:focus {
	outline: none !important;
	box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #000, 0 0 2px 5px #000, inset 0 0 0 1em #fff !important;
}

.standard-outline-direct.bgTintSubtleGrey a:focus {
	outline: none !important;
	box-shadow: 0 0 0 3px #f7f7f7, 0 0 2px 3px #f7f7f7, 0 0 4px 5px #000, 0 0 2px 5px #000, inset 0 0 0 1em #f7f7f7 !important;
}

/* use standard-outline-direct-no-inset instead of standard-outline-direct if the element's background-color is different from its container's background-color */
.standard-outline-direct-no-inset a:focus,
.aria-combobox-select-trigger:focus {
	outline: none !important;
	box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #000, 0 0 2px 5px #000 !important;
}

.standard-outline-direct-no-inset.bgTintSubtleGrey a:focus {
	outline: none !important;
	box-shadow: 0 0 0px 3px #f7f7f7, 0 0 2px 3px #f7f7f7, 0 0 4px 5px #000, 0 0 2px 5px #000 !important;
}

.bg-tsr-light-grey .aria-combobox-select-trigger:focus {
	box-shadow: 0 0 0px 3px #f4f4f4, 0 0 2px 3px #f4f4f4, 0 0 4px 5px #000, 0 0 2px 5px #000 !important;
}

/* IE11-only */
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
	/* workaround for IE11 weird artifacts from focus outline while tabbing through consecutive controls */
	.standard-outline-direct a,
	.standard-outline-direct-no-inset a {
		outline: 7px solid transparent !important;
	}

	/* prevent IE from showing focus outline on non-focusable elements */
	div:not([tabindex]):focus, body.is_tabbing div:not([tabindex]):focus {
		outline: none !important
	}
}
