


// same click function for Bell, Virgin and Lucky templates for Pci- compliance project. 
$(document).ready(function () {
    // Radio button click event
    $('input[type="radio"][name="identification"]').click(function () {
        // Get value of clicked radio button
        inputValue = $(this).attr('value');

        $(this).prop('checked', true);
        // Get the DOM box class
        let targetBox = $('.' + inputValue);
        
    // Hide all other box
    $('.box').not(targetBox).addClass('hide');
    // Show targeted box
    $(targetBox).removeClass('hide');
    });
    
});





