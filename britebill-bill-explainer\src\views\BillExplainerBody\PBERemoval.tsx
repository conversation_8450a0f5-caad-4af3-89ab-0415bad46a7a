import * as React from "react";
import { FormattedFromToDate, FormattedFromToDateSR, getCharge, getLobData, getPreviousData } from "../../utils/Utility";
import { IBillExplainerBody } from "./IBillExplainerBody";
import { fixPBELayout, refreshStickyNav, ExtractedFormattedMessage, FormattedDollarsAmount, CustomFormattedDateWithoutYearLong } from "singleban-components";
import { FormattedMessage } from "react-intl";

const PBERemoval = (props: IBillExplainerBody) => {
    const removedItem = props.chargeItems && props.chargeItems.length ? props.chargeItems[0] : null;

    const service = props.subscriberDetails?.subscriberType;
    const { lobName } = getLobData(service);
    const refund = getCharge(props.chargeItems);

    const { currentStartDate, currentEndDate, prevStartDate, prevEndDate, serviceModifyDate, width } = getPreviousData(props.cycleStartDate, props.cycleEndDate, removedItem);

    React.useEffect(() => {
        refreshStickyNav();
    });
    React.useEffect(() => {
        fixPBELayout(true);
    });
    return (
        <div className="container pad-b-45">
            <div className="pbe-chart pbe-service-removed-chart col-sm-10 margin-auto pad-30 pad-xs-0 borderRadiusAll10 border-gray2 no-borders-xs pad-h-xs-0">
                <h3 className="subtitle-2 margin-b-15">
                    <FormattedMessage id={lobName}>
                        {(serviceName: string) => <ExtractedFormattedMessage id="PAGE_REMOVED_HEADER3" values={{ service: serviceName }} />}
                    </FormattedMessage>
                </h3>
                <p className="margin-b-10">
                    {/* Last month you removed your {service} service. Because your services are billed in advance, you had already paid for the full month. As a result: */}
                    {/* <FormattedHTMLMessage id={props.description} /> */}
                </p>
                <ul className="margin-b-30 margin-l-15 pad-l-0">
                    <li>
                        <FormattedDollarsAmount amount={refund} showHyphenIfAmountIsNull={false}>{(amount: string) => <ExtractedFormattedMessage id="PAGE_REMOVED_BODY1" values={{ amount: amount }} />}</FormattedDollarsAmount>
                    </li>
                </ul>
                <div className="pbe-wrapper row pad-h-15 same-height-wrap">
                    <div className="pbe-col col-6 pad-4-right">
                        <div className="txtCenter margin-b-15">
                            <h3 className="subtitle-2 d-flex justify-center align-items-end same-height" data-same-height-index="1">
                                <ExtractedFormattedMessage id="PREVIOUS_BILL" />
                            </h3>
                            <div className="txtSize12">
                                <span aria-hidden="true"><FormattedFromToDate startDate={prevStartDate} endDate={prevEndDate} /></span>
                                <span className="sr-only"><FormattedFromToDateSR startDate={prevStartDate} endDate={prevEndDate} /></span>
                            </div>
                        </div>
                        <div className="sr-only">
                            <div>
                                <ExtractedFormattedMessage id="PAGE_REMOVED_PREVIOUS_SERVICE" />
                                <FormattedFromToDate startDate={prevStartDate} endDate={serviceModifyDate} />
                                <ExtractedFormattedMessage id="PAGE_REMOVED_PREVIOUS_SERVICE_BODY" />
                            </div>
                            <div>
                                {/* Service removed February 10 */}
                                <ExtractedFormattedMessage id="PAGE_REMOVED_SERVICE_REMOVED" />
                                <CustomFormattedDateWithoutYearLong date={serviceModifyDate}>{(date: string) => date}</CustomFormattedDateWithoutYearLong>
                            </div>
                            <div>
                                <ExtractedFormattedMessage id="PAGE_REMOVED_PREVIOUS_SERVICE" />
                                <ExtractedFormattedMessage id="PAGE_REMOVED_REFUND" />
                                <FormattedDollarsAmount amount={refund} showHyphenIfAmountIsNull={false}>{(amount: string) => amount}</FormattedDollarsAmount>
                                {/* Old package refund -24.32 dollars */}
                            </div>
                        </div>
                        <div className="js-split-wrapper relative invisible-force" data-split-divider-width="8" data-split-divider-margin="2" data-js-split-percentage={width}>
                            <div className="upper-line-container relative bgGray19 pad-t-20" aria-hidden="true">
                                <div className="pbe-line"></div>
                            </div>
                            <div className="bars-container single-bar bgGray19" aria-hidden="true">
                                <div className="d-flex height-31">
                                    <div className="split-bar-left overflowHidden">
                                        <div className="h-100 bgBlueExtraLight border-new-grey"></div>
                                    </div>
                                    <div className="split-bar-divider"></div>
                                    <div className="split-bar-right overflowHidden">
                                        <div className="h-100 bgBlueExtraDark2"></div>
                                    </div>
                                </div>
                                <div className="split-divider-indicator pbe-prev-package-change-blue-line-inner">
                                    <div className="h-100"></div>
                                </div>
                            </div>
                            <div className="labels-container" aria-hidden="true">
                                <div className="label-group-top relative bgGray19">
                                    <div className="split-divider-label d-flex flex-row">
                                        <div className="w-100">
                                            <div className="surtitle-black">
                                                {/* Service removed */}
                                                <ExtractedFormattedMessage id="PAGE_REMOVED_SERVICE_REMOVED" />
                                            </div>
                                            <div>
                                                {/* February 10 */}
                                                <CustomFormattedDateWithoutYearLong date={serviceModifyDate}>{(date: string) => date}</CustomFormattedDateWithoutYearLong>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="label-group-bottom relative">
                                    <div className="split-origin-label d-flex">
                                        <div className="label-indicator-line-origin">
                                        </div>
                                        <div>
                                            <div className="surtitle-black">
                                                {/* Old package */}
                                                <ExtractedFormattedMessage id="PAGE_REMOVED_PREVIOUS_SERVICE" />
                                            </div>
                                            <div className="small-text margin-3-top">
                                                <ExtractedFormattedMessage id="PAGE_REMOVED_PREVIOUS_SERVICE_BODY" />
                                                {/* (paid one month in advance) */}
                                            </div>
                                        </div>
                                    </div>
                                    <div className="split-right-label d-flex flex-column">
                                        <div className="label-indicator-line-bottom">
                                            <div></div>
                                        </div>
                                        <div className="w-100">
                                            <div className="surtitle-black">
                                                {/* Refund */}
                                                <ExtractedFormattedMessage id="PAGE_REMOVED_REFUND" />
                                            </div>
                                            <div className="small-text margin-3-top">
                                                {/* - <span>$</span>24.32 */}
                                                <FormattedDollarsAmount amount={refund} showHyphenIfAmountIsNull={false}>{(amount: string) => amount}</FormattedDollarsAmount>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="pbe-col col-6 pad-4-left">
                        <div className="txtCenter margin-b-15">
                            <h3 className="subtitle-2 d-flex justify-center align-items-end same-height" data-same-height-index="1">
                                <ExtractedFormattedMessage id="CURRENT_BILL" />
                            </h3>
                            <div className="txtSize12">
                                {/* <span aria-hidden="true">Feb 20 - Mar 19</span> <span className="sr-only">February 20 to March 19</span> */}
                                <span aria-hidden="true"><FormattedFromToDate startDate={currentStartDate} endDate={currentEndDate} /></span>
                                <span className="sr-only"><FormattedFromToDateSR startDate={currentStartDate} endDate={currentEndDate} /></span>
                            </div>
                        </div>
                        <div className="sr-only">
                            <div>
                                <ExtractedFormattedMessage id="PAGE_REMOVED_AFTER_REMOVAL" />
                                <FormattedFromToDateSR startDate={currentStartDate} endDate={currentEndDate} />
                                {/* No service {Month day} to {Month day} */}
                            </div>
                        </div>
                        <div className="js-split-wrapper relative invisible-force" data-split-divider-width="8" data-split-divider-margin="2" data-js-split-percentage="null">
                            <div className="upper-line-container relative bgGray19 pad-t-20" aria-hidden="true">
                                <div className="pbe-line"></div>
                            </div>
                            <div className="bars-container single-bar bgGray19" aria-hidden="true">
                                <div className="d-flex height-31">
                                    <div className="split-bar-left overflowHidden">
                                        <div className="h-100 bg-stripe-blue"></div>
                                    </div>
                                    <div className="split-bar-divider"></div>
                                    <div className="split-bar-right overflowHidden">
                                        <div className="h-100 bg-stripe-blue"></div>
                                    </div>
                                </div>
                            </div>
                            <div className="labels-container" aria-hidden="true">
                                <div className="label-group-top relative bgGray19">
                                </div>
                                <div className="label-group-bottom relative">
                                    <div className="split-right-label d-flex flex-column">
                                        <div className="label-indicator-line-bottom">
                                            <div></div>
                                        </div>
                                        <div className="w-100">
                                            <div className="surtitle-black">
                                                {/* No service */}
                                                <ExtractedFormattedMessage id="PAGE_REMOVED_AFTER_REMOVAL" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PBERemoval;