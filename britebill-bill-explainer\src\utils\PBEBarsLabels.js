export function initializePBESplitChart(){
    pbeSplitChartRemade();
    setUpPbeChartTextSpacingA11yMutationObserver();
}


export function pbeSplitChartRemade() {
    var $jsCharts = $('.pbe-js-chart'),
        $pbeChart = $('.pbe-chart');

    $jsCharts.find('.pbe-chart-container > div').remove();
    $jsCharts.find('.pbe-labels-container > div').remove();
    $jsCharts.removeData("split-chart");

    $.each($pbeChart, function () {
        $(this).find('.pbe-js-chart').first().find('.pbe-chart-container').addClass('pbe-chart-prev');
    });

    $.each($jsCharts, function () {

        var $jsChart = $(this),
            $pbeChartContainer = $jsChart.find('.pbe-chart-container'),
            splitChart = $jsChart.data('split-chart'),
            startDate = new Date(splitChart.startDate),
            endDate = new Date(splitChart.endDate),
            transactions = splitChart.transactions,
            transcount = (transactions !== undefined) ? transactions.length : 0,
            transMaxHeight,
            chargeItems = splitChart.chargeItems,
            itemcount = (chargeItems !== undefined) ? chargeItems.length - 1 : 0,
            oneDay = 24 * 60 * 60 * 1000,
            totalDays = Math.round(Math.abs((startDate - endDate) / oneDay)) + 1,
            useLegends = splitChart.UseLegendsForDiagram,
            isUpperExist = chargeItems.find(function(x){return x.position === 'upper'}) !== undefined,
            isBelowExist = chargeItems.find(function(x){return x.position === 'below'}) !== undefined || chargeItems.find(function(x){return x.position === 'CURRENT'}) !== undefined,
            isSingleBar = isBelowExist && !isUpperExist;

        //START: PBE labels
        var $pbeLabelsContainer = $jsChart.find('.pbe-labels-container'),
            $srLabelsContainer;

        $jsChart.append('<div class="pbe-sr-labels sr-only"></div>');
        $srLabelsContainer = $jsChart.find('.pbe-sr-labels');

        if (isUpperExist) {
            $pbeChartContainer.append("<div class='pbe-chart-bars-upper'></div>");
            $pbeLabelsContainer.append("<div class='pbe-label-top-group relative bgGray19'></div>");
        }

        if (isBelowExist) {
            $pbeChartContainer.append("<div class='pbe-chart-bars-below'></div>");
            $pbeLabelsContainer.append("<div class='pbe-label-bottom-group relative bgGray19'></div>");
        }

        if (transactions !== undefined && transactions.length > 0) {
            $pbeChartContainer.append("<div class='pbe-chart-transacs'></div>");
        }

        if (isSingleBar) {
            $pbeChartContainer.addClass('single-bar');
        }

        //START chargeItems/bars
        $.each(chargeItems, function () {
            var $this = this,
                $barContainer = ($this.position === "upper") ? $pbeChartContainer.find('.pbe-chart-bars-upper') : $pbeChartContainer.find('.pbe-chart-bars-below'),
                $bar,
                barDays,
                barLabel = $this.label,
                barSubLabel = $this.sub_label,
                itemLegend = $this.itemLegend,
                posDays,
                pOfBars,
                pPosDays,
                $legend = ($this.legend !== undefined) ? "<div class='pbe-legend'>" + $this.legend + "</div>" : '',
                legendOnly = '',
                horizontalPosition = 'middle-position';

            $this.startDate = new Date($this.startDate);
            $this.endDate = new Date($this.endDate);

            //width computation
            barDays = Math.round(Math.abs(($this.startDate - $this.endDate) / oneDay)) + 1;
            posDays = Math.round(Math.abs(($this.startDate - startDate) / oneDay));

            pOfBars = (barDays * 100) / totalDays;
            pPosDays = (posDays * 100) / totalDays;

            if (useLegends) {
                legendOnly = ($this.alwaysLabel !== true) ? 'legend-only' : '';
            }

            $bar = $("<div>", {
                "class": "pbe-chart-bar txtCenter txtWhite h-100 " + itemLegend + " " + legendOnly,
                "style": "width:" + pOfBars + "%; left:" + pPosDays + "%",
                "data-bar-id": itemcount
            });
            $bar.append($legend);
            $barContainer.append($bar);

            // START: PBE labels for bars
            var $labelContainer = ($this.position === "upper") ? $pbeLabelsContainer.find('.pbe-label-top-group') : $pbeLabelsContainer.find('.pbe-label-bottom-group'),
                labelWidth = pOfBars + "%", //width used is the same with its corresponding bar
                labelPosition = pPosDays + "%", //position used is the same with its corresponding bar
                $labelWrapper,
                $labelLineIndicator = "<div class='pbe-line-indicator'><div></div></div>",
                $labelDescription = "<div class='surtitle-black'>" + barLabel + "</div>",
                $labelDescriptionSub = (barSubLabel !== undefined) ? "<div class='small-text margin-t-4'>" + barSubLabel + "</div>" : '',
                $labelStructure,
                lastLabel,
                $srDateRange = legendOnly === "" ? "<div class='sr-only'> From " + $this.startDate.toDateString() + " - " + $this.endDate.toDateString() + "</div>" : '';

            lastLabel = (itemcount == 0) ? 'label-wrapper-last' : '';

            // position line indicator depending on the label position
            if (($this.alwaysLabel !== false && $this.alwaysLabel !== "" && $this.alwaysLabel !== null && $this.alwaysLabel !== undefined)) {
                if (barLabel !== '' && barLabel !== null && barLabel !== undefined && $this.alwaysLabel !== false) {
                    if ($this.position === "upper") {
                        $labelStructure = "<div class='pbe-label'>" + $labelDescription + $labelDescriptionSub + $srDateRange + "</div>" + $labelLineIndicator;
                    } else {
                        $labelStructure = $labelLineIndicator + "<div class='pbe-label'>" + $labelDescription + $labelDescriptionSub + $srDateRange + "</div>";
                    }
                }
            }

            if ($this.startDate.getTime() <= startDate.getTime() && $this.endDate.getTime() < endDate.getTime()) {
                horizontalPosition = 'left-position';
            } else if ($this.startDate.getTime() > startDate.getTime() && $this.endDate.getTime() >= endDate.getTime()) {
                horizontalPosition = 'right-position';
            }

            if (legendOnly !== '') {
                $labelWrapper = $("<div>", {
                    "class": "pbe-label-wrapper " + itemLegend + " " + legendOnly + " " + lastLabel,
                    "style": "width:" + labelWidth + "; left:" + labelPosition,
                    "data-label-bar-id": itemcount
                });
                $labelWrapper.append($labelStructure);
            } else {
                $labelWrapper = $("<div>", {
                    "class": "pbe-label-wrapper " + legendOnly + " " + lastLabel,
                    "style": "left:" + labelPosition,
                    "data-label-bar-id": itemcount
                });
                $labelWrapper.append($labelStructure);
            }

            $labelWrapper.addClass(horizontalPosition);
            $labelWrapper.data('horizontal-position', horizontalPosition);
            $labelContainer.append($labelWrapper);


            // START: label position add class
            var label_location = ($this.label_position !== undefined && $this.label_position !== '') ? $this.label_position : $this.position;

            //if (label_location !== "") {
            $labelWrapper = $labelContainer.find('.pbe-label-wrapper[data-label-bar-id="' + itemcount + '"]');
            $labelWrapper.addClass(label_location);
            //}
            // END: label position add class

            if (((useLegends && $this.alwaysLabel) || !useLegends) && label_location === "upper") {
                var $line = $labelWrapper.find('.pbe-line-indicator > div'),
                    lineTopCount;

                if (isSingleBar) {
                    lineTopCount = $jsChart.find('.pbe-label-bottom-group .pbe-label-wrapper.upper:not(.legend-only)').length + transactions.length;
                } else if (label_location === "upper") {
                    lineTopCount = $jsChart.find('.pbe-label-top-group .pbe-label-wrapper:not(.legend-only), .pbe-chart-transac').length + transactions.length;
                }

                $line.css('min-height', $line.height() * lineTopCount);
            }

            itemcount--;

            // adding sr-only for chart
            $.each(transactions, function () {
                var chargeItemsStartDate = new Date($this.startDate),
                    $transaction = this,
                    transactionDate = new Date($transaction.date);

                if (chargeItemsStartDate.toDateString() === transactionDate.toDateString()) {
                    $srLabelsContainer.append('<div>' + $transaction.label + " " + $transaction.date_label + '</div>');
                    return;
                }
            });

            var srLegend = "";
            if (legendOnly !== '') {
                srLegend = $this.legend + " ";
            }
            
            barSubLabel = (barSubLabel === undefined) ? "" : " " + barSubLabel;
            $srLabelsContainer.append('<div>' + srLegend + barLabel + barSubLabel + $srDateRange + '</div>');
        });

        //START transactions/divider
        $.each(transactions, function (index) {
            var $this = this,
                $transContainer = $pbeChartContainer.find('.pbe-chart-transacs'),
                $trans,
                transLabel = $this.label,
                transDate,
                transDateLabel = $this.date_label,
                posDays,
                pPosDays,
                barHeight = $pbeChartContainer.find('.pbe-chart-bars-below').height(),
                heightOffset = 14,
                height = barHeight + heightOffset,
                marginBottom = parseInt($pbeChartContainer.find('.pbe-chart-bars-upper').css('marginBottom')),
                bottom = 0,
                isTop = false,
                isBot = false,
                grey = '',
                isPrev = $pbeChartContainer.hasClass('pbe-chart-prev');

            $this.date = new Date($this.date);
            transDate = new Date($this.date);
            transDate = transDate.toDateString();

            isTop = chargeItems.find(function(x) {return x.startDate.getTime() == $this.date.getTime() && x.position === "upper"}) !== undefined ? true : isTop;
            isBot = chargeItems.find(function(x) {return x.startDate.getTime() == $this.date.getTime() && (x.position === "below" || x.position === "current")}) !== undefined ? true : isBot;

            //height computation
            height = (isTop && isBot) ? (transcount > 1) ? (height + heightOffset + marginBottom) * transcount : (height + heightOffset + marginBottom + (barHeight / 2)) : height * transcount;
            bottom = (isTop && !isBot) ? barHeight + marginBottom : bottom;

            posDays = Math.round(Math.abs(($this.date - startDate) / oneDay));
            pPosDays = (posDays * 100) / totalDays;

            $trans = $("<div>", { "class": "pbe-chart-transac" + grey, "style": "height: " + height + "px;left:" + pPosDays + "%;bottom: " + bottom + "px;", "data-transId": "trans-id-" + transcount });
            $trans.data('is-both', isTop && isBot);
            $transContainer.append($trans);

            // START: PBE labels for transaction (divider)
            var $labelContainer = ($pbeLabelsContainer.find('.pbe-label-top-group').length > 0) ? $pbeLabelsContainer.find('.pbe-label-top-group') : $pbeLabelsContainer.find('.pbe-label-bottom-group'),
                $transDivider,
                $transDescription = "<div class='surtitle-black'>" + transLabel + "</div>",
                $transDate = "<div class='small-text margin-t-4'>" + transDateLabel + "</div>";


            $transDivider = $("<div>", {
                "class": "pbe-label-divider middle-position",
                "style": "left:" + pPosDays + "%",
                "data-label-transid": "trans-id-" + transcount
            });
            $transDivider.append($("<div class='pbe-label'>" + $transDescription + $transDate + "</div>"));
            $transDivider.data('horizontal-position', 'middle-position');
            $labelContainer.append($transDivider);
            transcount--;
        });

        getPbePosition($jsChart);
        computeLineIndicatorHeight($jsChart);
        iterateDividerLabels($jsChart);
    });
}

//reposition the bar location depending on the top or bottom label height
function getPbePosition($chart) {
    var $barUpper = $chart.find('.pbe-chart-bars-upper'),
        $barBelow = $chart.find('.pbe-chart-bars-below'),
        $pbeContainer = $chart.find('.pbe-chart-container'),
        isSingleBar = $pbeContainer.hasClass('single-bar'),
        isPrev = $pbeContainer.hasClass('pbe-chart-prev'),
        barUpperMarginBottom;

    //START compute transaction max height and set it as min-height for pbe label group
    var $labelContainer = ($barUpper.length > 0) ? $chart.find('.pbe-label-top-group') : $chart.find('.pbe-label-bottom-group'),
        barHeight = ($barUpper.length > 0) ? $barUpper.height() : $barBelow.height(),
        $trans = $pbeContainer.find('.pbe-chart-transacs .pbe-chart-transac'),
        //splitChart = $labelBotContainer.closest('.pbe-js-chart').data('split-chart'),
        //chargeItems = splitChart.chargeItems,
        transMaxHeight = 0;

    $.each($trans, function () {
        var $this = $(this),
            height = $this.height(),
            label = $labelContainer.find('.pbe-label-divider[data-label-transid="' + $this.data('transid') + '"]').height(),
            spaceBetween = 15,
            isBoth = $this.data('is-both'),
            currentTransHeight = (isBoth) ? (height - barHeight) + label + spaceBetween : height + label + spaceBetween;

        transMaxHeight = (transMaxHeight > currentTransHeight) ? transMaxHeight : currentTransHeight;
    });

    if ($barUpper.length > 0 || (isSingleBar && $barBelow.length > 0)) {
        let labelGroupSelector = $barUpper.length > 0 ? '.pbe-label-top-group .pbe-label-wrapper' : '.pbe-label-bottom-group .pbe-label-wrapper.upper';
        $.each($chart.find(labelGroupSelector), function () {
            var $this = $(this),
                height = $this.height(),
                currentLabelHeight = height + 15;

            transMaxHeight = (transMaxHeight > currentLabelHeight) ? transMaxHeight : currentLabelHeight;
        });
    }

    $labelContainer.css('min-height', transMaxHeight);
    //END compute transaction max height and set it as min-height for pbe label group

    //bar location - top
    if ($barUpper.length > 0) {
        positionPbeBar($barUpper, $chart.find('.pbe-label-top-group'), 'upper');
        barUpperMarginBottom = parseInt($chart.find('.pbe-chart-bars-upper').css('margin-bottom'));
    }

    //bar location - bottom
    if ($barBelow.length > 0) {
        var $siblingChartContainer,
            $labelBotContainer = $chart.find('.pbe-label-bottom-group'),
            extraMinHeight = 0;


        if (isSingleBar && !isPrev) {
            let $singleChartContainer = $barBelow.closest('.pbe-chart-container');
            $siblingChartContainer = $chart.closest('.pbe-chart').find('.pbe-chart-container.pbe-chart-prev');
            let $legendOnly = $labelBotContainer.find('.pbe-label-wrapper.legend-only');

            if ($siblingChartContainer.length > 0) {

                if ($siblingChartContainer.hasClass('single-bar')) {
                    $labelBotContainer.css({
                        'min-height': $siblingChartContainer.siblings('.pbe-labels-container').find('.pbe-label-bottom-group').height()
                    });
                } else {
                    if ($labelContainer.find('.pbe-label-wrapper').length === 1) {
                        if ($labelContainer.find('.pbe-label-wrapper').hasClass('below')) {
                            extraMinHeight = $labelContainer.find('.pbe-label-wrapper').height();
                        }
                    }

                    $labelBotContainer.css({
                        'min-height': $siblingChartContainer.siblings('.pbe-labels-container').find('.pbe-label-top-group').height() + extraMinHeight
                    });
                }

            }

            $barBelow.css('top', $siblingChartContainer.find('.pbe-chart-bars-below').css('top'));
        }

        $labelBotContainer.find('.pbe-label-wrapper.below').css("display", "none");
        if (isSingleBar) {
            $labelBotContainer.find('.pbe-label-wrapper.upper').css("display", "none");
        }

        positionPbeBar($barBelow, $labelBotContainer, 'below', barUpperMarginBottom);
    }

    function positionPbeBar($bar, $labelWrapper, position, margin_bottom) {
        var barHeight = $bar.height(),
            labelHeight = $labelWrapper.height(),
            $pbeLabelsContainer = $labelWrapper.closest('.pbe-labels-container'),
            $graphContainer = $pbeLabelsContainer.siblings('.pbe-chart-container'),
            isSingleBar = $graphContainer.hasClass('single-bar'),
            isPrev = $graphContainer.hasClass('pbe-chart-prev'),
            //topPosition = (isSingleBar && isPrev) ? labelHeight - (barHeight * 2) : labelHeight - barHeight;
            topPosition = labelHeight - barHeight;

        // additional computation for bottom label container if the upper bar is present
        if (margin_bottom !== undefined) {
            let inlineStyle = {
                'min-height': barHeight + margin_bottom,
                'top': margin_bottom + "px"
            };
            $labelWrapper.css(inlineStyle);

            topPosition = $labelWrapper.offset().top - $bar.offset().top; // computation for pbe-chart-bars-below
            let barBottomPosition = topPosition;
        }

        if (topPosition <= 0 || labelHeight === undefined) { return; }
        if (isSingleBar && !isPrev) { return; }
        $bar.css('top', topPosition);
    }

    //transaction - recompute its location
    if ($chart.find('.pbe-chart-transacs').length > 0) {
        var $trans = $chart.find('.pbe-chart-transacs'),
            transOffset = $trans.offset().top,
            barBelowOffset = $barBelow.offset().top,
            barBelowHeight = $barBelow.height();

        $trans.css('bottom', (transOffset - barBelowOffset - barBelowHeight));
    }

    $chart.find('.pbe-label-wrapper').css("display", "");

    //transaction label mapping
    if ($chart.find('.pbe-chart-transac').length > 0) {
        var $chargeLabel,
            totalChargeLabelHeight = 0;

        $.each($chart.find('.pbe-chart-transac'), function () {
            var $this = $(this),
                dataTransID = $this.data('transid'),
                $divider = $chart.find('.pbe-label-divider[data-label-transid=' + dataTransID + ']'),
                divStyle;

            //NOTE: if charge label is present need to compute total height of all charge labels and deduct to transaction label offsets
            if ($chargeLabel === undefined) {
                $chargeLabel = $divider.prevAll('.pbe-label-wrapper:not(.legend-only)');
                $.each($chargeLabel, function () {
                    totalChargeLabelHeight += $(this).height();
                });
            }

            var $parent = $this.parent(),
                parentRightOffset = $parent.offset().left + $parent.width(),
                dividerLabelWidth = $divider.find('.surtitle-black').width(),
                dividerRightOffset = $divider.offset().left + dividerLabelWidth,
                isTextAlignRight = (parentRightOffset <= dividerRightOffset) ? true : false,
                divChangeLeftVal;

            $divider.data({
                'parent-offset-right': parentRightOffset,
                'parent-offset-left': $parent.offset().left,
                'trans-offset-left': $divider.offset().left,
                'trans-offset-right': dividerRightOffset
            });

            // condition for checking if text or label on transaction will be aligned on right side when the available space is not enough
            if (isTextAlignRight) {
                if (divChangeLeftVal === undefined) {
                    divChangeLeftVal = parseFloat($divider.css('left')) - dividerLabelWidth;
                    divChangeLeftVal = ((divChangeLeftVal / $parent.width()) * 100) + "%";
                }

                divStyle = {
                    "position": "absolute",
                    "top": mapLabelTransaction($this, $divider, totalChargeLabelHeight) - 10,
                    "left": divChangeLeftVal,
                    "text-align": "right"
                }
            } else {
                divStyle = {
                    "position": "absolute",
                    "top": mapLabelTransaction($this, $divider, totalChargeLabelHeight) - 10
                }
            }

            $divider.css(divStyle);
        });
    }
}

//compute the line indicators height per label (legends excluded)
function computeLineIndicatorHeight($chart) {
    var $lineIndicator = $chart.find('.pbe-line-indicator:visible'),
        lineTopCount = $chart.find('.pbe-label-top-group .pbe-label-wrapper:not(.legend-only), .pbe-chart-transac').length,
        lineBotCount = $chart.find('.pbe-label-bottom-group .pbe-label-wrapper:not(.legend-only)').length,
        prevMaxHeight;

    $.each($lineIndicator, function () {
        var $this = $(this),
            $line = $this.find('div'),
            $chartContainer = $this.closest('.pbe-js-chart'),
            $barChart,
            $labelWrapper = $this.closest('.pbe-label-wrapper'),
            parentLabelWrapperPosition,
            posVal = 0,
            isSingleBar = $chart.find('.pbe-chart-container').hasClass('single-bar');

        if ($line.closest('.pbe-label-top-group').length > 0) {
            lineTopCount--;
        } else {
            if (!isSingleBar || $labelWrapper.hasClass('below')) {
                $line.css('min-height', $line.height() * lineBotCount);
            }
            lineBotCount--;
        }

        $barChart = $chartContainer.find('.pbe-chart-container');
        parentLabelWrapperPosition = ($this.closest('.pbe-label-top-group').length > 0) ? "top" : "bottom";
        let labelOffsetTop = $labelWrapper.offset().top;
        isSingleBar = ($this.closest('.pbe-labels-container').prev('.pbe-chart-container.single-bar').length > 0) ? true : false;

        posVal = positionLineIndicator($labelWrapper, $barChart, parentLabelWrapperPosition, isSingleBar);
        $labelWrapper.css('top', posVal);

        if (parentLabelWrapperPosition === 'bottom' && isSingleBar === false) {
            prevMaxHeight = getMaxHeight(prevMaxHeight, $labelWrapper.height(), ($barChart.height() / 2));
            $this.closest('.pbe-label-bottom-group').css('min-height', prevMaxHeight);
        }

        checkOverlapText($labelWrapper);
    });
}

function iterateDividerLabels($chart) {
    var labelsTop = $chart.find('.pbe-label-top-group .pbe-label-divider'),
        $labelsTopFirst = labelsTop.first(),
        $chargeLabelRight = $chart.find('.pbe-label-top-group .pbe-label-wrapper.right-position:not(.legend-only)'),
        firstOffsetRight,
        labelsBot = $chart.find('.pbe-label-bottom-group .pbe-label-divider'),
        isOverlapping = false,
        isCropped = false,
        isOverlapChargeLabel = false;

    if (labelsTop.length > 0) {
        if ($labelsTopFirst.length > 0) {
            firstOffsetRight = $labelsTopFirst.offset().left + $labelsTopFirst.width();
            let $parent = $labelsTopFirst.closest('.pbe-label-top-group');
        }

        $.each(labelsTop, function () {
            var $this = $(this),
                $trans,
                $transLabel,
                transHeight,
                $transPrev,
                $transLabelPrev;

            checkOverlapText($this);
            $this.addClass('text-wrap');

            // START: additional checking, if transaction labels overlaps with charge item label on the right side change inline style
            if ($chargeLabelRight.length > 0) {
                isOverlapChargeLabel = ($chargeLabelRight.find('.pbe-line-indicator').offset().left <= ($this.offset().left + $this.width())) ? true : false;
                if (isOverlapChargeLabel) {
                    $this.css({
                        'left': parseInt($this.css('left')) - $this.width(),
                        'text-align': "right"
                    })
                }
            }
            // END: additional checking, if transaction labels overlaps with charge item label on the right side change inline style

            if (!$labelsTopFirst.is($this)) {
                isOverlapping = (firstOffsetRight >= $this.offset().left) ? true : false;
                isCropped = ($this.data('parent-offset-right') <= $this.data('trans-offset-right')) ? true : false;


                if (isOverlapping && isCropped) {
                    $trans = $chart.find('.pbe-chart-transac[data-transid="' + $this.data('label-transid') + '"]');
                    $transPrev = $trans.prev();
                    transHeight = $trans.height();

                    if ($transPrev.length > 0) {
                        // change height of previous transaction blue bar
                        $transPrev.css({
                            'height': ($transPrev.height() - transHeight)
                        });

                        // adjust transaction label based on the reduce height of the previous transaction bar height
                        $transLabel = $chart.find('.pbe-label-divider[data-label-transid="' + $trans.data('transid') + '"]');
                        $transLabelPrev = $chart.find('.pbe-label-divider[data-label-transid="' + $transPrev.data('transid') + '"]');
                        $transLabelPrev.css('top', parseFloat($transLabelPrev.css('top')) + transHeight);

                        if (($transLabel.data('trans-offset-left') <= $transLabelPrev.data('trans-offset-left'))) {
                            if ($transLabelPrev.data('location-changed') === undefined) {
                                $transLabelPrev.css({
                                    'left': parseInt($transLabelPrev.css('left')) - $transLabelPrev.width(),
                                    'text-align': "right"
                                });

                                checkTransactionLeftEdgeCropText($transLabelPrev);
                            }
                        } else if (($transLabel.offset().left <= $transLabelPrev.data('trans-offset-left')) && ($transPrev.offset().left <= $transLabelPrev.offset().left)) {
                            if ($transLabelPrev.data('location-changed') === undefined) {
                                $transLabelPrev.css({
                                    'left': parseInt($transLabelPrev.css('left')) - $transLabelPrev.width(),
                                    'text-align': "right"
                                });

                                checkTransactionLeftEdgeCropText($transLabelPrev);
                            }
                        }
                    }

                    //transaction label that overlaps or being cropped
                    $trans.css(
                        { "height": (transHeight * 2) }
                    );

                    $this.css({
                        "top": parseFloat($this.css('top')) - transHeight
                    });
                }
            } else if ($this.data('parent-offset-left') >= $this.offset().left) {
                checkTransactionLeftEdgeCropText($this);
            }
        });

        function checkTransactionLeftEdgeCropText($el) {
            var excess = ($el.data('parent-offset-left') - $el.offset().left);

            if (excess > 0) {
                $el.css({
                    'background-color': "#F4F4F4",
                    'width': ($el.width() - excess),
                    'left': parseInt($el.css('left')) + excess + 2
                }).addClass('is-cropped');

            }

            $el.data('location-changed', true);
        }
    }

    if (labelsBot.length > 0) {
        $.each(labelsBot, function () {
            checkOverlapText($(this));
        });
    }
}

function checkOverlapText($curLabel) {
    var $parent = $curLabel.parent(),
        bartype = ($curLabel.hasClass('pbe-label-wrapper')) ? 'data-bar-id' : 'data-transid',
        curDataLabelID = ($curLabel.hasClass('pbe-label-wrapper')) ? $curLabel.data('label-bar-id') : $curLabel.data('label-transid'),
        curHeight = $curLabel.height(),
        curOffsetLeft = $curLabel.offset().left,
        curOffsetTop = $curLabel.offset().top;

    // START: get current label width
    getLabelWidth($curLabel);
    let curOffsetRight = curOffsetLeft + $curLabel.data('label-width');
    let curOffsetBottom = curOffsetTop + curHeight;
    // END: get current label width

    // START: check if label horizontal (left / middle / right) position
    var $correspondingBar = $curLabel.closest('.pbe-js-chart').find("[" + bartype + "='" + curDataLabelID + "']"),
        $parentBar = $correspondingBar.parent(),
        isSingleBar = $parentBar.parent().hasClass('single-bar'),
        labelparentcount,
        curclassname = $curLabel.data('horizontal-position');

    // get label parent count and add to condition in determining the right position when top bar has only one charge item
    labelparentcount = $curLabel.closest('.pbe-labels-container').children().length;

    // END: check if label horizontal (left / middle / right) position

    $.each($parent.children(':not(.legend-only)'), function (e) {
        var $this = $(this),
            dataLabelID = ($this.hasClass('pbe-label-wrapper')) ? $this.data('label-bar-id') : $this.data('label-transid'),
            labelheight = $this.height(),
            labelwidth,
            offsetLeft = $this.offset().left,
            offsetTop = $this.offset().top,
            offsetRight,
            offsetBottom,
            leftPos = 0,
            rightPos = "auto",
            rightLabelSpacing = 30,
            parentOffsetRight = $parent.offset().left + $parent.width(),
            barWidth = $correspondingBar.width() - ($curLabel.closest('.pbe-js-chart').find('.pbe-chart-transac').width() / 2);

        if (barWidth <= 30) {
            if (curclassname === "left-position") {
                $curLabel.find('.pbe-line-indicator, .pbe-label').css('margin-left', (barWidth / 2) - 1);
            } else if (curclassname === "right-position") {
                rightLabelSpacing = (barWidth / 2) + parseInt($curLabel.find('.pbe-line-indicator, .pbe-label').css('margin-left'));
            }
        }

        if (curDataLabelID !== dataLabelID) {
            getLabelWidth($this);
            labelwidth = $this.data('label-width');
            offsetRight = offsetLeft + labelwidth;
            offsetBottom = offsetTop + labelheight;

            if (curOffsetLeft <= offsetRight && curOffsetTop <= offsetBottom) {
                if (isSingleBar) { return; } // container for bars - if single bar does not continue right position computation

                leftPos = (parseFloat($curLabel.css('left')) + labelwidth);
            }
        } else {
            // check if current label is cropped
            var actualLabelOffsetRight = parseInt($curLabel.find('.pbe-label').offset().left + $curLabel.data('label-width'));

            if (parentOffsetRight < actualLabelOffsetRight && curclassname === "middle-position" && isSingleBar) {
                rightPos = $curLabel.data('label-width');
                $curLabel.find('.pbe-label').css({ "position": "relative", "right": rightPos }); // for label to appear as text-align: right
                $curLabel.css("text-align", "right");
            } else if (parentOffsetRight < actualLabelOffsetRight || (curclassname === "right-position" && !isSingleBar)) {

                leftPos = ($parent.width() - rightLabelSpacing);
                rightPos = $curLabel.data('label-width');
                $curLabel.find('.pbe-label').css({ "position": "relative", "right": rightPos }); // for label to appear as text-align: right
                $curLabel.css({ 'left': leftPos });
            } else if (curclassname === "middle-position" && !isSingleBar) {
                leftPos = $correspondingBar.width() / 2;

                $curLabel.css('left', parseInt($curLabel.css('left')) + leftPos);
            }
        }
    });
}

function mapLabelTransaction($transaction, $transLabel, totalChargeLabelHeight) {
    var transactionOffset,
        transLabelOffset,
        transLabelHeight;
    if (totalChargeLabelHeight === undefined) { totalChargeLabelHeight = 0; }
    if ($transaction.length === 0 || $transLabel.length === 0) { return; }
    transactionOffset = $transaction.offset().top;
    transLabelOffset = $transLabel.offset().top;
    transLabelHeight = $transLabel.height();

    return transactionOffset - transLabelOffset - transLabelHeight + totalChargeLabelHeight;
}

function getMaxHeight(prev, current, elemheight) {
    var height;
    if (elemheight === undefined) { elemheight = 0 }
    if (prev === undefined) { height = current }
    if (prev >= current) { height = prev }
    if (prev < current) { height = current }
    return height + elemheight; //additional 5px as allowance to prevent cropping of text or label
}

function getLabelWidth($el) {
    var dataLabelWidth = $el.data('label-width');

    if (dataLabelWidth === undefined) {
        dataLabelWidth = 0
    }

    $.each($el.find('.pbe-label').children(), function () {
        var $this = $(this);
        $this.addClass('d-inline-flex');
        if (dataLabelWidth <= $this.width()) {
            dataLabelWidth = $this.width();
        }
        $this.removeClass('d-inline-flex');
    })
    $el.data('label-width', dataLabelWidth);
}

function positionLineIndicator($labelWrapper, $chartContainer, labelPosition, isSingleBar) {
    var $bar,
        $dataBarId,
        $parentLabelWrapper,
        barOffsetBottom,
        labelOffsetBottom,
        positionValue = 0,
        isLabelLocationPresent = (!$labelWrapper.hasClass('upper') && !$labelWrapper.hasClass('below')) ? false : true,
        labelLocation,
        labelDataBarId = $labelWrapper.data('label-bar-id'),
        currentContainerHeight,
        finalContainerHeight,
        isPrev = $chartContainer.hasClass('pbe-chart-prev');

    $bar = (labelPosition === 'top') ? $chartContainer.find('.pbe-chart-bars-upper') : $chartContainer.find('.pbe-chart-bars-below');
    barOffsetBottom = $bar.offset().top + $bar.height();
    labelOffsetBottom = $labelWrapper.offset().top + $labelWrapper.height();
    isSingleBar = (isSingleBar === undefined) ? false : isSingleBar;
    isPrev = $chartContainer.hasClass('pbe-chart-prev');
    labelLocation = ($labelWrapper.hasClass('upper')) ? "top" : "bottom";

    if ((isLabelLocationPresent && labelPosition !== labelLocation) || (isLabelLocationPresent === true && isSingleBar === true)) {
        if (labelPosition === "bottom" && labelLocation === "top") {
            positionValue = barOffsetBottom - labelOffsetBottom - ($bar.height() / 2);
        } else if ((labelPosition === "top" && labelLocation === "bottom") || (isSingleBar === true && labelPosition === labelLocation)) {
            positionValue = $bar.offset().top - $labelWrapper.offset().top + ($bar.height() / 2);
        }

        $parentLabelWrapper = $labelWrapper.parent();
        $dataBarId = $bar.find('.pbe-chart-bar[data-bar-id="' + labelDataBarId + '"]');
        let $pbeLabelContainer = $parentLabelWrapper.closest('.pbe-labels-container');

        // adjust height of label container when condition below is met
        if (isSingleBar === true && labelPosition === labelLocation) {

            if ($labelWrapper.find('.pbe-label').width() < $dataBarId.width()) {
                $labelWrapper.css('width', "calc(100% - " + parseFloat($labelWrapper.css('left')) + "px)");
            } else if (!$dataBarId.is(':first-child') && !$dataBarId.is(':last-child')) {
                var labelActualWidth = 0,
                    getLabelActualWidth = function ($el) {
                        $el.addClass('d-inline-flex');
                        $.each($el.children(), function () {
                            labelActualWidth = (labelActualWidth > $(this).width()) ? labelActualWidth : $(this).width();
                        });
                        $el.removeClass('d-inline-flex').css('width', labelActualWidth);
                    }
                getLabelActualWidth($labelWrapper.find('.pbe-label'));

                $labelWrapper.css({
                    "width": $dataBarId.css('width'),
                    'left': parseFloat($dataBarId.css('left')) + ($dataBarId.width() / 2) - parseInt($labelWrapper.find('.pbe-line-indicator').css('margin-left'))
                });
            } else {
                $labelWrapper.css({
                    'align-items': "flex-start",
                    "width": "auto"
                });
            }

            let pbeLabelContainerOffsetBottom = $pbeLabelContainer.offset().top + $pbeLabelContainer.outerHeight();

            let $upperLabelWrapper = $parentLabelWrapper.find('.pbe-label-wrapper.upper');


            if ($parentLabelWrapper.data('original-height') === undefined) {
                $parentLabelWrapper.data('original-height', parseInt($parentLabelWrapper.css('min-height')));
            }

            currentContainerHeight = $labelWrapper.height();

            if ($parentLabelWrapper.data('label-max-height') === undefined) {
                $parentLabelWrapper.data('label-max-height', currentContainerHeight);
                finalContainerHeight = currentContainerHeight;
            } else {
                let dataMaxHeight = $parentLabelWrapper.data('label-max-height');
                finalContainerHeight = (dataMaxHeight > currentContainerHeight ? dataMaxHeight : currentContainerHeight);
                $parentLabelWrapper.data('label-max-height', finalContainerHeight);
            }

            let $whiteBg = $pbeLabelContainer.find('.pbe-labels-white-bg').length > 0 ? $pbeLabelContainer.find('.pbe-labels-white-bg') : $("<div>", { "class": "bgWhite pbe-labels-white-bg" });

            if (isPrev) {
                $parentLabelWrapper.css('height', finalContainerHeight + $parentLabelWrapper.data('original-height'));
            }

            $whiteBg.css({
                "height": (($pbeLabelContainer.offset().top + $pbeLabelContainer.height()) - barOffsetBottom)
            });

            $pbeLabelContainer.prepend($whiteBg);
        } else if (isSingleBar === true && labelPosition !== labelLocation) {
            $labelWrapper.css({
                "width": $dataBarId.css('width'),
                "left": $dataBarId.css('left')
            });
        }

        return positionValue;
    }

    // retaining original computation for label positioning before introducing additional parameter to base labels upper/below location
    if (!isLabelLocationPresent || (labelPosition === labelLocation)) {
        if (labelPosition === 'bottom' && isSingleBar === false) {
            positionValue = barOffsetBottom - $labelWrapper.offset().top - ($bar.height() / 2);
        } else {
            positionValue = barOffsetBottom - labelOffsetBottom - ($bar.height() / 2);
        }

        return positionValue;
    }
}

function setUpPbeChartTextSpacingA11yMutationObserver() {
    var pbeChartTextSpacingA11yMutationObserver,
        target = document.getElementById('maincontent') == null ? 'body' : 'maincontent';

    pbeChartTextSpacingA11yMutationObserver = setUpMutationObserver(
        target,
        { subtree: true, attributeFilter: ["style"] },
        function (mutationList, observer) {
            var len = mutationList.length,
                mutation,
                i;

            for (i = 0; i < len; i++) {
                mutation = mutationList[i];

                if (mutation.type === "attributes" && mutation.attributeName === "style" && i + 1 == len) {
                    var textSpacingCountFlag = 0

                    for (var j = 0; j < mutation.target.style.length; j++) {
                        var style = mutation.target.style[j];
                        if (style === "line-height" || style === "letter-spacing" || style === "word-spacing") {
                            textSpacingCountFlag++;
                        }
                    }

                    if (textSpacingCountFlag === 3) {
                        var $jsCharts = $('.pbe-js-chart');

                        $jsCharts.find('.pbe-chart-container > div').remove();
                        $jsCharts.find('.pbe-labels-container > div').remove();
                        pbeSplitChartRemade();
                    }
                }
            }
        }
    );
}

function setUpMutationObserver(idElem, config, callback) {
    var targetNode, observer;

    if (idElem == null || config == null || typeof callback !== 'function') {
        return;
    }

    if (idElem.tagName !== undefined) {
        targetNode = idElem;
    } else if (typeof idElem === 'string') {
        targetNode = document.getElementById(idElem);

        if (targetNode == null) {
            return;
        }
    }

    observer = new MutationObserver(callback);
    observer.observe(targetNode, config);

    return observer;
}