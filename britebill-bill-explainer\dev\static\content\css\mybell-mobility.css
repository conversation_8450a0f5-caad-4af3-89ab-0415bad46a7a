﻿/***styles copied from shop.css not present in bell.css starts***/
main a,
.txtUnderlineOnHover {
    align-items: center;
    color: #00549a;
    display: inline-flex;
}
    /* for anchor tags inside main, focus only the element with the anchor-text class. to apply the same behavior to elements outside main, use the txtUnderlineOnHover class. */
    main a:not(.txtUnderline),
    main a:not(.txtUnderline):hover,
    main a:not(.txtUnderline):focus,
    main a:not(.txtUnderline) .anchor-icon,
    .txtUnderlineOnHover:not(.txtUnderline),
    .txtUnderlineOnHover:not(.txtUnderline):hover,
    .txtUnderlineOnHover:not(.txtUnderline):focus,
    .txtUnderlineOnHover:not(.txtUnderline) .anchor-icon {
        text-decoration: none;
    }

    main a:hover > .anchor-text,
    main a:focus > .anchor-text,
    .txtUnderlineOnHover:hover > .anchor-text,
    .txtUnderlineOnHover:focus > .anchor-text {
        text-decoration: underline;
    }

h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0;
}

p {
    margin-bottom: 15px;
}

.big-title,
.title,
.small-title,
.subtitle-2,
.subtitle-2-reg,
.surtitle,
.surtitle-black {
    max-width: 100%;
    width: auto;
    color: #111;
}
.big-title,
.title,
.small-title {
    font-family: "bellslim_font_black", Helvetica, Arial, sans-serif;
    font-weight: 400;
    letter-spacing: -.4px;
    line-height: 28px;
}

.small-title {
    font-size: 22px;
    line-height: 24px;
}

.form-control-select + span {
    height: auto;
    padding: 13px 10px;
    right: 0;
}
ul.no-list-style {
    list-style: none;
    margin: 0;
    padding: 0;
}

/***styles copied from shop.css not present in bell.css ends***/
.col35-minus-20px {
    width: calc(35% - 20px);
}

.col35-plus-175px {
    width: calc(35% + 175px);
}

.width-175 {
    width: 175px;
}

.error label {
    color: #BD2025;
}

.show-msg .form-group {
    display: flex;
}

    .show-msg .form-group.error label {
        margin-top: -21px;
    }

@media(min-width: 320px) and (max-width: 767.98px) {   
    .show-msg .form-group {
        display: block;
    }

    .modal-notes-footer {
        padding: 30px 15px;
        margin: 0 -15px;
    }
    .small-title {
        font-family: "bellslim_font_black", Helvetica, Arial, sans-serif;
        font-size: 24px;
        line-height: 26px;
    }
    .brfpad .container.container-pad-h-0-xs {
        padding-left: 0;
        padding-right: 0;
    }

    .sms-verification-cta-sm {
        width: calc(100% + 30px);
        position: relative;
        left: -15px;
    }

    .review-tooltip {
        display: none;
    }

    .rate-plan-box .ctrl_radioBtn .ctrl_element {
        top: 3px;
        left: 0;
    }
    .rate-plans-col-mar .rate-plan-box {
        margin: 0;
    }
    .lower-payment-boxes{
        margin: 0;
    }
}

/************************************/
/*** device details modal begins ***/
.device-details-modal .modal-body {
    padding-left: 0;
    padding-right: 0;
    margin-bottom: 0;
}

.device-details-modal .device-image {
    max-width: 116px;
    height: auto;
}

.device-details-modal .device-details-links ul li a {
    justify-content: space-between;
    padding: 25px 30px;
    width: 100%;
    border-top: 1px solid #e1e1e1;
}

.device-details-modal .device-details-links ul li a .small-title {
    font-size: 22px;
    color: #111;
}

.device-details-modal .device-details-links ul li a:hover {
    text-decoration: none;
}

.device-details-modal .device-details-links ul li a span.icon3 {
    font-size: 16px;
    color: #00549a;
}

.device-details-modal .device-details-links ul li a svg {
    padding-right: 3px;
}

.device-details-modal .device-details-links ul li a .bazaarvoice-stars {
    padding-left: 25px;
}

.device-features-modal .small-title {
    font-size: 22px;
}

.device-features-modal .features .title {
    font-size: 24px;
    font-weight: bold;
    display: block;
}

.device-features-modal .features .text {
    display: block;
}

.device-features-modal .features-row {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
    padding-top: 20px;
    align-items: center;
}

.device-features-modal .features-column-icon {
    width: 60px;
    flex: 0;
}

.device-features-modal .features-column-text {
    flex: 1;
}

.device-features-modal .features-column-text,
.device-features-modal .features-column-icon {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
}

.device-features-modal .features-column-text span {
    display: block;
}

.device-features-modal .features-title {
    font-size: 18px;
    font-weight: bold;
    color: #111;
    padding-bottom: 5px;
}

.device-memory-modal .memory-capacity-title {
    font-size: 20px;
    font-weight: bold;
    color: #111;
}

.device-memory-modal .memory-capacity span:after {
    background-color: #ccc;
    content: "";
    display: inline-block;
    height: 18px;
    margin: 0 15px;
    vertical-align: middle;
    width: 1px;
}

.device-memory-modal .memory-capacity span:last-child:after {
    margin: 0;
    width: 0px;
}

.device-memory-modal .device-memory-subsection {
    padding-top: 35px;
}

.device-memory-modal .device-memory-subsection h4 {
    font-size: 14px;
    font-weight: bold;
    color: #111;
}

.device-memory-modal .device-memory-subsection p {
    margin-top: 10px;
    margin-bottom: 0;
}

.device-memory-modal .data-usage-examples {
    padding-top: 30px;
}

.device-memory-modal .data-usage-examples,
.device-memory-modal .data-usage-examples .category,
.device-memory-modal .data-usage-examples .category-data,
.technical-specifications-modal .technical-specifications {
display: -webkit-flex;
flex-wrap: wrap;
-webkit-flex-wrap: wrap
}

.device-memory-modal .data-usage-examples .category:nth-child(odd) {
    flex: 1 1 45%;
}

.device-memory-modal .data-usage-examples .category:nth-child(even) {
    flex: 1 1 55%;
}

.device-memory-modal .data-usage-examples .category:last-child .category-data {
    padding-bottom: 0;
}

.device-memory-modal .data-usage-examples .category:last-child,
.device-memory-modal .data-usage-examples .category:nth-last-child(2):nth-child(odd) {
    flex: 0 0 45%;
}

.device-memory-modal .data-usage-examples .category-title,
.technical-specifications-modal .heading {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
    align-items: center;
}

.technical-specifications-modal .heading {
    border-bottom: 1px solid #d4d4d4;
    padding-bottom: 5px;
}

.device-memory-modal .data-usage-examples .category-data,
.technical-specifications-modal .technical-specifications {
    padding-top: 15px;
    padding-bottom: 25px;
}

.device-memory-modal .data-usage-examples .category-data-name,
.technical-specifications-modal .technical-specifications .technical-specifications-name {
    color: #111;
    font-weight: bold;
}

.technical-specifications-modal .technical-specifications .technical-specifications-data {
    text-align: right;
}

.device-memory-modal .data-usage-examples .category-data-name,
.device-memory-modal .data-usage-examples .category-data-usage {
    padding-bottom: 10px;
}

.technical-specifications-modal .technical-specifications .technical-specifications-name,
.technical-specifications-modal .technical-specifications .technical-specifications-data {
    padding-bottom: 15px;
}

.device-memory-modal .data-usage-examples .category-title .category-title-name {
    font-size: 18px;
    color: #111;
    margin-left: 20px;
}

.technical-specifications-modal .heading .heading-name {
    font-size: 18px;
    color: #111;
    margin-left: 10px;
}

.technical-specifications-modal .technical-specifications-category:nth-child(odd) .technical-specifications > div:nth-child(odd) {
    flex: 0 0 50%;
}

.technical-specifications-modal .technical-specifications-category:nth-child(odd) .technical-specifications > div:nth-child(even) {
    flex: 0 0 50%;
}

.technical-specifications-modal .technical-specifications-category:nth-child(even) .technical-specifications > div:nth-child(odd) {
    flex: 0 0 50%;
}

.technical-specifications-modal .technical-specifications-category:nth-child(even) .technical-specifications > div:nth-child(even) {
    flex: 0 0 50%;
}

.technical-specifications-modal .technical-specifications-wrapper {
    padding-top: 25px;
}

.more-about-device-modal .iphone11-img {
    display: block;
    max-width: 100%;
    height: auto;
}

.more-about-device-modal .modal-body {
    margin-bottom: 0;
}

.more-about-device-modal .more-about-device-title {
    padding-top: 35px;
    padding-bottom: 0;
}

.reviews-modal .reviews-title {
    padding-top: 35px;
    padding-bottom: 10px;
}

@media (min-width: 767.99px) {
    .device-memory-modal .data-usage-examples .category:nth-child(odd) .category-data > div:nth-child(odd) {
        flex: 1 1 45%;
    }

    .device-memory-modal .data-usage-examples .category:nth-child(odd) .category-data > div:nth-child(even) {
        flex: 1 1 55%;
    }

    .device-memory-modal .data-usage-examples .category:nth-child(even) .category-data > div:nth-child(odd) {
        flex: 1 1 35%;
    }

    .device-memory-modal .data-usage-examples .category:nth-child(even) .category-data > div:nth-child(even) {
        flex: 1 1 65%;
    }
}

@media (min-width: 320px) and (max-width: 767.98px) {
    .device-details-modal .device-details-links ul li a {
        padding-left: 15px;
        padding-right: 15px;
    }

    .device-details-modal .device-image {
        max-width: 105px;
    }

    .device-details-modal .device-details-links ul li a .bazaarvoice-stars {
        display: block;
        padding-left: 0;
        padding-top: 10px;
    }

    .device-features-modal .features-row,
    .device-features-modal .features-column-icon,
    .device-features-modal .features-column-text {
        display: block;
        width: 100%;
    }

    .device-features-modal .features-column-text {
        padding: 10px 0;
    }

    .device-features-modal .features-section {
        padding-left: 0;
        padding-right: 0;
    }

    .device-memory-modal .data-usage-examples,
    .device-memory-modal .data-usage-examples .category {
        display: block;
        width: 100%;
    }

    .device-memory-modal .data-usage-examples .category .category-data > div:nth-child(odd) {
        flex: 1 1 40%;
    }

    .device-memory-modal .data-usage-examples .category .category-data > div:nth-child(even) {
        flex: 1 1 60%
    }

    .more-about-device-modal .more-about-device-title {
        padding-bottom: 10px;
    }
}
/*** device details modal ends ***/
/**********************************/
/*** compare modal begins ***/
.compare-modal .compare-table-wrap .compare-modal-row-block {
    display: -webkit-flex;
    flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
}

.compare-modal .compare-table-wrap .current-solution,
.compare-modal .compare-table-wrap .new-solution {
    border: 1px solid #d4d4d4;
}

.compare-modal .compare-table-wrap .current-solution,
.compare-modal .compare-table-wrap .current-solution .compare-modal-heading{
    border-top-left-radius: 10px;
}

.compare-modal .compare-table-wrap .new-solution .compare-modal-heading,
.compare-modal .compare-table-wrap .new-solution {
    border-top-right-radius: 10px;
}

.compare-modal .compare-table-wrap .current-solution .compare-modal-total-monthly,
.compare-modal .compare-table-wrap .current-solution {
    border-bottom-left-radius: 10px;
}

.compare-modal .compare-table-wrap .new-solution .compare-modal-total-monthly,
.compare-modal .compare-table-wrap .new-solution {
    border-bottom-right-radius: 10px;
}

.compare-modal .compare-table {
    border-radius: 10px;
}

.compare-modal .compare-table-wrap .compare-modal-heading {
    text-align: center;
    vertical-align: middle;
    border-bottom: 1px solid #d4d4d4;
}

.compare-modal .compare-modal-title {
    font-family: "bellslim_mediumregular", Helvetica, Arial, sans-serif;
    font-size: 20px;
}

.compare-modal .compare-table-wrap .compare-modal-heading span {
    display: block;
}

.compare-modal .compare-table-wrap .compare-modal-heading .heading {
    font-size: 18px;
}

.compare-modal .compare-table-wrap .compare-modal-heading .subHeading{
    color: #c0ccdd;
}

.compare-modal .compare-table-wrap .compare-modal-phone .compare-modal-row-block > div:nth-child(odd) {
    flex: 0 0 25%;
}

.compare-modal .compare-table-wrap .compare-modal-phone .compare-modal-row-block > div:nth-child(even) {
    flex: 0 0 75%;
}

.compare-modal .compare-table-wrap .compare-modal-device .compare-modal-row-block > div:nth-child(odd),
.compare-modal .compare-table-wrap .compare-modal-rateplan .compare-modal-row-block > div:nth-child(odd),
.compare-modal .compare-table-wrap .compare-modal-addons .compare-modal-row-block > div:nth-child(odd) {
    flex: 0 0 75%;
}

.compare-modal .compare-table-wrap .compare-modal-device .compare-modal-row-block > div:nth-child(even),
.compare-modal .compare-table-wrap .compare-modal-rateplan .compare-modal-row-block > div:nth-child(even),
.compare-modal .compare-table-wrap .compare-modal-addons .compare-modal-row-block > div:nth-child(even) {
    flex: 0 0 25%;
}

.compare-modal .compare-table-wrap .compare-modal-total-monthly .compare-modal-row-block > div {
    flex: 0 0 50%;
}

.compare-modal img {
    display: block;
    max-width: 100%;
    height: auto;
}

.compare-modal hr {
    margin: 0;
}

.compare-modal .compare-table-wrap .compare-modal-total-monthly {
    vertical-align: middle;
    border-top: 1px solid #d4d4d4;
}

.compare-modal .compare-table-wrap .compare-modal-total-monthly .price{
    letter-spacing: 1px;
}

.compare-modal .compare-table-wrap .compare-modal-total-monthly .price sup {
    top: -8px;
    font-size: 14px;
}

.compare-modal .compare-table-wrap .compare-modal-total-monthly .txtWhite .price,
.compare-modal .compare-table-wrap .compare-modal-total-monthly .txtWhite .price sup {
    color: #fff;
}

.compare-modal .modal-body {
    margin-bottom: 0;
}

.compare-modal .modal-footer {
    margin-bottom: 0;
    border-top: 1px solid #d4d4d4;
    display: block;
}

.compare-modal .promoWrap span:not(.promo) {
    display: block;
}

.compare-modal .promoWrap .promo {
    width: auto;
    margin: auto;
    padding: 0 7px;
    font-size: 10px;
    text-transform: uppercase;
    display: inline-block;
    margin-top: 5px;
}

.compare-modal .current-solution .promoWrap .promo {
    color: #fff;
    background: #555;
}

.compare-modal .new-solution .promoWrap .promo {
    color: #fff;
    background: #00549A;
}

@media (min-width: 767.99px) {
    .compare-modal .compare-table-wrap .current-solution {
        border-right: none;
    }
}

@media (min-width: 320px) and (max-width: 767.98px) {
    .compare-modal .compare-table-wrap .new-solution {
        margin-top: 30px
    }
    .compare-modal .compare-table-wrap .current-solution, .compare-modal .compare-table-wrap .current-solution .compare-modal-heading {
        border-top-right-radius: 10px;
    }
    .compare-modal .compare-table-wrap .current-solution .compare-modal-total-monthly, .compare-modal .compare-table-wrap .current-solution {
        border-bottom-right-radius: 10px;
    }
    .compare-modal .compare-table-wrap .new-solution .compare-modal-heading, .compare-modal .compare-table-wrap .new-solution {
        border-top-left-radius: 10px;
    }
    .compare-modal .compare-table-wrap .new-solution .compare-modal-total-monthly, .compare-modal .compare-table-wrap .new-solution {
        border-bottom-left-radius: 10px;
    }
}

/*** compare modal ends ***/
/*********************************/

/*** payments modal starts ***/
/**********************************/

.lower-payment-links li {
    padding: 24px 0;
    border-top: 1px solid #D4D4D4;
}

.margin-left-auto {
    margin-left: auto;
}

.inline-100 {
    display: inline-block;
    width: 100%;
}

.lower-payment-links .icon-chevron {
    float: right;
}

.modal-notes-footer {
    border-radius: 0 0 9px 9px;
    padding: 30px;
    background-color: #f0f0f0;
    margin: 0 -30px;
}

.accordionContainer > ul > li:not(:last-child) {
    border-bottom: 1px solid #d4d4d4;
}

.accordionContainer li a[aria-expanded=true] span:not(.icon) {
    font-size: 14px;
    color: #111;
}

/*** payments modal ends ***/
/**********************************/

/**CSS for modal dialogues starts(copied from shop.css not present in bell.css)**/
.modal-dialog,
.modal-content {
    border-radius: 10px 10px 0 0;
}

.modal-content {
    height: 100%;
}

.modal-header {
    align-items: center;
    background-color: #f0f0f0;
    border-radius: 10px 10px 0 0;
    height: 74px;
    padding: 15px 20px 15px 15px;
}

.modal-dialog .modal-content .modal-header .close {
    margin: 0 -15px 0 15px;
    padding: 15px;
    border: 0;
    font-size: inherit;
}

.modal-body {
    margin-bottom: 30px;
    margin-top: 30px;
    padding: 0 15px;
}

    .modal-body.no-margin-bottom {
        margin-bottom: 0;
    }

.modal-open .modal-body.scrollAdjust {
    padding-bottom: 0;
}

.modal.modal-status .modal-dialog {
    border-radius: 10px;
    box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
    max-width: calc(100% - 30px);
    position: relative;
}

.modal.modal-status .modal-content {
    border-radius: 10px;
}

.modal.modal-status .modal-body {
    padding: 0 30px;
}

.modal-status-icon {
    font-size: 30px;
}
/* webkit only hack: custom scroll (.scrollAdjust) only works on webkit browsers so only adjust the margin and padding for those browsers */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .modal-open .modal-body.scrollAdjust:not(*:root) {
        margin-right: 10px;
        padding-right: 20px;
    }
}

@media(min-width:768px) {
    .modal-dialog,
    .modal-content {
        border-radius: 10px;
    }

    .modal .modal-dialog {
        max-height: calc(100% - 120px);
    }

    .modal-header {
        border-radius: 9px 9px 0 0;
        height: 70px;
        padding: 0 30px;
    }

    .modal-body {
        padding: 0 30px;
    }

    .modal .modal-footer {
        border-radius: 0 0 9px 9px;
    }

    .modal.modal-status .modal-body {
        padding-top: 10px;
        padding-bottom: 15px;
    }

    .modal-status-icon {
        font-size: 36px;
    }

    .small-title {
        font-family: "bellslim_font_black", Helvetica, Arial, sans-serif;
        font-size: 24px;
        line-height: 26px;
    }
}
/**CSS for modal dialogues ends**/
/**********************************/


/**********************************/
/**CSS for review summary page starts**/
.form-group > .row, .form-group.row {
    display: flex;
}
.flex-grid-no-spacing .row {
    margin: 0;
}

.flex-grid-no-spacing .col-xl,
.flex-grid-no-spacing .col-xl-auto,
.flex-grid-no-spacing .col-xl-12,
.flex-grid-no-spacing .col-xl-11,
.flex-grid-no-spacing .col-xl-10,
.flex-grid-no-spacing .col-xl-9,
.flex-grid-no-spacing .col-xl-8,
.flex-grid-no-spacing .col-xl-7,
.flex-grid-no-spacing .col-xl-6,
.flex-grid-no-spacing .col-xl-5,
.flex-grid-no-spacing .col-xl-4,
.flex-grid-no-spacing .col-xl-3,
.flex-grid-no-spacing .col-xl-2,
.flex-grid-no-spacing .col-xl-1,
.flex-grid-no-spacing .col-lg,
.flex-grid-no-spacing .col-lg-auto, .flex-grid-no-spacing .col-lg-12, .flex-grid-no-spacing .col-lg-11, .flex-grid-no-spacing .col-lg-10, .flex-grid-no-spacing .col-lg-9, .flex-grid-no-spacing .col-lg-8, .flex-grid-no-spacing .col-lg-7, .flex-grid-no-spacing .col-lg-6, .flex-grid-no-spacing .col-lg-5, .flex-grid-no-spacing .col-lg-4, .flex-grid-no-spacing .col-lg-3, .flex-grid-no-spacing .col-lg-2, .flex-grid-no-spacing .col-lg-1, .flex-grid-no-spacing .col-md, .flex-grid-no-spacing .col-md-auto, .flex-grid-no-spacing .col-md-12, .flex-grid-no-spacing .col-md-11, .flex-grid-no-spacing .col-md-10, .flex-grid-no-spacing .col-md-9, .flex-grid-no-spacing .col-md-8, .flex-grid-no-spacing .col-md-7, .flex-grid-no-spacing .col-md-6, .flex-grid-no-spacing .col-md-5, .flex-grid-no-spacing .col-md-4, .flex-grid-no-spacing .col-md-3, .flex-grid-no-spacing .col-md-2, .flex-grid-no-spacing .col-md-1, .flex-grid-no-spacing .col-sm, .flex-grid-no-spacing .col-sm-auto, .flex-grid-no-spacing .col-sm-12, .flex-grid-no-spacing .col-sm-11, .flex-grid-no-spacing .col-sm-10, .flex-grid-no-spacing .col-sm-9, .flex-grid-no-spacing .col-sm-8, .flex-grid-no-spacing .col-sm-7, .flex-grid-no-spacing .col-sm-6, .flex-grid-no-spacing .col-sm-5, .flex-grid-no-spacing .col-sm-4, .flex-grid-no-spacing .col-sm-3, .flex-grid-no-spacing .col-sm-2, .flex-grid-no-spacing .col-sm-1, .flex-grid-no-spacing .col, .flex-grid-no-spacing .col-auto, .flex-grid-no-spacing .col-12, .flex-grid-no-spacing .col-11, .flex-grid-no-spacing .col-10, .flex-grid-no-spacing .col-9, .flex-grid-no-spacing .col-8, .flex-grid-no-spacing .col-7, .flex-grid-no-spacing .col-6, .flex-grid-no-spacing .col-5, .flex-grid-no-spacing .col-4, .flex-grid-no-spacing .col-3, .flex-grid-no-spacing .col-2, .flex-grid-no-spacing .col-1 {
    padding: 0;
}
.price {
    color: #00549a;
    display: block;
    white-space: nowrap;
    line-height: .8;
    letter-spacing: -1px;
    font-family:"bellslim_font_heavy",Helvetica,Arial,sans-serif;
}

.icon-circle-70 {
    width: 70px;
    height: 70px;
    background-color: #00549a;
    display: inline-block;
    position: relative;
    border: 2px solid #00549a;
    border-radius: 50%;
    color: #fff;
    font-size: 32px;
}
.icon-circle-70:before {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.price sup {
    top: -.70em;
    font-size: 14px;
    letter-spacing: 0;
}
.rate-plan-box .price sup{
    font-size:18px;
}

.terms-scroll {
    height: 75px;
    transition: height .3s ease-out;
}

.terms-scroll.show {
    height: 400px !important;
    transition: height .3s ease-out
}

.terms-scroll.collapse {
    display: block;
    overflow-y: auto;
}

.blur {
    opacity: .3;
}

.form-up-carret:before {
    position: absolute;
    content: "";
    display: block;
    width: 0;
    height: 0;
    left: 48%;
    top: 0;
    border: 10px solid transparent;
    border-bottom: 10px solid #f4f4f4;
}

.simple-header h1 {
    line-height: normal;
}

.width-75-inline {
    width: 75px;
    display: inline-block;
}

.review-tooltip {
    display: inline-block;
}

.small-view-terms a, .small-view-terms a:hover, .small-view-terms a:focus {
    text-decoration: underline;
}
.share-group-title{
    height:70px;
}
.share-group-title > div{
    position:relative;
    top:50%;
    transform:translateY(-50%);
}
.share-group-content{
    padding:25px 30px 0 30px;
}
.share-group-content .small-box {
    padding: 4px 5px;
    font-size: 10px;
    color:#fff;
    white-space:nowrap;
}
.sharegroup .col-sm-6:first-child{
    border:1px solid #e1e1e1;
    border-radius:10px 0 0 10px;
    border-right:0;
}
.sharegroup .col-sm-6:last-child {
    border: 1px solid #e1e1e1;
    border-radius: 0 10px 10px 0;
}
.sharegroup .col-sm-6:first-child .share-group-title {
    border-radius:10px 0 0 0;
}
.sharegroup .col-sm-6:last-child .share-group-title {
    border-radius: 0 10px 0 0;
}
.sharegroup .icon{
    width:30px;
    text-align:center;
}
/**CSS for review summary page ends**/
/**********************************/
.accordionContainer > ul > li:not(:last-child) {
    border-bottom: 1px solid #d4d4d4;
}

.accordionContainer li a[aria-expanded=true] span:not(.icon) {
    font-size: 14px;
    color: #111;
}

/*** device listing starts ***/
.responsive-simplified-header h3.bellSlim, .responsive-simplified-header h1.bellSlim {
    line-height: initial;
    font-size: 24px;
}

.responsive-simplified-header button {
    font-size: 15px;
    padding: 8px 31px;
}

.device-listing-carousel-title-wrap {
    align-items: baseline;
}

.device-listing-tablist .tablist .active span {
    border-bottom: 4px solid #00549a;
    padding-bottom: 13px;
    color: #111;
}

.device-listing-header .back .icon:before {
    top: -3px;
}

.device-listing-header h1{
    letter-spacing: 0.3px;
}

.device-listing-accordion .card-body{
    padding: 6px;
}

.device-listing-accordion .accordionButton.open .icon-exapnd-outline-circled:before {
    content: "\e90b";
}

.device-listing-accordion .icon-exapnd-outline-circled{
    font-weight: normal;
}

.device-listing-accordion .accordionButton {
    align-items: center;
}

.prod-tile-content .prod-tile-promo .prod-tile-promo-title {
    color: #00549A;
    font-weight: bold;
    text-transform: uppercase;
    padding-top: 5px;
    padding-bottom: 10px;
}

.device-listing-top-message .h4 {
    font-weight: 500;
    line-height: 1.2;
}

.prod-tile-content.view-all {
    display: flex;
    flex-direction: row;
    flex-flow: column wrap;
    justify-content: center;
}

.prod-tile-content.view-all > div{
    width: 100%;
    text-align: center;
}

.prod-tile-content.view-all span {
    height: 150px;
    width: 150px;
    border-radius: 50%;
    color: #00549a;
    font-size: 150px;
    left: 0;
    display: inline-block;
}

.prod-tile-content.view-all .prod-tile-link {
    position: absolute;
    background-color: transparent;
    z-index: 10;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    border-radius: 0;
    border: none;
    background-color: transparent;
    box-shadow: none;
    transition: none;
}

.prod-tile-content.view-all .prod-tile-link:hover, 
.prod-tile-content.view-all .prod-tile-link:focus{
    box-shadow: none;
}

.prod-tile-content.view-all .view-all-link{
    text-decoration: underline;
    font-size: 18px;
    padding-top: 25px;
    width: 100%;
    color: #00549a;
}

@media (min-width: 767.99px) {
    .prod-tile-content .prod-tile-note {
    letter-spacing: 0.5px;
}
}

@media (min-width: 320px) and (max-width: 767.98px) {
    .device-listing-top-message {
        background: #fff;
        padding-bottom: 30px;
    }
    .device-listing-top-message .heading {
        padding-top: 20px;
    }
    .device-listing-select-device-title{
        padding-top: 30px;
    }
    .device-listing-select-device-title .title {
        font-size: 28px;
    }
    .device-listing-carousel-title-wrap .title {
        font-size: 26px;
    }
    .prod-tile-content .prod-tile-note {
        letter-spacing: 0.2px;
    }
    .sharegroup .col-sm-6:first-child, .sharegroup .col-sm-6:last-child {
        border-radius: 10px;
        border-right: 1px solid #e1e1e1;
        margin-bottom: 15px;
    }
    .sharegroup .col-sm-6:first-child .share-group-title, .sharegroup .col-sm-6:last-child .share-group-title {
        border-radius: 10px 10px 0 0;
    }
    .modal.tooltip-modal .modal-header {
        background-color: #fff;
        padding: 15px;
        height: 40px;
        border-bottom: none;
    }
    .modal.tooltip-modal {
        position: fixed;
        width: 100%;
        height: 100%;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        overflow: hidden;
    }
    .modal.tooltip-modal .modal-dialog {
        margin: auto 20px;
        -webkit-transform: translate(0%, -50%);
        transform: translate(0%, -50%);
        position: relative;
        bottom: 50%;
        width: calc(100% - 40px)
    }
    .modal.tooltip-modal .modal-body {
        margin-bottom: 30px;
        margin-top: 0;
        padding: 0 30px;
    }
    .modal.tooltip-modal .modal-content {
        border-radius: 10px;
    }
    .modal.tooltip-modal .modal-dialog .modal-content .modal-header .close {
        margin: 0;
        padding: 0;
    }
    .modal.tooltip-modal .modal-dialog .modal-content .modal-header .icon.icon-close {
        color: #999;
        font-size: 16px;
        font-weight: bold;
    }
}
/*** device listing ends ***/
/**********************************/

/**device details Lower payment**/
.range-slider-wrapper .ticks {
    display: flex;
    justify-content: space-between;
    padding: 0px;
    margin-top: -20px;
    z-index: 0;
    margin-bottom: 20px;
}

.range-slider-wrapper .ticks .tick {
    position: relative;
    justify-content: center;
    width: 1px;
    background-color: #fff;
    height: 10px;
    line-height: 50px;
    margin-bottom: 0px;
    font-size: 0px;
    z-index: 0;
}
  
.range-slider-wrapper input[type=range].range-slider.range-slider-partitions::-ms-fill-lower {
    border-radius: 5px;
    background:#00549a;
}

.range-slider-wrapper input[type=range].range-slider.range-slider-partitions::-ms-fill-upper {
    border-radius: 5px;
}
.range-slider-wrapper .ticks .tick:first-child,
.range-slider-wrapper .ticks .tick:last-child {
    background: none;
}

/* customize css for firefox only */
@-moz-document url-prefix() {
    .range-slider-wrapper .ticks {
        margin-top: -24px;
    }
}

/* customize css for ie */
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    .range-slider-wrapper .ticks {
        margin-top: -25px;
    }
}
/* customize css for edge */
@supports (-ms-ime-align:auto) {
    .range-slider-wrapper .ticks {
        margin-top: -41px;
    }
}
/* for tick line */
.range-slider-wrapper .range-wrap {
    position: relative;
}
.range-slider-wrapper .range-slider-label {
    display: block;
    text-align: center;
}

.range-slider-wrapper input[type=range].range-slider {
    -webkit-appearance: none;
    width: 100%;
    outline: none;
    -webkit-transition: .2s;
    transition: opacity .2s;
    border-radius:5px;
    padding:0;
  
}
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .range-slider-wrapper input[type=range].range-slider {
        -webkit-appearance: none;
        width: 100%;
        height:10px;
        outline: none;
        -webkit-transition: .2s;
        transition: opacity .2s;
        border-radius: 5px;
        margin:10px 0;
    }
}
 .range-slider-wrapper input[type=range].range-slider::-ms-tooltip {
    display: none;
}
.range-slider-wrapper input[type=range].range-slider::-ms-track {
    border-color: transparent;
    color: transparent;
    cursor: pointer;
    background: #e1e1e1;
    border-radius: 5px;
    padding: 0;
    margin: 10px 0;
    border: none;
    overflow:hidden;
}
input[type=range].range-slider::-ms-thumb{
    -webkit-appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #ffffff;
    cursor: pointer;
    box-shadow: 0 6px 25px 0 rgba(0,0,0,0.2);
}
input[type=range].range-slider::-moz-range-thumb {
    -webkit-appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #ffffff;
    cursor: pointer;
    box-shadow: 0 6px 25px 0 rgba(0,0,0,0.2);
}
 input[type=range].range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #ffffff;
    cursor: pointer;
    box-shadow: 0 6px 25px 0 rgba(0,0,0,0.2);
}

.graphical_ctrl_checkbox.mod .ctrl_element:after {
    left: 8px;
    top: 2px;
    width: 8px;
    height: 14px;
}
.rate-plan-box{
    width:300px;
}
.rate-plan-box li {
    margin-top: 5px;
}

.rate-plan-box .icon {
    flex-basis: 32px;
    flex-grow: 0;
    flex-shrink: 0;
    text-align: center;
}

.rate-plan-box .current-plan {
    padding: 3px 8px;
    position: absolute;
    transform: translate(-50%,-50%);
    color: #ffffff;
    top: 0;
    left: 50%;
    font-size: 10px;
}

.tablist-pills-container {
    display: flex;
    font-size: 16px;
    justify-content: center;
}
.tablist-pills-container ul {
    background-color: #F4F4F4;
    border: 1px solid #FFFFFF;
    border-radius: 30px;
    display: flex;
    flex-direction: row;
    padding: 5px;
    list-style: none;
    margin: 0;
    color: #00549A;
    text-align: center;
}
.tablist-pills-container ul li {
    padding: 8px 30px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}
.tablist-pills-container ul li[aria-selected="true"] {
    border: 1px solid #E1E1E1;
    border-radius: 30px;
    background-color: #FFFFFF;
    box-shadow: 0 0 25px 0 rgba(0,0,0,0.12);
    color: #111;
}
.info-yellow-icon {
    color: #DFA32A;
}
.rate-plans-col-mar .rate-plan-box:first-child {
    margin-right: 15px;
    width:300px;
}

.rate-plans-col-mar .rate-plan-box:last-child {
    margin-left: 15px;
    width: 300px;
}
.rate-plans-col-mar-3 .rate-plan-box:first-child {
    margin-right: 15px;
    width: 300px;
}
.rate-plans-col-mar-3 .rate-plan-box:nth-child(2){
    margin-right: 15px;
    margin-left:15px;
    width: 300px;
}
.rate-plans-col-mar-3 .rate-plan-box:last-child {
    margin-left: 15px;
    width: 300px;
}
.price-cancel:before{
    content:" ";
    position:absolute;
    width:calc(100% - 10px);
    top:50%;
    transform:translateY(-50%);
    height:1px;
    border:.5px solid #555555;
}
.device-deatils-margin{
    margin:0 40px;
}
.lower-monthly-section{
    width:630px;
}
.device-options-box{
    width:486px;
}
.apply-payment-box{
    width:300px;
    height:200px;
}
.device-return-box{
    width:300px;
}
.device-summary  .col-sm-6{
    border:1px solid #e1e1e1;
    background:#f4f4f4;
}
.device-summary .col-sm-6:first-child {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}
.device-summary .col-sm-6:last-child {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    border-left:none;
}
/**device details Lower payment**/
/**confirmation**/
.two-col-with-border .col-sm-6:first-child {
    border-right: 1px solid #e1e1e1;
}
/**confirmation**/

/**Change address modals**/
.change-address-modal .modal-footer {
    padding: 30px;
    background: #f0f0f0;
    border-top:1px solid #D4D4D4;
    display:block;
}
.modal.toaster .modal-dialog {
    position:absolute;
    top:90px;
    left:50%;
    transform:translateX(-50%);
    width:500px;
}
.modal.toaster .modal-body {
    padding: 15px;
    margin: 0;
}
.modal.toaster .modal-dialog, .modal.toaster .modal-content {
    border-radius: 2px;
    border-bottom: 2px solid #339043;
}
.modal.toaster .close {
    font-size: 10px;
    margin-top: -5px;
    margin-right:-5px;
}
/**Change address modals**/
/**css for tsliding tabs from shop.css**/
.tab-panels-container [role="tabpanel"][aria-hidden="true"]:not(.slick-slide) {
    display: none
}
/**css for tsliding tabs**/
/**css for price dock**/
.price-dock{   
    padding:15px 0 20px 0;
    width:100%;
}
.price-dock > div{
    padding:0 10px;
    border-right:1px solid #d4d4d4;
    text-align:center;
}
.price-dock .price , .price.txtHeavy{
    font-family: "bellslim_font_heavy",Helvetica,Arial,sans-serif;
}
.price-dock > div:last-child{
    border-right:none;
}
.price-dock.sticky {
    position: fixed;
    top: 0;
    left: 0;
    border-radius: 0 0 10px 10px;
    z-index: 50;
    border-bottom:1px solid #e1e1e1;
    display:none;
}
.validation-message {
    display: none;
}
.one-time-charges .table-footer {
    margin-left: -30px;
    width: calc(100% + 60px);
}
/**css for price dock**/
@media(min-width: 320px) and (max-width: 767.98px) {
 .rate-plan-box .ctrl_radioBtn .ctrl_element {
    top: 3px;
    left: 0;
}
    .rate-plans-col-mar .rate-plan-box:first-child {
        margin-right: 0;
        width: 100%;
    }

    .rate-plans-col-mar .rate-plan-box:last-child {
        margin-left: 0;
        width: 100%;
    }
    .rate-plans-col-mar-3 .rate-plan-box:first-child {
        margin-right: 0;
        width: 100%;
    }

    .rate-plans-col-mar-3 .rate-plan-box:nth-child(2) {
        margin-right: 0;
        margin-left: 0;
        width: 100%;
    }

    .rate-plans-col-mar-3 .rate-plan-box:last-child {
        margin-left: 0;
        width: 100%;
    }
.lower-payment-boxes {
    margin: 0;
}

.device-deatils-margin {
    margin: 0;
}
.two-col-with-border .col-sm-6:first-child {
    border: none;
}

.modal .modal-dialog {
    height: auto;
    max-height: calc(100% - 45px);
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
}

.modal .modal-dialog.modal-mobile-full-height {
    height: 100%;
    max-height: none;
}

.modal .modal-mobile-full-height .modal-header {
    border-radius: 0;
}

.modal .modal-mobile-full-height .modal-footer {
    position: absolute;
    width: 100%;
    bottom: 0;
}

.modal.toaster .modal-dialog {
    top: 42px;
    bottom: auto;
    width: calc(100% - 30px);
}
.one-time-charges .table-footer {
    margin-left: -15px;
    width: calc(100% + 30px);
}
.lower-monthly-section, .device-options-box, .device-options-box-550, .rate-plan-box, .apply-payment-box, .device-return-box {
    width: 100%;
}
.device-summary .col-sm-6 {
    border-radius: 10px;
}
.device-summary .col-sm-6:last-child {
    border-left:1px solid #e1e1e1;
}
.change-address-modal .modal-footer {
    padding: 30px 15px;
}
}
@media(min-width:1200px) {
    .device-deatils-margin {
        margin: 0 135px;
    }
    .price-dock.sticky{
        width:1200px;
        left:50%;
        transform:translateX(-50%);
    }
}
/**********************************/
/*** balance details modals begin ***/
.device-balance-modal .icon.icon-i-icon {
    font-size: 16px;
    top: 2px;
    position: relative;
}

.device-balance-modal .progress {
    height: 10px;
    margin-bottom: 0;
    background-color: #e1e1e1;
    border-radius: 5px;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.device-balance-modal .progress-bar {
    background: #00549A;
    box-shadow: none;
    line-height: 10px;
    float: left;
    width: 0;
    height: 100%;
    font-size: 12px;
    color: #fff;
    text-align: center;
    transition: width .6s ease;
}

.device-balance-modal .progress-text {
    margin: 10px 0;
    text-align: right;
    padding-right: 10px;
    position: relative;
    font-size: 12px;
}

.device-balance-modal .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0,0,0,0);
    border: 0;
}

.device-balance-modal .progress-pointer-top {
    top: 2px;
}

.device-balance-modal .progress-pointer-top,
.device-balance-modal .progress-pointer-bottom {
    position: absolute;
    right: 0;
    width: 1px;
    height: 35px;
    background: #00215E;
}

.device-balance-modal .progress-pointer-bottom {
    top: -20px;
}

@media(min-width: 320px) and (max-width: 767.98px) {
    .balance-details-modal .modal-footer {
        padding-left: 15px;
        padding-right: 15px;
    }
}
/*** balance details modals end ***/
/**********************************/
/*** leaving flow modal begin ***/
.leaving-flow-modal .modal-footer > div{
    margin: 0;
}

@media(min-width: 320px) and (max-width: 767.98px) {
    .leaving-flow-modal .modal-footer > div:first-child {
        padding-right: 0;
        padding-bottom: 15px;
    }
    .leaving-flow-modal .modal-footer {
        padding: 30px 15px;
    }
}
/*** leaving flow modal end ***/
/**********************************/
.simplified-header-back{
    width:auto;
}
/************************************************/
/*** device details options and pricing begin ***/
.device-details-header .back .icon:before {
    top: -3px;
}

.device-details-options-pricing .slick-dots {
    justify-content: center;
    padding-left: 0;
}

.device-details-options-pricing .slick-dots li {
    position: relative;
    display: inline-block;
    width: 20px;
    height: 20px;
    padding: 0;
    cursor: pointer;
}

.device-details-options-pricing .slick-dots li button {
    font-size: 0;
    line-height: 0;
    display: block;
    width: 10px;
    height: 10px;
    padding: 0;
    cursor: pointer;
    color: transparent;
    outline: none;
    background: transparent;
    border-radius: 100%;
    border: 1px solid #555;
}

.device-details-options-pricing .device-image-slick {
    opacity: 0;
    visibility: hidden;
    transition: opacity 1s ease;
    -webkit-transition: opacity 1s ease;
}
.device-details-options-pricing .device-image-slick.slick-initialized  {
    opacity: 1;
    visibility: visible;
}

.device-details-options-pricing .slick-dots li.slick-active button {
    background: #555;
}

.device-details-options-pricing .device-showcase img {
    max-width: 240px;
    height: auto;
    margin: 0 auto;
}

.device-details-options-pricing .device-showcase .slick-dots{
    display: none;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
}


.device-details-options-pricing .options-and-pricing {
    padding: 15px 15px 15px 30px;
    border-radius: 10px;
    border: 1px solid #e1e1e1;
    background-color: transparent;
    box-shadow: 0 6px 25px 0 rgba(0,0,0,0.12);
    transition: box-shadow .2s;
    text-align: left;
}

.device-details-options-pricing .options-and-pricing .links {
    display: flex;
}

.device-details-options-pricing .options-and-pricing .links a{
    text-decoration: underline;
}

.device-details-options-pricing .options-and-pricing .links a:first-child{
    padding-right: 10px;
    border-right: 1px solid #e1e1e1;
}

.device-details-options-pricing .options-and-pricing .links a:last-child{
    padding-left: 10px;
}


.device-details-options-pricing .offer .device-tile-flag {
    padding: 3px 7px 2px;
    background-color: #00549a;
    font-size: 10px;
    letter-spacing: 0.2px;
    font-weight: bold;
    line-height: 14px;
    color: #fff;
    text-transform: uppercase;
    text-align: center;
    display: inline-block;
    align-self: baseline;
    border-radius: 2px;
}

.device-details-options-pricing .offer .device-tile-price-wrap {
    display: flex;
    flex-direction: row;
    margin-top: 15px;
    position: relative;
}

.device-details-options-pricing .offer .device-tile-note {
    margin-top: 15px;
    font-size: 12px;
    position: relative;
    z-index: 15;
}

.device-details-options-pricing .offer .device-tile-promo {
    margin-top: 10px;
    border: 2px solid #00549A;
    border-radius: 10px;
    padding: 10px;
    position: relative;
}

.device-details-options-pricing .offer .device-tile-promo .device-tile-promo-title {
    color: #00549A;
    font-weight: bold;
    text-transform: uppercase;
    padding-bottom: 10px;
}

.device-details-options-pricing .offer .device-tile-promo-body {
    font-size: 12px;
}

.device-details-options-pricing .offer .device-tile-full-price {
    margin-top: 15px;
    position: relative;
}

.device-details-options-pricing .offer .device-tile-full-price > span {
    font-weight: bold;
    font-size: 18px;
}

.device-details-options-pricing .offer .device-tile-price-down {
    width: 25%;
    padding-right: 5px;
}

.device-details-options-pricing .offer .device-tile-price-month {
    width: 50%;
}

.device-details-options-pricing .offer .device-tile-price-note-top {
    height: 25px;
    font-size: 12px;
}

.device-details-options-pricing .offer .device-tile-price {
    color: #00549A;
    font-weight: bold;
    font-size: 18px;
}

.device-details-options-pricing .offer .device-tile-price-note-bottom {
    font-size: 12px;
}

.device-details-options-pricing .options-and-pricing .memory-filters .memory {
    display: flex;
    flex-shrink: 0;
}

.device-details-options-pricing .options-and-pricing .memory-filters .memory > button[aria-pressed="true"] {
    background-color: #003778;
    color: #fff;
}

.device-details-options-pricing .options-and-pricing .memory-filters .memory > button {
    display: block;
    color: #111111;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 16px;
    border: 1px solid #D4D4D4;
    border-radius: 15px;
    background-color: #FFFFFF;
    padding: 6px 14px;
    margin: 0 10px 0 0;
    cursor: pointer;
}

.device-details-options-pricing .options-and-pricing .color-filters ul {
    list-style-type: none;
    padding-left: 0;
}

.device-details-options-pricing .options-and-pricing .color-filters ul > li {
    display: block;
    float: left;
    border: 1px solid #cacaca;
    border-radius: 50%;
    cursor: pointer;
    transition: box-shadow .15s cubic-bezier(.17,.67,.83,.67), width .5s cubic-bezier(.55,0,.1,1);
    width: 20px;
    height: 20px;
    margin-right: 10px;
}

.device-details-options-pricing .options-and-pricing .color-filters ul > li > label{
    left: -2px;
}

.device-details-options-pricing .options-and-pricing .color-filters ul > li .ctrl_element{
    height: 20px;
    width: 20px;
    top: -1px;
    left: 1px;
    border: none;
    box-shadow: none;
}

.device-details-options-pricing .options-and-pricing .color-filters ul > li .ctrl_element:after{
    height: 18px;
    width: 18px;
    left: -1px;
    top: -1px;
    border-radius: 50%;
}

.device-details-options-pricing .options-and-pricing .color-filters ul > li .color-pill-appleblack .ctrl_element,
.device-details-options-pricing .options-and-pricing .color-filters ul > li .color-pill-appleblack .ctrl_element:after{
    background: #333;
}

.device-details-options-pricing .options-and-pricing .color-filters ul > li .color-pill-applegrey .ctrl_element,
.device-details-options-pricing .options-and-pricing .color-filters ul > li .color-pill-applegrey .ctrl_element:after{
    background: #D0C9DB;
}

.device-details-options-pricing .options-and-pricing .color-filters ul > li .color-pill-appleteal .ctrl_element,
.device-details-options-pricing .options-and-pricing .color-filters ul > li .color-pill-appleteal .ctrl_element:after{
    background: #A8E6C6;
}

.device-details-options-pricing .options-and-pricing .color-filters ul > li .color-pill-applered .ctrl_element,
.device-details-options-pricing .options-and-pricing .color-filters ul > li .color-pill-applered .ctrl_element:after{
    background: #B41324;
}

.device-details-options-pricing .options-and-pricing .color-filters ul > li .color-pill-applewhite .ctrl_element,
.device-details-options-pricing .options-and-pricing .color-filters ul > li .color-pill-applewhite .ctrl_element:after{
    background: #fff;
}

.device-details-options-pricing .options-and-pricing .color-filters ul > li .color-pill-applegold .ctrl_element,
.device-details-options-pricing .options-and-pricing .color-filters ul > li .color-pill-applegold .ctrl_element:after{
    background: #F9D045;
}

.device-details-options-pricing .options-and-pricing .color-filters ul > li.active .ctrl_element:after{
    border: 2px solid #fff;
}

.device-details-options-pricing .options-and-pricing .color-filters ul > li .color-pill-applewhite  .ctrl_element{
    border: 2px solid #999;
}


.device-details-options-pricing .title-wrap {
    padding-bottom: 10px;
}

.device-details-options-pricing .options-and-pricing .color-filters .graphical_ctrl input:checked ~ .ctrl_element {
    border: 2px solid #003778;
}

@media(min-width: 992px) {
    .device-details-options-pricing .device-showcase .slick-dots {
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        padding-top: 30px;
    }

        .device-details-options-pricing .device-showcase .slick-dots li:first-child {
            margin-left: 0;
        }

        .device-details-options-pricing .device-showcase .slick-dots li {
            position: relative;
            margin-left: 5px;
            width: auto;
            height: auto;
        }

        .device-details-options-pricing .device-showcase .slick-dots a img {
            height: 52px;
            margin: 0 10px 10px 10px;
        }

        .device-details-options-pricing .device-showcase .slick-dots li.slick-active:after {
            content: "";
            position: absolute;
            bottom: 0;
            height: 3px;
            background-color: #00549a;
            width: 100%;
            left: 0;
            z-index: 1;
        }
    .device-details-options-pricing .device-showcase-wrap{
        width: 38%;
        padding-top: 20px;
    }
    .device-details-options-pricing .options-and-pricing-wrap {
        width: 62%;
    }
    .device-details-options-pricing .options-and-pricing-wrap .offer{
        padding: 30px 25px 55px 25px;
    }
    .device-details-options-pricing .page-title-wrap {
        padding-bottom: 35px;
    }
}

@media(max-width: 991px) {
    .device-details-options-pricing .device-showcase-wrap, 
    .device-details-options-pricing .options-and-pricing-wrap {
        display: block;
        width: 100%;
    }
}

@media (min-width: 767.99px) and (max-width: 991px) {
    .device-details-options-pricing .options-and-pricing {
        display: flex;
    }
    .device-details-options-pricing .page-title-wrap{
        padding-bottom: 50px;
    }
    .device-details-options-pricing .device-showcase img{
        max-width: 200px;
    }
    .device-details-options-pricing .device-showcase-wrap .slick-dots {
        margin-top: 15px;
        margin-bottom: 20px;
    }
    .device-details-options-pricing .options-and-pricing-wrap .offer {
        padding: 30px;
    }
    .rate-plan-box{
        width:347px;
    }
    .rate-plans-col-mar .rate-plan-box:first-child {
        margin-right: 7.5px;
        width:346px;
    }
    .rate-plans-col-mar .rate-plan-box:last-child {
        margin-left: 7.5px;
        width: 346px;
    }
    .rate-plans-col-mar-3 .rate-plan-box:first-child {
        margin-right: 7.5px;
        width: 346px;
    }

    .rate-plans-col-mar-3 .rate-plan-box:nth-child(2) {
        margin-right: 0px;
        margin-left: 7.5px;
        width: 346px;
    }

    .rate-plans-col-mar-3 .rate-plan-box:last-child {
        margin-left: 0;
        width: 346px;
    }
}

@media (min-width: 767.99px) {
    .device-details-options-pricing .options-and-pricing {
        display: flex;
    }
}

@media(min-width: 320px) and (max-width: 767.98px) {
    .device-details-options-pricing .options-and-pricing {
        display: block;
    }

    .device-details-options-pricing .options-and-pricing {
        box-shadow: none;
        border: 0;
    }

    .device-details-options-pricing .device-showcase img {
        max-width: 100px;
    }

    .device-details-options-pricing .page-title-wrap {
        padding-bottom: 30px;
    }

    .device-details-options-pricing .options-and-pricing {
        padding: 0;
    }

    .device-details-options-pricing .offer {
        border-radius: 0;
        padding: 30px 15px;
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
    }
    .device-details-options-pricing .options-and-pricing-wrap .title-wrap {
        padding-top: 0;
        padding-bottom: 10px;
    }
    .device-details-options-pricing .device-showcase-wrap-row {
        padding-top: 20px;
        margin: 0;
        border-radius: 10px;
        border: 1px solid #e1e1e1;
        background-color: transparent;
        box-shadow: 0 6px 25px 0 rgba(0,0,0,0.12);
        transition: box-shadow .2s;
        display: block;
    }
    .device-details-options-pricing .device-showcase-wrap-row .specs{
        padding: 0 15px 10px 15px;
    }
    .device-details-options-pricing .device-showcase-wrap-row .slick-dots {
        margin-top: 10px;
    }
    .device-details-options-pricing .page-title-wrap .title {
        line-height: 32px;
    }
}
/*** device details options and pricing end ***/
/**********************************************/
/**Footer**/
footer{
    padding-top:25px;
    padding-bottom:30px;
}
footer img{
    margin-left:15px;
}
footer .separator{
    display:inline-block;
}
footer .copy-right{
    padding-top:5px;
}
@media(min-width: 320px) and (max-width: 767.98px){
    footer{
        text-align:center;
    }
    footer a{
        display:block;
        padding-bottom:8px;
    }
    footer img{
        display:block;
        margin:0 auto;
    }
    footer .separator{
        display:none;
    }
    footer .copy-right{
        padding-top:0;
    }
}
/**Footer**/
/**fix for images height**/
.device-option-image{
    height:110px;
    width:auto;
}
.review-summary-image{
    height: 96px;
    width: auto;
}
.img-trade-in{
    height:172px;
    width:auto;
    display:block;
}
.sms-verification-alert{
    border-top:1px solid #e1e1e1;
    border-bottom:1px solid #e1e1e1;
}
.cc-help-img{
    height:126px;
    width:auto;
}
.rate-plan-box .custom-control{
    padding-left:0;
}

.potential-promotion-loss-modal .modal-body{
    margin-bottom:20px;
}
.graphical_ctrl input:focus ~ .ctrl_element{
    outline:auto;
}
.technical-specifications-category .heading .icon-data-3:before{
    top:10px;
}
.technical-specifications-category .heading .icon-silhouette{
    top:-3px;
    position:relative;
}
.max-width185{
    max-width:185px
}
.display-inline{
    display:inline;
}
.display-block{
    display:block;
}
.max-width-rate-plans {
    max-width: 960px;
}
.pending-request-margin{
    margin-top:7px;
    margin-bottom:4px;
}
.incompatible-modal .ctrl_radioBtn .ctrl_element{
    top:3px;
    left:0;
}
.promo-wrapper {
    display: flex;
    flex-wrap: nowrap;
    width: 100%;
    margin-bottom:25px;
}
.scrollable-promo {
    display: flex;
    flex-direction: column;
    justify-content: center;
    background-color: #fff;
    overflow-x: auto;
    flex-shrink: 0;
}
.promo-wrapper-flex {
    display: flex;
    flex-shrink: 0;
}
.promo-wrapper-flex.single-promo{
    width:100%;
}
.single-promo .promo-box{
    width:100%;
}
.promo-box {
    width: 260px;
    margin-right: 7.5px;
}
.promo-box:nth-child(2){
    margin-left:7.5px;
}
.promo-box>div{    
    box-shadow: 0 0.6px 25px 0 rgba(0,0,0,0.12);
}
.promo-box .bonus{
    padding:3px 8px;
}
@media (min-width: 767.99px) and (max-width: 991px) {
    .img-trade-in {
        height:140px;
    }
    .max-width-rate-plans {
        max-width: 722px;
    }
}
@media(min-width:767.99px){
    .sms-verification-alert {
      border:none;
    }
    .device-options-box-550 {
        width: 550px;
    }
    .promo-wrapper-flex{
        width:100%;
    }
    .promo-box, .single-promo .promo-box {
        width: 50%;
    }
}
