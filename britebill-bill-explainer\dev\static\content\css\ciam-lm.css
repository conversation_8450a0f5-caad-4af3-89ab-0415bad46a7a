/* Helpers */

.form-default-theme .lm-form-control{
    border: 2px solid #949596;
}

.form-default-theme .btn-select{
    border: 2px solid #949596;
}

.form-default-theme input[type="text"].lm-form-control,
.form-default-theme input[type="number"].lm-form-control,
.form-default-theme input[type="tel"].lm-form-control,
.form-default-theme input[type="email"].lm-form-control,
.form-default-theme input[type="password"].lm-form-control,
.form-default-theme textarea.lm-form-control {
    color: #666;
    font-family: 'GTWalsheim';
    padding: 0 14px;
}

.form-default-theme .lm-checkbox-input ~ .lm-check-element {
    background: #fff;
    border: 1px solid #949596;
    box-shadow: inset 0 1px 3px 0 rgba(0,0,0,0.2);
}

.form-default-theme .lm-checkbox-input:checked ~ .lm-check-element {
    background: #002D72;
    border: 0;
    box-shadow: inset 0 1px 3px 0 rgba(0,0,0,0.14);
}

.form-default-theme .lm-form-control:focus {
    border: 2px solid #B0B0B0;
}

.form-default-theme.form-error input.lm-form-control,
.form-default-theme .form-error input.lm-form-control,
.form-error .form-default-theme input.lm-form-control,
.form-default-theme.form-error textarea.lm-form-control,
.form-default-theme .form-error textarea.lm-form-control,
.form-error .form-default-theme textarea.lm-form-control {
    border: 2px solid #D32020;
}

.lm-check-element.icon-checkmark-new {
    align-items: center;
    display: flex;
    font-size: 12px;
    justify-content: center;
    position: static;
}

.icon-checkmark-new:before {
    font-size: inherit;
    position: static;
}

.lm-check-element.icon-checkmark-new + .lm-check-control {
    position: static;
}

.form-default-theme .btn-primary,
.form-default-theme .btn-primary:not(:disabled):not(.disabled):active,
.form-default-theme .btn-primary:not(:disabled):not(.disabled):focus,
.btn-primary.button-shadow-border,
.btn-primary.button-shadow-border:not(:disabled):not(.disabled):active,
.btn-primary.button-shadow-border:not(:disabled):not(.disabled):focus {
    background: #41b6e6;
    border: none;
    border-radius: 2px;
    box-shadow: 1px 1px 2px 0 rgba(0,0,0,0.2);
    color: #002D72;
}

    .form-default-theme .btn-primary.disabled,
    .form-default-theme .btn-primary:disabled,
    .form-default-theme .btn-primary.disabled:active,
    .form-default-theme .btn-primary:disabled:active,
    .form-default-theme .btn-primary.disabled:focus,
    .form-default-theme .btn-primary:disabled:focus,
    .btn-primary.button-shadow-border.disabled,
    .btn-primary.button-shadow-border:disabled,
    .btn-primary.button-shadow-border.disabled:active,
    .btn-primary.button-shadow-border:disabled:active,
    .btn-primary.button-shadow-border.disabled:focus,
    .btn-primary.button-shadow-border:disabled:focus {
        background: #b7d7e2;
        border: none;
        border-radius: 2px;
        box-shadow: 1px 1px 2px 0 rgba(0,0,0,0.2);
        color: #7e949c;
        cursor: default;
    }

    .form-default-theme .btn-primary:not(:disabled):not(.disabled):hover,
    .btn-primary.button-shadow-border:not(:disabled):not(.disabled):hover {
        background: #80dbff;
    }

.form-default-theme .btn-secondary,
.form-default-theme .btn-secondary:not(:disabled):not(.disabled):active,
.form-default-theme .btn-secondary:not(:disabled):not(.disabled):focus,
.btn-secondary.button-shadow-border,
.btn-secondary.button-shadow-border:not(:disabled):not(.disabled):active,
.btn-secondary.button-shadow-border:not(:disabled):not(.disabled):focus {
    background: #fff;
    border: 2px solid #41B6E6;
    border-radius: 2px;
    box-shadow: 1px 1px 2px 0 rgba(0,0,0,0.2);
    color: #002D72;
}

    .form-default-theme .btn-secondary.disabled,
    .form-default-theme .btn-secondary:disabled,
    .form-default-theme .btn-secondary.disabled:active,
    .form-default-theme .btn-secondary:disabled:active,
    .form-default-theme .btn-secondary.disabled:focus,
    .form-default-theme .btn-secondary:disabled:focus,
    .btn-secondary.button-shadow-border.disabled,
    .btn-secondary.button-shadow-border:disabled,
    .btn-secondary.button-shadow-border.disabled:active,
    .btn-secondary.button-shadow-border:disabled:active,
    .btn-secondary.button-shadow-border.disabled:focus,
    .btn-secondary.button-shadow-border:disabled:focus {
        background: #b7d7e2;
        border: 2px solid #b7d7e2;
        border-radius: 2px;
        box-shadow: 1px 1px 2px 0 rgba(0,0,0,0.2);
        color: #7e949c;
        cursor: default;
    }

    .form-default-theme .btn-secondary:not(:disabled):not(.disabled):hover,
    .btn-secondary.button-shadow-border:not(:disabled):not(.disabled):hover {
        background: #e2f8ff;
    }

.form-default-theme .btn-show-password,
.form-default-theme .btn-show-password:not(:disabled):not(.disabled):active,
.form-default-theme .btn-show-password:not(:disabled):not(.disabled):focus {
    background: #666;
    border: none;
    border-radius: 5px;
    padding: 5px 6px;
    font-family: Helvetica, Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    line-height: 16px;
    position: absolute;
    top: 35px;
    right: 12px;
}

.btn-show-password:focus {
    box-shadow: none;
}

.error-message {
    display: none;
}

.form-error .error-message {
    display: block;
}

    .form-error .error-message.flex {
        display: flex;
    }

.form-error label,
.messages-error {
    color: #D32020;
}

.form-select-container.active {
    height: 68px;
}

.inline-flex {
    display: inline-flex;
}

.pad-t-3 {
    padding-top: 3px;
}

.margin-b-6{
    margin-bottom: 6px;
}

.max-w-290 {
    max-width: 290px;
}

.max-w-340 {
    max-width: 340px;
}

.inset-shadow-grey {
    box-shadow: inset 0 1px 0 0 #E1E1E1;
}

.line-height-12 {
    line-height: 12px;
}

.line-height-14 {
    line-height: 14px;
}

.line-height-16 {
    line-height: 16px;
}

.line-height-18 {
    line-height: 18px;
}

.line-height-20 {
    line-height: 20px;
}

.line-height-22 {
    line-height: 22px;
}

.line-height-28 {
    line-height: 28px;
}

.txt222 { /* can't use txtDarkGrey because both LM.css and allBrowsers_framework.css are using it but with different colors */
    color: #222;
}

.txt555 { /* can't use txtDarkGrey because both LM.css and allBrowsers_framework.css are using it but with different colors */
    color: #555;
}

.top-4 {
    top: 4px;
}

.relative-links a {
    position: relative;
}

.list-style-inside {
    list-style-position: inside;
}

ul.pipe-separated-list {
    display: flex;
    justify-content: center;
    list-style: none;
}

    ul.pipe-separated-list li {
        display: flex;
        align-items: center;
    }

        ul.pipe-separated-list li:not(:last-child):after {
            border-right: 1px solid #d4d4d4;
            content: '';
            display: inline-block;
            height: 12px;
            margin: 0 10px;
        }

        ul.pipe-separated-list li a {
            color: #555;
        }

.js-milestone-progressbar .ui-progressbar-value {
    display: block !important; /*. force show for zero value animation */
    transition: all 0.5s;
}

.js-readonly-checklist:not(.js-readonly-checklist-initialized) .js-readonly-checklist-checked {
    display: none;
}

.tooltip-default-theme {
    position: relative;
}

    .tooltip-default-theme .tooltip.show {
        opacity: 1;
    }

    .tooltip-default-theme .tooltip {
        border: 1px solid #E1E1E1;
        background-color: #FFFFFF;
        box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
        font-family: Helvetica, Arial, sans-serif;
    }

        .tooltip-default-theme .tooltip .tooltip-inner {
            background-color: #FFFFFF;
            border: 0;
            box-shadow: none;
        }

        .tooltip-default-theme .tooltip .arrow {
            display: block;
            height: 0;
            width: 0;
            pointer-events: none;
        }

            .tooltip-default-theme .tooltip .arrow:before,
            .tooltip-default-theme .tooltip .arrow:after {
                content: "";
                display: block;
                border-width: 15px;
                border-color: transparent;
                border-style: solid;
                position: relative;
            }

            .tooltip-default-theme .tooltip .arrow:after {
                border-width: 14px;
            }

        .tooltip-default-theme .tooltip.bs-tooltip-bottom .arrow {
            left: 50% !important;
            top: -30px !important;
        }

            .tooltip-default-theme .tooltip.bs-tooltip-bottom .arrow:before {
                border-bottom-color: #e1e1e1;
                transform: translateX(-50%);
            }

            .tooltip-default-theme .tooltip.bs-tooltip-bottom .arrow:after {
                border-bottom-color: #fff;
                transform: translateX(-50%);
                position: absolute;
                top: 2px;
                left: 0;
            }

        .tooltip-default-theme .tooltip.bs-tooltip-right .arrow {
            left: -30px !important;
            top: 50% !important;
        }

            .tooltip-default-theme .tooltip.bs-tooltip-right .arrow:before {
                border-right-color: #e1e1e1;
                transform: translateY(-50%);
            }

            .tooltip-default-theme .tooltip.bs-tooltip-right .arrow:after {
                border-right-color: #fff;
                transform: translateY(-50%);
                position: absolute;
                top: 0;
                left: 2px;
            }

    .tooltip-default-theme.tooltip-min-w-300 .tooltip {
        min-width: 300px;
        max-width: 300px;
    }

    .tooltip-default-theme .tooltip .tooltip-inner {
        max-width: 100%;
    }

    .line-height-18 .tooltip-default-theme .tooltip,
    .tooltip-default-theme .tooltip .line-height-18,
    .tooltip-default-theme.line-height-18 .tooltip {
        line-height: 18px;
    }

    .tri-up-line { 
        position: relative; 
        margin-top: 15px; 
        border-top: 1px solid #d7d7d7; 
        background: #fff; 
    }
    
        .tri-up-line:before, .tri-up-line:after {
            content: ""; 
            position: absolute; 
            width: 0; 
            height: 0; 
            border-style: solid; 
            border-color: transparent; 
            border-top: 0; 
        }
    
        .tri-up-line:before {
            top: -17px; 
            left: 50%; 
            border-bottom-color: #d7d7d7;
            border-width: 16px; 
            transform: translateX(-50%); 
        }
    
        .tri-up-line:after {
            top: -15px; 
            left: 50%; 
            border-bottom-color: #fff; 
            border-width: 15px; 
            transform: translateX(-50%); 
        }
    
@media (max-width: 767.98px) {
	html.affix-footer-xs {
		display: flex;
		height: -webkit-fill-available;
	}

		html.affix-footer-xs body,
		html.affix-footer-xs .landmarks-ancestor {
			display: flex;
			min-height: 100vh;
			min-height: -webkit-fill-available;
			width: 100%;
		}

		html.affix-footer-xs body:not(.body-with-landmarks-ancestor),
		html.affix-footer-xs .landmarks-ancestor {
			align-items: center;
			flex-direction: column;
		}

        html.affix-footer-xs header,
        html.affix-footer-xs footer {
            flex-shrink: 0;
        }

        html.affix-footer-xs main {
            flex-grow: 1;
            flex-shrink: 0;
        }

    .max-w-xs-280 {
        max-width: 280px;
    }

    .tooltip-hidden-xs .tooltip {
        display: none;
    }

    .margin-b-xs-3{
        margin-bottom: 3px;
    }
}

@media (min-width: 768px) {
    .bgLuckyBlue-sm {
        background: #41B6E6;
    }

    .borderRadius-t-sm-3 {
        border-radius: 3px 3px 0 0;
    }

    .borderRadius-b-sm-3 {
        border-radius: 0 0 3px 3px;
    }

    .borderGrayLight6-upper-sm {
        border: 1px solid #d4d4d4;
        border-bottom: 0;
    }

    .borderGrayLight6-middle-sm {
        border: 1px solid #d4d4d4;
        border-top: 0;
        border-bottom: 0;
    }

    .borderGrayLight6-lower-sm {
        border: 1px solid #d4d4d4;
        border-top: 0;
    }

    .max-w-sm-402 {
        max-width: 402px;
    }
}

@media (min-width: 992px) {
    .bgLuckyBlue-sm {
        background: #41B6E6;
    }

    .borderRadius-t-sm-3 {
        border-radius: 3px 3px 0 0;
    }

    .borderRadius-b-sm-3 {
        border-radius: 0 0 3px 3px;
    }

    .borderGrayLight6-upper-sm {
        border: 1px solid #d4d4d4;
        border-bottom: 0;
    }

    .borderGrayLight6-middle-sm {
        border: 1px solid #d4d4d4;
        border-top: 0;
        border-bottom: 0;
    }

    .borderGrayLight6-lower-sm {
        border: 1px solid #d4d4d4;
        border-top: 0;
    }

    .max-w-sm-402 {
        max-width: 402px;
    }
}

/* Focus Outline */

/* Focus Outline - fallback style */
.standard-outline-fallback *:focus {
    outline: solid 2px #000;
}

/* Focus Outline - header and footer */

footer.standard-outline-footer ul.pipe-separated-list li a {
    position: relative;
}

    header.standard-outline-header a:focus,
    footer.standard-outline-footer ul.pipe-separated-list li a:focus {
        outline: none;
    }

        header.standard-outline-header a:focus::after,
        footer.standard-outline-footer ul.pipe-separated-list li a:focus::before {
            content: '';
            height: calc(100% + 6px);
            width: calc(100% + 6px);
            position: absolute;
            top: -3px;
            left: -3px;
            display: block;
            box-shadow: 0 0 3px 1px #000, 0 0 3px 2px #000;
            z-index: 1;
            pointer-events: none;
        }

/* Focus Outline - others */

.standard-outline-form .lm-graphical-ctrl input[type="checkbox"]:focus,
.standard-outline-form .lm-graphical-ctrl input[type="radio"]:focus {
    outline: none;
}

    .standard-outline-form .lm-graphical-ctrl input[type="radio"]:focus ~ .lm-radio-element,
    .standard-outline-form .lm-graphical-ctrl input[type="checkbox"]:focus ~ .lm-check-element,
    .standard-outline-form select.lm-form-control:focus,
    .standard-outline-form input[type="text"].lm-form-control:focus,
    .standard-outline-form input[type="number"].lm-form-control:focus,
    .standard-outline-form input[type="tel"].lm-form-control:focus,
    .standard-outline-form input[type="email"].lm-form-control:focus,
    .standard-outline-form input[type="password"].lm-form-control:focus,
    .standard-outline-form textarea.lm-form-control:focus {
        outline: none;
        box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #000, 0 0 2px 5px #000;
    }

.standard-outline-relative a {
    position: relative;
}

    .standard-outline-relative a:focus,
    .standard-outline-relative button:focus,
    .standard-outline-relative input[type="submit"]:focus,
    .standard-outline-self-after:focus {
        outline: none;
    }

        .standard-outline-relative a:focus::after,
        .standard-outline-relative button:focus::after,
        .standard-outline-relative input[type="submit"]:focus::after,
        .standard-outline-self-after:focus::after {
            content: '';
            height: calc(100% + 6px);
            width: calc(100% + 6px);
            position: absolute;
            top: -3px;
            left: -3px;
            display: block;
            box-shadow: 0 0 3px 1px #000, 0 0 3px 2px #000;
            z-index: 1;
            pointer-events: none;
        }

        .standard-outline-relative .btn-secondary:focus::after {
            height: calc(100% + 10px);
            width: calc(100% + 10px);
            top: -5px;
            left: -5px;
        }

/* use standard-outline-direct for links that wrap. note that uncommenting the commented-out styles will reduce the outline "padding" to 1px to improve visibility of wrapped text while focused */
.standard-outline-direct a:focus {
    outline: none;
    box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #000, 0 0 2px 5px #000, inset 0 0 0 1em #fff;
    /*box-shadow: 0 0 2px 1px #fff, 0 0 0 1px #fff, 0 0 3px 3px #000, 0 0 2px 3px #000, inset 0 0 0 1em #fff;*/
}

.standard-outline-direct.bgTintSubtleGrey a:focus {
    outline: none;
    box-shadow: 0 0 0 3px #f7f7f7, 0 0 2px 3px #f7f7f7, 0 0 4px 5px #000, 0 0 2px 5px #000, inset 0 0 0 1em #f7f7f7;
    /*box-shadow: 0 0 2px 1px #f7f7f7, 0 0 0 1px #f7f7f7, 0 0 3px 3px #000, 0 0 2px 3px #000, inset 0 0 0 1em #f7f7f7;*/
}

/* use standard-outline-direct-no-inset instead of standard-outline-direct if the element's background-color is different from its container's background-color */
.standard-outline-direct-no-inset a:focus {
    outline: none;
    box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #000, 0 0 2px 5px #000;
}

.standard-outline-direct-no-inset.bgTintSubtleGrey a:focus {
    outline: none;
    box-shadow: 0 0 0px 3px #f7f7f7, 0 0 2px 3px #f7f7f7, 0 0 4px 5px #000, 0 0 2px 5px #000;
}

/* Focus Outline - IE-Only */

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    /* seems like IE rendering engine has an issue dealing with box-shadow directly applied to elements. use outline on default state to hide weird artifacts being left-out on blur */
    .standard-outline-direct a,
    .standard-outline-direct.bgTintSubtleGrey a {
        outline: 7px solid transparent;
    }

    /* IE is allowing focus on elements that technically shouldn't be focusable so hide the outline */
    html:not([tabindex]):focus,
    body:not([tabindex]):focus,
    main:not([tabindex]):focus,
    header:not([tabindex]):focus,
    footer:not([tabindex]):focus,
    div:not([tabindex]):focus,
    section:not([tabindex]):focus,
    article:not([tabindex]):focus,
    aside:not([tabindex]):focus,
    nav:not([tabindex]):focus,
    p:not([tabindex]):focus,
    span:not([tabindex]):focus,
    label:not([tabindex]):focus,
    fieldset:not([tabindex]):focus {
        outline: none;
    }

    
}

/* hide the native input clear and show password button of IE and Edge */
::-ms-clear,
::-ms-reveal {
    display: none;
}

/* Page-Specific */
#password-tooltip-container input:focus ~ * > .tooltip,
#password-tooltip-container input:hover ~ * > .tooltip {
    opacity: 0;
}

.btn-select.btn-default ul li.ciam-lm-font-med.selected:before {
    font-family: 'ciam-lm-icons' !important;
}

.form-default-theme .lm-checkbox-input ~ .lm-radio-element{
    border: 1px solid #949596;
}