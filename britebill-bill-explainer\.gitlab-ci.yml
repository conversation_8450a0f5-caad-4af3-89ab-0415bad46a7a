trigger_singleban:
  rules:
    - if: $CI_COMMIT_BRANCH == "Release"
      when: manual
    - when: always
  variables:
    JOBTRIGGER: $CI_PROJECT_NAME
    BRANCHTRIGGER: ${CI_COMMIT_BRANCH}
  trigger:
    project: uxp/mybell-singleban
    branch: ${CI_COMMIT_BRANCH}
trigger_job_mybell_singleban_bill_comparison:
  rules:
    - if: $CI_COMMIT_BRANCH == "Release"
      when: manual
    - when: always
  variables:
    JOBTRIGGER: $CI_PROJECT_NAME
    BRANCHTRIGGER: ${CI_COMMIT_BRANCH}
  trigger:
    project: uxp/mybell-singleban-bill-comparison
    branch: ${CI_COMMIT_BRANCH}
trigger_job_mybell_britebill_bill_explainer:
  rules:
    - if: $CI_COMMIT_BRANCH == "Release"
      when: manual
    - when: always
  variables:
    JOBTRIGGER: $CI_PROJECT_NAME
    BRANCHTRIGGER: ${CI_COMMIT_BRANCH}
  trigger:
    project: uxp/mybell-britebill-bill-explainer
    branch: ${CI_COMMIT_BRANCH}

