
@font-face {
    font-family: Gibson-Regular;
    src: url(../fonts/Gibson.ttf);
  }
@font-face {
    font-family: Gibson-Medium;
    src: url(../fonts/Gibson-SemiBold.ttf);
}
@font-face {
    font-family: FuturaLT-CondensedLight;
    src: url(../fonts/FuturaLT-CondensedLight.ttf);
   
}
@font-face {
    font-family: FuturaLT-CondensedBold;
    src: url(../fonts/FuturaLT-Bold.ttf);
   
}

.Gibson-Regular {
    font-family: Gibson-Regular, Arial, Helvetica, sans-serif;
    font-style: normal;
    font-stretch: normal;
  }
  
  .Gibson-Medium {
    font-family: Gibson-Medium, Arial, Helvetica, sans-serif;
    font-style: normal;
    font-stretch: normal;
  }
.FuturaLT-Condensed {
    font-family: FuturaLT-Condensed,"Trebuchet MS",Arial,Helvetica,sans-serif;
    font-style: normal;
    font-stretch: normal
}
.FuturaLT-CondensedLight {
    font-family: FuturaLT-CondensedLight,"Trebuchet MS",Arial,Helvetica,sans-serif;
    font-style: normal;
    font-stretch: normal
}
.FuturaLT-CondensedBold {
    font-family: FuturaLT-CondensedBold,"Trebuchet MS",Arial,Helvetica,sans-serif;
    font-style: normal;
    font-stretch: normal
}

html, body {
    margin: 0;
    height: 100%;
    position: relative;
   
    font-family: sans-serif;
}

body {
    background-color: #f4f4f4;
    font-size: 14px;
    line-height: 18px;
    color: #555;
}

p{font-family: "Open Sans Condensed", "Open Sans Condensed", Arial, Helvetica, sans-serif}
svg:not(:root) {
    overflow: hidden
}

figure {
    margin: 0
}

fieldset {
    border: 1px solid silver;
    margin: 0 2px;
    padding: .35em .625em .75em
}

legend {
    border: 0;
    padding: 0
}

button,
input,
select,
textarea {
    font-family: inherit;
    font-size: 100%;
    margin: 0
}

button,
input {
    line-height: normal
}

button,
select {
    text-transform: none
}

button,
html input[type=button],
input[type=reset],
input[type=submit] {
    -webkit-appearance: button;
    cursor: pointer
}

button[disabled],
html input[disabled] {
    cursor: default
}

input[type=checkbox],
input[type=radio] {
    box-sizing: border-box;
    padding: 0
}

input[type=search] {
    -webkit-appearance: textfield;
    -moz-box-sizing: content-box;
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}

input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-decoration {
    -webkit-appearance: none
}

button::-moz-focus-inner,
input::-moz-focus-inner {
    border: 0;
    padding: 0
}

textarea {
    overflow: auto;
    vertical-align: top
}

table {
    border-collapse: collapse;
    border-spacing: 0
}

.page-curl {
    background-image: url(../images/pctelecom/pcmobile-promo-tiles-bg-left.png), url(../images/pctelecom/pcmobile-promo-tiles-bg-right.png);
    background-position: 0 0, 100% 0;
    background-repeat: no-repeat
}

.clearfix:before,
.clearfix:after {
    display: table;
    content: ""
}

.clearfix:after {
    clear: both
}

.hide-text {
    background-color: transparent;
    border: 0;
    overflow: hidden
}

.hide-text:before {
    content: "";
    display: block;
    width: 0;
    height: 150%
}


.container_12 {
    margin-left: auto;
    margin-right: auto;
    position: relative;
    width: 100%;
    max-width: 1200px
}
.container_10 {
    margin-left: auto;
    margin-right: auto;
    position: relative;
    width: 100%;
    max-width: 1024px
}

.container_9 {
    margin-left: auto;
    margin-right: auto;
    position: relative;
    width: 700px
}

.grid_1,
.grid_2,
.grid_3,
.grid_4,
.grid_5,
.grid_6,
.grid_7,
.grid_8,
.grid_9,
.grid_10,
.grid_11,
.grid_12 {
    display: inline;
    float: left;
    position: relative;
    padding-left: 10px;
    padding-right: 10px
}

.push_1,
.pull_1,
.push_2,
.pull_2,
.push_3,
.pull_3,
.push_4,
.pull_4,
.push_5,
.pull_5,
.push_6,
.pull_6,
.push_7,
.pull_7,
.push_8,
.pull_8,
.push_9,
.pull_9,
.push_10,
.pull_10,
.push_11,
.pull_11 {
    position: relative
}

.alpha {
    padding-left: 0
}

.omega {
    padding-right: 0
}

.container_12 .grid_1,
.container_10 .grid_1,
.container_9 .grid_1 {
    width: 8.33333333%
}

.container_12 .grid_2,
.container_10 .grid_2,
.container_9 .grid_2 {
    width: 16.66666667%
}

.container_12 .grid_3,
.container_10 .grid_3,
.container_9 .grid_3 {
    width: 25%
}

.container_12 .grid_4,
.container_10 .grid_4,
.container_9 .grid_4 {
    width: 33.33333333%
}

.container_12 .grid_5,
.container_10 .grid_5,
.container_9 .grid_5 {
    width: 41.66666667%
}

.container_12 .grid_6,
.container_10 .grid_6,
.container_9 .grid_6 {
    width: 50%
}

.container_12 .grid_7,
.container_10 .grid_7,
.container_9 .grid_7 {
    width: 58.33333333%
}

.container_12 .grid_8,
.container_10 .grid_8,
.container_9 .grid_8 {
    width: 66.66666667%
}

.container_12 .grid_9,
.container_10 .grid_9,
.container_9 .grid_9 {
    width: 75%
}

.container_12 .grid_10,
.container_10 .grid_10,
.container_9 .grid_10 {
    width: 83.33333333%
}

.container_12 .grid_11,
.container_10 .grid_11,
.container_9 .grid_11 {
    width: 91.66666667%
}

.container_12 .grid_12,
.container_10 .grid_11,
.container_9 .grid_12 {
    width: 100%
}

.container_12 .prefix_1 {
    margin-left: 8.33333333%
}

.container_12 .prefix_2 {
    margin-left: 16.66666667%
}

.container_12 .prefix_3 {
    margin-left: 25%
}

.container_12 .prefix_4 {
    margin-left: 33.33333333%
}

.container_12 .prefix_5 {
    margin-left: 41.66666667%
}

.container_12 .prefix_6 {
    margin-left: 50%
}

.container_12 .prefix_7 {
    margin-left: 58.33333333%
}

.container_12 .prefix_8 {
    margin-left: 66.66666667%
}

.container_12 .prefix_9 {
    margin-left: 75%
}

.container_12 .prefix_10 {
    margin-left: 83.33333333%
}

.container_12 .prefix_11 {
    margin-left: 91.66666667%
}

.container_12 .suffix_1 {
    margin-right: 80px
}

.container_12 .suffix_2 {
    margin-right: 160px
}

.container_12 .suffix_3 {
    margin-right: 240px
}

.container_12 .suffix_4 {
    margin-right: 320px
}

.container_12 .suffix_5 {
    margin-right: 400px
}

.container_12 .suffix_6 {
    margin-right: 480px
}

.container_12 .suffix_7 {
    margin-right: 560px
}

.container_12 .suffix_8 {
    margin-right: 640px
}

.container_12 .suffix_9 {
    margin-right: 720px
}

.container_12 .suffix_10 {
    margin-right: 800px
}

.container_12 .suffix_11 {
    margin-right: 880px
}

.container_12 .push_1 {
    left: 8.33333333%
}

.container_12 .push_2 {
    left: 16.66666667%
}

.container_12 .push_3 {
    left: 25%
}

.container_12 .push_4 {
    left: 33.33333333%
}

.container_12 .push_5 {
    left: 41.66666667%
}

.container_12 .push_6 {
    left: 50%
}

.container_12 .push_7 {
    left: 58.33333333%
}

.container_12 .push_8 {
    left: 66.66666667%
}

.container_12 .push_9 {
    left: 75%
}

.container_12 .push_10 {
    left: 83.33333333%
}

.container_12 .push_11 {
    left: 91.66666667%
}

.container_12 .pull_1 {
    left: -8.33333333%
}

.container_12 .pull_2 {
    left: -16.66666667%
}

.container_12 .pull_3 {
    left: -25%
}

.container_12 .pull_4 {
    left: -33.33333333%
}

.container_12 .pull_5 {
    left: -41.66666667%
}

.container_12 .pull_6 {
    left: -50%
}

.container_12 .pull_7 {
    left: -58.33333333%
}

.container_12 .pull_8 {
    left: -66.66666667%
}

.container_12 .pull_9 {
    left: -75%
}

.container_12 .pull_10 {
    left: -83.33333333%
}

.container_12 .pull_11 {
    left: -91.66666667%
}

.clear {
    clear: both;
    display: block;
    overflow: hidden;
    visibility: hidden;
    width: 0;
    height: 0
}

.clearfix:before,
.container_12:before,
.clearfix:after,
.container_12:after {
    display: table;
    content: ""
}

.clearfix:after,
.container_12:after {
    clear: both
}

.simplified-header {
    background: #fff none repeat scroll 0 0;
    box-shadow: 0 0 50px 0 rgba(0,0,0,0.2);
    height: 75px;
    position: relative;
    text-align: center;
    z-index: 50;
}

.simplified-header-area-title {
    color: #000;
    font-size: 28px;
    font-weight: 500;
    line-height: 26px;
    margin: 0 auto;
    width: 50%;
}

@media screen and (max-width:60em) {
    
    .container_12,
    .container_10,
    .container_9 {
        width: 100%
    }
}

@media screen and (max-width:47.9375em) {
    .container_12 [class*=grid_],
    .container_10 [class*=grid_],
    .container_9 [class*=grid_] {
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        display: block;
        float: none;
        clear: both;
        width: 100%
    }
}

input[type=submit],
input[type=button],
input[type=reset],
button,
.button,
a:link.button,
a:visited.button {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: 0;
    padding: .5rem 1.25rem;
    display: inline-block;
    background: #e41c11;
    color: #fff;
    line-height: 31px;
    text-decoration: none;
    text-align: center;
   font-family:  FuturaLT-CondensedLight,"Trebuchet MS",Arial,Helvetica,sans-serif;
}

.wf-active input[type=submit],
.wf-active input[type=button],
.wf-active input[type=reset],
.wf-active button,
.wf-active .button,
.wf-active a:link.button,
.wf-active a:visited.button {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif
}

input[type=submit] .subtext,
input[type=button] .subtext,
input[type=reset] .subtext,
button .subtext,
.button .subtext,
a:link.button .subtext,
a:visited.button .subtext {
    display: block;
    font-size: .417em;
    line-height: 1.5;
    text-transform: none
}

.wf-active input[type=submit] .subtext,
.wf-active input[type=button] .subtext,
.wf-active input[type=reset] .subtext,
.wf-active button .subtext,
.wf-active .button .subtext,
.wf-active a:link.button .subtext,
.wf-active a:visited.button .subtext {
    font-family: "Open Sans Condensed", "Open Sans Condensed", Arial, Helvetica, sans-serif
}

.ch input[type=submit],
.ch input[type=button],
.ch input[type=reset],
.ch button,
.ch .button,
.ch a:link.button,
.ch a:visited.button {
    text-decoration: underline
}

input[type=submit]:hover,
input[type=button]:hover,
input[type=reset]:hover,
button:hover,
.button:hover,
a:link.button:hover,
a:visited.button:hover,
input[type=submit]:focus,
input[type=button]:focus,
input[type=reset]:focus,
button:focus,
.button:focus,
a:link.button:focus,
a:visited.button:focus {
    background: #e41c11
}

input[type=submit].button-black,
input[type=button].button-black,
input[type=reset].button-black,
button.button-black,
.button.button-black,
a:link.button.button-black,
a:visited.button.button-black {
    background: #000
}

.ch input[type=submit].button-black,
.ch input[type=button].button-black,
.ch input[type=reset].button-black,
.ch button.button-black,
.ch .button.button-black,
.ch a:link.button.button-black,
.ch a:visited.button.button-black {
    background: #000
}

input[type=submit].alt,
input[type=button].alt,
input[type=reset].alt,
button.alt,
.button.alt,
a:link.button.alt,
a:visited.button.alt {
    background-color: #bbb;
    background-repeat: repeat-x;
    background-image: -webkit-linear-gradient(top, #eee, #bbb);
    background-image: linear-gradient(to bottom, #eee, #bbb);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#ffeeeeee', endColorstr='#ffbbbbbb', GradientType=0);
    color: #000
}

input[type=submit].white,
input[type=button].white,
input[type=reset].white,
button.white,
.button.white,
a:link.button.white,
a:visited.button.white {
    color: #e41c11;
    background-color: #fff
}

label.button input {
    width: 1px;
    height: 1px;
    position: absolute!important;
    overflow: hidden;
    clip: rect(1px, 1px, 1px, 1px)
}

.margin-10-top{margin-top: 10px;}

.icon {
    display: inline-block;
    vertical-align: top;
   
}
.icon-close-modal{ -ms-transform: rotate(45deg); /* IE 9 */
    transform: rotate(45deg);}

.icon.icon-grey-arrow-up {
    background-position: -240px 0
}

.icon.icon-grey-bubble {
    background-position: -265px 0
}

.icon.icon-grey-face {
    background-position: -190px 0
}

.icon.icon-grey-facebook {
    background-position: -315px 0
}

.icon.icon-grey-pin {
    background-position: -165px 0
}

.icon.icon-grey-power {
    background-position: -215px 0
}

.icon.icon-white-arrow-up {
    background-position: -240px -25px
}

.icon.icon-white-bubble {
    background-position: -265px -25px
}

.icon.icon-white-face {
    background-position: -190px -25px
}

.icon.icon-white-facebook {
    background-position: -315px -25px
}

.icon.icon-white-pin {
    background-position: -165px -25px
}

.icon.icon-white-power {
    background-position: -215px -25px
}

.icon.icon-user-guide {
    width: 26px;
    height: 26px;
    background-position: 0 -75px
}

.icon.icon-checked {
    width: 16px;
    height: 16px;
    background-position: -294px -30px
}

[class^=icon-btn-],
[class*=" icon-btn-"] {
    margin: -1px 5px auto -5px;
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    vertical-align: middle
}

.icon-btn-add {
    background-position: -48px -50px
}

.icon-btn-compare {
    width: 11px;
    margin-top: -2px;
    background-position: -112px -50px
}

.inset-active .icon-btn-compare {
    background-position: -96px -50px
}

.icon-btn-details {
    background-position: -80px -50px
}

.inset-active .icon-btn-details {
    background-position: -64px -50px
}

.icon-btn-email {
    margin-top: 0;
    background-position: -16px -50px
}

.icon-btn-mobile {
    width: 11px;
    background-position: 0 -50px
}

.icon-btn-print {
    background-position: -32px -50px
}

[class^=icon-support-],
[class*=" icon-support-"] {
    border-radius: 3px;
    display: inline-block;
    width: 45px;
    height: 45px;
    background: #e41c11 url(../images/pctelecom/pcmobile-sprite.svg) no-repeat
}

.icon-support-calendar {
    background-position: 0 -100px
}

.icon-support-selfserve {
    background-position: -45px -100px
}

.icon-support-bill {
    background-position: -90px -100px
}

.icon-support-globe {
    background-position: -135px -100px
}

.icon-support-services {
    background-position: -180px -100px
}

.icon-support-guide {
    background-position: -225px -100px
}

[class^=icon-nav-],
[class*=" icon-nav-"] {
    width: 25px;
    height: 25px;
    vertical-align: text-bottom
}

.icon-nav-monthly {
    background-position: -214px -50px
}

.icon-nav-prepaid {
    background-position: -239px -50px
}

.icon-nav-login {
    background-position: -264px -50px
}

.icon-nav-register {
    background-position: -289px -50px
}

.icon-nav-support {
    background-position: -314px -50px
}

.icon-nav-about {
    background-position: -339px -50px
}

.icon-nav-offers {
    background-position: -364px -50px
}

.icon-nav-storelocator {
    background-position: -214px -75px
}

.icon-nav-contact {
    background-position: -239px -75px
}

.icon-nav-phones {
    background-position: -264px -75px
}

.icon-nav-plans {
    background-position: -289px -75px
}

.icon-nav-wizard {
    background-position: -314px -75px
}

.icon-nav-coverage,
.icon-nav-longdistance {
    background-position: -339px -75px
}

.icon-nav-activate {
    background-position: -189px -50px
}

.icon-nav-topup {
    background-position: -189px -75px
}

.icon-nav-downloads {
    background-position: -364px -75px
}

#colorbox,
#cboxOverlay,
#cboxWrapper {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999;
    overflow-x: hidden
}

#cboxOverlay {
    position: fixed;
    width: 100%;
    height: 100%
}

#cboxMiddleLeft,
#cboxBottomLeft {
    clear: left
}

#cboxContent {
    position: relative
}

#cboxLoadedContent {
    overflow: auto
}

#cboxTitle {
    margin: 0
}

#cboxLoadingOverlay,
#cboxLoadingGraphic {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

#cboxPrevious,
#cboxNext,
#cboxClose,
#cboxSlideshow {
    cursor: pointer
}

.cboxPhoto {
    float: left;
    margin: auto;
    border: 0;
    display: block
}

.cboxIframe {
    width: 100%;
    height: 100%;
    display: block;
    border: 0
}

#colorbox * {
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box
}

#cboxOverlay {
    background-color: rgba(0, 0, 0, .5)
}

.no-rgba #cboxOverlay {
    background: url(../images/pctelecom/pcmobile-cbox-overlay.png) repeat
}

#colorbox {
    -webkit-box-shadow: 5px 10px 20px rgba(0, 0, 0, .4);
    box-shadow: 5px 10px 20px rgba(0, 0, 0, .4)
}

.no-boxshadow #colorbox {
    border: 1px solid #333
}

#colorbox:focus {
    outline: 0
}

#cboxTopLeft {
    width: 1px;
    height: 1px
}

#cboxTopRight {
    width: 1px;
    height: 1px
}

#cboxBottomLeft {
    width: 1px;
    height: 1px
}

#cboxBottomRight {
    width: 1px;
    height: 1px
}

#cboxMiddleLeft {
    width: 1px
}

#cboxMiddleRight {
    width: 1px
}

#cboxTopCenter {
    height: 1px
}

#cboxBottomCenter {
    height: 1px
}

#cboxContent {
    overflow-x: hidden;
    background: #fff
}

.cboxIframe {
    background: #fff
}

#cboxError {
    padding: 32px;
    border: 1px solid #ccc
}

#cboxLoadedContent {
    padding: 32px
}

#cboxTitle {
    display: none!important
}

#cboxCurrent {
    display: none!important
}

#cboxSlideshow {
    display: none!important
}

#cboxPrevious {
    display: none!important
}

#cboxNext {
    display: none!important
}

#cboxLoadingOverlay {
    padding: 32px;
    background: #fff url(../images/pctelecom/pcmobile-cbox-loading.gif) no-repeat 5px 5px
}

#cboxClose {
    display: block;
    position: absolute;
    top: 26px;
    right: 26px;
    width: 24px;
    height: 24px;
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 1.25rem;
    line-height: 1em;
    background-color: #555;
    background-repeat: repeat-x;
    background-image: -webkit-linear-gradient(top, #999, #555);
    background-image: linear-gradient(to bottom, #999, #555);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#ff999999', endColorstr='#ff555555', GradientType=0);
    -webkit-box-shadow: 1px 1px 2px #999;
    box-shadow: 1px 1px 2px #999;
    cursor: pointer
}

.cboxIE #cboxTopLeft,
.cboxIE #cboxTopCenter,
.cboxIE #cboxTopRight,
.cboxIE #cboxBottomLeft,
.cboxIE #cboxBottomCenter,
.cboxIE #cboxBottomRight,
.cboxIE #cboxMiddleLeft,
.cboxIE #cboxMiddleRight {
    -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#00FFFFFF,endColorstr=#00FFFFFF)"
}

@media screen and (max-width:56.625em) {
    #colorbox,
    #cboxOverlay,
    #cboxWrapper {
        overflow: visible
    }
    #cboxOverlay {
        background: 0 0
    }
    #colorbox {
        width: 82%!important;
        height: auto!important;
        top: 60px!important;
        left: 18%!important;
        bottom: 0!important;
        background: #fff;
        -webkit-box-shadow: 0 0 12px rgba(0, 0, 0, .4);
        box-shadow: 0 0 12px rgba(0, 0, 0, .4)
    }
    #cboxWrapper {
        width: 100%!important;
        height: auto!important;
        transition: left 500ms ease
    }
    #cboxContent {
        width: 100%!important;
        height: auto!important
    }
    #cboxLoadedContent {
        width: auto!important;
        height: auto!important;
        padding: 12px
    }
    #cboxTopLeft,
    #cboxTopRight,
    #cboxBottomLeft,
    #cboxBottomRight,
    #cboxMiddleLeft,
    #cboxMiddleRight,
    #cboxTopCenter,
    #cboxBottomCenter {
        display: none!important
    }
    #cboxClose {
        top: 10px;
        right: 10px;
        width: 27px;
        height: 27px;
        font-size: 1.4rem;
        line-height: .85em;
        border-radius: 14px
    }
}

body div.pageContainer div#interiorPageContent {
    background: transparent url() repeat-x 0 0
}

body div.pageContainer div#fullWidthGreyHeadingContentContainer {
    width: 100%;
    background: transparent url() repeat-x 0 0
}

body div.pageContainer div#fullWidthNoHeadingContentContainer {
    width: 100%;
    background: #fff url(../images/pctelecom/bg_interior_contentarea.png) no-repeat 100% 0;
    padding: 0;
    position: relative;
    z-index: 30
}

body div.pageContainer div#fullWidthNoHeadingContentContainer div#interiorPageContentWrapper {
    background: transparent url(../images/pctelecom/bg_interior_contentarea_left.png) no-repeat 0 0
}

body div.pageContainer div#interiorThreeColumnAdTilesContainer {
    width: 100%
}

body div.pageContainer div#interiorThreeColumnAdTiles {
    width: 960px;
    margin: 0 auto
}

body div.pageContainer div#interiorThreeColumnAdTiles div.interiorThreeColumnAdTile {
    display: inline;
    float: left;
    width: 318px;
    height: 130px
}

div.leftColumnNavigation {
    width: 220px;
    margin: 0 10px;
    display: inline;
    float: left
}

div.interiorPageRightMainColumn {
    width: 700px;
    margin: 0 10px;
    display: inline;
    float: left
}

div.leftColumnPromo {
    margin: 0 0 20px
}

div.promoContainer {
    margin: 0 0 10px;
    padding: 0 0 10px
}

div#rightColAdContainer {
    padding-top: 65px
}

p.required {
    color: #959595
}

p.required span,
span.required {
    color: #e41c11
}

div.interiorPageRightMainColumn div#genericContentContainer h2 {
    font-size: 200%;
    font-weight: 400;
    line-height: 1em;
    padding: 0 0 5px;
    margin: 10px 0 15px;
    border-bottom: 1px solid #cecece
}

div.interiorPageRightMainColumn div#genericContentContainer p {
    font-size: 105%;
    margin: 0 0 15px;
    line-height: 1.2em
}

div.interiorPageRightMainColumn div#genericContentContainer ul,
div.interiorPageRightMainColumn div#genericContentContainer ol {
    padding-left: 15px;
    margin-left: 15px;
    margin-bottom: 15px
}

div.interiorPageRightMainColumn div#genericContentContainer ul li {
    list-style: disc
}

div.interiorPageRightMainColumn div#genericContentContainer ol li {
    list-style: decimal
}

div.interiorPageRightMainColumn div#genericContentContainer div.leftColumn {
    float: left;
    display: inline;
    width: 50%
}

div.interiorPageRightMainColumn div#genericContentContainer div.rightColumn {
    float: left;
    display: inline;
    width: 50%
}

div#paginationFilters {
    margin: 20px 0 0 10px;
    position: relative;
    height: 30px
}

div#paginationFilters span.results {
    position: absolute;
    top: 0;
    left: 0
}

div#paginationFilters span#itemsPerPage {
    position: absolute;
    top: 0;
    left: 30%
}

div#paginationFilters span#itemsPerPage span,
div#paginationFilters span#itemsPerPage a {
    display: inline;
    float: left;
    margin-right: 3px;
    height: 22px;
    line-height: 22px
}

div#paginationFilters span#itemsPerPage span.current,
div#paginationFilters span#itemsPerPage a {
    width: 22px;
    text-align: center;
    border: 1px solid #ebebeb
}

div#paginationFilters span#itemsPerPage span.current {
    background-color: #ebebeb
}

div#paginationFilters span#itemsSort {
    position: absolute;
    top: 0;
    right: 7px
}

div#paginationFilters span#itemsSort span,
div#paginationFilters span#itemsSort a {
    display: inline;
    float: left;
    margin-right: 3px;
    height: 22px;
    line-height: 22px
}

div#paginationFilters span#itemsSort span.current,
div#paginationFilters span#itemsSort a {
    padding: 0 5px;
    text-align: center;
    border: 1px solid #ebebeb
}

div#paginationFilters span#itemsSort span.current {
    background-color: #ebebeb
}

div#paginationNavigation {
    margin: 10px 0 20px;
    position: relative;
    height: 30px;
    border-top: 1px solid #ebebeb
}

div#paginationNavigation ul {
    position: absolute;
    top: 8px;
    right: 0
}

div#paginationNavigation ul li {
    display: inline;
    float: left;
    margin: 0 8px 0 0;
    height: 22px;
    line-height: 22px
}

div#paginationNavigation ul li.box {
    width: 22px;
    border: 1px solid #ebebeb;
    text-align: center
}

div#paginationNavigation ul li.current {
    background-color: #ebebeb
}

div#resultListViewControls {
    height: 53px;
    background: transparent url(../images/pctelecom/bg_searchgridlistnav_pc.gif) no-repeat 0 0;
    position: relative;
    margin: 0
}

div#resultListViewControls a {
    position: absolute;
    bottom: 0;
    display: block;
    width: 94px;
    height: 43px
}

div#resultListViewControls a span {
    position: absolute;
    text-indent: -3000px
}

div#resultListViewControls a#gridview {
    background: transparent url(../images/pctelecom/btn_viewgrid_off_pc.gif) no-repeat 0 0;
    left: 8px
}

div#resultListViewControls a#gridview.active {
    background: transparent url(../images/pctelecom/btn_viewgrid_on_pc.gif) no-repeat 0 0
}

div#resultListViewControls a#listview {
    background: transparent url(../images/pctelecom/btn_viewlist_off_pc.gif) no-repeat 0 0;
    left: 105px
}

div#resultListViewControls a#listview.active {
    background: transparent url(../images/pctelecom/btn_viewlist_on_pc.gif) no-repeat 0 0
}

.threeQuarterPromoSlot img,
.threeQuarterPromoSlot object,
.threeQuarterPromoSlot embed {
    margin: 15px 0 10px
}

#fullWidthNoHeadingContentContainer h3 {
    font-size: 175%;
    font-weight: 400
}

div#fullWidthNoHeadingContentContainer h3.cta {
    margin: 0 0 8px 10px
}

#fullWidthNoHeadingContentContainer #genericContentContainer h3 {
    font-size: 150%;
    margin: 0 0 10px
}

.tagContent {
    color: #959595;
    margin: 0 0 0 10px;
    line-height: 1.2em
}

#productIntro .tagContent {
    display: block;
    margin: 10px 0 0
}

a.arrow {
    display: block;
    padding: 0 0 0 8px;
    margin: 0;
    background: transparent url(../images/pctelecom/arrow_red.gif) no-repeat 0 .3em
}

a.arrowExpandedDown {
    display: block;
    padding: 0 0 0 12px;
    margin: 0;
    background: transparent url(../images/pctelecom/arrow_red_down_pc.gif) no-repeat 0 .5em
}

a.arrowExpandedUp {
    display: block;
    padding: 0 0 0 12px;
    margin: 0;
    background: transparent url(../images/pctelecom/arrow_red_up_pc.gif) no-repeat 0 .5em
}

div#interiorGenericPageContent div.introPromoContainer {
    margin: 10px 0
}

div#interiorGenericPageContent div.interiorCTAContainer {
    margin: 10px 0 0
}

div#storeLocatorPageIntroContainer h2,
div#interiorGenericPageContent h2 {
    font-size: 190%;
    line-height: 1em;
    padding: 0 0 5px;
    margin: 15px 0 5px;
    border-bottom: 1px solid #cecece
}

div#interiorGenericPageContent h2 {
    margin-bottom: 10px;
    margin-left: 0
}

div#storeLocatorPageIntroContainer p,
div#interiorGenericPageContent p {
    margin-bottom: 15px
}

div#interiorGenericPageContent h4,
div#interiorGenericPageContent p {
    font-size: 105%;
    margin-left: 0
}

div.searchResultsItemContent a.storeTitle {
    font-weight: 700
}

div.searchResultsItemContent p.phoneNumber {
    border-bottom: 1px solid #ececec
}

div#interiorGenericPageContent div#storeLocatorPageMainContent p {
    font-size: 100%;
    margin-bottom: 0
}

div.error label {
    color: #ee1c24
}

span.errorMessage {
    color: #e41c11;
    display: block;
    margin: 0;
    font-weight: 700
}

div#memberForgotPassword {
    padding: 10px 0 30px
}

div#memberForgotPassword h3 {
    font-size: 180%;
    text-transform: uppercase;
    color: #e31c2a;
    margin: 30px 0 10px
}

div#memberForgotPassword p {
    margin: 0 0 15px;
    font-size: 105%
}

div#memberForgotPassword form div.row {
    clear: left;
    height: 4em
}

div#memberForgotPassword form div.row label,
div#memberForgotPassword form div.row span.label {
    display: inline;
    float: left;
    width: 180px;
    font-size: 115%
}

div#memberForgotPassword form div.row input#tb_pc_email_username {
    display: inline;
    float: left;
    padding: 5px;
    border: 1px solid #cecece;
    width: 295px
}

div#memberResetPassword {
    padding: 10px 0 30px
}

div#memberResetPassword h3 {
    font-size: 180%;
    text-transform: uppercase;
    color: #e31c2a;
    margin: 30px 0 10px
}

div#memberResetPassword p {
    margin: 0 0 15px;
    font-size: 105%
}

div#memberResetPassword form div.row {
    clear: left;
    height: 4em
}

div#memberResetPassword form div.row label,
div#memberResetPassword form div.row span.label {
    display: inline;
    float: left;
    width: 180px;
    font-size: 115%
}

div#memberResetPassword form div.row input#pw_password,
div#memberResetPassword form div.row input#pw_passwordconfirm {
    display: inline;
    float: left;
    padding: 5px;
    border: 1px solid #cecece;
    width: 295px
}

div#memberResetPassword div.row div.instructional {
    display: none;
    float: left;
    width: 200px;
    margin-left: 10px;
    color: #959595
}

div#memberResetPassword a.btnSave {
    display: inline;
    float: left;
    height: 25px;
    line-height: 25px;
    color: #fff!important;
    background: transparent url(../images/pctelecom/bg_btn_darkgrey_right.gif) no-repeat 100% -25px;
    padding: 0 18px 0 0
}

div#memberResetPassword a.btnSave span {
    display: inline;
    float: left;
    height: 25px;
    line-height: 25px;
    background: transparent url(../images/pctelecom/bg_btn_darkgrey_left.gif) no-repeat 0 -25px;
    padding: 0 10px;
    font-weight: 700;
    cursor: pointer
}

div#memberResetPassword a.btnSave:hover {
    text-decoration: none!important;
    background-position: 100% 0
}

div#memberResetPassword a.btnSave:hover span {
    background-position: 0 0
}

div#genericLandingPageStaticContent h2 {
    font-size: 180%;
    font-weight: 400;
    line-height: 1em;
    margin: 10px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid #ccc
}

div#genericLandingPageStaticContent div.grid_6 h3 {
    font-size: 150%
}

div#genericLandingPageStaticContent div.grid_6 p {
    margin: 0 0 15px;
    font-size: 105%;
    line-height: 1.2em
}

div#contestCTASlotContainer {
    margin-top: 60px
}

div#contestCTASlotContainer img {
    margin: 0!important
}

div#interiorGenericPageContent table.naturalValue {
    table-layout: fixed;
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0
}

div#interiorGenericPageContent table.naturalValue caption {
    text-align: left;
    font-size: 170%;
    font-weight: 700;
    margin-left: 5px
}

div#interiorGenericPageContent table.naturalValue col {
    width: 23%
}

div#interiorGenericPageContent table.naturalValue col#store {
    width: 30%
}

div#interiorGenericPageContent table.naturalValue th {
    text-align: left;
    padding: 20px 0 5px 5px;
    border-top: 1px solid #ebebeb;
    font-size: 105%
}

div#interiorGenericPageContent table.naturalValue td {
    font-size: 105%;
    padding: 4px 0 4px 5px;
    line-height: 1.2em
}

div#interiorGenericPageContent table.naturalValue tr.odd td {
    background-color: #ebebeb
}

p.introText {
    font-size: 140%;
    line-height: 1.2em;
    padding: 0 20px 0 30px;
    margin: 10px 0
}

a.btnContactCustomerService,
a.btnDelete,
a.btnGoSearch,
a.btnSaveChanges,
a.btnLogin,
a.btnSignup,
a.btnSearch,
a.btnLoginRegister,
a.btnSubmit,
a.btnCreateNewList,
a.btnSend,
a.btnEnglish,
a.btnFrench,
a.btnLearnMore {
    display: block;
    position: relative;
    outline: 0
}

a.btnDeleteItem,
a.btnDeleteAllItemsFromRecipe {
    display: block;
    position: relative;
    outline: 0
}

a.btnContactCustomerService span,
a.btnDelete span,
a.btnGoSearch span,
a.btnSaveChanges span,
a.btnLogin span,
a.btnSignup span,
a.btnSearch span,
a.btnLoginRegister span,
a.btnSubmit span,
a.btnCreateNewList span,
a.btnSend span,
a.btnEnglish span,
a.btnFrench span,
a.btnLearnMore span,
a.btnDeleteItem span,
a.btnDeleteAllItemsFromRecipe span {
    position: absolute;
    left: -3000px
}

@media projection,
screen {
    .tabs-hide {
        display: none
    }
}

@media print {
    .tabs-nav {
        display: none
    }
}

.tabs-container {
    border: 1px solid #ccc;
    padding: 1em 0;
    background: #fff
}

.tabs-loading em {
    padding: 0 0 0 20px
}

div#overlayWidgetTabNavigationContainer {
    border-top: 1px solid #ebebeb;
    padding-top: 57px;
    background: transparent url(../images/pctelecom/bg_storelocator_header.gif) no-repeat 0 1px;
    position: relative
}

div#overlayWidgetTabNavigationContainer ul.tabs-nav {
    position: absolute;
    top: 9px;
    left: 9px
}

div#overlayWidgetTabNavigationContainer ul.tabs-nav li {
    float: left;
    height: 43px;
    margin: 0;
    position: relative;
    width: auto;
    background: transparent url(../images/pctelecom/bg_tabnav_right_off_pc.gif) no-repeat 100% 0;
    border: 0
}

div#overlayWidgetTabNavigationContainer ul.tabs-nav li.tabs-selected {
    background: transparent url(../images/pctelecom/bg_tabnav_right_on_pc.gif) no-repeat 100% 0
}

div#overlayWidgetTabNavigationContainer ul.tabs-nav li a {
    display: inline;
    float: left;
    height: 38px;
    background: transparent url(../images/pctelecom/bg_tabnav_left_off_pc.gif) no-repeat 0 0;
    color: #000;
    padding-left: 10px;
    font-size: 90%;
    padding-top: 5px
}

div#overlayWidgetTabNavigationContainer ul.tabs-nav li.tabs-selected a {
    background: transparent url(../images/pctelecom/bg_tabnav_left_on_pc.gif) no-repeat 0 0
}

div#overlayWidgetTabNavigationContainer div.tabs-container {
    padding: 0 8px 8px;
    background-color: #fff;
    border: 0
}

div#overlayWidgetTabNavigationContainer div.tabs-container div.row label span {
    color: #e41c11
}

div#overlayWidgetTabNavigationContainer div.tabs-container div.row select {
    background-color: #fff
}

div#overlayWidgetTabNavigationContainer div.tabs-container div.row input.large {
    width: 300px;
    padding: 5px
}

div#overlayWidgetTabNavigationContainer div.tabs-container div.row input.small {
    width: 90px;
    padding: 5px
}

div#reviewRecipeWidget,
div#shoppingListsWidget,
div#memberProfileTourOverlay,
div#deleteAccountWidget,
div#memberPhotoUploadWidget,
div#storeDetailWidget,
div#shoppingListsOverlayWidget,
div#notLoggedInWidget,
div#contactCustomerServiceWidget,
div#springboardWidget,
div#contestRulesWidget,
div#TB_ajaxContent div#productDetail,
div#genericMessageOverlay,
div#heroAdOverlay {
    padding: 10px 20px
}

div#genericMessageOverlay h2 {
    font-size: 130%;
    padding: 0 0 10px;
    margin: 0
}

div#genericMessageOverlay p {
    margin: 0 0 10px
}

div#deleteAccountWidget fieldset div.row {
    margin: 0 0 15px
}

div#deleteAccountWidget fieldset div.row label,
div#deleteAccountWidget fieldset div.row span.label {
    display: block;
    font-size: 115%;
    margin: 0 0 5px
}

div#deleteAccountWidget fieldset div.row label span,
div#deleteAccountWidget fieldset div.row span.label span {
    color: #e41c11
}

div#deleteAccountWidget fieldset div#otherReason {
    display: none
}

div#deleteAccountWidget fieldset div.row select {
    width: 500px;
    padding: 5px;
    border: 1px solid #cecece
}

div#deleteAccountWidget fieldset div.row input#pw_delete_account_password {
    width: 325px;
    padding: 5px;
    border: 1px solid #cecece
}

div#deleteAccountWidget fieldset div.row input#tb_delete_account_reason_other {
    width: 325px;
    padding: 5px;
    border: 1px solid #cecece
}

div#deleteAccountWidget a.btnDelete {
    display: inline;
    float: left;
    margin: -5px 10px 0 0
}

div#contestRulesWidget h2 {
    font-size: 140%
}

div#contestRulesWidget h3 {
    font-size: 120%
}

div#contestRulesWidget li {
    margin: 0 0 10px 15px;
    list-style: decimal
}

div#contestRulesWidget ul li {
    list-style: disc
}

div#notLoggedInWidget p {
    margin: 0 0 10px
}

div#notLoggedInWidget a.btnLoginRegister {
    display: inline;
    float: left;
    margin: -5px 10px 0 0
}

div#contactCustomerServiceWidget fieldset div.row {
    margin: 0 0 15px
}

div#contactCustomerServiceWidget fieldset div.row label {
    display: block;
    font-size: 115%;
    margin: 0 0 5px
}

div#contactCustomerServiceWidget fieldset div.row label.inline {
    display: inline
}

div#contactCustomerServiceWidget fieldset div.row select {
    width: 325px;
    padding: 5px;
    border: 1px solid #cecece;
    background-color: #ebe9e3
}

div#contactCustomerServiceWidget fieldset div.row input {
    width: 250px;
    padding: 5px
}

div#contactCustomerServiceWidget fieldset div.row input#tb_contactreason_other {
    display: none
}

div#contactCustomerServiceWidget fieldset div.row textarea {
    width: 250px
}

div#contactCustomerServiceWidget fieldset a.btnSend {
    margin: -5px 10px 0 0;
    display: inline;
    float: left
}

div#springboardWidget fieldset div.row div.instructional {
    display: none;
    float: left;
    width: 200px;
    margin-left: 10px;
    color: #959595
}

div#springboardWidget fieldset div.row {
    margin: 0 0 15px;
    padding-bottom: 25px;
    clear: left
}

div#springboardWidget fieldset div.row label {
    display: block;
    font-size: 115%;
    margin: 0 0 5px
}

div#springboardWidget fieldset div.row label.inline {
    display: inline
}

div#springboardWidget fieldset div.row select {
    width: 325px;
    padding: 5px;
    border: 1px solid #cecece;
    background-color: #ebe9e3
}

div#springboardWidget fieldset div.row input {
    display: inline;
    float: left;
    width: 250px;
    padding: 5px
}

div#springboardWidget fieldset a.btnSubmit {
    margin: -5px 10px 0 0;
    display: inline;
    float: left
}

div#heroAdOverlay h2 {
    font-size: 140%;
    margin: 0 0 10px
}

div#heroAdOverlay p {
    margin: 0 0 10px;
    padding: 0
}

div#heroAdOverlay li {
    margin: 0 0 10px 20px;
    list-style: decimal
}

div#heroAdOverlay ul li {
    list-style: disc;
    margin-left: 15px
}

div#heroAdOverlay table {
    border-collapse: collapse
}

div#genericMessageOverlay {
    background-color: #fff
}

div#genericMessageOverlay h3 {
    font-size: 180%;
    text-transform: uppercase;
    color: #e31c2a;
    margin: 0 0 10px;
    line-height: 1em
}

div#genericMessageOverlay p {
    margin: 0 0 10px
}

div#genericMessageOverlay ol,
div#genericMessageOverlay ul {
    margin: 0 0 0 15px;
    padding: 0 0 0 10px
}

div#genericMessageOverlay ol li,
div#genericMessageOverlay ul li {
    margin: 0 0 5px
}

div#genericMessageOverlay ol li {
    list-style: decimal
}

div#genericMessageOverlay ul li {
    list-style: disc
}

div#globalSearchResultsCategorized ul li {
    padding: 5px 0;
    border-bottom: 1px solid #ebebeb
}

div#globalSearchResultsCategorized div.searchCategoryHeader {
    position: relative;
    height: 30px;
    background-color: #ebebeb;
    margin: 15px 0 0
}

div#globalSearchResultsCategorized div.searchCategoryHeader h3 {
    font-size: 115%;
    font-weight: 700;
    margin: 0 0 0 8px;
    padding: 0;
    line-height: 30px
}

div#globalSearchResultsCategorized div.searchCategoryHeader span.results {
    position: absolute;
    right: 10px;
    top: 0;
    line-height: 30px;
    height: 30px
}

div.collapsibleItems div.item {
    overflow: hidden
}

form#activateOnline label,
form#activateOnline span.label {
    display: inline;
    float: left;
    width: 200px
}

form#activateOnline label.inline {
    float: none;
    width: auto
}

form#activateOnline label.block {
    float: none;
    display: block;
    width: auto;
    margin: 0 0 10px
}

div#memberSignup {
    padding: 0 0 50px
}

div#memberSignup h3 {
    font-size: 105%;
    text-transform: uppercase;
    font-weight: 700;
    color: #000;
    margin: 0!important;
    padding-top: 20px;
    border-top: 1px solid #cecece;
    line-height: 1em!important;
    height: 1em!important
}

div#memberSignup form label,
div#memberSignup form span.label {
    display: inline;
    float: left;
    width: 180px
}

div#memberSignup form label.inline {
    float: none;
    width: auto
}

div#memberSignup div.row {
    padding: 0 0 10px
}

div#memberSignup div.row label span {
    color: #e31c2a
}

div#memberSignup div.row div.instructional {
    display: none
}

div.mainTabbedContainer .tabs-container {
    background-color: #fff;
    border: 0
}

.highContrast div#productLandingInfo ul.tabs-nav li a {
    color: #000
}

div.styleRed1 {
    background-color: #e6e7e8
}

div.styleRed1 h5 {
    background: #ee3124;
    color: #fff!important;
    height: 38px;
    line-height: 38px;
    padding-left: 15px
}

div.styleRed1 p {
    margin: 0 15px 10px!important
}

div.styleRed1 ul {
    padding-bottom: 15px!important
}

div#productDetailPageIntroContainer {
    background: transparent url(../images/pctelecom/bg_productdetail.jpg) no-repeat 50% 0;
    height: 382px
}

div#productIntro {
    position: relative
}

div.introImage {
    position: absolute;
    top: 16px;
    right: 50px
}

div.introImage img {
    margin-left: 20px
}

div#productIntro div.content {
    width: 350px;
    padding: 80px 0 0 40px;
    color: #fff;
    font-weight: 700
}

div#productIntro div.content h2 {
    font-size: 250%
}

.ts-2 div#productIntro div.content h2 {
    font-size: 200%
}

.ts-3 div#productIntro div.content h2 {
    font-size: 150%
}

.ts-4 div#productIntro div.content h2 {
    font-size: 120%
}

div#productIntro div.content h2 span {
    color: #000
}

div#productIntro div.content p {
    margin-bottom: 10px
}

div#productIntro div.content p.price {
    font-size: 110%;
    margin-bottom: 0
}

div#productIntro div.content p.itemnumber {
    color: #000;
    margin-bottom: 10px
}

div#productIntro div.content p.price span.strike {
    text-decoration: line-through
}

.ts-2 div#productIntro div.content p {
    font-size: 100%
}

div#productIntro div.content a.btn_howBuyPhone {
    display: block;
    width: 140px;
    height: 40px;
    outline: 0;
    text-indent: -3000em
}

div#productIntro div.content a.btn_howBuyPhone:hover {
    background-position: 0 -40px
}

div#productDetailedInfo {
    position: relative;
    top: -49px
}

div#productDetailedInfo ul.tabs-nav {
    padding: 0
}

div#productDetailedInfo ul.tabs-nav li {
    margin: 0 3px 0 0;
    border: 0;
    background: transparent url(../images/pctelecom/bg_producttab_inactive_left.gif) no-repeat 0 0;
    padding-left: 15px
}

div#productDetailedInfo ul.tabs-nav li a {
    background: transparent url(../images/pctelecom/bg_producttab_inactive_right.gif) no-repeat 100% 0;
    color: #666;
    line-height: 49px;
    height: 49px;
    display: inline;
    float: left;
    padding: 0 15px 0 0;
    position: static;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 110%;
    text-decoration: none
}

.highContrast div#productDetailedInfo ul.tabs-nav li a {
    color: #000
}

div#productDetailedInfo ul.tabs-nav li.tabs-selected {
    background: transparent url(../images/pctelecom/bg_producttab_active_left.gif) no-repeat 0 0
}

div#productDetailedInfo ul.tabs-nav li.tabs-selected a {
    background: transparent url(../images/pctelecom/bg_producttab_active_right.gif) no-repeat 100% 0;
    color: #e31c2a
}

div#productDetailedInfo .tabs-container {
    background-color: #fff;
    border: 0
}

div#productDetailedInfo h4 {
    font-size: 180%;
    text-transform: uppercase;
    color: #e31c2a;
    margin: 30px 0
}

div#productDetailedInfo p {
    margin: 0 0 10px
}

div#productDetailedInfo div ul {
    list-style: disc;
    margin: 0 0 20px 20px
}

div#productDetailedInfo div ul li {
    margin: 0 0 5px
}

div#productDetailedInfo h4 span {
    color: #000
}

a.iconQuestion {
    font-size: 100%;
    padding: 5px 23px 5px 0;
    background: transparent url(../images/pctelecom/icon_question.gif) no-repeat 100% 3px
}

div#productDetailedInfo p.legal {
    margin: 10px 0;
    color: #666;
    font-weight: 700
}

div#itemTools {
    position: absolute;
    bottom: 5px;
    right: 0
}

div#itemTools ul {
    display: inline;
    float: left;
    margin: 0 10px 0 0
}

div#itemTools ul a {
    color: #fff;
    text-transform: uppercase
}

div.promotionItems div.item {
    margin: 20px 0 30px;
    padding: 25px 0 5px;
    clear: both;
    overflow: auto
}

div.promotionItems div.item img {
    float: right;
    display: inline;
    margin: 0 0 0 10px
}

div#homeSecondaryContentWrapper div.promotionItems div.item img {
    margin-right: 20px
}

div.promotionItems div.item h3 {
    font-size: 190%;
    color: #e31c2a;
    border-top: 2px solid #000;
    border-bottom: 2px solid #000;
    padding: 15px 0;
    margin: 0 135px 15px 0;
    line-height: 1.1em;
    text-transform: uppercase
}

div.promotionItems div.item h3 span {
    color: #000;
    display: block
}

div.grid_8 div.promotionItems div.item {
    margin: 1em 0;
    padding: 0;
    zoom: 1;
    font-size: 1.25em
}

div.grid_8 div.promotionItems div.item p {
    font-size: 1em
}

div.grid_8 div.promotionItems div.item img {
    float: left;
    display: inline;
    margin: 0 20px 10px 0
}

#interiorPageContent div.grid_8 div.promotionItems div.item h3 {
    font-size: 120%;
    color: #000;
    padding: 0;
    margin: 0 0 10px;
    line-height: 1.1em;
    text-transform: uppercase;
    border: 0;
    clear: none!important
}

div#homeFeatureContent {
    background-color: #fff;
    border-bottom: 1px solid #c8c7c7
}

div#homeSecondaryContent {
    background: transparent url(../images/pctelecom/bg_home_contentarea_left.gif) no-repeat 0 0
}

div#homeSecondaryContentWrapper {
    background: transparent url(../images/pctelecom/bg_home_contentarea_right.gif) no-repeat 100% 0
}

#homeSecondaryContentWrapper div.promotionItems div.item {
    overflow: visible
}

div.buyThisPhone div.column1 {
    display: inline;
    float: left;
    width: 325px;
    margin: 0 20px 0 0;
    padding-right: 15px;
    border-right: 1px solid #ccc
}

div.buyThisPhone div.column2 {
    display: inline;
    float: left;
    width: 325px
}

div.buyThisPhone h3 {
    font-size: 180%;
    text-transform: uppercase;
    color: #e31c2a;
    margin: 0 0 5px;
    line-height: 1.2em
}

div.buyThisPhone h3 span {
    color: #000;
    display: block
}

div.buyThisPhone img {
    display: inline;
    float: right;
    margin: 0 0 10px 10px
}

div.buyThisPhone a.btn_red {
    text-decoration: none
}

div.buyThisPhone a.btn_red span {
    font-size: 100%
}

div#genericContent {
    margin-bottom: 50px
}

#genericContent h3 {
    text-transform: uppercase;
    color: #000;
    margin: 0 0 10px;
    font-weight: 400;
    clear: left
}

div#genericContent h3 span {
    color: #000
}

div#genericContent p,
div#genericContentShort p,
div#genericTabbedContent p,
div#myPCSignupPageMainContent p {
    margin: 0 0 15px
}

div#genericContent p strong,
div#genericContentShort p strong {
    text-transform: uppercase;
    font-size: 110%
}

div#genericTabbedContent {
    position: relative;
    top: -49px;
    margin-bottom: 70px
}

div#genericContent h4 {
    font-size: 180%;
    text-transform: uppercase;
    color: #e31c2a;
    margin: 30px 0 10px
}

div#genericContent h4 span {
    color: #000
}

div#genericContent h5 {
    font-size: 140%;
    text-transform: uppercase;
    color: #000;
    margin: 30px 0 10px
}

div#genericContent h5 span {
    color: #e31c2a
}

div#genericContent h6 {
    font-size: 130%;
    text-transform: uppercase;
    color: #000;
    margin: 30px 0 10px
}

div#genericContent h6 span {
    color: #e31c2a
}

div#genericContent hr,
div#genericTabbedContent hr {
    margin: 30px 0;
    height: 1px;
    border: 0;
    border-bottom: 1px solid #ccc
}

div#genericContent ol,
div#genericContent ul,
div#genericTabbedContent ol,
div#genericTabbedContent ul,
div#myPCSignupPageMainContent ul {
    margin: 0 0 15px 25px
}

div#genericContent ol li,
div#genericTabbedContent ol li {
    list-style: decimal
}

div#genericContent ul li,
div#genericTabbedContent ul li,
div#myPCSignupPageMainContent ul li {
    list-style: disc
}

div#genericContent ul.checkmark li,
div#genericTabbedContent ul.checkmark li {
    list-style: none;
    background: transparent url(../images/pctelecom/icon_checkmark.gif) no-repeat 0 0;
    padding-left: 15px
}

div.grid_3 div.callout,
div.grid_4 div.callout,
div.grid_8 div.callout {
    margin: 40px 0 15px;
    background-color: #eaeaea;
    border: 1px solid #eaeaea;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    padding: 15px 20px
}

div.grid_8 div.callout {
    margin-top: 0;
    overflow: auto;
    clear: left;
    zoom: 1
}

div.grid_4 div.callout h4,
div.grid_3 div.callout h4 {
    font-size: 140%;
    text-transform: uppercase;
    color: #e31c2a;
    margin: 0 0 10px
}

div.grid_4 div.callout h4 span,
div.grid_3 div.callout h4 span {
    color: #000
}

div.grid_4 div.callout p,
div.grid_3 div.callout p {
    margin: 0 0 15px
}

div.grid_4 div.callout p strong,
div.grid_3 div.callout p strong {
    display: block
}

div.grid_8 div.callout h5 {
    text-transform: uppercase;
    color: #e31c2a!important;
    font-size: 110%!important;
    padding-left: 20px;
    background: transparent url(../images/pctelecom/icon_bubble.gif) no-repeat 0 0;
    line-height: 16px;
    margin: 0 0 10px!important
}

div.callout ul.twoColumn {
    margin: 0 0 15px 20px!important;
    padding: 0
}

div.callout ul.twoColumn li {
    list-style: none;
    width: 200px;
    display: inline;
    float: left;
    margin: 0 0 5px
}

div.topListing {
    margin: 40px 0 15px
}

div.topListing h3 {
    font-weight: 700!important;
    font-size: 150%;
    color: #e31c2a;
    border-top: 2px solid #000;
    border-bottom: 2px solid #000;
    padding: 15px 0;
    margin: 0 0 10px;
    line-height: 1.1em;
    text-transform: uppercase
}

div.topListing h3 span {
    color: #000
}

div.topListing ol {
    margin: 0 0 15px 30px
}

div.topListing ol li {
    list-style: decimal
}

div.rightColItem {
    margin: 40px 0 15px
}

div.rightColItem h3 {
    font-weight: 700!important;
    font-size: 150%;
    color: #e31c2a;
    border-top: 2px solid #000;
    border-bottom: 2px solid #000;
    padding: 15px 0;
    margin: 0 0 10px;
    line-height: 1.1em;
    text-transform: uppercase
}

div.rightColItem h3 span {
    color: #000
}

div.rightColItem a.btn_darkgrey {
    margin: 0 20px 0 0
}

div.rightColItem p {
    margin: 0 0 15px
}

form#activateOnline fieldset {
    padding: 0;
    margin-top: 0;
    border: 0
}

form#activateOnline legend {
    text-transform: uppercase;
    color: #e31c2a;
    margin: 0 0 10px;
    font-weight: 700
}

form#activateOnline legend span {
    color: #000
}

form#activateOnline div.row {
    padding: 0 0 10px
}

form#activateOnline div.row input {
    width: 220px;
    padding: 2px 0
}

form#activateOnline div.row select {
    width: 220px;
    padding: 2px 0
}

form#activateOnline div.row input.radio,
form#activateOnline label.inline input {
    width: auto;
    padding: 0
}

form#activateOnline div.row input.submit {
    width: auto;
    padding: 0
}

form#activateOnline div.row input#tb_pcode1,
form#activateOnline div.row input#tb_pcode2 {
    width: 40px
}

form#activateOnline div.row input#tb_phone1,
form#activateOnline div.row input#tb_phone2 {
    width: 40px
}

form#activateOnline div.row input#tb_phone3 {
    width: 50px
}

form#activateOnline div.row input#tb_dob1,
form#activateOnline div.row input#tb_dob2 {
    width: 40px
}

form#activateOnline div.row input#tb_dob3 {
    width: 50px
}

form#activateOnline div.row input#tb_phonecontact1,
form#activateOnline div.row input#tb_phonecontact2 {
    width: 40px
}

form#activateOnline div.row input#tb_phonecontact3 {
    width: 50px
}

form#activateOnline table.compare label {
    width: auto;
    float: none
}

form#activateOnline table.compare th label {
    white-space: nowrap
}

form#activateOnline span.legal,
form#activateOnline p.legal {
    display: block;
    font-style: italic;
    color: #666
}

form#activateOnline div#phoneInfo {
    display: none
}

form#activateOnline div#phoneInfo a#exampleID {
    text-align: center;
    display: block;
    text-decoration: none;
    margin-bottom: 10px
}

form#activateOnline div#phoneInfo img {
    display: block;
    margin: 0 auto 10px
}

div#myPCSignupPageMainContent {
    padding-bottom: 50px
}

div#myPCSignupPageMainContent h3 {
    height: 50px;
    line-height: 50px;
    margin: 0 0 15px
}

div#myPCSignupPageMainContent div p {
    margin: 0 0 15px;
    font-size: 115%
}

div#myPCSignupPageMainContent div form div.row {
    clear: left;
    padding-bottom: 15px
}

div#myPCSignupPageMainContent div form div.row div.instructional {
    display: none;
    float: left;
    width: 200px;
    margin-left: 10px;
    color: #959595
}

div#myPCSignupPageMainContent div form div.row label,
div#myPCSignupPageMainContent div form div.row span.label {
    display: inline;
    float: left;
    width: 180px;
    font-size: 115%
}

div#myPCSignupPageMainContent div form div.row label span,
div#myPCSignupPageMainContent div form div.row span.label span {
    color: #ed1c24
}

div#myPCSignupPageMainContent div form div.row span.description {
    clear: left;
    color: #000;
    display: block;
    float: none;
    margin-left: 5px
}

div#myPCSignupPageMainContent div form div.row label.inline {
    margin: 0 15px 0 0;
    width: 100px
}

div#myPCSignupPageMainContent div form div.row input#tb_firstname,
div#myPCSignupPageMainContent div form div.row input#tb_member_prefs_firstname,
div#myPCSignupPageMainContent div form div.row input#tb_lastname,
div#myPCSignupPageMainContent div form div.row input#tb_member_prefs_lastname,
div#myPCSignupPageMainContent div form div.row input#tb_username,
div#myPCSignupPageMainContent div form div.row input#pw_password,
div#myPCSignupPageMainContent div form div.row input#pw_passwordconfirm,
div#myPCSignupPageMainContent div form div.row input#tb_email,
div#myPCSignupPageMainContent div form div.row input#tb_emailconfirm,
div#myPCSignupPageMainContent div form div.row input#tb_phone,
div#myPCSignupPageMainContent div form div.row input#tb_address1,
div#myPCSignupPageMainContent div form div.row input#tb_member_prefs_address1,
div#myPCSignupPageMainContent div form div.row input#tb_address2,
div#myPCSignupPageMainContent div form div.row input#tb_member_prefs_address2 {
    display: inline;
    float: left;
    padding: 5px;
    border: 1px solid #cecece;
    width: 295px
}

div#myPCSignupPageMainContent div form div.row input#tb_pcode1,
div#myPCSignupPageMainContent div form div.row input#tb_member_prefs_pcode1,
div#myPCSignupPageMainContent div form div.row input#tb_pcode2,
div#myPCSignupPageMainContent div form div.row input#tb_member_prefs_pcode2,
div#myPCSignupPageMainContent div form div.row input#tb_member_prefs_areacode,
div#myPCSignupPageMainContent div form div.row input#tb_member_prefs_phone1,
div#myPCSignupPageMainContent div form div.row input#tb_member_prefs_phone2 {
    padding: 5px;
    border: 1px solid #cecece;
    width: 80px
}

div#myPCSignupPageMainContent div form div.row select#dd_gender,
div#myPCSignupPageMainContent div form div.row select#dd_member_prefs_gender {
    padding: 5px;
    border: 1px solid #cecece;
    width: 200px
}

div#myPCSignupPageMainContent div form div.row select#dd_ethnicity {
    padding: 5px;
    border: 1px solid #cecece;
    width: 295px
}

div#myPCSignupPageMainContent div form div.row select#dd_age {
    padding: 5px;
    border: 1px solid #cecece;
    width: 130px
}

div#myPCSignupPageMainContent div form div.row select#dd_income {
    padding: 5px;
    border: 1px solid #cecece;
    width: 295px
}

div#myPCSignupPageMainContent div form div.row label.inline input {
    float: none;
    width: auto;
    padding: 0;
    border: 0
}

div#myPCSignupPageMainContent div form fieldset#optinInfo {
    margin: 20px 0
}

div#myPCSignupPageMainContent div form fieldset#optinInfo legend {
    font-size: 115%;
    margin: 0 0 10px
}

div#myPCSignupPageMainContent div form fieldset#optinInfo p {
    padding: 0 0 5px 10px;
    margin: 0
}

div#myPCSignupPageMainContent div form fieldset#optinInfo p.storeList {
    width: 366px;
    padding-bottom: 0;
    height: 30px
}

div#myPCSignupPageMainContent div form fieldset#optinInfo p.selected {
    background: transparent url(../images/pctelecom/bg_pcsignup_storelist_top_pc.gif) no-repeat 0 0
}

div#myPCSignupPageMainContent div form fieldset#optinInfo div#storeList {
    display: none;
    padding: 5px 0 5px 15px;
    width: 351px;
    background: transparent url(../images/pctelecom/bg_pcsignup_storelist_pc.gif) no-repeat 0 100%
}

div#myPCSignupPageMainContent div form fieldset#recaptcha {
    margin: 0 0 20px
}

div#myPCSignupPageMainContent div form fieldset#recaptcha .recaptchatable .recaptcha_r4_c4 {
    background: #860400 url(../images/pctelecom/recaptcha_logo_block.gif) no-repeat 0 0
}

div#myPCSignupPageMainContent div form input.image {
    display: block;
    margin: 0 0 10px;
    padding: 0;
    width: auto;
    border: 0
}

div#myPCSignupPageMainContent a.btnSignup {
    display: inline;
    float: left;
    margin: 0 10px 0 15px;
    height: 25px;
    line-height: 25px;
    color: #fff!important;
    background: transparent url(../images/pctelecom/bg_btn_darkgrey_right.gif) no-repeat 100% -25px;
    padding: 0 18px 0 0;
    width: auto
}

div#myPCSignupPageMainContent a.btnSignup span {
    position: static;
    display: inline;
    float: left;
    height: 25px;
    line-height: 25px;
    background: transparent url(../images/pctelecom/bg_btn_darkgrey_left.gif) no-repeat 0 -25px;
    padding: 0 10px;
    font-weight: 700
}

div#myPCSignupPageMainContent a.btnSignup:hover {
    text-decoration: none!important;
    background-position: 100% 0
}

div#myPCSignupPageMainContent a.btnSignup:hover span {
    background-position: 0 0
}

div#memberAccountPageIntroContent div#memberIdentifier {
    margin: 10px 0 20px 10px
}

div#memberAccountPageIntroContent div#memberIdentifier img {
    margin: 0 10px -10px 0
}

div#memberAccountPageIntroContent div#memberIdentifier span {
    font-size: 210%;
    margin: 0
}

div#memberPreferencesNavigation {
    position: relative;
    height: 50px;
    background: transparent url(../images/pctelecom/bg_member_subnav_en_pc.gif) no-repeat 0 0
}

div#memberPreferencesNavigation ul {
    position: absolute;
    top: 9px;
    left: 19px
}

div#memberPreferencesNavigation ul li {
    display: inline;
    float: left
}

#memberPreferencesPersonalInfoDetails {
    position: relative
}

#memberPreferencesPersonalInfoDetails h2 {
    font-size: 150%;
    font-weight: 400;
    line-height: 1em;
    padding: 0 0 5px;
    margin: 0 25px 5px;
    border-bottom: 1px solid #cecece
}

#memberPreferencesPersonalInfoDetails p {
    margin-left: 25px
}

#memberPreferencesPersonalInfoDetails p.required {
    position: absolute;
    top: 0;
    right: 25px
}

#memberPreferencesPersonalInfoDetails #memberPreferencesPhoto {
    margin-top: 30px
}

#memberPreferencesPersonalInfoDetails #memberPreferencesPhoto img {
    display: block;
    margin: 0 0 10px 15px
}

#memberPreferencesPersonalInfoDetails #memberPreferencesPhoto fieldset {
    margin: 0 15px 10px
}

#memberPreferencesPersonalInfoDetails #memberPreferencesPersonalInfoForm {
    margin-top: 30px;
    padding-bottom: 30px
}

#memberPreferencesPersonalInfoDetails #memberPreferencesPersonalInfoForm fieldset {
    padding-left: 20px
}

#memberPreferencesPersonalInfoForm div.row {
    clear: left;
    padding-bottom: 15px
}

#memberPreferencesPersonalInfoForm div.row span.errorMessage {
    color: #ed1c24;
    display: block;
    margin: 0;
    font-weight: 700
}

#memberPreferencesPersonalInfoForm div.row div.instructional {
    display: none;
    float: left;
    width: 180px;
    margin-left: 10px;
    color: #959595
}

#memberPreferencesPersonalInfoForm div.row div.confirmField {
    display: none;
    clear: left;
    float: none;
    padding-top: 10px
}

#memberPreferencesPersonalInfoForm div.row label,
#memberPreferencesPersonalInfoForm div.row span.label {
    display: inline;
    float: left;
    width: 180px;
    font-size: 115%
}

#memberPreferencesPersonalInfoForm div.row label span,
#memberPreferencesPersonalInfoForm div.row span.label span {
    color: #ed1c24
}

#memberPreferencesPersonalInfoForm div.row span.description {
    clear: left;
    color: #000;
    display: block;
    float: none;
    margin-left: 5px
}

#memberPreferencesPersonalInfoForm div.row label.inline {
    margin: 0 15px 0 0;
    width: 100px
}

#memberPreferencesPersonalInfoForm div.row label.inlineWide {
    width: auto
}

#memberPreferencesPersonalInfoForm div.row input {
    display: inline;
    float: left
}

#memberPreferencesPersonalInfoForm div.row input#tb_member_prefs_username,
#memberPreferencesPersonalInfoForm div.row input#tb_member_prefs_email,
#memberPreferencesPersonalInfoForm div.row input#tb_member_prefs_confirmemail,
#memberPreferencesPersonalInfoForm div.row input#pw_member_prefs_password,
#memberPreferencesPersonalInfoForm div.row input#pw_member_prefs_confirmpassword,
#memberPreferencesPersonalInfoForm div.row input#tb_member_prefs_firstname,
#memberPreferencesPersonalInfoForm div.row input#tb_member_prefs_lastname,
#memberPreferencesPersonalInfoForm div.row input#tb_member_prefs_phonenumber,
#memberPreferencesPersonalInfoForm div.row input#tb_member_prefs_address1,
#memberPreferencesPersonalInfoForm div.row input#tb_member_prefs_address2 {
    padding: 5px;
    border: 1px solid #cecece;
    width: 295px
}

#memberPreferencesPersonalInfoForm div.row input#tb_member_prefs_pcode1,
#memberPreferencesPersonalInfoForm div.row input#tb_member_prefs_pcode2,
#memberPreferencesPersonalInfoForm div.row input#tb_member_prefs_pin {
    padding: 5px;
    border: 1px solid #cecece;
    width: 80px;
    float: none
}

#memberPreferencesPersonalInfoForm div.row label.inline input,
#memberPreferencesPersonalInfoForm div.row label.inlineWide input {
    float: none;
    width: auto;
    padding: 0;
    border: 0
}

#memberPreferencesPersonalInfoForm div.row select {
    padding: 5px;
    border: 1px solid #cecece
}

#memberPreferencesPersonalInfoForm div.row select#dd_member_prefs_gender {
    width: 200px
}

#memberPreferencesPersonalInfoForm div.row select#dd_ethnicity {
    width: 295px
}

#memberPreferencesPersonalInfoForm div.row select#dd_age {
    width: 120px
}

#memberPreferencesPersonalInfoForm div.row p {
    margin-left: 0;
    margin-bottom: 5px
}

#memberPreferencesPersonalInfoForm fieldset#optinInfo {
    margin: 20px 0
}

#memberPreferencesPersonalInfoForm fieldset#optinInfo legend {
    font-size: 115%;
    margin: 0 0 10px
}

#memberPreferencesPersonalInfoForm fieldset#optinInfo p {
    padding: 0 0 5px 10px;
    margin: 0
}

#memberPreferencesPersonalInfoForm fieldset#optinInfo p.storeList {
    width: 366px;
    padding-bottom: 0;
    height: 30px
}

#memberPreferencesPersonalInfoForm fieldset#optinInfo p.selected {
    background: transparent url(../images/pctelecom/bg_pcsignup_storelist_top_pc.gif) no-repeat 0 0
}

#memberPreferencesPersonalInfoForm fieldset#optinInfo div#storeList {
    display: none;
    padding: 5px 0 5px 15px;
    width: 351px;
    background: transparent url(../images/pctelecom/bg_pcsignup_storelist_pc.gif) no-repeat 0 100%
}

#memberPreferencesPersonalInfoForm a.btnSaveChanges,
#memberPreferencesPersonalInfoForm a.btnSave {
    display: inline;
    float: left;
    margin-right: 10px;
    position: static!important
}

#memberPreferencesPersonalInfoForm a.cancel {
    display: inline;
    float: left;
    margin-right: 40px
}

#memberPreferencesPhoto a#btnUploadPhoto {
    margin: 0 0 10px 15px;
    position: static!important
}

#memberPreferencesPhoto span {
    margin: 0 20px 0 15px;
    line-height: 1.2em;
    display: block
}

#memberPreferencesPhoto a#btnUploadPhoto span {
    position: absolute;
    left: -3000px
}

#memberPreferencesPersonalInfoForm a#btnDeleteAccount {
    margin-left: 200px;
    margin-right: 20px;
    display: inline;
    float: left;
    height: 25px;
    line-height: 25px;
    color: #fff;
    background: transparent url(../images/pctelecom/bg_btn_grey_left.gif) no-repeat 0 0
}

#memberPreferencesPersonalInfoForm a#btnDeleteAccount span {
    display: inline;
    float: left;
    height: 25px;
    line-height: 25px;
    background: transparent url(../images/pctelecom/bg_btn_grey_right.gif) no-repeat 100% 0;
    padding: 0 25px 0 10px;
    font-weight: 700;
    cursor: pointer
}

#memberPreferencesPersonalInfoForm a#btnDeleteAccount:hover {
    text-decoration: none!important;
    background-position: 0 -25px
}

#memberPreferencesPersonalInfoForm a#btnDeleteAccount:hover span {
    background-position: 100% -25px
}

#memberPreferencesPersonalInfoForm a.btnSave {
    margin-right: 15px;
    display: inline;
    float: left;
    height: 25px;
    line-height: 25px;
    color: #fff!important;
    background: transparent url(../images/pctelecom/bg_btn_darkgrey_right.gif) no-repeat 100% -25px;
    padding: 0 18px 0 0
}

#memberPreferencesPersonalInfoForm a.btnSave span {
    display: inline;
    float: left;
    height: 25px;
    line-height: 25px;
    background: transparent url(../images/pctelecom/bg_btn_darkgrey_left.gif) no-repeat 0 -25px;
    padding: 0 10px;
    font-weight: 700;
    cursor: pointer
}

#memberPreferencesPersonalInfoForm a.btnSave:hover {
    text-decoration: none!important;
    background-position: 100% 0
}

#memberPreferencesPersonalInfoForm a.btnSave:hover span {
    background-position: 0 0
}

#memberPreferencesPersonalInfoForm hr {
    height: 1px;
    background-color: #ebebeb;
    border: 0;
    border-bottom: 1px solid #ebebeb;
    margin: 20px 0
}

div.searchResultsGrid div.row div.searchResultsGridItem div.content a.btnAddToShoppingList {
    display: block;
    margin: 10px auto 0;
    width: 113px;
    height: 25px;
    background: transparent url(../images/pctelecom/btn_addtoshoppinglist_off_en.gif) no-repeat 0 0;
    position: relative;
    outline: 0
}

div.searchResultsList ul li div.content a.btnAddToShoppingList {
    display: block;
    margin: 10px auto 0 0;
    width: 113px;
    height: 25px;
    background: transparent url(../images/pctelecom/btn_addtoshoppinglist_off_en.gif) no-repeat 0 0;
    position: relative;
    outline: 0
}

div.searchResultsGrid div.row div.searchResultsGridItem div.content a.btnAddingToShoppingList {
    display: block;
    margin: 10px auto 0;
    width: 113px;
    height: 25px;
    background: transparent url(../images/pctelecom/btn_addingtoshoppinglist_en_pc.gif) no-repeat 0 0;
    position: relative;
    outline: 0
}

div.searchResultsList ul li div.content a.btnAddingToShoppingList {
    display: block;
    margin: 10px auto 0 0;
    width: 113px;
    height: 25px;
    background: transparent url(../images/pctelecom/btn_addingtoshoppinglist_en_pc.gif) no-repeat 0 0;
    position: relative;
    outline: 0
}

div.searchResultsGrid div.row div.searchResultsGridItem div.content a.btnAddedToShoppingList {
    display: block;
    margin: 10px auto 0;
    width: 113px;
    height: 25px;
    background: transparent url(../images/pctelecom/btn_addedtoshoppinglist_en_pc.gif) no-repeat 0 0;
    position: relative;
    outline: 0
}

div.searchResultsList ul li div.content a.btnAddedToShoppingList {
    display: block;
    margin: 10px auto 0 0;
    width: 113px;
    height: 25px;
    background: transparent url(../images/pctelecom/btn_addedtoshoppinglist_en_pc.gif) no-repeat 0 0;
    position: relative;
    outline: 0
}

div#memberPreferencesNavigation ul li a {
    display: inline;
    float: left;
    width: 154px;
    height: 41px;
    text-indent: -3000px;
    outline: 0
}

div#memberPreferencesNavigation ul li#aboutme a {
    background: transparent url(../images/pctelecom/btn_aboutme_off_pc_en.gif) no-repeat 0 0
}

div#memberPreferencesNavigation ul li#aboutme.active a {
    background: transparent url(../images/pctelecom/btn_aboutme_on_pc_en.gif) no-repeat 0 0
}

div#memberPreferencesNavigation ul li#shoppinglists a {
    background: transparent url(../images/pctelecom/btn_shoppinglists_off_pc_en.gif) no-repeat 0 0
}

div#memberPreferencesNavigation ul li#shoppinglists.active a {
    background: transparent url(../images/pctelecom/btn_shoppinglists_on_pc_en.gif) no-repeat 0 0
}

div#memberPreferencesNavigation ul li#myfavourites a {
    background: transparent url(../images/pctelecom/btn_favourites_off_pc_en.gif) no-repeat 0 0
}

div#memberPreferencesNavigation ul li#myfavourites.active a {
    background: transparent url(../images/pctelecom/btn_favourites_on_pc_en.gif) no-repeat 0 0
}

div#memberPreferencesNavigation ul li#mystores a {
    background: transparent url(../images/pctelecom/btn_mystores_off_pc_en.gif) no-repeat 0 0
}

div#memberPreferencesNavigation ul li#mystores.active a {
    background: transparent url(../images/pctelecom/btn_mystores_on_pc_en.gif) no-repeat 0 0
}

div#memberPreferencesNavigation ul li#mytransactions a {
    background: transparent url(../images/pctelecom/btn_transactions_off_pc_en.gif) no-repeat 0 0
}

div#memberPreferencesNavigation ul li#mytransactions.active a {
    background: transparent url(../images/pctelecom/btn_transactions_on_pc_en.gif) no-repeat 0 0
}

div#memberPreferencesNavigation ul li#personalinfo a {
    background: transparent url(../images/pctelecom/btn_personalinfo_off_pc_en.gif) no-repeat 0 0
}

div#memberPreferencesNavigation ul li#personalinfo.active a {
    background: transparent url(../images/pctelecom/btn_personalinfo_on_pc_en.gif) no-repeat 0 0
}

div#memberPreferencesDetails form fieldset#memberPreferencesTags a.btnAddTerm {
    display: inline;
    float: left;
    width: 58px;
    height: 33px;
    position: relative;
    background: transparent url(../images/pctelecom/btn_member_add_pc_en_large.gif) no-repeat 0 0
}

#memberPreferencesPhoto a#btnUploadPhoto {
    position: relative;
    display: block;
    width: 128px;
    height: 25px;
    background: transparent url(../images/pctelecom/btn_uploadphoto_en_pc.gif) no-repeat 0 0;
    outline: 0
}

#memberPreferencesPersonalInfoForm a#btnDeleteAccount {
    position: relative;
    outline: 0
}

div#memberPreferencesMyFavourites a.btnAddFavourite {
    display: block;
    width: 113px;
    height: 25px;
    background: transparent url(../images/pctelecom/btn_addfavourite_en_pc.gif) no-repeat 0 0;
    outline: 0
}

div#memberPreferencesMyFavourites div.favouritesCategory div.footer a.btnRemoveSelectedOff {
    display: inline;
    float: left;
    width: 155px;
    height: 25px;
    background: transparent url(../images/pctelecom/btn_removeselected_off_en_pc.gif) no-repeat 0 0;
    outline: 0
}

div#memberPreferencesMyFavourites div.favouritesCategory div.footer a.btnRemoveSelectedOn {
    display: inline;
    float: left;
    width: 155px;
    height: 25px;
    background: transparent url(../images/pctelecom/btn_removeselected_on_en_pc.gif) no-repeat 0 0;
    outline: 0
}

div#productIntro div.content a.btn_howBuyPhone {
    background: transparent url(../images/pctelecom/btn_howBuyPhone_en.gif) no-repeat 0 0
}

.slider {
    position: relative;
    width: 100%;
    overflow: hidden
}

.slides {
    height: 100%;
    overflow: hidden;
    *zoom: 1;
    -webkit-backface-visibility: hidden;
    -webkit-transform-style: preserve-3d
}

.slide {
    height: 100%;
    float: left;
    clear: none
}

.slider-arrow {
    position: absolute;
    display: block;
    margin-bottom: -20px;
    padding: 20px
}

.slider-arrow--right {
    bottom: 50%;
    right: 30px
}

.slider-arrow--left {
    bottom: 50%;
    left: 30px
}

.slider-nav {
    position: absolute;
    bottom: 0
}

.slider-nav__item {
    width: 20px;
    height: 20px;
    float: left;
    clear: none;
    display: block;
    margin: 0 5px;
    background: #fff;
    border: 1px solid #000;
    border-radius: 20px
}

.slider-nav__item:hover {
    background: #000
}

.slider-nav__item--current {
    background: #000
}

* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box
}

a:link,
a:visited {
    color: #e41c11
}

a:link.more,
a:visited.more {
    text-decoration: none
}

.ch a:link.more,
.ch a:visited.more {
    text-decoration: underline
}

html {
    font-size: 16px;
    font-family: "Open Sans Condensed", Arial, Helvetica, sans-serif;
    letter-spacing: 1px;
    color: #000
}

html.ch {
    color: #000
}

html.ts-1 {
    font-size: 16px
}

html.ts-2 {
    font-size: 1.111em
}

html.ts-3 {
    font-size: 1.222em
}

html.ts-4 {
    font-size: 1.333em
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-weight: 400
}

h1,
h2,
h3 {
    color: #333
}

h3 {
    font-size: 1.5em
}

p {
    font-size: 1.25em
}

sub,
sup {
    font-size: 1em
}

sup {
    top: -.8em
}

h1 sup {
    font-size: .35em;
    top: -1.5em
}

.title-wrapper {
    margin: 3.75em 0;
    background: #000;
    background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2RmMjYyNSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUwJSIgc3RvcC1jb2xvcj0iI2RmMjYyNSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUxJSIgc3RvcC1jb2xvcj0iIzAwMDAwMCIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMwMDAwMDAiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
    background: -webkit-linear-gradient(left, #e41c11 0, #e41c11 50%, #000 51%, #000 100%);
    background: linear-gradient(to right, #e41c11 0, #e41c11 50%, #000 51%, #000 100%)
}

.prepaid .title-wrapper {
    background: #e41c11;
    background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzAwMDAwMCIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUwJSIgc3RvcC1jb2xvcj0iIzAwMDAwMCIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUxJSIgc3RvcC1jb2xvcj0iI2RmMjYyNSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNkZjI2MjUiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
    background: -webkit-linear-gradient(left, #000 0, #000 50%, #e41c11 51%, #e41c11 100%);
    background: linear-gradient(to right, #000 0, #000 50%, #e41c11 51%, #e41c11 100%)
}

.print .title-wrapper {
    margin: 1em 0;
    background: #fff;
    filter: none
}

@media screen and (max-width:47.9375em) {
    .title-wrapper {
        margin-top: 0;
        margin-bottom: 2.188em
    }
    .title-wrapper h1 sup {
        font-size: .5em
    }
}

.title-wrapper h1,
.title-wrapper #genericContent h2,
.title-wrapper #genericContentShort h2 {
    font-size: 3.375em;
    line-height: 1em;
    color: #fff;
    background: #000;
    padding: 0;
    margin: 0
}

.title-wrapper h1 span,
.title-wrapper #genericContent h2 span,
.title-wrapper #genericContentShort h2 span {
    display: inline-block;
    padding: 10px 20px 4px;
    background: #e41c11
}

.prepaid .title-wrapper h1,
.prepaid .title-wrapper #genericContent h2,
.prepaid .title-wrapper #genericContentShort h2 {
    background: #e41c11
}

.prepaid .title-wrapper h1 span,
.prepaid .title-wrapper #genericContent h2 span,
.prepaid .title-wrapper #genericContentShort h2 span {
    background: #000
}

.print .title-wrapper h1,
.print .title-wrapper #genericContent h2,
.print .title-wrapper #genericContentShort h2 {
    background: #fff
}

.print .title-wrapper h1 span,
.print .title-wrapper #genericContent h2 span,
.print .title-wrapper #genericContentShort h2 span {
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 8px;
    background: #fff;
    color: #000
}

@media screen and (max-width:47.9375em) {
    .title-wrapper h1,
    .title-wrapper #genericContent h2,
    .title-wrapper #genericContentShort h2 {
        font-size: 2.8125em
    }
}

h1,
#genericContent h2,
#genericContentShort h2 {
    font-size: 3.75em;
    line-height: 1em;
    color: #000
}

@media screen and (max-width:47.9375em) {
    h1,
    #genericContent h2,
    #genericContentShort h2 {
        font-size: 2.8125em
    }
}

h2 {
    font-size: 2.8125em
}

@media screen and (max-width:47.9375em) {
    h2 {
        font-size: 2.5em
    }
}

table {
    margin: 2em 0;
    border: 1px solid #ccc;
    border-collapse: collapse;
    table-layout: fixed;
    font-size: 1.25em
}

table th,
table td {
    padding: 5px
}

table th {
    background-color: #f7f7f7;
    font-weight: 400;
    text-align: left;
    text-transform: uppercase;
    border-bottom: 1px solid #ccc
}

table td {
    border-top: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-left: 1px solid #ccc;
    text-align: center;
    vertical-align: middle
}

table td.alt {
    background-color: #ccc;
    text-align: left
}

table td.alt h4 {
    margin: 0;
    text-align: center
}

table ul {
    padding-left: 2em;
    text-align: left
}

table.compare {
    border-collapse: separate;
    border-spacing: 2px;
    table-layout: auto;
    width: 100%;
    margin: 0 0 15px
}

table.compare td {
    padding: 8px 12px;
    border-left: 0;
    border-right: 0;
    background-color: #eaeaea;
    vertical-align: top;
    text-align: left
}

table.compare th {
    color: #fff;
    text-align: left;
    background-color: #666;
    height: 38px;
    vertical-align: middle;
    padding: 0 12px;
    border-top: 1px solid #666
}

table.compare th.first {
    border-left: 1px solid #666;
    border-radius: 3px 0 0 3px
}

table.compare th.last {
    border-right: 1px solid #666;
    border-radius: 0 3px 3px 0
}

table.compare th.active {
    background-color: #e31c2a
}

table.compare th.plan {
    width: 140px
}

table.compare th.details {
    width: 140px
}

table.compare td.phone {
    width: 130px
}

table.compare td.style {
    width: 110px
}

table.compare td.plan {
    width: 140px
}

table.compare td img.phone {
    display: block;
    margin: 0 auto;
    width: 60%
}

table.compare p {
    margin: 0!important
}

table.compare p.title {
    font-weight: 700;
    text-align: center
}

table.compare li {
    margin: 0 0 3px
}

table.compare .strike {
    text-decoration: line-through
}

table.styleGreen1,
table.styleGreen2 {
    border-bottom: 1px solid #000;
    border-collapse: collapse;
    border-spacing: 0
}

table.styleGreen1 th a,
table.styleGreen2 th a {
    color: #fff
}

table.styleGreen1 td,
table.styleGreen2 td,
table.styleRed td {
    background-color: #fff;
    border-right: 1px solid #000
}

table.styleGreen1 td.noborder,
table.styleGreen2 td.noborder,
table.styleRed td.noborder {
    border: 0
}

table.styleGreen1 .grey td,
table.styleGreen2 .grey td,
table.styleRed .grey td,
table.styleGrey .grey td {
    background-color: #e6e7e8
}

table.styleGreen1 th {
    border: 0!important;
    border-left: 1px solid #fff!important;
    background-color: #41ad49;
    text-transform: uppercase;
    font-size: 140%
}

table.styleGreen1 th sup {
    top: -1em;
    font-size: .7em!important
}

table.styleGreen1 th.first {
    border: 0;
    border-radius: 0;
    background-color: #41ad49;
    padding: 0 12px 0 15px
}

table.styleGreen1 th.last {
    background-color: #525255;
    border-radius: 0
}

table.styleGreen1 td.last {
    background-color: #000;
    color: #fff;
    border: 0;
    font-weight: 700
}

table.styleGreen1 .grey td.last {
    background-color: #404040
}

table.styleGreen1 .black td {
    background-color: #000;
    color: #fff
}

table.styleGreen1 .black td.last {
    background-color: #404040
}

table.styleGreen2 th {
    border: 0!important;
    border-left: 1px solid #fff!important;
    border-radius: 0!important;
    background-color: #41ad49;
    text-transform: uppercase;
    font-size: 140%
}

table.styleGreen2 th.last {
    background-color: #41ad49;
    font-size: 120%;
    padding: 0 15px 0 12px;
    text-transform: none;
    white-space: nowrap
}

table.styleGreen2 td.last {
    text-align: center;
    border-right: 0;
    font-weight: 700
}

table.styleRed,
table.styleGrey {
    border-collapse: collapse;
    border-spacing: 0
}

table.styleRed th {
    border: 0!important;
    border-radius: 0!important;
    background-color: #ee3124;
    padding: 0 15px 0 12px;
    text-transform: uppercase;
    font-size: 140%
}

table.styleRed th.last {
    background-color: #ee3124;
    border-left: 1px solid #fff!important;
    text-transform: none
}

table.styleRed td.last {
    border: 0;
    font-weight: 700
}

table.styleGrey th {
    text-transform: uppercase;
    font-size: 130%;
    border-radius: 0!important
}

table.styleGrey th a {
    color: #fff
}

table.styleGrey td {
    background-color: #fff
}

.overthrow-enabled .overthrow,
.overthrow-enabled .overthrow-x {
    -webkit-overflow-scrolling: touch
}

.overthrow-enabled .overthrow {
    overflow: auto
}

.overthrow-enabled .overthrow-x {
    overflow: hidden;
    overflow-x: auto
}

table.responsive {
    width: calc(100% - 170px);
    margin-bottom: 0;
    border-right: 0
}

table.responsive td,
table.responsive th {
    position: relative;
    overflow: hidden
}

table.responsive td {
    width: 240px
}

table.responsive td:last-child {
    border-right: 0
}

.table-wrapper {
    position: relative;
    overflow: hidden;
    border-right: 1px solid #ccc
}

.table-wrapper .scrollable table {
    margin-left: 169px
}

.table-wrapper .scrollable td[headers=phone-model] {
    min-height: 164px
}

.pinned {
    position: absolute;
    left: 0;
    top: 0;
    background: #f7f7f7;
    width: 170px;
    border-left: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    overflow: hidden
}

.pinned table {
    width: 100%;
    margin-bottom: 0;
    border-right: 0;
    border-left: 0
}

.pinned table td:last-child {
    border-bottom: 0
}

.pinned .phone-compare {
    border-bottom: 0
}

#skip-nav {
    position: absolute;
    top: 0;
    left: -9999px;
    z-index: 100;
    text-transform: uppercase
}

#skip-nav:focus {
    left: 0
}

#page-header {
    position: relative;
    z-index: 20;
    background: #333;
    background: #fff;
    color: #000;
    height: 115px
}

#page-header.header-mini {
    height: 48px;
    border-top: 1px solid #b2b2b2;
    border-bottom: 1px solid #b2b2b2;
    position: fixed;
    width: 100%;
    padding-left: 20px;
    padding-right: 20px;
    top: -46px;
    -webkit-transition: -webkit-transform 450ms ease;
    transition: transform 450ms ease
}

.csstransforms #page-header.header-mini {
    -webkit-transform: translate(0, 46px);
    -ms-transform: translate(0, 46px);
    transform: translate(0, 46px)
}

.no-csstransforms #page-header.header-mini {
    top: 0
}

#page-header.header-mini+#page-main {
    margin-top: 138px
}

@media screen and (max-width:47.9375em) {
    #page-header.header-mini {
        position: relative
    }
}

@media screen and (max-width:47.9375em) {
    #page-header {
        height: 60px
    }
}

#site-warning {
    display: none;
    position: relative;
    background: #333;
    color: #fff;
    text-align: center;
    padding: 10px 30px;
    z-index: 0;
    top: 30px
}

.header-mini #site-warning {
    top: 0
}

@media screen and (max-width:47.9375em) {
    #site-warning {
        top: 20px
    }
}

#site-warning #close-warning {
    font-family: arial sans-serif;
    position: absolute;
    right: -30px;
    top: 0;
    text-decoration: none;
    background: #ccc;
    color: #333;
    font-size: 0;
    font-weight: 700;
    display: block;
    height: 30px;
    width: 30px;
    text-align: center;
    background: url(../images/pctelecom/warning-close.svg) no-repeat;
    opacity: .5;
    -ms-filter: "alpha(opacity=50)"
}

#site-warning #close-warning:hover {
    opacity: .7;
    -ms-filter: "alpha(opacity=70)"
}

.no-svg #site-warning #close-warning {
    background: url(../images/pctelecom/warning-close.png) no-repeat
}

.csstransforms #site-warning #close-warning {
    top: 50%;
    -webkit-transform: translate(0, -50%);
    -ms-transform: translate(0, -50%);
    transform: translate(0, -50%)
}

.csstransforms #site-warning #close-warning:before {
    position: absolute;
    top: 100%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%)
}

#site-warning.site-warning--yellow {
    background-color: #fff569;
    text-align: left;
    top: 0;
    margin-bottom: 12px;
    margin-top: 12px;
    padding: 16px
}

#site-warning.site-warning--yellow p {
    background: transparent url(../images/pctelecom/icon-info.svg) 0 0 no-repeat;
    letter-spacing: 0;
    font-size: 18px;
    font-weight: 700;
    color: #000;
    padding-left: 34px;
    padding-right: 34px;
    margin: 0
}

#site-warning.site-warning--yellow #close-warning {
    display: none
}

#page-main {
    position: relative
}

#page-main img {
    max-width: 100%;
    height: auto
}

#site-logo {
    margin-top: 1.625em;
    width: 17%;
    margin-right: 3%;
    height: 58px
}

@media screen and (max-width:57.5em) {
    #site-logo {
        margin-right: 1%
    }
}

[lang=fr] .prepaid #site-logo {
    margin-right: 0
}

@media screen and (max-width:47.9375em) {
    [lang=fr] .prepaid #site-logo {
        margin-right: auto
    }
}

#site-logo .logo {
    display: block;
    width: 100%
}

#site-logo .logo img {
    width: 100%;
    vertical-align: middle
}

.header-mini #site-logo {
    display: none
}

@media screen and (max-width:47.9375em) {
    #site-logo {
        position: relative;
        z-index: 10;
        width: 125px;
        height: 40px;
        margin: 12px auto 0;
        padding: 0
    }
    #site-logo .logo {
        width: 125px;
        height: 40px;
        display: block;
        margin-top: -2px
    }
    .header-mini #site-logo {
        display: block
    }
}

@media screen and (max-width:47.9375em) {
    header.rockstopper #site-logo {
        position: absolute;
        left: 50%;
        transform: translateX(-50%)
    }
    header.rockstopper #site-logo:first-child {
        margin-left: -62px
    }
    header.rockstopper #site-logo:nth-child(2) {
        margin-left: 62px
    }
}

@media (max-width:375px) {
    header.rockstopper #site-logo {
        width: 100px
    }
    header.rockstopper #site-logo .logo {
        width: 100px
    }
    header.rockstopper #site-logo:first-child {
        margin-left: -50px
    }
    header.rockstopper #site-logo:nth-child(2) {
        margin-left: 50px
    }
}

#site-tools {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1.25em;
    position: absolute;
    top: 20px;
    right: 20px
}

#site-tools .wish-list-wrapper,
#site-tools .store-locator,
#site-tools .why-join,
#site-tools .lang-toggle,
#site-tools .login {
    display: block;
    float: left;
    position: relative;
    margin-left: 15px
}

@media screen and (max-width:54.375em) {
    [lang=fr] #site-tools {
        font-size: 1em
    }
}

#site-tools .why-join,
#site-tools .lang-toggle {
    margin-top: 4px
}

#site-tools a {
    color: #000;
    text-decoration: none
}

#site-tools a:hover,
#site-tools a:active,
#site-tools a:focus {
    color: #e41c11
}

.ch #site-tools a {
    text-decoration: underline
}

#site-tools .icon-btn {
    display: inline-block;
    background-color: transparent;
    border: 0;
    overflow: hidden
}

#site-tools .icon-btn:before {
    content: "";
    display: block;
    width: 0;
    height: 150%
}

#site-tools .login {
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -290px 0;
    width: 31px;
    height: 31px
}

.no-svg #site-tools .login {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -290px 0;
    width: 31px;
    height: 31px
}

#site-tools .login:hover,
#site-tools .login:active,
#site-tools .login:focus {
    background-position: -290px -40px
}

.prepaid #site-tools .login {
    display: none
}

#site-tools .store-locator {
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -260px 0;
    width: 20px;
    height: 30px
}

.no-svg #site-tools .store-locator {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -260px 0;
    width: 20px;
    height: 30px
}

#site-tools .store-locator:hover,
#site-tools .store-locator:active,
#site-tools .store-locator:focus {
    background-position: -260px -41px
}

#site-tools .wish-list-icon {
    position: relative;
    top: 3px;
    width: 34px;
    height: 26px;
    margin-bottom: 5px
}

#site-tools .wish-list-icon.full:after {
    content: '';
    display: block;
    position: absolute;
    top: -3px;
    right: -12px;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -160px -80px;
    width: 17px;
    height: 17px
}

.no-svg #site-tools .wish-list-icon.full:after {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -160px -80px;
    width: 17px;
    height: 17px
}

#site-tools .wish-list-icon span {
    position: absolute;
    display: block;
    height: 2px;
    width: 23px;
    background: #000;
    right: 0
}

#site-tools .wish-list-icon span:before {
    content: '';
    border: 1px solid #000;
    position: absolute;
    width: 5px;
    height: 5px;
    left: -10px;
    top: -2px;
    border-radius: 5px
}

#site-tools .wish-list-icon:hover span,
#site-tools .wish-list-icon:active span,
#site-tools .wish-list-icon.active span,
#site-tools .wish-list-icon:focus span {
    background-color: #e41c11
}

#site-tools .wish-list-icon:hover span:before,
#site-tools .wish-list-icon:active span:before,
#site-tools .wish-list-icon.active span:before,
#site-tools .wish-list-icon:focus span:before {
    border-color: #e41c11
}

#site-tools .wish-list-icon .phone {
    top: 3px
}

.has-selected-phone #site-tools .wish-list-icon .phone:before {
    border-color: #e41c11;
    background-color: #e41c11
}

#site-tools .wish-list-icon .plan {
    top: 12px
}

.has-selected-plan #site-tools .wish-list-icon .plan:before {
    border-color: #e41c11;
    background-color: #e41c11
}

#site-tools .wish-list-icon .store {
    top: 21px
}

.has-selected-store #site-tools .wish-list-icon .store:before {
    border-color: #e41c11;
    background-color: #e41c11
}

.prepaid #site-tools .wish-list-icon {
    display: none
}

#site-tools .wish-list-wrapper.active .wish-list-icon span {
    background-color: #e41c11
}

#site-tools .wish-list-wrapper.active .wish-list-icon span:before {
    border-color: #e41c11
}

#site-tools #wish-list {
    position: absolute;
    z-index: 5;
    right: 0
}

@media screen and (max-width:47.9375em) {
    #site-tools #wish-list {
        right: -22px
    }
}

#site-tools #wish-list #wish-list-container {
    width: 300px;
    border: 1px solid #b2b2b2;
    background: #fff;
    padding: 20px
}

#site-tools #wish-list .wish-list-title {
    margin: 0;
    font-size: 2.25em;
    text-transform: uppercase
}

#site-tools #wish-list .wish-list-legal {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif
}

#site-tools #wish-list .wish-list-indicators {
    margin: 0;
    padding: 0;
    list-style: none
}

#site-tools #wish-list .wish-list-indicators:before,
#site-tools #wish-list .wish-list-indicators:after {
    display: table;
    content: ""
}

#site-tools #wish-list .wish-list-indicators:after {
    clear: both
}

#site-tools #wish-list .wish-list-indicators:before,
#site-tools #wish-list .wish-list-indicators:after {
    display: table;
    content: ""
}

#site-tools #wish-list .wish-list-indicators:after {
    clear: both
}

#site-tools #wish-list .wish-list-indicators li {
    position: relative;
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    text-transform: uppercase;
    font-weight: 700;
    letter-spacing: 0;
    font-size: 1.5em;
    width: 100%;
    padding: .5em 0;
    border-bottom: 1px solid #b2b2b2
}

#site-tools #wish-list .wish-list-indicators li:last-child {
    margin-bottom: 0;
    border: 0
}

#site-tools #wish-list .wish-list-indicators li:after {
    content: '';
    display: block;
    position: absolute;
    top: .5em;
    right: 0;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -240px -80px;
    width: 30px;
    height: 30px
}

.no-svg #site-tools #wish-list .wish-list-indicators li:after {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -240px -80px;
    width: 30px;
    height: 30px
}

#site-tools #wish-list .wish-list-indicators li.selected:after {
    background-position: -200px -80px
}

.has-selected-phone #site-tools #wish-list .wish-list-indicators li.phone:after {
    background-position: -200px -80px
}

.has-selected-plan #site-tools #wish-list .wish-list-indicators li.plan:after {
    background-position: -200px -80px
}

.has-selected-store #site-tools #wish-list .wish-list-indicators li.store:after {
    background-position: -200px -80px
}

#site-tools #wish-list .wish-list-cta {
    font-size: 1.5em;
    margin: 20px 40px 0;
    text-align: center;
    color: #fff;
    display: block
}

#site-tools #wish-list .wish-list-cta:hover,
#site-tools #wish-list .wish-list-cta:active {
    color: #fff
}

#site-tools .wish-list-flyout {
    display: none
}

#site-tools .wish-list-wrapper.active .wish-list-flyout {
    display: block
}

.no-js #site-tools .wish-list-wrapper:hover .wish-list-flyout,
.no-js #site-tools .wish-list-wrapper:active .wish-list-flyout,
.no-js #site-tools .wish-list-wrapper:focus .wish-list-flyout {
    display: block
}

@media screen and (max-width:47.9375em) {
    #site-tools .wish-list-wrapper {
        position: absolute;
        right: 45px;
        top: 15px
    }
}

.header-mini #site-tools {
    top: 7px;
    right: 0
}

.header-mini #site-tools .why-join,
.header-mini #site-tools .lang-toggle {
    display: none
}

#site-tools .nav-toggle {
    display: none;
    background-color: transparent;
    border: 0;
    overflow: hidden;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -280px -80px;
    width: 32px;
    height: 32px
}

#site-tools .nav-toggle:before {
    content: "";
    display: block;
    width: 0;
    height: 150%
}

.no-svg #site-tools .nav-toggle {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -280px -80px;
    width: 32px;
    height: 32px
}

@media screen and (max-width:47.9375em) {
    #site-tools {
        top: 0;
        left: 0;
        right: 0;
        width: 100%
    }
    #site-tools .why-join,
    #site-tools .lang-toggle,
    #site-tools .login {
        display: none
    }
    #site-tools .nav-toggle,
    #site-tools .wish-list-wrapper,
    #site-tools .store-locator {
        position: absolute;
        top: 15px
    }
    #site-tools .wish-list-wrapper {
        right: 25px
    }
    #site-tools .store-locator {
        right: 20px
    }
    #site-tools .nav-toggle {
        display: block;
        left: 15px
    }
}

#nav-global {
    position: relative;
    text-transform: uppercase
}

@media (min-width:768px) {
    header.rockstopper #nav-global {
        display: none
    }
}

#nav-global a:link,
#nav-global a:visited {
    text-decoration: none
}

.ch #nav-global a:link,
.ch #nav-global a:visited {
    text-decoration: underline
}

#nav-global a[href*=iphone] {
    text-transform: none
}

#nav-global ul {
    list-style: none;
    padding-left: 0
}

#nav-global>.container_12 {
    position: relative
}

#nav-global .nav-flyout {
    display: none
}

#nav-global .nav-section-heading,
#nav-global .nav-section-heading-back {
    display: none
}

@media screen and (max-width:47.9375em) {
    #nav-global {
        overflow: hidden;
        padding-bottom: 10%
    }
    .nav-tertiary-open #nav-global li.open .nav-flyout {
        display: block
    }
}

#nav-primary {
    margin: 0;
    position: relative;
    border-bottom: 1px solid #b2b2b2;
    width: 80%
}

#nav-primary:before,
#nav-primary:after {
    display: table;
    content: ""
}

#nav-primary:after {
    clear: both
}

#nav-primary:before,
#nav-primary:after {
    display: table;
    content: ""
}

#nav-primary:after {
    clear: both
}

[lang=fr] .prepaid #nav-primary {
    width: 83%
}

.header-mini #nav-primary {
    width: 95%;
    border: 0
}

#nav-primary .nav-heading {
    padding: 16px;
    display: block;
    float: left;
    color: #000;
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 225%;
    line-height: 1em
}

.header-mini #nav-primary .nav-heading {
    display: none
}

.wf-active #nav-primary .nav-heading {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 225%
}

@media screen and (max-width:47.9375em) {
    .wf-active #nav-primary .nav-heading {
        font-size: 2.25em
    }
}

@media screen and (max-width:47.9375em) {
    #nav-primary .nav-heading {
        display: block;
        float: none;
        background: 0 0;
        padding: 15px 20px 0;
        position: relative
    }
}

#nav-primary .nav-sub {
    display: none;
    position: absolute;
    bottom: 0;
    z-index: 5;
    margin-top: 0;
    margin-bottom: -46px;
    padding-left: 0;
    width: 100%
}

#nav-primary .nav-sub:before,
#nav-primary .nav-sub:after {
    display: table;
    content: ""
}

#nav-primary .nav-sub:after {
    clear: both
}

#nav-primary .nav-sub:before,
#nav-primary .nav-sub:after {
    display: table;
    content: ""
}

#nav-primary .nav-sub:after {
    clear: both
}

.header-mini #nav-primary .nav-sub {
    position: static
}

#nav-primary .nav-sub>li {
    float: left
}

#nav-primary .nav-sub>li:last-child a {
    padding-right: 0
}

#nav-primary .nav-sub>li:first-child a:before {
    display: none
}

#nav-primary .nav-sub>li.active a {
    color: #e41c11
}

.header-mini #nav-primary .nav-sub>li>a:hover,
.header-mini #nav-primary .nav-sub>li>a:active {
    color: #fff;
    background: #000
}

#nav-primary .nav-sub>li a {
    position: relative;
    display: block;
    height: 46px;
    padding: 0 15px;
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 22px;
    font-weight: 400;
    line-height: 46px;
    color: #000
}

.wf-active #nav-primary .nav-sub>li a {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 22px;
    font-weight: 400
}

@media screen and (max-width:57.5em) {
    #nav-primary .nav-sub>li a {
        padding: 0 10px
    }
}

@media screen and (max-width:58.125em) {
    .wf-active #nav-primary .nav-sub>li a {
        font-size: 20px
    }
}

@media screen and (max-width:57.5em) {
    .wf-active #nav-primary .nav-sub>li a {
        font-size: 18px
    }
}

@media screen and (max-width:68.75em) {
    .wf-active[lang=fr] #nav-primary .nav-sub>li a {
        font-size: 19px
    }
}

@media screen and (max-width:54.375em) {
    .wf-active[lang=fr] #nav-primary .nav-sub>li a {
        padding: 0 7px
    }
}

@media screen and (max-width:68.75em) {
    .wf-active[lang=fr] .prepaid #nav-primary .nav-sub>li a {
        font-size: 19px
    }
}

@media screen and (max-width:57.188em) {
    .wf-active[lang=fr] .prepaid #nav-primary .nav-sub>li a {
        padding: 0 6px
    }
}

@media screen and (max-width:54.375em) {
    .wf-active[lang=fr] .prepaid #nav-primary .nav-sub>li a {
        font-size: 16px;
        padding: 0 5px
    }
}

#nav-primary .nav-sub>li a:hover,
#nav-primary .nav-sub>li a:active,
#nav-primary .nav-sub>li a:focus {
    color: #e41c11
}

#nav-primary .nav-sub>li a.back-to-top {
    display: none
}

.header-mini #nav-primary .nav-sub>li a.back-to-top {
    font-size: 0;
    position: relative;
    top: 11px;
    left: 7px;
    display: block;
    padding: 0;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -380px 0;
    width: 25px;
    height: 24px
}

.no-svg .header-mini #nav-primary .nav-sub>li a.back-to-top {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -380px 0;
    width: 25px;
    height: 24px
}

.header-mini #nav-primary .nav-sub>li a.back-to-top:hover,
.header-mini #nav-primary .nav-sub>li a.back-to-top:focus {
    background-position: -380px -40px
}

#nav-primary .nav-sub>li.has-flyout,
#nav-primary .nav-sub>li.phone-flyout {
    position: relative
}

#nav-primary .nav-sub>li.has-flyout.open .nav-flyout,
#nav-primary .nav-sub>li.phone-flyout.open .nav-flyout {
    display: block
}

.no-js #nav-primary .nav-sub>li.has-flyout:hover .nav-flyout,
.no-js #nav-primary .nav-sub>li.phone-flyout:hover .nav-flyout {
    display: block
}

#nav-primary .nav-sub>li.has-flyout:hover>a:after,
#nav-primary .nav-sub>li.phone-flyout:hover>a:after {
    background-position: -140px -48px
}

.ch #nav-primary .nav-sub>li.has-flyout:hover>a,
.ch #nav-primary .nav-sub>li.phone-flyout:hover>a {
    color: #000
}

#nav-primary .nav-sub>li.has-flyout>a:focus+.nav-flyout,
#nav-primary .nav-sub>li.phone-flyout>a:focus+.nav-flyout {
    display: block
}

#nav-primary .nav-sub>li.has-flyout>a:after,
#nav-primary .nav-sub>li.phone-flyout>a:after {
    margin-left: .5em;
    content: '';
    display: inline-block;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -140px -8px;
    width: 15px;
    height: 15px
}

.no-svg #nav-primary .nav-sub>li.has-flyout>a:after,
.no-svg #nav-primary .nav-sub>li.phone-flyout>a:after {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -140px -8px;
    width: 15px;
    height: 15px
}

@media screen and (max-width:47.9375em) {
    #nav-primary .nav-sub>li.has-flyout>a,
    #nav-primary .nav-sub>li.phone-flyout>a {
        padding: 8px 10px;
        position: relative
    }
    #nav-primary .nav-sub>li.has-flyout>a:after,
    #nav-primary .nav-sub>li.phone-flyout>a:after {
        position: absolute;
        right: 10px;
        top: 12px;
        background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
        background-size: 500px 500px;
        background-position: -160px -8px;
        width: 15px;
        height: 15px
    }
    .no-svg #nav-primary .nav-sub>li.has-flyout>a:after,
    .no-svg #nav-primary .nav-sub>li.phone-flyout>a:after {
        background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
        background-position: -160px -8px;
        width: 15px;
        height: 15px
    }
    #nav-primary .nav-sub>li.has-flyout>a:hover:after,
    #nav-primary .nav-sub>li.phone-flyout>a:hover:after {
        background-position: -160px -48px
    }
}

@media screen and (max-width:47.9375em) {
    #nav-primary .nav-sub>li.has-flyout,
    #nav-primary .nav-sub>li.phone-flyout {
        position: static
    }
}

#nav-primary .nav-sub .nav-flyout {
    border: 1px solid #b2b2b2;
    width: 180px;
    position: absolute;
    right: 0;
    padding: 0 20px;
    background: #fff;
    text-align: left
}

#nav-primary .nav-sub .nav-flyout li {
    display: block;
    float: none
}

#nav-primary .nav-sub .nav-flyout a {
    color: #000;
    height: auto;
    font-size: 22px;
    line-height: 22px;
    margin: 22px 0;
    padding: 0
}

#nav-primary .nav-sub .nav-flyout a:hover,
#nav-primary .nav-sub .nav-flyout a:focus {
    color: #e41c11
}

.ch #nav-primary .nav-sub .nav-flyout a {
    color: #000
}

@media screen and (max-width:47.9375em) {
    #nav-primary .nav-sub .nav-flyout {
        border: 0;
        background: 0 0;
        position: absolute;
        top: 0;
        padding: 0;
        bottom: auto;
        left: 100%;
        width: 100%
    }
    #nav-primary .nav-sub .nav-flyout a {
        margin: 0;
        padding: 8px 10px
    }
}

@media screen and (max-width:47.9375em) {
    #nav-primary .nav-sub {
        position: absolute;
        top: 0;
        bottom: auto;
        left: 100%;
        width: 100%;
        -webkit-transition: -webkit-transform 350ms ease;
        transition: transform 350ms ease
    }
    #nav-primary .nav-sub li {
        float: none;
        padding-left: 10px
    }
    #nav-primary .nav-sub li a {
        position: relative;
        height: auto;
        display: block;
        padding: 10px;
        color: #000;
        font-size: 25px;
        line-height: 25px;
        text-transform: none
    }
    .wf-active #nav-primary .nav-sub li a {
        font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
        font-size: 25px;
        line-height: 25px
    }
    .csstransforms .nav-tertiary-open #nav-primary .nav-sub {
        -webkit-transform: translate(-100%, 0);
        -ms-transform: translate(-100%, 0);
        transform: translate(-100%, 0)
    }
    .no-csstransforms .nav-tertiary-open #nav-primary .nav-sub {
        position: relative;
        left: -100%
    }
}

@media screen and (max-width:47.9375em) {
    #nav-primary {
        border: 0;
        -webkit-transition: -webkit-transform 350ms ease;
        transition: transform 350ms ease
    }
    .csstransforms .nav-sub-open.nav-open #nav-primary {
        -webkit-transform: translate(-100%, 0);
        -ms-transform: translate(-100%, 0);
        transform: translate(-100%, 0)
    }
    .no-csstransforms .nav-sub-open.nav-open #nav-primary {
        position: relative;
        left: -100%
    }
}

@media screen and (max-width:47.9375em) {
    header.rockstopper #nav-primary {
        display: none
    }
}

.monthly #nav-monthly .nav-heading,
.prepaid #nav-prepaid .nav-heading {
    position: relative;
    color: #fff
}

.monthly #nav-monthly .nav-heading+.nav-sub,
.prepaid #nav-prepaid .nav-heading+.nav-sub {
    display: block
}

@media screen and (max-width:47.9375em) {
    .monthly #nav-monthly .nav-heading,
    .prepaid #nav-prepaid .nav-heading {
        color: #000
    }
}

.monthly #nav-monthly .nav-heading {
    background-color: #e41c11
}

@media screen and (max-width:47.9375em) {
    .monthly #nav-monthly .nav-heading {
        background-color: transparent
    }
}

.prepaid #nav-prepaid .nav-heading {
    background-color: #000
}

@media screen and (max-width:47.9375em) {
    .prepaid #nav-prepaid .nav-heading {
        background-color: transparent
    }
}

.prepaid #nav-primary .nav-sub>li a {
    padding: 0 8px
}

.prepaid .header-mini #nav-primary .nav-sub>li a {
    padding: 0 12px
}

.prepaid #nav-prepaid .nav-heading:before {
    left: -.286em;
    border-top-left-radius: .286em
}

.mobile-nav-menu-item {
    position: relative;
    height: auto;
    display: block;
    padding: 10px;
    color: #000;
    font-size: 25px;
    line-height: 25px;
    text-transform: none
}

.wf-active .mobile-nav-menu-item {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 25px;
    line-height: 25px
}

#mobile-nav-heading {
    padding-bottom: 5px
}

.wf-active #mobile-nav-heading {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 36px
}

#mobile-nav-tools {
    display: none;
    padding: 20px 10px
}

#mobile-nav-tools a {
    position: relative;
    height: auto;
    display: block;
    padding: 10px;
    color: #000;
    font-size: 25px;
    line-height: 25px;
    text-transform: none
}

.wf-active #mobile-nav-tools a {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 25px;
    line-height: 25px
}

#mobile-nav-tools a:first-child {
    border-top: 1px solid #b2b2b2;
    padding-top: 15px
}

header.rockstopper #mobile-nav-tools a:first-child {
    border-top: 0;
    padding-top: 0
}

@media screen and (max-width:47.9375em) {
    #mobile-nav-tools {
        display: block;
        -webkit-transition: -webkit-transform 350ms ease;
        transition: transform 350ms ease
    }
    .csstransforms .nav-sub-open.nav-open #mobile-nav-tools {
        -webkit-transform: translate(-100%, 0);
        -ms-transform: translate(-100%, 0);
        transform: translate(-100%, 0)
    }
    .no-csstransforms .nav-sub-open.nav-open #mobile-nav-tools {
        position: relative;
        left: -100%
    }
}

#mobile-back-button {
    display: none
}

@media screen and (max-width:47.9375em) {
    #mobile-back-button {
        padding: 0 20px
    }
    .wf-active #mobile-back-button {
        font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
        font-size: 23px
    }
    .nav-sub-open #mobile-back-button,
    .nav-tertiary-open #mobile-back-button {
        display: block
    }
    #mobile-back-button a {
        color: #000;
        position: relative;
        display: block;
        padding: 20px;
        border-bottom: 1px solid #b2b2b2;
        margin-bottom: 10px
    }
    #mobile-back-button a:before {
        content: '';
        display: block;
        position: absolute;
        left: 0;
        top: 27px;
        background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
        background-size: 500px 500px;
        background-position: -120px -8px;
        width: 15px;
        height: 15px
    }
    .no-svg #mobile-back-button a:before {
        background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
        background-position: -120px -8px;
        width: 15px;
        height: 15px
    }
}

#page-footer {
    padding: 2em 0 0;
    position: relative
}

#page-footer a {
    color: #fff;
    text-transform: uppercase;
    font-family: Arial, Helvetica, sans-serif;
    font-size: .75em
}

.wf-active #page-footer a {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 18px;
    letter-spacing: .03em
}

#page-footer a:hover,
#page-footer a:active {
    color: #e41c11
}

.ch #page-footer a {
    text-decoration: underline
}

#page-footer a:link,
#page-footer a:visited {
    text-decoration: none
}

.ch #page-footer a:link,
.ch #page-footer a:visited {
    text-decoration: underline
}

#page-footer a:hover,
#page-footer a:focus {
    color: #e41c11
}

#page-footer ul {
    margin: .25em 0;
    padding: 0;
    list-style: none
}

#page-footer .links-social {
    float: right
}

#page-footer .links-social a {
    color: #000;
    display: block;
    float: left;
    position: relative;
    line-height: 32px;
    margin: 0 15px
}

#page-footer .links-social a:hover,
#page-footer .links-social a:focus {
    color: #e41c11
}

#page-footer .links-social .join-us {
    padding-right: 42px
}

#page-footer .links-social .join-us span {
    position: absolute;
    top: 0;
    right: 0;
    display: block;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: 0 0;
    width: 32px;
    height: 32px
}

.no-svg #page-footer .links-social .join-us span {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: 0 0;
    width: 32px;
    height: 32px
}

#page-footer .links-social .join-us:hover span,
#page-footer .links-social .join-us:focus span {
    background-position: 0 -40px
}

@media screen and (max-width:47.9375em) {
    #page-footer .links-social {
        float: none;
        clear: both;
        display: table;
        margin: 0 auto;
        padding: 10px 0
    }
}

#footer-legal {
    font-size: 1.25em;
    color: #888
}

#footer-legal p {
    margin: 0 0 1.5em 20px;
    font-size: .6875em
}

@media screen and (max-width:47.9375em) {
    #footer-legal p {
        margin-bottom: 0;
        margin-left: 0;
        padding-top: 1em
    }
}

#footer-legal ul {
    margin: 20px auto;
    padding: 0;
    width: 100%;
    max-width: 1045px
}

#footer-legal ul:before,
#footer-legal ul:after {
    display: table;
    content: ""
}

#footer-legal ul:after {
    clear: both
}

#footer-legal ul:before,
#footer-legal ul:after {
    display: table;
    content: ""
}

#footer-legal ul:after {
    clear: both
}

[lang=fr] #footer-legal ul {
    width: auto;
    text-align: center;
    margin: 10px 0;
    max-width: none
}

@media screen and (max-width:54.375em) {
    #footer-legal ul {
        width: auto;
        text-align: center;
        margin: 10px 0
    }
}

#footer-legal li {
    float: left;
    padding: 0 24px
}

#footer-legal li.first {
    padding-left: 0
}

#footer-legal li.last {
    padding-right: 0
}

[lang=fr] #footer-legal li {
    padding: 0;
    float: none;
    display: inline-block;
    text-align: center;
    padding: 0 10px;
    margin: 0 0 5px
}

[lang=fr] #footer-legal li:last-child {
    border: 0
}

@media screen and (max-width:54.375em) {
    #footer-legal li {
        padding: 0;
        float: none;
        display: inline-block;
        text-align: center;
        border-right: 1px solid #b2b2b2;
        padding: 0 10px;
        margin: 0 0 5px
    }
    #footer-legal li:last-child {
        border: 0
    }
}

@media screen and (max-width:47.9375em) {
    #footer-legal {
        padding: .5em 0 1em;
        text-align: center
    }
}

#footer-top>.container_12 {
    border-top: 1px solid #b2b2b2;
    padding: 12px 0
}

@media screen and (max-width:47.9375em) {
    #footer-top>.container_12 {
        padding: 5px 0 0
    }
}

@media screen and (max-width:47.9375em) {
    #footer-top {
        padding: 0 10px
    }
}

#footer-bottom {
    background: #000;
    font-family:FuturaLT-Condensed,"Trebuchet MS",Arial,Helvetica,sans-serif;
}

#footer-display-controls {
    color: #000;
    padding-left: 20px
}

#footer-display-controls .control {
    float: left;
    margin-right: 45px
}

#footer-display-controls .control a {
    background-color: transparent;
    border: 0;
    overflow: hidden;
    display: block
}

#footer-display-controls .control a:before {
    content: "";
    display: block;
    width: 0;
    height: 150%
}

#footer-display-controls .label {
    padding-top: 6px;
    float: left
}

#footer-display-controls .contrast {
    margin-top: 2px;
    float: left
}

#footer-display-controls .contrast li {
    display: block;
    float: left
}

#footer-display-controls .contrast li a {
    background-color: transparent;
    border: 0;
    overflow: hidden;
    display: block
}

#footer-display-controls .contrast li a:before {
    content: "";
    display: block;
    width: 0;
    height: 150%
}

#footer-display-controls .contrast .cn a {
    margin: 0 10px;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -220px -2px;
    width: 27px;
    height: 27px
}

.no-svg #footer-display-controls .contrast .cn a {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -220px -2px;
    width: 27px;
    height: 27px
}

#footer-display-controls .contrast .cn:hover a,
#footer-display-controls .contrast .cn.active a,
#footer-display-controls .contrast .cn a:focus {
    background-position: -220px -42px
}

#footer-display-controls .contrast .ch a {
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -180px -2px;
    width: 27px;
    height: 27px
}

.no-svg #footer-display-controls .contrast .ch a {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -180px -2px;
    width: 27px;
    height: 27px
}

#footer-display-controls .contrast .ch:hover a,
#footer-display-controls .contrast .ch.active a,
#footer-display-controls .contrast .ch a:focus {
    background-position: -180px -42px
}

#footer-display-controls .size {
    margin: 0;
    float: left
}

#footer-display-controls .size li {
    display: block;
    float: left
}

#footer-display-controls .size li .disabled {
    cursor: not-allowed
}

#footer-display-controls .size li.font-size-down a {
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -80px -1px;
    width: 40px;
    height: 30px
}

.no-svg #footer-display-controls .size li.font-size-down a {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -80px -1px;
    width: 40px;
    height: 30px
}

#footer-display-controls .size li.font-size-down:hover a,
#footer-display-controls .size li.font-size-down.active a {
    background-position: -80px -41px
}

#footer-display-controls .size li.font-size-down:hover a.disabled,
#footer-display-controls .size li.font-size-down.active a.disabled {
    background-position: -80px -1px
}

#footer-display-controls .size li.font-size-up {
    margin-left: -5px
}

#footer-display-controls .size li.font-size-up a {
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -40px -1px;
    width: 40px;
    height: 30px
}

.no-svg #footer-display-controls .size li.font-size-up a {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -40px -1px;
    width: 40px;
    height: 30px
}

#footer-display-controls .size li.font-size-up:hover a,
#footer-display-controls .size li.font-size-up.active a,
#footer-display-controls .size li.font-size-up a:focus {
    background-position: -40px -41px
}

#footer-display-controls .size li.font-size-up:hover a.disabled,
#footer-display-controls .size li.font-size-up.active a.disabled,
#footer-display-controls .size li.font-size-up a:focus.disabled {
    background-position: -40px -1px
}

@media screen and (max-width:47.9375em) {
    #footer-display-controls {
        display: table;
        margin: 0 auto;
        padding-bottom: 5px;
        padding-left: 0
    }
    #footer-display-controls .control {
        margin: 0
    }
    #footer-display-controls .control:first-child {
        margin-right: 20px
    }
    #footer-display-controls:after {
        display: block;
        content: ' ';
        width: 100%;
        position: absolute;
        left: 0;
        top: 42px;
        border-bottom: 1px solid #b2b2b2
    }
}

.checkmark {
    display: block;
    width: .8em;
    margin: .5em auto 0;
    font-size: 150%;
    font-weight: 400
}

.tabs-nav {
    list-style: none!important;
    margin-left: 0!important;
    padding-left: 0;
    white-space: nowrap
}

.tabs-nav li {
    display: inline-block;
    list-style: none!important;
    vertical-align: top
}

.tabs-nav li a {
    border-top-right-radius: .714em;
    border-top-left-radius: .714em;
    padding: .714em 1.429em;
    display: block;
    min-height: 2.714em;
    background-color: #bbb;
    background-repeat: repeat-x;
    background-image: -webkit-linear-gradient(top, #eee, #bbb);
    background-image: linear-gradient(to bottom, #eee, #bbb);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#ffeeeeee', endColorstr='#ffbbbbbb', GradientType=0);
    color: #666;
    font-size: 1.25em;
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    text-decoration: none;
    text-transform: uppercase
}

.tabs-nav li a span {
    display: block
}

.ch .tabs-nav li a {
    text-decoration: underline
}

.tabs-nav li.active a {
    background-color: #fff;
    background-repeat: repeat-x;
    background-image: -webkit-linear-gradient(top, #f3f3f3, #fff);
    background-image: linear-gradient(to bottom, #f3f3f3, #fff);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#fff3f3f3', endColorstr='#ffffffff', GradientType=0);
    color: #e41c11
}

.tabs-navigation {
    list-style: none!important;
    margin-left: 0!important;
    padding-left: 0;
    white-space: nowrap
}

.tabs-navigation li {
    display: inline-block;
    list-style: none!important;
    vertical-align: top;
    border-top-right-radius: .714em;
    border-top-left-radius: .714em;
    padding: .714em 1.429em;
    min-height: 2.714em;
    background-color: #bbb;
    background-repeat: repeat-x;
    background-image: -webkit-linear-gradient(top, #eee, #bbb);
    background-image: linear-gradient(to bottom, #eee, #bbb);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#ffeeeeee', endColorstr='#ffbbbbbb', GradientType=0);
    color: #666;
    font-size: 1.25em;
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    text-decoration: none;
    text-transform: uppercase
}

.tabs-navigation li span {
    display: block
}

.ch .tabs-navigation li {
    text-decoration: underline
}

.tabs-navigation li[aria-selected=true] {
    background-color: #fff;
    background-repeat: repeat-x;
    background-image: -webkit-linear-gradient(top, #f3f3f3, #fff);
    background-image: linear-gradient(to bottom, #f3f3f3, #fff);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#fff3f3f3', endColorstr='#ffffffff', GradientType=0);
    color: #e41c11
}

.modal-inline {
    display: none
}

.action-items {
    margin-top: 1em;
    margin-bottom: 1em
}

.action-items dt {
    max-width: 70%;
    float: left
}

.action-items dt h1,
.action-items dt h2,
.action-items dt h3,
.action-items dt h4,
.action-items dt h5,
.action-items dt h6 {
    margin: 0
}

.action-items dt p {
    margin: .5em 0;
    font-size: 1.25em
}

.action-items dd {
    margin-left: 0;
    float: right
}

.action-items dd p {
    margin: 0
}

.links-interior-nav {
    margin: 0;
    padding: 0 0 0 20px
}

.wf-active .links-interior-nav {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1.625em
}

.links-interior-nav li {
    margin-bottom: 1.25em;
    position: relative;
    list-style: none;
    text-transform: uppercase
}

.links-interior-nav li.active a:link,
.links-interior-nav li.active a:visited {
    color: #e41c11
}

.ch .links-interior-nav li.active a:link,
.ch .links-interior-nav li.active a:visited {
    color: #000
}

.links-interior-nav li.active a:link:before,
.links-interior-nav li.active a:visited:before {
    display: block;
    position: absolute;
    top: .286em;
    left: -20px;
    content: '';
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -160px -48px;
    width: 15px;
    height: 15px
}

.no-svg .links-interior-nav li.active a:link:before,
.no-svg .links-interior-nav li.active a:visited:before {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -160px -48px;
    width: 15px;
    height: 15px
}

.links-interior-nav a:link,
.links-interior-nav a:visited {
    text-decoration: none;
    color: #000
}

.ch .links-interior-nav a:link,
.ch .links-interior-nav a:visited {
    text-decoration: underline
}

.links-interior-nav a:hover {
    color: #e41c11
}

.callout {
    margin: 1em 0;
    padding: 20px 0;
    display: block;
    border-top: 1px solid #ccc
}

@media screen and (max-width:47.9375em) {
    .callout {
        border: 0
    }
}

.callout h3 {
    margin: 0 0 .5em;
    color: #e41c11;
    font-size: 1.375em;
    text-transform: uppercase
}

.callout.alt {
    background-color: #e41c11;
    color: #fff
}

.callout.alt h3 {
    color: #fff
}

.callout.alt h2 {
    color: #fff
}

.callout .button {
    padding-left: 1.5em!important;
    padding-right: 1.5em!important
}

.accordion {
    margin: 0 0 1em
}

.accordion dt {
    margin: 1em 0;
    padding-left: 10px;
    position: relative;
    cursor: pointer;
    text-transform: uppercase
}

.wf-active .accordion dt {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1.5em
}

.accordion dt:first-child {
    margin-top: 0
}

.accordion dt:before {
    position: absolute;
    top: .286em;
    left: -10px;
    content: '';
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -160px -8px;
    width: 15px;
    height: 15px
}

.no-svg .accordion dt:before {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -160px -8px;
    width: 15px;
    height: 15px
}

.accordion dt.open+dd {
    display: block
}

.accordion dt.open:before {
    background-position: -140px -8px
}

.accordion dd {
    margin: 0;
    display: none
}

.accordion-plus-icons {
    border-bottom: 1px solid #b2b2b2
}

.accordion-plus-icons dt {
    border-top: 1px solid #b2b2b2;
    padding-left: 58px;
    padding-top: 24px;
    padding-bottom: 22px;
    margin-bottom: 0;
    margin-top: 0;
    text-transform: none;
    font-family: FuturaLT-CondensedBold, "Trebuchet MS", Arial, Helvetica, sans-serif!important;
    font-size: 22px!important;
    letter-spacing: 0
}

.accordion-plus-icons dt:before {
    content: '';
    left: 12px;
    top: 20px;
    position: absolute;
    display: block;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -447px 1px;
    width: 34px;
    height: 34px
}

.no-svg .accordion-plus-icons dt:before {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -447px 1px;
    width: 34px;
    height: 34px
}

.accordion-plus-icons dt.open:before {
    content: '';
    left: 12px;
    top: 20px;
    position: absolute;
    display: block;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -407px 1px;
    width: 34px;
    height: 34px
}

.no-svg .accordion-plus-icons dt.open:before {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -407px 1px;
    width: 34px;
    height: 34px
}

.accordion-plus-icons dd {
    padding-left: 58px
}

.accordion-plus-icons dd p:first-child {
    margin-top: 0
}

.two-tone {
    margin: .5em 0;
    color: #e41c11;
    font-size: 2.25em;
    line-height: 1em;
    text-transform: uppercase
}

.two-tone span {
    color: #000
}

.ch .two-tone span {
    color: #000
}

.tile-thumb+.two-tone {
    margin-right: 100px
}

.media-centre-secondary .grid_4 {
    width: 100%
}

.media-centre-secondary p {
    margin: 1em 0
}

.store-search-form:before,
.store-search-form:after {
    display: table;
    content: ""
}

.store-search-form:after {
    clear: both
}

.store-search-form:before,
.store-search-form:after {
    display: table;
    content: ""
}

.store-search-form:after {
    clear: both
}

.store-search-form .checkbox {
    margin-bottom: 10px
}

.store-search-form label[for=city-or-postal] {
    position: absolute;
    overflow: hidden;
    clip: rect(0 0 0 0);
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    border: 0
}

.store-search-form fieldset {
    margin: 0;
    padding: 0;
    border: 0
}

.store-search-form .checkbox label {
    display: inline
}

.store-search-form input[type=text] {
    -webkit-appearance: none;
    appearance: none;
    float: left;
    width: 75%;
    height: 50px;
    padding: 0 .5em;
    border: 1px solid #ccc;
    border-radius: 0;
    font-size: 1.25em
}

@media screen and (max-width:47.9375em) {
    .store-search-form input[type=text] {
        font-family: FuturaLT-CondensedBold, "Trebuchet MS", Arial, Helvetica, sans-serif
    }
}

.store-search-form .input-wrap input[type=text] {
    border-right: 0
}

.store-search-form .input-wrap button[type=submit] {
    background-color: transparent;
    border: 0;
    overflow: hidden;
    position: relative;
    width: 25%;
    height: 50px;
    padding: 0;
    float: left;
    background: #e41c11
}

.store-search-form .input-wrap button[type=submit]:before {
    content: "";
    display: block;
    width: 0;
    height: 150%
}

.store-search-form .input-wrap button[type=submit]:after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -14px 0 0 -14px;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -452px -80px;
    width: 28px;
    height: 29px
}

.no-svg .store-search-form .input-wrap button[type=submit]:after {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -452px -80px;
    width: 28px;
    height: 29px
}

.store-search-form .errorMessage {
    margin-bottom: 1em;
    color: #e41c11
}

.store-search-form .form-row .parsley-error-list {
    bottom: 0;
    float: left;
    clear: both;
    position: relative
}

.store-search-form .input-wrap .parsley-error-list {
    bottom: 0;
    float: left;
    clear: both;
    position: absolute
}

.store-search-form input.parsley-error {
    border: 1px solid #e41c11;
    background: #fef6f6
}

#store-locator-results-wrap .parsley-error-list {
    display: none!important
}

.wrapped-img {
    margin: 2em 0
}

.wrapped-img.left h2,
.wrapped-img.left h3,
.wrapped-img.left p {
    margin-left: 300px
}

.wrapped-img.left img {
    float: left
}

.wrapped-img.right h3,
.wrapped-img.right p {
    margin-right: 300px
}

.wrapped-img.right img {
    float: right
}

.wrapped-img img {
    display: block;
    max-width: 275px
}

.wrapped-img h2,
.wrapped-img h3 {
    margin: 0;
    text-transform: uppercase
}

.wf-active .wrapped-img h2,
.wf-active .wrapped-img h3 {
    font-size: 2em
}

@media screen and (max-width:47.9375em) {
    .wrapped-img h2,
    .wrapped-img h3 {
        margin: .25em 0 0
    }
}

.wrapped-img h2 {
    color: #df2625;
    margin-top: 0;
    margin-bottom: 0
}

.wf-active .wrapped-img h2 {
    font-size: 2.5em
}

.wrapped-img p {
    margin: .5em 0
}

.wrapped-img .subhead {
    margin: 1em 0;
    font-size: 1.333em;
    color: #555
}

.sub-notes dd {
    margin-left: 0
}

ul.sub-notes,
.sub-notes ul {
    padding-left: 2.5em
}

#mainPageContentContainer #genericContent+.grid_4 .promo-tiles-interior img {
    margin-right: 0
}

.mainTabbedContainer {
    position: relative;
    top: -47px
}

.genericIntroBanner {
    height: 200px;
    margin-left: 10px;
    padding: 0;
    overflow: hidden
}

.iconPDF:before {
    display: block;
    float: left;
    content: '';
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -360px -80px;
    width: 25px;
    height: 33px
}

.no-svg .iconPDF:before {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -360px -80px;
    width: 25px;
    height: 33px
}

.collapsibleItems {
    position: relative
}

.collapsibleItems .item,
.collapsibleItems .noncollapseitem {
    padding: 6px 0 0;
    border-top: 1px solid #ccc;
    zoom: 1
}

.collapsibleItems .item sup,
.collapsibleItems .noncollapseitem sup {
    *position: static;
    *vertical-align: text-top;
    *line-height: .2em
}

.collapsibleItems .item h3,
.collapsibleItems .noncollapseitem h3 {
    width: auto;
    margin: 0 0 10px!important;
    font-weight: 400!important;
    text-transform: uppercase;
    cursor: pointer;
    padding-right: 100px;
    line-height: 1.3em!important
}

[lang=fr] .collapsibleItems .item h3,
[lang=fr] .collapsibleItems .noncollapseitem h3 {
    padding-right: 145px
}

.collapsibleItems .item .content {
    margin-right: 30px;
    zoom: 1
}

.collapsibleItems .iconPDF {
    padding-left: 28px
}

.collapsibleItems .toggleLink {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    visibility: hidden;
    position: static;
    display: inline;
    float: right;
    width: 22px;
    height: 22px;
    border: 1px solid #eae8e8;
    border-radius: 4px;
    text-decoration: none;
    text-align: center;
    color: #918f90;
    font-size: 16px;
    background-color: #f7f7f7;
    background-repeat: repeat-x;
    background-image: -webkit-linear-gradient(top, #fff, #f7f7f7);
    background-image: linear-gradient(to bottom, #fff, #f7f7f7);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#fff7f7f7', GradientType=0)
}

.collapsibleItems .toggleLink:after {
    content: "+"
}

.collapsibleItems .toggleLink span {
    display: block;
    position: absolute;
    right: 27px;
    padding-right: 5px;
    font-size: .875em;
    line-height: 22px;
    height: 22px;
    white-space: nowrap;
    color: #e41c11!important
}

.collapsibleItems .collapse:after {
    content: "â€“"
}

#mktg-prefs-form .form-actions {
    margin: 1em 0 2em;
    text-align: left
}

.form-row {
    clear: both;
    position: relative
}

.form-row:before,
.form-row:after {
    display: table;
    content: ""
}

.form-row:after {
    clear: both
}

.form-row:before,
.form-row:after {
    display: table;
    content: ""
}

.form-row:after {
    clear: both
}

.form-row+.form-row,
.form-row+.form-actions {
    margin-top: .75em
}

.form-actions {
    text-align: right
}

label {
    display: block;
    margin-bottom: .25em;
    cursor: pointer
}

label.invisible {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px
}

label.checkbox {
    margin: .167em 0 .667em
}

input[type=text],
input[type=email],
select {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: .25em
}

.parsley-error-list {
    position: absolute;
    bottom: 30px;
    list-style: none;
    margin: .5em 0;
    padding: .167em .5em;
    color: #e41c11;
    border: 1px solid #e41c11;
    border-radius: 5px
}

.parsley-error-list li {
    margin: .333em 0
}

#mktg-prefs-form .parsley-error-list {
    position: relative;
    bottom: 0
}

.js #home-hero-outer {
    margin-top: 2.5em;
    border-bottom: 2px solid #fff;
    position: relative;
    background-color: #fff
}

.js #home-hero-outer:before {
    height: 5em;
    position: absolute;
    top: 385px;
    right: 0;
    left: 0;
    background-color: #eee;
    content: ''
}

@media screen and (max-width:47.9375em) {
    .js #home-hero-outer {
        border-bottom: 0
    }
    .js #home-hero-outer:before {
        display: none
    }
}

.js #home-hero {
    height: 450px
}

#home-hero {
    margin: 0 auto;
    width: 100%;
    position: relative
}

#home-hero .pause-play {
    text-align: right
}

#home-hero .pause-play .pause,
#home-hero .pause-play .play {
    margin-right: 5px;
    width: 25px;
    height: 25px;
    display: inline-block
}

#home-hero .pause-play .pause:before,
#home-hero .pause-play .play:before {
    background-image: url(../images/pause.svg);
    background-repeat: no-repeat;
    width: 100%;
    height: 100%;
    content: ' ';
    display: inline-block
}

#home-hero .pause-play .pause:before {
    background-image: url(../images/pause.svg)
}

#home-hero .pause-play .play:before {
    background-image: url(../images/play.svg)
}

#home-hero .controls {
    width: 100%;
    height: 62px;
    position: absolute;
    bottom: 0;
    left: 10px
}

@media screen and (max-width:47.9375em) {
    #home-hero .controls {
        height: auto;
        bottom: 20px;
        text-align: center
    }
}

#home-hero .controls ul,
#home-hero .controls li {
    margin: 0;
    padding: 0;
    list-style: none
}

#home-hero .controls li {
    width: 25%;
    float: left;
    display: inline-block
}

@media screen and (max-width:47.9375em) {
    #home-hero .controls li {
        width: auto;
        float: none
    }
}

#home-hero .controls a {
    display: block;
    padding: 20px 14px 7px;
    height: 4.429em;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    color: #666;
    font-size: 1.25em;
    line-height: 1.2;
    text-align: center;
    text-decoration: none;
    overflow: hidden
}

.ch #home-hero .controls a {
    text-decoration: underline
}

#home-hero .controls a.active {
    color: #000
}

.ch #home-hero .controls a.active {
    color: #000
}

#home-hero .controls a:before {
    width: 2px;
    height: 4.429em;
    display: block;
    position: absolute;
    top: 17px;
    left: 0;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat -153px 0;
    content: ''
}

#home-hero .controls a:first-child:before {
    display: none
}

@media screen and (max-width:47.9375em) {
    #home-hero .controls a {
        margin-right: 3px;
        margin-left: 3px;
        border-radius: .714em;
        padding: 0;
        width: .714em;
        height: .714em;
        display: inline-block;
        float: none;
        background-color: #ccc;
        text-indent: 100%;
        white-space: nowrap;
        vertical-align: middle
    }
    #home-hero .controls a.active {
        background-color: #000
    }
}

#home-hero .controls .timer {
    height: 6px;
    display: block;
    position: absolute;
    top: .25em;
    left: 4em;
    right: 4em;
    background-color: #ccc;
    text-align: left;
    overflow: hidden
}

#home-hero .controls .timer .bar {
    width: 0;
    height: 6px;
    display: block;
    background-color: #e41c11
}

#home-hero .slideWrapper {
    margin-left: 10px;
    width: 100%;
    height: 340px;
    position: relative;
    z-index: 0
}

@media screen and (max-width:47.9375em) {
    #home-hero .slideWrapper {
        height: auto;
        overflow: visible
    }
}

#home-hero .slide {
    width: 100%;
    height: 340px;
    position: absolute;
    top: 0;
    left: 0;
    background-repeat: no-repeat;
    background-color: #e41c11;
    border: 4px solid #000
}

@media screen and (max-width:47.9375em) {
    #home-hero .slide {
        height: auto;
        background-position: 50% 0;
        background-size: auto 200px
    }
}

#home-hero .slide .heroText {
    display: block;
    margin: 45px 10px 0 365px;
    color: #666
}

#home-hero .slide .heroText .red {
    display: inline;
    padding: 0;
    color: #e41c11;
    background: 0 0
}

#home-hero .slide.layout2 .heroText {
    margin: 45px 320px 0 20px
}

#home-hero .slide.layout4 .heroText {
    margin: 45px 10px 0 450px
}

#home-hero .slide.layout2 {
    background-position: right!important
}

@media screen and (max-width:47.9375em) {
    #home-hero .slide .heroText,
    #home-hero .slide.layout1 .heroText,
    #home-hero .slide.layout2 .heroText,
    #home-hero .slide.layout3 .heroText,
    #home-hero .slide.layout4 .heroText {
        margin: 200px 0 0;
        padding: 34px 12px 50px;
        color: #000
    }
    #home-hero .slide.no-mobileimage {
        color: #fff
    }
    #home-hero .slide.no-mobileimage .heroText {
        margin-top: 0
    }
}

#home-hero .slide h1 {
    margin: 0 0 20px;
    background: 0 0;
    font-size: 2.9375em;
    line-height: .95em;
    text-transform: uppercase;
    color: #fff;
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif
}

@media screen and (max-width:47.9375em) {
    #home-hero .slide h1 {
        margin: -.167em 0 .333em;
        font-size: 2.25em
    }
}

.ch #home-hero .slide h1 {
    color: #000
}

#home-hero .slide .description {
    margin: 0 0 1.333em;
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1.25em;
    color: #fff
}

#home-hero .slide .description .hero-plus {
    float: left;
    padding-right: 10px
}

@media screen and (max-width:47.9375em) {
    #home-hero .slide .description {
        font-size: 1em
    }
}

#home-hero .slide .button {
    font-size: 1.5em
}

@media screen and (max-width:47.9375em) {
    #home-hero .slide .button {
        font-size: 1.25em
    }
}

#home-hero .slides {
    display: none
}

@media screen and (max-width:47.9375em) {
    .js #home-hero {
        padding: 0;
        min-height: 390px;
        height: auto
    }
}

@media screen and (max-width:54.375em) {
    #home-hero {
        width: 100%
    }
    #home-hero .slideWrapper,
    #home-hero .slide,
    #home-hero .controls {
        width: 100%
    }
    #home-hero .slideWrapper {
        margin-left: 0
    }
    #home-hero .controls {
        left: 0
    }
}

.promo-tiles-interior,
.promo-tiles-home {
    padding: 1.5em 0;
    background-color: #f7f7f7
}

.promo-tiles-interior .tile-thumb,
.promo-tiles-home .tile-thumb {
    float: right;
    width: 89px;
    height: 100px;
    margin-top: 1em
}

.plans-features .promo-tiles-interior .tile-thumb,
.plans-features .promo-tiles-home .tile-thumb {
    float: left;
    width: 130px;
    height: auto
}

.phone-listing .promo-tiles-interior .tile-thumb,
.phone-listing .promo-tiles-home .tile-thumb {
    width: 130px;
    height: auto
}

.promo-tiles-interior .tile-copy,
.promo-tiles-home .tile-copy {
    margin-right: 2em
}

.promo-tiles-interior .button:link,
.promo-tiles-home .button:link {
    font-size: 1.5em
}

@media screen and (max-width:47.9375em) {
    .promo-tiles-interior .tile-copy,
    .promo-tiles-home .tile-copy {
        margin-right: 0
    }
    .promo-tiles-interior .tile-thumb,
    .promo-tiles-home .tile-thumb {
        margin-top: 0
    }
    .promo-tiles-interior .grid_4+.grid_4,
    .promo-tiles-home .grid_4+.grid_4 {
        margin-top: 2em
    }
}

.plans-features .promo-tiles-interior {
    clear: both;
    padding: 1.5em 0;
    background: 0 0
}

.plans-features .promo-tiles-interior:before,
.plans-features .promo-tiles-interior:after {
    display: table;
    content: ""
}

.plans-features .promo-tiles-interior:after {
    clear: both
}

.plans-features .promo-tiles-interior:before,
.plans-features .promo-tiles-interior:after {
    display: table;
    content: ""
}

.plans-features .promo-tiles-interior:after {
    clear: both
}

@media screen and (max-width:47.9375em) {
    .plans-features .promo-tiles-interior .grid_6+.grid_6 {
        margin-top: 2em
    }
}

.plans-features .promo-tiles-interior .tile-thumb {
    float: left;
    width: 130px;
    height: auto;
    margin: 0
}

@media screen and (max-width:47.9375em) {
    .plans-features .promo-tiles-interior .tile-thumb {
        width: 86.66666667px
    }
}

.plans-features .promo-tiles-interior .tile-title {
    margin: 0 0 0 140px;
    font-size: 1.5em;
    text-transform: uppercase;
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif!important
}

@media screen and (max-width:47.9375em) {
    .plans-features .promo-tiles-interior .tile-title {
        margin-left: 93.33333333px
    }
}

.wf-active .plans-features .promo-tiles-interior .tile-title {
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 2.5em
}

.plans-features .promo-tiles-interior p {
    margin: .25em 0 .25em 140px;
    font-size: 1.25em;
    line-height: 1.5em
}

@media screen and (max-width:47.9375em) {
    .plans-features .promo-tiles-interior p {
        margin-left: 93.33333333px
    }
}

.plans-features .promo-tiles-interior a {
    text-decoration: none
}

.grid_6>.plans-features .promo-tiles-interior .grid_6 {
    margin-left: 0
}

#home-text-banner {
    background-color: #fff;
    font-size: 133%;
    line-height: 59px;
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    text-align: center;
    text-transform: uppercase;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

#home-text-banner span {
    color: #e41c11
}

.home-page-wrapper {
    margin-top: 3.75em;
    background: #fff
}

@media screen and (max-width:47.9375em) {
    .home-page-wrapper {
        margin-top: 0;
        background: 0 0
    }
}

.home-row {
    margin-top: 1%;
    margin-bottom: 1%
}

.home-row:before,
.home-row:after {
    display: table;
    content: ""
}

.home-row:after {
    clear: both
}

.home-row:before,
.home-row:after {
    display: table;
    content: ""
}

.home-row:after {
    clear: both
}

.home-row a.button {
    font-size: 1.625em;
    background: #df2625;
    border: 3px solid #000;
    margin: 5px 0 10px
}

.home-row a.button.mobile {
    display: none
}

.home-row:first-child {
    margin-top: 0
}

@media screen and (max-width:47.9375em) {
    .home-row {
        margin: 20px 10px
    }
    .home-row .home-banner+.home-banner {
        margin-top: 20px
    }
}

.home-banner {
    position: relative;
    overflow: hidden
}

@media screen and (max-width:47.9375em) {
    .home-banner {
        overflow: visible
    }
    .home-banner a.button {
        font-size: 1.625em
    }
}

.home-banner:before,
.home-banner:after {
    display: table;
    content: ""
}

.home-banner:after {
    clear: both
}

.home-banner:before,
.home-banner:after {
    display: table;
    content: ""
}

.home-banner:after {
    clear: both
}

.home-banner a.button {
    font-size: 1.75em;
    background: #df2625;
    border: 3px solid #000
}

.home-banner.home-banner-large {
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 2.25em;
    text-transform: uppercase;
    letter-spacing: -.04em;
    min-height: 566px
}

@media screen and (max-width:47.9375em) {
    .home-banner.home-banner-large {
        padding-top: 10px;
        margin-top: 10px;
        border: 0;
        min-height: 0;
        padding-bottom: 1.5em
    }
}

.home-banner.home-banner-large .hero-button {
    position: absolute;
    top: 80px;
    left: 50px;
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    color: #fff;
    background-color: #df2625;
    border: 3px solid #000;
    text-decoration: none;
    padding: 8px 20px;
    letter-spacing: 1px;
    text-transform: uppercase;
    font-size: .722em
}

@media screen and (max-width:47.9375em) {
    .home-banner.home-banner-large .hero-button {
        position: absolute;
        margin-top: 75%;
        left: 0
    }
}

.home-banner.home-banner-large .hero-button:hover {
    background-color: #e41c11
}

.home-banner.home-banner-large .home-banner-block {
    background-color: #e41c11;
    color: #fff;
    margin: 0 240px;
    padding: 75px 60px 0
}

@media screen and (max-width:47.9375em) {
    .home-banner.home-banner-large .home-banner-block {
        margin: 0;
        border: 3px solid #000
    }
}

.home-banner.home-banner-large .home-banner-block .home-banner-wrapper {
    text-align: center
}

.home-banner.home-banner-large .home-banner-block .home-banner-wrapper.space {
    margin-top: 10px
}

.home-banner.home-banner-large .home-banner-block .home-banner-wrapper .home-banner-text {
    border-left: 2px solid #000;
    border-right: 2px solid #000;
    padding: 0;
    display: inline-block
}

.home-banner.home-banner-large .home-banner-block .home-banner-wrapper .home-banner-text .space_20 {
    display: block;
    height: 20px
}

.home-banner.home-banner-large .home-banner-block .home-banner-wrapper .home-banner-text .message-block {
    font-size: .667em;
    text-transform: none;
    padding: 14px 25px 65px 32px;
    max-width: 260px;
    text-align: left;
    letter-spacing: normal
}

.home-banner.home-banner-large .home-banner-block .home-banner-wrapper .home-banner-text span {
    width: auto;
    margin: 0 auto;
    border-left: 2px solid #000;
    border-right: 2px solid #000;
    border-top: 4px solid #000;
    border-bottom: 4px solid #000;
    display: block;
    background-color: #fff;
    color: #e41c11;
    padding: .4em .639em
}

.home-banner.home-banner-large .home-banner-block .home-banner-wrapper .home-banner-text span.plus-icon {
    background: #fff url(../images/pctelecom/Plus_icon_51.png) 10px 13px no-repeat;
    padding: .5em .278em .5em 1.944em
}

.home-banner.home-banner-large .home-banner-block .home-banner-wrapper .home-banner-text span sup {
    font-size: .556em
}

@media screen and (max-width:47.9375em) {
    .home-banner.home-banner-large .home-banner-block .home-banner-wrapper .home-banner-text {
        font-size: .556em;
        letter-spacing: -.01em
    }
    .home-banner.home-banner-large .home-banner-block .home-banner-wrapper .home-banner-text span.plus-icon {
        background-image: url(../images/pctelecom/Plus_icon_25.png)
    }
    .home-banner.home-banner-large .home-banner-block .home-banner-wrapper .home-banner-text .home-banner-text {
        font-size: 1em
    }
}

.home-banner.home-banner-large img {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%
}

@media screen and (max-width:47.9375em) {
    .home-banner.home-banner-large img {
        position: relative
    }
}

.home-banner.home-banner-large .messaging {
    position: absolute;
    top: 20%;
    width: 45%
}

@media screen and (max-width:68.75em) {
    .home-banner.home-banner-large .messaging {
        top: 15%
    }
}

@media screen and (max-width:50em) {
    .home-banner.home-banner-large .messaging {
        top: 10%
    }
}

.home-banner.home-banner-large h1 {
    position: relative;
    color: #e41c11;
    background: #fff;
    margin: 0;
    padding-left: 10%;
    padding-bottom: 8px;
    padding-right: 16px;
    display: inline-block
}

.ch .home-banner.home-banner-large h1 {
    color: #000
}

.wf-active .home-banner.home-banner-large h1 {
    font-size: 3.75em
}

@media screen and (max-width:68.75em) {
    .wf-active .home-banner.home-banner-large h1 {
        font-size: 3.125em
    }
}

@media screen and (max-width:50em) {
    .wf-active .home-banner.home-banner-large h1 {
        font-size: 2.8125em;
        padding-bottom: 6px
    }
}

.home-banner.home-banner-large p {
    color: #fff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, .5);
    margin: 0
}

.wf-active .home-banner.home-banner-large p {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    line-height: 1.25em;
    font-size: 2.25em
}

.ch .home-banner.home-banner-large p {
    margin-top: 0;
    margin-bottom: 0;
    padding: .5em;
    background: #000
}

@media screen and (max-width:47.9375em) {
    .ch .home-banner.home-banner-large p {
        background: 0 0;
        padding: 0
    }
}

@media screen and (max-width:68.75em) {
    .wf-active .home-banner.home-banner-large p {
        font-size: 1.75em
    }
}

@media screen and (max-width:50em) {
    .wf-active .home-banner.home-banner-large p {
        margin-top: .5em;
        margin-bottom: .5em
    }
}

.home-banner.home-banner-large .button {
    margin-left: 10%
}

@media screen and (max-width:47.9375em) {
    .home-banner.home-banner-large {
        margin-top: 0
    }
    .home-banner.home-banner-large img {
        width: 200%!important;
        left: -50%!important;
        max-width: 200%!important
    }
    .home-banner.home-banner-large .messaging {
        width: 100%;
        top: auto;
        position: static
    }
    .home-banner.home-banner-large h1 {
        padding: 0
    }
    .wf-active .home-banner.home-banner-large h1 {
        font-size: 2.8125em
    }
    .home-banner.home-banner-large p {
        color: #000;
        text-shadow: none;
        margin-left: 0
    }
    .wf-active .home-banner.home-banner-large p {
        margin-top: .15em
    }
    .home-banner.home-banner-large .button {
        margin-left: 0
    }
}

.home-banner.home-banner-medium {
    padding-top: 48%;
    width: 48.5%;
    float: left;
    margin-left: 1%;
    margin-right: 1%;
    background-color: #e41c11;
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
    min-height: 582px
}

.home-banner.home-banner-medium:last-child {
    margin-left: 0
}

.home-banner.home-banner-medium img {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%
}

.home-banner.home-banner-medium h2 {
    width: 50%;
    margin: 0;
    position: absolute;
    top: 20px;
    left: 20px;
    color: #fff;
    line-height: 1.15em;
    font-weight: 400;
    text-shadow: 0 1px 3px rgba(0, 0, 0, .5)
}

.ch .home-banner.home-banner-medium h2 {
    background: #000;
    padding: .5em
}

@media screen and (max-width:47.9375em) {
    .ch .home-banner.home-banner-medium h2 {
        background: 0 0;
        padding: 0
    }
}

.wf-active .home-banner.home-banner-medium h2 {
    font-size: 2em
}

@media screen and (max-width:50em) {
    .wf-active .home-banner.home-banner-medium h2 {
        font-size: 1.875em
    }
}

.home-banner.home-banner-medium .button {
    position: absolute;
    bottom: 20px;
    left: 20px
}

@media screen and (max-width:47.9375em) {
    .home-banner.home-banner-medium {
        padding-top: 0;
        width: 100%;
        margin-left: 0;
        margin-right: 0
    }
    .home-banner.home-banner-medium img {
        display: block;
        position: static
    }
    .home-banner.home-banner-medium h2 {
        position: static;
        color: #000;
        text-shadow: none;
        width: 100%;
        margin: .5em 0
    }
    .home-banner.home-banner-medium .button {
        position: static;
        bottom: 20px;
        left: 20px
    }
    .short .home-banner.home-banner-medium {
        padding-top: 0;
        border-top: 1px solid #ccc;
        border-bottom: 1px solid #ccc;
        padding-bottom: 20px
    }
    .short .home-banner.home-banner-medium img {
        display: none
    }
}

.home-banner.home-banner-medium.left {
    padding-top: 23px!important
}

.home-banner.home-banner-medium.right {
    padding-top: 147px!important
}

.home-banner.home-banner-medium.right .home-banner-text span {
    padding: .639em .833em;
    line-height: 1.1em
}

.home-banner.home-banner-medium .home-banner-image {
    padding: 23px 0 0;
    min-height: 422px
}

.home-banner.home-banner-medium .home-banner-image img {
    position: static
}

.home-banner.home-banner-medium .home-banner-image .home-banner-image-text {
    color: #fff;
    position: absolute;
    padding: 0 0 0 30px;
    font-size: 1.5em
}

@media screen and (max-width:47.9375em) {
    .home-banner.home-banner-medium .home-banner-image .home-banner-image-text {
        font-size: 1.25em;
        padding-left: .833em
    }
}

.home-banner.home-banner-medium .home-banner-text {
    border-right: 2px solid #000;
    padding: 0;
    display: inline-block
}

.home-banner.home-banner-medium .home-banner-text span {
    border: 4px solid #000;
    border-left: 0;
    background-color: #fff;
    padding: .639em .833em;
    color: #e41c11;
    font-size: 2.25em;
    text-transform: uppercase;
    display: block
}

@media screen and (max-width:47.9375em) {
    .home-banner.home-banner-medium .home-banner-text {
        width: 90%
    }
    .home-banner.home-banner-medium .home-banner-text span {
        font-size: 1.25em
    }
}

.home-banner.home-banner-medium .home-banner-white-text {
    color: #fff;
    font-size: 1.5em;
    padding: 17px 0 245px 35px
}

@media screen and (max-width:47.9375em) {
    .home-banner.home-banner-medium {
        border: 0;
        background-color: transparent;
        min-height: 0
    }
    .home-banner.home-banner-medium .mobile-border {
        border: 3px solid #000;
        background-color: #e41c11;
        padding-top: 1.438em
    }
    .home-banner.home-banner-medium .mobile-border .home-banner-image {
        min-height: 0
    }
    .home-banner.home-banner-medium a.button {
        margin-top: 10px
    }
    .home-banner.home-banner-medium.right {
        padding-top: 0!important
    }
    .home-banner.home-banner-medium .home-banner-text .home-banner-white-text {
        font-size: 1.25em;
        padding-left: .833em;
        padding-bottom: 4.5em
    }
}

.home-banner.home-banner-small {
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
    padding-top: 48%;
    width: 48.5%;
    float: left;
    margin-left: 1%;
    margin-right: 1%;
    padding-top: 19.825%
}

.home-banner.home-banner-small:last-child {
    margin-left: 0
}

.home-banner.home-banner-small img {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%
}

.home-banner.home-banner-small h2 {
    width: 50%;
    margin: 0;
    position: absolute;
    top: 20px;
    left: 20px;
    color: #fff;
    line-height: 1.15em;
    font-weight: 400;
    text-shadow: 0 1px 3px rgba(0, 0, 0, .5)
}

.ch .home-banner.home-banner-small h2 {
    background: #000;
    padding: .5em
}

@media screen and (max-width:47.9375em) {
    .ch .home-banner.home-banner-small h2 {
        background: 0 0;
        padding: 0
    }
}

.wf-active .home-banner.home-banner-small h2 {
    font-size: 2em
}

@media screen and (max-width:50em) {
    .wf-active .home-banner.home-banner-small h2 {
        font-size: 1.875em
    }
}

.home-banner.home-banner-small .button {
    position: absolute;
    bottom: 20px;
    left: 20px
}

@media screen and (max-width:47.9375em) {
    .home-banner.home-banner-small {
        padding-top: 0;
        width: 100%;
        margin-left: 0;
        margin-right: 0
    }
    .home-banner.home-banner-small img {
        display: block;
        position: static
    }
    .home-banner.home-banner-small h2 {
        position: static;
        color: #000;
        text-shadow: none;
        width: 100%;
        margin: .5em 0
    }
    .home-banner.home-banner-small .button {
        position: static;
        bottom: 20px;
        left: 20px
    }
    .short .home-banner.home-banner-small {
        padding-top: 0;
        border-top: 1px solid #ccc;
        border-bottom: 1px solid #ccc;
        padding-bottom: 20px
    }
    .short .home-banner.home-banner-small img {
        display: none
    }
}

.home-banner.home-banner-small.left {
    border: 4px solid #000;
    background-color: #e41c11;
    padding-top: 30px!important
}

.home-banner.home-banner-small.left .home-banner-text {
    border-right: 2px solid #000;
    padding: 0 0 90px;
    display: inline-block
}

.home-banner.home-banner-small.left .home-banner-text span {
    border: 4px solid #000;
    border-left: 0;
    background-color: #fff;
    padding: .639em .833em;
    color: #e41c11;
    font-size: 1.75em;
    text-transform: uppercase;
    display: block;
    letter-spacing: -.05em;
    line-height: 1.1em
}

@media screen and (max-width:47.9375em) {
    .home-banner.home-banner-small {
        padding-top: 0;
        border-top: 1px solid #ccc;
        border-bottom: 1px solid #ccc;
        padding-bottom: 20px
    }
    .home-banner.home-banner-small.left {
        background-color: transparent;
        border: 0;
        padding-top: 0!important
    }
    .home-banner.home-banner-small img {
        display: none
    }
    .home-banner.home-banner-small .mobile-border {
        padding-top: 1.438em;
        border: 3px solid #000;
        background-color: #e41c11
    }
    .home-banner.home-banner-small .mobile-border .home-banner-text {
        width: 90%;
        padding-bottom: 4.5em
    }
    .home-banner.home-banner-small .mobile-border .home-banner-text span {
        font-size: 1.25em;
        letter-spacing: -.01em
    }
    .home-banner.home-banner-small .mobile-border .home-banner-text .home-banner-white-text {
        font-size: 1.25em;
        padding-left: .833em;
        padding-bottom: 4.5em
    }
    .home-banner.home-banner-small a.button {
        margin-top: 10px
    }
    .home-banner.home-banner-small.store-finder {
        margin-top: 0
    }
}

.home-banner.store-finder {
    background: #fff
}

.home-banner.store-finder .store-finder-wrapper {
    position: absolute;
    top: 50%;
    margin-top: -17%;
    width: 100%;
    padding: 20px
}

.home-banner.store-finder .store-finder-wrapper:before,
.home-banner.store-finder .store-finder-wrapper:after {
    display: table;
    content: ""
}

.home-banner.store-finder .store-finder-wrapper:after {
    clear: both
}

.home-banner.store-finder .store-finder-wrapper:before,
.home-banner.store-finder .store-finder-wrapper:after {
    display: table;
    content: ""
}

.home-banner.store-finder .store-finder-wrapper:after {
    clear: both
}

.ch .home-banner.store-finder .store-finder-wrapper h2 {
    background: 0 0;
    padding: auto
}

@media screen and (max-width:57.188em) {
    .home-banner.store-finder .store-finder-wrapper {
        padding: 10px
    }
}

@media screen and (max-width:47.9375em) {
    .home-banner.store-finder .store-finder-wrapper {
        position: static;
        margin: 0;
        top: auto
    }
}

.home-banner.store-finder .locator-icon {
    position: static;
    display: block;
    float: left;
    width: 21.25%
}

.home-banner.store-finder h2 {
    position: static;
    margin-bottom: 1em;
    float: right;
    width: 75%;
    color: #000;
    text-shadow: none
}

.wf-active .home-banner.store-finder h2 {
    font-size: 1.625em
}

@media screen and (max-width:50em) {
    .wf-active .home-banner.store-finder h2 {
        font-size: 1.375em
    }
}

@media screen and (max-width:47.9375em) {
    .wf-active .home-banner.store-finder h2 {
        font-size: 1.125em
    }
}

.home-banner.store-finder .store-search-form {
    float: right;
    width: 75%
}

.promo-tiles-interior {
    clear: both;
    padding: 1.5em
}

.promo-tiles-interior:before,
.promo-tiles-interior:after {
    display: table;
    content: ""
}

.promo-tiles-interior:after {
    clear: both
}

.promo-tiles-interior:before,
.promo-tiles-interior:after {
    display: table;
    content: ""
}

.promo-tiles-interior:after {
    clear: both
}

@media screen and (max-width:47.9375em) {
    .promo-tiles-interior .grid_6+.grid_6 {
        margin-top: 2em
    }
}

.promo-tiles-interior .grid_4 {
    width: 100%
}

.phone-listing .promo-tiles-interior .grid_4 {
    width: 50%
}

.product-row:before,
.product-row:after {
    display: table;
    content: ""
}

.product-row:after {
    clear: both
}

.product-row:before,
.product-row:after {
    display: table;
    content: ""
}

.product-row:after {
    clear: both
}

.product {
    padding-left: 20px;
    margin-bottom: 20px;
    margin-top: 20px
}

.product:before,
.product:after {
    display: table;
    content: ""
}

.product:after {
    clear: both
}

.product:before,
.product:after {
    display: table;
    content: ""
}

.product:after {
    clear: both
}

.product a {
    color: #000;
    text-decoration: none
}

@media screen and (max-width:54.375em) {
    .product.grid_6 {
        clear: both;
        margin: 0 auto 20px;
        width: 80%;
        float: none;
        display: block
    }
}

@media screen and (max-width:47.9375em) {
    .product {
        padding-left: 10px;
        margin-bottom: 10px;
        margin-top: 10px;
        min-height: 1px
    }
    .product.grid_6 {
        margin-left: auto;
        margin-right: auto
    }
}

@media screen and (max-width:28.125em) {
    .product.grid_6 {
        width: 100%
    }
}

.product img {
    display: block;
    float: left;
    margin-right: 10px;
    width: 30%
}

@media screen and (max-width:47.9375em) {
    .product img {
        max-width: 181px;
        margin-right: 10px
    }
}

.product .product-name,
.product h2 {
    font-family: "Open Sans Condensed", Arial, Helvetica, sans-serif;
    font-size: 2.25em;
    line-height: 100%;
    margin: 0;
    cursor: pointer
}

.product .product-name:hover,
.product h2:hover {
    color: #e41c11
}

@media screen and (max-width:54.375em) {
    .product .product-name,
    .product h2 {
        font-size: 2em
    }
}

@media screen and (max-width:47.9375em) {
    .product .product-name,
    .product h2 {
        font-size: 1.75em
    }
}

.product-new-badge {
    background: #80b33a;
    color: #fff;
    font-weight: 700;
    text-transform: uppercase;
    padding: 3px 20px;
    display: inline-block;
    font-size: .813em;
    position: relative;
    z-index: -1;
    margin-left: -20px;
    margin-top: 10px
}

@media screen and (max-width:47.9375em) {
    .product-new-badge {
        font-size: .7em;
        padding: 3px 10px 3px 20px
    }
}

.product-new-badge+.product-pricing {
    margin-top: 0
}

.product-price {
    color: #fff;
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-weight: 700;
    font-size: 4.5em;
    letter-spacing: -.025em;
    margin: -8px 10px 0 -5px;
    padding: 0;
    vertical-align: middle;
    display: inline-block
}

.product-price sup {
    font-size: .45em;
    font-weight: 700;
    vertical-align: 0
}

@media screen and (max-width:54.375em) {
    .product-price {
        font-size: 4em
    }
}

@media screen and (max-width:47.9375em) {
    .product-price {
        font-size: 3em;
        float: none
    }
}

.product-plan {
    margin: 0 0 1.1em;
    overflow: hidden;
    width: auto;
    display: inline-block;
    vertical-align: middle;
    max-width: 100%!important
}

.product-plan h4 {
    margin: 0;
    padding: 0;
    font-size: 1.125em;
    text-transform: uppercase;
    font-family: FuturaPT-Demi, "Trebuchet MS", Arial, Helvetica, sans-serif
}

.product-plan small {
    font-family: FuturaPT-Demi, "Trebuchet MS", Arial, Helvetica, sans-serif;
    text-transform: none;
    display: block;
    font-size: 13px;
    margin-top: .1em;
    border-top: solid 1px #fff
}

@media screen and (max-width:54.375em) {
    .product-plan {
        margin: .5em 0 1.1em
    }
    .product-plan small {
        margin-top: .1em
    }
}

@media screen and (max-width:47.9375em) {
    .product-plan {
        margin: -10px 0 0 0
    }
    .product-plan h4 {
        font-size: 1.25em;
        margin-top: 0
    }
}

@media screen and (max-width:47.9375em) {
    .product-plan {
        max-width: 100%!important
    }
}

.product-new-activation {
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: .625em;
    font-weight: 700;
    text-transform: uppercase;
    color: #e41c11;
    background-color: #fff;
    border: solid 2px #000;
    padding: .375em .5em .375em 2.7em;
    background: #fff url(../images/pctelecom/Plus_icon_15.png) no-repeat 8px center;
    margin-left: 30%
}

.product-new-activation sup {
    top: -.5em;
    vertical-align: 0
}

.product-pricing {
    color: #fff;
    background: #e41c11;
    margin-left: 20px;
    padding: .938em;
    padding-left: 0;
    border: .313em solid #333;
    margin-top: 10px
}

@media screen and (max-width:54.375em) {
    .product-pricing {
        width: auto
    }
}

@media screen and (max-width:47.9375em) {
    .product-pricing {
        min-width: 0!important
    }
}

.product-specs {
    margin: 0;
    padding: 0;
    width: 50%;
    max-width: 250px;
    float: left
}

.product-specs li {
    display: block;
    float: left;
    width: 33.3%
}

.product-specs dl {
    margin: 20px auto;
    text-align: center;
    cursor: pointer
}

.product-specs dl:hover {
    color: #e41c11
}

.product-specs dl:before {
    content: ' ';
    display: inline-block
}

.product-specs dl.camera:before {
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -40px -80px;
    width: 29px;
    height: 29px
}

.no-svg .product-specs dl.camera:before {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -40px -80px;
    width: 29px;
    height: 29px
}

.product-specs dl.memory:before {
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -80px -80px;
    width: 25px;
    height: 29px
}

.no-svg .product-specs dl.memory:before {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -80px -80px;
    width: 25px;
    height: 29px
}

.product-specs dl.android:before {
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -80px -80px;
    width: 26px;
    height: 29px
}

.no-svg .product-specs dl.android:before {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -80px -80px;
    width: 26px;
    height: 29px
}

.product-specs dl.display:before {
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -121px -80px;
    width: 19px;
    height: 29px
}

.no-svg .product-specs dl.display:before {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -121px -80px;
    width: 19px;
    height: 29px
}

@media screen and (max-width:47.9375em) {
    .product-specs dl {
        margin-top: 10px;
        font-size: .75em
    }
}

.product-specs dd {
    margin: 0;
    font-weight: 700
}

@media screen and (max-width:54.375em) {
    .product-specs {
        width: 40%
    }
}

@media screen and (max-width:47.9375em) {
    .product-specs {
        width: 58%;
        float: right
    }
}

.new-badge {
    background: #80b33a;
    color: #fff;
    font-weight: 700;
    text-transform: uppercase;
    padding: 3px 20px;
    display: inline-block
}

.slider ul {
    list-style: none;
    margin: 0;
    padding: 0
}

.phone-nav {
    position: relative
}

.phone-nav a {
    color: #000;
    text-decoration: none;
    display: block;
    position: absolute;
    top: -50px
}

.phone-nav a:hover {
    color: #e41c11
}

.ch .phone-nav a {
    text-decoration: underline
}

.wf-active .phone-nav a {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1.25em
}

.phone-nav .next {
    right: 20px
}

.phone-nav .next:after {
    position: absolute;
    top: 4px;
    right: -20px;
    content: '';
    display: block;
    float: left;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -160px -8px;
    width: 15px;
    height: 15px
}

.no-svg .phone-nav .next:after {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -160px -8px;
    width: 15px;
    height: 15px
}

.phone-nav .next:hover:after {
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -160px -48px;
    width: 15px;
    height: 15px
}

.no-svg .phone-nav .next:hover:after {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -160px -48px;
    width: 15px;
    height: 15px
}

.phone-nav .prev {
    left: 20px
}

.phone-nav .prev:before {
    position: absolute;
    top: 4px;
    left: -20px;
    content: '';
    display: block;
    float: left;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -120px -8px;
    width: 15px;
    height: 15px
}

.no-svg .phone-nav .prev:before {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -120px -8px;
    width: 15px;
    height: 15px
}

.phone-nav .prev:hover:before {
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -120px -48px;
    width: 15px;
    height: 15px
}

.no-svg .phone-nav .prev:hover:before {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -120px -48px;
    width: 15px;
    height: 15px
}

@media screen and (max-width:47.9375em) {
    .phone-nav {
        display: none
    }
}

.phone-detail .hero {
    z-index: 10;
    background: #fff
}

.phone-detail h2 {
    text-transform: uppercase
}

.wf-active .phone-detail h2 {
    font-size: 2.5em
}

.phone-detail p,
.phone-detail dt,
.phone-detail dd {
    font-size: 1.25em
}

.phone-detail dt {
    font-weight: 700
}

.phone-detail dt+dt {
    margin-top: 1em
}

.phone-detail dd {
    margin: 0 0 1em
}

.phone-detail .slider {
    padding-bottom: 50px
}

.phone-detail .slider .slides {
    max-height: 567px
}

.phone-detail .slider .slide {
    width: 100%
}

.phone-detail .slider .slide img {
    width: 100%;
    max-width: 290px!important
}

@media screen and (max-width:47.9375em) {
    .phone-detail .slider .slide img {
        max-width: 45%!important
    }
}

.phone-detail .slider ul {
    text-align: center
}

.phone-detail .description {
    font-size: 1.25em;
    margin: 1em 0
}

.phone-detail .description.mobile {
    display: none
}

.phone-detail .description p {
    margin: 0;
    font-size: 1em
}

.phone-detail .description p+p {
    margin-top: 1em
}

@media screen and (max-width:47.9375em) {
    .phone-detail .description {
        display: none
    }
    .phone-detail .description.mobile {
        display: block;
        border-top: 1px solid #b2b2b2;
        border-bottom: 1px solid #b2b2b2;
        padding: 20px 0;
        font-size: 1.125em
    }
}

.phone-detail .plan-wrap {
    position: relative
}

.phone-detail .plan-wrap:before,
.phone-detail .plan-wrap:after {
    display: table;
    content: ""
}

.phone-detail .plan-wrap:after {
    clear: both
}

.phone-detail .plan-wrap:before,
.phone-detail .plan-wrap:after {
    display: table;
    content: ""
}

.phone-detail .plan-wrap:after {
    clear: both
}

.phone-detail .current-plan-container .plan-options-selector {
    max-width: 100%
}

.phone-detail .current-plan-container .product-new-activation {
    clear: both;
    float: left;
    margin-left: 0
}

@media screen and (max-width:47.9375em) {
    .phone-detail .current-plan-container {
        display: table;
        margin: 0 auto;
        padding-bottom: 10px;
        padding-left: 10px;
        padding-right: 10px
    }
    .phone-detail .current-plan-container:before,
    .phone-detail .current-plan-container:after {
        display: table;
        content: ""
    }
    .phone-detail .current-plan-container:after {
        clear: both
    }
    .phone-detail .current-plan-container:before,
    .phone-detail .current-plan-container:after {
        display: table;
        content: ""
    }
    .phone-detail .current-plan-container:after {
        clear: both
    }
    .phone-detail .current-plan-container .plan-options-selector {
        max-width: 100%
    }
}

@media screen and (max-width:54.375em) {
    .phone-detail .current-plan-container .product-new-activation {
        width: auto;
        margin: 0;
        position: static
    }
}

@media screen and (max-width:28.125em) {
    .phone-detail .current-plan-container .plan-options-selector {
        clear: both
    }
}

.phone-detail .new-badge {
    position: absolute;
    top: -28px;
    text-align: center;
    display: block;
    left: -10px
}

.phone-detail .price {
    color: #fff;
    letter-spacing: 0;
    position: relative;
    float: left;
    margin-bottom: -40px;
    padding-right: 20px
}

.phone-detail .price .currency {
    position: relative
}

.phone-detail .price .dollars {
    font-weight: 700
}

.wf-active .phone-detail .price {
    font-family: FuturaLT-CondensedBold, "Trebuchet MS", Arial, Helvetica, sans-serif
}

.phone-detail .plan-options-selector {
    float: left;
    position: relative;
    margin-top: 28px;
    cursor: pointer;
    max-width: 50%
}

.phone-detail .plan-options-selector .learn-more {
    display: block;
    float: left;
    text-decoration: none;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: 0 -120px;
    width: 20px;
    height: 20px;
    text-indent: -99999px
}

.ch .phone-detail .plan-options-selector .learn-more {
    text-decoration: underline
}

.no-svg .phone-detail .plan-options-selector .learn-more {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: 0 -120px;
    width: 20px;
    height: 20px
}

.phone-detail .plan-options-selector .learn-more:hover {
    color: #e41c11;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -20px -120px;
    width: 20px;
    height: 20px
}

.no-svg .phone-detail .plan-options-selector .learn-more:hover {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -20px -120px;
    width: 20px;
    height: 20px
}

.wf-active .phone-detail .plan-options-selector .learn-more {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1.25em
}

@media screen and (max-width:47.9375em) {
    .phone-detail .plan-options-selector .learn-more {
        left: 0;
        top: 10px
    }
}

.phone-detail .plan-options-selector.open .current-plan-option .plan-name {
    color: #f1f1f1
}

.phone-detail .plan-options-selector.open .current-plan-option .plan-details {
    color: #f1f1f1
}

.phone-detail .plan-options-selector.open .current-plan-option:after {
    content: '';
    display: block;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -340px -80px;
    width: 20px;
    height: 20px
}

.no-svg .phone-detail .plan-options-selector.open .current-plan-option:after {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -340px -80px;
    width: 20px;
    height: 20px
}

.phone-detail .plan-options-selector.open .all-plans {
    display: block
}

@media screen and (max-width:57.188em) {
    .phone-detail .plan-options-selector .learn-more {
        margin-top: 16px
    }
}

@media screen and (max-width:54.375em) {
    .phone-detail .plan-options-selector {
        max-width: 100%;
        width: auto;
        position: static
    }
}

@media screen and (max-width:47.9375em) {
    .phone-detail .plan-options-selector {
        max-width: 60%
    }
}

.phone-detail .plan-name {
    text-transform: uppercase
}

.wf-active .phone-detail .plan-name {
    font-family: FuturaPT-Demi, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-weight: 400
}

.phone-detail .plan-details {
    letter-spacing: 0
}

.phone-detail .current-plan-wrap {
    border: solid 5px #000;
    background: #e41c11;
    position: relative;
    margin-top: 28px;
    float: left;
    width: calc(100% - 200px);
    padding: 0 15px 15px;
    margin-bottom: 85px
}

.phone-detail .current-plan-wrap:before,
.phone-detail .current-plan-wrap:after {
    display: table;
    content: ""
}

.phone-detail .current-plan-wrap:after {
    clear: both
}

.phone-detail .current-plan-wrap:before,
.phone-detail .current-plan-wrap:after {
    display: table;
    content: ""
}

.phone-detail .current-plan-wrap:after {
    clear: both
}

.js .phone-detail .current-plan-wrap {
    margin-bottom: 20px
}

.phone-detail .current-plan-wrap .price {
    letter-spacing: 0;
    margin-top: 0
}

.phone-detail .current-plan-wrap .price .currency {
    font-weight: 700;
    font-size: 1.8em;
    position: relative;
    top: -.85em
}

@media screen and (max-width:57.188em) {
    .phone-detail .current-plan-wrap .price .currency {
        font-size: 2.1875em;
        top: -.45em
    }
}

.phone-detail .current-plan-wrap .price .dollars {
    font-size: 4em;
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif
}

@media screen and (max-width:57.188em) {
    .wf-active .phone-detail .current-plan-wrap .price .dollars {
        font-size: 4.0625em
    }
}

@media screen and (max-width:47.9375em) {
    .phone-detail .current-plan-wrap {
        left: auto;
        width: 100%;
        display: block;
        margin: 28px auto 0;
        padding: 0
    }
    .phone-detail .current-plan-wrap .new-badge {
        width: auto;
        padding: 3px 15px;
        display: inline-block;
        text-align: center
    }
}

.phone-detail .current-plan-option {
    float: left;
    position: relative;
    color: #fff;
    padding-bottom: 20px
}

.phone-detail .current-plan-option .plan-name {
    font-size: 1.625em;
    padding-right: 20px
}

.phone-detail .current-plan-option .plan-name small {
    display: block;
    text-transform: none;
    font-weight: 400;
    font-size: .5em;
    border-top: 1px solid #fff
}

@media screen and (max-width:47.9375em) {
    .phone-detail .current-plan-option .plan-name small {
        font-size: .5em
    }
}

.phone-detail .current-plan-option:after {
    content: '';
    display: block;
    position: absolute;
    top: 8px;
    right: 0;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -320px -80px;
    width: 20px;
    height: 20px
}

.no-svg .phone-detail .current-plan-option:after {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -320px -80px;
    width: 20px;
    height: 20px
}

.no-js .phone-detail .current-plan-option:after {
    display: none
}

.phone-detail .current-plan-option .plan-details {
    font-size: .9375em
}

.phone-detail .all-plans {
    background: #fff;
    position: absolute;
    margin-top: 10px
}

.phone-detail .all-plans:before,
.phone-detail .all-plans:after {
    display: table;
    content: ""
}

.phone-detail .all-plans:after {
    clear: both
}

.phone-detail .all-plans:before,
.phone-detail .all-plans:after {
    display: table;
    content: ""
}

.phone-detail .all-plans:after {
    clear: both
}

.js .phone-detail .all-plans {
    padding: 20px;
    width: 240px;
    bottom: auto;
    border: 1px solid #b2b2b2;
    display: none
}

.phone-detail .all-plans .plan {
    position: relative;
    float: left;
    margin: 0 20px 0 0
}

.phone-detail .all-plans .plan:before,
.phone-detail .all-plans .plan:after {
    display: table;
    content: ""
}

.phone-detail .all-plans .plan:after {
    clear: both
}

.phone-detail .all-plans .plan:before,
.phone-detail .all-plans .plan:after {
    display: table;
    content: ""
}

.phone-detail .all-plans .plan:after {
    clear: both
}

.js .phone-detail .all-plans .plan {
    float: none;
    margin: 0 0 10px
}

.phone-detail .all-plans .plan .price {
    color: #e41c11;
    top: 0
}

.phone-detail .all-plans .price {
    margin: 0;
    margin-right: .25em
}

.wf-active .phone-detail .all-plans .price {
    font-size: 1.1875em
}

.phone-detail .all-plans .price .currency {
    font-size: 1em;
    position: relative;
    top: auto
}

.wf-active .phone-detail .all-plans .price .dollars {
    font-size: 1em
}

.phone-detail .all-plans .plan-name {
    clear: left
}

.wf-active .phone-detail .all-plans .plan-name {
    font-size: 1.1875em
}

.phone-detail .all-plans .plan-name small {
    font-family: "Open Sans Condensed", Arial, Helvetica, sans-serif;
    font-weight: 400;
    font-size: .75em;
    line-height: .25em
}

.phone-detail .all-plans .plan-name small:before {
    content: "";
    display: block
}

.phone-detail .all-plans .plan-details {
    font-size: .9375em;
    padding-top: 3px;
    clear: both
}

.phone-detail .actions {
    margin-top: 28px;
    width: 180px;
    float: right
}

@media screen and (max-width:50em) {
    .phone-detail .actions .button {
        font-size: 1.875em
    }
    .phone-detail .actions .note {
        font-size: 1.25em;
        margin: .5em 0
    }
}

@media screen and (max-width:47.9375em) {
    .phone-detail .actions {
        text-align: center;
        float: none;
        display: table;
        width: 180px;
        margin: 0 auto
    }
}

.phone-detail .specs {
    clear: both;
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #b2b2b2
}

.phone-detail .owners-manual {
    text-decoration: none;
    color: #000;
    position: relative;
    text-transform: uppercase;
    padding-left: 33px
}

.ch .phone-detail .owners-manual {
    text-decoration: underline
}

.phone-detail .owners-manual:before {
    content: '';
    position: absolute;
    display: block;
    left: 0;
    top: -3px;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -360px -80px;
    width: 25px;
    height: 33px
}

.no-svg .phone-detail .owners-manual:before {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -360px -80px;
    width: 25px;
    height: 33px
}

.phone-detail .owners-manual:hover,
.phone-detail .owners-manual:active,
.phone-detail .owners-manual:focus,
.phone-detail .owners-manual.active {
    color: #e41c11
}

.phone-detail .owners-manual:hover:before,
.phone-detail .owners-manual:active:before,
.phone-detail .owners-manual:focus:before,
.phone-detail .owners-manual.active:before {
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -400px -80px;
    width: 25px;
    height: 33px
}

.no-svg .phone-detail .owners-manual:hover:before,
.no-svg .phone-detail .owners-manual:active:before,
.no-svg .phone-detail .owners-manual:focus:before,
.no-svg .phone-detail .owners-manual.active:before {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -400px -80px;
    width: 25px;
    height: 33px
}

.wf-active .phone-detail .owners-manual {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1.5em
}

.phone-detail .view-all-phones {
    text-decoration: none;
    text-transform: uppercase;
    position: relative;
    color: #000;
    padding-left: 18px
}

.ch .phone-detail .view-all-phones {
    text-decoration: underline
}

.phone-detail .view-all-phones:before {
    content: '';
    position: absolute;
    display: block;
    left: 0;
    top: 4px;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -120px -8px;
    width: 15px;
    height: 15px
}

.no-svg .phone-detail .view-all-phones:before {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -120px -8px;
    width: 15px;
    height: 15px
}

.phone-detail .view-all-phones:hover,
.phone-detail .view-all-phones:active,
.phone-detail .view-all-phones:focus,
.phone-detail .view-all-phones.active {
    color: #e41c11
}

.phone-detail .view-all-phones:hover:before,
.phone-detail .view-all-phones:active:before,
.phone-detail .view-all-phones:focus:before,
.phone-detail .view-all-phones.active:before {
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -120px -48px;
    width: 15px;
    height: 15px
}

.no-svg .phone-detail .view-all-phones:hover:before,
.no-svg .phone-detail .view-all-phones:active:before,
.no-svg .phone-detail .view-all-phones:focus:before,
.no-svg .phone-detail .view-all-phones.active:before {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -120px -48px;
    width: 15px;
    height: 15px
}

.wf-active .phone-detail .view-all-phones {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1.25em
}

.device-subsidy-table {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    z-index: 10;
    background-color: #fff;
    padding: 20px;
    border: 1px solid #b2b2b2;
    max-width: 620px;
    display: none
}

.device-subsidy-table .close-btn {
    position: absolute;
    top: 20px;
    right: 20px
}

.device-subsidy-table .has-tooltip {
    color: #e41c11
}

.device-subsidy-table h3 {
    text-transform: uppercase;
    margin: 0
}

.wf-active .device-subsidy-table h3 {
    font-size: 1.75em
}

.device-subsidy-table table {
    border: 1px solid #b2b2b2;
    width: 100%;
    margin: 0;
    font-size: 1em
}

.device-subsidy-table table tr,
.device-subsidy-table table th,
.device-subsidy-table table td {
    text-align: center;
    padding: 10px
}

.device-subsidy-table table th {
    border-top: 1px solid #ccc
}

.device-subsidy-table table thead th {
    color: #fff;
    background: #000;
    border-right: 1px solid #ccc
}

.device-subsidy-table table thead th:last-child {
    border-right: 0
}

.wf-active .device-subsidy-table table thead th {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1.75em
}

.device-subsidy-table table tbody th {
    background-color: #f7f7f7;
    font-weight: 700
}

.callout .device-subsidy-table {
    top: auto;
    width: 620px
}

.close-btn {
    background-color: transparent;
    border: 0;
    overflow: hidden;
    display: block;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -120px -120px;
    width: 32px;
    height: 32px
}

.close-btn:before {
    content: "";
    display: block;
    width: 0;
    height: 150%
}

.no-svg .close-btn {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -120px -120px;
    width: 32px;
    height: 32px
}

.close-btn:hover {
    background-position: -120px -160px
}

#phone-details-wrapper {
    position: relative;
    left: -2px;
    margin-bottom: 1.5em;
    border: 1px solid #ccc;
    padding-left: 0
}

#nav-phones ul {
    list-style: none;
    margin: 0;
    padding: 0
}

#nav-phones ul:before,
#nav-phones ul:after {
    display: table;
    content: ""
}

#nav-phones ul:after {
    clear: both
}

#nav-phones ul:before,
#nav-phones ul:after {
    display: table;
    content: ""
}

#nav-phones ul:after {
    clear: both
}

#nav-phones ul li {
    display: block;
    float: left;
    width: 50%;
    height: 14.25em
}

#nav-phones ul a {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    display: block;
    height: 100%;
    padding: .5em;
    border-width: 0 1px 1px 0;
    border-style: solid;
    border-color: #ccc;
    background-color: #fff;
    text-decoration: none;
    text-align: center;
    color: #000
}

.ch #nav-phones ul a {
    text-decoration: underline
}

#nav-phones ul a.active {
    background-color: #b52322;
    background-repeat: no-repeat;
    background-image: -webkit-radial-gradient(circle, #e41c11, #b52322);
    -webkit-box-shadow: none;
    box-shadow: none;
    color: #fff
}

#nav-phones ul a.active h2 {
    color: #fff
}

#nav-phones ul a.active:not(:hover) .red,
#nav-phones ul a.active .grey {
    color: #fff
}

#nav-phones ul a:hover,
#nav-phones ul a:focus {
    background-color: #ccc;
    background-repeat: repeat-x;
    background-image: -webkit-linear-gradient(top, #fff, #ccc);
    background-image: linear-gradient(to bottom, #fff, #ccc);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#ffcccccc', GradientType=0);
    -webkit-box-shadow: inset 1px 1px 1px #ccc;
    box-shadow: inset 1px 1px 1px #ccc;
    color: #000
}

#nav-phones ul a:hover h2,
#nav-phones ul a:focus h2 {
    color: #000
}

#nav-phones ul a h4,
#nav-phones ul a h2,
#nav-phones ul a p {
    margin: 0;
    font-family: "Open Sans Condensed", Arial, Helvetica, sans-serif;
    font-size: 1em;
    font-weight: 700
}

#nav-phones ul a img {
    max-width: 100%;
    height: 120px
}

#phone-term-selector {
    margin: .5em 0
}

#phone-details {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding-left: 10px
}

#phone-details .phone-metadata {
    font-size: 1.25em;
    margin-left: 0
}

#phone-details .phone-name {
    max-width: 25em;
    font-size: 250%
}

#phone-details .phone-name span {
    color: #e41c11
}

#phone-details p,
#phone-details ul,
#phone-details .phone-specs {
    margin-right: 2em;
    font-size: 1.25em
}

#phone-details p {
    line-height: 1.5em
}

#phone-details ul {
    padding-left: 1.25em
}

#phone-details ul li+li {
    margin-top: .333em
}

#phone-details .phone-photo {
    width: 100%;
    height: auto
}

#phone-details .phone-photo-thumb {
    display: inline-block;
    width: 50px;
    text-align: center
}

#phone-details .phone-photo-thumb img {
    height: 50px
}

#phone-details .phone-document {
    display: block;
    margin: 1em 0;
    font-size: 1.25em;
    line-height: 26px;
    text-decoration: none;
    color: #e41c11
}

.ch #phone-details .phone-document {
    text-decoration: underline
}

#phone-details .phone-document .icon {
    display: none
}

#phone-details .sharing-tools {
    position: absolute;
    top: -32px;
    right: 24px
}

#phone-details #phone-price-wrapper~.sharing-tools {
    top: 32px
}

.monthly #phone-price-wrapper {
    display: table;
    margin-right: 2em;
    border-collapse: collapse;
    background-color: #dfdfdf
}

.monthly #phone-price-wrapper .phone-price {
    display: table-cell;
    vertical-align: bottom;
    width: 33%;
    height: 100%;
    margin: 0;
    border: 1px solid #fff;
    font-size: 150%
}

.monthly #phone-price-wrapper .phone-price .term {
    display: block;
    max-width: 75%;
    min-height: 4.667em;
    margin-bottom: .5em;
    font-size: 50%;
    font-weight: 400
}

.monthly #phone-price-wrapper .phone-price .sale-wrap {
    display: block;
    font-size: 40%
}

.monthly #phone-price-wrapper .phone-price .sale-price {
    display: block;
    text-decoration: line-through;
    color: red;
    font-size: 166%
}

.monthly #phone-price-wrapper .phone-price-inner {
    position: relative;
    padding: .5em
}

.price-tooltip-wrap {
    position: absolute;
    top: 1em;
    right: 1em;
    width: 30px;
    height: 30px
}

.price-tooltip-wrap .price-tooltip-trigger {
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    width: 30px;
    height: 30px;
    background-color: transparent;
    border: 0;
    overflow: hidden;
    text-decoration: none;
    border-radius: 15px;
    background-color: #fff;
    -webkit-transition: all 250ms ease-out;
    transition: all 250ms ease-out
}

.price-tooltip-wrap .price-tooltip-trigger:before {
    content: "";
    display: block;
    width: 0;
    height: 150%
}

.price-tooltip-wrap .price-tooltip-trigger:after {
    display: block;
    position: absolute;
    top: 18%;
    right: 40%;
    content: 'i';
    font-size: 133%;
    font-weight: 700;
    color: #000
}

.price-tooltip-wrap .price-tooltip-content {
    z-index: 5;
    display: block;
    position: absolute;
    bottom: 30px;
    right: 0;
    width: 500%;
    padding: 1em;
    background-color: #000;
    color: #fff;
    font-family: FuturaPT-Demi, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-weight: 400;
    opacity: 0;
    -ms-filter: "alpha(opacity=0)";
    -webkit-transition: all 250ms cubic-bezier(0, .5, .5, 1);
    transition: all 250ms cubic-bezier(0, .5, .5, 1)
}

.price-tooltip-wrap .price-tooltip-content:target {
    bottom: 15px;
    opacity: 1;
    -ms-filter: "alpha(opacity=100)"
}

.price-tooltip-wrap.active .price-tooltip-content {
    bottom: 15px;
    opacity: 1;
    -ms-filter: "alpha(opacity=100)"
}

.price-tooltip-wrap.active .price-tooltip-trigger {
    background-color: #000
}

.phone-compare {
    width: 100%;
    margin-top: 0
}

.phone-compare img {
    max-height: 120px;
    margin: 0 auto;
    display: block
}

.pinned .phone-compare tr:last-child td:first-child {
    border-bottom: 1px #f7f7f7 solid
}

.phone-compare [headers=phone-model] h4,
.phone-compare [headers=phone-model] h2 {
    font-family: "Open Sans Condensed", Arial, Helvetica, sans-serif;
    font-size: 1em;
    margin: 0
}

.phone-compare [headers=phone-price] {
    font-weight: 700
}

.phone-compare #phone-top_features,
.phone-compare [headers=phone-top_features] {
    vertical-align: top
}

.phone-compare #phone-top_features {
    padding-top: 1.5em;
    border-bottom: 0
}

.phone-compare #phone-model,
.phone-compare #phone-price,
.phone-compare #phone-os,
.phone-compare #phone-top_features {
    padding-left: 10px
}

.phone-compare .phone-model h4,
.phone-compare .phone-model h2 {
    font-family: "Open Sans Condensed", Arial, Helvetica, sans-serif;
    font-size: 1em;
    margin: 0
}

.phone-compare .phone-price {
    font-weight: 700
}

.phone-compare .phone-top_features {
    vertical-align: top
}

.phone-compare tr:nth-child(4) td[scope=row] {
    padding-top: 1.5em;
    vertical-align: top
}

.phone-compare td[scope=row] {
    padding-left: 10px;
    border-left: 0
}

.phone-compare td {
    padding-top: 0;
    padding-bottom: 0
}

.phone-compare tr:first-child td {
    border-top: 0
}

.offers-row {
    overflow: hidden
}

.offers-row+.offers-row {
    margin-top: 3em;
    padding-top: 3em;
    border-top: 1px solid #ccc
}

@media screen and (max-width:47.9375em) {
    .offers-row+.offers-row {
        margin-top: 0;
        padding-top: 0;
        border-top: 0
    }
}

.offer img {
    width: 100%;
    height: auto
}

.offer p a {
    text-decoration: none
}

.ch .offer p a {
    text-decoration: underline
}

.offer .offer-title {
    margin: 0 0 .25em;
    padding-top: .333em;
    font-size: 3em;
    text-transform: uppercase
}

.offer .offer-description {
    margin: .5em 0;
    font-size: 1.25em
}

.offer .footnotes {
    margin: 1em 0;
    font-size: 1em
}

.offer.offer-large img {
    float: left;
    width: 48%
}

.offer.offer-large .offer-title,
.offer.offer-large p {
    margin-left: 50%
}

.offer.offer-medium {
    width: 49%;
    float: left;
    margin-left: 2%
}

.offer.offer-medium:first-child {
    margin-left: 0
}

.offer.offer-small {
    width: 32%;
    float: left;
    margin-left: 2%
}

.offer.offer-small:first-child {
    margin-left: 0
}

@media screen and (max-width:47.9375em) {
    .offer {
        margin: 0 0 1em;
        padding: 2em 0 1em;
        border-bottom: 1px solid #ccc
    }
    .offer.offer-large,
    .offer.offer-medium,
    .offer.offer-small {
        width: 100%;
        float: none;
        margin: 0
    }
    .offer.offer-large img,
    .offer.offer-medium img,
    .offer.offer-small img {
        float: none;
        width: 100%
    }
    .offer.offer-large .offer-title,
    .offer.offer-medium .offer-title,
    .offer.offer-small .offer-title,
    .offer.offer-large p,
    .offer.offer-medium p,
    .offer.offer-small p {
        margin-left: 0
    }
}

.support-tiles li {
    margin-bottom: 2%;
    border: 1px solid #ccc;
    height: 18.75em;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    overflow: hidden
}

.support-tiles li.grid_4 {
    width: 32%;
    padding: 0;
    margin-right: 2%
}

.support-tiles li.grid_4.omega {
    margin-right: 0
}

@media screen and (max-width:47.9375em) {
    .support-tiles li.grid_4 {
        width: 100%;
        margin-right: auto
    }
}

.support-tiles li h2 {
    margin-top: 0;
    font-size: 1.188em;
    text-transform: uppercase;
    position: relative;
    background: #000;
    color: #fff;
    display: block;
    padding: 10px 20px
}

.wf-active .support-tiles li h2 {
    font-size: 1.625em
}

@media screen and (max-width:57.188em) {
    .wf-active .support-tiles li h2 {
        font-size: 1.375em
    }
}

@media screen and (max-width:47.9375em) {
    .support-tiles li h2 {
        margin: 0
    }
}

.support-tiles li p {
    font-size: .875em;
    margin: .85em 0;
    padding-left: 20px;
    padding-right: 20px
}

@media screen and (max-width:57.188em) {
    .support-tiles li p {
        font-size: .8125em
    }
}

@media screen and (max-width:50em) {
    .support-tiles li p {
        font-size: .75em
    }
}

@media screen and (max-width:47.9375em) {
    .support-tiles li p {
        padding-left: 0;
        padding-right: 0
    }
}

.support-tiles li .button {
    position: absolute;
    bottom: 20px;
    left: 20px;
    margin-right: 20px;
    font-size: 1.125em
}

.support-tiles [class^=icon-support-],
.support-tiles [class*=" icon-support-"] {
    float: right
}

.support-category {
    margin-bottom: 2em;
    font-size: 1.25em
}

.support-category h3,
.support-category h2 {
    text-transform: uppercase
}

.wf-active .support-category h3,
.wf-active .support-category h2 {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1.75em
}

.support-category h3:first-child,
.support-category h2:first-child {
    margin-top: 0
}

.support-category p {
    font-size: 1em;
    line-height: 1.5em
}

.wf-active .support-category dt {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1.375em
}

.support-category dd {
    margin-left: 0
}

.support-category a:link.view-all,
.support-category a:visited.view-all {
    color: #e41c11;
    text-decoration: none
}

.ch .support-category a:link.view-all,
.ch .support-category a:visited.view-all {
    text-decoration: underline
}

.support-category .overflow {
    display: none
}

.support-category .toggle-expand {
    display: inline-block
}

.wish-list-page .list-print,
.wish-list-page .list-email {
    background-color: transparent;
    border: 0;
    overflow: hidden;
    position: relative;
    float: right;
    display: block;
    width: 4.125em;
    height: 3.125em;
    margin-left: .667em;
    padding: 0;
    font-size: 16px;
    background-color: #000
}

.wish-list-page .list-print:before,
.wish-list-page .list-email:before {
    content: "";
    display: block;
    width: 0;
    height: 150%
}

@media screen and (max-width:47.9375em) {
    .wish-list-page .list-print,
    .wish-list-page .list-email {
        float: left;
        margin: 0 .667em 0 0
    }
}

.wish-list-page .list-print:after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -16px 0 0 -16px;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -400px -120px;
    width: 33px;
    height: 35px
}

.no-svg .wish-list-page .list-print:after {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -400px -120px;
    width: 33px;
    height: 35px
}

.wish-list-page .list-email:after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -12px 0 0 -17px;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -447px -120px;
    width: 33px;
    height: 24px
}

.no-svg .wish-list-page .list-email:after {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -447px -120px;
    width: 33px;
    height: 24px
}

.wish-list-page .page-actions {
    margin: 0 0 1em;
    overflow: hidden
}

.wish-list-page .page-actions p {
    margin: 0;
    font-size: 1.25em
}

.wish-list-page .page-actions p.big {
    font-size: 1.625em
}

@media screen and (max-width:47.9375em) {
    .wish-list-page .page-actions p {
        margin: .25em 0 1em;
        font-size: 1em
    }
    .wish-list-page .page-actions p.big {
        margin: 0;
        font-size: 1.25em
    }
}

.wish-list-page .selection-wrap {
    position: relative;
    margin: 1em 0;
    border: 1px solid #ccc
}

.wish-list-page .selection-wrap>.name {
    margin: 0;
    padding: 20px;
    background: #000;
    color: #fff;
    font-size: 2.25em;
    text-transform: uppercase
}

.print .wish-list-page .selection-wrap>.name {
    background: #fff;
    color: #000
}

.print .wish-list-page .selection-wrap>.name:after {
    display: none
}

.wish-list-page .selection-wrap>.name:after {
    content: '';
    display: block;
    position: absolute;
    top: 15px;
    right: 15px;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: 0 -160px;
    width: 45px;
    height: 45px
}

.no-svg .wish-list-page .selection-wrap>.name:after {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: 0 -160px;
    width: 45px;
    height: 45px
}

.wish-list-page .selection-wrap.selected .name:after {
    background-position: -60px -160px
}

.wish-list-page .selection-wrap.notSelected .preview {
    position: static;
    padding-top: 0;
    height: auto
}

.wish-list-page .selection-wrap .preview {
    position: relative;
    height: 0;
    padding-top: 96%;
    background-color: #fff
}

.wish-list-page .selection-wrap .preview .thumb {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    max-width: 100%;
    max-height: 100%
}

.wish-list-page .selection-wrap .preview .messages {
    position: absolute;
    bottom: 0;
    left: 0;
    list-style: none;
    margin: 0;
    padding: 0
}

.wish-list-page .selection-wrap .preview .messages li {
    display: block;
    float: left;
    clear: left;
    margin-top: .25em;
    padding: .125em .5em .25em;
    background-color: #fff;
    color: #e41c11;
    font-size: 1.625em;
    white-space: nowrap;
    text-transform: uppercase
}

.wf-active .wish-list-page .selection-wrap .preview .messages li {
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif
}

@media screen and (max-width:47.9375em) {
    .wish-list-page .selection-wrap .preview .messages li {
        font-size: 1.25em
    }
}

.wish-list-page .selection-wrap .preview .metadata {
    position: relative;
    background-color: #e41c11;
    border: solid 5px #000;
    padding: 35px 40px 0
}

.wish-list-page .selection-wrap .preview .metadata .metawrapper {
    padding-bottom: 35px;
    border-left: solid 2px #000;
    border-right: solid 2px #000
}

.wish-list-page .selection-wrap .preview .metadata .metawrapper ul {
    background-color: #fff;
    border: solid 5px #000;
    padding: 4px 0 4px 10px;
    margin: 0;
    list-style-type: none
}

.wish-list-page .selection-wrap .preview .metadata .metawrapper ul li {
    background: url(../images/pctelecom/Plus_icon.png) no-repeat left 3px;
    padding: 0 0 0 35px;
    color: #e41c11;
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1.875em;
    text-transform: uppercase;
    margin: 0;
    letter-spacing: -.03em
}

@media screen and (max-width:47.9375em) {
    .wish-list-page .selection-wrap .preview .metadata .metawrapper ul li {
        font-size: 1.5em
    }
}

.print .wish-list-page .selection-wrap .preview .metadata .metawrapper {
    border: 0
}

.print .wish-list-page .selection-wrap .preview .metadata .metawrapper ul {
    border: 0;
    list-style-type: disc;
    padding: 0
}

.print .wish-list-page .selection-wrap .preview .metadata .metawrapper ul li {
    color: #000;
    background: 0 0;
    padding: 0
}

.wish-list-page .selection-wrap .actions {
    padding: 20px 10%;
    text-align: center
}

.wish-list-page .selection-wrap .actions .button {
    width: 100%
}

@media screen and (max-width:47.9375em) {
    .wish-list-page .selection-wrap .actions {
        padding-left: 5%;
        padding-right: 5%
    }
}

.wish-list-page .selection-wrap .plan-wrap,
.wish-list-page .selection-wrap .phone-wrap,
.wish-list-page .selection-wrap .store-wrap {
    margin-top: 0;
    border-width: 0
}

.print .wish-list-page .selection-wrap .plan-wrap,
.print .wish-list-page .selection-wrap .phone-wrap,
.print .wish-list-page .selection-wrap .store-wrap {
    margin-bottom: 0
}

.wish-list-page .selection-wrap .plan-wrap .name,
.wish-list-page .selection-wrap .phone-wrap .name,
.wish-list-page .selection-wrap .store-wrap .name {
    padding-top: 15px;
    padding-bottom: 0;
    border: 0;
    font-size: 1.5em
}

.wish-list-page .selection-wrap .plan-wrap {
    margin-bottom: 0;
    border: 0
}

.wish-list-page .selection-wrap .phone-wrap+.actions {
    margin-top: -1px;
    border-top: 1px solid #ccc
}

.wish-list-page .selection-wrap .store-wrap .name {
    padding-left: 20px;
    text-align: left
}

.wish-list-page .selection-wrap .store-wrap .address {
    min-height: 140px;
    margin: 0;
    padding: 10px 20px 20px;
    background-color: #000;
    color: #fff;
    font-size: 1.25em
}

.print .wish-list-page .selection-wrap .store-wrap .address {
    min-height: 206px;
    background: #fff;
    color: #000
}

.wish-list-page .selection-wrap .store-wrap .address a {
    text-decoration: none
}

.wish-list-page .selection-wrap .store-wrap .store-data-wrap {
    margin: 0;
    padding: 0 20px
}

.wish-list-page .selection-wrap .store-wrap .store-data-wrap .collapsible-trigger {
    border: 0;
    font-size: 1.5em;
    line-height: 2.6;
    text-transform: uppercase;
    color: #000
}

.wish-list-page .selection-wrap .store-wrap .store-data-wrap .collapsible-trigger:hover,
.wish-list-page .selection-wrap .store-wrap .store-data-wrap .collapsible-trigger:focus {
    color: #e41c11
}

.wish-list-page .selection-wrap .store-wrap .store-data-wrap .collapsible-target {
    margin-left: 0
}

.wish-list-page .selection-wrap .store-wrap .storeHoursList {
    width: 100%;
    margin: 0 0 1em;
    border: 0;
    font-size: 1em
}

.wish-list-page .selection-wrap .store-wrap .storeHoursList caption,
.wish-list-page .selection-wrap .store-wrap .storeHoursList+strong {
    font-weight: 400;
    text-align: left;
    text-transform: uppercase
}

.wf-active .wish-list-page .selection-wrap .store-wrap .storeHoursList caption,
.wf-active .wish-list-page .selection-wrap .store-wrap .storeHoursList+strong {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1.25em
}

.wish-list-page .selection-wrap .store-wrap .storeHoursList th,
.wish-list-page .selection-wrap .store-wrap .storeHoursList td {
    padding: 0;
    border: 0;
    background: 0 0;
    text-transform: none
}

.wish-list-page .selection-wrap .store-wrap .storeHoursList th {
    width: 30%
}

.wish-list-page .selection-wrap .store-wrap .storeServicesList {
    list-style-type: square;
    margin: 0 0 1em;
    padding-left: 1.25em
}

.phone-details {
    font-size: 1em;
    line-height: 1.5em
}

.phone-details .two-tone {
    margin-top: 0;
    border-top: 0;
    border-bottom: 1px solid #eee;
    padding-top: 0;
    font-size: 1.583em
}

.phone-details .col-1 {
    width: 220px;
    float: left
}

.phone-details .preview-thumbs {
    margin: 0 0 1em;
    padding: 0
}

.phone-details .preview-thumbs li {
    width: 50px;
    height: 50px;
    display: inline-block;
    list-style: none;
    text-align: center
}

.phone-details .preview-thumbs li img {
    max-width: 100%;
    max-height: 100%;
    vertical-align: top
}

.phone-details .col-2 {
    margin-left: 230px
}

.phone-details .price {
    margin-bottom: 0;
    font-size: 2.167em
}

.phone-details .term {
    font-size: 1.167em;
    text-transform: uppercase
}

.phone-details h5 {
    margin: 1em 0 .5em;
    font-size: 1em
}

.phone-details .features {
    margin-top: 0;
    padding-left: 1.5em
}

#email-wizard-summary h2 {
    font-size: .75em;
    display: inline-block;
    margin: 0 0 .5em
}

.wf-active #email-wizard-summary h2 {
    font-size: 1.5em
}

.wf-active #email-wizard-summary label {
    font-family: "Open Sans Condensed", "Open Sans Condensed", Arial, Helvetica, sans-serif
}

#email-wizard-summary .button {
    font-size: .75em
}

.wf-active #email-wizard-summary .button {
    font-size: 1.25em
}

#email-wizard-summary .form-actions {
    margin: 1.5em 0 0;
    text-align: left
}

.media-centre-main {
    margin-bottom: 1em
}

.media-centre-item {
    padding: 1.5em 0
}

.media-centre-item+.media-centre-item {
    border-top: 2px solid #ccc
}

.media-centre-item .item-date {
    font-weight: 700;
    text-transform: uppercase;
    color: #888
}

.media-centre-item .item-title {
    margin: .5em 0
}

.media-centre-item .item-title .iconPDF {
    display: inline;
    height: auto;
    padding-bottom: 0;
    font-size: inherit;
    line-height: inherit;
    position: relative;
    padding-left: 38px
}

.media-centre-item .item-title .iconPDF:before {
    position: absolute;
    top: 8px;
    left: 0
}

.media-centre-item .item-title a {
    text-decoration: none
}

.media-centre-item p {
    margin: 0
}

.contact-main h2 {
    margin: 0;
    text-transform: uppercase
}

.contact-main .grid_4 {
    width: 50%
}

@media screen and (max-width:47.9375em) {
    .contact-main .grid_4 {
        width: 100%
    }
}

.contact-main .monthly dd,
.contact-main .prepaid dd {
    padding: 1em
}

.contact-main .monthly dt,
.contact-main .prepaid dt {
    padding: .25em;
    background-color: #000;
    text-align: center;
    color: #fff;
    text-transform: uppercase
}

.wf-active .contact-main .monthly dt,
.wf-active .contact-main .prepaid dt {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 2em;
    font-weight: 400
}

.contact-main .monthly dd,
.contact-main .prepaid dd {
    margin: 0;
    padding-bottom: 140px;
    background-color: #fff;
    background-repeat: no-repeat;
    border: 1px solid #ccc
}

.contact-main .monthly ul,
.contact-main .prepaid ul {
    margin-top: 0;
    padding-left: 1.5em
}

.contact-main .monthly a.more,
.contact-main .prepaid a.more {
    margin-left: 1.5em
}

.contact-main .monthly .callout,
.contact-main .prepaid .callout {
    display: block;
    width: 75%;
    margin: 1em auto 0;
    padding: 20px;
    border: 0;
    text-align: center
}

.contact-main .monthly .callout p,
.contact-main .prepaid .callout p {
    margin-top: 0
}

.contact-main .monthly .button,
.contact-main .prepaid .button {
    font-size: 1.75em
}

.contact-main .monthly dd,
.contact-main .prepaid dd {
    background-position: 50% 88%
}

.contac.25t-info {
    font-size: 1em;
    line-height: 1.5em
}

.contac.25t-info a {
    color: #e41c11;
    text-decoration: none
}

.ch .contac.25t-info a {
    text-decoration: underline
}

.contac.25t-info h2 {
    margin-top: 0;
    font-size: 1.583em;
    text-transform: uppercase
}

.contac.25t-info dt {
    font-weight: 700
}

.contac.25t-info dd {
    margin-bottom: 1em;
    margin-left: 0
}

.contact-secondary .tile-thumb {
    float: right
}

.contact-secondary .tile {
    margin-right: 85px;
    margin-bottom: 1em
}

.contact-secondary .tile h3 {
    margin-top: 0
}

.contact-secondary .tile .button {
    font-size: 1.625em
}

.contact-secondary .button {
    font-size: 1.625em
}

.contact-secondary .store-search-form {
    margin-bottom: 2em
}

#results-cap,
#results-range {
    width: 48%
}

#results-range {
    margin-left: 2%
}

.has-tooltip {
    cursor: pointer
}

.has-tooltip:after {
    content: '*'
}

.tooltip-container {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    z-index: 100;
    display: block;
    width: 280px;
    position: absolute
}

.tooltip-container i {
    display: none
}

.csstransforms .tooltip-container i {
    z-index: 2;
    position: relative;
    left: 50%;
    display: block;
    width: 16px;
    height: 16px;
    margin: 4px 0 -8px -12px;
    border-width: 1px 1px 0 0;
    border-style: solid;
    border-color: #fff;
    background: #000;
    -webkit-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    transform: rotate(-45deg)
}

.tooltip-container .content {
    z-index: 1;
    position: relative;
    display: block;
    width: 100%;
    padding: 15px 20px;
    border: 1px solid #fff;
    border-radius: .3125em;
    background-color: #000;
    text-align: left;
    color: #fff;
    font-size: 16px;
    font-family: Arial, Helvetica, sans-serif;
    text-transform: none
}

.wf-active .tooltip-container .content {
    font-family: "Open Sans Condensed", "Open Sans Condensed", Arial, Helvetica, sans-serif
}

.footnotes .note-highlight {
    margin: 0;
    font-size: 1.333em;
    letter-spacing: 0
}

.footnotes .has-tooltip {
    color: #e41c11
}

.collapsible-trigger {
    position: relative;
    display: block;
    padding-bottom: .125em;
    border-bottom: 1px solid #ccc;
    font-size: 2.5em;
    color: #000;
    text-decoration: none
}

.collapsible-trigger:after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    right: 0;
    margin-top: -16px;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -408px 0;
    width: 32px;
    height: 32px
}

.no-svg .collapsible-trigger:after {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -408px 0;
    width: 32px;
    height: 32px
}

.collapsible-trigger:hover:after,
.collapsible-trigger:focus:after {
    background-position: -408px -40px
}

.collapsible-trigger[aria-expanded=false]:after {
    background-position: -448px 0
}

.collapsible-trigger[aria-expanded=false]:hover:after,
.collapsible-trigger[aria-expanded=false]:focus:after {
    background-position: -448px -40px
}

.wf-active .collapsible-trigger {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif
}

.plans-features.container_12 .grid_4 h2.find-store {
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-weight: 700;
    font-size: 1.25em;
    padding-top: 582px
}

.plans-features .tabs {
    border-bottom: 1px solid #ccc
}

.plans-features .tabs li {
    display: inline-block;
    list-style: none
}

.plans-features .tabs li+li {
    margin-left: -.3em
}

.plans-features .tabs li a {
    padding: .5em 1em;
    display: block;
    border-bottom: 0;
    color: #000;
    font-size: 1.125em;
    text-decoration: none;
    text-transform: uppercase
}

.wf-active .plans-features .tabs li a {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 2.1875em
}

.ch .plans-features .tabs li a {
    text-decoration: underline;
    border-color: #000
}

.plans-features .tabs li.active a {
    border-color: #000;
    background-color: #000;
    color: #fff
}

@media screen and (max-width:47.9375em) {
    .plans-features .tabs:before,
    .plans-features .tabs:after {
        display: table;
        content: ""
    }
    .plans-features .tabs:after {
        clear: both
    }
    .plans-features .tabs:before,
    .plans-features .tabs:after {
        display: table;
        content: ""
    }
    .plans-features .tabs:after {
        clear: both
    }
    .plans-features .tabs li {
        display: block;
        float: left;
        width: 50%;
        text-align: center
    }
    .wf-active .plans-features .tabs li a {
        font-size: 1.625em
    }
}

.plans-features .tabs-new {
    border-bottom: 1px solid #ccc
}

.plans-features .tabs-new li {
    display: inline-block;
    list-style: none;
    padding: .5em 1em;
    border-bottom: 0;
    color: #000;
    font-size: 1.125em;
    text-decoration: none;
    text-transform: uppercase
}

.plans-features .tabs-new li+li {
    margin-left: -.3em
}

.wf-active .plans-features .tabs-new li {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 2.1875em
}

.ch .plans-features .tabs-new li {
    text-decoration: underline;
    border-color: #000
}

.plans-features .tabs-new li[aria-selected=true] {
    border-color: #000;
    background-color: #000;
    color: #fff
}

@media screen and (max-width:47.9375em) {
    .plans-features .tabs-new:before,
    .plans-features .tabs-new:after {
        display: table;
        content: ""
    }
    .plans-features .tabs-new:after {
        clear: both
    }
    .plans-features .tabs-new:before,
    .plans-features .tabs-new:after {
        display: table;
        content: ""
    }
    .plans-features .tabs-new:after {
        clear: both
    }
    .plans-features .tabs-new li {
        display: block;
        float: left;
        width: 50%;
        text-align: center
    }
    .wf-active .plans-features .tabs-new li a {
        font-size: 1.625em
    }
}

.plans-features .plans-include-wrap {
    margin: 0;
    overflow: hidden
}

.plans-features .plans-include-wrap.monthly-phones li {
    width: 33%
}

@media screen and (max-width:54.375em) {
    .plans-features .plans-include-wrap.monthly-phones li {
        width: 100%
    }
}

@media screen and (max-width:47.9375em) {
    .plans-features .plans-include-wrap.monthly-phones li {
        width: 100%
    }
}

.plans-features .plans-include-wrap dd {
    margin: 0
}

.plans-features .plans-include-wrap ul {
    list-style: none;
    margin: 1em 0;
    padding: 0
}

.plans-features .plans-include-wrap li {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    width: 25%;
    margin-right: -.4em;
    padding: 0 50px;
    text-align: center;
    font-size: 1.25em
}

.plans-features .plans-include-wrap li .has-tooltip {
    color: #e41c11
}

.plans-features .plans-include-wrap li+li:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    display: block;
    width: 1px;
    height: 2em;
    margin-top: -1em;
    background-color: #ccc
}

@media screen and (max-width:68.75em) {
    .plans-features .plans-include-wrap li {
        padding-left: 20px;
        padding-right: 20px
    }
}

@media screen and (max-width:54.375em) {
    .plans-features .plans-include-wrap ul {
        margin-top: 0
    }
    .plans-features .plans-include-wrap li {
        display: block;
        width: 100%;
        margin-right: 0;
        padding: 10px;
        font-size: 1em
    }
    .plans-features .plans-include-wrap li+li {
        border-top: 1px solid #ccc
    }
    .plans-features .plans-include-wrap li+li:before {
        display: none
    }
}

.plan-wrap .metadata {
    padding-top: 35px!important
}

.plan-wrap p.price {
    position: relative;
    padding-top: 60px
}

.plan-wrap p.price span.starting-from {
    position: absolute;
    top: 6px;
    left: 18px
}

.plan-wrap,
.phone-wrap,
.store-wrap,
.prepaid-plan-wrap {
    position: relative;
    margin: 1em 0;
    border: 1px solid #ccc
}

.txtbold{font-weight: bold;}

.plan-wrap .name,
.phone-wrap .name,
.store-wrap .name,
.prepaid-plan-wrap .name {
    margin: 0;
    padding: 10px;
    background: #000;
    color: #fff;
    font-size: 2em;
    text-align: center;
    text-transform: uppercase;
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif
}

.print .plan-wrap .name,
.print .phone-wrap .name,
.print .store-wrap .name,
.print .prepaid-plan-wrap .name {
    background: #fff;
    color: #000
}

.plan-wrap .phone-name,
.phone-wrap .phone-name,
.store-wrap .phone-name,
.prepaid-plan-wrap .phone-name {
    min-height: 65px;
    margin: 0;
    padding-left: 20px;
    border-bottom: 1px solid #ccc;
    font-size: 1.5em;
    line-height: 65px
}

.print .plan-wrap .phone-name,
.print .phone-wrap .phone-name,
.print .store-wrap .phone-name,
.print .prepaid-plan-wrap .phone-name {
    border-top: 1px solid #ccc
}

.plan-wrap .price,
.phone-wrap .price,
.store-wrap .price,
.prepaid-plan-wrap .price {
    margin: 0;
    padding: 30px 10px 10px;
    background-color: #e41c11;
    color: #fff;
    text-align: center;
    border: solid 5px #000
}

.print .plan-wrap .price,
.print .phone-wrap .price,
.print .store-wrap .price,
.print .prepaid-plan-wrap .price {
    background: #fff;
    color: #000
}

.wf-active .plan-wrap .price .currency,
.wf-active .phone-wrap .price .currency,
.wf-active .store-wrap .price .currency,
.wf-active .prepaid-plan-wrap .price .currency,
.wf-active .plan-wrap .price .per,
.wf-active .phone-wrap .price .per,
.wf-active .store-wrap .price .per,
.wf-active .prepaid-plan-wrap .price .per {
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-weight: 700
}

.plan-wrap .price .currency,
.phone-wrap .price .currency,
.store-wrap .price .currency,
.prepaid-plan-wrap .price .currency {
    position: relative;
    top: -.75em;
    left: .15em;
    font-size: 3.6em;
    color: #fff
}

.print .plan-wrap .price .currency,
.print .phone-wrap .price .currency,
.print .store-wrap .price .currency,
.print .prepaid-plan-wrap .price .currency {
    color: #000
}

@media screen and (max-width:47.9375em) {
    .plan-wrap .price .currency,
    .phone-wrap .price .currency,
    .store-wrap .price .currency,
    .prepaid-plan-wrap .price .currency {
        font-size: 3em
    }
}

.plan-wrap .price .dollars,
.phone-wrap .price .dollars,
.store-wrap .price .dollars,
.prepaid-plan-wrap .price .dollars {
    font-size: 6.5em;
    font-weight: 700;
    line-height: 1em;
    color: #fff;
    letter-spacing: -.008em;
    display: inline-block;
    min-height: 120px
}

.print .plan-wrap .price .dollars,
.print .phone-wrap .price .dollars,
.print .store-wrap .price .dollars,
.print .prepaid-plan-wrap .price .dollars {
    color: #000
}

.wf-active .plan-wrap .price .dollars,
.wf-active .phone-wrap .price .dollars,
.wf-active .store-wrap .price .dollars,
.wf-active .prepaid-plan-wrap .price .dollars {
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif
}

@media screen and (max-width:47.9375em) {
    .plan-wrap .price .dollars,
    .phone-wrap .price .dollars,
    .store-wrap .price .dollars,
    .prepaid-plan-wrap .price .dollars {
        font-size: 5em
    }
}

.plan-wrap .price .per,
.phone-wrap .price .per,
.store-wrap .price .per,
.prepaid-plan-wrap .price .per {
    font-size: 1.25em;
    text-transform: uppercase
}

.plan-wrap .metadata,
.phone-wrap .metadata,
.store-wrap .metadata,
.prepaid-plan-wrap .metadata {
    position: relative;
    background-color: #e41c11;
    border: solid 5px #000;
    padding: 35px 40px 0
}

.plan-wrap .metadata.img,
.phone-wrap .metadata.img,
.store-wrap .metadata.img,
.prepaid-plan-wrap .metadata.img {
    padding-top: 240px
}

.print .plan-wrap .metadata,
.print .phone-wrap .metadata,
.print .store-wrap .metadata,
.print .prepaid-plan-wrap .metadata {
    background: #fff;
    color: #000
}

.plan-wrap .metadata .metawrapper,
.phone-wrap .metadata .metawrapper,
.store-wrap .metadata .metawrapper,
.prepaid-plan-wrap .metadata .metawrapper {
    padding-bottom: 35px;
    border-left: solid 2px #000;
    border-right: solid 2px #000
}

.plan-wrap .metadata .metawrapper ul,
.phone-wrap .metadata .metawrapper ul,
.store-wrap .metadata .metawrapper ul,
.prepaid-plan-wrap .metadata .metawrapper ul {
    background-color: #fff;
    border: solid 5px #000;
    padding: 4px 0 4px 10px;
    margin: 0;
    list-style-type: none
}

.plan-wrap .metadata .metawrapper ul li,
.phone-wrap .metadata .metawrapper ul li,
.store-wrap .metadata .metawrapper ul li,
.prepaid-plan-wrap .metadata .metawrapper ul li {
    background: url(../images/pctelecom/Plus_icon.png) no-repeat left 3px;
    padding: 0 0 0 35px;
    color: #e41c11;
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1.875em;
    text-transform: uppercase;
    margin: 0;
    letter-spacing: -.03em
}

@media screen and (max-width:47.9375em) {
    .plan-wrap .metadata .metawrapper ul li,
    .phone-wrap .metadata .metawrapper ul li,
    .store-wrap .metadata .metawrapper ul li,
    .prepaid-plan-wrap .metadata .metawrapper ul li {
        font-size: 1.5em
    }
}

.print .plan-wrap .metadata .metawrapper,
.print .phone-wrap .metadata .metawrapper,
.print .store-wrap .metadata .metawrapper,
.print .prepaid-plan-wrap .metadata .metawrapper {
    border: 0
}

.print .plan-wrap .metadata .metawrapper ul,
.print .phone-wrap .metadata .metawrapper ul,
.print .store-wrap .metadata .metawrapper ul,
.print .prepaid-plan-wrap .metadata .metawrapper ul {
    border: 0;
    list-style-type: disc;
    padding: 0
}

.print .plan-wrap .metadata .metawrapper ul li,
.print .phone-wrap .metadata .metawrapper ul li,
.print .store-wrap .metadata .metawrapper ul li,
.print .prepaid-plan-wrap .metadata .metawrapper ul li {
    color: #000;
    background: 0 0;
    padding: 0
}

.plan-wrap .metadata:after,
.phone-wrap .metadata:after,
.store-wrap .metadata:after,
.prepaid-plan-wrap .metadata:after {
    border-left: solid 1px #000;
    border-right: solid 1px #000;
    background: url(../images/pctelecom/Plus_icon.png);
    width: 100%;
    height: 100%
}

.plan-wrap .metadata .thumb,
.phone-wrap .metadata .thumb,
.store-wrap .metadata .thumb,
.prepaid-plan-wrap .metadata .thumb {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    max-width: 100%;
    max-height: 100%
}

.plan-wrap .metadata .flag,
.phone-wrap .metadata .flag,
.store-wrap .metadata .flag,
.prepaid-plan-wrap .metadata .flag {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: block;
    padding: .25em 1.125em;
    background-color: #80b33a;
    color: #fff;
    font-size: 1.125em;
    font-weight: 700;
    text-transform: uppercase;
    text-align: center
}

.plan-wrap .metadata .mins-data,
.phone-wrap .metadata .mins-data,
.store-wrap .metadata .mins-data,
.prepaid-plan-wrap .metadata .mins-data {
    position: absolute;
    bottom: 0;
    left: 0;
    list-style: none;
    margin: 0;
    padding: 0
}

.plan-wrap .metadata .mins-data li,
.phone-wrap .metadata .mins-data li,
.store-wrap .metadata .mins-data li,
.prepaid-plan-wrap .metadata .mins-data li {
    display: block;
    float: left;
    clear: left;
    margin-top: .25em;
    padding: 0 1em .25em;
    background-color: #fff;
    color: #e41c11;
    font-size: 1.25em;
    white-space: nowrap
}

.print .plan-wrap .metadata .mins-data li,
.print .phone-wrap .metadata .mins-data li,
.print .store-wrap .metadata .mins-data li,
.print .prepaid-plan-wrap .metadata .mins-data li {
    color: #000
}

.wf-active .plan-wrap .metadata .mins-data li,
.wf-active .phone-wrap .metadata .mins-data li,
.wf-active .store-wrap .metadata .mins-data li,
.wf-active .prepaid-plan-wrap .metadata .mins-data li {
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif
}

.plan-wrap .metadata .mins-data .amount,
.phone-wrap .metadata .mins-data .amount,
.store-wrap .metadata .mins-data .amount,
.prepaid-plan-wrap .metadata .mins-data .amount {
    font-size: 2.375em;
    font-weight: 700
}

@media screen and (max-width:54.375em) {
    .plan-wrap .metadata .mins-data .amount,
    .phone-wrap .metadata .mins-data .amount,
    .store-wrap .metadata .mins-data .amount,
    .prepaid-plan-wrap .metadata .mins-data .amount {
        font-size: 2em
    }
}

.plan-wrap .actions,
.phone-wrap .actions,
.store-wrap .actions,
.prepaid-plan-wrap .actions {
    padding: 20px 0 0;
    text-align: center
}

.plan-wrap .actions .note,
.phone-wrap .actions .note,
.store-wrap .actions .note,
.prepaid-plan-wrap .actions .note {
    margin: .5em 0 1em
}

.plan-wrap .actions .button,
.phone-wrap .actions .button,
.store-wrap .actions .button,
.prepaid-plan-wrap .actions .button {
    width: 80%
}

.plan-wrap .options,
.phone-wrap .options,
.store-wrap .options,
.prepaid-plan-wrap .options {
    margin: 0;
    padding: 10px 20px
}

.print .plan-wrap .options,
.print .phone-wrap .options,
.print .store-wrap .options,
.print .prepaid-plan-wrap .options {
    border-top: 1px solid #ccc
}

.plan-wrap .options .has-tooltip,
.phone-wrap .options .has-tooltip,
.store-wrap .options .has-tooltip,
.prepaid-plan-wrap .options .has-tooltip {
    color: #e41c11
}

.wf-active .plan-wrap .options,
.wf-active .phone-wrap .options,
.wf-active .store-wrap .options,
.wf-active .prepaid-plan-wrap .options {
    font-family: FuturaLT-CondensedBold, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1.25em
}

.plan-wrap .option,
.phone-wrap .option,
.store-wrap .option,
.prepaid-plan-wrap .option {
    display: block;
    margin: 0;
    text-decoration: none
}

.ch .plan-wrap .option,
.ch .phone-wrap .option,
.ch .store-wrap .option,
.ch .prepaid-plan-wrap .option {
    text-decoration: underline
}

.print .plan-wrap .option,
.print .phone-wrap .option,
.print .store-wrap .option,
.print .prepaid-plan-wrap .option {
    text-decoration: none
}

.print .plan-wrap .option:hover,
.print .phone-wrap .option:hover,
.print .store-wrap .option:hover,
.print .prepaid-plan-wrap .option:hover {
    text-decoration: none
}

.plan-wrap .option:hover,
.phone-wrap .option:hover,
.store-wrap .option:hover,
.prepaid-plan-wrap .option:hover {
    text-decoration: underline
}

.plan-wrap .option+.option,
.phone-wrap .option+.option,
.store-wrap .option+.option,
.prepaid-plan-wrap .option+.option {
    margin-top: 5px
}

.plan-wrap .stacks-wrap,
.phone-wrap .stacks-wrap,
.store-wrap .stacks-wrap,
.prepaid-plan-wrap .stacks-wrap {
    margin: 0;
    padding: 0 20px
}

.plan-wrap .stacks-wrap .collapsible-trigger,
.phone-wrap .stacks-wrap .collapsible-trigger,
.store-wrap .stacks-wrap .collapsible-trigger,
.prepaid-plan-wrap .stacks-wrap .collapsible-trigger {
    border-bottom: 0;
    border-top: 1px solid #ccc;
    font-size: 1.5em;
    line-height: 2;
    text-transform: uppercase
}

.plan-wrap .stacks-wrap .collapsible-target,
.phone-wrap .stacks-wrap .collapsible-target,
.store-wrap .stacks-wrap .collapsible-target,
.prepaid-plan-wrap .stacks-wrap .collapsible-target {
    margin-left: 0
}

.plan-wrap .stacks-wrap dl,
.phone-wrap .stacks-wrap dl,
.store-wrap .stacks-wrap dl,
.prepaid-plan-wrap .stacks-wrap dl {
    margin-top: 0
}

.plan-wrap .stacks-wrap dl dt,
.phone-wrap .stacks-wrap dl dt,
.store-wrap .stacks-wrap dl dt,
.prepaid-plan-wrap .stacks-wrap dl dt {
    font-weight: 700
}

.plan-wrap .stacks-wrap dl dd,
.phone-wrap .stacks-wrap dl dd,
.store-wrap .stacks-wrap dl dd,
.prepaid-plan-wrap .stacks-wrap dl dd {
    margin-left: 0;
    margin-bottom: .5em
}

.phone-wrap .metadata .thumb {
    right: 0;
    bottom: 0;
    width: auto;
    height: 90%;
    margin: auto
}

.prepaid-plans-include-wrap dd {
    margin: 0
}

.prepaid-plans-include-items {
    margin: 20px 0;
    padding: 0;
    display: table;
    position: relative;
    text-align: center;
    width: 100%;
    font-size: 1.25em
}

.prepaid-plans-include-items .has-tooltip {
    color: #e41c11
}

.prepaid-plans-include-items li {
    display: table-cell;
    position: relative
}

.prepaid-plans-include-items li:after {
    content: '';
    display: inline-block;
    position: absolute;
    top: 40%;
    right: -1px;
    width: 6px;
    height: 6px;
    background: #000;
    border-radius: 6px
}

.prepaid-plans-include-items li:last-child:after {
    display: none
}

.lt-ie9 .prepaid-plans-include-items li {
    display: inline-block;
    padding: 0 20px
}

@media screen and (max-width:47.9375em) {
    .prepaid-plans-include-items {
        margin: 0
    }
    .prepaid-plans-include-items li {
        display: block;
        border-bottom: 1px solid #ccc;
        padding: .5em
    }
    .prepaid-plans-include-items li:after {
        display: none
    }
}

.prepaid-plan-wrap dl {
    margin: 0 0 10px
}

.prepaid-plan-wrap .amount {
    font-weight: 700
}

.prepaid-plan-wrap dd {
    margin: 0 20px;
    padding-left: 20px;
    padding-right: 20px;
    font-size: 1.25em;
    border-bottom: 1px solid #ccc;
    vertical-align: middle;
    padding: 10px 20px
}

.prepaid-plan-wrap dd.last {
    border: 0
}

.prepaid-plan-wrap dd.no-value {
    text-align: center;
    min-height: 75px;
    line-height: 50px
}

@media screen and (max-width:47.9375em) {
    .prepaid-plan-wrap dd.no-value {
        min-height: 0;
        line-height: 1em
    }
}

.prepaid-plan-wrap .has-tooltip {
    color: #e41c11
}

.add-ons h2:first-child {
    margin-bottom: 0
}

.add-ons .collapsible-trigger {
    color: #000
}

.add-ons .stacks-wrap {
    font-size: 1.25em
}

.add-ons dd {
    margin: 0
}

.add-ons dd dl {
    padding-bottom: 1em;
    margin-bottom: 0;
    border-bottom: 1px solid #ccc
}

.add-ons dd dl:before,
.add-ons dd dl:after {
    display: table;
    content: ""
}

.add-ons dd dl:after {
    clear: both
}

.add-ons dd dl:before,
.add-ons dd dl:after {
    display: table;
    content: ""
}

.add-ons dd dl:after {
    clear: both
}

.add-ons dd dl dt {
    width: 70%;
    float: left
}

.add-ons dd dl dd {
    float: right
}

.plan-hero-wrap {
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif
}

.plan-hero-wrap .plan-hero-text {
    max-width: 585px;
    min-height: 87px;
    margin: 40px auto 0;
    border-left: 3px solid #000;
    border-right: 3px solid #000;
    padding-bottom: 45px
}

.plan-hero-wrap .plan-hero-text span {
    display: block;
    max-width: 585px;
    font-size: 2.25em;
    background-color: #fff;
    border: 5px solid #000;
    border-left-width: 3px;
    border-right-width: 3px;
    padding: 20px 0;
    text-align: center;
    color: #e41c11;
    text-transform: uppercase
}

.plan-hero-wrap p {
    margin-top: 0
}

.plan-hero-wrap .hero {
    width: 100%
}

.plan-hero-wrap .messages {
    position: absolute;
    bottom: 0;
    left: 0;
    list-style: none;
    margin: 0;
    padding: 0
}

@media screen and (max-width:47.9375em) {
    .plan-hero-wrap .messages {
        position: static
    }
}

.plan-hero-wrap .messages li {
    display: block;
    float: left;
    clear: left;
    margin-top: .25em;
    padding: 0 1em .25em;
    background-color: #fff;
    color: #e41c11;
    white-space: nowrap;
    text-transform: uppercase
}

.wf-active .plan-hero-wrap .messages li {
    font-size: 1.875em
}

.print .plan-hero-wrap .messages li {
    color: #000
}

@media screen and (max-width:47.9375em) {
    .plan-hero-wrap .messages li {
        padding: 10px 0;
        width: 100%;
        font-size: 1.625em;
        white-space: normal;
        border-bottom: 1px solid #ccc
    }
    .plan-hero-wrap .messages li:last-child {
        border-bottom: 0
    }
}

.coverage h2 {
    margin: 0;
    text-transform: uppercase
}

.coverage iframe {
    border: 0;
    width: 100%;
    height: 700px
}

.coverage .rates {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    margin-right: 0;
    border-right-width: 1px
}

.coverage .rates:before,
.coverage .rates:after {
    display: table;
    content: ""
}

.coverage .rates:after {
    clear: both
}

.coverage .rates:before,
.coverage .rates:after {
    display: table;
    content: ""
}

.coverage .rates:after {
    clear: both
}

.coverage .rates dl {
    margin: 0
}

.coverage .rates dt {
    clear: left;
    padding: .5em;
    background-color: #000;
    text-transform: uppercase;
    color: #fff
}

.wf-active .coverage .rates dt {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1.75em
}

.coverage .rates dd {
    margin: 0 0 1em
}

.coverage .rates dd:before,
.coverage .rates dd:after {
    display: table;
    content: ""
}

.coverage .rates dd:after {
    clear: both
}

.coverage .rates dd:before,
.coverage .rates dd:after {
    display: table;
    content: ""
}

.coverage .rates dd:after {
    clear: both
}

.coverage .rates dd+dt {
    margin-top: 1em
}

.coverage .rates table {
    margin-bottom: 0;
    font-size: 1.25em
}

@media screen and (max-width:47.9375em) {
    .coverage .rates table {
        font-size: .85em
    }
}

.coverage .rates table th {
    vertical-align: bottom
}

.coverage .rates table td {
    font-weight: 700;
    vertical-align: top
}

.coverage .rates .long-distance-zones table {
    margin: 0
}

.coverage .rates .long-distance-zones table th {
    border-right: 1px solid #ccc;
    border-left: 1px solid #ccc;
    padding: 1em 1.5em;
    font-family: "Open Sans Condensed", Arial, Helvetica, sans-serif;
    font-weight: 700;
    text-transform: none
}

.coverage .rates .long-distance-zones table thead th {
    background-color: #fff;
    text-align: center;
    vertical-align: middle
}

.coverage .rates .long-distance-zones table tbody th {
    width: 80px;
    text-align: center;
    vertical-align: middle
}

.coverage .rates .long-distance-zones table tbody th+td {
    text-align: left
}

.coverage .rates .long-distance-zones table tbody th+td+td {
    width: 120px;
    vertical-align: middle
}

.coverage .rates .long-distance-zones table tbody td {
    padding: 1em 1.5em;
    font-weight: 400
}

.coverage .rates .long-distance-rates,
.coverage .rates .roaming-rates {
    padding: 0 10px 10px;
    border: 1px solid #ccc
}

.coverage .rates .long-distance-rates .rate-summary,
.coverage .rates .roaming-rates .rate-summary {
    display: none
}

.coverage .rates .long-distance-rates label,
.coverage .rates .roaming-rates label {
    display: inline-block;
    margin-right: .4em
}

.coverage .rates .long-distance-rates .rate-summary-country,
.coverage .rates .roaming-rates .rate-summary-country,
.coverage .rates .long-distance-rates .rate-summary-zone,
.coverage .rates .roaming-rates .rate-summary-zone {
    font-weight: 700
}

.coverage .rates .long-distance-rates th.rate-summary-country,
.coverage .rates .roaming-rates th.rate-summary-country {
    font-weight: 400
}

.coverage .rates .long-distance-rates .rate-summary-table tbody th,
.coverage .rates .roaming-rates .rate-summary-table tbody th,
.coverage .rates .long-distance-rates .rate-service-table tbody th,
.coverage .rates .roaming-rates .rate-service-table tbody th {
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    vertical-align: middle;
    font-family: "Open Sans Condensed", Arial, Helvetica, sans-serif;
    font-weight: 700;
    text-align: center;
    text-transform: none;
    background-color: #fff
}

.coverage .rates .long-distance-rates .rate-summary-table tbody td,
.coverage .rates .roaming-rates .rate-summary-table tbody td,
.coverage .rates .long-distance-rates .rate-service-table tbody td,
.coverage .rates .roaming-rates .rate-service-table tbody td {
    vertical-align: middle;
    font-weight: 400
}

.coverage .rates .long-distance-rates .rate-summary-table th,
.coverage .rates .long-distance-rates .rate-summary-table td {
    padding: .5em 1em
}

.coverage .rates .roaming-rates .rate-summary-table {
    width: 100%
}

.coverage .rates .rate-service-table th,
.coverage .rates .rate-service-table td {
    padding: .5em 1em
}

.coverage .rates small {
    font-size: 1em;
    width: 50%;
    float: left;
    position: relative;
    padding-left: 10px;
    padding-right: 10px
}

@media screen and (max-width:47.9375em) {
    .coverage .rates small {
        width: 100%;
        float: left;
        position: relative;
        padding-left: 10px;
        padding-right: 10px
    }
}

.coverage .map-large {
    width: 83.33333333%;
    float: left
}

.coverage .map-large img {
    width: 100%
}

.coverage .map-legend {
    float: left;
    width: 16.66666667%
}

.coverage .map-legend ul {
    list-style: none;
    padding: 0
}

.coverage .map-legend ul li p {
    margin-left: 1.667em
}

.coverage .map-legend ul li strong:before {
    content: '';
    display: inline-block;
    vertical-align: middle;
    width: 15px;
    height: 14px;
    margin: 0 .5em 0 0;
    border-radius: 3px
}

.coverage .map-legend ul li.zone-1a strong:before {
    background-color: #8fa16f
}

.coverage .map-legend ul li.zone-1b strong:before {
    background-color: #549dd2
}

.coverage .map-legend ul li.zone-2 strong:before {
    background-color: #fdaf33
}

.coverage .map-legend ul li.zone-canada strong:before {
    background-color: #ee2d24
}

.coverage .roaming-rates-zone {
    clear: none!important;
    width: 50%;
    float: left;
    position: relative;
    padding-left: 10px;
    padding-right: 10px
}

.coverage .roaming-rates-zone table {
    width: 100%;
    margin-top: 0
}

.coverage .roaming-rates-zone table thead tr:first-child th {
    border-top: 1px solid #ccc;
    background-color: #fff
}

.coverage .roaming-rates-zone table tbody th {
    border-top: 1px solid #ccc;
    background-color: #fff
}

.coverage .roaming-rates-zone table th {
    text-align: center;
    text-transform: none;
    font-weight: 700
}

.coverage .roaming-rates-zone table td {
    font-weight: 400
}

.coverage .roaming-rates-zone table th,
.coverage .roaming-rates-zone table td {
    padding: .5em 1em;
    vertical-align: middle;
    font-family: "Open Sans Condensed", Arial, Helvetica, sans-serif
}

.coverage .roaming-rates-zone.float {
    clear: both!important
}

.coverage .roaming-rates-zone~p {
    padding-left: 10px;
    padding-right: 10px
}

.rates small+h2 {
    padding-left: 10px;
    padding-right: 10px;
    clear: both;
    padding-top: 20px
}

.prepaid .long-distance-rates {
    margin-bottom: 2em
}

.prepaid .long-distance-rates table {
    font-size: 1.25em
}

.prepaid .long-distance-rates label {
    display: inline-block;
    margin-right: .4em
}

.prepaid .long-distance-rates .rate-summary-country,
.prepaid .long-distance-rates .rate-summary-zone {
    font-weight: 700
}

.prepaid .long-distance-rates th.rate-summary-country {
    font-weight: 400
}

.prepaid .long-distance-rates .rate-summary-table,
.prepaid .long-distance-rates .rate-service-table {
    margin-bottom: .5em
}

.prepaid .long-distance-rates .rate-summary-table tbody th,
.prepaid .long-distance-rates .rate-service-table tbody th {
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    vertical-align: middle;
    font-family: "Open Sans Condensed", Arial, Helvetica, sans-serif;
    font-weight: 700;
    text-align: center;
    text-transform: none;
    background-color: #fff
}

.prepaid .long-distance-rates .rate-summary-table tbody td,
.prepaid .long-distance-rates .rate-service-table tbody td {
    vertical-align: middle;
    font-weight: 400
}

.prepaid .long-distance-rates .rate-summary-table th,
.prepaid .long-distance-rates .rate-summary-table td {
    padding: .5em 1em
}

#store-locator-wrap {
    display: block;
    position: relative;
    margin-bottom: 2em
}

#store-locator-wrap address {
    margin: 0;
    font-style: normal
}

#store-locator-wrap .storeName,
#store-locator-wrap .storeAddress1,
#store-locator-wrap .storeAddress2,
#store-locator-wrap .storeOwner,
#store-locator-wrap .storeServicesToggle {
    display: block
}

#store-locator-wrap .storeName {
    text-decoration: none
}

.wf-active #store-locator-wrap .storeName {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-weight: 400;
    font-size: 1.25em;
    text-transform: uppercase
}

#store-locator-wrap .storeHoursToggle {
    display: block;
    margin: 0 0 0 1.85em;
    text-decoration: none
}

.wf-active #store-locator-wrap .storeHoursToggle {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-weight: 400;
    font-size: 1.25em;
    text-transform: uppercase
}

#store-locator-wrap .storeHoursToggle:after {
    content: ' \25BC';
    font-size: .6875em;
    text-decoration: none!important
}

#store-locator-wrap .storeHoursToggle.open:after {
    content: ' \25B2'
}

#store-locator-wrap .storeHoursListWrap {
    display: none;
    margin: 0 0 0 2.333em;
    padding: 0
}

#store-locator-wrap .storeHoursList {
    width: 100%;
    margin: .5em 0;
    border: 0;
    font-size: 100%;
    line-height: inherit
}

#store-locator-wrap .storeHoursList caption {
    text-align: left
}

.wf-active #store-locator-wrap .storeHoursList caption {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-weight: 400;
    font-size: 1.25em;
    text-transform: uppercase
}

.wf-active #store-locator-wrap .storeHoursList+strong {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-weight: 400;
    font-size: 1.25em;
    text-transform: uppercase
}

#store-locator-wrap .storeHoursList th {
    padding: 0;
    border: 0;
    background: 0 0;
    text-transform: none;
    text-align: left;
    font-weight: 400;
    font-style: italic
}

#store-locator-wrap .storeHoursList tbody th {
    font-style: normal
}

#store-locator-wrap .storeHoursList td {
    padding: 0;
    border: 0;
    text-align: left
}

#store-locator-wrap .storeServicesList {
    list-style: none;
    margin: 0;
    padding: 0
}

#store-locator-results-wrap {
    position: relative
}

#store-locator-results-wrap img {
    max-width: none
}

#store-locator-results-wrap>h2 {
    margin: 0;
    font-size: 1.5em;
    text-transform: uppercase
}

#store-locator-results-wrap>h2~p {
    margin-top: .5em
}

#store-locator-results-wrap .button {
    font-size: 1.25em;
    font-weight: 400
}

#store-locator-results-wrap .store-search-form input[type=text] {
    width: 85%
}

#store-locator-results-wrap .store-search-form button[type=submit] {
    width: 15%
}

@media screen and (max-width:54.375em) {
    #store-locator-results-wrap .store-search-form input[type=text] {
        width: 80%
    }
    #store-locator-results-wrap .store-search-form button[type=submit] {
        width: 20%
    }
}

#store-locator-results {
    position: relative;
    margin-top: 13px;
    font-size: 1.25em
}

#store-locator-results .resultsList {
    list-style: none;
    margin: .667em 0 0;
    padding: 0
}

#store-locator-results .resultsList>li {
    margin: 1em 0;
    padding: .5em 0 1.333em;
    border-bottom: 1px solid #ccc
}

#store-locator-results .resultsList>li:last-child {
    border-bottom: 0
}

#store-locator-results .resultsList .marker {
    float: left
}

#store-locator-results .resultsList .storeDistance {
    float: right;
    margin: 0 0 1em 1em;
    font-weight: 700
}

#store-locator-results .resultsList address {
    margin-left: 2.333em;
    *margin-top: -1em
}

#store-locator-results .resultsList address+.button {
    margin: .5em 0 .5em 1.85em
}

#store-locator-static-map-wrap {
    display: none
}

#store-locator-map-wrap {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 10px;
    border: 10px solid #eee
}

#store-locator-map {
    height: 800px
}

#store-locator-map .storeInfoWindow {
    width: 250px;
    font-size: 16px
}

#store-locator-map .storeInfoWindow .storeLogo {
    display: block;
    max-width: 250px;
    height: 45px;
    margin-bottom: .5em
}

#store-locator-map .storeInfoWindow a {
    font-weight: 700
}

#store-locator-map .storeInfoWindow address {
    margin-bottom: .5em
}

#store-locator-map .storeInfoWindow address span {
    font-family: "Open Sans Condensed", "Open Sans Condensed", Arial, Helvetica, sans-serif;
    font-size: 16px
}

.wf-active #store-locator-map .storeInfoWindow .storeName {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    text-transform: uppercase
}

#store-locator-map .storeInfoWindow .storeName,
#store-locator-map .storeInfoWindow .storeAddress1,
#store-locator-map .storeInfoWindow .storeAddress2 {
    display: block
}

#store-locator-map .storeInfoWindow .storeActions {
    margin: .5em 0;
    padding: .5em 0;
    border-top: 1px solid #ccc;
    border-bottom: 1px solid #ccc
}

#store-locator-map .storeInfoWindow .storeActions a {
    display: block
}

#store-locator-map .storeInfoWindow .storeHoursToggle {
    display: none
}

#store-locator-map .storeInfoWindow .storeHoursListWrap {
    display: block;
    margin: 0
}

#store-locator-map .storeInfoWindow .storeHoursList {
    margin: 0;
    font-family: "Open Sans Condensed", "Open Sans Condensed", Arial, Helvetica, sans-serif;
    line-height: 1.4
}

#store-locator-map .storeInfoWindow .storeServicesList,
#store-locator-map .storeInfoWindow .storeHoursList+strong {
    display: none
}

.legal-page h2,
.legal-page h3,
.legal-page p {
    margin: 1em 0
}

.legal-page h2 {
    font-size: 2em
}

.legal-page h3 {
    font-size: 1.5em
}

.legal-page ol {
    padding-left: 1.25em
}

.legal-page .sub-section {
    display: none
}

.legal-page .tabs {
    border-bottom: 1px solid #ccc
}

.legal-page .tabs li {
    display: inline-block;
    list-style: none
}

.legal-page .tabs li+li {
    margin-left: -.3em
}

.legal-page .tabs li a {
    padding: .5em 1em;
    display: block;
    border: 1px solid #ccc;
    border-bottom: 0;
    color: #000;
    font-size: 1.125em;
    text-decoration: none;
    text-transform: uppercase
}

.wf-active .legal-page .tabs li a {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1.375em
}

.ch .legal-page .tabs li a {
    text-decoration: underline;
    border-color: #000
}

.legal-page .tabs li.active a {
    border-color: #e41c11;
    background-color: #e41c11;
    color: #fff
}

#interiorPageContent.container_12 h3 {
    font-size: 1.875em;
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    color: #e41c11;
    margin: 0 0 0 10px;
    line-height: 2em;
    border-bottom: 1px solid #ccc
}

.offers-banner {
    background: #e41c11;
    border: 4px solid #000;
    margin-bottom: 60px
}

.offers-banner .offers-banner-text {
    width: 75%;
    margin-right: 0;
    border-right: 2px solid #000;
    padding: 0;
    display: inline-block;
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
    margin-top: 40px;
    margin-right: 40px
}

.offers-banner .offers-banner-text h1,
.offers-banner .offers-banner-text h2,
.offers-banner .offers-banner-text h3,
.offers-banner .offers-banner-text h4,
.offers-banner .offers-banner-text h5 {
    margin: 0
}

@media screen and (max-width:28.125em) {
    .offers-banner .offers-banner-text {
        margin-top: 20px;
        margin-right: 20px
    }
}

@media screen and (max-width:47.9375em) {
    .offers-banner .offers-banner-text {
        min-width: 50%;
        width: auto
    }
}

.offers-banner .offers-banner-text h2 {
    font-size: 1em;
    font-weight: 400;
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif
}

.offers-banner .offers-banner-text span {
    width: auto;
    margin: 0 auto;
    border-right: 2px solid #000;
    border-top: 4px solid #000;
    border-bottom: 4px solid #000;
    display: block;
    background-color: #fff;
    color: #e41c11;
    text-transform: uppercase;
    font-size: 2.625em;
    padding: .6em .88em .4em;
    line-height: 1.25em
}

@media screen and (max-width:37.5em) {
    .offers-banner .offers-banner-text span {
        font-size: 2em
    }
}

.offers-banner .offers-banner-text span br {
    display: inline
}

@media screen and (max-width:64em) {
    .offers-banner .offers-banner-text span br {
        display: none
    }
}

.offers-banner .offers-banner-text .offers-banner-subtext {
    color: #fff;
    font-size: 2.375em;
    padding: .6em 1.05em .4em
}

@media screen and (max-width:37.5em) {
    .offers-banner .offers-banner-text .offers-banner-subtext {
        font-size: 1.5em
    }
}

.offers-banner .offers-banner-text .offers-banner-subtext .bigDollar {
    font-size: 2.3em;
    border: 0;
    background-color: transparent;
    padding: 0;
    color: #fff;
    display: inline-block;
    letter-spacing: -.1em
}

.offers-banner .offers-banner-text .offers-banner-subtext .bigDollar sup {
    font-size: .522em
}

.offers-banner .offers-banner-text .offers-banner-subtext-sm {
    color: #fff;
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1em;
    padding: 15px 0 45px 29px
}

div.grid_8 div.promotionItems div.item img {
    margin-bottom: 3em
}

div.grid_8 div.promotionItems div.item h2 {
    font-size: 120%!important;
    color: #000;
    padding: 0;
    margin: 0 0 10px;
    line-height: 1.1em;
    text-transform: uppercase;
    border: 0;
    clear: none!important
}

div.grid_8 div.promotionItems div.item .offers-banner {
    max-width: 725px
}

div.grid_8 div.promotionItems div.item .offers-banner .offers-banner-text {
    width: auto
}

div.grid_8 div.promotionItems div.item .offers-banner .offers-banner-text span {
    font-size: 1.6em
}

div.grid_8 div.promotionItems div.item .offers-banner .offers-banner-subtext {
    font-size: 1em;
    padding: 10px 29px 0
}

div.grid_8 div.promotionItems div.item .offers-image {
    float: right;
    padding: 30px 20px 0 0
}

#interiorPageContent div.callout h3.title-with-bubble {
    text-transform: uppercase;
    color: #e31c2a!important;
    font-size: 110%!important;
    padding-left: 20px;
    background: transparent url(../images/pctelecom/icon_bubble.gif) no-repeat 0 0;
    line-height: 16px;
    margin: 0 0 10px!important;
    border: 0
}

.hideIfNoTOSAccept {
    display: none
}

.error #page-header {
    height: 93px
}

.error #site-tools,
.error #nav-global {
    display: none
}

.error #error-hero {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0 2em;
    background: #fff;
    text-align: center
}

.error #error-hero h1,
.error #error-hero h2 {
    margin: 0;
    text-transform: uppercase
}

.error #error-hero h1 {
    font-size: 4.375rem;
    letter-spacing: -3px
}

.error #error-hero h2 span {
    color: #e41c11
}

.error #error-hero img {
    max-width: 100%;
    margin: 2em auto
}

.error #error-hero p {
    margin: 0
}

.error #error-hero p+h1 {
    margin-top: 1.5em
}

[lang=fr] .error .promo-tiles-home .two-tone {
    margin-right: 0
}

[lang=fr] .error .promo-tiles-home .tile-thumb {
    display: none
}

.two-columns:before,
.two-columns:after {
    display: table;
    content: ""
}

.two-columns:after {
    clear: both
}

.two-columns:before,
.two-columns:after {
    display: table;
    content: ""
}

.two-columns:after {
    clear: both
}

.two-columns .col {
    float: left;
    width: 50%
}

.two-columns .col:before,
.two-columns .col:after {
    display: table;
    content: ""
}

.two-columns .col:after {
    clear: both
}

.two-columns .col:before,
.two-columns .col:after {
    display: table;
    content: ""
}

.two-columns .col:after {
    clear: both
}

@media screen and (max-width:37.5em) {
    .two-columns .col {
        width: 100%;
        float: none
    }
}

.two-columns .col:first-child {
    padding-right: 20px
}

@media screen and (max-width:37.5em) {
    .two-columns .col:first-child {
        padding: 0
    }
}

.two-columns .col:last-child {
    padding-left: 20px
}

@media screen and (max-width:37.5em) {
    .two-columns .col:last-child {
        padding: 0
    }
}

.plans p {
    font-size: 2em;
    margin-top: 0;
    margin-bottom: 30px
}

@media screen and (max-width:28.125em) {
    .plans p {
        font-size: 1.5em
    }
}

.plans p+.plans p {
    margin-top: .5em
}

.plans .big li {
    font-size: 2em
}

@media screen and (max-width:28.125em) {
    .plans .big li {
        font-size: 1.5em
    }
}

.plans .offers-banner {
    margin-bottom: 15px
}

.plans .callout {
    text-align: center;
    border: 4px solid #000
}

.plans .col h3 {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 2.625em;
    text-transform: uppercase;
    margin-top: 0;
    margin-bottom: .3em
}

.plans .three-columns {
    margin-bottom: 20px
}

.plans .three-columns:before,
.plans .three-columns:after {
    display: table;
    content: ""
}

.plans .three-columns:after {
    clear: both
}

.plans .three-columns:before,
.plans .three-columns:after {
    display: table;
    content: ""
}

.plans .three-columns:after {
    clear: both
}

.plans .three-columns .col {
    float: left;
    width: 33%;
    padding-left: 20px;
    padding-right: 20px
}

@media screen and (max-width:58.125em) {
    .plans .three-columns .col {
        float: left;
        width: 50%
    }
}

@media screen and (max-width:37.5em) {
    .plans .three-columns .col {
        float: none;
        width: 100%;
        padding-left: 0;
        padding-right: 0
    }
}

.plans .three-columns .col:nth-child(2n+1):last-child {
    clear: none;
    width: 33%
}

@media screen and (max-width:58.125em) {
    .plans .three-columns .col:nth-child(2n+1):last-child {
        clear: both;
        width: 100%;
        padding-left: 0
    }
}

@media screen and (max-width:37.5em) {
    .plans .three-columns .col:nth-child(2n+1):last-child {
        clear: none;
        width: 100%
    }
}

.plans .three-columns .col:nth-child(3n) {
    padding-right: 0
}

@media screen and (max-width:58.125em) {
    .plans .three-columns .col:nth-child(3n) {
        padding-right: 20px
    }
}

.plans .three-columns .col:first-child,
.plans .three-columns .col:nth-child(3n+1) {
    padding-left: 0
}

@media screen and (max-width:56.25em) {
    .plans .three-columns .col:first-child,
    .plans .three-columns .col:nth-child(3n+1) {
        clear: both
    }
}

.plans .three-columns .col img {
    width: 33%;
    float: right;
    margin-left: 16px;
    margin-bottom: 16px
}

@media screen and (max-width:58.125em) {
    .plans .three-columns .col img {
        width: 20%
    }
}

@media screen and (max-width:37.5em) {
    .plans .three-columns .col img {
        width: 33%
    }
}

.plans .three-columns .col+.col {
    border-left: 1px solid #000
}

@media screen and (max-width:58.125em) {
    .plans .three-columns .col+.col {
        border-left: 0
    }
}

.plans .two-columns .image {
    padding-right: 10px;
    padding-left: 10px;
    float: left;
    width: 20%;
    margin: 0 auto
}

@media screen and (max-width:58.125em) {
    .plans .two-columns .image {
        float: left;
        width: 25%
    }
}

@media screen and (max-width:37.5em) {
    .plans .two-columns .image {
        padding-right: 0;
        padding-left: 0;
        float: none;
        max-width: 50%;
        margin-bottom: 1em
    }
}

.plans .two-columns .image img {
    max-width: 100%
}

.plans .two-columns .text {
    float: right;
    width: 70%
}

@media screen and (max-width:37.5em) {
    .plans .two-columns .text {
        float: none;
        width: 100%
    }
}

.plans .two-columns .text p {
    font-size: 1.5em;
    margin-bottom: .5em
}

h2.black-bar {
    padding: .5em 1.21em .3em;
    color: #fff;
    background-color: #000;
    text-transform: uppercase
}

h2.futura-bold {
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 2.0625em
}

@media screen and (max-width:28.125em) {
    h2.futura-bold {
        font-size: 1.5em
    }
}

.panels .grid_6 {
    padding-bottom: 20px
}

@media screen and (max-width:47.9375em) {
    .panels .grid_6 {
        padding-bottom: 0
    }
}

.panels .grid_6:nth-child(2n+1) {
    padding-left: 0
}

@media screen and (max-width:47.9375em) {
    .panels .grid_6:nth-child(2n+1) {
        padding-right: 0
    }
}

.panels .grid_6:nth-child(2n+2) {
    padding-right: 0
}

@media screen and (max-width:47.9375em) {
    .panels .grid_6:nth-child(2n+2) {
        padding-left: 0
    }
}

.panels .grid_6:nth-child(2n+1):nth-last-child(2) {
    width: 50%;
    padding-left: 0;
    padding-right: 0;
    left: 25%
}

@media screen and (max-width:47.9375em) {
    .panels .grid_6:nth-child(2n+1):nth-last-child(2) {
        width: 100%;
        left: 0
    }
}

.panels:before,
.panels:after {
    display: table;
    content: ""
}

.panels:after {
    clear: both
}

.panels:before,
.panels:after {
    display: table;
    content: ""
}

.panels:after {
    clear: both
}

.panels .panel {
    border: 4px solid #000;
    display: table;
    width: 100%
}

@media screen and (max-width:47.9375em) {
    .panels .panel {
        margin-bottom: 2em
    }
}

.panels .panel .top {
    display: table-row;
    min-height: 2em;
    background-color: #e41c11
}

.panels .panel .content {
    border-top: 4px solid #000;
    padding-top: 1em;
    padding-bottom: 1em;
    display: flex;
    flex-direction: column
}

.panels .panel p,
.panels .panel ul,
.panels .panel .block {
    padding-left: 39px;
    padding-right: 39px
}

@media screen and (max-width:28.125em) {
    .panels .panel p,
    .panels .panel ul,
    .panels .panel .block {
        padding-left: 20px;
        padding-right: 20px
    }
}

.panels .panel .block {
    margin-bottom: 1em;
    margin-top: auto
}

.panels .panel img {
    display: block;
    max-width: 100%;
    width: auto;
    margin: 0 auto
}

.panels .panel li,
.panels .panel p {
    font-size: 1.5em
}

@media screen and (max-width:28.125em) {
    .panels .panel li,
    .panels .panel p {
        font-size: 1em
    }
}

.panels .panel p.center {
    text-align: center
}

.panels .panel li {
    line-height: 1.3em
}

.panels .panel ul {
    margin-top: 0;
    list-style-type: none
}

.panels .panel li:before {
    position: absolute;
    content: 'â€¢';
    font-size: 28px;
    left: 21px
}

@media screen and (max-width:28.125em) {
    .panels .panel li:before {
        font-size: 20px
    }
}

.panels .panel li+li {
    margin-top: .75em
}

.panels .panel h3 {
    display: table-cell;
    vertical-align: middle;
    margin: 0;
    color: #fff;
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
    text-transform: uppercase;
    font-size: 2.375em;
    padding: .6em 1.05em .4em
}

@media screen and (max-width:37.5em) {
    .panels .panel h3 {
        font-size: 1.5em
    }
}

.panels .panel .price {
    padding-left: 22px;
    padding-right: 22px;
    text-align: center;
    font-size: 2.4em;
    letter-spacing: 0;
    margin: 0;
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif
}

@media screen and (max-width:64em) {
    .panels .panel .price {
        font-size: 2.2em
    }
}

@media screen and (max-width:56.25em) {
    .panels .panel .price {
        font-size: 2em
    }
}

@media screen and (max-width:28.125em) {
    .panels .panel .price {
        font-size: 1.5em
    }
}

.panels .panel .price .currency {
    position: relative;
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 1.2em;
    top: -.6em
}

.panels .panel .price .dollars {
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-size: 2em
}

@media screen and (max-width:57.188em) {
    .wf-active .panels .panel .price .dollars {
        font-size: 2.8125em
    }
}

.panels .panel .price .period {
    position: relative;
    left: -.5em;
    font-size: .8em
}

.panels .panel .price .period .period-abbr {
    display: none
}

@media screen and (max-width:54.375em) {
    .panels .panel .price .period .period-abbr {
        display: inline
    }
}

.panels .panel .price .period .period-full {
    display: inline
}

@media screen and (max-width:54.375em) {
    .panels .panel .price .period .period-full {
        display: none
    }
}

@media screen and (max-width:47.9375em) {
    .panels .panel .price {
        margin: 0
    }
}

.panels .panel .options {
    list-style: none
}

.panels .panel .options li {
    position: relative
}

.panels .panel .options li:before {
    display: none
}

.panels .panel .options li a.prefix:before,
.panels .panel .options li span.prefix:before {
    left: -30px;
    width: 30px;
    position: absolute;
    text-align: center
}

@media screen and (max-width:28.125em) {
    .panels .panel .options li a.prefix:before,
    .panels .panel .options li span.prefix:before {
        left: -15px;
        width: 15px
    }
}

.panels .panel .options li a.prefix.plus:before,
.panels .panel .options li span.prefix.plus:before {
    content: '+'
}

.panels .panel .options li a.prefix.minus:before,
.panels .panel .options li span.prefix.minus:before {
    content: '-'
}

.panels .panel .options li a.prefix.dot:before,
.panels .panel .options li span.prefix.dot:before {
    content: 'â€¢'
}

.panels .panel .options li a.prefix.circle:before,
.panels .panel .options li span.prefix.circle:before {
    content: 'â—‹';
    font-size: .6em
}

.panels .panel .options li+li {
    margin-top: .4em
}

.panels .panel .options li>ul li {
    font-size: 1em
}

.panels .center {
    text-align: center
}

.panels .top-padding {
    padding-top: 15px
}

.smaller,
.panels .panel p.smaller,
.panels .panel .options li {
    font-size: 1.8125em
}

@media screen and (max-width:58.125em) {
    .smaller,
    .panels .panel p.smaller,
    .panels .panel .options li {
        font-size: 1.375em
    }
}

@media screen and (max-width:47.9375em) {
    .smaller,
    .panels .panel p.smaller,
    .panels .panel .options li {
        font-size: 1.25em
    }
}

.smallest,
.device-pricing-modal p.disclaimer {
    font-size: 1.5625em
}

@media screen and (max-width:58.125em) {
    .smallest,
    .device-pricing-modal p.disclaimer {
        font-size: 1.25em
    }
}

@media screen and (max-width:47.9375em) {
    .smallest,
    .device-pricing-modal p.disclaimer {
        font-size: 1.125em
    }
}

.default-font {
    font-family: sans-serif
}

sup {
    font-size: .6em;
    line-height: 0
}

sup a {
    color: #000
}

sup a:visited {
    color: #000
}

small sup {
    font-size: .8em;
    vertical-align: -.3em
}

.futura-bold {
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif
}

.red {
    color: #e41c11
}

.grey {
    color: #696969
}

.device-pricing-modal h3 {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    margin: 0
}

.device-pricing-modal table {
    width: 100%;
    border: 4px #000 solid;
    margin: .5em 0
}

.device-pricing-modal td,
.device-pricing-modal th {
    text-align: center
}

.device-pricing-modal th {
    padding-top: 20px;
    padding-bottom: 20px;
    font-size: 1.375em;
    background-color: #e41c11;
    color: #fff;
    text-transform: uppercase;
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif
}

.device-pricing-modal td.subhead {
    background-color: #f1f2f2;
    font-family: FuturaLT-CondensedBold, "Trebuchet MS", Arial, Helvetica, sans-serif
}

.device-pricing-modal tbody td {
    padding-top: 8px;
    padding-bottom: 8px;
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif
}

.device-pricing-modal tbody td:first-child {
    border-right: 1px #000 solid
}

.device-pricing-modal p.disclaimer {
    margin-bottom: 0
}

.data-usage-alerts-modal .col {
    display: table
}

.data-usage-alerts-modal .image {
    height: 120px;
    width: 100%;
    display: table-row
}

.data-usage-alerts-modal .image img {
    display: block;
    margin: 0 auto
}

.data-usage-alerts-modal .centering-wrap {
    vertical-align: bottom;
    display: table-cell;
    width: 100%
}

.data-usage-alerts-modal .text h2 {
    font-size: 2em;
    margin-bottom: .5em;
    margin-top: .5em;
    text-transform: uppercase
}

.data-usage-alerts-modal .text h3 {
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    margin: 0;
    font-size: 1.6em
}

.data-usage-alerts-modal .text ul {
    margin: 0;
    margin-bottom: .8em
}

.data-usage-alerts-modal .text ul,
.data-usage-alerts-modal .text p {
    font-size: 1.5em
}

@media screen and (max-width:28.125em) {
    .data-usage-alerts-modal .text ul,
    .data-usage-alerts-modal .text p {
        font-size: 1.2em
    }
}

.data-usage-alerts-modal .text ul ul {
    font-size: 1em
}

.data-usage-alerts-modal .text li {
    line-height: 1.3em;
    margin-bottom: .8em
}

#cboxClose {
    background-color: transparent;
    border: 0;
    overflow: hidden;
    display: block;
    background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
    background-size: 500px 500px;
    background-position: -120px -120px;
    width: 32px;
    height: 32px;
    box-shadow: none;
    -webkit-box-shadow: none
}

#cboxClose:before {
    content: "";
    display: block;
    width: 0;
    height: 150%
}

.no-svg #cboxClose {
    background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
    background-position: -120px -120px;
    width: 32px;
    height: 32px
}

#cboxClose:hover {
    background-position: -120px -160px
}

#cboxLoadedContent {
    padding: 26px;
    padding-top: 80px
}

#travelling h3 {
    color: #e41c11;
    font-size: 1.7em;
    margin: 0;
    text-transform: uppercase
}

#travelling dt {
    background-color: #e41c11;
    margin-bottom: 15px
}

#travelling thead {
    color: #e41c11
}

#travelling thead p {
    text-align: left;
    margin: 5px
}

#travelling thead th {
    color: #fff;
    background-color: #e41c11
}

#travelling thead th p {
    font-size: .5em
}

#travelling #voice-headers {
    color: #e41c11;
    text-transform: uppercase
}

#travelling #sms-headers {
    color: #e41c11;
    text-transform: uppercase
}

#travelling #data-headers2 {
    color: #e41c11;
    text-transform: uppercase
}

#travelling #data-headers {
    color: #e41c11;
    text-transform: uppercase
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0
}

#sitemap h2 {
    margin: .5em 0
}

#sitemap h4 {
    margin: .3em 0;
    font-size: 1.23em
}

#sitemap ul {
    list-style-type: none;
    padding-left: 20px;
    margin-top: 0;
    margin-bottom: 0
}

#sitemap li {
    padding: 2px
}

li[role=tab] {
    cursor: pointer
}

.tabbedContent[aria-hidden=true] {
    display: none
}

.tabbedContent[aria-hidden=false] {
    display: block
}

div.pinned[aria-hidden=true] {
    display: block
}

#page-main .phone-detail .plan-options-selector {
    margin-top: 28px!important;
    float: left!important;
    max-width: 50%!important;
    top: 0!important
}

#page-main .phone-detail .current-plan-container .plan-options-selector {
    padding-bottom: 0!important
}

#page-main .phone-detail .current-plan-option {
    padding-bottom: 20px!important
}

#page-main .product-new-activation {
    margin-bottom: 0!important
}

@media screen and (max-width:54.375em) {
    #page-main .phone-detail .current-plan-container .product-new-activation {
        margin-top: 0!important;
        margin-bottom: 0!important
    }
}

table.faq {
    width: 100%
}

table.faq th {
    display: none;
    text-transform: none;
    font-size: 25px;
    white-space: nowrap;
    text-align: center;
    font-weight: 700
}

@media (min-width:480px) {
    table.faq th {
        background-color: #000;
        color: #fff
    }
}

table.faq td {
    display: block;
    border-bottom: 0;
    border-top: 0
}

@media (min-width:480px) {
    table.faq td {
        border-top: 1px #ccc solid
    }
}

table.faq td:first-child {
    padding-top: .5em;
    border-top: 1px #ccc solid
}

@media (min-width:480px) {
    table.faq td:first-child {
        padding-top: .25em
    }
}

table.faq td:last-child {
    padding-bottom: .5em;
    border-bottom: 0
}

table.faq td:before {
    width: 100%;
    color: #666;
    font-size: 20px;
    text-align: center;
    content: attr(data-label);
    display: inline-block
}

@media (min-width:480px) {
    table.faq td:before {
        display: none
    }
}

table.faq td p:first-child {
    margin-top: 0
}

table.faq td p:last-child {
    margin-bottom: 0
}

table.faq th,
table.faq td {
    vertical-align: middle
}

@media (min-width:480px) {
    table.faq th,
    table.faq td {
        display: table-cell;
        padding: .25em .5em
    }
}

@media screen and (max-width:47.9375em) {
    #page-wrapper {
        min-height: 500px;
        overflow: hidden
    }
    #page-header>.container_12,
    #page-main,
    #page-footer,
    #nav-global {
        width: 100%;
        -webkit-transition: -webkit-transform 350ms ease;
        transition: transform 350ms ease;
        -webkit-transform: translate(0, 0);
        -ms-transform: translate(0, 0);
        transform: translate(0, 0)
    }
    .csstransforms .nav-open #page-header>.container_12,
    .csstransforms .nav-open #page-main,
    .csstransforms .nav-open #page-footer {
        -webkit-transform: translate(82%, 0);
        -ms-transform: translate(82%, 0);
        transform: translate(82%, 0)
    }
    .no-csstransforms .nav-open #page-header>.container_12,
    .no-csstransforms .nav-open #page-main,
    .no-csstransforms .nav-open #page-footer {
        position: relative;
        left: 82%
    }
    .csstransforms .nav-open #nav-global {
        -webkit-transform: translate(-100%, 0);
        -ms-transform: translate(-100%, 0);
        transform: translate(-100%, 0)
    }
    .no-csstransforms .nav-open #nav-global {
        left: -100%
    }
    .nav-item-has-child-arrow {
        content: '';
        display: block;
        position: absolute;
        top: 50%;
        right: 10px;
        background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
        background-size: 500px 500px;
        background-position: -160px -8px;
        width: 15px;
        height: 15px
    }
    .no-svg .nav-item-has-child-arrow {
        background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
        background-position: -160px -8px;
        width: 15px;
        height: 15px
    }
    #nav-global {
        position: absolute;
        top: 0;
        left: -100%;
        width: 82%;
        border: 0;
        background: #fff;
        -webkit-box-shadow: inset -6px 0 8px -5px #c6c6c6;
        box-shadow: inset -6px 0 8px -5px #c6c6c6
    }
    .csstransforms #nav-global {
        left: 0;
        -webkit-transform: translate(-100%, 0);
        -ms-transform: translate(-100%, 0);
        transform: translate(-100%, 0)
    }
    #nav-global>.container_12 {
        -webkit-transition: -webkit-transform 350ms ease;
        transition: transform 350ms ease
    }
    .csstransforms .nav-sub-open.nav-open #nav-global>.container_12 {
        -webkit-transform: translate(-100%, 0);
        -ms-transform: translate(-100%, 0);
        transform: translate(-100%, 0)
    }
    .no-csstransforms .nav-sub-open.nav-open #nav-global>.container_12 {
        position: relative;
        left: -100%
    }
    #nav-primary {
        margin: 0;
        padding: 0
    }
    #nav-primary .nav-sub a:before {
        display: none
    }
    #nav-primary .nav-heading:after {
        content: '';
        display: block;
        position: absolute;
        top: 50%;
        right: 10px;
        background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
        background-size: 500px 500px;
        background-position: -160px -8px;
        width: 15px;
        height: 15px
    }
    .no-svg #nav-primary .nav-heading:after {
        background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
        background-position: -160px -8px;
        width: 15px;
        height: 15px
    }
    .monthly #nav-monthly .nav-heading:before,
    .prepaid #nav-monthly .nav-heading:before,
    .monthly #nav-prepaid .nav-heading:before,
    .prepaid #nav-prepaid .nav-heading:before {
        display: none
    }
    .monthly #nav-monthly .nav-heading+.nav-sub,
    .prepaid #nav-monthly .nav-heading+.nav-sub,
    .monthly #nav-prepaid .nav-heading+.nav-sub,
    .prepaid #nav-prepaid .nav-heading+.nav-sub {
        display: none
    }
    #nav-secondary {
        position: static;
        right: auto;
        z-index: auto;
        margin: 0;
        padding: 0;
        text-align: left
    }
    #nav-secondary li {
        display: block
    }
    #nav-secondary li.has-flyout {
        cursor: pointer
    }
    #nav-secondary li.has-flyout:hover a {
        background: 0 0;
        color: #000
    }
    #nav-secondary li.has-flyout:hover .nav-flyout {
        display: none
    }
    #nav-secondary li.has-flyout>a:after {
        content: '';
        display: block;
        position: absolute;
        top: 50%;
        right: 10px;
        background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat;
        background-size: 500px 500px;
        background-position: -160px -8px;
        width: 15px;
        height: 15px
    }
    .no-svg #nav-secondary li.has-flyout>a:after {
        background: url(../images/pctelecom/pcmobile-sprite.png) no-repeat;
        background-position: -160px -8px;
        width: 15px;
        height: 15px
    }
    #nav-secondary .nav-flyout {
        position: absolute;
        top: 3rem;
        bottom: auto;
        left: 100%;
        width: 100%;
        margin: 0;
        padding: 0;
        border: 0;
        background: 0 0
    }
    #nav-secondary .nav-flyout li {
        float: none
    }
    #nav-secondary .nav-flyout-section {
        width: 100%
    }
    #nav-secondary .nav-flyout-section+.nav-flyout-section {
        margin: 0
    }
    #nav-secondary .nav-flyout-section h2 {
        margin: 0;
        padding: 0;
        border: 0
    }
    #nav-secondary .nav-flyout-submenu {
        display: none
    }
    .links-interior-nav {
        display: none
    }
    .accordion {
        border-bottom: 1px solid #ccc
    }
    .accordion dt {
        margin: 0;
        border-top: 1px solid #ccc;
        padding-top: .5em;
        padding-bottom: .5em;
        padding-left: 1.33em;
        color: #000;
        font-size: 1.285em;
        font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
        font-weight: 400
    }
    .accordion dt:before {
        margin-top: -.444em;
        border-width: .444em .666em;
        border-left-color: #5f5f5f;
        top: 50%
    }
    .accordion dt.open {
        border-bottom: 0
    }
    .accordion dt.open:before {
        margin-top: -.277em;
        border-width: .666em .444em;
        border-top-color: #5f5f5f;
        border-left-color: transparent
    }
    .accordion-plus-icons {
        border-bottom: 1px solid #b2b2b2
    }
    .accordion-plus-icons dt {
        border-top: 1px solid #b2b2b2;
        padding-left: 58px;
        padding-top: 24px;
        padding-bottom: 22px;
        margin-bottom: 0;
        margin-top: 0;
        text-transform: none;
        font-family: "Open Sans Condensed", Arial, Helvetica, sans-serif!important;
        font-size: 20px!important;
        font-weight: 700;
        letter-spacing: 0
    }
    .accordion-plus-icons dt:before,
    .accordion-plus-icons dt.open:before {
        margin-top: 0;
        border: 0;
        top: 22px
    }
    .accordion-plus-icons dd {
        padding-left: 58px
    }
    .monthly .action-items .button,
    .prepaid .action-items .button {
        font-size: 1.125rem;
        font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif
    }
    .monthly .action-items .button-left,
    .prepaid .action-items .button-left,
    .monthly .action-items .button-right,
    .prepaid .action-items .button-right {
        width: 50%;
        text-align: center
    }
    .monthly .action-items .button-left i,
    .prepaid .action-items .button-left i,
    .monthly .action-items .button-right i,
    .prepaid .action-items .button-right i {
        display: none
    }
    .monthly .action-items dt,
    .prepaid .action-items dt {
        max-width: none;
        float: none
    }
    .monthly .action-items dd,
    .prepaid .action-items dd {
        margin-top: 1em;
        float: none
    }
    .container_12 .support-tiles {
        padding: 0
    }
    .support-tiles li {
        margin-bottom: 0;
        border: 0;
        border-top: 1px solid #000;
        height: auto
    }
    .container_12 .support-tiles li {
        padding: 0
    }
    .support-tiles li p {
        margin: 0
    }
    .support-tiles li p:last-of-type {
        border-bottom: 1px solid #ccc
    }
    .support-tiles li p a {
        border-top: 1px solid #ccc;
        padding: .75em;
        padding-right: 29px;
        display: block;
        position: relative;
        font-size: 1.142em;
        text-decoration: none
    }
    .support-tiles li p a:after {
        margin-top: -7px;
        width: 9px;
        height: 14px;
        display: block;
        position: absolute;
        top: 50%;
        right: 10px;
        background: url(../images/pctelecom/pcmobile-sprite.svg) no-repeat -374px -31px;
        content: ''
    }
    .support-tiles li .button {
        margin: .857em;
        position: static
    }
    .support-tiles [class^=icon-support-],
    .support-tiles [class*=" icon-support-"] {
        margin-top: 8px;
        margin-right: 8px;
        width: 31px;
        height: 31px;
        background-size: 276px 101px
    }
    .support-tiles .icon-support-calendar {
        background-position: 0 -69px
    }
    .support-tiles .icon-support-selfserve {
        background-position: -31px -69px
    }
    .support-tiles .icon-support-bill {
        background-position: -62px -69px
    }
    .support-tiles .icon-support-globe {
        background-position: -93px -69px
    }
    .support-tiles .icon-support-services {
        background-position: -124px -69px
    }
    .support-tiles .icon-support-guide {
        background-position: -155px -69px
    }
    .table-wrapper .scrollable table {
        margin-left: 35px
    }
    .pinned {
        width: 35px
    }
    table th {
        font-size: .875rem
    }
    #phone-compare-wrapper {
        padding: 0
    }
    .phone-compare td[scope=row] {
        border-top: 1px solid #eee;
        line-height: 1;
        text-align: center
    }
    .overthrow-enabled.csstransforms3d .phone-compare td[headers] {
        -webkit-transform: translate3d(0, 0, 0)
    }
    .overthrow-enabled.csstransforms3d .overthrow .phone-compare .th-wrap {
        display: none
    }
    .phone-compare .th-wrap {
        position: relative;
        height: 100%
    }
    .phone-compare .th-wrap span {
        display: block;
        position: absolute;
        top: -.333em;
        left: -.333em;
        -webkit-transform: rotate(90deg) translateX(1.5em);
        transform: rotate(90deg) translateX(1.5em);
        -webkit-transform-origin: 1rem 50%;
        transform-origin: 1rem 50%;
        white-space: nowrap
    }
    .phone-compare #phone-top_features {
        padding-top: 5px
    }
    [lang=en] .phone-compare #phone-price {
        height: 3.25em!important
    }
    [lang=en] .phone-compare #phone-os {
        height: 2em!important
    }
    [lang=en] .phone-compare #phone-top_features {
        height: 11em!important
    }
    [lang=fr] .phone-compare #phone-price {
        height: 2.95em!important
    }
    [lang=fr] .phone-compare #phone-os {
        height: 6.5em!important
    }
    [lang=fr] .phone-compare #phone-top_features {
        height: 18.85em!important
    }
    #phone-details-wrapper {
        left: auto;
        padding: 0;
        border-width: 1px 0 0;
        background-image: none
    }
    #nav-phones {
        padding: 0
    }
    #nav-phones ul {
        display: block;
        white-space: nowrap
    }
    #nav-phones ul li {
        margin-right: -4px;
        min-width: 235px;
        height: 140px;
        display: inline-block;
        float: none;
        vertical-align: top
    }
    #nav-phones ul a {
        border-width: 0 1px 1px 0;
        text-align: left;
        white-space: normal
    }
    #nav-phones ul a img {
        margin-right: 10px;
        height: 100%;
        float: left
    }
    #nav-phones ul a h4,
    #nav-phones ul a p {
        font-size: .937em
    }
    #nav-phones ul a h4 {
        font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
        font-weight: 400
    }
    #phone-details {
        padding: 6px 12px
    }
    #phone-details .sharing-tools {
        display: none
    }
    #phone-details .phone-name {
        margin-top: .5em
    }
    #phone-details .phone-photos {
        position: relative
    }
    #phone-details .phone-photo {
        display: block;
        width: auto;
        height: auto;
        max-width: 44%;
        max-height: 100%;
        position: absolute;
        top: 0;
        right: 12px
    }
    #phone-details .phone-photo-thumb,
    #phone-details .phone-document {
        display: block
    }
    #phone-details .phone-document {
        width: 50px;
        line-height: 1.25;
        text-align: center;
        white-space: normal
    }
    #phone-details .phone-document .icon {
        margin-right: auto;
        margin-bottom: 5px;
        margin-left: auto;
        display: block
    }
    #phone-details .phone-metadata {
        padding: 0
    }
    #phone-details .phone-metadata ul {
        padding-left: 0
    }
    #phone-details .phone-metadata li {
        margin-bottom: .5em;
        list-style-type: none
    }
    #phone-details p,
    #phone-details ul,
    #phone-details .phone-specs {
        margin-right: 0
    }
    #colorbox #phone-details {
        padding: 0
    }
    #colorbox #phone-details .phone-name {
        margin-top: 0;
        margin-right: 2em;
        font-size: .9375rem
    }
    #colorbox #phone-details .phone-photos,
    #colorbox #phone-details .phone-metadata {
        padding: 0
    }
    #colorbox #phone-details .phone-photo-thumb,
    #colorbox #phone-details .phone-document {
        margin-left: 10px
    }
    #colorbox #phone-details .phone-photo {
        margin-right: 10px
    }
    #colorbox #phone-details p,
    #colorbox #phone-details ul,
    #colorbox #phone-details .phone-specs {
        margin-right: 0
    }
    #phone-price-wrapper {
        min-width: 100%;
        margin-right: -12px;
        margin-left: -12px
    }
    #phone-price-wrapper .phone-price-inner {
        padding: 0
    }
    #phone-price-wrapper .phone-price {
        padding: .85em;
        font-size: 100%
    }
    #phone-price-wrapper .phone-price .term {
        max-width: 65%;
        min-height: 5.5em;
        font-size: 60%
    }
    #phone-price-wrapper .phone-price .sale-wrap {
        font-size: 60%
    }
    #phone-price-wrapper .phone-price:first-child .price-tooltip-content {
        left: 0;
        right: auto
    }
    #phone-price-wrapper .phone-price .price-tooltip-wrap {
        top: 0;
        right: 0
    }
    .plans-features h1+.grid_12 {
        padding: 0
    }
    .features {
        width: 100%
    }
    .features.wizard-plans {
        margin-top: 1em
    }
    .features th {
        font-size: .75rem
    }
    .features thead th[scope] {
        width: 25%
    }
    .features thead th[scope] span {
        font-size: .8125rem
    }
    .features thead th .button {
        margin: 0 .333em 1em;
        padding-left: .75em;
        padding-right: .75em;
        white-space: normal;
        word-break: break-word
    }
    .features [headers=plan-monthly_fee],
    .features [headers=plan-any_time],
    .features [headers=plan-data_included] {
        font-size: .9375rem
    }
    .features small,
    .features~.legal {
        display: block;
        padding: 12px
    }
    .wizard-phones {
        width: 100%;
        height: 440px;
        padding: 0;
        border-width: 1px 0;
        border-style: solid;
        border-color: #ccc;
        white-space: nowrap
    }
    .wizard-phones .grid_2 {
        display: inline-block;
        vertical-align: top;
        width: 41%;
        height: 440px;
        margin-right: -.4em;
        padding: 10px;
        border-radius: 0;
        white-space: normal
    }
    .wizard-phones .grid_2+.grid_2 {
        border-left: 1px solid #ccc
    }
    .wizard-phones .grid_2 .button {
        width: 100%;
        padding-left: 0;
        padding-right: 0
    }
    .wizard-phones .grid_2 h4 {
        min-height: 4em;
        margin: 1em 0;
        text-transform: uppercase;
        font-size: .9375rem
    }
    .wizard-phones .grid_2 .details {
        font-size: .9375rem
    }
    .nav-wizard ol {
        border-bottom: 1px solid #ccc;
        text-align: left
    }
    .nav-wizard li {
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        display: block;
        max-width: none;
        width: 100%;
        margin: 0;
        padding: 10px 15px;
        border-top: 1px solid #ccc;
        opacity: .4
    }
    .nav-wizard li:before,
    .nav-wizard li:after {
        display: table;
        content: ""
    }
    .nav-wizard li:after {
        clear: both
    }
    .nav-wizard li:before,
    .nav-wizard li:after {
        display: table;
        content: ""
    }
    .nav-wizard li:after {
        clear: both
    }
    [lang=fr] .nav-wizard li {
        max-width: none
    }
    .nav-wizard li a {
        border-radius: 0;
        padding: 0;
        background-color: transparent;
        color: #000;
        font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
        font-size: 1.125rem;
        font-weight: 400;
        text-decoration: none;
        text-transform: uppercase;
        white-space: normal;
        -webkit-box-shadow: none;
        box-shadow: none
    }
    .nav-wizard li p {
        display: block;
        margin: .25em 0 0 2em
    }
    .nav-wizard li.active {
        color: #000;
        opacity: 1
    }
    .nav-wizard li.active a {
        position: relative;
        background-color: transparent;
        -webkit-box-shadow: none;
        box-shadow: none
    }
    .nav-wizard li.active a:before,
    .nav-wizard li.active a:after {
        display: none
    }
    .nav-wizard li.complete {
        opacity: 1
    }
    .nav-wizard li.complete .icon-checked {
        width: 22px;
        height: 22px;
        margin: 0 0 0 -.25em;
        background-position: -291px -1px
    }
    .nav-wizard li.complete .details {
        margin: 0 0 0 2em
    }
    .nav-wizard li.complete .details img {
        display: none
    }
    .nav-wizard li.complete .details h4 {
        font-family: "Open Sans Condensed", Arial, Helvetica, sans-serif;
        font-weight: 700
    }
    .nav-wizard li.complete .details p {
        margin: 0;
        font-weight: 400
    }
    .email-wizard-trigger {
        width: 100%;
        font-size: 1.125rem!important;
        text-align: center
    }
    .email-wizard-trigger .icon-btn-email {
        display: none
    }
    .email-wizard-trigger+.button {
        display: none!important
    }
    .wizard-summary {
        margin-top: 1em;
        padding: 0!important
    }
    .wizard-summary .action-items {
        margin: 0
    }
    .wizard-summary .action-items dt {
        display: none
    }
    .wizard-summary .action-items dd {
        margin: 0
    }
    .wizard-summary .phone,
    .wizard-summary .plan,
    .wizard-summary .legal {
        width: auto;
        margin: 0;
        padding: 0
    }
    .wizard-summary .phone dl,
    .wizard-summary .plan dl,
    .wizard-summary .legal dl {
        border: 0
    }
    .wizard-summary .phone dt,
    .wizard-summary .plan dt,
    .wizard-summary .legal dt {
        padding: 14px 20px;
        font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
        font-weight: 400;
        font-size: .8125rem
    }
    .wizard-summary .phone dd,
    .wizard-summary .plan dd,
    .wizard-summary .legal dd {
        padding: 20px
    }
    .wizard-summary .phone .callout,
    .wizard-summary .plan .callout,
    .wizard-summary .legal .callout {
        margin-bottom: 0
    }
    .wizard-summary .phone .model {
        margin: 0
    }
    .wizard-summary .phone .price {
        margin: 0
    }
    .wizard-summary .phone .term {
        margin-top: .333em
    }
    .wizard-summary .plan h4 {
        display: none
    }
    .wizard-summary .plan ul {
        margin-top: 0;
        padding: 0 0 0 1.25em
    }
    .wizard-summary .legal {
        padding: 0 20px
    }
    .wrapped-img {
        font-size: 1em
    }
    .wrapped-img.right img,
    .wrapped-img.left img {
        margin: auto;
        width: 100%;
        max-width: none;
        float: none
    }
    .wrapped-img.right h2,
    .wrapped-img.left h2,
    .wrapped-img.right h3,
    .wrapped-img.left h3,
    .wrapped-img.right p,
    .wrapped-img.left p {
        margin-right: 0;
        margin-left: 0
    }
    .wrapped-img.right h2,
    .wrapped-img.left h2,
    .wrapped-img.right h3,
    .wrapped-img.left h3 {
        margin-top: .5em;
        margin-bottom: .5em
    }
    .contact-main .monthly,
    .contact-main .prepaid {
        padding-right: 0;
        padding-left: 0
    }
    .contact-main .monthly ul,
    .contact-main .prepaid ul,
    .contact-main .monthly a.more,
    .contact-main .prepaid a.more {
        margin-right: auto;
        margin-left: auto;
        width: 75%;
        display: block
    }
    .contact-secondary .store-search-form {
        clear: both
    }
    .contact-info h2 {
        margin-right: 40px
    }
    .coverage .map-large {
        width: auto;
        height: auto;
        float: none
    }
    .coverage .map-large img {
        width: 100%
    }
    .coverage .map-legend {
        width: 100%;
        height: auto;
        float: none;
        overflow: auto
    }
    .coverage .map-legend li {
        padding: 0 .25em;
        width: 25%;
        display: block;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        float: left
    }
    .coverage .roaming-rates-zone {
        width: 100%;
        float: none
    }
    .coverage .roaming-rates-zone+.roaming-rates-zone {
        margin-left: 0
    }
    .coverage .sub-section {
        margin-top: 1em
    }
    div.grid_8 div.promotionItems div.item img {
        margin: auto auto 1em;
        float: none;
        display: block
    }
    div.promotionItems div.item {
        padding-top: 0
    }
    div#genericContent h5 {
        clear: both
    }
    .alphaAnchorList th {
        word-break: break-word
    }
    .alphaAnchorList .inlineAlphaLink {
        margin-bottom: .5em!important
    }
    .coverage .tabs,
    .terms .tabs {
        padding: 0 12px
    }
    .coverage .tabs li a,
    .terms .tabs li a {
        padding: .667em 1em
    }
    .genericIntroBanner {
        display: none
    }
    .mainTabbedContainer {
        top: auto
    }
    .coverage .tabs,
    .tabs-nav {
        padding: 0;
        border-bottom: 1px solid #ccc
    }
    #genericContent ul.coverage .tabs,
    #genericContent ul.tabs-nav {
        margin: 0 -20px
    }
    .coverage .tabs li,
    .tabs-nav li {
        border-top: 1px solid #ccc;
        display: block
    }
    .coverage .tabs li a,
    .tabs-nav li a {
        min-height: 0;
        border-radius: 0;
        background: #fff;
        font-size: .875rem;
        white-space: normal
    }
    .coverage .tabs li a span,
    .tabs-nav li a span {
        display: inline
    }
    .coverage .tabs~.sub-section,
    .tabs-nav~.sub-section {
        margin-top: 0;
        border-top: 0
    }
    table.compare th {
        font-size: .8em;
        line-height: 1.25
    }
    #coverage p {
        margin-left: 0!important
    }
    #store-locator-wrap {
        font-size: 100%
    }
    #store-locator-results-wrap {
        padding: 0
    }
    #store-locator-results-wrap div.checkbox {
        margin: 0 0 .333em
    }
    #store-locator-results .resultsList {
        padding-right: 0
    }
    #store-locator-static-map-wrap {
        display: block;
        margin: 1em 0 1em -12px
    }
    #store-locator-static-map-image {
        max-width: 100%;
        height: auto
    }
    #store-locator-map-wrap {
        display: none
    }
    .error #page-header {
        height: 60px
    }
    .error #error-hero {
        padding: 2.333em
    }
    .error #error-hero h1,
    .error #error-hero h2 {
        margin: 0;
        text-transform: uppercase
    }
    .error #error-hero h1 {
        font-size: 2.5rem
    }
    .error #error-hero h2 {
        font-size: .75rem
    }
    .error #error-hero img {
        display: block;
        max-width: 90%;
        margin: 1em auto .5em
    }
    .error #error-hero p {
        display: inline
    }
}

.rockstopper-page {
    padding-top: 16px
}

.rockstopper-page h1,
.rockstopper-page h2 {
    text-transform: uppercase
}

.rockstopper-page h2 {
    font-size: 32px;
    padding-top: 10px
}

.rockstopper-page .promo-tiles-interior h2 {
    margin-top: 0;
    padding-top: 0
}

.rockstopper-page .section {
    margin-top: 40px
}

.rockstopper-page .section:before,
.rockstopper-page .section:after {
    display: table;
    content: ""
}

.rockstopper-page .section:after {
    clear: both
}

.rockstopper-page .section:before,
.rockstopper-page .section:after {
    display: table;
    content: ""
}

.rockstopper-page .section:after {
    clear: both
}

@media screen and (max-width:47.9375em) {
    .rockstopper-page .section {
        margin-top: 20px
    }
}

.rockstopper-page .callout {
    padding: 30px;
    border-top: 0;
    margin: 0
}

.rockstopper-page .callout h1 {
    font-size: 30px;
    font-weight: 400;
    font-family: FuturaLT-CondensedLight, "Trebuchet MS", Arial, Helvetica, sans-serif;
    color: #fff;
    margin-top: 0
}

.rockstopper-page .callout .text {
    font-family: FuturaLT-Bold, "Trebuchet MS", Arial, Helvetica, sans-serif;
    font-weight: 400;
    margin-top: 10px;
    margin-bottom: 10px
}

.rockstopper-page .rockstopper-legal p {
    font-size: 13px;
    line-height: 1.25em;
    letter-spacing: 0
}

.rockstopper-page .rockstopper-legal p:first-child {
    font-size: 18px;
    font-weight: 700
}

body.print #page-header {
    background: 0 0;
    border-bottom: 1px solid #000
}

body.print #logo-print {
    width: 143px;
    height: 44px;
    margin: 1em 0
}

.modal-footerPad{padding: 18px 0 30px 10px;}
.pcLogo {width:105px}
.letterSpacing0{letter-spacing: 0;}
.pc-heading{letter-spacing: 1px;}
.borderTop{border-top: 1px solid #c7c7c7;}
p{font-size:14px;color:#000000;line-height:19px; "Open Sans Condensed", "Open Sans Condensed", Arial, Helvetica, sans-serif;text-transform:none; letter-spacing: 0;}
.boxContainer{border: 1px solid #c7c7c7; padding: 25px 30px; margin:0 15px;}
h2{letter-spacing: 0;}


.txtSize19{font-size: 19px;}
.pad-left-right30{padding: 0 30px;}
.pad-left-right15{padding: 0 15px;}

.boxContainer h2{font-size: 19px;}

.lineHeight22{line-height: 22px;}
.noPad{padding: 0;}
@media screen and (width: 768px){
    .btnPC{
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        border: 0;
        padding: .5rem 1.25rem;
        display: inline-block;
        background: #e41c11;
        color: #fff;
      
        text-decoration: none;
        text-align: center;
        font-family:  FuturaLT-CondensedLight,"Trebuchet MS",Arial,Helvetica,sans-serif;

}}
.btnfocus button{ background: none; border: none;}

.btnfocus button:hover{ background: none;}

@media screen and (max-width:767px){
    .PCfooter{bottom: 0; left: 0; position: absolute; width: 100%; height:110px;}
    .PCfooterHome{bottom: 0; left: 0; position: relative; width: 100%; height:110px;}
    }
@media screen and (min-width: 768px){
    .height210 {height: 210px;}
    .PCfooter{bottom: 0; left: 0; position: absolute; width: 100%; height:110px;}
    .PCfooterHome{bottom: 0; left: 0; position: absolute; width: 100%; height:110px;}
    .height417{height: 417px;}
    .sub-heading30{font-size:30px; line-height: 28px;}
    .width100{width:100%;}
    .footer-link{background: #000; padding: 20px 30px;
      
    }
.footer-link ul{list-style: none;  padding-left: 0;}
.footer-link ul li{ list-style: none; float: left; margin:0 0 10px 0;}
.footer-link  a{color: #ffffff; padding-right: 25px; 
    font-family: FuturaLT-CondensedLight,"Trebuchet MS",Arial,Helvetica,sans-serif;
    text-transform:capitalize;font-size: 16px; text-align: left;
}
.txtSizeMob-desktp{font-size: 34px; line-height: 28px;}
.footer-link p{color: #c7c7c7; font-size: 12px; line-height: 14px;}
.marginLeft52{margin-left: 52px;}
.minusMargin20Tp{margin-top: -20px;}
.minusMargin10Tp{margin-top: -10px;}
.txtCenterLeft{text-align: center;}

}
@media only screen and (width: 768px){

    .height210 {height: auto!important;}
}
@media screen and (max-width: 767px){
    .txtSizeMob-desktp{font-size: 30px; line-height:32px;}
    .height210 {height: auto;}
  
    .btnPC{
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        border: 0;
        padding: .5rem 1.25rem;
        display: inline-block;
        background: #e41c11;
        color: #fff;
        text-decoration: none;
        text-align: center;
        font-family:  FuturaLT-CondensedLight,"Trebuchet MS",Arial,Helvetica,sans-serif;
        width:100%;
    }
    .txtCenterLeft{text-align: left;}
    .minusMargin10Tp{margin-top: 0;}
    .marginLeft52{margin-left: 0;}
    .footer-link{background: #000; padding: 20px ;}
    .footer-link ul{list-style: none;  padding-left: 0;}
.footer-link ul li{ list-style: none; float: none; display: table-cell; margin:0 0 10px 0; text-align: center;}
.footer-link  a{color: #ffffff; padding-right: 25px;
font-family: FuturaLT-CondensedLight,"Trebuchet MS",Arial,Helvetica,sans-serif;
text-transform: capitalize;font-size: 16px; text-align: center;
}

.footer-link p{color: #c7c7c7; font-size: 11px;}
.modal .modal-dialog.modal-md.bell-modal-md {
    margin: 0;
    position: absolute;
    bottom: 0;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255,255,255,.15);
    
}
.sub-heading29{font-size:29px;}
.height417{height: auto;}
.modal-footerPad{padding:15px;}
.txtCenterMb{text-align: center;}
.padTop10mb{padding-top: 10px;}
.modal-dialog {
    vertical-align: middle;
}
}

.margin-30-bot{margin-bottom: 30px;}
.displayB{display: block;}
.displayInlineB{display: inline-block;}
.margin-20-bottom{margin-bottom: 20px;}
@media (max-width: 999px)
{
    header .skip-to-main-link{

        display:none!important;
    }

}

/*BRF3/Virgin Component Styleguide*/
.big-title,.title,.small-title,.small-title-2{font-family: "VMUltramagneticNormalRegular", Arial, Helvetica, sans-serif;font-weight: normal}
.big-title{font-size:41px;font-weight:normal;line-height:1}
.title{font-size:28px;font-weight:normal;line-height:1}
.small-title-1{font-size:26px;font-weight:normal;line-height:1}
.small-title-2{font-size:22px;font-weight:normal;line-height:1}
.subtitle{font-size:18px;font-weight:bold;line-height:1}
.surtitle{font-size:14px;font-weight:bold;line-height:1}
.small-copy{font-size:12px;font-weight:normal;line-height:14px}
.tiny-copy{font-size:10px;font-weight:normal;line-height:1}

.form-control {
    display: block;
    width: 100%;
    height: 44px;
    background-color: #fff;
    background-image: none;
    border: 1px solid #d1d1d1;
    padding-left: 15px;
    padding-right: 15px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,0);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.0);
    border-radius: 0;
}

.form-control.form-control-gray {
    background-color: #f4f4f4;
}

.form-control:focus {
    border-color: #BD2025;
}

.minheight-50 {
    min-height: 50px;
}

.img-responsive {
    display: block;
    max-width: 100%;
    height: auto;
}

.has-error .form-control, .has-error .form-control:focus {
    border: 2px solid #CC0000;
}

.form-group.has-error label, .form-group.has-error .form-error {
    color: #CC0000;
}

.form-error {
    display: none;
}

.has-error .form-error.error {
    display: block;
}

/*START MODALS*/
/* Modal changes from shop */
.modal-content {
    border-radius: 10px 10px 0 0;
    -webkit-box-shadow: 0 0 30px rgba(0,0,0,0.3);
    -moz-box-shadow: 0 0 30px rgba(0,0,0,0.3);
    box-shadow: 0 0 30px rgba(0,0,0,0.3)
}

.modal-header .close {
    margin-top: -7px;
    margin-right: -15px
}

.modal-lg .modal-header .close {
    margin-top: -7px;
    margin-right: -17px
}

.modal-lg.bell-modal-lg .modal-header .close {
    padding: 15px;
    margin: -5px -15px -15px -18px
}

.modal .modal-close_cros {
    border: 0;
    background-color: transparent;
    width: 40px;
    height: 40px
}

.modal .modal-md .close {
    padding: 15px;
    margin: -7px -15px -15px -18px
}

.modal.modal-tooltip .modal-body, .modal-header, .modal-footer {
    padding: 15px 30px
}

.modal.modal-tooltip {
    z-index: 99999
}

    .modal.modal-tooltip .modal-body {
        padding: 0 40px 40px
    }
/* Modal changes from shop */
.modal-body {
    margin-bottom: 30px;
    margin-top: 30px;
    padding: 0 15px
}

.modalSelectNav {
    width: 90%;
    margin: auto
}

.close {
    opacity: 1
}

    .close:hover, .close:focus {
        opacity: 1
    }

.modal-footer {
    text-align: left;
    border-top: none
}

    .modal-footer .btn + .btn {
        margin-bottom: 0;
        margin-left: 0
    }

button.close:focus {
    border: 1px dotted
}

.modal-content {
    border: 0
}

.modal-title.line-height-20 {
    line-height: 23px;
    margin-top: 0px
}

.modal-title {
    line-height: 26px;
}

.unfocus, .unfocus:focus {
    border: 0;
    outline: 0
}

.modal .modal-md .close {
    margin-top: -20px
}

.modal-header-gray {
    height: 74px;
    background-color: #e1e1e1
}

.modal-header-blue {
    height: 74px;
    background-color: #00549a
}

.modal-dialog {
    width: 645px;
    max-width: 100%
}

.modal.modal-tooltip .modal-content {
    border: 0
}

.modal-open .modal {
    overflow: hidden
}
/*fix for view portview width issue on mobile*/
.modal {
    width: calc(var(--vw, 1vw) * 100)
}

/* Modal changes from shop */
.modal-dialog {
    border-radius: 10px 10px 0 0;
    overflow: hidden;
}

.modal-header {
    align-items: center;
    background-color: #f0f0f0;
    padding: 15px 20px 15px 15px;
}

.modal-dialog.modal-dialog-fullscreen {
    max-width: 1200px;
    width: 100%;
}

.modal-dialog .modal-content .modal-header .close {
    margin: 0 -15px 0 15px;
    padding: 15px;
    border: 0;
}

/* some browsers such as IE and FF ignore padding-bottom when content overflows so we'll use :after pseudo-element instead */
.modal-open .modal-body.scrollAdjust {
    padding-bottom: 0;
}

.modal.modal-status .modal-dialog {
    border-radius: 10px;
    box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
    max-width: calc(100% - 30px);
    position: relative;
}

.modal.modal-status .modal-content {
    border-radius: 10px;
}

.modal.modal-status .modal-body {
    padding: 0 30px;
}

/*MODAL WINDOW VERTICAL CENTERING*/
.modal {
    text-align: center;
    padding: 0;
    z-index: 99999
}

    .modal:before {
        content: '';
        display: inline-block;
        height: 100%;
        vertical-align: middle;
        margin-right: 0
    }

.modal-dialog {
    display: inline-block;
    text-align: left;
    vertical-align: middle;
    margin: auto;
}
/*MODAL WINDOW VERTICAL CENTERING*/
.modal.modal-tooltip .modal-content {
    border: 0
}
/*END MODALS*/

.modal.modal-tooltip .modal-header {
    border-bottom: 0
}

@media screen and (max-width:991.98px) {
    .modal:before, .modal-dialog {
        vertical-align: middle
    }

    .modal.modal-tooltip {
        padding-top: 0px;
    }
}

@media screen and (max-width:767.98px) {
    .modal.modal-tooltip {
        bottom: unset
    }

        .modal.modal-tooltip .tooltip-dialog {
            margin: auto 15px;
            -webkit-transform: translate(0%, -50%);
            transform: translate(0%, -50%);
            position: relative;
            bottom: 50%;
        }

            .modal.modal-tooltip .tooltip-dialog .modal-header {
                padding-bottom: 10px
            }

            .modal.modal-tooltip .tooltip-dialog button.close:focus {
                outline: 1px;
                outline-color: #00549a;
            }

            .modal.modal-tooltip .tooltip-dialog .close {
                padding: 15px;
                font-size: 12px
            }

    .modal-header-blue {
        height: 60px;
        background-color: #00549a;
        padding-top: 22px
    }

    .modal-header-gray {
        height: 60px;
        background-color: #e1e1e1;
        padding-top: 22px
    }

    .modal:before, .modal-dialog {
        vertical-align: middle
    }

    .modal.modal-tooltip {
        padding-top: 0px;
    }

    /*Modal dialog*/
    .modal .modal-dialog.modal-lg.bell-modal-lg {
        margin: 0px;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100%;
        background-color: #f0f0f0;
        width: 100%
    }

    .modal .modal-dialog.modal-lg-2.bell-modal-lg {
        background-color: #fff
    }

    .modal .modal-dialog.center-screen.modal-md.bell-modal-md {
        margin: 0 auto;
        position: relative;
        width: 92%;
    }

    .modal .modal-dialog.modal-md.bell-modal-md {
        margin: 0;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        top: auto;
        height: auto;
        background-color: transparent;
    }

    .modal-dialog.modal-md.bell-modal-md.bgGray19 {
        background-color: #f4f4f4
    }

    /*Modal changes from SHOP*/
    .modal .modal-dialog {
        height: auto;
        max-height: calc(100% - 45px);
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
    }
}

@media screen and (max-width:520.98px) {
    .modal.modal-tooltip {
        position: fixed;
        padding-top: 0px;
        width: 100%;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%)
    }

    .modal-scroll-area {
        padding: 0
    }
}

@media (min-width:992px) {
    .modal-scroll-area {
        padding: 30px 40px 0 30px
    }
}

@media (min-width:1200px) {
    .modal .modal-lg.bell-modal-lg {
        width: 1190px;
        margin-left: -9px
    }

    .modal-scroll-area {
        padding: 30px 40px 0 30px
    }
}

@media (max-width:1199.98px) {
    .modal.modal-tooltip {
        position: fixed;
        width: 100%;
        height: 100%;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        overflow: hidden;
    }
}

@media (min-width:768px) {
    /*Modal changes from shop*/
    .modal-dialog {
        border-radius: 10px;
        max-height: calc(100% - 120px);
    }

    .modal-header {
        height: 70px;
        padding: 0 30px;
    }

    .modal-body {
        padding: 0 30px;
    }

    .modal.modal-status .modal-body {
        padding-top: 10px;
        padding-bottom: 15px;
    }
}

/* custom made for fullscreen modal (tablet to medium desktop sizes) only*/
@media (min-width: 768px) and (max-width: 1240px) {
    .modal.show .modal-dialog.modal-dialog-fullscreen {
        height: auto;
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        transform: translateY(-50%);
    }
}

/* webkit only hack: custom scroll (.scrollAdjust) only works on webkit browsers so only adjust the margin and padding for those browsers */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .modal-open .modal-body.scrollAdjust:not(*:root) {
        margin-right: 10px;
        padding-right: 20px;
    }
}
/*END MODALS*/

/*START TOOLTIP*/
.tooltip {
    width: 315px;
    position: absolute !important
}

.tooltip-inner {
    max-width: 290px;
    padding: 30px 25px 30px 25px;
    color: #555;
    font-size: 14px;
    text-align: left;
    background-color: #FFF;
    border-radius: 0;
    box-shadow: 0 0 11px 0 rgba(0,0,0,0.27);
    -webkit-box-shadow: 0 0 11px 0 rgba(0,0,0,0.27);
    -moz-box-shadow: 0 0 11px 0 rgba(0,0,0,0.27);
}

.tooltip-lg .tooltip {
    width: 700px
}

    .tooltip-lg .tooltip .tooltip-inner {
        max-width: 700px;
        padding: 30px
    }

.tooltip.show {
    opacity: 1
}

.tooltip .arrow, .tooltip.bs-tooltip-auto .arrow {
    height: 30px
}

.tooltip.bs-tooltip-right, .tooltip.bs-tooltip-auto[x-placement^=right] {
    padding: 0 5px;
    margin-left: 20px
}

    .tooltip.bs-tooltip-right .arrow, .tooltip.bs-tooltip-auto[x-placement^=right] .arrow {
        margin-top: -25px
    }

    .tooltip.bs-tooltip-right .arrow, .tooltip.bs-tooltip-auto[x-placement^=right] .arrow {
        -webkit-filter: drop-shadow(-6px 0px 3px rgba(0,0,0,0.12));
        -moz-filter: drop-shadow(-6px 0px 3px rgba(0,0,0,0.12));
        filter: drop-shadow(-6px 0px 3px rgba(0,0,0,0.12));
    }

        .tooltip.bs-tooltip-right .arrow::before, .tooltip.bs-tooltip-auto[x-placement^=right] .arrow::before {
            border-width: 25px 25px 25px 0;
            border-right-color: #fff
        }

.tooltip.bs-tooltip-left, .tooltip.bs-tooltip-auto[x-placement^=left] {
    padding: 0 5px;
    margin-right: 20px
}

    .tooltip.bs-tooltip-left .arrow, .tooltip.bs-tooltip-auto[x-placement^=left] .arrow {
        margin-top: -25px
    }

        .tooltip.bs-tooltip-left .arrow::before, .tooltip.bs-tooltip-auto[x-placement^=left] .arrow::before {
            border-width: 25px 0 25px 25px;
            border-left-color: #fff
        }

    .tooltip.bs-tooltip-left .arrow, .tooltip.bs-tooltip-auto[x-placement^=left] .arrow {
        -webkit-filter: drop-shadow(4px -1px 2px rgba(0,0,0,0.17));
        -moz-filter: drop-shadow(4px -1px 2px rgba(0,0,0,0.17));
        filter: drop-shadow(4px -1px 2px rgba(0,0,0,0.17));
    }

.tooltip.bs-tooltip-top, .tooltip.bs-tooltip-auto[x-placement^=top] {
    padding: 0 5px;
    margin-bottom: 20px
}

    .tooltip.bs-tooltip-top .arrow, .tooltip.bs-tooltip-auto[x-placement^=top] .arrow {
        margin-left: -35px;
        margin-bottom: -25px
    }

        .tooltip.bs-tooltip-top .arrow::before, .tooltip.bs-tooltip-auto[x-placement^=top] .arrow::before {
            border-width: 25px 25px 0;
            border-top-color: #fff
        }

    .tooltip.bs-tooltip-top .arrow, .tooltip.bs-tooltip-auto[x-placement^=top] .arrow {
        -webkit-filter: drop-shadow(0px 9px 5px rgba(0,0,0,0.12));
        -moz-filter: drop-shadow(0px 9px 5px rgba(0,0,0,0.12));
        filter: drop-shadow(0px 9px 5px rgba(0,0,0,0.12));
    }

.tooltip.bs-tooltip-bottom, .tooltip.bs-tooltip-auto[x-placement^=bottom] {
    padding: 0 5px;
    margin-top: 25px
}

    .tooltip.bs-tooltip-bottom .arrow, .tooltip.bs-tooltip-auto[x-placement^=bottom] .arrow {
        margin-left: -35px;
        margin-top: -25px
    }

        .tooltip.bs-tooltip-bottom .arrow::before, .tooltip.bs-tooltip-auto[x-placement^=bottom] .arrow::before {
            border-width: 0 25px 25px;
            border-bottom-color: #fff
        }

.tooltip.in {
    filter: alpha(opacity=100);
    opacity: 1
}

.tooltip.bs-tooltip-bottom .arrow, .tooltip.bs-tooltip-auto[x-placement^=bottom] .arrow {
    -webkit-filter: drop-shadow(0px -8px 5px rgba(0,0,0,0.12));
    -moz-filter: drop-shadow(0px -8px 5px rgba(0,0,0,0.12));
    filter: drop-shadow(0px -8px 5px rgba(0,0,0,0.12));
}
/*END TOOLTIP*/


/*BRF3 New Styleguide container and grid*/
.col-lg-1,.col-lg-10,.col-lg-11,.col-lg-12,.col-lg-2,.col-lg-3,.col-lg-4,.col-lg-5,.col-lg-6,.col-lg-7,.col-lg-8,.col-lg-9,.col-md-1,.col-md-10,.col-md-11,.col-md-12,.col-md-2,.col-md-3,.col-md-4,.col-md-5,.col-md-6,.col-md-7,.col-md-8,.col-md-9,.col-sm-1,.col-sm-10,.col-sm-11,.col-sm-12,.col-sm-2,.col-sm-3,.col-sm-4,.col-sm-5,.col-sm-6,.col-sm-7,.col-sm-8,.col-sm-9,.col-xs-1,.col-xs-10,.col-xs-11,.col-xs-12,.col-xs-2,.col-xs-3,.col-xs-4,.col-xs-5,.col-xs-6,.col-xs-7,.col-xs-8,.col-xs-9,.col-1,.col-2,.col-3,.col-4,.col-5,.col-6,.col-7,.col-8,.col-9,.col-10,.col-11,.col-12,.col-auto,.col-sm-auto,.col-md-auto,.col-lg-auto{padding-right: 0;padding-left: 0}

@media (min-width: 320px) and (max-width: 767.98px) {
    .container, .container.liquid-container {
        padding-left: 15px;
        padding-right: 15px
    }
}

@media (min-width: 320px) and (max-width: 991.98px) {
    .container, .container.liquid-container {
        width: 100%;
        max-width: 100%
    }

    /*.simplified-header {
        height: 55px;
    }*/
}

@media (min-width: 768px) and (max-width: 991.98px) {
    .container, .container.liquid-container {
        padding-left: 30px;
        padding-right: 30px
    }
}


@media (min-width:992px) {
    .container, .container.liquid-container {
        width: 100%;
        max-width: 100%
    }
}

@media (min-width: 992px) and (max-width: 1239.98px) {
    .container, .container.liquid-container {
        padding-left: 16px;
        padding-right: 16px
    }
}

@media (min-width:1240px) {
    .container, .container.liquid-container {
        width: 1200px
    }

    .container {
        padding-right: 0px;
        padding-left: 0px;
    }
}



