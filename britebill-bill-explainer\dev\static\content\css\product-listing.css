.prod-carousel {
    position: relative;
}

    .prod-carousel > .container {
        padding-left: 0;
        padding-right: 0;
    }

.prod-list .prod-tile {
    margin-bottom: 30px;
}

.theme-virgin.prod-list .prod-tile {
    margin-bottom: 15px;
}

.prod-tile-flag {
    padding: 3px 7px 2px;
    background-color: #00549a;
    font-size: 10px;
    letter-spacing: 0.2px;
    font-weight: bold;
    line-height: 14px;
    color: #fff;
    text-transform: uppercase;
    text-align: center;
    display: inline-block;
    margin-top: 10px;
    align-self: baseline;
    border-radius: 2px;
}

.theme-virgin .prod-tile-flag {
    margin-top: 15px;
    background-color: #CC0000;
}

.prod-carousel-content {
    position: relative;
    display: none;
}

    .prod-carousel-content.slick-initialized {
        display: block;
    }

    .prod-carousel-content .slick-list {
        overflow: visible !important;
        width: calc(100% - 45px);
    }

    .prod-carousel-content .prod-tile {
        padding-top: 30px;
        padding-bottom: 30px;
        padding-left: 7px;
        padding-right: 7px;
        display: flex;
        flex-direction: column;
    }

.prod-tile-content {
    border-radius: 10px;
    padding: 10px 15px 30px;
    position: relative;
    z-index: 10;
}

.prod-tile-link {
    position: absolute;
    background-color: transparent;
    z-index: 10;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    border-radius: 10px;
    border: 1px solid #e1e1e1;
    background-color: transparent;
    box-shadow: 0 6px 25px 0 rgba(0,0,0,0.12);
    transition: box-shadow .2s;
}

.theme-virgin .prod-tile-link {
    border-radius: 4px;
}

.prod-list .prod-tile-link:hover,
.prod-carousel .prod-tile-link:hover,
.prod-list .prod-tile-link:focus,
.prod-carousel .prod-tile-link:focus {
    box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
}

.prod-carousel-content .slick-track {
    display: flex;
}

.prod-carousel-content .slick-slide {
    height: inherit;
}

    .prod-carousel-content .slick-slide > div,
    .prod-carousel-content .prod-tile,
    .prod-carousel-content .prod-tile-content {
        height: 100%;
    }

    .prod-carousel-content .slick-slide > div {
        display: flex;
    }

.prod-tile-img {
    display: block;
    max-width: 100%;
    align-self: center;
    height: auto;
    margin: 0 auto;
    margin-bottom: 13px;
    height: 155px;
    position: relative;
}

.prod-tile-name {
    color: #111111;
    font-family: "bellslim_font_black";
    font-size: 22px;
    letter-spacing: -0.4px;
    line-height: 24px;
    position: relative;
}

.theme-virgin .prod-tile-name {
    color: #333333;
    font-family: "VMUltramagneticNormalRegular";
    font-size: 24px;
    letter-spacing: 0;
    line-height: 30px;
    font-weight: normal;
    text-transform: uppercase;
}

    .theme-virgin .prod-tile-name sup {
        text-transform: none;
    }

.prod-tile-price-wrap {
    display: flex;
    flex-direction: row;
    margin-top: 15px;
    position: relative;
}

.prod-tile-price-down {
    width: 25%;
    padding-right: 5px;
}

.prod-tile-price-month {
    width: calc(55% - 15px);
    padding-right: 5px;
}

.prod-tile-price-apr {
    width: 20%;
}

.prod-tile-price-note-top {
    height: 25px;
    font-size: 12px;
}

.prod-tile-price {
    color: #00549A;
    font-weight: bold;
}

.theme-virgin .prod-tile-price {
    color: #111;
}

.prod-tile-price-note-bottom {
    margin-top: 3px;
}

.prod-tile-note {
    margin-top: 15px;
    font-size: 12px;
    position: relative;
    z-index: 15;
}

.prod-tile-full-price {
    margin-top: 15px;
    position: relative;
}

    .prod-tile-full-price > span {
        font-weight: bold;
    }

.prod-tile-promo {
    margin-top: 15px;
    border: 1px solid #00549A;
    border-radius: 10px;
    padding: 10px;
    position: relative;
}

.theme-virgin .prod-tile-promo {
    border: 1px solid #CC0000;
}

.theme-virgin .prod-tile-promo-title {
    color: #00549A;
    font-size: 14px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 16px;
    margin-bottom: 3px;
}

.theme-virgin .prod-tile-promo-title {
    color: #CC0000;
}

.prod-tile-promo-body {
    font-size: 12px;
}

.prod-tile-colors {
    position: absolute;
    top: 20px;
    right: 15px;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    align-content: flex-end;
    /* z-index: 15; */
}

    .prod-tile-colors > div,
    .product-color-option .ctrl_element {
        width: 14px;
        height: 14px;
        position: relative;
    }

    .prod-tile-colors > div {
        margin: 0 0 7px 5px;
    }

.theme-virgin .prod-tile-colors > div {
    margin: 0 0 10px 5px;
}

.prod-tile-colors input {
    display: block;
    width: 5px;
    height: 5px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.product-color-option input:focus {
    outline: none !important;
}

.product-color-option .ctrl_element {
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    cursor: pointer;
}

.theme-virgin .product-color-option .ctrl_element {
    width: 16px;
    height: 16px;
    border-radius: 1px;
    border: 1px solid #2390B9;
}

.product-color-option input:focus + .ctrl_element {
    box-shadow: 0px 0px 0px 3px rgba(77, 144, 254,.75);
}

.product-color-option input + .ctrl_element:after {
    content: "";
    border: 2px solid #00549a;
    border-radius: 50%;
    display: none;
    position: absolute;
    width: 22px;
    height: 22px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.theme-virgin .product-color-option input + .ctrl_element:after {
    border-radius: 1px;
}

.product-color-option input:checked + .ctrl_element:after {
    display: block;
}

.prod-carousel-content ul.slick-dots {
    list-style-type: none;
    padding: 0;
    margin: 0 auto;
    text-align: center;
    line-height: 0;
}

    .prod-carousel-content ul.slick-dots > li {
        display: inline-block;
    }

.prod-carousel-content .slick-dots button {
    display: inline-block;
    width: 10px;
    height: 10px;
    border: 1px solid #555;
    background-color: transparent;
    border-radius: 50%;
    font-size: 0;
    padding: 0;
    margin: 0 4px;
    cursor: pointer;
}

.prod-carousel-content .slick-dots .slick-active > button {
    background-color: #555;
}

.prod-carousel-content .slick-prev,
.prod-carousel-content .slick-next {
    position: absolute;
    z-index: 100;
    top: 50%;
    transform: translateY(-50%);
    left: -5px;
    width: 50px;
    height: 50px;
    border: 0;
    border-radius: 50%;
    background-color: #fff;
    cursor: pointer;
    transition: all .25s cubic-bezier(.55,0,.1,1);
    font-size: 0;
    box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
    border: 1px solid #e1e1e1;
    color: #00549A;
    display: none !important;
    cursor: pointer;
}

.theme-virgin .prod-carousel-content .slick-prev,
.theme-virgin .prod-carousel-content .slick-next {
    color: #CC0000;
}


.prod-carousel-content .slick-next {
    right: -5px;
    left: auto;
}

    .prod-carousel-content .slick-prev:before,
    .prod-carousel-content .slick-next:before {
        font-family: 'bell-icon';
        content: "\eaa4";
        top: 8px;
        right: 18px;
        position: absolute;
        font-size: 22px;
    }

    .prod-carousel-content .slick-prev.slick-disabled,
    .prod-carousel-content .slick-next.slick-disabled {
        display: none !important;
    }

.prod-carousel-content .slick-prev:before {
    transform: scaleX(-1);
    right: 20px;
}

.prod-carousel-content .slick-prev:hover,
.prod-carousel-content .slick-next:hover,
.prod-carousel-content .slick-prev:focus,
.prod-carousel-content .slick-next:focus {
    outline: none;
    box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3), inset 0px 0px 0px 2px #2672cb;
}

.prod-carousel-content .tooltip-interactive {
    display: inline-block;
}

@media (max-width: 767px) {
    .prod-carousel-content .slick-prev,
    .prod-carousel-content .slick-next {
        display: none !important;
    }

    .prod-carousel-slide img {
        height: 150px;
    }

    .prod-carousel-content .slick-list {
        margin: 0 auto 0 7px;
    }
}

@media (min-width: 768px) {

    .prod-list-content {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    .prod-list .prod-tile {
        display: flex;
        flex-direction: column;
        position: relative;
    }

    .prod-list .prod-tile-content {
        height: auto;
        display: flex;
        flex: 1 0 auto;
        flex-direction: column;
    }

    .prod-list-content .prod-tile {
        width: calc(50% - 15px);
    }

    .theme-virgin .prod-list-content .prod-tile {
        width: calc(50% - 7px);
    }

    .prod-carousel-content .slick-list {
        width: calc(100% - 46px);
        margin: 0 auto;
    }

    .prod-tile-content {
        padding: 10px 30px 30px;
    }

    .prod-tile-img {
        height: 210px;
        margin-bottom: 13px;
    }

    .prod-list .prod-tile-img {
        margin-bottom: 13px;
    }

    .prod-tile-colors {
        position: absolute;
        top: 35px;
        right: 30px;
    }

    .prod-tile-name {
        font-size: 24px;
        line-height: 26px;
    }

    .prod-tile-price-wrap {
        margin-top: 15px;
    }

    .prod-tile-price-note-top {
        height: 20px;
    }
}

@media (min-width: 992px) {
    .prod-carousel-content .slick-prev,
    .prod-carousel-content .slick-next {
        display: block !important;
    }

    .prod-carousel-content .slick-list {
        width: 100%;
    }

    .prod-carousel-content .slick-list {
        padding-left: 7px;
        padding-right: 7px;
    }

    .prod-list-content .prod-tile,
    .prod-list-content:after {
        width: calc(33.3333% - 20px);
    }

    .theme-virgin .prod-list-content .prod-tile,
    .theme-virgin .prod-list-content:after {
        width: calc(33.3333% - 10px);
    }

    .prod-list-content:after {
        content: "";
        display: block;
    }
}

@media (min-width: 1240px) {

    .prod-carousel > .container {
        width: 1244px;
    }

    .prod-carousel-content .slick-list {
        padding-left: 15px;
        padding-right: 15px;
        position: relative;
        z-index: 10;
    }

    .prod-carousel-content:before,
    .prod-carousel-content:after {
        content: "";
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 20px;
        background: #000;
        background: linear-gradient(90deg, rgba(255,255,255,1) 50%, rgba(255,255,255,0) 100%);
        z-index: 80;
    }

    .prod-carousel-content:after {
        left: auto;
        right: 0;
        background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 50%);
    }

    .prod-carousel > .container:before,
    .prod-carousel > .container:after {
        content: "";
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 20px;
        width: calc(50% - 620px);
        background: #fff;
        z-index: 80;
    }

    .prod-carousel > .container:after {
        left: auto;
        right: 0;
    }
}


/*FILTERS*/

.prod-filter {
    display: flex;
    flex-direction: column;
    justify-content: center;
    background-color: #fff;
    height: 90px;
    overflow-x: auto;
    flex-shrink: 0;
}

    .prod-filter.theme-virgin {
        height: auto;
        border-bottom: 1px solid #D8D8D8;
    }

        .prod-filter.theme-virgin > .container {
            padding-left: 0;
            padding-right: 0;
        }

.prod-filter-content {
    display: flex;
    flex-wrap: nowrap;
    white-space: nowrap;
    height: 30px;
    width: 100%;
}

.theme-virgin .prod-filter-content {
    height: auto;
}

.prod-filter-brands {
    display: flex;
    flex-shrink: 0;
}

    .prod-filter-brands > button {
        display: block;
        color: #111111;
        font-size: 14px;
        letter-spacing: 0;
        line-height: 16px;
        border: 1px solid #D4D4D4;
        border-radius: 15px;
        background-color: #FFFFFF;
        padding: 6px 14px;
        margin: 0 10px 0 0;
        cursor: pointer;
    }

.theme-virgin .prod-filter-brands > button,
.theme-virgin .prod-filter-more-btn {
    font-weight: normal;
    border: none;
    padding: 15px 0 16px;
    position: relative;
    text-decoration: none;
    margin: 0 10px;
    color: #111111;
    letter-spacing: 0.35px;
}

    .theme-virgin .prod-filter-brands > button:first-child {
        margin-left: 15px;
    }

    .theme-virgin .prod-filter-brands > button[aria-pressed="true"]:before {
        content: "";
        display: block;
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        width: 100%;
        background-color: #CC0000;
    }

    .theme-virgin .prod-filter-brands > button[aria-pressed="true"] {
        background-color: transparent;
        color: #CC0000;
    }

.prod-filter-brands > button[aria-pressed="true"] {
    background-color: #003778;
    color: #fff;
}

.prod-filter-brands > button:hover,
.prod-filter-brands > button:focus {
    border-color: #003778;
}

.prod-filter-more-btn {
    border: none;
    background-color: transparent;
    text-decoration: underline;
    color: #0056b3;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 16px;
    padding: 6px 13px;
    cursor: pointer;
}

    .prod-filter-more-btn:hover {
        text-decoration: none;
    }

.prod-filter-more-btn-wrap {
    display: flex;
}

    .prod-filter-more-btn-wrap:before {
        content: "";
        display: block;
        height: 30px;
        width: 1px;
        background-color: #D4D4D4;
        margin-left: 10px;
        margin-right: 3px;
        ;
    }

.theme-virgin .prod-filter-more-btn-wrap:before {
    display: none;
}


@media (min-width: 768px) {

    .theme-virgin .prod-filter-brands > button,
    .theme-virgin .prod-filter-more-btn {
        font-family: "VMUltramagneticNormalRegular";
        font-size: 16px;
        letter-spacing: 0.4px;
        line-height: 17px;
        color: #333;
        border: none;
        text-transform: uppercase;
        padding: 30px 15px 18px 15px;
        text-decoration: none;
        margin: 0;
    }
}

@media (min-width: 1240px) {

    .theme-virgin .prod-filter-brands > button:first-child {
        margin-left: 0;
    }

    .prod-filter.theme-virgin > .container {
        width: 1230px;
    }
}
