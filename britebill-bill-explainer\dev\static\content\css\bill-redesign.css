﻿/*Start Header and Footer Override*/
@media (min-width: 320px) and (max-width: 1229.98px) {
    .bill-redesign .container,
    .gh-myb .container,
    .gf-complete .container,
    .standard-step-flow-header .container {
        padding-left: 15px;
        padding-right: 15px;
        margin: 0;
        max-width: none;
        width: 100%;
    }
}

@media (min-width: 1230px) {
    .bill-redesign .container,
    .gh-myb .container,
    .gf-complete .container,
    .standard-step-flow-header .container {
        padding-left: 0;
        padding-right: 0;
        margin: 0 auto;
        max-width: 1200px;
        width: 100%;
    }
}

/*from searchui.css - for dropdown chevron icons*/
.gh-myb .gn-mybell a.trigger-dropdown .icon-global-nav.icon-chevron.chevron-down {
    top: 0px !important;
}
/*from searchui.css - for dropdown chevron icons*/
.bill-redesign-accss .search-bar-footer [type="search"] {
    border: 2px solid #707070;
}

.gh-myb .global-navigation.gn-mybell #connector-search #voice_search {
    right: 40px;
}
/*End Header and Footer Override*/
/* START - standard-step-flow-header */
.standard-step-flow-header .btn.btn-primary-white {
    padding: 7px 28px;
}

/*START - override html and body*/
html, body {
    overflow-x: hidden;
}

    html.connector-expanded {
        overflow: hidden;
    }
/*END - override html and body*/

/*START - override main bell.css*/
main.bill-redesign {
    overflow-x: visible;
    overflow-y: visible;
}
/*END - override main bell.css*/
/*New Footer*/
.footer-with-accordion .footer-outline-icon {
    padding: 2.5px 3.9px;
    border: solid 2px #00549a;
    border-radius: 50%;
    display: inline-block;
}

.footer-with-accordion .accordion-button {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    width: 100%;
    text-align: left;
}

.footer-with-accordion .footer-center-icon {
    position: relative;
    top: 2px;
}

.footer-with-accordion .h-divider {
    display: inline-block;
    color: #d4d4d4;
    border-right: solid 1px #d4d4d4;
    height: 25px;
}

.footer-with-accordion .accordion-arrow.icon {
    right: 7px;
    top: 5px;
}

.gf-complete div[role="listitem"]:not(.h-divider) {
    height: 26px;
}

ul.resources-column {
    columns: 2;
    -webkit-columns: 2;
    -moz-columns: 2;
}

/*New Footer*/
/*Helper class - Start*/
body.bill-redesign-accss.is_tabbing *:focus {
    outline: 0 !important;
    box-shadow: none !important;
}

.bill-redesign-accss .offer-tiles-modal .accordion-body.hide {
    display: none;
}

.bill-redesign-accss .offer-tiles-modal .accordion-body:not(.hide) {
    display: block;
}

.bill-redesign .big-price,
.bill-redesign-accss .offer-tiles-modal .big-price {
    font-size: 40px;
    line-height: 46px;
    color: #00549A;
}

    .bill-redesign .big-price span,
    .bill-redesign-accss .offer-tiles-modal .big-price span {
        font-size: 18px;
        letter-spacing: -0.45px;
        line-height: 22px;
        position: relative;
        vertical-align: top;
        top: 7px;
    }

        .bill-redesign .big-price span.big-price-dollar:last-of-type,
        .bill-redesign-accss .offer-tiles-modal .big-price span.big-price-dollar:last-of-type {
            margin-left: 4px;
        }

        .bill-redesign .big-price span.big-price-dollar:first-of-type,
        .bill-redesign-accss .offer-tiles-modal .big-price span.big-price-dollar:first-of-type {
            margin-right: 2px;
            margin-left: 0px;
        }

        .bill-redesign .big-price span:not(.big-price-dollar):first-of-type,
        .bill-redesign-accss .offer-tiles-modal .big-price span:not(.big-price-dollar):first-of-type {
            margin-right: 0;
            margin-left: 4px;
        }

        .bill-redesign .big-price span:not(.big-price-dollar):last-of-type,
        .bill-redesign-accss .offer-tiles-modal .big-price span:not(.big-price-dollar):last-of-type {
            margin-left: 4px;
        }

.bill-redesign .big-price-header,
.bill-redesign-accss .offer-tiles-modal .big-price-header {
    font-size: 40px;
    line-height: 46px;
    color: #00549A;
}

    .bill-redesign .big-price-header span,
    .bill-redesign-accss .offer-tiles-modal .big-price-header span {
        font-size: 18px;
        letter-spacing: -0.45px;
        line-height: 22px;
        position: relative;
        vertical-align: top;
        top: 7px;
    }

        .bill-redesign .big-price-header span.big-price-header-dollar:last-of-type,
        .bill-redesign-accss .offer-tiles-modal .big-price-header span.big-price-header-dollar:last-of-type {
            margin-left: 4px;
        }

        .bill-redesign .big-price-header span.big-price-header-dollar:first-of-type,
        .bill-redesign-accss .offer-tiles-modal .big-price-header span.big-price-header-dollar:first-of-type {
            margin-right: 2px;
            margin-left: 0px;
        }

        .bill-redesign .big-price-header span:not(.big-price-header-dollar):first-of-type,
        .bill-redesign-accss .offer-tiles-modal .big-price-header span:not(.big-price-header-dollar):first-of-type {
            margin-right: 0;
            margin-left: 4px;
        }

        .bill-redesign .big-price-header span:not(.big-price-header-dollar):last-of-type,
        .bill-redesign-accss .offer-tiles-modal .big-price-header span:not(.big-price-header-dollar):last-of-type {
            margin-left: 4px;
        }

.bill-redesign .active_tabs::after {
    display: none;
}

.bgVeryLightBlue {
    background: #EDF3F8;
}

.txtCoolBlue {
    color: #C2CEDF;
}

.txtLightGrayMobile {
    color: #BABEC2;
}

.txtYellow-large-icon {
    color: #E2A724;
}

.dimension-12 {
    width: 12px;
    height: 12px;
}

.dimension-16 {
    width: 16px;
    height: 16px;
}

.dimension-20 {
    width: 20px;
    height: 20px;
}

.dimension-23 {
    width: 23px;
    height: 23px;
}

.dimension-27 {
    min-width: 27px;
    width: 27px;
    height: 27px;
}

.dimension-35 {
    width: 35px;
    height: 35px;
}

.dimension-60 {
    width: 60px;
    height: 60px;
}

.bill-redesign .title {
    font-size: 26px;
    letter-spacing: -.4px;
    line-height: 28px;
}

.bill-redesign .subtitle-2-reg {
    font-size: 18px;
    line-height: 22px;
}

.txtSize7 {
    font-size: 7px;
}

.txtSize13 {
    font-size: 13px;
}

.txtSize17 {
    font-size: 17px;
}

.txtSize23 {
    font-size: 23px;
}

.txtSize25 {
    font-size: 25px;
}

.txtSize27 {
    font-size: 27px;
}

.txtSize33 {
    font-size: 33px;
}

.txtSize36 {
    font-size: 36px;
}

.txtSize38 {
    font-size: 38px;
}

.txtSize45 {
    font-size: 45px;
}

.txtSize46 {
    font-size: 46px;
}

.txtSize55 {
    font-size: 55px;
}

.txtSize90 {
    font-size: 90px;
}

.txtBlack3 {
    color: #0E0E0E;
}

.line-height-14 {
    line-height: 14px;
}

.line-height-18 {
    line-height: 18px;
}

.bill-redesign-accss .line-height-18-force {
    line-height: 18px !important;
}

.line-height-20 {
    line-height: 20px;
}

.line-height-21 {
    line-height: 21px;
}

.line-height-22 {
    line-height: 22px;
}

.letter-spacing-neg-023 {
    letter-spacing: -0.23px;
}

.width-15percent {
    width: 15%;
}

.width-37percent {
    width: 37%;
}

.width-40percent {
    width: 40%;
}

.width-45 {
    width: 45px;
}

.width-50percent {
    width: 50%;
}

.width-53percent {
    width: 53%;
}

.width-55percent {
    width: 55%;
}

.width-60, .width-60px {
    width: 60px;
}

.width-67 {
    width: 67px;
}

.width-70 {
    width: 70px;
}

.width-75 {
    width: 75px;
}

.width-80px {
    width: 80px;
}

.width-86 {
    width: 86px;
}

.width-90 {
    width: 90px;
}

.width-95percent {
    width: 95%;
}

.width-93 {
    width: 93px;
}

.width-113 {
    width: 113px;
}

.width-150 {
    width: 150px;
}

.width-190 {
    width: 190px;
}

.width-195 {
    width: 195px;
}

.width-320 {
    width: 320px;
}

.width-850 {
    width: 850px;
}

.max-width-320 {
    max-width: 320px;
}

.max-width-735 {
    max-width: 735px;
}

.max-width-893 {
    max-width: 893px;
    width: 100%;
}

.height-31 {
    height: 31px;
}

.height-50 {
    height: 50px;
}

.height-68 {
    height: 68px;
}

.height-175 {
    height: 175px;
}

.height-100-percent {
    height: 100%;
}

.left-0 {
    left: 0;
}

.right-0 {
    right: 0;
}

.pad-h-0-forced {
    padding-left: 0px !important;
    padding-right: 0px !important;
}

.pad-h-6 {
    padding-left: 6px;
    padding-right: 6px;
}

.pad-h-12 {
    padding-left: 12px;
    padding-right: 12px;
}

.pad-v-2p5 {
    padding-top: 2.5px;
    padding-bottom: 2.5px;
}

.pad-v-7p5 {
    padding-top: 7.5px;
    padding-bottom: 7.5px;
}

.pad-v-8 {
    padding-top: 8px;
    padding-bottom: 8px;
}

.pad-v-12p5 {
    padding-top: 12.5px;
    padding-bottom: 12.5px;
}

.pad-l-0 {
    padding-left: 0px !important;
}

.pad-t-1 {
    padding-top: 1px;
}

.pad-t-3 {
    padding-top: 3px;
}

.pad-t-4 {
    padding-top: 4px;
}

.pad-b-1 {
    padding-bottom: 1px;
}

.pad-b-2 {
    padding-bottom: 2px;
}

.pad-b-3 {
    padding-bottom: 3px;
}

.pad-b-7 {
    padding-bottom: 7px;
}

.pad-b-12 {
    padding-bottom: 12px;
}

.pad-l-12 {
    padding-left: 12px;
}

.pad-l-13 {
    padding-left: 13px;
}

.pad-l-75 {
    padding-left: 75px;
}

.pad-r-11 {
    padding-right: 11px;
}

.pad-r-13 {
    padding-right: 13px;
}

.pad-r-18 {
    padding-right: 18px;
}

.pad-r-33 {
    padding-right: 33px;
}

.pad-r-77 {
    padding-right: 77px;
}

.pad-0-force {
    padding: 0 !important;
}

.margin-0-force {
    margin: 0 !important;
}

.margin-h-0-force {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

.margin-right-neg-4 {
    margin-right: -4px;
}

.margin-top-neg-5 {
    margin-top: -5px;
}

.margin-left-neg-5 {
    margin-left: -5px;
}

.margin-right-neg-5 {
    margin-right: -5px;
}

.margin-right-neg-12p55 {
    margin-right: -12.55px;
}

.margin-left-neg-35 {
    margin-left: -35px;
}

.margin-top-neg-10 {
    margin-top: -10px;
}

.margin-top-neg-15 {
    margin-top: -15px;
}

.margin-t-2 {
    margin-top: 2px;
}

.margin-t-7 {
    margin-top: 7px;
}

.margin-t-11 {
    margin-top: 11px;
}

.margin-t-17 {
    margin-top: 17px;
}

.margin-t-18 {
    margin-top: 18px;
}

.margin-neg-top-104 {
    margin-top: -104px;
}

.margin-neg-top-124 {
    margin-top: -124px;
}

.margin-top-neg-145 {
    margin-top: -145px;
}

.margin-b-0-if-last-force:last-of-type {
    margin-bottom: 0 !important;
}

.margin-b-11 {
    margin-bottom: 11px;
}

.bill-redesign .margin-b-22p5 {
    margin-bottom: 22.5px;
}

.margin-b-23 {
    margin-bottom: 23px;
}

.margin-l-38 {
    margin-left: 38px;
}

.margin-l-60 {
    margin-left: 60px;
}

.margin-r-13 {
    margin-right: 13px;
}

.margin-r-160 {
    margin-right: 160px;
}

.margin-h-7 {
    margin-left: 7px;
    margin-right: 7px;
}

.max-width-none-force {
    max-width: none !important;
}

.max-width-85 {
    max-width: 85px;
}

.max-width-101 {
    max-width: 101px;
}

.max-width-105 {
    max-width: 105px;
}

.max-width-135 {
    max-width: 135px;
}

.max-width-170 {
    max-width: 170px;
}

.max-width-200 {
    max-width: 200px;
}

.max-width-290 {
    max-width: 290px;
}

.min-width-22 {
    min-width: 22px;
}

.min-width-301 {
    min-width: 301px;
}

.max-width-164 {
    max-width: 164px;
}

.max-width-301 {
    max-width: 301px;
}

.max-width-353 {
    max-width: 353px;
}

.max-width-678 {
    max-width: 678px;
}

.max-width-95-pct {
    max-width: 95%;
}

.min-height-65 {
    min-height: 65px;
}

.img-fit-cover {
    object-fit: cover;
}

.overflow-y-auto {
    overflow-y: auto;
}
.bill-redesign-accss .overflow-visible {
    overflow: visible;
}
.column-spacer-15 {
    margin-left: -7.5px;
    margin-right: -7.5px;
}

    .column-spacer-15 > div {
        padding-left: 7.5px;
        padding-right: 7.5px;
    }

.txtPreWrap {
    white-space: pre-wrap;
}

.white-space-normal {
    white-space: normal;
}

.borderRadius10-t-l {
    border-top-left-radius: 10px;
}

.borderRadius10-t-r {
    border-top-right-radius: 10px;
}

.borderRadius10-b-l {
    border-bottom-left-radius: 10px;
}

.borderRadius10-b-r {
    border-bottom-right-radius: 10px;
}

.borderRadius10-left {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}

.borderRadius10-right {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
}

.borderRadiusAll4 {
    border-radius: 4px;
}

.borderRadiusAll5 {
    border-radius: 5px;
}

.border-allRound-force {
    -webkit-border-radius: 100% !important;
    -moz-border-radius: 100% !important;
    border-radius: 100% !important;
}

.borderRadius5-top {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

.border-white-right {
    border-right: 1px solid #ffffff;
}

.border-r-blue {
    border-right: 1px solid #00549a;
}

.border-1-l-GrayLight9 {
    border-left: 1px solid #979797;
}

.border-1-top-dashed-GrayLight6 {
    border-top: 1px dashed #D4D4D4;
}

.border-t-0 {
    border-top: 0
}

.border-spacing-0 {
    border-spacing: 0px;
}

.border-collapse-separate {
    border-collapse: separate;
}

.z-index-1 {
    z-index: 1;
}

.bill-redesign .border-lightGray-bottom-2px {
    border-bottom: 2px solid #D4D4D4;
}

.bill-redesign .border-black-bottom-2px {
    border-bottom: 2px solid #0E0E0E;
}

.txtWithIconUnderlineOnInteraction,
.txtWithIconUnderlineOnInteraction:hover,
.txtWithIconUnderlineOnInteraction:focus {
    text-decoration: none !important;
}

    .txtWithIconUnderlineOnInteraction:hover .interaction-decor-target,
    .txtWithIconUnderlineOnInteraction:focus .interaction-decor-target {
        text-decoration: underline !important;
    }

.icon-bill-redesign.icon-bill-redesign.txtYellow {
    color: #CC860B;
}

.icon-bill-redesign.icon-bill-redesign.txtGrey {
    color: #707070;
}

.list-style-type-disc {
    list-style-type: disc;
}

.bill-redesign-accss .top-1 {
    top: 1px;
}
/*Helper class - End*/

/*START* Rules and regulatory style*/

.bill-redesign .legal-regulatory-container button.disclosure-toggle {
    background: none;
    border: none;
    padding: 0;
}

.bill-redesign .legal-regulatory-container .disclosure-content {
    display: none;
}

    .bill-redesign .legal-regulatory-container .disclosure-content.show {
        display: block;
    }

/*END* Rules and regulatory style*/
.bill-redesign button:not(.btn):not([disabled]):not(.disabled) {
    cursor: pointer;
}

.bill-redesign .header-tab-control a {
    align-items: center;
    display: inline-flex;
}

.bill-redesign .tab-control .header-tab-control ul li a:not(.active):focus {
    border-bottom: 0px;
}

.bill-redesign .form-control-border {
    border-color: #949596;
}

.bill-redesign .form-control-select {
    padding-left: 10px;
}

    .bill-redesign .form-control-select + span {
        right: 15px;
        top: 5px;
    }
/*Hierarchy Design in Accordion*/
.vertical-bullet {
    margin-left: 5px;
    border-left: 1px solid lightGray;
    height: 30px;
}

li:last-child > div > div > span.vertical-bullet {
    height: 8px;
}

.horizontal-bullet {
    border-bottom: 1px solid lightGray;
    width: 10px;
    margin-bottom: 20px;
}

.height-vertical-end {
    height: 15px;
}

.card-dimension {
    height: 20px;
}
/*Tooltip Billing Custom*/

.payment-tooltip .tooltip {
    max-width: 290px;
}

.payment-tooltip .tooltip-inner {
    max-width: 290px;
    padding: 25px;
}

.info-tooltip-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}

.info-tooltip-modal-dialog {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 645px;
    width: 100%;
    max-height: 100%;
    overflow-y: auto;
    padding: 45px 15px;
}

.info-tooltip-modal .close {
    margin-top: 5px;
    margin-right: 5px;
    padding: 10px;
    font-size: inherit;
}

.info-tooltip-modal button.close:focus {
    border: 0;
    border-radius: 2px;
}

.history-disabled-tooltip .tooltip {
    max-width: 315px;
}

.history-disabled-tooltip .btn.disabled .tooltip {
    opacity: 1 !important;
}

.history-disabled-tooltip .tooltip-inner {
    max-width: 315px;
    padding: 15px;
}

.billhistory-d.container {
    padding-left: 15px;
    padding-right: 15px;
}

.billhistory-m tr:last-of-type {
    border-bottom: 1px solid #e1e1e1;
}

.bill-history-table th {
    width: auto;
}

    .bill-history-table th:nth-child(2),
    .bill-history-table td:nth-child(2) {
        width: 150px;
        padding-left: 30px;
    }
        .bill-history-table th:nth-child(2) > div,
        .bill-history-table td:nth-child(2) > div {
            text-align: right;
        }

    .bill-history-table th:nth-child(3),
    .bill-history-table td:nth-child(3) {
        width: 90px;
        padding-left: 30px;
    }

    .bill-history-table th:nth-child(4),
    .bill-history-table td:nth-child(4) {
        width: 66px;
        padding-left: 30px;
    }

    .bill-history-table th:nth-child(5),
    .bill-history-table td:nth-child(5) {
        width: 117px;
        padding-left: 30px;
        padding-right: 20px;
    }

.payment-history-table th {
    width: auto;
}

    .payment-history-table th:first-child,
    .payment-history-table td:first-child {
        width: 205px;
    }

    .payment-history-table th:nth-child(3),
    .payment-history-table td:nth-child(3) {
        text-align: right;
    }

/*Hierarchy Design in Accordion*/
.bill-redesign .modal.modal-tooltip .modal-body {
    padding: 0px 30px 30px;
    margin-bottom: 0px;
    margin-top: 0px;
}

.bill-history .modal.modal-tooltip .modal-body {
    padding: 0px 20px 15px;
    margin-bottom: 0px;
    margin-top: 0px;
}

.bill-redesign .close:not(:disabled):not(.disabled):focus,
.bill-redesign .close:not(:disabled):not(.disabled):hover {
    opacity: 1;
}

/*IE arrow tooltip fix*/

.bill-redesign .tooltip.tooltip.bs-tooltip-top .arrow::before {
    border-width: 20px 20px 0;
    top: 5px;
    left: 5px;
    padding-bottom: 15px;
}

.bill-redesign .tooltip.tooltip.bs-tooltip-bottom .arrow::before {
    padding-top: 15px;
}

.bill-redesign .tooltip.bs-tooltip-top {
    top: -10px !important;
}

.bill-redesign .tooltip.bs-tooltip-bottom {
    top: 10px !important;
}

.bill-redesign .tooltip .arrow {
    margin-bottom: -20px;
}

/*Billing Grid Header - Start*/
.billing-treegrid-header-bg {
    height: 339px;
}

.billing-treegrid-header {
    margin-top: -339px;
}

.billing-treegrid-header-d-print-accordion,
.billing-treegrid-header-d-download-accordion {
    left: -90px;
    z-index: 2;
    transition-duration: 0s;
}

.billing-treegrid-header-d-download-accordion {
    left: -74px;
}

    .billing-treegrid-header-d-print-accordion button:hover,
    .billing-treegrid-header-d-download-accordion button:hover,
    .billing-treegrid-header-d-print-accordion button:focus,
    .billing-treegrid-header-d-download-accordion button:focus,
    .billing-services-print-modal .billing-services-print-btns button:hover,
    .billing-services-download-modal .billing-services-download-btns button:hover,
    .billing-services-print-modal .billing-services-print-btns button:focus,
    .billing-services-download-modal .billing-services-download-btns button:focus {
        background-color: #F4F4F4;
    }

    .billing-treegrid-header-d-print-accordion .arrow,
    .billing-treegrid-header-d-download-accordion .arrow {
        position: relative;
        /*margin-top: 5px;*/
        background: #fff;
    }

        .billing-treegrid-header-d-print-accordion .arrow:after,
        .billing-treegrid-header-d-download-accordion .arrow:after {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-color: transparent;
            border-top: 0;
            top: -10px;
            left: 50%;
            border-bottom-color: #fff;
            border-width: 10px;
            transform: translateX(-50%);
        }

.billing-services-print-modal .modal-header,
.billing-services-download-modal .modal-header {
    z-index: 1;
}
/*Billing Grid Header - End*/

/*Treegrid - start*/

.billing-treegrid {
    width: 100%;
    white-space: nowrap;
    border-collapse: separate;
    table-layout: fixed;
    height: 1px;
}

    .billing-treegrid > td {
        height: 100%;
    }

    .billing-treegrid tr {
        display: table-row;
        cursor: default;
    }

    /* Extra space between columns for readability */
    .billing-treegrid th {
        overflow-x: hidden;
    }

        .billing-treegrid th > div {
            width: 100%;
            white-space: normal;
            text-align: right;
        }

    /*.billing-treegrid tr:focus,
.billing-treegrid td:focus,
.billing-treegrid a:focus {
    outline: 2px solid hsl(216, 94%, 70%) !important;
    outline: 2px solid hsl(216, 94%, 70%) !important;
    box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc;
    outline: 2px solid hsl(216, 94%, 70%) !important;
    background-color: hsl(216, 80%, 97%);
    box-shadow: none;
}*/

    /*.billing-treegrid a:focus {
    border-bottom: none;
}*/

    /* Hide collapsed rows */
    .billing-treegrid tr.hidden {
        display: none !important;
    }

/*default border transparent*/
.billing-treegrid-row-services td .billing-treegrid-cell-wrap {
    border-width: 2px 0px;
    border-style: solid;
    border-color: transparent;
}

.billing-treegrid-row-services td:first-child .billing-treegrid-cell-wrap {
    border-width: 2px 0px 2px 2px;
}

.billing-treegrid-row-services td:last-child .billing-treegrid-cell-wrap {
    border-width: 2px 2px 2px 0px;
}

/*border on hover*/
.billing-treegrid-row-services:hover,
.billing-treegrid-row-services:focus {
    position: relative;
}

.billing-treegrid-row-services[aria-expanded="false"]:hover::before,
.billing-treegrid-row-services[aria-expanded="false"]:focus::before {
    content: '';
    height: calc(100% - 15px);
    width: calc(100% - 30px);
    position: absolute;
    top: 7.5px;
    left: 15px;
    display: block;
    border: 2px solid #00549A;
    background-color: rgba(194,206,223,0.17);
    box-shadow: 0 6px 25px 0 rgba(0,0,0,0.12);
    z-index: 1;
    pointer-events: none;
    border-radius: 10px;
}

/*billing-treegrid-row-services gray border bottom*/
.billing-treegrid-row-services hr {
    width: calc(100% - 60px);
    left: 30px;
}

.billing-treegrid-row-services hr {
    width: calc(100% - 60px);
    left: 30px;
}

.billing-treegrid-row-services .billing-treegrid-row-services-title-wrap {
    padding-top: 15px;
    padding-bottom: 15px;
}

.billing-treegrid {
    height: auto;
}

    .billing-treegrid,
    .billing-treegrid thead,
    .billing-treegrid tbody {
        display: flex !important;
        flex-direction: column;
    }

        .billing-treegrid thead tr {
            min-height: 79px;
        }

        .billing-treegrid tbody tr.billing-treegrid-row-services {
            min-height: 80px;
        }

        .billing-treegrid tbody tr.billing-treegrid-row-subtotal {
            min-height: 78px;
        }

        .billing-treegrid tbody tr.billing-treegrid-row-misc {
            min-height: 58px;
        }

        .billing-treegrid tbody tr.billing-treegrid-row-total {
            min-height: 102px;
            border-radius: 0 0 10px 10px;
        }

        .billing-treegrid tbody tr.billing-treegrid-row-services,
        .billing-treegrid tbody tr.billing-treegrid-row-user {
            cursor: pointer;
        }

        .billing-treegrid tr, .billing-grid-sticky-nav-row, .billing-comparison-sticky-nav-row {
            display: flex !important;
            width: 100%;
            position: relative !important;
        }

            .billing-treegrid tr th, .billing-grid-sticky-nav-row > div,
            .billing-comparison-sticky-nav-row > div {
                display: inline-flex !important;
                justify-content: center;
                align-items: flex-end;
                flex-grow: 0;
                flex-shrink: 0;
                flex-direction: column;
            }

                .billing-treegrid tr th:first-child, .billing-grid-sticky-nav-row div:first-child,
                .billing-comparison-sticky-nav-row div:first-child,
                .billing-comparison-sticky-nav-row div:last-child {
                    align-items: flex-start;
                }



            .billing-treegrid tr td {
                display: inline-flex !important;
                justify-content: flex-end;
                align-items: stretch;
                flex-grow: 0;
                flex-shrink: 0;
            }

            .billing-treegrid tr > th:nth-child(2),
            .billing-treegrid tr > td:nth-child(2),
            .billing-grid-sticky-nav-row > div:nth-child(2),
            .billing-comparison-sticky-nav-row > div:nth-child(2) {
                /*width: 90px !important;*/
                width: 120px !important;
                white-space: normal;
                word-break: break-all;
            }

                .billing-treegrid tr > th:nth-child(2) > div,
                .billing-treegrid tr > th:nth-child(3) > div {
                    width: 100px !important;
                }

            .billing-treegrid tr > th:nth-child(3),
            .billing-treegrid tr > td:nth-child(3),
            .billing-grid-sticky-nav-row > div:nth-child(3),
            .billing-comparison-sticky-nav-row > div:nth-child(3) {
                /*width: 150px !important;*/
                width: 150px !important;
                white-space: normal;
                word-break: break-all;
            }

            .billing-treegrid tr > th:last-child:not(:first-child),
            .billing-treegrid tr > td:last-child:not(:first-child),
            .billing-grid-sticky-nav-row > div:last-child:not(:first-child),
            .billing-comparison-sticky-nav-row > div:last-child:not(:first-child) {
                width: 170px !important;
                white-space: normal;
                text-align: center;
            }

            .billing-treegrid:not(.billing-comparison-grid) tr > th:nth-child(3),
            .billing-treegrid:not(.billing-comparison-grid) tr > td:nth-child(3),
            .billing-grid-sticky-nav-row > div:nth-child(3) {
                width: 140px !important;
                color: #555555;
            }

            .billing-treegrid:not(.billing-comparison-grid) tr > th:last-child:not(:first-child),
            .billing-treegrid:not(.billing-comparison-grid) tr > td:last-child:not(:first-child),
            .billing-grid-sticky-nav-row > div:last-child:not(:first-child) {
                width: 180px !important;
            }

            .billing-treegrid tr > th:first-child,
            .billing-treegrid tr > td:first-child,
            .billing-grid-sticky-nav-row > div:first-child,
            .billing-grid-sticky-nav-row > div:first-child > div.billing-grid-sticky-nav-service,
            .billing-comparison-sticky-nav-row > div:first-child,
            .billing-comparison-sticky-nav-row > div:first-child > div.billing-grid-sticky-nav-service {
                width: 100% !important;
                flex-grow: 1;
                flex-shrink: 1;
                justify-content: flex-start;
            }

            .billing-treegrid tr > td > div {
                flex-basis: 100%;
            }

.billing-treegrid-row-services #injectible-expand-text,
.billing-treegrid-row-user #injectible-expand-text-2 {
    pointer-events: none;
}

.billing-treegrid tr td:last-child .icon-chevron-up {
    z-index: 2;
}

.billing-treegrid-services-title {
    margin-top: 0px !important;
}

.billing-treegrid-row-services .icon-tv:before {
    margin-top: -2px;
}

.billing-treegrid-row-services .icon-laptop:before {
    margin-top: -5px;
}

.injectible-expand-text-inner {
    height: 100%;
    display: flex;
    align-items: center;
}

.billing-treegrid tr > th:nth-child(2) > div > div:nth-child(2),
.billing-treegrid tr > th:nth-child(2) > div > span > div:nth-child(2),
.billing-grid-sticky-nav-row > div:nth-child(2) > div > div:nth-child(2),
.billing-grid-sticky-nav-row > div:nth-child(2) > span > div:nth-child(2) {
    white-space: nowrap;
}

/*Row services focus - start*/
.billing-treegrid tr:focus,
.billing-treegrid-row-services:focus {
    outline: 0 !important;
    box-shadow: none !important;
    position: relative;
}

    .billing-treegrid tr:focus::after {
        content: '';
        height: calc(100% - 6px);
        width: calc(100% - 29px);
        position: absolute;
        top: 3px;
        left: 15px;
        display: block;
        outline: 2px solid #0075ff !important;
        z-index: 1;
        pointer-events: none;
        border-radius: 10px;
    }

.billing-treegrid tr.billing-treegrid-row-services:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-mobility:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-tv:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-internet:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-homephone:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-other:focus::after,
.billing-treegrid tr.billing-treegrid-row-subtotal:focus::after {
    height: calc(100% + 6px);
    width: calc(100% - 24px);
    position: absolute;
    top: -3px;
    left: 12px;
}

.billing-treegrid tr.billing-treegrid-row-services[aria-expanded="false"]:focus::after {
    height: calc(100% - 9px);
    width: calc(100% - 24px);
    top: 4px;
    position: absolute;
    left: 12px;
}

.billing-treegrid tr.billing-treegrid-row-2:focus::after,
.billing-treegrid tr.billing-treegrid-row-3:focus::after,
.billing-treegrid tr.billing-treegrid-row-4:focus::after,
.billing-treegrid tr.billing-treegrid-row-5:focus::after {
    height: 100%;
    width: calc(100% - 130px);
    position: absolute;
    top: 0px;
    left: 99px;
}

.billing-treegrid tr.billing-treegrid-row-user:focus::after {
    width: calc(100% - 54px);
    left: 27px;
}

.billing-treegrid tr.billing-treegrid-row-sharing:focus::after {
    height: calc(100% - 12px);
    width: calc(100% - 54px);
    top: 11px;
    left: 27px;
}

.billing-treegrid tr.billing-treegrid-row-ads:focus::after {
    height: calc(100% - 20px);
    top: 20px;
}

.billing-treegrid tr.billing-treegrid-subtotal-mobility:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-tv:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-internet:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-homephone:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-other:focus::after {
    height: calc(100% - 9px);
    top: 12px;
}

.billing-treegrid tr.billing-treegrid-user-subtotal:focus::after {
    height: calc(100% - 20px);
    top: 10px;
}

.billing-treegrid tr.billing-treegrid-row-total:focus::after {
    outline: 2px solid #ffffff !important;
}


/*Treegrid cell focus - start*/
.billing-treegrid td:focus {
    outline: 0 !important;
    box-shadow: none !important;
    position: relative;
}

    .billing-treegrid td:focus::after {
        content: '';
        height: calc(100% - 18px);
        width: calc(100% - 6px);
        position: absolute;
        top: 9px;
        left: 3px;
        display: block;
        outline: 2px solid #0075ff !important;
        z-index: 1;
        pointer-events: none;
        border-radius: 5px;
    }

.billing-treegrid-row-total td:focus::after {
    outline: 2px solid #ffffff !important;
}

.billing-treegrid td:first-child:focus::after {
    width: calc(100% - 33px);
    position: absolute;
    left: 29px;
    border-radius: 5px;
}

.billing-treegrid td:last-child:focus::after {
    width: calc(100% - 27px);
}

.billing-treegrid .billing-treegrid-row-misc td:first-child:focus::after {
    left: 22.5px;
}

.billing-treegrid .billing-treegrid-row-total:focus::after {
    height: calc(100% - 12px);
    top: 6px;
}

.billing-treegrid .billing-treegrid-row-total td:first-child:focus::after {
    left: 16.5px;
}

.billing-treegrid tr.billing-treegrid-row-3 td:focus::after,
.billing-treegrid tr.billing-treegrid-row-4 td:focus::after,
.billing-treegrid tr.billing-treegrid-row-5 td:focus::after {
    width: calc(100% - 6px);
    height: calc(100% - 6px);
    top: 3px;
    left: 3px;
}

.billing-treegrid tr.billing-treegrid-row-3 td:first-child:focus::after,
.billing-treegrid tr.billing-treegrid-row-4 td:first-child:focus::after,
.billing-treegrid tr.billing-treegrid-row-5 td:first-child:focus::after {
    width: calc(100% - 103px);
    left: 99px;
}

.billing-treegrid tr.billing-treegrid-row-3 td:last-child:focus::after,
.billing-treegrid tr.billing-treegrid-row-4 td:last-child:focus::after,
.billing-treegrid tr.billing-treegrid-row-5 td:last-child:focus::after {
    width: calc(100% - 35px);
}

.billing-treegrid tr.billing-treegrid-row-user td:first-child:focus::after {
    width: calc(100% - 43px);
    left: 34px;
}

.billing-treegrid tr.billing-treegrid-row-sharing td:focus::after {
    height: calc(100% - 18px);
    width: calc(100% - 33px);
    top: 14px;
    left: 27px;
}

.billing-treegrid tr.billing-treegrid-row-user td:last-child:focus::after {
    width: calc(100% - 27px);
}

.billing-treegrid tr.billing-treegrid-row-sharing td:last-child:focus::after {
    width: calc(100% - 30px);
    left: 3px;
}

.billing-treegrid tr.billing-treegrid-subtotal-mobility td:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-tv td:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-internet td:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-homephone td:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-other td:focus::after {
    height: calc(100% - 33px);
    width: calc(100% - 6px);
    top: 24px;
}

.billing-treegrid tr.billing-treegrid-subtotal-mobility td:first-child:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-tv td:first-child:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-internet td:first-child:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-homephone td:first-child:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-other td:first-child:focus::after {
    width: calc(100% - 33px);
    left: 25px;
}

.billing-treegrid tr.billing-treegrid-subtotal-mobility td:last-child:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-tv td:last-child:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-internet td:last-child:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-homephone td:last-child:focus::after,
.billing-treegrid tr.billing-treegrid-subtotal-other td:last-child:focus::after {
    width: calc(100% - 27px);
}

.billing-treegrid tr.billing-treegrid-user-subtotal td:focus::after {
    height: calc(100% - 26px);
    top: 13px;
}

.billing-treegrid tr.billing-treegrid-row-3[aria-posinset="1"].not-collapsible-row:not(.billing-treegrid-row-sharing):focus::after,
.billing-treegrid tr.billing-treegrid-row-4[aria-posinset="1"].not-collapsible-row:not(.billing-treegrid-row-sharing):focus::after {
    height: calc(100% - 15px);
    top: auto;
    bottom: 0;
}

.billing-treegrid tr.billing-treegrid-row-3[aria-posinset="1"][aria-level="2"].not-collapsible-row:not(.billing-treegrid-row-sharing):focus::after {
    height: calc(100% - 30px);
}


.billing-treegrid tr.billing-treegrid-row-3[aria-posinset="1"].not-collapsible-row:not(.billing-treegrid-row-sharing) td:focus::after,
.billing-treegrid tr.billing-treegrid-row-4[aria-posinset="1"].not-collapsible-row:not(.billing-treegrid-row-sharing) td:focus::after {
    height: calc(100% - 21px);
    top: auto;
    bottom: 3px;
}

.billing-treegrid-row-total .big-price,
.billing-treegrid-row-mob-total .big-price {
    color: #ffffff;
    font-size: 30px;
    letter-spacing: -0.75px;
    line-height: 22px;
}

    .billing-treegrid-row-total .big-price span,
    .billing-treegrid-row-mob-total .big-price span {
        font-size: 14px;
        letter-spacing: -0.35px;
        line-height: 22px;
        top: -3px;
    }

        .billing-treegrid-row-total .big-price span:last-of-type,
        .billing-treegrid-row-mob-total .big-price span:last-of-type {
            margin-left: 2px;
        }

/*Row expanded chevron - start*/
.billing-treegrid tr[aria-expanded="true"] .icon-chevron-up:before {
    -webkit-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    transform: rotate(-90deg);
    -webkit-transform-origin: center;
    -ms-transform-origin: center;
    transform-origin: center;
}

.billing-treegrid tr[aria-expanded="false"] .icon-chevron-up:before {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-transform-origin: center;
    -ms-transform-origin: center;
    transform-origin: center;
}

/*Row Service expanded*/
.billing-treegrid-row-services[aria-expanded="true"] .billing-treegrid-cell-wrap,
.billing-treegrid-subtotal-mobility .billing-treegrid-cell-wrap,
.billing-treegrid-subtotal-tv .billing-treegrid-cell-wrap,
.billing-treegrid-subtotal-internet .billing-treegrid-cell-wrap,
.billing-treegrid-subtotal-homephone .billing-treegrid-cell-wrap,
.billing-treegrid-subtotal-other .billing-treegrid-cell-wrap {
    background-color: rgba(194,206,223,0.17);
}

.billing-treegrid-row-services[aria-expanded="true"] hr,
.billing-treegrid-row-user[aria-expanded="true"] hr {
    border: none;
}

.billing-treegrid-row-services[aria-expanded="true"] .billing-treegrid-row-services-amount,
.billing-treegrid-row-user[aria-expanded="true"] .billing-treegrid-row-user-amount {
    display: none;
}

.billing-treegrid-row-services[aria-expanded="true"] .icon-increase,
.billing-treegrid-row-services[aria-expanded="true"] .icon-decrease,
.billing-treegrid-row-services[aria-expanded="true"] #injectible-expand-text,
.billing-treegrid-row-user[aria-expanded="true"] #injectible-expand-text-2 {
    display: none;
}

.billing-treegrid-row-services[aria-expanded="true"] + tr:not(.billing-treegrid-row-user):not(.billing-treegrid-row-sharing) {
    padding-top: 15px;
}

    .billing-treegrid-row-services[aria-expanded="true"] + tr:not(.billing-treegrid-row-user):not(.billing-treegrid-row-sharing):focus::after {
        padding-top: 15px;
        height: calc(100% - 15px);
        top: 15px;
    }

.billing-treegrid tbody tr.billing-treegrid-row-services[aria-expanded="true"] {
    min-height: 65px;
}

.billing-treegrid-service-subtotal td:first-child .billing-treegrid-cell-wrap {
    padding-top: 15px;
    padding-bottom: 15px;
    min-height: 65px;
}

/*Row Expanded Blue border*/
.billing-treegrid-row-services[aria-expanded="true"]:before,
.billing-treegrid-row-2:before,
.billing-treegrid-row-3:before,
.billing-treegrid-row-4:before,
.billing-treegrid-row-5:before,
.billing-treegrid-subtotal-mobility:before,
.billing-treegrid-subtotal-tv:before,
.billing-treegrid-subtotal-internet:before,
.billing-treegrid-subtotal-homephone:before,
.billing-treegrid-subtotal-other:before {
    content: '';
    height: 100%;
    width: calc(100% - 30px);
    position: absolute;
    top: 0px;
    left: 15px;
    display: block;
    border: 2px solid #00549A;
    z-index: 0;
    border-radius: 10px;
    pointer-events: none;
}

.billing-treegrid-service-subtotal {
    margin-bottom: 7.5px;
}

.billing-treegrid-row-services[aria-expanded="true"] {
    margin-top: 7.5px;
}

.billing-treegrid thead tr {
    margin-bottom: 7.5px;
}

.billing-treegrid-row-services[aria-expanded="true"]:before,
.billing-treegrid-row-services[aria-expanded="true"]:hover::before {
    border-bottom: 0px;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
}

.billing-treegrid-row-2:before,
.billing-treegrid-row-3:before,
.billing-treegrid-row-4:before,
.billing-treegrid-row-5:before {
    border-top: 0px;
    border-bottom: 0px;
    border-radius: 0px;
}

.billing-treegrid-subtotal-mobility:before,
.billing-treegrid-subtotal-tv:before,
.billing-treegrid-subtotal-internet:before,
.billing-treegrid-subtotal-homephone:before,
.billing-treegrid-subtotal-other:before {
    border-top: 0px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
}

/*Start - Billing Grid Sticky Nav*/
.billing-grid-sticky-nav {
    z-index: 99998;
}

.billing-grid-sticky-nav-row,
.billing-grid-sticky-nav-row-mob,
.billing-comparison-sticky-nav-row {
    height: 79px;
}

    .billing-grid-sticky-nav-row .icon-chevron-up, .billing-grid-sticky-nav-row #injectible-expand-text, .billing-grid-sticky-nav-row #injectible-expand-text-2,
    .billing-comparison-sticky-nav-row .icon-chevron-up, .billing-comparison-sticky-nav-row #injectible-expand-text, .billing-comparison-sticky-nav-row #injectible-expand-text-2 {
        visibility: hidden;
    }
/*End - Billing Grid Sticky Nav*/

/*Billing History-accordion*/
.billing-download-accordion {
    left: -58px;
    z-index: 2;
    transition-duration: 0s;
}

    .billing-download-accordion button:hover,
    .billing-download-accordion button:focus {
        background-color: #F4F4F4;
    }

    .billing-download-accordion .arrow {
        position: relative;
        background: #fff;
    }
        .billing-download-accordion .arrow:before {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-color: transparent;
            border-bottom-color: #E1E1E1;
            border-width: 10px;
            border-top: 0;
            top: -11px;
            left: 48%;
            z-index: -1;
            transform: translateX(-50%);
        }

        .billing-download-accordion .arrow:after {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-color: transparent;
            border-top: 0;
            top: -10px;
            left: 48%;
            border-bottom-color: #fff;
            border-width: 10px;
            transform: translateX(-50%);
        }

/*End Billing History-accordion*/


/*Bill Comparison - Start*/
.billing-comparison-grid tr > th:nth-child(3),
.billing-comparison-grid tr > td:nth-child(3) {
    width: 125px !important;
}

.billing-comparison-grid tr > th:last-child:not(:first-child),
.billing-comparison-grid tr > td:last-child:not(:first-child) {
    width: 210px !important;
    white-space: normal;
    text-align: center;
}

.billing-treegrid-row-chart-bar {
    padding-right: 203px;
    width: calc(100% - 31px);
    right: 0;
}

.billing-treegrid-row-chart-bar-previous,
.billing-treegrid-row-chart-bar-current {
    width: 60px;
}

.billing-treegrid-row-chart-bar-previous {
    height: 98px;
}

.billing-treegrid-row-chart-bar-current {
    height: 68px;
}

.billing-treegrid-row-chart-bar-current-label {
    min-width: 100px;
    max-width: 100px;
    margin-left: -20px;
    white-space: normal;
    text-align: left;
    align-items: flex-end;
    padding-left: 23px;
}

    .billing-treegrid-row-chart-bar-current-label span.icon-bill-redesign {
        margin-bottom: 3px;
    }

.billing-treegrid-row-chart-line-amount {
    min-width: 53px;
    margin-right: 0px;
    font-size: 12px;
    line-height: 14px;
    padding-right: 15px;
    text-align: right;
}

.billing-treegrid-row-chart-baseline .billing-treegrid-row-chart-line-style {
    border-top: 1px solid #E1E1E1;
}

button.btn-compare-bills.disabled,
button.btn-compare-bills.disabled:hover,
button.btn-compare-bills.disabled:focus {
    pointer-events: auto;
    background-color: #CCCCCC;
    border: 2px solid #CCCCCC;
    opacity: 1;
}

.billing-treegrid-row-chart-line .billing-treegrid-row-chart-line-row:last-child {
    margin-bottom: 0px;
}

.billing-treegrid-row-chart-bar-wrap {
    height: 1px;
    width: 100%;
    display: none;
    right: 203px;
}

.billing-treegrid-row-chart-baseline .billing-treegrid-row-chart-bar-wrap {
    display: block;
}

.billing-treegrid-row-chart-bar-wrap .billing-treegrid-row-chart-bar-previous {
    position: absolute;
    bottom: 0;
    right: 126px;
}

.billing-treegrid-row-chart-bar-wrap .billing-treegrid-row-chart-bar-current {
    height: 98px;
    position: absolute;
    right: 0;
}

.billing-treegrid-row-chart-bar-current .billing-treegrid-row-chart-bar-current-label {
    bottom: 100%;
}

.billing-treegrid-row-chart-bar-positive {
    bottom: 0;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

.billing-treegrid-row-chart-bar-negative {
    top: 0;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}

/*Bill Comparison - End*/

/*PBE - Start*/

.pbe-chart {
    max-width: 795px;
    transition: none;
}

.pbe-line {
    width: 100%;
    height: 10px;
    border-left: 1px solid #979797;
    border-right: 1px solid #979797;
    background: linear-gradient(#979797,#979797) center/100% 1px no-repeat;
    position: absolute;
    top: -5px;
}

.pbe-tag {
    background-color: rgba(0,84,154,0.2);
}

.pbe-tag-icon img {
    width: auto;
    height: 14px;
    display: flex;
}

/* START Bill explainer */

/* PBE helpers start */
.border-new-grey {
    border: 1px solid #949596;
}

.bg-stripe-blue {
    background-image: repeating-linear-gradient(135deg, #00549a 0, #00549a 1px, #c2cedf 0, #c2cedf 50%);
    background-size: 10px 10px;
}

.bg-dark-blue-5E {
    background-color: #00215E;
}

.invisible-force {
    visibility: hidden !important;
    opacity: 0 !important;
}

.d-none-force {
    display: none !important;
}

/* PBE helpers end */

/* bars start */

.bars-container {
    position: absolute;
    width: 100%;
    border-radius: 0 0 5px 5px;
    z-index: 1;
    transform: translateY(-15px);
}

.split-bar-left,
.split-bar-left > div {
    border-radius: 5px 0 0 5px;
}

.split-bar-right,
.split-bar-right > div {
    border-radius: 0 5px 5px 0;
}

.split-divider-indicator {
    position: absolute;
    bottom: 0;
    height: calc(100% + 9px);
}
    .split-divider-indicator.first-row-only {
        bottom: 34px;
    }

    .split-divider-indicator > div {
        background: #0075FF;
    }

/* bars end */

/* label group start */

.label-group-top {
    margin-bottom: 34px;
}

.label-group-bottom {
    margin-top: 34px;
}

.single-bar + .labels-container .label-group-bottom {
    margin-top: -34px;
}

.pbe-col {
    z-index: 0;
}

.pbe-last-charge-label {
    bottom: 0;
    left: 0;
    z-index: 1;
}

.dashed-line-indicator-left,
.dashed-line-indicator-right {
    border-bottom: 1px dashed #949596;
    width: 100%;
    position: relative;
}

.dashed-line-indicator-right {
    flex: 1;
}

.dashed-line-indicator-left-h {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    bottom: 0;
    height: 64px;
    border-left: 1px dashed #949596;
}

.dashed-line-indicator-right-arrow {
  position: absolute;
  right: 0;
  height: 10px;
  width: 10px;
  line-height: 10px;
  text-align: center;
}

.dashed-line-indicator-right-arrow:after {
  position: absolute;
  top: 0;
  left: 0;
  content: '';
  height: 100%;
  width: 100%;
  border: 1px solid #949596;
  border-bottom: 0;
  border-left: 0;
  transform: rotateX(45deg) rotateZ(45deg);
}

.current-late-payment-charge-label {
  padding-left: 18px;
}

/* label group end */

/* divider label start */

.split-divider-label {
    padding-bottom: 27px; /* 15px (half of bar) + 9px (divider indicator excess top) + 3px (space between divider label and indicator) */
    position: absolute;
    z-index: 2;
    bottom: 0;
}

/* divider label end */
/* origin label start */
.split-origin-label {
    padding-top: 21px; /* 15px (half of bar) + 6px (space between origin label and bar) */
    padding-bottom: 7.5px;
    padding-right: 10px;
    position: absolute;
    z-index: 0;
}

.label-indicator-line-origin {
    height: 30px;
    width: 1px;
    background: #949596;
    position: relative;
    top: -13.5px;
    flex-grow: 0;
}

    .label-indicator-line-origin + div {
        padding-left: 10px;
    }

/* origin label end */

/* right label start */

.split-right-label {
    position: relative;
    z-index: 2;
    padding-left: 25px;
}

    .split-right-label:only-child .label-indicator-line-top .same-height {
        min-height: 48px;
    }

    .split-right-label:only-child .label-indicator-line-bottom .same-height {
        min-height: 48px;
    }

.split-right-label-DS .label-indicator-line-top {
    margin-top:2px;
}
/* right label end */

/* right label indicator line start */
.label-indicator-line-divider,
.label-indicator-line-top,
.label-indicator-line-bottom {
    width: 1px;
    background: #949596;
    position: relative;
}

.label-indicator-line-top {
    margin-top: 7px; /* space between top indicator line and right label */
}

.label-indicator-line-bottom {
    margin-bottom: 2px; /* space between bottom indicator line and right label */
}

    .label-indicator-line-top::after,
    .label-indicator-line-bottom::after {
        content: '';
        position: absolute;
        left: 50%;
        height: 7px;
        width: 7px;
        border-radius: 50%;
        background-color: #949596;
    }

.label-indicator-line-top::after {
    bottom: 0;
    transform: translate(-50%, 50%);
}

.label-indicator-line-bottom::after {
    top: 0;
    transform: translate(-50%, -50%);
}

.label-indicator-line-divider {
    top: 3px;
}
/* right label indicator line end */

/* END Bill explainer */

/*Start Pagination*/
.bill-redesign .page-number-active {
    background-color: #00549a;
    color: #ffffff;
    font-weight: bold;
}

.bill-redesign .page-number-nav {
    height: 30px;
    width: 30px;
    border-radius: 50%;
}

.bill-redesign .pagination > li > a, .pagination > li > span {
    border-radius: 0;
    padding: 0px 0px;
}


.bill-pagination {
    color: #00549A;
}

.pagination {
    font-size: 14px;
    text-align: center;
}

.bill-pagination ol {
    list-style-type: none;
    padding: 0;
    margin: 0;
    display: inline-block;
}

.bill-pagination li.active a {
    background: #00549A;
    color: #fff;
    display: flex;
}

    .bill-pagination li.active a:hover {
        text-decoration: none;
        cursor: default;
    }

.bill-prev.disabled, .bill-next.disabled {
    cursor: default;
    color: #949596;
}

    .bill-prev.disabled:hover, .bill-next.disabled:hover {
        cursor: default;
        text-decoration: none;
    }

.bill-prev {
    margin-right: 2.5px;
}

.bill-next {
    margin-left: 2.5px;
}
/*End Pagination*/

.pbe-charges-list li span.pbe-charges-list-span {
    position: relative;
    left: 10px;
}

.pbe-charges-list li {
    margin-top: 10px
}

.message > span:not(.message-count):not(.sr-only) p,
.message-mob > span:not(.message-count):not(.sr-only) p {
    margin-bottom: 0;
}

.message > .message-count,
.message-mob > .message-count {
    position: absolute;
    margin-top: 0.25em;
}

.overflow-information,
.overflow-information > span:not(.message-count):not(.sr-only) {
    overflow: hidden;
    display: flex;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 90%;
}

    .overflow-information > span:not(.message-count):not(.sr-only),
    .overflow-information > span:not(.message-count):not(.sr-only) p {
        display: inline;
        width: 100%;
    }

        .overflow-information > span:not(.message-count):not(.sr-only) p:not(:first-of-type),
        .overflow-information > span:not(.message-count):not(.sr-only) p:not(:first-of-type) .message-count.invisible {
            display: none;
        }
/*Share Group Modal*/

.share-group-donut-graph-inner {
    width: 247px;
}

.share-group-absolute {
    position: absolute;
    top: 20%;
    right: 0;
    left: 0;
}

.share-group-text {
    text-anchor: middle;
}

.share-group-text-header {
    font-family: "bellslim_font_black",Helvetica,Arial,sans-serif;
    font-size: 6.22px;
    font-weight: 700;
    fill: #00549A !important;
}

.share-group-text-body {
    font-size: 2.4px;
    fill: #555555;
}

.share-group-text-body-red {
    fill: #BD2025;
}

.share-group-visually-hidden {
    border: 0;
    padding: 0;
    margin: 0;
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
    clip: rect(1px 1px 1px 1px);
    clip: rect(1px, 1px, 1px, 1px);
    white-space: nowrap;
    font-size: 0px;
    line-height: 0;
}

.share-group-table tbody tr.share-group-row:before {
    position: absolute;
    border-bottom: 1px solid #e1e1e1;
    width: calc(100% - 60px);
    left: 30px;
    content: '';
    display: block;
    z-index: 1;
}

tr.share-group-row > td:first-child > div {
    margin-left: 30px;
}

tr.share-group-row > td:last-child > div {
    margin-right: 30px;
}

tr.share-group-row:last-child > td > div {
    border: 0;
}

.share-group-table,
.share-group-table thead,
.share-group-table tbody,
.share-group-table tfoot {
    display: flex !important;
    flex-direction: column;
}

    .share-group-table tr {
        display: flex !important;
        width: 100%;
        position: relative !important;
    }

        .share-group-table tr th {
            display: inline-flex !important;
            justify-content: center;
            flex-grow: 0;
            flex-shrink: 0;
            flex-direction: column;
        }

        .share-group-table tr td {
            display: inline-flex !important;
            justify-content: flex-end;
            align-items: stretch;
            flex-grow: 0;
            flex-shrink: 0;
        }

        .share-group-table tr > th:first-child,
        .share-group-table tr > td:first-child {
            width: 100% !important;
            flex-grow: 1;
            flex-shrink: 1;
            justify-content: flex-start;
            overflow: hidden;
        }

        .share-group-table tr > td > div {
            flex-basis: 100%;
        }

        .share-group-table tr > th:nth-child(2),
        .share-group-table tr > th:nth-child(3),
        .share-group-table tr > td:nth-child(2),
        .share-group-table tr > td:nth-child(3) {
            width: 23% !important;
        }

    .share-group-table tbody tr > td > div {
        word-break: break-all;
    }

    .share-group-table tbody tr > td:nth-child(2) > div,
    .share-group-table tbody tr > td:nth-child(3) > div {
        padding-left: 5px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }

    .share-group-table tr > th:first-child {
        padding-left: 30px;
    }

    .share-group-table tr > th:last-child {
        padding-right: 30px;
    }

.share-group-footer {
    margin-left: 30px;
}

tr > td:last-child > div > span.share-group-footer {
    margin-left: 0px;
    margin-right: 30px;
}

.share-group-donut-graph-inner .icon-infinity-no-pad {
    top: 36%;
}

/*Share Group Modal*/

/*Bill-Comparison Modal*/
.bill-comparison-table,
.bill-comparison-table thead,
.bill-comparison-table tbody,
.bill-comparison-table tfoot {
    display: flex !important;
    flex-direction: column;
}

    .bill-comparison-table tr {
        display: flex !important;
        width: 100%;
        position: relative !important;
    }

        .bill-comparison-table tr th {
            display: inline-flex !important;
            justify-content: center;
            flex-grow: 0;
            flex-shrink: 0;
            flex-direction: column;
        }

        .bill-comparison-table tr td {
            display: inline-flex !important;
            justify-content: flex-end;
            align-items: stretch;
            flex-grow: 0;
            flex-shrink: 0;
        }

        .bill-comparison-table tr > th:first-child,
        .bill-comparison-table tr > td:first-child {
            flex-grow: 1;
            flex-shrink: 1;
            justify-content: flex-start;
        }

        .bill-comparison-table tr > th:first-child {
            width: 100% !important;
        }

        .bill-comparison-table tr > td:first-child {
            width: 38%;
        }


        .bill-comparison-table tr > td > div {
            flex-basis: 100%;
        }

        .bill-comparison-table tr > td:nth-child(2),
        .bill-comparison-table tr > td:nth-child(3) {
            width: 90px !important;
        }


    .bill-comparison-table tbody tr > td:nth-child(2) > div,
    .bill-comparison-table tbody tr > td:nth-child(3) > div {
        word-break: break-all;
    }
/*Bill Comparison Modal*/
/* START Billing Bill Boards */
.PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-wrapper {
    padding: 0;
}

.PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-tile-container {
    display: flex !important;
    flex-direction: column;
    margin-bottom: 0;
}

    .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-tile-container .txtWithIconUnderlineOnInteraction {
        display: inline-flex !important;
    }

.PersonalizationTilesContainer[data-location="BillingBillBoards"].personalization-revamped-container .personalization-slider-container.responsive.slick-initialized {
    box-shadow: none;
    background: none;
}

.PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container .offer-tile-img-container {
    margin-left: 0;
    padding-top: 0;
    padding-left: 30px;
    padding-bottom: 0;
    padding-right: 0;
}

    .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container .offer-tile-img-container > img {
        width: auto;
        height: 156px;
        margin-bottom: -40px;
    }

.PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container .offer-tile-content-container > div:last-child a {
    padding: 8px 15px;
    font-size: 15px;
    line-height: 17px;
    letter-spacing: -0.25px;
    border-radius: 17.5px;
    display: flex;
    align-items: center;
}

.PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container .offer-tiles-modal:not(.show){
    display: none;
}

.PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container .slick-dots button:focus {
    outline-offset: 4px;
    outline: 2px solid #0075ff !important;
    box-shadow: none !important;
}

.PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container.slick-initialized .slick-slide > div > div {
    flex-direction: column;
}

.opacity-0-force {
    opacity: 0 !important;
}

.offer-tiles-modal:not(.show) {
    display: none !important;
}

.offer-tiles-modal.show {
    display: block !important;
}

.billingcta .margin-4-top {
    margin-top: 4px;
}
/* END Billing Bill Boards */

/* START PBE MODAL */

.ds-pbe-modal:not(.ds-pbe-modal-late-payment-charge-lg) .modal-dialog {
    text-align: center;
}

    .ds-pbe-modal:not(.ds-pbe-modal-late-payment-charge-lg) .modal-dialog .modal-content {
        max-width: 465px;
        max-height: 100%;
        text-align: left;
    }

.ds-pbe-modal .modal-dialog .modal-content .modal-header {
    height: auto;
}

.ds-pbe-modal .modal-dialog .modal-content .modal-body {
    max-height: none !important;
    overflow-y: visible !important;
}

    .ds-pbe-modal .modal-dialog .modal-content .modal-body .border-2px {
        border-width: 2px !important;
    }

    .ds-pbe-modal .modal-dialog .modal-content .modal-body ul.pbe-list-service li:last-of-type {
        margin-bottom: 0;
    }

    .ds-pbe-modal .modal-dialog .modal-content .modal-body .pbe-mobile-container {
        display: inline-flex;
        width: 248px;
        flex-wrap: wrap;
        margin-bottom: -12.55px;
    }

        .ds-pbe-modal .modal-dialog .modal-content .modal-body .pbe-mobile-container span.icon-bill-redesign {
            margin-bottom: 12.55px;
            margin-right: 12.55px;
        }

/* END PBE MODAL */
@media all and (-ms-high-contrast:none) {
    .billing-treegrid-row-services:focus::after {
        left: 17px;
    }
}
/*Treegrid - End*/

/*Billing services Mobile - start*/
.billing-services-mob-list li button:hover,
.billing-services-mob-list li button:focus {
    background-color: rgba(194,206,223,0.17);
}

/*Print/Download Dialog - Mobile - Start*/
.billing-services-print-modal .modal-dialog,
.billing-services-download-modal .modal-dialog {
    width: 194px;
}

.billing-services-print-modal .modal-header,
.billing-services-download-modal .modal-header {
    height: auto;
}

.billing-services-print-modal .modal-dialog .modal-content .modal-header .close,
.billing-services-download-modal .modal-dialog .modal-content .modal-header .close {
    margin: 0;
    padding: 0;
    border: 0;
}

/*Mobility Modal - Start */
.bell-services-mobility-modal li hr {
    margin: 15px auto;
    width: calc(100% - 30px);
    border-top: 1px solid #E1E1E1;
}

.bell-services-mobility-modal ul {
    margin-top: 15px;
}

    .bell-services-mobility-modal ul ul {
        margin-top: 0px;
    }

    .bell-services-mobility-modal ul:last-of-type > li:last-of-type hr {
        margin-bottom: 0px;
        border: none;
    }

.bell-services-mobility-modal button {
    padding: 0px;
}

.bell-services-mobility-modal li button[aria-expanded="false"] .icon-chevron-up:before {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-transform-origin: center;
    -ms-transform-origin: center;
    transform-origin: center;
}

.bell-services-mobility-modal li button[aria-expanded="true"] .icon-chevron-up:before {
    -webkit-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    transform: rotate(-90deg);
    -webkit-transform-origin: center;
    -ms-transform-origin: center;
    transform-origin: center;
}

.bell-services-mobility-modal li button[aria-expanded="true"] + hr {
    background: none;
    clear: both;
    float: none;
    width: 100%;
    height: 1px;
    border: none;
    margin: -1px 0;
}


.bell-services-mobility-modal .table > tbody > tr > th {
    padding: 15px 0 0 15px;
}

.bell-services-mobility-modal .table td {
    padding: 0;
}

.bell-services-mobility-modal .table {
    table-layout: unset;
}

.bell-services-modal ul {
    margin-top: 0px;
}

.bell-services-modal li button[aria-expanded="true"] {
    margin-bottom: -30px;
}

.bell-services-modal li hr {
    margin: 0px auto;
}

.bell-services-modal-user-btn, .bell-service-compare-modal-user-btn {
    min-height: 65px;
}

.pbe-spacer {
    padding: 0 30px 5px !important;
}

.service-width {
    min-width: 120px;
    max-width: 100%;
}

.bell-services-mobility-modal {
    z-index: 99997;
}
/*End Mobility Modal*/

/*Start - Billing Date - dropdown*/

.bill-redesign .form-control-select.aria-combobox-select-trigger {
    padding: 13px 15px 12px 15px;
}

.aria-combobox-select-wrapper .aria-combobox-select-listbox {
    position: absolute;
    left: 0;
    width: 100%;
    z-index: 2;
    background-color: white;
    border: #949596 2px solid;
    border-top: 0;
}

.bill-redesign .form-control-border.aria-combobox-select-trigger {
    border: 2px solid #707070;
}

.aria-combobox-select-wrapper .aria-combobox-select-listbox li {
    padding: 5px 10px;
    text-align: left;
    color: #555555;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin: 10px;
    cursor: pointer;
}

    .aria-combobox-select-wrapper .aria-combobox-select-listbox li:hover,
    .aria-combobox-select-listbox li.custom-highlight {
        background-color: #E6E6E6;
        border-radius: 3px;
    }

.aria-combobox-select-wrapper .aria-combobox-select-trigger[aria-expanded=true] {
    border-bottom: 0;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
}

    .aria-combobox-select-wrapper .aria-combobox-select-trigger[aria-expanded=true] ~ .aria-combobox-select-listbox {
        border-top: 0;
    }


/*End - Billing Date - dropdown*/

.list-separator-gray2 > li:not(:last-of-type) {
    border-bottom: 1px solid #e1e1e1;
}
.list-separator-gray2-2px > li:not(:last-of-type) {
    border-bottom: 2px solid #e1e1e1;
}

.bill-and-payment-services {
    margin-right: -15px;
}



/*Start Bar Graph*/
.barcontainer {
    position: relative;
    border-radius: 5px 5px 0 0;
    margin: 0 -30px;
    min-height: 100px;
    max-height: 297px;
    min-width: 300px;
    max-width: none;
    width: calc(100% + 60px);
    z-index: 1;
    margin-bottom: 30px;
    width: 100%;
}

.bar-history {
    display: inline-block;
    bottom: 0;
    background: transparent;
    border-radius: 5px 5px 0 0;
    max-width: 45px;
    width: 100%;
    text-align: center;
    color: white;
    flex-grow: 1;
    flex-shrink: 1;
    margin: 0 30px;
    left: 0 !important;
    height: 88px;
}

.bar-history-inner {
    background: #00549A;
    border-radius: 5px 5px 0 0;
    min-height: 2px;
    max-height: 88px;
    position: absolute;
    bottom: 0;
}

.bar-history:nth-last-child(n+7),
.bar-history:nth-last-child(n+7) ~ .bar-history {
    margin: 0 10px;
}

.bar-history:hover .bar-history-inner {
    background: #003778;
}

.bar-history:hover .barlabel, .bar-history:focus .barlabel {
    font-weight: bold;
}

.barlabel {
    position: absolute;
    bottom: -12px;
    padding-top: 0;
    top: auto;
    width: 100%;
    color: #555555;
    transform: translateY(100%)
}

/*End Bar graph*/

/*Bar Graph Tooltip*/
.bar-tooltip .tooltip,
.bar-tooltip-div.tooltip {
    width: 133px;
}

    .bar-tooltip .tooltip-inner,
    .bar-tooltip-div.tooltip .tooltip-inner {
        max-width: 133px;
        padding: 15px 20px;
        box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
        border: solid 1px #E1E1E1;
    }

    .bar-tooltip .tooltip.bs-tooltip-top .arrow,
    .bar-tooltip-div.tooltip.bs-tooltip-top .arrow,
    .tooltip.bs-tooltip-auto[x-placement^=top] .arrow,
    .bar-tooltip-div.tooltip.bs-tooltip-auto[x-placement^=top] .arrow {
        left: -28px;
    }

        .bar-tooltip .tooltip.bs-tooltip-top .arrow::before,
        .bar-tooltip-div.tooltip.bs-tooltip-top .arrow::before {
            border-width: 15px 20px 0;
        }
/*Bar Graph tooltip*/

/*Download Multiple Bills dialog - start*/
ul.multiple-bill-dates-list {
    columns: 2;
    -webkit-columns: 2;
    -moz-columns: 2;
    width: 76%;
}

    ul.multiple-bill-dates-list li {
        -webkit-transform: translateZ(0);
        -moz-transform: translateZ(0);
        -ms-transform: translateZ(0);
        -o-transform: translateZ(0);
        transform: translateZ(0);
    }
/*Download Multiple Bills dialog - end*/

.bill-tile-icon {
    padding: 22px;
}

/*grid pbe tooltip*/
.grid-pbe-tooltip thead tr {
    margin-bottom: 0px;
}

.grid-pbe-tooltip thead tr {
    min-height: 0px;
}

.grid-pbe-tooltip tbody {
    display: table-row-group !important;
}

.grid-pbe-tooltip thead {
    display: table-header-group !important;
}

.grid-pbe-tooltip table tr {
    display: table-row !important;
}

.grid-pbe-tooltip tr td {
    display: table-cell !important;
}

.grid-pbe-tooltip tr > td,
.grid-pbe-tooltip tr > th:first-child,
.grid-pbe-tooltip tr > td:first-child,
.grid-pbe-tooltip tr > td:nth-child(2),
.grid-pbe-tooltip tr > th:last-child:not(:first-child),
.grid-pbe-tooltip tr > td:last-child:not(:first-child) {
    width: auto !important;
    text-align: right;
}

    .grid-pbe-tooltip tr > td:first-child {
        text-align: left;
    }

.grid-pbe-tooltip .tooltip-inner {
    padding: 25px;
}

.grid-pbe-tooltip .table-bordered,
.grid-pbe-tooltip .table-bordered td,
.grid-pbe-tooltip .table-bordered th {
    border: 1px solid #dddddd;
}

.grid-pbe-tooltip tbody td:first-child {
    color: #111111;
}

.grid-pbe-tooltip-content {
    margin-right: 10px;
}

/* Summary Changes */
.summary-changes-header {
    max-width: 604px;
    width: 100%;
}

.summary-changes-timeline-list {
    max-width: 762px;
    width: 100%;
}

.summary-changes-timeline-custom-line-wrap {
    min-width: 19px;
}

.summary-changes-timeline-date-wrap {
    min-width: 81px;
    max-width: 81px;
    padding-right: 5px;
    position: relative;
}

    .summary-changes-timeline-date-wrap .summary-changes-timeline-date {
        position: absolute;
        right: 5px;
    }

.summary-changes-timeline-custom-bullet {
    width: 19px;
    height: 19px;
    border: 2px solid #f4f4f4;
    top: 31.5px;
}

.summary-changes-timeline-custom-line {
    width: 3px;
    height: 100%;
    background-color: #00549A;
    z-index: -1;
    margin-left: 8px;
}

.summary-changes-timeline-content-wrap {
    flex-grow: 1;
    width: 100%;
    margin-left: 10px;
}

    .summary-changes-timeline-content-wrap .summary-changes-timeline-content-arrow {
        position: relative;
        background: #fff;
    }

        .summary-changes-timeline-content-wrap .summary-changes-timeline-content-arrow:after {
            content: "";
            position: absolute;
            border-bottom: 1px solid #e1e1e1;
            border-right: 1px solid #e1e1e1;
            width: 21px;
            height: 21px;
            background-color: #ffffff;
            transform: rotate(135deg);
            left: 5px;
        }

.summary-changes-timeline-content-arrow:before {
    content: '';
    top: 0;
    left: -29px;
    position: absolute;
    width: 19px;
    height: 19px;
    border: 2px solid #f4f4f4;
    background-color: #00549a;
    border-radius: 50%;
}

.summary-changes-timeline-content-inner,
.summary-changes-timeline-content-wrap .summary-changes-timeline-date {
    margin-left: 15px;
}

.summary-changes-timeline-load-more-container {
    background: linear-gradient(180deg, rgba(238,238,238,0) 0%, rgba(255,255,255,0.99) 99.05%, #FFFFFF 100%);
    position: absolute;
    bottom: 0;
    left: 0;
}

.summary-changes-timeline-load-more-bg {
    background: linear-gradient(180deg, rgba(238,238,238,0) 0%, rgba(255,255,255,0.99) 99.05%, #FFFFFF 100%);
    width: 100%;
    height: 150px;
}

.summary-changes-timeline-content-lob ~ .summary-changes-timeline-content-lob {
    margin-top: 30px;
}

.summary-changes-timeline-content-lob > div > p:last-child,
.summary-changes-timeline-content-lob > p:last-child {
    margin-bottom: 0px;
}

.promotion-tag {
    font-size: 10px;
    line-height: 14px;
    padding: 3px 8px;
    border: 1px solid #555555;
    border-radius: 2px;
    background-color: #FFFFFF;
    text-align: center;
    white-space: nowrap;
}

/*Filter by*/
.border-bottom-gray {
    border-bottom: 1px solid #D4D4D4;
}

.filter-category {
    border-radius: 15px;
    padding-left: 15px;
    padding-top: 8px;
    padding-right: 16px;
    padding-bottom: 8px;
    margin-right: 10px;
    background-color: #FFFFFF;
    border: 1px solid #949596;
}

    .filter-category.selected {
        background-color: #003778;
        border: 1px solid #D4D4D4;
        color: #FFFFFF;
    }

    .filter-category[aria-expanded="true"] {
        background-color: #003778;
        border: 1px solid #D4D4D4;
        color: #FFFFFF;
    }

    .filter-category:hover {
        background: #00549A;
        color: #FFFFFF;
    }

        .filter-category:hover span,
        .filter-category.selected span {
            color: #FFFFFF;
        }

    .filter-category[aria-expanded="true"] span {
        color: #FFFFFF;
    }

.filter-applied {
    border-radius: 15px;
    padding-left: 15px;
    padding-right: 10px;
    padding-top: 5px;
    padding-bottom: 5px;
    border: 1px solid #D4D4D4;
    background: #003778;
    cursor: pointer;
}

.filter-close-button {
    border-radius: 50px;
    height: 16px;
    width: 16px;
    position: relative;
}

.filter-applied:hover {
    background: #00549A;
    color: #FFFFFF;
}

    .filter-applied:hover .filter-close-button {
        background: #00549A;
    }

.select-filter .graphical_ctrl {
    padding-left: 50px;
    padding-right: 15px;
}

    .select-filter .graphical_ctrl:hover {
        background: #E1E1E1;
    }

    .select-filter .graphical_ctrl input:checked ~ span:not(.ctrl_element) {
        font-weight: bold;
    }

    .select-filter .graphical_ctrl .ctrl_element {
        left: 15px;
    }

.form-control-sort-by-selection .form-control-select {
    padding-left: 15px;
    padding-top: 13px;
    padding-bottom: 12px;
    padding-right: 30px;
    border: 2px solid #949596;
    font-size: 14px;
}

    .form-control-sort-by-selection .form-control-select + span {
        padding-right: 0px;
    }

.filter-accordion[aria-expanded="true"] ~ div.border-bottom-gray {
    display: none;
}

.select-filter .graphical_ctrl_checkbox input:disabled ~ .ctrl_element:after {
    border-color: #e6e6e6;
}

.filter-applied, .clear-applied-filter {
    margin-bottom: 30px;
}
/*Filter by*/

/*Proactive tour dialog - START*/
.slider-rotating-carousel-buttons {
    height: 34px;
    border-radius: 16px;
    background-color: #fff;
    list-style: none;
    display: inline-flex;
    flex-direction: row;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.2);
    margin: 0;
    margin-right: 68px;
    padding: 4px 2px;
}

.slider-rotating-carousel-button {
    position: relative;
    border-radius: 50%;
    border: none;
    height: 26px;
    width: 26px;
    background-color: #fff;
    color: #003778;
    font-size: 14px;
    line-height: 16px;
    text-align: center;
    margin: 0 2px;
    padding: 0;
    display: block;
}

    .slider-rotating-carousel-button:hover {
        text-decoration: none;
        cursor: pointer;
    }

.slider-rotating-carousel-pause:hover,
#slider-rotating-carousel-component :hover,
.slider-rotating-carousel-pause-bg:hover,
.slider-rotating-carousel-pause-bg::before:hover,
.slider-rotating-carousel-pause-bg::before:hover {
    cursor: pointer;
}

.slider-rotating-carousel-button > * {
    pointer-events: none;
}

.slider-rotating-carousel-buttons > li.slick-active > .slider-rotating-carousel-button {
    color: #fff;
    background-color: #00549A;
}

.slider-rotating-carousel-pause {
    height: 38px;
    width: 38px;
    border-radius: 50%;
    border: none;
    background-color: transparent;
    position: absolute;
    bottom: 0px;
    z-index: 10;
    padding: 0;
    cursor: pointer;
    transform: translate(-50%);
}

.slider-rotating-carousel-pause-bg {
    height: 34px;
    width: 34px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    border-radius: 50%;
    background-color: #FFF;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.2);
    pointer-events: none;
}

svg.slider-rotating-carousel-progress {
    overflow: visible;
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
    transform: rotate(-90deg);
}

    svg.slider-rotating-carousel-progress circle.slider-rotating-carousel-progress_initial {
        display: none;
    }

    svg.slider-rotating-carousel-progress circle {
        stroke: #0075FF;
        stroke-width: 3px;
        stroke-dasharray: 125;
        stroke-dashoffset: 0;
        fill: rgba(225,255,255,0);
    }

.slider-rotating-carousel-pause.state-paused:not(state-paused) .img-stated-play,
.slider-rotating-carousel-pause.state-paused .img-stated-paused {
    display: block;
}

.slider-rotating-carousel-pause:not(state-paused) .img-stated-play,
.slider-rotating-carousel-pause.state-paused .img-stated-paused {
    display: none;
}

.bill-redesign-accss .bill-redesign button.slider-rotating-carousel-button:focus {
    outline-offset: 2px;
}

.slider-banner-container-details {
    max-width: 500px;
    margin: 0 auto;
    padding-bottom: 15px;
}

.slider-banner-img {
    width: 100%;
    height: auto;
}


.bill-redesign .reactive-tour-dialog .reactive-tour-content video::-webkit-media-controls-play-button:focus,
.bill-redesign .proactive-tour-dialog .proactive-tour-carousel-container video::-webkit-media-controls-play-button:focus {
    outline: 2px #0075FF solid !important;
    outline-offset: -4px;
}

.bill-redesign .reactive-tour-dialog .reactive-tour-content video::-webkit-media-controls-mute-button:focus,
.bill-redesign .proactive-tour-dialog .proactive-tour-carousel-container video::-webkit-media-controls-mute-button:focus {
    outline: 2px #0075FF solid !important;
    outline-offset: -4px;
}

.bill-redesign .reactive-tour-dialog .reactive-tour-content video::-webkit-media-controls-fullscreen-button:focus,
.bill-redesign .proactive-tour-dialog .proactive-tour-carousel-container video::-webkit-media-controls-fullscreen-button:focus {
    outline: 2px #0075FF solid !important;
    outline-offset: -4px;
}

.bill-redesign .reactive-tour-dialog .reactive-tour-content video::-webkit-media-controls-timeline:focus,
.bill-redesign .proactive-tour-carousel-container video::-webkit-media-controls-timeline:focus {
    outline: 2px #0075FF solid !important;
}

.bill-redesign .reactive-tour-dialog .reactive-tour-content video::-webkit-media-controls-timeline,
.bill-redesign .proactive-tour-carousel-container video::-webkit-media-controls-timeline {
    margin-bottom: 18px;
    padding-bottom: 2px;
    padding-top: 2px;
    outline-offset: 3px;
}

.bill-redesign .reactive-tour-dialog .reactive-tour-content video::-webkit-media-controls-volume-slider:focus,
.bill-redesign .proactive-tour-dialog .proactive-tour-carousel-container video::-webkit-media-controls-volume-slider:focus {
    outline: 2px #0075FF solid !important;
    outline-offset: -2px;
}

.proactive-tour-dialog .modal-dialog,
.reactive-tour-dialog .modal-dialog {
    max-width: 960px;
    width: 100%;
}

.reactive-tour-content.active-tour-content img {
    object-fit: cover;
    object-position: center;
    width: 100%;
}

.reactive-tour-dialog-navigation {
    position: relative;
    transform-style: preserve-3d;
    height: 60px;
}

    .reactive-tour-dialog-navigation a,
    .reactive-tour-dialog-navigation span {
        color: #00549A;
        font-size: 14px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 18px;
    }

    .reactive-tour-dialog-navigation::before {
        content: "";
        position: absolute;
        inset: 0px;
        transform: translate3d(0, 0px, -1px);
        background: rgb(0,0,0);
        background: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,.7) 100%);
        filter: blur(10px);
    }

.reactive-tour-dialog-navigation-content {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 60px;
    padding: 21px 0;
}


.reactive-tour-container .reactive-tour-content.active-tour-content {
    display: none;
}

    .reactive-tour-container .reactive-tour-content.active-tour-content.active {
        display: block;
    }

.reactive-tour-container .reactive-tour-content.active-tour-content {
    display: none;
}

.tour-intro-dialog .tour-intro-banner {
    width: 420px;
}

    .tour-intro-dialog .tour-intro-banner img{
        object-fit: contain;
    }

.tour-intro-dialog .tour-intro-content .tour-intro-content-text {
    max-width: 452px;
}

/*Proactive tour dialog - END*/

button.tour-next-tab.disabled,
button.tour-next-tab.disabled:hover,
button.tour-next-tab.disabled:focus {
    pointer-events: auto;
    background-color: #CCCCCC;
    border: 2px solid #CCCCCC;
    opacity: 1;
}

/*Bill Tour Location*/
.ds-bill-tour-loc-container {
    background: none repeat scroll #003778;
    box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
    position: fixed;
    left: calc(100% - 40px);
    z-index: 49;
    border-radius: 10px 0 0 10px;
}

.ds-bill-tour-label {
    text-align: center;
    overflow: hidden;
    position: relative;
    transform: rotate(-90deg);
    transform-origin: top left;
    bottom: -1px;
    line-height: 18px;
    font-size: 14px;
}

.bill-tour-tooltip {
    padding: 12px;
    border-radius: 10px 0 0 10px;
}

    .bill-tour-tooltip .tooltip {
        margin-right: 0;
    }

.bill-redesign-accss .bill-redesign .ds-bill-tour a.bill-tour-tooltip:focus {
    outline: 0px !important;
    border: 0px !important;
}

.bill-redesign-accss .bill-redesign .ds-bill-tour a.bill-tour-tooltip::after {
    content: '';
    position: absolute;
    display: block;
    width: 18px;
    height: calc(100% - 20px);
    bottom: 10px;
    left: 11px;
    pointer-events: auto;
    cursor: pointer;
}

.bill-redesign-accss .bill-redesign .ds-bill-tour a.bill-tour-tooltip:focus::after {
    outline: 2px solid #ffffff;
    outline-offset: 2px;
    border-radius: 2px;
}

.bill-tour-tooltip .tooltip-inner {
    padding: 25px;
}

.bill-tour-tooltip .tooltip.bs-tooltip-left .arrow::before {
    border-width: 18px 0 18px 18px;
}

.bill-tour-tooltip .btn-primary:focus,
.bill-tour-tooltip .btn-default:focus {
    padding: 7px 30px;
}


/*Tips Text Tag*/
.tips-text-tag-container + .tips-text-tag-container {
    margin-top: 10px;
}

.tips-text-tag {
    background: #00549A;
    border-radius: 2px;
    font-size: 10px;
    line-height: 14px;
    color: #FFFFFF;
    padding: 2px 4px;
    position: relative;
    margin-right: 10px;
}

    .tips-text-tag::after {
        content: '';
        transform: translateY(-50%);
        position: absolute;
        top: 50%;
        right: -8px;
        border-top: 3.5px solid transparent;
        border-left: 4px solid #00549A;
        border-right: 4px solid transparent;
        border-bottom: 3.5px solid transparent;
    }

.tips-tag.container {
    padding-left: 30px;
    padding-right: 30px;
    padding-top: 20px;
    padding-bottom: 20px;
}

.billing-treegrid-row-misc div + .tips-text-tag-container {
    margin-top: 10px;
    padding-right: 30px;
}

.billing-services-mob-list div + .tips-text-tag-container {
    margin-top: 5px;
    padding-right: 10px;
}

.tips-icon-img {
    margin-right: 7px;
}

.tips-text-tag-in-grid.tips-text-tag-in-grid {
    margin-left: -24px;
}
/*Offer Tile*/

.exclusive-internet-o {
    color: #000000;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 18px;
}

.account-number-b-1-ys {
    color: #555555;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 14px;
}

.offer-tile {
    box-shadow: 0 6px 24px 0 rgba(0,0,0,0.14);
}

.offer-tile-header {
    color: #000000;
    font-size: 20px;
    font-weight: 900;
    letter-spacing: -0.33px;
    line-height: 22px;
}

.offer-tile-text {
    font-size: 13px;
    line-height: 17px;
    letter-spacing: -0.22px;
}

.offer-tile .btn {
    padding: 8px 15px;
    font-size: 15px;
    line-height: 17px;
    letter-spacing: -0.25px;
    border-radius: 17.5px;
    display: flex;
    align-items: center;
}

.proposed-tile-img-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.offer-tile-img-container {
    padding-left: 30px;
    padding-right: 30px;
    min-width: 233px;
}

.offer-tile-content-container {
    padding-bottom: 30px;
    padding-left: 30px;
    padding-right: 30px;
}

.offer-tile-custom {
    max-width: 301px;
}

    .offer-tile-custom .offer-tile-img-container {
        margin-bottom: -11px;
    }

.offer-tiles-modal {
    padding-right: 0 !important;
}

.offer-tiles-modal {
    padding-right: 0 !important;
}

    .offer-tiles-modal .modal-dialog .modal-content .modal-header {
        background: none;
        padding: 0;
        height: auto;
    }

        .offer-tiles-modal .modal-dialog .modal-content .modal-header .close {
            margin: 0;
            padding: 0;
        }

    .offer-tiles-modal .modal-dialog .offer-tile-img-container {
        margin-top: -50px;
        padding-left: 0;
        padding-right: 0;
        width: 477px;
    }

    .offer-tiles-modal .modal-dialog .offer-tile-content-container {
        padding: 0;
        width: 370px;
        flex: 1;
    }

    .offer-tiles-modal.tile-v2 .modal-dialog .offer-tile-content-container {
        padding: 0;
        padding-left: 15px;
        width: 370px;
        flex: 1;
    }

    .offer-tiles-modal .modal-dialog .offer-tile-content-container .exclusive-offer {
        color: #000000;
        font-size: 14px;
        letter-spacing: 0;
        line-height: 18px;
    }

    .offer-tiles-modal .modal-dialog .offer-tile-content-container .account-number-b-1-ys {
        opacity: 0.7;
        color: #555555;
        font-size: 12px;
        letter-spacing: -0.07px;
        line-height: 14px;
    }

    .offer-tiles-modal .modal-dialog .offer-tile-content-container .offer-tile-header {
        color: #111111;
        font-size: 24px;
        font-weight: 900;
        letter-spacing: -0.4px;
        line-height: 26px;
    }

    .offer-tiles-modal .modal-dialog .offer-tile-content-container .offer-text {
        opacity: 0.7;
        color: #111111;
        font-size: 14px;
        letter-spacing: -0.23px;
        line-height: 18px;
    }

    .offer-tiles-modal .modal-dialog .offer-tile-content-container .offer-note {
        color: #555555;
        font-size: 12px;
        letter-spacing: 0;
        line-height: 14px;
    }

    .offer-tiles-modal .offer-tile-custom-disclosure-trigger[aria-expanded="true"] span.custom-collapsed-text,
    .offer-tiles-modal .offer-tile-custom-disclosure-trigger[aria-expanded="false"] span.custom-expanded-text {
        display: none;
    }

.icon-bill-redesign.icon-bill-redesign.bill-tile-icon.icon-get-started {
    font-size: 30px;
}

.icon-bill-redesign.icon-bill-redesign.bill-tile-icon.icon-pencil-icon {
    font-size: 30px;
}

.icon-bill-redesign.icon-bill-redesign.bill-tile-icon.icon-mobile {
    font-size: 40px;
}

.icon-bill-redesign.icon-bill-redesign.bill-tile-icon.icon-magnifying-glass {
    font-size: 32px;
}

.icon-bill-redesign.icon-bill-redesign.bill-tile-icon.icon-XX_auto_pay1 {
    font-size: 40px;
}
.icon-bill-redesign.icon-bill-redesign.bill-tile-icon.icon-user_profile {
    font-size: 36px;
}

.icon-bill-redesign.icon-bill-redesign.bill-tile-icon.icon-consolidate-your-bills {
    font-size: 32px;
}

.icon-bill-redesign.icon-bill-redesign.bill-tile-icon.icon-heart {
    font-size: 38px;
}

.icon-bill-redesign.icon-bill-redesign.bill-tile-icon.icon-network {
    font-size: 50px;
}

.icon-bill-redesign.icon-bill-redesign.bill-tile-icon.icon-bell_lets_talk {
    font-size: 40px;
}

.icon-bill-redesign.icon-bill-redesign.bill-tile-icon.icon-ebill {
    font-size: 45px;
}

.icon-bill-redesign.icon-bill-redesign.bill-tile-icon.icon-mobile {
    font-size: 40px;
}

.icon-bill-redesign.icon-bill-redesign.bill-tile-icon.icon-consolidate-your-bills {
    font-size: 35px;
}

.payment-summary-trigger[aria-expanded="false"] ~ .payment-summary-trigger-detail .img-expanded,
.message-trigger[aria-expanded="false"] .img-expanded,
.important-message-trigger[aria-expanded="false"] .img-expanded,
.payment-summary-trigger[aria-expanded="true"] ~ .payment-summary-trigger-detail .img-collapsed,
.message-trigger[aria-expanded="true"] .img-collapsed,
.important-message-trigger[aria-expanded="true"] .img-collapsed {
    display: none;
}

.bill-redesign-pay-your-bill-dialog .modal-content .modal-header > div > .close {
    outline-offset: -4px;

}

    .bill-redesign-pay-your-bill-dialog .modal-content .modal-header > div > .close:only-child {
        margin-left: auto;
    }

.pay-your-bill-warning-dialog .modal-header {
    background-color: #ffffff;
    border: 0;
    height: 60px;
    align-items: flex-end !important;
}

.pay-your-bill-warning-dialog-banner {
    margin-top: -30px;
}

.value-footer-services li:last-child {
    margin-bottom: 0;
}

.billing-treegrid-row-ads .PersonalizationTilesContainer {
    display: flex;
}

    .billing-treegrid-row-ads .PersonalizationTilesContainer > div:not(:only-child) > div {
        margin-right: 30px;
    }

    .billing-treegrid-row-ads .PersonalizationTilesContainer > div:last-child > div {
        margin-right: 0px;
    }

.billing-treegrid .billing-treegrid-row-misc td .PersonalizationTilesContainer > div,
.summary-changes-timeline-container .summary-changes-timeline-content-inner .PersonalizationTilesContainer > div {
    margin: 0;
    padding: 0;
    height: 0;
}

.billing-treegrid-row-misc .PersonalizationTilesContainer:empty {
    margin: 0;
    padding: 0;
}

/*Start - Delta - PBE*/
.pbe-inner-scrollable .bg-stripe-gray-8D {
    background-image: repeating-linear-gradient(135deg, #8D8D8D 0, #8D8D8D 1px, #BABEC2 0, #BABEC2 50%);
    background-size: 10px 10px;
}

.pbe-inner-scrollable .bgMediumGray {
    background-color: #555;
}

.pbe-inner-scrollable {
    overflow-x: hidden;
    overflow-y: hidden;
}

    .pbe-inner-scrollable::-webkit-scrollbar {
        border-radius: 3px;
        background: #E1E1E1;
        height: 6px;
    }

    .pbe-inner-scrollable::-webkit-scrollbar-thumb:horizontal {
        background: #00549A;
        border-radius: 3px;
    }

    .pbe-inner-scrollable .bars-container:not(.single-bar) + .labels-container .label-group-bottom {
        margin-top: 33px;
    }

.pbe-inner-scrollable .split-divider-label {
    min-width: 100px;
}

        .pbe-inner-scrollable .split-divider-label.left {
            text-align: left;
            padding-right: 25px;
        }

        .pbe-inner-scrollable .split-divider-label.right {
            text-align: right;
            padding-left: 25px;
        }

    .pbe-inner-scrollable .split-origin-label {
        padding-top: 0;
        z-index: 1;
        text-align: left;
    }

        .pbe-inner-scrollable .split-origin-label:only-child {
            position: relative;
        }

    .pbe-inner-scrollable .label-indicator-line-origin {
        height: 36px;
        top: 0;
    }

        .pbe-inner-scrollable .label-indicator-line-origin + div {
            padding-left: 0;
            padding-top: 5px;
        }

    .pbe-inner-scrollable .split-right-label {
        left: 0;
        text-align: right;
    }

        .pbe-inner-scrollable .split-right-label:not(.flex-column) {
            text-align: right !important;
        }

            .pbe-inner-scrollable .split-right-label:not(.flex-column) > .label-indicator-line-bottom,
            .pbe-inner-scrollable .split-right-label:not(.flex-column) > .label-indicator-line-top {
                margin: 0;
                bottom: 0;
            }

    .pbe-inner-scrollable .label-group-top .split-right-label:not(.flex-column) > div:not(.label-indicator-line-top, .label-indicator-line-bottom) {
        padding-right: 10px;
        padding-bottom: 31px;
    }

    .pbe-inner-scrollable .label-group-bottom .split-right-label:not(.flex-column) > div:not(.label-indicator-line-top, .label-indicator-line-bottom) {
        padding-right: 10px;
        padding-top: 31px;
    }

    .pbe-inner-scrollable .label-indicator-line-end::after,
    .pbe-inner-scrollable .label-indicator-line-origin::after {
        content: '';
        position: absolute;
        left: 50%;
        height: 7px;
        width: 7px;
        border-radius: 50%;
        background-color: #949596;
    }

    .pbe-inner-scrollable .label-indicator-line-end::after,
    .pbe-inner-scrollable .label-indicator-line-origin::after {
        top: 0;
        transform: translate(-50%, -50%);
    }
    /* left/center label start */
    .pbe-inner-scrollable .split-center-label,
    .pbe-inner-scrollable .split-left-label {
        position: relative;
        z-index: 2;
        padding-right: 25px;
        text-align: left;
    }
        .pbe-inner-scrollable .split-left-label.pos-absolute{
            position:absolute;
        }


        .pbe-inner-scrollable .split-center-label .label-indicator-line-top > div,
        .pbe-inner-scrollable .split-center-label .label-indicator-line-bottom > div,
        .pbe-inner-scrollable .split-left-label:only-child .label-indicator-line-top .same-height,
        .pbe-inner-scrollable .split-left-label:only-child .label-indicator-line-bottom .same-height {
            min-height: 48px;
        }

        .pbe-inner-scrollable .split-left-label:not(.flex-column) .label-indicator-line-top {
            margin: 0;
        }

        .pbe-inner-scrollable .split-left-label:not(.flex-column) > div:not(.label-indicator-line-top) {
            padding-left: 10px;
        }
    /* left/center label end */
    /* center label additional styles start */
    .pbe-inner-scrollable .split-center-label {
        position: absolute;
        padding-bottom: 7.5px;
    }
        .pbe-inner-scrollable .split-center-label.right {
            padding-right: 0;
            padding-left: 25px;
            text-align: right;
        }
    /* end center label */
    /* end label start */
    .pbe-inner-scrollable .split-end-label {
        padding-bottom: 7.5px;
        padding-left: 10px;
        position: absolute;
        z-index: 1;
        text-align: right;
    }
        .pbe-inner-scrollable .split-end-label:only-child {
            position: relative;
        }

    .pbe-inner-scrollable .label-indicator-line-end {
        height: 36px;
        width: 1px;
        background: #949596;
        position: relative;
        flex-grow: 0;
        align-self: flex-end;
    }

        .pbe-inner-scrollable .label-indicator-line-end + div {
            padding-top: 5px;
        }
/* end label end */
.ds-pbe-modal-late-payment-charge-lg .modal-dialog {
    width: 100%;
    max-width: none;
}

    .ds-pbe-modal-late-payment-charge-lg .modal-dialog .modal-content {
        border-radius: 10px 10px 0 0;
    }

.ds-pbe-modal-late-payment-charge-lg .modal-content {
    max-width: 1200px;
    width: 100%;
    margin: auto;
}

.late-payment-charge-tip-wrap {
    max-width: 810px;
}

.late-payment-charge-tip-icon-img {
    width: 36px;
    height: auto;
}

.overage-graph-details-wrao {
    margin-top: 37px;
}

.overage-graph-details-indicator {
    width: 30px;
    height: 1px;
    background-color: #949596;
    margin-top: 12px;
    margin-right: 10.5px;
    margin-left: -4px;
    position: relative;
    display: flex;
    align-items: center;
}

    .overage-graph-details-indicator:before {
        content: '';
        position: absolute;
        left: -4px;
        height: 8px;
        width: 8px;
        border-radius: 50%;
        background-color: #949596;
    }

.overage-table {
    table-layout: fixed;
}

    .overage-table td,
    .overage-table th {
        padding: 0;
        margin: 0;
    }

        .overage-table td > div,
        .overage-table th > div {
            padding-top: 7px;
            padding-bottom: 7px;
            padding-left: 8.5px;
            padding-right: 20px;
            min-height: 50px;
        }

        .overage-table td:first-child > div,
        .overage-table th:first-child > div {
            padding-left: 20px;
            padding-right: 8.5px;
        }

.pbe-last-payment-charge-container .pbe-col.col-4 {
    flex: 1;
    margin-right: 8px;
}

.pbe-last-payment-charge-container .pbe-col.col-4:nth-child(3){
    margin-right: 0;
}
/*END - Delta - PBE*/

.bill-redesign-accss .icon-link-decoration-none,
.bill-redesign-accss .icon-link-decoration-none::before {
    display: inline-block;
    text-decoration: none;
}

@media (max-width: 767.98px) {
    .bill-redesign .big-price {
        font-size: 30px;
        line-height: normal;
    }

        .bill-redesign .big-price span {
            font-size: 14px;
            line-height: 22px;
            letter-spacing: -0.35px;
            vertical-align: top;
            top: 4px;
        }

            .bill-redesign .big-price span:last-of-type {
                margin-left: 4px;
            }

    .billing-treegrid-row-mob-total .big-price span:last-of-type {
        margin-left: 2px;
    }

    .dimension-60-xs {
        width: 60px;
        height: 60px;
    }

    .txtSize13-xs {
        font-size: 13px;
    }

    .txtSize22-xs {
        font-size: 22px;
    }

    .txtSize22-xs-force {
        font-size: 22px !important;
    }

    .txtSize25-xs {
        font-size: 27px;
    }

    .txtSize28-xs {
        font-size: 28px;
    }

    .txtSize35-xs {
        font-size: 35px;
    }

    .txtSize38-xs {
        font-size: 38px;
    }

    .txtSize72-xs {
        font-size: 72px;
    }

    .margin-r-xs-13 {
        margin-right: 13px;
    }

    .margin-r-xs-75 {
        margin-right: 75px;
    }

    .pad-t-xs-7 {
        padding-top: 7px;
    }

    .pad-t-xs-18 {
        padding-top: 18px;
    }

    .margin-top-neg-10-xs {
        margin-top: -10px;
    }

    .margin-t-xs-18 {
        margin-top: 18px;
    }

    .margin-t-xs-22 {
        margin-top: 22px;
    }

    .margin-b-xs-7p5 {
        margin-bottom: 7.5px;
    }

    .margin-b-xs-8 {
        margin-bottom: 8px;
    }

    .pad-b-xs-7p5 {
        padding-bottom: 7.5px;
    }

    .pad-b-xs-13 {
        padding-bottom: 13px;
    }

    .pad-v-xs-0-force {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }

    .pad-h-xs-0-force {
        padding-left: 0 !important;
        padding-right: 0 !important
    }

    .max-width-xs-260 {
        max-width: 260px;
    }

    .max-width-xs-none {
        max-width: none !important;
    }

    .max-height-xs-218 {
        max-height: 218px;
    }

    .bill-redesign-accss .margin-left-neg-5-xs {
        margin-left: -5px;
    }

    .bill-redesign-accss .d-xs-none {
        display: none;
    }

    .flex-direction-column-xs-force {
        flex-direction: column !important;
    }

    .payment-bill-summary-content {
        background: #FFFFFF;
    }
    /*Hierarchy Design in Accordion*/
    .vertical-bullet {
        margin-left: 5px;
        border-left: 1px solid lightGray;
        height: 48px;
    }

    li:last-child > div > div > span.vertical-bullet {
        height: 10px;
    }

    .horizontal-bullet {
        border-bottom: 1px solid lightGray;
        width: 10px;
        margin-bottom: 36px;
    }

    .height-vertical-end {
        height: 15px;
    }


    .bill-redesign .form-control-select + span {
        right: 10px;
        top: 3px;
    }
    /*Hierarchy Design in Accordion*/

    .billing-services-print-modal .modal-dialog,
    .billing-services-download-modal .modal-dialog {
        position: relative;
    }
    /*Modal design in tooltip*/
    .billing-tooltip-info .modal.modal-tooltip .tooltip-dialog {
        margin: auto 10px;
    }

        .billing-tooltip-info .modal.modal-tooltip .tooltip-dialog .close {
            padding: 11px;
        }

    .bill-history-info .modal.modal-tooltip .tooltip-dialog {
        margin: auto 45px;
    }

        .bill-history-info .modal.modal-tooltip .tooltip-dialog .close {
            padding: 10px;
        }
    /*Start Bar Graph*/
    .barcontainer {
        min-height: 145px;
        min-width: 180px;
        max-width: 270px;
        overflow-x: auto;
        overflow-y: hidden;
        padding-bottom: 40px;
        margin-bottom: 0px;
    }

        .barcontainer::-webkit-scrollbar {
            border-radius: 10px;
            background: #d7d7d7;
            height: 10px;
            width: 20px;
        }

        .barcontainer::-webkit-scrollbar-thumb:horizontal {
            background: #003778;
            border-radius: 10px;
        }
    /*.barcontainer::-webkit-scrollbar-thumb:hover {
           background: #003778;
       }*/

    .bar-history {
        min-width: 20px;
        max-width: 30px;
        margin: 0 10px;
    }

        .bar-history:nth-last-child(n+6),
        .bar-history:nth-last-child(n+6) ~ .bar-history {
            margin: 0 7.5px;
        }

    /*End Bar Graph*/

    /* Card Carousel Start */
    .tile-carousel {
        padding: 0 0;
    }

        .tile-carousel.slick-initialized {
            margin-left: 0;
            margin-right: 0;
        }

            .tile-carousel.slick-initialized .slick-list .slick-track {
                margin-left: 0;
            }

            .tile-carousel.slick-initialized.centered-mobile-slide .slick-list .slick-track {
                margin-left: -15px;
            }

            .tile-carousel.slick-initialized .slick-slide > div {
                padding-left: 7.5px;
                padding-right: 7.5px;
            }
            /* Card Carousel Start */
            .tile-carousel.slick-initialized .slick-list {
                overflow: visible;
                margin: 0 -30px;
                padding: 0 37.5px;
            }

            .tile-carousel.slick-initialized .slick-slide {
                margin-left: 0;
                margin-right: 0;
            }

                .tile-carousel:not(.slick-initialized) > div,
                .tile-carousel.slick-initialized .slick-slide > div > div {
                    flex: auto;
                }

                .tile-carousel.slick-initialized .slick-slide > div > div {
                    margin-bottom: 0;
                }

                .tile-carousel.slick-initialized .radio-card,
                .tile-carousel.slick-initialized .slick-slide > div > div {
                    width: auto;
                    max-width: none;
                }

        .tile-carousel .slick-dots li button {
            border: 1px solid #555;
            opacity: 1;
            background-color: transparent;
            color: #000;
            font-size: 0;
        }

        .tile-carousel .slick-dots li.slick-active button {
            background: #555;
            background-color: #555;
            color: #fff;
        }

    /* Card Carousel End */

    /*Start Pagination*/
    .bill-pagination li:first-child,
    .bill-pagination li.active,
    .bill-pagination li.active-sibling:nth-last-child(2),
    .bill-pagination li:last-child {
        display: inline-block !important;
    }

        .bill-pagination li:first-child:nth-last-child(n+6) ~ li {
            display: none;
        }

            .bill-pagination li:first-child:nth-last-child(n+6) ~ li:nth-last-child(-n + 3) {
                display: inline-block;
            }

            .bill-pagination li:first-child:nth-last-child(n+6) ~ li:nth-last-child(3):before {
                content: "\2026";
                font-size: 24px;
                display: inline-block;
                margin-right: 2.5px;
            }

        .bill-pagination li:first-child:nth-last-child(n+6).active:before, li:first-child:nth-last-child(n+6) ~ li.active:before {
            content: "\2026";
            font-size: 24px;
            display: inline-block;
            margin-right: 2.5px;
        }

        .bill-pagination li:first-child:nth-last-child(n+6).active:after, li:first-child:nth-last-child(n+6) ~ li.active:after {
            content: "\2026";
            font-size: 24px;
            display: inline-block;
            margin-left: 2.5px;
        }

        .bill-pagination li:first-child:nth-last-child(n+6).active:nth-child(-n + 2):before,
        .bill-pagination li:first-child:nth-last-child(n+6) ~ li.active:nth-child(-n + 2):before,
        .bill-pagination li:first-child:nth-last-child(n+6).active:nth-last-child(-n + 2):before,
        .bill-pagination li:first-child:nth-last-child(n+6) ~ li.active:nth-last-child(-n + 2):before,
        .bill-pagination li:first-child:nth-last-child(n+6).active:nth-child(-n + 2):after,
        .bill-pagination li:first-child:nth-last-child(n+6) ~ li.active:nth-child(-n + 2):after,
        .bill-pagination li:first-child:nth-last-child(n+6).active:nth-last-child(-n + 2):after,
        .bill-pagination li:first-child:nth-last-child(n+6) ~ li.active:nth-last-child(-n + 2):after {
            display: none;
        }

        .bill-pagination li:first-child:nth-last-child(n+6).active ~ li:nth-last-child(-n + 3),
        .bill-pagination li:first-child:nth-last-child(n+6) ~ li.active ~ li:nth-last-child(-n + 3) {
            display: none;
        }

        .bill-pagination li:first-child:nth-last-child(n+6).active ~ li:nth-child(-n + 3),
        .bill-pagination li:first-child:nth-last-child(n+6) ~ li.active ~ li:nth-child(-n + 3) {
            display: inline-block;
        }

        .bill-pagination li:first-child:nth-last-child(n+6).active ~ li:nth-child(-n + 2):after,
        .bill-pagination li:first-child:nth-last-child(n+6) ~ li.active ~ li:nth-child(-n + 2):after {
            display: none;
        }

        .bill-pagination li:first-child:nth-last-child(n+6).active ~ li:nth-child(3):after,
        .bill-pagination li:first-child:nth-last-child(n+6) ~ li.active ~ li:nth-child(3):after {
            content: "\2026";
            font-size: 24px;
            display: inline-block;
            margin-left: 2.5px;
        }
    /*End of Pagination*/

    .payment-list-width {
        width: calc(100% - 104px);
    }

    /*Share Group Modal*/

    .share-group-table tbody tr.share-group-row:not(last-child):before {
        width: calc(100% - 30px);
        left: 15px;
    }

    tr.share-group-row > td:first-child > div {
        margin-left: 15px;
    }

    tr.share-group-row > td:last-child > div {
        margin-right: 15px;
    }

    tr.share-group-row:last-child > td > div {
        border: 0;
    }

    .share-group-table tr > th:nth-child(2),
    .share-group-table tr > td:nth-child(2){
        width: 68px !important;
    }

    .share-group-table tr > th:nth-child(3),
    .share-group-table tr > td:nth-child(3) {
        width: 79px !important;
    }

    .share-group-table tr > th:first-child {
        padding-left: 15px;
    }

    .share-group-table tr > th:last-child {
        padding-right: 15px;
        text-align: right;
    }

    .share-group-footer {
        margin-left: 15px;
    }

    tr > td:last-child > div > span.share-group-footer {
        margin-left: 0px;
        margin-right: 15px;
    }

    /*Share Group Modal*/

    .billing-treegrid-row-chart-container {
        padding-top: 35px;
        padding-bottom: 20px;
    }

    .billing-treegrid-row-chart-bar {
        padding-bottom: 26px;
        padding-right: 0px;
    }

    .billing-treegrid-row-chart-line-row {
        margin-bottom: 23px;
    }

    .billing-treegrid-row-chart-bar-previous {
        height: 70px;
    }

    .billing-treegrid-row-chart-bar-current {
        height: 47px;
    }

    .billing-treegrid-row-chart-bar-previous,
    .billing-treegrid-row-chart-bar-current {
        width: 45px;
    }

    .billing-treegrid-row-chart-bar-current-label {
        margin-left: 0px;
        margin-right: -23px;
        font-size: 12px;
        line-height: 14px;
        min-width: 90px;
        max-width: 90px;
    }

        .billing-treegrid-row-chart-bar-current-label span.icon-bill-redesign {
            margin-bottom: 2px;
        }

    .billing-treegrid-row-chart-bar-wrap {
        right: 40px;
    }

    .billing-treegrid-row-chart-bar-current .billing-treegrid-row-chart-bar-current-label {
        position: absolute;
        right: 0px;
        font-size: 12px;
        line-height: 14px;
        min-width: 90px;
        max-width: 90px;
    }

    .billing-treegrid-row-chart-bar-wrap .billing-treegrid-row-chart-bar-previous {
        position: absolute;
        bottom: 0;
        right: 33.3333333333%;
        margin-right: -15px;
    }

    /*Download Multiple Bills dialog - start*/
    ul.multiple-bill-dates-list {
        columns: 1;
        -webkit-columns: 1;
        -moz-columns: 1;
        width: 100%;
    }
    /*Download Multiple Bills dialog - end*/

    .bill-tile-icon {
        padding: 0px 10px 0px 10px;
    }

    /*header/footer focus outline - start*/
    .bill-redesign-accss .bill-redesign-accss-footer .footer-c-t-a .btn.btn-primary.call-to-action:focus::before {
        height: 100% !important;
        width: 100% !important;
        left: 0 !important;
        outline-offset: 6px !important;
    }

    .bill-redesign-accss .bill-redesign-accss-footer .footer-c-t-a {
        display: flex;
        flex-direction: column;
    }
    /*header/footer focus outline - end*/

    .summary-changes-timeline-custom-bullet  {
        top: 63.5px;
    }

    /*Proactive tour dialog - START*/
    .slider-banner-img {
        height: auto;
    }
    /*Proactive tour dialog - END*/

    /*Bill Tour Location*/
    .ds-bill-tour-loc-container {
        top: 140px;
        z-index: 1101;
    }

    .connector-active .ds-bill-tour-loc-container {
        z-index: 49;
    }

    .bill-tour-tooltip .tooltip {
        max-width: 272px;
        margin-right: 0;
    }

    .bill-tour-tooltip .tooltip-inner {
        max-width: 272px;
    }


    /*Offer Tile*/
    .offer-tile-header {
        padding-top: 15px;
    }

    .offer-tile {
        margin-bottom: 15px;
    }

        .offer-tile:last-child {
            margin-bottom: 0px;
        }

    .offer-tile-img-container {
        padding-top: 25px;
    }

    .offer-tile-custom .offer-tile-img-container {
        padding-top: 0;
    }

    .offer-tile-img-container > img {
        width: 100px;
        height: auto;
    }

    .offer-tiles-modal .modal-dialog .dimension-35 {
        height: 26px;
        width: 26px;
    }

    .offer-tiles-modal .modal-dialog button.close img{
        height: 10.5px;
    }

    .offer-tiles-modal .modal-dialog .offer-tile-img-container {
        margin-top: 25px;
        padding-top: 0;
        width: 100%;
        box-shadow: inset 0 0 30px 0 rgba(0,0,0,0.07);
        text-align: center;
        height: 160px;
    }

        .offer-tiles-modal .modal-dialog .offer-tile-img-container div {
            justify-content: center;
            align-content: flex-end;
            height: 100%;
        }

        .offer-tiles-modal .modal-dialog .offer-tile-img-container img {
            max-width: 250px;
            object-position: center;
            object-fit: contain;
            padding-top: 10px;
        }

    .offer-tiles-modal .modal-dialog .offer-tile-content-container {
        width: 100%;
    }

        .offer-tiles-modal .modal-dialog .offer-tile-content-container .offer-tile-header {
            padding-top: 0px;
            font-size: 20px;
            letter-spacing: -0.4px;
            line-height: 24px;
        }

    /*proposed tile*/
    .proposed-tile-img-container {
        height: 60px;
        width: 60px;
    }

        .proposed-tile-img-container > img {
            width: 60px;
            height: 60px;
            object-fit: contain;
        }

    .icon-bill-redesign.icon-bill-redesign.icon-10_order_list {
        font-size: 30px;
    }

    .bill-redesign-pay-your-bill-dialog .modal-header {
        padding: 15px;
    }

    .pay-your-bill-warning-dialog .modal-header {
        height: 55px;
        padding: 0px 30px !important
    }

    .tour-intro-dialog .modal-content {
        border-radius: 10px;
    }

    .tour-intro-dialog .modal-dialog {
        display: flex !important;
        align-items: center;
        justify-content: center;
        height: 100%;
        width: 100%;
    }

        .tour-intro-dialog .modal-dialog .modal-content {
            width: 290px;
        }

    .tour-intro-dialog .tour-intro-banner {
        max-width: 100%;
        width: 100%;
    }

        .tour-intro-dialog .tour-intro-banner img {
            width: 244px;
        }

    .tour-intro-dialog .tour-intro-content .tour-intro-content-text {
        max-width: 260px;
    }

    .guide-tour-intro-dialog .modal-dialog {
        position: absolute !important;
    }
    /* START Billing Bill Boards */
    .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container.slick-initialized .offer-tile-img-container {
        display: none !important;
    }

    .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container.slick-initialized .offer-tile-content-container {
        padding: 30px;
    }

    .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container.slick-initialized .personalization-tile-container > .d-flex {
        justify-content: space-between;
    }

    .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container.slick-initialized .slick-dots {
        text-align: center;
    }

    .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container.slick-initialized .slick-slide:nth-child(2) {
        box-shadow: 0 6px 24px 0 rgba(0,0,0,0.14);
        border: 0;
    }

    .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container .slick-list {
        padding: 15px;
        margin: -15px;
        padding-bottom: 30px;
        margin-bottom: -30px;
    }

    .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container .slick-slide:first-child:last-child {
        width: calc(100vw - 15px - 15px) !important;
    }

    .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container .slick-slide:first-child:nth-last-child(2),
    .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container .slick-slide:first-child:nth-last-child(2) ~ .slick-slide {
        width: calc(100vw - 15px - 15px - 30px) !important;
    }

    .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container .slick-slide:not(:last-child) {
        margin-right: 15px;
    }

    .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container .slick-track:has(.slick-slide.slick-current.slick-active:last-child) {
        left: 30px;
    }

    .offer-tiles-modal .modal-dialog {
        position: absolute !important;
    }

    .offer-tiles-modal .modal-dialog {
        position: absolute !important;
        top: auto;
        bottom: 0;
        left: 0;
        right: auto;
        margin: 0;
    }
    /* END Billing Bill Boards */

    /* START PBE MODAL */
    .ds-pbe-modal.modal {
        font-size: 0;
    }

    .ds-pbe-modal:not(.ds-pbe-modal-late-payment-charge-lg) .modal-dialog {
        position: relative;
    }

    .overage-graph-details {
        margin-left: 7.24px;
        margin-top: 2px;
    }

    .overage-table td > div,
    .overage-table th > div{
        padding-left: 4.94px;
    }

    .overage-table td:first-child > div,
    .overage-table th:first-child > div {
        padding-right: 4.94px;
    }
    /* END PBE MODAL */

    /*Start - Delta - PBE*/
    .pbe-inner-scrollable {
        overflow-x: auto;
        padding-bottom: 30px;
    }

     .pbe-inner-scrollable .pbe-wrapper {
        min-width: 708px;
        width: 100%;
     }
    /*END - Delta - PBE*/
}

@media (min-width: 768px) {
    .bill-redesign .title {
        font-size: 32px;
        letter-spacing: -.5px;
        line-height: 38px;
    }

    .same-height-316 {
        height: 316px;
    }

    .width-190-sm {
        width: 190px;
    }

    .width-65percent {
        width: 65%;
    }

    .min-width-sm-175 {
        min-width: 175px;
    }

    .max-width-sm-108 {
        max-width: 108px;
    }

    .margin-t-sm-7 {
        margin-top: 7px;
    }

    .margin-h-sm-7 {
        margin-left: 7px;
        margin-right: 7px;
    }

    .margin-h-sm-7p5 {
        margin-left: 7.5px;
        margin-right: 7.5px;
    }

    .pad-l-0-sm {
        padding-left: 0;
    }

    .pad-h-7p5-sm {
        padding-left: 7.5px;
        padding-right: 7.5px;
    }

    .pad-sm-t-5 {
        padding-top: 5px;
    }

    .pad-t-sm-12 {
        padding-top: 12px;
    }

    .txtSize-sm-36 {
        font-size: 36px;
    }

    .box-shadow-sm {
        box-shadow: 0 6px 25px 0 rgba(0,0,0,0.12);
    }

    .bill-redesign .modal-content {
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
    }

    .bill-redesign .form-absolute-right {
        position: absolute;
        top: 0;
        right: 0;
        margin-top: 76px;
        max-width: 189px;
    }

    .bill-redesign .infoblock-slider .slick-list {
        margin: 0 -7.5px;
        padding: 0;
    }

    .bill-redesign .infoblock-slider .slick-track {
        margin-left: 0;
    }

    .bill-redesign .slick-overflow-visible .slick-list {
        overflow: visible;
    }

    .bill-redesign .infoblock-slider .slick-slide {
        margin-right: 7.5px;
        margin-left: 7.5px;
    }

    /*call to action - focus outline*/
    .bill-redesign-accss-footer a.call-to-action:focus::before {
        width: calc(100% - 12px);
        border-radius: 30px;
        left: -9px;
    }

    /*Start Pagination*/
    .bill-pagination li:first-child,
    .bill-pagination li.active-sibling,
    .bill-pagination li.active,
    .bill-pagination li.active + li,
    .bill-pagination li:last-child {
        display: inline-block !important;
    }

        .bill-pagination li:first-child:nth-last-child(n+8) ~ li {
            display: none;
        }

            .bill-pagination li:first-child:nth-last-child(n+8) ~ li.active-sibling:before {
                content: "\2026";
                font-size: 24px;
                display: inline-block;
                margin-right: 2.5px;
            }

            .bill-pagination li:first-child:nth-last-child(n+8) ~ li.active + li:after {
                content: "\2026";
                font-size: 24px;
                display: inline-block;
                margin-left: 2.5px;
            }

            .bill-pagination li:first-child:nth-last-child(n+8) ~ li:nth-last-child(-n + 5) {
                display: inline-block;
            }

            .bill-pagination li:first-child:nth-last-child(n+8) ~ li:nth-last-child(5):before {
                content: "\2026";
                font-size: 24px;
                display: inline-block;
                margin-right: 2.5px;
            }

            .bill-pagination li:first-child:nth-last-child(n+8) ~ li:nth-child(-n + 2):before,
            .bill-pagination li:first-child:nth-last-child(n+8) ~ li:nth-last-child(-n + 2):before,
            .bill-pagination li:first-child:nth-last-child(n+8) ~ li.active-sibling:nth-last-child(-n + 4):before,
            .bill-pagination li:first-child:nth-last-child(n+8) ~ li:nth-child(-n + 2):after,
            .bill-pagination li:first-child:nth-last-child(n+8) ~ li:nth-last-child(-n + 2):after,
            .bill-pagination li:first-child:nth-last-child(n+8) ~ li.active-sibling:nth-last-child(-n + 4):after {
                display: none !important;
            }

            .bill-pagination li:first-child:nth-last-child(n+8).active ~ li:nth-last-child(-n + 5),
            .bill-pagination li:first-child:nth-last-child(n+8) ~ li.active ~ li:nth-last-child(-n + 5) {
                display: none;
            }

                .bill-pagination li:first-child:nth-last-child(n+8).active ~ li:nth-last-child(-n + 5):before,
                .bill-pagination li:first-child:nth-last-child(n+8) ~ li.active ~ li:nth-last-child(-n + 5):before {
                    display: none;
                }

            .bill-pagination li:first-child:nth-last-child(n+8).active ~ li:nth-child(-n + 5),
            .bill-pagination li:first-child:nth-last-child(n+8) ~ li.active ~ li:nth-child(-n + 5) {
                display: inline-block;
            }

            .bill-pagination li:first-child:nth-last-child(n+8).active ~ li:nth-child(-n + 4):after,
            .bill-pagination li:first-child:nth-last-child(n+8) ~ li.active ~ li:nth-child(-n + 4):after {
                display: none;
            }

    li:first-child:nth-last-child(n+8).active ~ li:nth-child(5):after,
    li:first-child:nth-last-child(n+8) ~ li.active ~ li:nth-child(5):after {
        content: "\2026";
        font-size: 24px;
        display: inline-block;
        margin-left: 2.5px;
    }

    .bill-pagination li:first-child:nth-last-child(n+8).active:before,
    .bill-pagination li:first-child:nth-last-child(n+8) ~ li.active:before,
    .bill-pagination li:first-child:nth-last-child(n+8).active:after, li:first-child:nth-last-child(n+8) ~ li.active:after {
        display: none;
    }
    /*End Pagination*/

    .pay-now-absolute {
        position: absolute;
        right: 0;
        top: 106px;
    }

    .pay-now-amount {
        min-width: 215px;
        text-align: right;
    }

    .payment-list-width {
        width: calc(100% - 280px);
    }

    /*Share Group Modal*/
    .payment-tile-width {
        max-width: 353px;
    }

    .bill-tile-width {
        max-width: 361px;
    }

    /*Add data to share group modal - start*/
    .add-data-to-share-dialog .select-wrap .col-sm-6:nth-last-child(n+3),
    .add-data-to-share-dialog .select-wrap .col-sm-6:nth-last-child(n+3) ~ .col-sm-6 {
        flex: 0 0 33.3333333333%;
        max-width: 33.3333333333%;
    }

    .add-data-to-share-dialog .select-wrap .col-sm-6:nth-last-child(n+4),
    .add-data-to-share-dialog .select-wrap .col-sm-6:nth-last-child(n+4) ~ .col-sm-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .add-data-to-share-dialog .select-wrap .col-sm-6:nth-last-child(n+5),
    .add-data-to-share-dialog .select-wrap .col-sm-6:nth-last-child(n+5) ~ .col-sm-6 {
        flex: 0 0 33.3333333333%;
        max-width: 33.3333333333%;
    }
    /*Add data to share group modal - end*/

    /*Filter by*/
    .container-disclosure {
        width: 260px;
        position: absolute;
        z-index: 10;
        border-radius: 4px;
        border: 1px solid #949596;
        box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
    }

    /*Bill Tour Location*/

    .ds-bill-tour-loc-container {
        top: 170px;
    }

    .bill-tour-tooltip {
        padding-top: calc(100% - 10px);
    }

        .bill-tour-tooltip.bill-tour-tooltip .tooltip {
            width: 413px;
            top: calc(50% - 20px) !important;
        }

        .bill-tour-tooltip .tooltip-inner {
            max-width: 413px;
        }

    .proposed-tile-img-container {
        min-height: 72px;
        min-width: 72px;
    }

        .proposed-tile-img-container > span,
        .offer-tile-img-container > span {
            height: 72px;
            width: 72px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .proposed-tile-img-container > img {
            width: auto;
            height: 100px;
            margin-left: 15px;
            margin-top: 15px;
        }

    .offer-tile-img-container > img {
        height: 130px;
        width: auto;
        margin-bottom: -33px;
    }

    .offer-tile-img-container {
        padding-top: 30px;
    }

    .offer-tile-custom .offer-tile-img-container {
        padding-top: 0;
        margin-bottom: -6px;
    }

    .offer-tile-content-container {
        padding-bottom: 30px;
        position: relative;
        z-index: 1;
    }

    #bills > div,
    #payments > div {
        margin-right: 0px;
    }

    #bills ~ .bills-payments-tile-container,
    #payments ~ .bills-payments-tile-container {
        margin-left: 30px;
    }

    .icon-bill-redesign.icon-bill-redesign.icon-10_order_list {
        font-size: 36px;
    }
    /* START Billing Bill Boards */
    .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container:not(.slick-initialized) > div {
        box-shadow: none;
        border: 1px solid #e1e1e1;
        border-radius: 10px;
        width: auto !important;
    }

        .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container.slick-initialized .slick-slide:nth-child(2),
        .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container:not(.slick-initialized) > div:nth-child(2) {
            box-shadow: 0 6px 24px 0 rgba(0,0,0,0.14);
            border: 0;
        }

    .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container.slick-initialized .slick-slide:nth-child(2) {
        box-shadow: 0 6px 24px 0 rgba(0,0,0,0.14);
        border: 0;
    }

    .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container .slick-list {
        padding: 30px 15px 30px 15px;
        margin: -30px -15px -30px -15px;
    }

    .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container .slick-track {
        left: 0px !important;
    }

    .offer-tiles-modal .modal-dialog {
        padding-left: 30px;
        padding-right: 30px;
    }

    .offer-tiles-modal .modal-content {
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
    }
    /* END Billing Bill Boards */

    /*START - Delta - PBE*/
    .pbe-chart-scrollable {
        max-width: 888px;
    }

    .late-payment-charge-details {
        max-width: 595px;
    }

    .what-happened-date {
        min-width: 64px;
    }

    .what-happened-content {
        flex: 1;
    }
    /*END - Delta - PBE*/
}

@media (min-width:768px) and (max-width:991.98px) {
    .bill-and-payment-services div:nth-last-child(n+4) .dimension-72,
    .bill-and-payment-services div:nth-last-child(n+4) ~ div span.dimension-72 {
        width: 60px;
        height: 60px;
    }

    .bill-and-payment-services div:nth-last-child(n+4) .icon-mobile,
    .bill-and-payment-services div:nth-last-child(n+4) ~ div .icon-mobile {
        font-size: 35px;
    }

    .bill-and-payment-services div:nth-last-child(n+4) .icon-tv,
    .bill-and-payment-services div:nth-last-child(n+4) ~ div .icon-tv {
        font-size: 25px;
    }

    .bill-and-payment-services div:nth-last-child(n+4) .icon-laptop,
    .bill-and-payment-services div:nth-last-child(n+4) ~ div .icon-laptop {
        font-size: 22px;
    }

    .bill-and-payment-services div:nth-last-child(n+4) .icon-homephone,
    .bill-and-payment-services div:nth-last-child(n+4) ~ div .icon-homephone {
        font-size: 38px;
    }

    /*Bill Comparison Grid - Tablet*/
    .billing-comparison-grid tr > th:last-child:not(:first-child),
    .billing-comparison-grid tr > td:last-child:not(:first-child) {
        width: 185px !important;
    }

    .billing-treegrid-row-chart-bar {
        padding-right: 175px;
    }

    .billing-treegrid-row-chart-bar-wrap {
        right: 175px;
    }

    /*New footer*/
    .gf-complete .footer-with-accordion.container {
        padding-left: 30px;
        padding-right: 30px;
    }

    /*Proactive tour dialog - START*/
    .proactive-tour-dialog .modal-dialog,
    .reactive-tour-dialog .modal-dialog {
        max-width: 100%;
        height: auto;
        max-height: calc(100% - 45px);
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-radius: 10px 10px 0 0;
    }

    .proactive-tour-dialog .modal-content,
    .reactive-tour-dialog .modal-content {
        border-radius: 10px 10px 0 0;
    }
    /*Proactive tour dialog - END*/

    .summary-changes-timeline-date-wrap {
        min-width: 148px;
        max-width: 148px;
    }

    .margin-h-sm-neg-7 {
        margin-left: -7px;
        margin-right: -7px;
    }

    .ds-pbe-modal-late-payment-charge-lg .modal-dialog {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
    }
}

@media (min-width: 992px) {
    .margin-top-neg-190-md {
        margin-top: -190px;
    }

    .margin-t-md-8 {
        margin-top: 8px;
    }

    .margin-l-md-0 {
        margin-left: 0px;
    }

    .margin-l-md-70 {
        margin-left: 70px;
    }

    .margin-h-md-7p5 {
        margin-left: 7.5px;
        margin-right: 7.5px;
    }

    .pad-l-md-75 {
        padding-left: 75px;
    }

    .pad-r-md-4 {
        padding-right: 4px;
    }

    .pad-t-md-5 {
        padding-top: 5px;
    }

    .min-width-md-250 {
        min-width: 250px;
    }

    .min-width-md-301 {
        min-width: 301px;
    }

    .payment-tile-width {
        max-width: 301px;
    }

    .bill-redesign .form-absolute-right {
        position: absolute;
        top: 0;
        right: 0;
        margin-top: 195px;
        max-width: 189px;
    }

    .flex-direction-column-md {
        flex-direction: column;
    }

    .bill-redesign .hidden-md {
        display: none;
    }

    .bgGray19-md {
        background-color: #f4f4f4;
    }

    .bill-redesign .big-title {
        font-size: 40px;
        letter-spacing: -.7px;
        line-height: 46px;
    }

    .billing-download-accordion .arrow:after {
        left: 46.8%;
    }

    .payment-list-width {
        width: calc(100% - 136px);
    }

    .gh-myb.gh-myb .global-navigation .menu-flyout-visible .sub-nav-item ul > li a:focus {
        text-decoration: underline
    }

    .gh-myb .global-navigation.gn-shop .menu-flyout-visible .sub-nav-item .sub-nav-level4 li > a {
        display: inline-block;
    }

    .gh-myb #federal-store-locator-links a:not(:last-child) {
        margin-bottom: 15px;
        padding-bottom: 0px;
    }

    .bill-redesign-accss .gh-myb .global-navigation.gn-mybell #connector-search input.ui-autocomplete-input:focus {
        outline: 2px solid #FFFFFF !important;
        outline-offset: 4px;
        box-shadow: none !important
    }

    .bill-redesign-accss .gh-myb #accessible-connector .menu-flyout-visible .sub-nav-level-1:focus {
        outline: 2px solid #0075ff !important;
        outline-offset: 4px;
    }


    .bill-redesign-accss .gh-myb #accessible-connector .menu-flyout .sub-nav-root .sub-nav-level4 li > a:focus::before {
        content: '' !important;
        height: 100%;
        width: calc(100% - 60px);
        position: absolute;
        display: inline-block !important;
        outline-offset: 2px;
        outline: 2px solid #0075ff !important;
        z-index: 1;
        top: 0;
        left: 30px;
        pointer-events: none;
        border-radius: inherit
    }

    .summary-changes-header,
    .summary-changes-filter-sort-container,
    .summary-changes-timeline-container {
        padding-left: 116px;
        padding-right: 83px;
    }

    .ds-bill-tour-loc-container {
        top: 256px;
    }

    .offer-tile-img-container {
        padding-top: 0;
        margin-left: -30px;
    }

    .tile-v2 .modal-dialog .offer-tile-img-container {
        margin-left: 0;
        margin-right: -30px;
    }

    .offer-tile-img-container > img {
        width: 100%;
        height: auto;
    }

    .offer-tile-custom .offer-tile-img-container {
        margin-left: 0;
        padding: 0;

    }
        .offer-tile-custom .offer-tile-img-container > img {
            width: auto;
            height: 156px;
            margin-bottom: -40px;
        }

    .offer-tile-content-container {
        padding: 30px;
        position: relative;
        z-index: 1;
    }

    .summary-changes-timeline-date {
        min-width: 140px;
        max-width: 140px;
    }
    /* START Billing Bill Boards */
    .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container {
        flex-direction: column;
    }

        .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container > div {
            margin: 0;
            min-width: 301px;
            max-width: 301px;
        }

            .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container > div:not(:last-child) {
                margin-bottom: 15px;
                margin-right: 0 !important;
            }

        .PersonalizationTilesContainer[data-location="BillingBillBoards"] .personalization-slider-container .offer-tile-img-container {
            padding-left: 0;
        }
    /* END Billing Bill Boards */

    .ds-pbe-modal-late-payment-charge-lg {
        font-size: 0;
    }

        .ds-pbe-modal-late-payment-charge-lg .modal-dialog {
            max-width: none;
            max-height: none;
            width: 100%;
            padding-left: 16px;
            padding-right: 16px;
            padding-top: 60px;
            padding-bottom: 60px;
        }

            .ds-pbe-modal-late-payment-charge-lg .modal-dialog .modal-content {
                border-radius: 10px;
            }

    .late-payment-charge-details {
        max-width: 730px;
    }

    .what-happened-date {
        min-width: 59px;
    }
}

@media (max-width: 991.98px) {
    .bill-redesign-accss .gh-myb #accessible-connector a.connector-brand:focus,
    .bill-redesign-accss .gh-myb #accessible-connector .connector-area div > a:last-child:focus {
        outline: 0px !important;
    }

        .bill-redesign-accss .gh-myb #accessible-connector a.connector-brand:focus::after {
            outline-offset: 4px;
            border-radius: 2px;
        }

        .bill-redesign-accss .gh-myb #accessible-connector a.connector-brand:focus::after,
        .bill-redesign-accss .gh-myb .global-navigation #connector-search-button:focus,
        .bill-redesign-accss .gh-myb #accessible-connector .menu-flyout-visible .sub-nav-level-1:focus,
        .bill-redesign-accss.bill-redesign-accss .gh-myb .global-navigation .connector-nav-open-button:focus,
        .bill-redesign-accss .gh-myb .global-navigation .federal-bar-mobile .custom-select-trigger:focus {
            outline: 2px solid #FFFFFF !important;
        }

    .bill-redesign-accss .gh-myb .global-navigation.gn-mybell #connector-search input.ui-autocomplete-input:focus {
        outline: 2px solid #0075ff !important;
    }

    .bill-redesign-accss.bill-redesign-accss .gh-myb .global-navigation .connector-nav-open-button:focus {
        border-radius: 50% !important;
    }

    .bill-redesign-accss .gh-myb .federal-bar-mobile > li a,
    .bill-redesign-accss .gh-myb .connector-settings-mobile > li > a,
    .bill-redesign-accss .gh-myb .global-navigation select:focus ~ .custom-select-trigger {
        border-radius: 0px !important;
    }

    .bill-redesign-accss .gh-myb .global-navigation #connector-search-button:focus,
    .bill-redesign-accss.bill-redesign-accss .gh-myb .global-navigation .connector-nav-open-button:focus {
        outline-offset: 0px !important;
    }

    .bill-redesign-accss .gh-myb .connector-settings-mobile > li > a,
    .bill-redesign-accss .gh-myb .custom-select-mobile-federal-bar > select,
    .bill-redesign-accss .gh-myb .federal-bar-mobile > li a,
    .bill-redesign-accss .gh-myb .global-navigation.gn-mybell #connector-search input.ui-autocomplete-input:focus,
    .bill-redesign-accss .gh-myb .custom-select-trigger.mobile-federal-province:focus,
    .bill-redesign-accss .gh-myb .global-navigation select:focus ~ .custom-select-trigger {
        outline-offset: -2px !important;
    }

    .bill-redesign-accss .gh-myb #accessible-connector .connector-area div > a:last-child:focus {
        position: relative;
    }

        .bill-redesign-accss .gh-myb #accessible-connector .connector-area div > a:last-child:focus::before,
        .bill-redesign-accss .gh-myb #accessible-connector .menu-flyout .sub-nav-root .sub-nav-level4 li > a:focus::before {
            content: '' !important;
            height: 100%;
            width: calc(100% + 8px);
            position: absolute;
            display: block;
            outline-offset: 0;
            outline: 2px solid #FFFFFF !important;
            z-index: 1;
            top: 2px;
            left: 1px;
            pointer-events: none;
            border-radius: 0px;
        }

    .bill-redesign-accss .gh-myb #accessible-connector .menu-flyout .sub-nav-root .sub-nav-level4 li > a:focus::before {
        left: -4px;
        height: calc(100% - 4px);
        top: 2px;
    }

    .gh-myb .sub-nav-group a {
        margin-left: 6px;
        margin-right: 6px;
    }
}

@media (min-width: 992px) and (max-width: 1140px) {
    .bill-redesign .margin-r-md-30 {
        margin-right: 30px;
    }
}

@media (min-width: 1200px) {
    .ds-pbe-modal-late-payment-charge-lg {
        width: 100%;
    }
}

.gh-myb .connector-log-out-button:focus {
    overflow: initial;
}

.gh-myb .connector-nav-open-button:focus,
.gh-myb .connector-mobile-bar .connector-brand:focus,
.bill-redesign-accss-footer .skip-to-main-link:focus {
    position: absolute;
}

    .gh-myb .connector-mobile-bar .connector-brand:focus::before {
        height: calc(100% - 9px);
        width: calc(100% + 6px);
        top: 3px;
    }

.bill-redesign-accss .scrollToTop.mobile:focus {
    position: fixed;
}

.bill-redesign-accss .bill-redesign .btn:focus::after,
.bill-redesign-accss-footer .btn:focus::before,
.bill-redesign-accss .standard-step-flow-header button:focus::after {
    height: calc(100% + 12px);
    width: calc(100% + 12px);
    top: -6px;
    left: -6px;
}

.bill-redesign-accss .bell-services-mobility-modal li button:focus::after {
    height: calc(100% + 30px);
    width: calc(100% - 8px);
    position: absolute;
    top: -12px;
    left: 5px;
}

.bill-redesign-accss .bell-services-modal li button.bell-services-modal-user-btn:focus::after {
    height: calc(100% - 6px);
    width: calc(100% - 8px);
    position: absolute;
    top: 3px;
    left: 4px;
}

.bill-redesign-accss .bell-services-modal li button.bell-service-compare-modal-user-btn:focus::after {
    height: calc(100% - 6px);
    width: calc(100% - 8px);
    position: absolute;
    top: 3px;
    left: 4px;
}

.bill-redesign-accss .bell-services-mobility-modal button:focus::after {
    height: calc(100% - 8px);
    width: calc(100% - 8px);
    position: absolute;
    top: 5px;
    left: 5px;
}

.bill-redesign .btn-default-white:focus {
    background-color: #FFFFFF;
    border-color: #FFFFFF;
}

.bill-redesign .btn-primary:active,
.bill-redesign .btn-primary:not(:disabled):not(.disabled):active,
.bill-redesign .btn-primary:focus {
    padding: 7px 30px;
}

.bill-redesign-accss .billing-services-mob-list a:focus::after,
.bill-redesign-accss .billing-services-mob-list button:focus::after,
.bill-redesign-accss .billing-services-print-modal .billing-services-print-btns button:focus::after,
.bill-redesign-accss .billing-services-download-modal .billing-services-download-btns button:focus::after,
.bill-redesign-accss .modal-tooltip button.close:focus::after {
    height: calc(100% - 6px);
    width: calc(100% - 6px);
    position: absolute;
    top: 3px;
    left: 3px;
}

.bill-redesign-accss .modal-tooltip button.close:focus {
    border: 0;
}

.bill-redesign-accss .bill-redesign select:focus {
    box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc !important;
}

#bills[tabindex="0"]:focus .billhistory-m::after,
#payments[tabindex="0"]:focus .paymenthistory-m::after {
    width: calc(100% + 5px);
    left: -5px;
}

.billing-treegrid-row-chart td[colspan="4"]:focus::after {
    width: calc(100% - 43px);
    left: 22px;
}

.bill-redesign-accss .bar-history:focus .bar-history-inner {
    position: absolute;
}

/*New outline focus*/
.bill-redesign-accss .bill-redesign a:focus,
.bill-redesign-accss .bill-redesign button:focus,
.bill-redesign-accss .bill-redesign .tooltip-static:focus,
.bill-redesign-accss .bill-redesign .tooltip:focus,
.bill-redesign-accss .bill-redesign .tooltip-interactive:focus,
.standard-step-flow-header-upper.bgBlue a:focus,
.standard-step-flow-header-upper.bgBlue button:focus,
.bill-redesign-accss .bar-history:focus .bar-history-inner,
.select-filter input:focus ~ .ctrl_element,
.bill-redesign-accss .bill-redesign select:focus,
.bill-redesign a.filter-accordion:focus,
.bill-redesign-accss .ds-bill-tour a:focus,
.bill-redesign-accss .ds-bill-tour button:focus,
.bill-redesign-accss .offer-tiles-modal .modal-header button.close:focus,
.bill-redesign-accss .offer-tiles-modal .modal-dialog button.close:focus
.bill-redesign-accss .offer-tiles-modal .modal-header a:focus,
.bill-redesign-accss .offer-tiles-modal .modal-dialog a:focus {
    outline-offset: 4px !important;
    outline: 2px solid #0075ff !important;
    box-shadow: none !important;
}

.bill-redesign-accss .offer-tiles-modal .modal-header button.close:focus,
.bill-redesign-accss .offer-tiles-modal .modal-dialog button.close:focus {
    border-radius: 50%;
}

.bill-redesign-accss .offer-tiles-modal a.txtUnderline:focus {
    border-radius: 2px !important;
}

.bill-redesign-accss .bill-redesign a:focus::after,
.bill-redesign-accss .bill-redesign button:focus::after,
.bill-redesign-accss .bill-redesign .tooltip-static:focus::after,
.bill-redesign-accss .bill-redesign .tooltip:focus::after,
.bill-redesign-accss .bill-redesign .tooltip-interactive:focus::after {
    display: none;
}

/* START - global header and footer*/
body.is_tabbing .gh-myb #accessible-connector a:focus,
body.is_tabbing .gh-myb .global-navigation .connector-active-secondary-nav a:focus,
body.is_tabbing .gh-myb .global-navigation.gn-mybell #connector-search input.ui-autocomplete-input:focus,
body.is_tabbing .gh-myb #accessible-connector [type="submit"]:focus,
body.is_tabbing .gh-myb .federal-bar-store-locator-popup a:focus,
body.is_tabbing .gh-myb .federal-bar-store-locator-popup button:focus,
body.is_tabbing .gh-myb .federal-bar-select-provinces-popup a:focus,
body.is_tabbing .gh-myb #accessible-connector .menu-flyout a:focus,
body.is_tabbing .gh-myb #accessible-connector .connector-login-modal a:focus,
body.is_tabbing .gh-myb #accessible-connector .voice-search-btn:focus,
body.is_tabbing .gh-myb #accessible-connector .hideAutocomplete:focus,
body.is_tabbing .gh-myb #accessible-connector [type="submit"]:focus,
body.is_tabbing .gh-myb #accessible-secondary-nav.connector-active-secondary-nav .secondary-nav-dropdown.connector-drop-down a:focus,
body.is_tabbing .gh-myb .global-navigation .federal-bar-links a:focus,
body.is_tabbing .gh-myb .global-navigation .federal-bar-store-locator-popup .graphical_ctrl_checkbox input[type="checkbox"]:focus ~ .ctrl_element,
.gh-myb .global-navigation .federal-bar-links a:focus,
.gh-myb #accessible-connector-search #connector-search [type="reset"]:focus::before,
.gh-myb #accessible-connector-search #connector-search [type="submit"]:focus::before,
.gh-myb #accessible-connector-search #connector-search #voice_search:focus::before,
.bill-redesign-accss-footer a:focus,
.bill-redesign-accss-footer button:focus,
.bill-redesign-accss-footer input:focus,
.bill-redesign-accss-footer [tabindex='0']:focus,
.bill-redesign-accss-footer .search-bar-footer [type="reset"]:focus::before,
body.is_tabbing .bill-redesign-accss-footer a:focus,
body.is_tabbing .bill-redesign-accss-footer button:focus,
body.is_tabbing .bill-redesign-accss-footer .search-bar-footer [type="search"]:focus {
    box-shadow: none !important
}



.bill-redesign-accss .gh-myb a:focus,
.bill-redesign-accss .gh-myb #accessible-connector a:focus,
.bill-redesign-accss .gh-myb .global-navigation .connector-active-secondary-nav a:focus,
.bill-redesign-accss .gh-myb #accessible-connector .menu-flyout a:focus,
.bill-redesign-accss .gh-myb #accessible-secondary-nav.connector-active-secondary-nav .secondary-nav-dropdown.connector-drop-down a.services-selection.active:focus,
.bill-redesign-accss .gh-myb div.federal-bar-links.federal-bar-links_left > a:focus {
    outline: 2px solid #FFFFFF !important;
    outline-offset: 4px;
    box-shadow: none !important
}

.bill-redesign-accss .gh-myb .global-navigation.gn-shop #connector-search #voice_search:focus .icon-voice-search::before,
.bill-redesign-accss .gh-myb #accessible-connector [type="submit"]:focus::after,
.bill-redesign-accss .gh-myb #connector-search [type="submit"]:focus::after,
.bill-redesign-accss .gh-myb #connector-search [type="reset"]:focus .icon-close-solid,
.bill-redesign-accss .gh-myb #accessible-secondary-nav.connector-active-secondary-nav .secondary-nav-dropdown.connector-drop-down a:focus,
.bill-redesign-accss .gh-myb #accessible-connector .menu-flyout a:focus,
.bill-redesign-accss .gh-myb #accessible-connector .connector-login-modal.user-control-menu a:focus,
.gh-myb #federal-store-locator-links a:focus,
.bill-redesign-accss .gh-myb .global-navigation .federal-bar-store-locator-popup .graphical_ctrl_checkbox input[type="checkbox"]:focus ~ .ctrl_element,
.bill-redesign-accss .gh-myb .global-navigation .federal-bar-store-locator-popup input:focus,
.bill-redesign-accss .gh-myb .global-navigation .federal-bar-store-locator-popup button:focus,
.bill-redesign-accss .bill-redesign-accss-footer a:focus,
.bill-redesign-accss .bill-redesign-accss-footer button:focus,
.bill-redesign-accss .bill-redesign-accss-footer input:focus,
.bill-redesign-accss .bill-redesign-accss-footer [tabindex='0']:focus,
.bill-redesign-accss .bill-redesign-accss-footer .search-bar-footer [type="search"]:focus,
.bill-redesign-accss .bill-redesign-accss-footer .search-bar-footer [type="reset"]:focus .icon,
.bill-redesign .grid-pbe-tooltip-close:focus .icon-x-close,
body.is_tabbing .pbe-inner-scrollable:focus {
    outline: 2px solid #0075ff !important;
    outline-offset: 4px;
}

.bill-redesign-accss .gh-myb .global-navigation .connector-active-secondary-nav .secondary-nav-dropdown a:focus,
.bill-redesign-accss .gh-myb #accessible-connector .menu-flyout-visible .sub-nav-level-1:focus,
.bill-redesign-accss .gh-myb .global-navigation .menu-flyout-visible .sub-nav-level-1:focus,
.bill-redesign-accss .gh-myb .connector-login-modal.user-control-menu a,
.bill-redesign-accss .bill-redesign-accss-footer .search-bar-footer .search-btn,
.bill-redesign-accss .bill-redesign-accss-footer .legal-links.txtBlue a,
.bill-redesign-accss .tooltipinfo-modal-close-button button.close:focus{
    outline-offset: -2px !important;
}

.bill-redesign-accss .bill-redesign-accss-footer .search-bar-footer .search-btn {
    outline-offset: 0px !important;
}

.bill-redesign-accss .gh-myb #accessible-connector .menu-flyout-visible .sub-nav-level-1:focus {
    border-radius: inherit !important;
}

.bill-redesign-accss .gh-myb a:not(.btn):not(.sub-nav-level-1):not(.services-selection),
.bill-redesign-accss .bill-redesign-accss-footer a:not(.btn) {
    border-radius: 2px !important;
}

.bill-redesign-accss .gh-myb .global-navigation .connector-active-secondary-nav li.active .secondary-nav-lob:focus,
.bill-redesign-accss .gh-myb .global-navigation a.login-register-button:focus,
.bill-redesign-accss .gh-myb #accessible-connector .menu-flyout .sub-nav-level4 li > a:focus,
.bill-redesign-accss .gh-myb #accessible-connector a.login-register-button:focus,
.bill-redesign-accss .gh-myb #accessible-connector-settings .connector-log-out-button:focus,
.bill-redesign-accss .gh-myb .global-navigation #voice_search:focus:before,
.bill-redesign-accss .bill-redesign-accss-footer .footer-c-t-a .btn.btn-primary.call-to-action:focus,
.bill-redesign-accss .bill-redesign-accss-footer .legal-links.txtBlue a,
.bill-redesign-accss .bill-redesign-accss-footer .search-bar-footer [type="reset"]:focus,
.bill-redesign-accss .bill-redesign .grid-pbe-tooltip-close:focus,
.bill-redesign-accss .bill-redesign-accss-footer .search-bar-footer [type="reset"]:focus,
.bill-redesign-accss .tab-control .header-tab-control ul li a:focus,
.bill-redesign-accss .tablist-underlined [role=tab]:focus {
    outline: 0px !important;
}

.bill-redesign-accss .gh-myb .login-register-button:focus,
.bill-redesign-accss .bill-redesign-accss-footer .footer-c-t-a .btn.btn-primary.call-to-action:focus,
.bill-redesign-accss .bill-redesign-accss-footer .legal-links.txtBlue a,
.bill-redesign-accss .gh-myb #accessible-connector .menu-flyout .sub-nav-level4 li > a:focus,
.bill-redesign-accss .gh-myb #accessible-connector-settings .connector-log-out-button,
.bill-redesign-accss .tab-control .header-tab-control ul li a:focus,
.bill-redesign-accss .tablist-underlined [role=tab]:focus {
    position: relative;
}

    .bill-redesign-accss .gh-myb .login-register-button:focus::after,
    .bill-redesign-accss .gh-myb .global-navigation .connector-active-secondary-nav li.active .secondary-nav-lob:focus::after,
    .bill-redesign-accss .gh-myb #accessible-connector-settings .connector-log-out-button:focus::after,
    .bill-redesign-accss .bill-redesign-accss-footer .footer-c-t-a .btn.btn-primary.call-to-action:focus::before,
    .bill-redesign-accss .bill-redesign-accss-footer .legal-links.txtBlue a:focus::before,
    .bill-redesign-accss .tab-control .header-tab-control ul li a:focus::before,
    .bill-redesign-accss .tablist-underlined [role=tab]:focus::before {
        content: '';
        height: calc(100% + 6px);
        width: 100%;
        position: absolute;
        display: block;
        outline-offset: 4px;
        outline: 2px solid #FFFFFF !important;
        z-index: 1;
        top: 0;
        left: 0;
        pointer-events: none;
        border-radius: inherit;
    }

    .bill-redesign-accss .tab-control .header-tab-control ul li a:focus::before,
    .bill-redesign-accss .tablist-underlined [role=tab]:focus::before {
        outline: 2px solid #0075ff !important;
        height: calc(100% - 2px);
    }

    .bill-redesign-accss .gh-myb #accessible-connector-settings .connector-log-out-button:focus::after {
        height: calc(100% - 6px);
        width: calc(100% - 11px);
        left: 11px;
        top: 3px;
    }

    .bill-redesign-accss .bill-redesign-accss-footer .footer-c-t-a .btn.btn-primary.call-to-action:focus::before,
    .bill-redesign-accss .bill-redesign-accss-footer .legal-links.txtBlue a:focus::before {
        outline: 2px solid #0075ff !important;
        height: 100%;
        width: calc(100% - 10px);
        left: -4px;
    }

    .bill-redesign-accss .bill-redesign-accss-footer .legal-links.txtBlue a:focus::before {
        width: calc(100% - 14px);
        left: 0px;
    }

.bill-redesign-accss .bill-redesign-accss-footer .legal-links a:last-child:focus::before {
    width: 100%;
}


.bill-redesign-accss .gh-myb .login-register-button:focus::after {
    height: 100%;
    width: calc(100% - 15px);
}

.bill-redesign-accss .gh-myb .login-register-button {
    display: inline-flex;
    align-items: center;
}

    .bill-redesign-accss .gh-myb .login-register-button .icon.icon-chevron.chevron-down {
        top: 0px;
    }

.bill-redesign-accss .gh-myb .connector-brand a {
    display: inline-block
}

.bill-redesign-accss .gh-myb #accessible-federal-bar.federal-bar .graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element {
    z-index: 1;
}

.bill-redesign-accss .gh-myb #accessible-connector .menu-flyout .sub-nav-level4 li > a:focus {
    overflow: visible;
}
/* END - global header and footer*/

.billing-treegrid-header-d-print-accordion button:focus::after {
    display: block;
}

.bill-redesign-accss .bill-redesign a.txtWithIconUnderlineOnInteraction:focus,
.bill-redesign-accss .bill-redesign a.txtDecorationNoneHover:not(.pbe-tag):focus,
.bill-redesign-accss .bill-redesign a.txtUnderline:focus,
.bill-redesign-accss .bill-redesign .ds-pbe-modal a:not(.btn):focus,
.billing-treegrid-header button[data-toggle="modal"],
.bill-redesign button.disclosure-toggle:focus,
.standard-step-flow-header-upper.bgBlue a:not(.btn):focus,
.modal-header button.close:focus,
.modal-dialog button.close:focus,
.reactive-tour-dialog .close:focus,
.tour-dialog .close:focus,
.delta-scope-dialog .modal-header button.close:focus,
.payment-tooltic:focus,
.tooltip-interactive:focus,
.tooltip-static:focus,
.modal-tooltip button.close:focus {
    border-radius: 2px;
}

.bell-services-modal .modal-header .close:focus,
.bill-redesign .billing-services-mob-list li button:focus,
.bill-redesign-accss .bill-redesign .bell-services-modal-user-btn:focus,
.billing-services-print-modal .billing-services-print-btns button:focus,
.billing-services-download-modal .billing-services-download-btns button:focus {
    outline-offset: -2px !important;
}

.payment-tile-width a.txtWithIconUnderlineOnInteraction:focus {
    border-radius: 2px;
    outline-offset: 4px;
    outline: 2px solid #0075ff !important;
}

    .payment-tile-width a.txtWithIconUnderlineOnInteraction:focus::after {
        display: none;
    }

.payment-tile-width.bgBlue a.txtWithIconUnderlineOnInteraction:focus,
.billing-treegrid-row-mob-total.bgBlue button.btn-default-white,
.bill-redesign .billing-treegrid-row-sharing a:focus,
.bill-redesign .billing-services-modal-sharing a:focus,
.standard-step-flow-header-upper.bgBlue a:focus,
.standard-step-flow-header-upper.bgBlue button:focus,
.bill-redesign-pay-your-bill-dialog .bgBlue button.btn-default-white:focus,
.share-group-table thead.bgBlue a:focus,
.bill-redesign-accss .bill-redesign .filter-applied div button:focus,
.bill-redesign-accss .bill-redesign .ds-bill-tour a:focus {
    outline: 2px solid #FFFFFF !important;
}

.billing-treegrid-header-d-print-accordion button:focus,
.billing-treegrid-header-d-download-accordion button:focus,
.billing-download-accordion button:focus,
ul.multiple-bill-dates-list li a:focus {
    position: relative;
    border-radius: inherit;
}

.bill-redesign-accss .bill-redesign a.page-number.txtDecorationNoneHover:focus {
    border-radius: 50%;
}

.share-group-table thead.bgBlue a:focus {
    outline-offset: 6px !important;
}

.bill-redesign-accss .bill-redesign .ds-pbe-modal a:not(.btn):focus {
    outline-offset: 2px !important;
}

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    /* seems like IE rendering engine has an issue dealing with box-shadow directly applied to elements. use outline on default state to hide weird artifacts being left-out on blur */
    .gh-myb a, .bill-redesign-accss-footer a {
        outline: 7px solid transparent;
    }

    .bill-redesign .tooltip.bs-tooltip-top .arrow,
    .bill-redesign .tooltip.bs-tooltip-bottom .arrow {
        box-shadow: none;
    }

        .bill-redesign .tooltip.bs-tooltip-top .arrow::after {
            content: '';
            display: block;
            position: absolute;
            width: 40px;
            height: 40px;
            z-index: -1;
            transform: rotate(45deg);
            top: -24px;
            left: 5px;
            box-shadow: 0 0 9px 5px rgba(0, 0, 0, 0.10);
        }

        .bill-redesign .tooltip.bs-tooltip-bottom .arrow::after {
            content: '';
            display: block;
            position: absolute;
            width: 40px;
            height: 40px;
            z-index: -1;
            transform: rotate(45deg);
            top: 15px;
            left: 5px;
            box-shadow: 0 0 9px 5px rgba(0, 0, 0, 0.10);
        }
}

/* END - Focus Outline  Styles */

@media print {
    .billing-treegrid-header .icon-bill-redesign.icon-tv_internet_mobile,
    .billing-services-mob .billing-treegrid-row-mob-total *,
    .billing-services-mob .billing-services-mob-list li.pad-h-15 {
        color: #111 !important;
    }
}


.labels-container .bottom-0 {
    bottom: 0px !important;
}
.labels-container .text-align {
    max-width: 120px;
    text-align: right;
    margin-left: -120px;
}
    .labels-container .text-label.text-align-r {
        text-align: right;
    }
    .labels-container .text-label.text-align-l {
        text-align: left;
    }
 
    .labels-container .text-label.margin-l-neg-80 {
        margin-left: -80px;
    }

/*MOBILE ONLY*/
@media (max-width: 767.98px) {
    
    .split-center-label.d-flex.flex-column.center-label {
        right: 25px;
        top: 95px;
    }

        .split-center-label.d-flex.flex-column.center-label .label-indicator-line-top {
            height: 26px;
        }

    .labels-container .text-align {
        text-align: right;
        margin-left: -90px;
    }

    .labels-container .label-indicator-line-top.s-right {
        right: -30px;
    }

    .labels-container .text-label.margin-l-neg-20 {
        margin-left: 5px;
        max-width: 100px;
    }

    .labels-container .text-label.margin-l-negative {
        margin-left: -28px;
        
    }
}    
/*TABLET ONLY*/
@media (min-width: 768px) and (max-width: 991.98px) {
    .labels-container .text-label.margin-l-negative {
        margin-left: -58px;
    }
    .split-center-label.d-flex.flex-column.center-label .label-indicator-line-top {
        height: 30px;
    }
    .split-center-label.d-flex.flex-column.center-label {
        right: 10px;
        top: 50px;
    }
    .labels-container .text-align {
        text-align: right;
        margin-left: -90px;
    }
    .labels-container .label-indicator-line-top.s-right {
        right: -30px;
    }
    .labels-container .text-label.margin-l-neg-20 {
        margin-left: -5px;
    }
    .labels-container .text-label.margin-l-negative {
        margin-left: -30px;
    }
    .split-center-label.d-flex.flex-column.center-label {
        right:25px;
        top: 90px;
    }
        .split-center-label.d-flex.flex-column.center-label .label-indicator-line-top {
            height: 32px;
        }
}

@media (min-width: 799px) {
    .w-100.text-label.margin-l-negative.text-align-r {
        width: calc(100% + 40px) !important;
    }
    .labels-container .text-label.margin-l-negative {
        margin-left: -75px;
    }
}
/*Desktop ONLY*/
@media (min-width: 991.98px) {
    .labels-container .text-label.text-align-r {
        margin-bottom: 10px;
    }
        .labels-container .text-label.margin-l-negative {
            margin-left: -85px;
        }
        .labels-container .text-label.margin-l-neg-20 {
            margin-left: -5px;
        }
    .split-center-label.d-flex.flex-column.center-label {
        right: 30px;
        top: 85px;
    }
        .split-center-label.d-flex.flex-column.center-label .label-indicator-line-top {
            height: 36px;
        }

}



.labels-container .text-label.text-align-r {
    margin-bottom: 10px;
}
.label-indicator-line-top.increase-indicator-height {
    height: 122px;
}

.split-center-label.d-flex.flex-column.indicator-align, .split-right-label.d-flex.flex-column.indicator-align {
    height: calc(100% + 10px);
}

.bill-redesign-accss .xsmall-title {
    font-size: 14px;
    line-height: 24px;
}
.bill-redesign-accss .tooltip-padding {
    padding: 1px 3px 1px 10px;
}
.icon-bill-redesign.icon-question-multi.blue:before {
    color: #00549A;
}

.d-sm-inline-flex {
    display:inline-flex !important;
}
.bill-redesign-accss .loader-fixed .loading-indicator-spinner {
    background-image: url("../img/loading-spinner.png");
    background-repeat: no-repeat;
    display: inline-block;
    width: 37px;
    height: 37px;
    margin-right: 10px;
    vertical-align: middle;
    -webkit-animation: spin 1.1s linear infinite;
    -moz-animation: spin 1.1s linear infinite;
    animation: spin 1.1s linear infinite;
}
body.modalScrollablePad.bill-redesign-accss .bill-redesign #container.modalScrollablePad {
    padding-right: 17px;
}

.bill-redesign .legend {
    padding-left: 7px;
    padding-right: 7px;
    padding-top: 1px;
    padding-bottom: 2px;
    border-radius: 5px;
}


/*new pbe styles*/
.pbe-js-chart {
    padding-top: 40px;
    position: relative;
    border-top: 1px solid #979797;
    overflow: hidden;
}

    .pbe-js-chart:before {
        content: "";
        position: absolute;
        top: -5px;
        left: 0;
        right: 0;
        height: 10px;
        border-left: 1px solid #979797;
        border-right: 1px solid #979797;
    }

    .pbe-js-chart .pbe-chart-container {
        position: absolute;
        width: 100%;
        border-radius: 0 0 5px 5px;
        z-index: 2;
    }

.pbe-labels-white-bg {
    position: absolute;
    width: 100%;
    z-index: 1;
    bottom: 0;
}

.pbe-chart-bars-upper,
.pbe-chart-bars-below {
    position: relative;
    height: 31px;
    border-radius: 5px;
    overflow: hidden;
}

.pbe-chart-bars-upper {
    margin-bottom: 5px;
}

.pbe-chart-bar.legend-only {
    display: flex;
    align-items: center;
    justify-content: center;
}

.pbe-chart-bar:not(.legend-only) .pbe-legend {
    display: none;
}

.legend.bg-stripe-gray-8D {
    background-image: repeating-linear-gradient(135deg, #8D8D8D 0, #8D8D8D 1px, #BABEC2 0, #BABEC2 50%);
    background-size: 10px 10px;
}

.pbe-chart-bar.legend-only.bgBlue .pbe-legend,
.pbe-chart-bar.legend-only.bg-dark-blue-5E .pbe-legend,
.legend.bgBlue,
.legend.bg-dark-blue-5E,
.legend.bg-stripe-gray-8D,
.legend.bg-stripe-blue {
    color: #fff;
}

.pbe-chart-bar.legend-only.bgBlueExtraLight .pbe-legend,
.pbe-chart-bar.legend-only.bg-stripe-blue .pbe-legend,
.legend.bgBlueExtraLight {
    color: #111111;
}

.pbe-chart-transacs {
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
}

.pbe-chart-transac {
    position: absolute;
    width: 8px;
    background: #0075FF;
    border-left: 2px solid #f4f4f4;
    border-right: 2px solid #f4f4f4;
    transform: translateX(-50%);
}

    .pbe-chart-transac.grey {
        background: #555;
    }

.pbe-chart-bar {
    position: absolute;
    top: 0;
}

.pbe-labels-container {
    padding-bottom: 5px;
}

.pbe-label-wrapper {
    background: none !important;
}

.pbe-label-wrapper,
.pbe-label-divider {
    position: relative;
    z-index: 2;
}

.pbe-label-divider {
    position: absolute;
}

    .pbe-label-wrapper.legend-only {
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
    }


    .pbe-label-wrapper.legend-only .pbe-line-indicator,
    .pbe-label-wrapper.legend-only .pbe-label,
    .pbe-label-wrapper:not(.legend-only) .pbe-legend {
        display: none;
    }

    .pbe-labels-container .pbe-label-divider .pbe-label {
        margin-left: 0;
    }

        .pbe-labels-container .pbe-label-divider.is-cropped:after {
            content: '';
            background: #f4f4f4;
            position: absolute;
            left: 0;
            width: 100%;
            height: 5px;
            bottom: 0;
            transform: translateY(100%);
        }

    .pbe-label-divider .surtitle-black,
    .pbe-label-divider .ssmall-text {
        display: inline-flex;
    }

.pbe-chart-container.single-bar ~ .pbe-labels-container {
    padding-bottom: 0px;
}

.pbe-chart-container.single-bar ~ .pbe-labels-container .pbe-label-wrapper {
    display: flex;
    flex-direction: column-reverse;
}

.pbe-line-indicator {
    width: 1px;
    background: #949596;
    position: relative;
}

    .pbe-line-indicator > div {
        min-height: 42px;
    }

    .pbe-line-indicator:after {
        content: '';
        position: absolute;
        left: 50%;
        height: 9px;
        width: 9px;
        border-radius: 50%;
        background-color: #949596;
    }

.pbe-label-top-group,
.pbe-label-bottom-group {
    min-height: 31px;
}

    .pbe-label-top-group .pbe-label-wrapper.legend-only {
        bottom: 6.5px;
    }

    .pbe-label-bottom-group .pbe-label-wrapper.legend-only {
        top: 6.5px;
    }

    .pbe-label-bottom-group .pbe-label-wrapper:only-of-type,
    .pbe-chart-container.single-bar ~ .pbe-labels-container .pbe-label-bottom-group .pbe-label-wrapper.label-wrapper-last {
        align-items: center;
    }

    .single-bar ~ .pbe-labels-container .pbe-label-bottom-group .pbe-label-wrapper.right-position:only-of-type,
    .pbe-chart-container.single-bar ~ .pbe-labels-container .pbe-label-bottom-group .pbe-label-wrapper.right-position.label-wrapper-last {
        align-items: flex-end;
    }

    .pbe-label-top-group .pbe-line-indicator:after,
    .pbe-chart-container.single-bar ~ .pbe-labels-container .pbe-label-bottom-group .pbe-line-indicator:after {
        bottom: 0;
        transform: translate(-50%, 50%);
    }

.pbe-chart-container.single-bar ~ .pbe-labels-container .pbe-label-bottom-group .pbe-line-indicator:after {
    top: auto;
}

.pbe-label-top-group .pbe-line-indicator,
.pbe-label-top-group .pbe-label {
    margin-left: 15px;
}

.pbe-label-bottom-group .pbe-line-indicator,
.pbe-label-bottom-group .pbe-label {
    margin-left: 15px;
}

    .pbe-label-bottom-group .pbe-line-indicator:after {
        top: 0;
        transform: translate(-50%, -50%);
    }

.pbe-label-bottom-group .pbe-label-wrapper:only-child.middle-position .pbe-line-indicator,
.pbe-label-bottom-group .pbe-label-wrapper:only-child.middle-position .pbe-label {
    margin-left: 0px;
}    

.pbe-label-bottom-group .pbe-label-wrapper.right-position .pbe-line-indicator,
.pbe-label-bottom-group .pbe-label-wrapper.right-position .pbe-label {
    margin-left: 15px;
}

.single-bar ~ .pbe-labels-container .pbe-label-bottom-group .pbe-label-wrapper.right-position .pbe-line-indicator,
.single-bar ~ .pbe-labels-container .pbe-label-bottom-group .pbe-label-wrapper.right-position .pbe-label {
    margin-right: 15px;
    text-align: right;
}   

.margin-t-4 {
    margin-top: 4px;
}

.pbe-chart-container:not(.single-bar) ~ .pbe-labels-container .pbe-label-bottom-group {
    background: #FFFFFF;
}

.pbe-labels-container {
    position: relative;
}

.pbe-inner-scrollable .pbe-wrapper {
    gap: 10px;
}

/* label location */
.pbe-label-wrapper.upper,
.pbe-label-wrapper.below {
    display: inline-flex;
    flex-direction: column-reverse;
}

.pbe-chart-container:not(.single-bar) ~ .pbe-labels-container .pbe-label-top-group .pbe-label-wrapper.upper,
.pbe-chart-container:not(.single-bar) ~ .pbe-labels-container .pbe-label-bottom-group .pbe-label-wrapper.below {
    display: block;
}

.pbe-chart-container.single-bar ~ .pbe-labels-container .pbe-label-wrapper.below {
    flex-direction: column;
}

.pbe-label-top-group .pbe-label-wrapper.below .pbe-line-indicator:after,
.pbe-chart-container.single-bar ~ .pbe-labels-container .pbe-label-wrapper.below .pbe-line-indicator:after {
    top: -8.5px;
    bottom: auto;
}

.pbe-label-bottom-group .pbe-label-wrapper.upper .pbe-line-indicator:after {
    top: auto;
}

.pbe-label-top-group .pbe-label-divider:not(.text-wrap) .pbe-label > div {
    white-space: nowrap;
}