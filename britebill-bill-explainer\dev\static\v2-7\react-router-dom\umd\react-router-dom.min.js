!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],e):e(t.ReactRouterDOM={},t.React)}(this,function(t,e){"use strict";function n(t){return t&&t.__esModule?t.default:t}function o(t,e){return e={exports:{}},t(e,e.exports),e.exports}function r(t){return function(){return t}}function i(t){return"/"===t.charAt(0)}function a(t,e){for(var n=e,o=n+1,r=t.length;o<r;n+=1,o+=1)t[n]=t[o];t.pop()}function c(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=t&&t.split("/")||[],o=e&&e.split("/")||[],r=t&&i(t),c=e&&i(e),s=r||c;if(t&&i(t)?o=n:n.length&&(o.pop(),o=o.concat(n)),!o.length)return"/";var u=void 0;if(o.length){var p=o[o.length-1];u="."===p||".."===p||""===p}else u=!1;for(var l=0,f=o.length;f>=0;f--){var h=o[f];"."===h?a(o,f):".."===h?(a(o,f),l++):l&&(a(o,f),l--)}if(!s)for(;l--;l)o.unshift("..");!s||""===o[0]||o[0]&&i(o[0])||o.unshift("");var d=o.join("/");return u&&"/"!==d.substr(-1)&&(d+="/"),d}function s(t,e){if(t===e)return!0;if(null==t||null==e)return!1;if(Array.isArray(t))return Array.isArray(e)&&t.length===e.length&&t.every(function(t,n){return s(t,e[n])});var n=void 0===t?"undefined":ct(t);if(n!==(void 0===e?"undefined":ct(e)))return!1;if("object"===n){var o=t.valueOf(),r=e.valueOf();if(o!==t||r!==e)return s(o,r);var i=Object.keys(t),a=Object.keys(e);return i.length===a.length&&i.every(function(n){return s(t[n],e[n])})}return!1}function u(t){return function(){return t}}function p(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function l(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function f(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}function h(t){return"/"===t.charAt(0)}function d(t,e){for(var n=e,o=n+1,r=t.length;o<r;n+=1,o+=1)t[n]=t[o];t.pop()}function y(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=t&&t.split("/")||[],o=e&&e.split("/")||[],r=t&&h(t),i=e&&h(e),a=r||i;if(t&&h(t)?o=n:n.length&&(o.pop(),o=o.concat(n)),!o.length)return"/";var c=void 0;if(o.length){var s=o[o.length-1];c="."===s||".."===s||""===s}else c=!1;for(var u=0,p=o.length;p>=0;p--){var l=o[p];"."===l?d(o,p):".."===l?(d(o,p),u++):u&&(d(o,p),u--)}if(!a)for(;u--;u)o.unshift("..");!a||""===o[0]||o[0]&&h(o[0])||o.unshift("");var f=o.join("/");return c&&"/"!==f.substr(-1)&&(f+="/"),f}function v(t,e){if(t===e)return!0;if(null==t||null==e)return!1;if(Array.isArray(t))return Array.isArray(e)&&t.length===e.length&&t.every(function(t,n){return v(t,e[n])});var n=void 0===t?"undefined":Ft(t);if(n!==(void 0===e?"undefined":Ft(e)))return!1;if("object"===n){var o=t.valueOf(),r=e.valueOf();if(o!==t||r!==e)return v(o,r);var i=Object.keys(t),a=Object.keys(e);return i.length===a.length&&i.every(function(n){return v(t[n],e[n])})}return!1}function m(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function b(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function g(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}function w(t,e){for(var n,o=[],r=0,i=0,a="",c=e&&e.delimiter||"/";null!=(n=oe.exec(t));){var s=n[0],u=n[1],p=n.index;if(a+=t.slice(i,p),i=p+s.length,u)a+=u[1];else{var l=t[i],f=n[2],h=n[3],d=n[4],y=n[5],v=n[6],m=n[7];a&&(o.push(a),a="");var b=null!=f&&null!=l&&l!==f,g="+"===v||"*"===v,w="?"===v||"*"===v,O=n[2]||c,P=d||y;o.push({name:h||r++,prefix:f||"",delimiter:O,optional:w,repeat:g,partial:b,asterisk:!!m,pattern:P?E(P):m?".*":"[^"+R(O)+"]+?"})}}return i<t.length&&(a+=t.substr(i)),a&&o.push(a),o}function O(t){return encodeURI(t).replace(/[\/?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}function P(t){return encodeURI(t).replace(/[?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}function x(t){for(var e=new Array(t.length),n=0;n<t.length;n++)"object"==typeof t[n]&&(e[n]=new RegExp("^(?:"+t[n].pattern+")$"));return function(n,o){for(var r="",i=n||{},a=(o||{}).pretty?O:encodeURIComponent,c=0;c<t.length;c++){var s=t[c];if("string"!=typeof s){var u,p=i[s.name];if(null==p){if(s.optional){s.partial&&(r+=s.prefix);continue}throw new TypeError('Expected "'+s.name+'" to be defined')}if(Xt(p)){if(!s.repeat)throw new TypeError('Expected "'+s.name+'" to not repeat, but received `'+JSON.stringify(p)+"`");if(0===p.length){if(s.optional)continue;throw new TypeError('Expected "'+s.name+'" to not be empty')}for(var l=0;l<p.length;l++){if(u=a(p[l]),!e[c].test(u))throw new TypeError('Expected all "'+s.name+'" to match "'+s.pattern+'", but received `'+JSON.stringify(u)+"`");r+=(0===l?s.prefix:s.delimiter)+u}}else{if(u=s.asterisk?P(p):a(p),!e[c].test(u))throw new TypeError('Expected "'+s.name+'" to match "'+s.pattern+'", but received "'+u+'"');r+=s.prefix+u}}else r+=s}return r}}function R(t){return t.replace(/([.+*?=^!:${}()[\]|\/\\])/g,"\\$1")}function E(t){return t.replace(/([=!:$\/()])/g,"\\$1")}function j(t,e){return t.keys=e,t}function T(t){return t.sensitive?"":"i"}function S(t,e){var n=t.source.match(/\((?!\?)/g);if(n)for(var o=0;o<n.length;o++)e.push({name:o,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,asterisk:!1,pattern:null});return j(t,e)}function _(t,e,n){for(var o=[],r=0;r<t.length;r++)o.push(A(t[r],e,n).source);return j(new RegExp("(?:"+o.join("|")+")",T(n)),e)}function C(t,e,n){return k(w(t,n),e,n)}function k(t,e,n){Xt(e)||(n=e||n,e=[]);for(var o=(n=n||{}).strict,r=!1!==n.end,i="",a=0;a<t.length;a++){var c=t[a];if("string"==typeof c)i+=R(c);else{var s=R(c.prefix),u="(?:"+c.pattern+")";e.push(c),c.repeat&&(u+="(?:"+s+u+")*"),i+=u=c.optional?c.partial?s+"("+u+")?":"(?:"+s+"("+u+"))?":s+"("+u+")"}}var p=R(n.delimiter||"/"),l=i.slice(-p.length)===p;return o||(i=(l?i.slice(0,-p.length):i)+"(?:"+p+"(?=$))?"),i+=r?"$":o&&l?"":"(?="+p+"|$)",j(new RegExp("^"+i,T(n)),e)}function A(t,e,n){return Xt(e)||(n=e||n,e=[]),n=n||{},t instanceof RegExp?S(t,e):Xt(t)?_(t,e,n):C(t,e,n)}function L(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function M(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function U(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}function q(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function H(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function I(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}function Y(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function B(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function W(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}function N(t,e){var n={};for(var o in t)e.indexOf(o)>=0||Object.prototype.hasOwnProperty.call(t,o)&&(n[o]=t[o]);return n}function D(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function F(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function $(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}function V(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function z(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function K(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}function G(t,e){var n={};for(var o in t)e.indexOf(o)>=0||Object.prototype.hasOwnProperty.call(t,o)&&(n[o]=t[o]);return n}e=e&&e.hasOwnProperty("default")?e.default:e;var J=function(){},Q=J,X=function(){};X.thatReturns=r,X.thatReturnsFalse=r(!1),X.thatReturnsTrue=r(!0),X.thatReturnsNull=r(null),X.thatReturnsThis=function(){return this},X.thatReturnsArgument=function(t){return t};var Z=X,tt=function(t){},et=function(t,e,n,o,r,i,a,c){if(tt(e),!t){var s;if(void 0===e)s=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var u=[n,o,r,i,a,c],p=0;(s=new Error(e.replace(/%s/g,function(){return u[p++]}))).name="Invariant Violation"}throw s.framesToPop=1,s}},nt="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",ot=function(){function t(t,e,n,o,r,i){i!==nt&&et(!1,"Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types")}function e(){return t}t.isRequired=t;var n={array:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e};return n.checkPropTypes=Z,n.PropTypes=n,n},rt=o(function(t){t.exports=ot()}),it=function(t,e,n,o,r,i,a,c){if(!t){var s;if(void 0===e)s=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var u=[n,o,r,i,a,c],p=0;(s=new Error(e.replace(/%s/g,function(){return u[p++]}))).name="Invariant Violation"}throw s.framesToPop=1,s}},at=Object.freeze({default:c}),ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},st=Object.freeze({default:s}),ut=o(function(t,e){e.__esModule=!0;e.addLeadingSlash=function(t){return"/"===t.charAt(0)?t:"/"+t},e.stripLeadingSlash=function(t){return"/"===t.charAt(0)?t.substr(1):t};var n=e.hasBasename=function(t,e){return new RegExp("^"+e+"(\\/|\\?|#|$)","i").test(t)};e.stripBasename=function(t,e){return n(t,e)?t.substr(e.length):t},e.stripTrailingSlash=function(t){return"/"===t.charAt(t.length-1)?t.slice(0,-1):t},e.parsePath=function(t){var e=t||"/",n="",o="",r=e.indexOf("#");-1!==r&&(o=e.substr(r),e=e.substr(0,r));var i=e.indexOf("?");return-1!==i&&(n=e.substr(i),e=e.substr(0,i)),{pathname:e,search:"?"===n?"":n,hash:"#"===o?"":o}},e.createPath=function(t){var e=t.pathname,n=t.search,o=t.hash,r=e||"/";return n&&"?"!==n&&(r+="?"===n.charAt(0)?n:"?"+n),o&&"#"!==o&&(r+="#"===o.charAt(0)?o:"#"+o),r}}),pt=at&&c||at,lt=st&&s||st,ft=o(function(t,e){function n(t){return t&&t.__esModule?t:{default:t}}e.__esModule=!0,e.locationsAreEqual=e.createLocation=void 0;var o=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},r=n(pt),i=n(lt);e.createLocation=function(t,e,n,i){var a=void 0;"string"==typeof t?(a=(0,ut.parsePath)(t)).state=e:(void 0===(a=o({},t)).pathname&&(a.pathname=""),a.search?"?"!==a.search.charAt(0)&&(a.search="?"+a.search):a.search="",a.hash?"#"!==a.hash.charAt(0)&&(a.hash="#"+a.hash):a.hash="",void 0!==e&&void 0===a.state&&(a.state=e));try{a.pathname=decodeURI(a.pathname)}catch(t){throw t instanceof URIError?new URIError('Pathname "'+a.pathname+'" could not be decoded. This is likely caused by an invalid percent-encoding.'):t}return n&&(a.key=n),i?a.pathname?"/"!==a.pathname.charAt(0)&&(a.pathname=(0,r.default)(a.pathname,i.pathname)):a.pathname=i.pathname:a.pathname||(a.pathname="/"),a},e.locationsAreEqual=function(t,e){return t.pathname===e.pathname&&t.search===e.search&&t.hash===e.hash&&t.key===e.key&&(0,i.default)(t.state,e.state)}}),ht=o(function(t,e){e.__esModule=!0;var n=function(t){return t&&t.__esModule?t:{default:t}}(Q);e.default=function(){var t=null,e=[];return{setPrompt:function(e){return(0,n.default)(null==t,"A history supports only one prompt at a time"),t=e,function(){t===e&&(t=null)}},confirmTransitionTo:function(e,o,r,i){if(null!=t){var a="function"==typeof t?t(e,o):t;"string"==typeof a?"function"==typeof r?r(a,i):((0,n.default)(!1,"A history needs a getUserConfirmation function in order to use a prompt message"),i(!0)):i(!1!==a)}else i(!0)},appendListener:function(t){var n=!0,o=function(){n&&t.apply(void 0,arguments)};return e.push(o),function(){n=!1,e=e.filter(function(t){return t!==o})}},notifyListeners:function(){for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];e.forEach(function(t){return t.apply(void 0,n)})}}}}),dt=o(function(t,e){e.__esModule=!0;e.canUseDOM=!("undefined"==typeof window||!window.document||!window.document.createElement),e.addEventListener=function(t,e,n){return t.addEventListener?t.addEventListener(e,n,!1):t.attachEvent("on"+e,n)},e.removeEventListener=function(t,e,n){return t.removeEventListener?t.removeEventListener(e,n,!1):t.detachEvent("on"+e,n)},e.getConfirmation=function(t,e){return e(window.confirm(t))},e.supportsHistory=function(){var t=window.navigator.userAgent;return(-1===t.indexOf("Android 2.")&&-1===t.indexOf("Android 4.0")||-1===t.indexOf("Mobile Safari")||-1!==t.indexOf("Chrome")||-1!==t.indexOf("Windows Phone"))&&(window.history&&"pushState"in window.history)},e.supportsPopStateOnHashChange=function(){return-1===window.navigator.userAgent.indexOf("Trident")},e.supportsGoWithoutReloadUsingHash=function(){return-1===window.navigator.userAgent.indexOf("Firefox")},e.isExtraneousPopstateEvent=function(t){return void 0===t.state&&-1===navigator.userAgent.indexOf("CriOS")}}),yt=n(o(function(t,e){function n(t){return t&&t.__esModule?t:{default:t}}e.__esModule=!0;var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},i=n(Q),a=n(it),c=n(ht),s=function(){try{return window.history.state||{}}catch(t){return{}}};e.default=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,a.default)(dt.canUseDOM,"Browser history needs a DOM");var e=window.history,n=(0,dt.supportsHistory)(),u=!(0,dt.supportsPopStateOnHashChange)(),p=t.forceRefresh,l=void 0!==p&&p,f=t.getUserConfirmation,h=void 0===f?dt.getConfirmation:f,d=t.keyLength,y=void 0===d?6:d,v=t.basename?(0,ut.stripTrailingSlash)((0,ut.addLeadingSlash)(t.basename)):"",m=function(t){var e=t||{},n=e.key,o=e.state,r=window.location,a=r.pathname+r.search+r.hash;return(0,i.default)(!v||(0,ut.hasBasename)(a,v),'You are attempting to use a basename on a page whose URL path does not begin with the basename. Expected path "'+a+'" to begin with "'+v+'".'),v&&(a=(0,ut.stripBasename)(a,v)),(0,ft.createLocation)(a,o,n)},b=function(){return Math.random().toString(36).substr(2,y)},g=(0,c.default)(),w=function(t){r(L,t),L.length=e.length,g.notifyListeners(L.location,L.action)},O=function(t){(0,dt.isExtraneousPopstateEvent)(t)||R(m(t.state))},P=function(){R(m(s()))},x=!1,R=function(t){x?(x=!1,w()):g.confirmTransitionTo(t,"POP",h,function(e){e?w({action:"POP",location:t}):E(t)})},E=function(t){var e=L.location,n=T.indexOf(e.key);-1===n&&(n=0);var o=T.indexOf(t.key);-1===o&&(o=0);var r=n-o;r&&(x=!0,_(r))},j=m(s()),T=[j.key],S=function(t){return v+(0,ut.createPath)(t)},_=function(t){e.go(t)},C=0,k=function(t){1===(C+=t)?((0,dt.addEventListener)(window,"popstate",O),u&&(0,dt.addEventListener)(window,"hashchange",P)):0===C&&((0,dt.removeEventListener)(window,"popstate",O),u&&(0,dt.removeEventListener)(window,"hashchange",P))},A=!1,L={length:e.length,action:"POP",location:j,createHref:S,push:function(t,r){(0,i.default)(!("object"===(void 0===t?"undefined":o(t))&&void 0!==t.state&&void 0!==r),"You should avoid providing a 2nd state argument to push when the 1st argument is a location-like object that already has state; it is ignored");var a=(0,ft.createLocation)(t,r,b(),L.location);g.confirmTransitionTo(a,"PUSH",h,function(t){if(t){var o=S(a),r=a.key,c=a.state;if(n)if(e.pushState({key:r,state:c},null,o),l)window.location.href=o;else{var s=T.indexOf(L.location.key),u=T.slice(0,-1===s?0:s+1);u.push(a.key),T=u,w({action:"PUSH",location:a})}else(0,i.default)(void 0===c,"Browser history cannot push state in browsers that do not support HTML5 history"),window.location.href=o}})},replace:function(t,r){(0,i.default)(!("object"===(void 0===t?"undefined":o(t))&&void 0!==t.state&&void 0!==r),"You should avoid providing a 2nd state argument to replace when the 1st argument is a location-like object that already has state; it is ignored");var a=(0,ft.createLocation)(t,r,b(),L.location);g.confirmTransitionTo(a,"REPLACE",h,function(t){if(t){var o=S(a),r=a.key,c=a.state;if(n)if(e.replaceState({key:r,state:c},null,o),l)window.location.replace(o);else{var s=T.indexOf(L.location.key);-1!==s&&(T[s]=a.key),w({action:"REPLACE",location:a})}else(0,i.default)(void 0===c,"Browser history cannot replace state in browsers that do not support HTML5 history"),window.location.replace(o)}})},go:_,goBack:function(){return _(-1)},goForward:function(){return _(1)},block:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=g.setPrompt(t);return A||(k(1),A=!0),function(){return A&&(A=!1,k(-1)),e()}},listen:function(t){var e=g.appendListener(t);return k(1),function(){k(-1),e()}}};return L}})),vt=function(){},mt=vt,bt=function(t,e,n,o,r,i,a,c){if(!t){var s;if(void 0===e)s=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var u=[n,o,r,i,a,c],p=0;(s=new Error(e.replace(/%s/g,function(){return u[p++]}))).name="Invariant Violation"}throw s.framesToPop=1,s}},gt=function(){};gt.thatReturns=u,gt.thatReturnsFalse=u(!1),gt.thatReturnsTrue=u(!0),gt.thatReturnsNull=u(null),gt.thatReturnsThis=function(){return this},gt.thatReturnsArgument=function(t){return t};var wt=gt,Ot=function(t){},Pt=function(t,e,n,o,r,i,a,c){if(Ot(e),!t){var s;if(void 0===e)s=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var u=[n,o,r,i,a,c],p=0;(s=new Error(e.replace(/%s/g,function(){return u[p++]}))).name="Invariant Violation"}throw s.framesToPop=1,s}},xt="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",Rt=function(){function t(t,e,n,o,r,i){i!==xt&&Pt(!1,"Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types")}function e(){return t}t.isRequired=t;var n={array:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e};return n.checkPropTypes=wt,n.PropTypes=n,n},Et=o(function(t){t.exports=Rt()}),jt=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},Tt=function(t){function n(){var e,o,r;p(this,n);for(var i=arguments.length,a=Array(i),c=0;c<i;c++)a[c]=arguments[c];return e=o=l(this,t.call.apply(t,[this].concat(a))),o.state={match:o.computeMatch(o.props.history.location.pathname)},r=e,l(o,r)}return f(n,t),n.prototype.getChildContext=function(){return{router:jt({},this.context.router,{history:this.props.history,route:{location:this.props.history.location,match:this.state.match}})}},n.prototype.computeMatch=function(t){return{path:"/",url:"/",params:{},isExact:"/"===t}},n.prototype.componentWillMount=function(){var t=this,n=this.props,o=n.children,r=n.history;bt(null==o||1===e.Children.count(o),"A <Router> may have only one child element"),this.unlisten=r.listen(function(){t.setState({match:t.computeMatch(r.location.pathname)})})},n.prototype.componentWillReceiveProps=function(t){mt(this.props.history===t.history,"You cannot change <Router history>")},n.prototype.componentWillUnmount=function(){this.unlisten()},n.prototype.render=function(){var t=this.props.children;return t?e.Children.only(t):null},n}(e.Component);Tt.propTypes={history:Et.object.isRequired,children:Et.node},Tt.contextTypes={router:Et.object},Tt.childContextTypes={router:Et.object.isRequired};var St="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_t=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},Ct=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},kt=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)},At=function(t,e){var n={};for(var o in t)e.indexOf(o)>=0||Object.prototype.hasOwnProperty.call(t,o)&&(n[o]=t[o]);return n},Lt=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e},Mt=function(t){function n(){var e,o,r;_t(this,n);for(var i=arguments.length,a=Array(i),c=0;c<i;c++)a[c]=arguments[c];return e=o=Lt(this,t.call.apply(t,[this].concat(a))),o.history=yt(o.props),r=e,Lt(o,r)}return kt(n,t),n.prototype.componentWillMount=function(){},n.prototype.render=function(){return e.createElement(Tt,{history:this.history,children:this.props.children})},n}(e.Component),Ut=n(o(function(t,e){function n(t){return t&&t.__esModule?t:{default:t}}e.__esModule=!0;var o=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},r=n(Q),i=n(it),a=n(ht),c={hashbang:{encodePath:function(t){return"!"===t.charAt(0)?t:"!/"+(0,ut.stripLeadingSlash)(t)},decodePath:function(t){return"!"===t.charAt(0)?t.substr(1):t}},noslash:{encodePath:ut.stripLeadingSlash,decodePath:ut.addLeadingSlash},slash:{encodePath:ut.addLeadingSlash,decodePath:ut.addLeadingSlash}},s=function(){var t=window.location.href,e=t.indexOf("#");return-1===e?"":t.substring(e+1)},u=function(t){return window.location.hash=t},p=function(t){var e=window.location.href.indexOf("#");window.location.replace(window.location.href.slice(0,e>=0?e:0)+"#"+t)};e.default=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,i.default)(dt.canUseDOM,"Hash history needs a DOM");var e=window.history,n=(0,dt.supportsGoWithoutReloadUsingHash)(),l=t.getUserConfirmation,f=void 0===l?dt.getConfirmation:l,h=t.hashType,d=void 0===h?"slash":h,y=t.basename?(0,ut.stripTrailingSlash)((0,ut.addLeadingSlash)(t.basename)):"",v=c[d],m=v.encodePath,b=v.decodePath,g=function(){var t=b(s());return(0,r.default)(!y||(0,ut.hasBasename)(t,y),'You are attempting to use a basename on a page whose URL path does not begin with the basename. Expected path "'+t+'" to begin with "'+y+'".'),y&&(t=(0,ut.stripBasename)(t,y)),(0,ft.createLocation)(t)},w=(0,a.default)(),O=function(t){o(U,t),U.length=e.length,w.notifyListeners(U.location,U.action)},P=!1,x=null,R=function(){var t=s(),e=m(t);if(t!==e)p(e);else{var n=g(),o=U.location;if(!P&&(0,ft.locationsAreEqual)(o,n))return;if(x===(0,ut.createPath)(n))return;x=null,E(n)}},E=function(t){P?(P=!1,O()):w.confirmTransitionTo(t,"POP",f,function(e){e?O({action:"POP",location:t}):j(t)})},j=function(t){var e=U.location,n=C.lastIndexOf((0,ut.createPath)(e));-1===n&&(n=0);var o=C.lastIndexOf((0,ut.createPath)(t));-1===o&&(o=0);var r=n-o;r&&(P=!0,k(r))},T=s(),S=m(T);T!==S&&p(S);var _=g(),C=[(0,ut.createPath)(_)],k=function(t){(0,r.default)(n,"Hash history go(n) causes a full page reload in this browser"),e.go(t)},A=0,L=function(t){1===(A+=t)?(0,dt.addEventListener)(window,"hashchange",R):0===A&&(0,dt.removeEventListener)(window,"hashchange",R)},M=!1,U={length:e.length,action:"POP",location:_,createHref:function(t){return"#"+m(y+(0,ut.createPath)(t))},push:function(t,e){(0,r.default)(void 0===e,"Hash history cannot push state; it is ignored");var n=(0,ft.createLocation)(t,void 0,void 0,U.location);w.confirmTransitionTo(n,"PUSH",f,function(t){if(t){var e=(0,ut.createPath)(n),o=m(y+e);if(s()!==o){x=e,u(o);var i=C.lastIndexOf((0,ut.createPath)(U.location)),a=C.slice(0,-1===i?0:i+1);a.push(e),C=a,O({action:"PUSH",location:n})}else(0,r.default)(!1,"Hash history cannot PUSH the same path; a new entry will not be added to the history stack"),O()}})},replace:function(t,e){(0,r.default)(void 0===e,"Hash history cannot replace state; it is ignored");var n=(0,ft.createLocation)(t,void 0,void 0,U.location);w.confirmTransitionTo(n,"REPLACE",f,function(t){if(t){var e=(0,ut.createPath)(n),o=m(y+e);s()!==o&&(x=e,p(o));var r=C.indexOf((0,ut.createPath)(U.location));-1!==r&&(C[r]=e),O({action:"REPLACE",location:n})}})},go:k,goBack:function(){return k(-1)},goForward:function(){return k(1)},block:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=w.setPrompt(t);return M||(L(1),M=!0),function(){return M&&(M=!1,L(-1)),e()}},listen:function(t){var e=w.appendListener(t);return L(1),function(){L(-1),e()}}};return U}})),qt=function(t){function n(){var e,o,r;_t(this,n);for(var i=arguments.length,a=Array(i),c=0;c<i;c++)a[c]=arguments[c];return e=o=Lt(this,t.call.apply(t,[this].concat(a))),o.history=Ut(o.props),r=e,Lt(o,r)}return kt(n,t),n.prototype.componentWillMount=function(){},n.prototype.render=function(){return e.createElement(Tt,{history:this.history,children:this.props.children})},n}(e.Component),Ht=function(t){return!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)},It=function(t){function n(){var e,o,r;_t(this,n);for(var i=arguments.length,a=Array(i),c=0;c<i;c++)a[c]=arguments[c];return e=o=Lt(this,t.call.apply(t,[this].concat(a))),o.handleClick=function(t){if(o.props.onClick&&o.props.onClick(t),!t.defaultPrevented&&0===t.button&&!o.props.target&&!Ht(t)){t.preventDefault();var e=o.context.router.history,n=o.props,r=n.replace,i=n.to;r?e.replace(i):e.push(i)}},r=e,Lt(o,r)}return kt(n,t),n.prototype.render=function(){var t=this.props,n=(t.replace,t.to),o=t.innerRef,r=At(t,["replace","to","innerRef"]);this.context.router||it(!1);var i=this.context.router.history.createHref("string"==typeof n?{pathname:n}:n);return e.createElement("a",Ct({},r,{onClick:this.handleClick,href:i,ref:o}))},n}(e.Component);It.defaultProps={replace:!1},It.contextTypes={router:rt.shape({history:rt.shape({push:rt.func.isRequired,replace:rt.func.isRequired,createHref:rt.func.isRequired}).isRequired}).isRequired};var Yt=o(function(t,e){e.__esModule=!0;e.addLeadingSlash=function(t){return"/"===t.charAt(0)?t:"/"+t},e.stripLeadingSlash=function(t){return"/"===t.charAt(0)?t.substr(1):t};var n=e.hasBasename=function(t,e){return new RegExp("^"+e+"(\\/|\\?|#|$)","i").test(t)};e.stripBasename=function(t,e){return n(t,e)?t.substr(e.length):t},e.stripTrailingSlash=function(t){return"/"===t.charAt(t.length-1)?t.slice(0,-1):t},e.parsePath=function(t){var e=t||"/",n="",o="",r=e.indexOf("#");-1!==r&&(o=e.substr(r),e=e.substr(0,r));var i=e.indexOf("?");return-1!==i&&(n=e.substr(i),e=e.substr(0,i)),{pathname:e,search:"?"===n?"":n,hash:"#"===o?"":o}},e.createPath=function(t){var e=t.pathname,n=t.search,o=t.hash,r=e||"/";return n&&"?"!==n&&(r+="?"===n.charAt(0)?n:"?"+n),o&&"#"!==o&&(r+="#"===o.charAt(0)?o:"#"+o),r}}),Bt=Yt.addLeadingSlash,Wt=Yt.parsePath,Nt=Yt.createPath,Dt=Object.freeze({default:y}),Ft="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$t=Object.freeze({default:v}),Vt=Dt&&y||Dt,zt=$t&&v||$t,Kt=o(function(t,e){function n(t){return t&&t.__esModule?t:{default:t}}e.__esModule=!0,e.locationsAreEqual=e.createLocation=void 0;var o=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},r=n(Vt),i=n(zt);e.createLocation=function(t,e,n,i){var a=void 0;"string"==typeof t?(a=(0,Yt.parsePath)(t)).state=e:(void 0===(a=o({},t)).pathname&&(a.pathname=""),a.search?"?"!==a.search.charAt(0)&&(a.search="?"+a.search):a.search="",a.hash?"#"!==a.hash.charAt(0)&&(a.hash="#"+a.hash):a.hash="",void 0!==e&&void 0===a.state&&(a.state=e));try{a.pathname=decodeURI(a.pathname)}catch(t){throw t instanceof URIError?new URIError('Pathname "'+a.pathname+'" could not be decoded. This is likely caused by an invalid percent-encoding.'):t}return n&&(a.key=n),i?a.pathname?"/"!==a.pathname.charAt(0)&&(a.pathname=(0,r.default)(a.pathname,i.pathname)):a.pathname=i.pathname:a.pathname||(a.pathname="/"),a},e.locationsAreEqual=function(t,e){return t.pathname===e.pathname&&t.search===e.search&&t.hash===e.hash&&t.key===e.key&&(0,i.default)(t.state,e.state)}}),Gt=o(function(t,e){e.__esModule=!0;var n=function(t){return t&&t.__esModule?t:{default:t}}(mt);e.default=function(){var t=null,e=[];return{setPrompt:function(e){return(0,n.default)(null==t,"A history supports only one prompt at a time"),t=e,function(){t===e&&(t=null)}},confirmTransitionTo:function(e,o,r,i){if(null!=t){var a="function"==typeof t?t(e,o):t;"string"==typeof a?"function"==typeof r?r(a,i):((0,n.default)(!1,"A history needs a getUserConfirmation function in order to use a prompt message"),i(!0)):i(!1!==a)}else i(!0)},appendListener:function(t){var n=!0,o=function(){n&&t.apply(void 0,arguments)};return e.push(o),function(){n=!1,e=e.filter(function(t){return t!==o})}},notifyListeners:function(){for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];e.forEach(function(t){return t.apply(void 0,n)})}}}}),Jt=n(o(function(t,e){function n(t){return t&&t.__esModule?t:{default:t}}e.__esModule=!0;var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},i=n(mt),a=n(Gt),c=function(t,e,n){return Math.min(Math.max(t,e),n)};e.default=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.getUserConfirmation,n=t.initialEntries,s=void 0===n?["/"]:n,u=t.initialIndex,p=void 0===u?0:u,l=t.keyLength,f=void 0===l?6:l,h=(0,a.default)(),d=function(t){r(w,t),w.length=w.entries.length,h.notifyListeners(w.location,w.action)},y=function(){return Math.random().toString(36).substr(2,f)},v=c(p,0,s.length-1),m=s.map(function(t){return"string"==typeof t?(0,Kt.createLocation)(t,void 0,y()):(0,Kt.createLocation)(t,void 0,t.key||y())}),b=Yt.createPath,g=function(t){var n=c(w.index+t,0,w.entries.length-1),o=w.entries[n];h.confirmTransitionTo(o,"POP",e,function(t){t?d({action:"POP",location:o,index:n}):d()})},w={length:m.length,action:"POP",location:m[v],index:v,entries:m,createHref:b,push:function(t,n){(0,i.default)(!("object"===(void 0===t?"undefined":o(t))&&void 0!==t.state&&void 0!==n),"You should avoid providing a 2nd state argument to push when the 1st argument is a location-like object that already has state; it is ignored");var r=(0,Kt.createLocation)(t,n,y(),w.location);h.confirmTransitionTo(r,"PUSH",e,function(t){if(t){var e=w.index+1,n=w.entries.slice(0);n.length>e?n.splice(e,n.length-e,r):n.push(r),d({action:"PUSH",location:r,index:e,entries:n})}})},replace:function(t,n){(0,i.default)(!("object"===(void 0===t?"undefined":o(t))&&void 0!==t.state&&void 0!==n),"You should avoid providing a 2nd state argument to replace when the 1st argument is a location-like object that already has state; it is ignored");var r=(0,Kt.createLocation)(t,n,y(),w.location);h.confirmTransitionTo(r,"REPLACE",e,function(t){t&&(w.entries[w.index]=r,d({action:"REPLACE",location:r}))})},go:g,goBack:function(){return g(-1)},goForward:function(){return g(1)},canGo:function(t){var e=w.index+t;return e>=0&&e<w.entries.length},block:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return h.setPrompt(t)},listen:function(t){return h.appendListener(t)}};return w}})),Qt=function(t){function n(){var e,o,r;m(this,n);for(var i=arguments.length,a=Array(i),c=0;c<i;c++)a[c]=arguments[c];return e=o=b(this,t.call.apply(t,[this].concat(a))),o.history=Jt(o.props),r=e,b(o,r)}return g(n,t),n.prototype.componentWillMount=function(){mt(!this.props.history,"<MemoryRouter> ignores the history prop. To use a custom history, use `import { Router }` instead of `import { MemoryRouter as Router }`.")},n.prototype.render=function(){return e.createElement(Tt,{history:this.history,children:this.props.children})},n}(e.Component);Qt.propTypes={initialEntries:Et.array,initialIndex:Et.number,getUserConfirmation:Et.func,keyLength:Et.number,children:Et.node};var Xt=Array.isArray||function(t){return"[object Array]"==Object.prototype.toString.call(t)},Zt=A,te=w,ee=x,ne=k,oe=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");Zt.parse=te,Zt.compile=function(t,e){return x(w(t,e))},Zt.tokensToFunction=ee,Zt.tokensToRegExp=ne;var re={},ie=0,ae=function(t,e){var n=""+e.end+e.strict+e.sensitive,o=re[n]||(re[n]={});if(o[t])return o[t];var r=[],i={re:Zt(t,r,e),keys:r};return ie<1e4&&(o[t]=i,ie++),i},ce=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"string"==typeof e&&(e={path:e});var n=e,o=n.path,r=void 0===o?"/":o,i=n.exact,a=void 0!==i&&i,c=n.strict,s=void 0!==c&&c,u=n.sensitive,p=ae(r,{end:a,strict:s,sensitive:void 0!==u&&u}),l=p.re,f=p.keys,h=l.exec(t);if(!h)return null;var d=h[0],y=h.slice(1),v=t===d;return a&&!v?null:{path:r,url:"/"===r&&""===d?"/":d,isExact:v,params:f.reduce(function(t,e,n){return t[e.name]=y[n],t},{})}},se=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},ue=function(t){return 0===e.Children.count(t)},pe=function(t){function n(){var e,o,r;L(this,n);for(var i=arguments.length,a=Array(i),c=0;c<i;c++)a[c]=arguments[c];return e=o=M(this,t.call.apply(t,[this].concat(a))),o.state={match:o.computeMatch(o.props,o.context.router)},r=e,M(o,r)}return U(n,t),n.prototype.getChildContext=function(){return{router:se({},this.context.router,{route:{location:this.props.location||this.context.router.route.location,match:this.state.match}})}},n.prototype.computeMatch=function(t,e){var n=t.computedMatch,o=t.location,r=t.path,i=t.strict,a=t.exact,c=t.sensitive;if(n)return n;bt(e,"You should not use <Route> or withRouter() outside a <Router>");var s=e.route,u=(o||s.location).pathname;return r?ce(u,{path:r,strict:i,exact:a,sensitive:c}):s.match},n.prototype.componentWillMount=function(){mt(!(this.props.component&&this.props.render),"You should not use <Route component> and <Route render> in the same route; <Route render> will be ignored"),mt(!(this.props.component&&this.props.children&&!ue(this.props.children)),"You should not use <Route component> and <Route children> in the same route; <Route children> will be ignored"),mt(!(this.props.render&&this.props.children&&!ue(this.props.children)),"You should not use <Route render> and <Route children> in the same route; <Route children> will be ignored")},n.prototype.componentWillReceiveProps=function(t,e){mt(!(t.location&&!this.props.location),'<Route> elements should not change from uncontrolled to controlled (or vice versa). You initially used no "location" prop and then provided one on a subsequent render.'),mt(!(!t.location&&this.props.location),'<Route> elements should not change from controlled to uncontrolled (or vice versa). You provided a "location" prop initially but omitted it on a subsequent render.'),this.setState({match:this.computeMatch(t,e.router)})},n.prototype.render=function(){var t=this.state.match,n=this.props,o=n.children,r=n.component,i=n.render,a=this.context.router,c=a.history,s=a.route,u=a.staticContext,p={match:t,location:this.props.location||s.location,history:c,staticContext:u};return r?t?e.createElement(r,p):null:i?t?i(p):null:o?"function"==typeof o?o(p):ue(o)?null:e.Children.only(o):null},n}(e.Component);pe.propTypes={computedMatch:Et.object,path:Et.string,exact:Et.bool,strict:Et.bool,sensitive:Et.bool,component:Et.func,render:Et.func,children:Et.oneOfType([Et.func,Et.node]),location:Et.object},pe.contextTypes={router:Et.shape({history:Et.object.isRequired,route:Et.object.isRequired,staticContext:Et.object})},pe.childContextTypes={router:Et.object.isRequired};var le=function(t){var n=t.to,o=t.exact,r=t.strict,i=t.location,a=t.activeClassName,c=t.className,s=t.activeStyle,u=t.style,p=t.isActive,l=t.ariaCurrent,f=At(t,["to","exact","strict","location","activeClassName","className","activeStyle","style","isActive","ariaCurrent"]);return e.createElement(pe,{path:"object"===(void 0===n?"undefined":St(n))?n.pathname:n,exact:o,strict:r,location:i,children:function(t){var o=t.location,r=t.match,i=!!(p?p(r,o):r);return e.createElement(It,Ct({to:n,className:i?[c,a].filter(function(t){return t}).join(" "):c,style:i?Ct({},u,s):u,"aria-current":i&&l},f))}})};le.defaultProps={activeClassName:"active",ariaCurrent:"true"};var fe=function(t){function e(){return q(this,e),H(this,t.apply(this,arguments))}return I(e,t),e.prototype.enable=function(t){this.unblock&&this.unblock(),this.unblock=this.context.router.history.block(t)},e.prototype.disable=function(){this.unblock&&(this.unblock(),this.unblock=null)},e.prototype.componentWillMount=function(){bt(this.context.router,"You should not use <Prompt> outside a <Router>"),this.props.when&&this.enable(this.props.message)},e.prototype.componentWillReceiveProps=function(t){t.when?this.props.when&&this.props.message===t.message||this.enable(t.message):this.disable()},e.prototype.componentWillUnmount=function(){this.disable()},e.prototype.render=function(){return null},e}(e.Component);fe.propTypes={when:Et.bool,message:Et.oneOfType([Et.func,Et.string]).isRequired},fe.defaultProps={when:!0},fe.contextTypes={router:Et.shape({history:Et.shape({block:Et.func.isRequired}).isRequired}).isRequired};var he=function(t){var e=t||"/",n="",o="",r=e.indexOf("#");-1!==r&&(o=e.substr(r),e=e.substr(0,r));var i=e.indexOf("?");return-1!==i&&(n=e.substr(i),e=e.substr(0,i)),{pathname:e,search:"?"===n?"":n,hash:"#"===o?"":o}},de=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},ye=function(t,e,n,o){var r=void 0;"string"==typeof t?(r=he(t)).state=e:(void 0===(r=de({},t)).pathname&&(r.pathname=""),r.search?"?"!==r.search.charAt(0)&&(r.search="?"+r.search):r.search="",r.hash?"#"!==r.hash.charAt(0)&&(r.hash="#"+r.hash):r.hash="",void 0!==e&&void 0===r.state&&(r.state=e));try{r.pathname=decodeURI(r.pathname)}catch(t){throw t instanceof URIError?new URIError('Pathname "'+r.pathname+'" could not be decoded. This is likely caused by an invalid percent-encoding.'):t}return n&&(r.key=n),o?r.pathname?"/"!==r.pathname.charAt(0)&&(r.pathname=y(r.pathname,o.pathname)):r.pathname=o.pathname:r.pathname||(r.pathname="/"),r},ve=function(t,e){return t.pathname===e.pathname&&t.search===e.search&&t.hash===e.hash&&t.key===e.key&&v(t.state,e.state)},me=function(t){function e(){return Y(this,e),B(this,t.apply(this,arguments))}return W(e,t),e.prototype.isStatic=function(){return this.context.router&&this.context.router.staticContext},e.prototype.componentWillMount=function(){bt(this.context.router,"You should not use <Redirect> outside a <Router>"),this.isStatic()&&this.perform()},e.prototype.componentDidMount=function(){this.isStatic()||this.perform()},e.prototype.componentDidUpdate=function(t){var e=ye(t.to),n=ye(this.props.to);ve(e,n)?mt(!1,"You tried to redirect to the same route you're currently on: \""+n.pathname+n.search+'"'):this.perform()},e.prototype.perform=function(){var t=this.context.router.history,e=this.props,n=e.push,o=e.to;n?t.push(o):t.replace(o)},e.prototype.render=function(){return null},e}(e.Component);me.propTypes={push:Et.bool,from:Et.string,to:Et.oneOfType([Et.string,Et.object]).isRequired},me.defaultProps={push:!1},me.contextTypes={router:Et.shape({history:Et.shape({push:Et.func.isRequired,replace:Et.func.isRequired}).isRequired,staticContext:Et.object}).isRequired};var be=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},ge=function(t){var e=t.pathname,n=void 0===e?"/":e,o=t.search,r=void 0===o?"":o,i=t.hash,a=void 0===i?"":i;return{pathname:n,search:"?"===r?"":r,hash:"#"===a?"":a}},we=function(t,e){return t?be({},e,{pathname:Bt(t)+e.pathname}):e},Oe=function(t,e){if(!t)return e;var n=Bt(t);return 0!==e.pathname.indexOf(n)?e:be({},e,{pathname:e.pathname.substr(n.length)})},Pe=function(t){return"string"==typeof t?Wt(t):ge(t)},xe=function(t){return"string"==typeof t?t:Nt(t)},Re=function(t){return function(){bt(!1,"You cannot %s with <StaticRouter>",t)}},Ee=function(){},je=function(t){function n(){var e,o,r;D(this,n);for(var i=arguments.length,a=Array(i),c=0;c<i;c++)a[c]=arguments[c];return e=o=F(this,t.call.apply(t,[this].concat(a))),o.createHref=function(t){return Bt(o.props.basename+xe(t))},o.handlePush=function(t){var e=o.props,n=e.basename,r=e.context;r.action="PUSH",r.location=we(n,Pe(t)),r.url=xe(r.location)},o.handleReplace=function(t){var e=o.props,n=e.basename,r=e.context;r.action="REPLACE",r.location=we(n,Pe(t)),r.url=xe(r.location)},o.handleListen=function(){return Ee},o.handleBlock=function(){return Ee},r=e,F(o,r)}return $(n,t),n.prototype.getChildContext=function(){return{router:{staticContext:this.props.context}}},n.prototype.componentWillMount=function(){mt(!this.props.history,"<StaticRouter> ignores the history prop. To use a custom history, use `import { Router }` instead of `import { StaticRouter as Router }`.")},n.prototype.render=function(){var t=this.props,n=t.basename,o=(t.context,t.location),r=N(t,["basename","context","location"]),i={createHref:this.createHref,action:"POP",location:Oe(n,Pe(o)),push:this.handlePush,replace:this.handleReplace,go:Re("go"),goBack:Re("goBack"),goForward:Re("goForward"),listen:this.handleListen,block:this.handleBlock};return e.createElement(Tt,be({},r,{history:i}))},n}(e.Component);je.propTypes={basename:Et.string,context:Et.object.isRequired,location:Et.oneOfType([Et.string,Et.object])},je.defaultProps={basename:"",location:"/"},je.childContextTypes={router:Et.object.isRequired};var Te=function(t){function n(){return V(this,n),z(this,t.apply(this,arguments))}return K(n,t),n.prototype.componentWillMount=function(){bt(this.context.router,"You should not use <Switch> outside a <Router>")},n.prototype.componentWillReceiveProps=function(t){mt(!(t.location&&!this.props.location),'<Switch> elements should not change from uncontrolled to controlled (or vice versa). You initially used no "location" prop and then provided one on a subsequent render.'),mt(!(!t.location&&this.props.location),'<Switch> elements should not change from controlled to uncontrolled (or vice versa). You provided a "location" prop initially but omitted it on a subsequent render.')},n.prototype.render=function(){var t=this.context.router.route,n=this.props.children,o=this.props.location||t.location,r=void 0,i=void 0;return e.Children.forEach(n,function(n){if(e.isValidElement(n)){var a=n.props,c=a.path,s=a.exact,u=a.strict,p=a.sensitive,l=a.from,f=c||l;null==r&&(i=n,r=f?ce(o.pathname,{path:f,exact:s,strict:u,sensitive:p}):t.match)}}),r?e.cloneElement(i,{location:o,computedMatch:r}):null},n}(e.Component);Te.contextTypes={router:Et.shape({route:Et.object.isRequired}).isRequired},Te.propTypes={children:Et.node,location:Et.object};var Se={childContextTypes:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,mixins:!0,propTypes:!0,type:!0},_e={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Ce=Object.defineProperty,ke=Object.getOwnPropertyNames,Ae=Object.getOwnPropertySymbols,Le=Object.getOwnPropertyDescriptor,Me=Object.getPrototypeOf,Ue=Me&&Me(Object),qe=function t(e,n,o){if("string"!=typeof n){if(Ue){var r=Me(n);r&&r!==Ue&&t(e,r,o)}var i=ke(n);Ae&&(i=i.concat(Ae(n)));for(var a=0;a<i.length;++a){var c=i[a];if(!(Se[c]||_e[c]||o&&o[c])){var s=Le(n,c);try{Ce(e,c,s)}catch(t){}}}return e}return e},He=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t};t.BrowserRouter=Mt,t.HashRouter=qt,t.Link=It,t.MemoryRouter=Qt,t.NavLink=le,t.Prompt=fe,t.Redirect=me,t.Route=pe,t.Router=Tt,t.StaticRouter=je,t.Switch=Te,t.matchPath=ce,t.withRouter=function(t){var n=function(n){var o=n.wrappedComponentRef,r=G(n,["wrappedComponentRef"]);return e.createElement(pe,{render:function(n){return e.createElement(t,He({},r,n,{ref:o}))}})};return n.displayName="withRouter("+(t.displayName||t.name)+")",n.WrappedComponent=t,n.propTypes={wrappedComponentRef:Et.func},qe(n,t)},Object.defineProperty(t,"__esModule",{value:!0})});
