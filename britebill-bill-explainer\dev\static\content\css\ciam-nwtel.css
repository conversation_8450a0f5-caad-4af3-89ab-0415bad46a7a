/* Helpers */
.max-w-340 {
  max-width: 340px;
}
.top-3 {
  top: 3px;
}
.width-165 {
  min-width: 165px;
}
.txt333 {
  color: #333333;
}
.txt00865 {
  color: #00865d;
}
.letter-spacing-3 {
  letter-spacing: 0.3px;
}
.line-height-16 {
  line-height: 16px;
}
.line-height-38 {
  line-height: 38px;
}
.margin-r-3 {
  margin-right: 3px;
}
.margin-b-75 {
  margin-bottom: 75px;
}
.margin-b-80 {
  margin-bottom: 80px;
}
.pad-h-24 {
  padding-left: 24px;
  padding-right: 24px;
}

/* Custom styles */
/* Checkmark */
.nwtel-check-element.icon-checkmark-new {
  align-items: center;
  display: flex;
  font-size: 12px;
  justify-content: center;
  position: static;
}
.icon-checkmark-new:before {
  font-size: inherit;
  position: static;
}
.nwtel-check-element.icon-checkmark-new + .nwtel-check-control {
  position: static;
}

/* button password */
.btn-show-password {
  position: absolute;
  top: 35px;
  right: 15px;
  padding: 0;
}
.btn-show-password:focus {
  box-shadow: none;
}

/* Error */
.form-error label,
.messages-error,
.form-error .error-message,
.error-message {
  color: #bd2025;
}

.form-error input,
.form-error select,
.form-error textarea{
  color: #333333;
}

/* separator (footer) */
ul.pipe-separated-list {
  display: flex;
  justify-content: center;
  list-style: none;
}
ul.pipe-separated-list li {
  display: flex;
  align-items: center;
}
ul.pipe-separated-list li:not(:last-child):after {
  border-right: 1px solid #d4d4d4;
  content: "";
  display: inline-block;
  height: 12px;
  margin: 0 10px;
}
ul.pipe-separated-list li a {
  color: #555;
}

/* progress bar */
.js-milestone-progressbar .ui-progressbar-value {
  display: block !important; /*. force show for zero value animation */
  transition: all 0.5s;
}
.js-readonly-checklist:not(.js-readonly-checklist-initialized) .js-readonly-checklist-checked {
  display: none;
}

/* tooltip */
.tooltip-default-theme {
  position: relative;
}
.tooltip-default-theme .tooltip.show {
  opacity: 1;
}
    .tooltip-default-theme .tooltip {
        border: 1px solid #E1E1E1;
        background-color: #FFFFFF;
        box-shadow: 0 14px 36px 0 rgba(0,0,0,0.3);
        font-family: Helvetica, Arial, sans-serif;
    }
        .tooltip-default-theme .tooltip .tooltip-inner {
            background-color: #FFFFFF;
            border: 0;
            box-shadow: none;
            padding: 20px;
            padding-bottom: 30px;
        }
.tooltip-default-theme .tooltip .arrow {
  display: block;
  height: 0;
  width: 0;
  pointer-events: none;
}
.tooltip-default-theme .tooltip .arrow:before,
.tooltip-default-theme .tooltip .arrow:after {
  content: "";
  display: block;
  border-width: 15px;
  border-color: transparent;
  border-style: solid;
  position: relative;
}
.tooltip-default-theme .tooltip .arrow:after {
  border-width: 14px;
}
.tooltip-default-theme .tooltip.bs-tooltip-bottom .arrow {
  left: 50% !important;
  top: -30px !important;
}
.tooltip-default-theme .tooltip.bs-tooltip-bottom .arrow:before {
  border-bottom-color: #e1e1e1;
  transform: translateX(-50%);
}
.tooltip-default-theme .tooltip.bs-tooltip-bottom .arrow:after {
  border-bottom-color: #fff;
  transform: translateX(-50%);
  position: absolute;
  top: 2px;
  left: 0;
}
.tooltip-default-theme .tooltip.bs-tooltip-right .arrow {
  left: -30px !important;
  top: 50% !important;
}
.tooltip-default-theme .tooltip.bs-tooltip-right .arrow:before {
  border-right-color: #e1e1e1;
  transform: translateY(-50%);
}
.tooltip-default-theme .tooltip.bs-tooltip-right .arrow:after {
  border-right-color: #fff;
  transform: translateY(-50%);
  position: absolute;
  top: 0;
  left: 2px;
}
.tooltip-default-theme.tooltip-min-w-300 .tooltip {
  min-width: 300px;
  max-width: 300px;
}
.tooltip-default-theme .tooltip .tooltip-inner {
  max-width: 100%;
}

    .tooltip-default-theme .btn-show-password {
        position: absolute;
        top: 12px;
        right: 45px;
        padding: 0;
    }
.line-height-18 .tooltip-default-theme .tooltip,
.tooltip-default-theme .tooltip .line-height-18,
.tooltip-default-theme.line-height-18 .tooltip {
  line-height: 18px;
}

/* Tri-up line */
.tri-up-line {
  position: relative;
  margin-top: 15px;
  border-top: 1px solid #d7d7d7;
  background: #fff;
}
.tri-up-line:before, .tri-up-line:after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  border-color: transparent;
  border-top: 0;
}
.tri-up-line:before {
  top: -17px;
  left: 50%;
  border-bottom-color: #d7d7d7;
  border-width: 16px;
  transform: translateX(-50%);
}
.tri-up-line:after {
  top: -15px;
  left: 50%;
  border-bottom-color: #fff;
  border-width: 15px;
  transform: translateX(-50%);
}

@media (max-width: 767.98px) {
 /* affix footer */
  html.affix-footer-xs {
    display: flex;
  }
  html.affix-footer-xs body {
    align-items: center;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    width: 100%;
  }
  html.affix-footer-xs header,
  html.affix-footer-xs footer {
    flex-shrink: 0;
  }
  html.affix-footer-xs main {
    flex-grow: 1;
    flex-shrink: 0;
  }

  /* tooltip */
  .tooltip-hidden-xs .tooltip {
    display: none;
  }
  .tooltip-default-theme .btn-show-password-xs {
    position: absolute;
    top: 12px;
    right: 15px;
    padding: 0;
  }
}

@media (min-width: 768px) {
  /* Helpers */
  .bgAzure-sm {
    background: #f0f4f5;
  }
   .borderRadius-t-sm-3 {
    border-radius: 3px 3px 0 0;
  }
  .borderRadius-b-sm-3 {
    border-radius: 0 0 3px 3px;
  }
  .borderGrayLight6-upper-sm {
    border: 1px solid #d4d4d4;
    border-bottom: 0;
  }
  .borderGrayLight6-middle-sm {
    border: 1px solid #d4d4d4;
    border-top: 0;
    border-bottom: 0;
  }
  .borderGrayLight6-lower-sm {
    border: 1px solid #d4d4d4;
    border-top: 0;
  }
  .max-w-sm-402 {
    max-width: 402px;
  }
  .max-w-sm-400 {
    max-width: 400px;
  }
  .max-w-sm-230 {
    max-width: 230px;
  }
}

@media (min-width: 992px) {
}

/* Page-Specific */
#password-tooltip-container input:focus ~ * > .tooltip,
#password-tooltip-container input:hover ~ * > .tooltip {
  opacity: 0;
}

/* Focus Outline */

/* Focus Outline - fallback style */
.standard-outline-fallback *:focus {
  outline: solid 2px #000;
}

/* Focus Outline - header and footer */
footer.standard-outline-footer ul.pipe-separated-list li a {
  position: relative;
}
header.standard-outline-header a:focus,
footer.standard-outline-footer ul.pipe-separated-list li a:focus {
  outline: none;
}
header.standard-outline-header a:focus::after,
footer.standard-outline-footer ul.pipe-separated-list li a:focus::before {
  content: "";
  height: calc(100% + 6px);
  width: calc(100% + 6px);
  position: absolute;
  top: -3px;
  left: -3px;
  display: block;
  box-shadow: 0 0 3px 1px #000, 0 0 3px 2px #000;
  z-index: 1;
  pointer-events: none;
}

/* Focus Outline - others */
.standard-outline-form .nwtel-graphical-ctrl input[type="checkbox"]:focus,
.standard-outline-form .nwtel-graphical-ctrl input[type="radio"]:focus,
.standard-outline-form .nwtel-graphical-ctrl .btn-select:focus {
  outline: none;
}
.standard-outline-form .nwtel-graphical-ctrl input[type="radio"]:focus ~ .nwtel-radio-element,
.standard-outline-form .nwtel-graphical-ctrl input[type="checkbox"]:focus ~ .nwtel-check-element,
.standard-outline-form select.nwtel-form-control:focus,
.standard-outline-form input[type="text"].nwtel-form-control:focus,
.standard-outline-form input[type="number"].nwtel-form-control:focus,
.standard-outline-form input[type="tel"].nwtel-form-control:focus,
.standard-outline-form input[type="email"].nwtel-form-control:focus,
.standard-outline-form input[type="password"].nwtel-form-control:focus,
.standard-outline-form textarea.nwtel-form-control:focus,
.standard-outline-form .btn-select.nwtel-form-control:focus {
  outline: none;
  box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #000,
    0 0 2px 5px #000;
}
.standard-outline-relative a {
  position: relative;
}
.standard-outline-relative a:focus,
.standard-outline-relative button:focus,
.standard-outline-relative input[type="submit"]:focus,
.standard-outline-self-after:focus {
  outline: none;
}
.standard-outline-relative a:focus::after,
.standard-outline-relative button:focus::after,
.standard-outline-relative input[type="submit"]:focus::after,
.standard-outline-self-after:focus::after {
  content: "";
  height: calc(100% + 6px);
  width: calc(100% + 6px);
  position: absolute;
  top: -3px;
  left: -3px;
  display: block;
  box-shadow: 0 0 3px 1px #000, 0 0 3px 2px #000;
  z-index: 1;
  pointer-events: none;
}
.standard-outline-relative .btn-secondary:focus::after {
  height: calc(100% + 10px);
  width: calc(100% + 10px);
  top: -5px;
  left: -5px;
}

/* use standard-outline-direct for links that wrap. note that uncommenting the commented-out styles will reduce the outline "padding" to 1px to improve visibility of wrapped text while focused */
.standard-outline-direct a:focus {
  outline: none;
  box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #000,
    0 0 2px 5px #000, inset 0 0 0 1em #fff;
  /*box-shadow: 0 0 2px 1px #fff, 0 0 0 1px #fff, 0 0 3px 3px #000, 0 0 2px 3px #000, inset 0 0 0 1em #fff;*/
}

.standard-outline-direct.bgTintSubtleGrey a:focus {
  outline: none;
  box-shadow: 0 0 0 3px #f7f7f7, 0 0 2px 3px #f7f7f7, 0 0 4px 5px #000,
    0 0 2px 5px #000, inset 0 0 0 1em #f7f7f7;
  /*box-shadow: 0 0 2px 1px #f7f7f7, 0 0 0 1px #f7f7f7, 0 0 3px 3px #000, 0 0 2px 3px #000, inset 0 0 0 1em #f7f7f7;*/
}

/* use standard-outline-direct-no-inset instead of standard-outline-direct if the element's background-color is different from its container's background-color */
.standard-outline-direct-no-inset a:focus {
  outline: none;
  box-shadow: 0 0 0px 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #000,
    0 0 2px 5px #000;
}

.standard-outline-direct-no-inset.bgTintSubtleGrey a:focus {
  outline: none;
  box-shadow: 0 0 0px 3px #f7f7f7, 0 0 2px 3px #f7f7f7, 0 0 4px 5px #000,
    0 0 2px 5px #000;
}

/* Focus Outline - IE-Only */
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  /* seems like IE rendering engine has an issue dealing with box-shadow directly applied to elements. use outline on default state to hide weird artifacts being left-out on blur */
  .standard-outline-direct a,
  .standard-outline-direct.bgTintSubtleGrey a {
    outline: 7px solid transparent;
  }

  /* IE is allowing focus on elements that technically shouldn't be focusable so hide the outline */
  html:not([tabindex]):focus,
  body:not([tabindex]):focus,
  main:not([tabindex]):focus,
  header:not([tabindex]):focus,
  footer:not([tabindex]):focus,
  div:not([tabindex]):focus,
  section:not([tabindex]):focus,
  article:not([tabindex]):focus,
  aside:not([tabindex]):focus,
  nav:not([tabindex]):focus,
  p:not([tabindex]):focus,
  span:not([tabindex]):focus,
  label:not([tabindex]):focus,
  fieldset:not([tabindex]):focus {
    outline: none;
  }

}

/* hide the native input clear and show password button of IE and Edge */
::-ms-clear,
::-ms-reveal {
  display: none;
}