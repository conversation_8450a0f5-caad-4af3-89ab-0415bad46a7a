/*Global Nav icons*/
@font-face{font-family:'mybell-icon2';src:url(../../content/fonts/mybell-icon2.eot?#iefix) format("embedded-opentype"),url(../../content/fonts/mybell-icon2.woff) format("woff"),url(../../content/fonts/mybell-icon2.ttf) format("truetype"),url(../../content/fonts/mybell-icon2.svg) format("svg");font-weight:400;font-style:normal}

.gn-icon, .gn.icon{
    font-family: 'mybell-icon2';
    font-style: normal;
    speak: none;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}
.gn-icon:before,.gn.icon:before{font-family:'mybell-icon2';position:relative;top:0px;}

.gn-icon_homephone-menu:before {
  content: "\e902";
}
.gn-icon_bills-menu:before {
  content: "\e901";
}
.gn-icon_internet-menu:before {
  content: "\e903";
}
.gn-icon_mobility-menu:before {
  content: "\e904";
}
.gn-icon_tv-menu:before {
  content: "\e909";
}
.gn-icon_mobility_bill-menu:before {
  content: "\e905";
}
.gn-icon_one_bill-menu:before {
  content: "\e906";
}
.gn-icon_pay-menu:before {
  content: "\e907";
}
.gn-icon_profile-menu:before {
  content: "\e908";
}
.gn-icon_user_services-menu:before {
  content: "\e90d";
}
.gn-icon_view_all-menu:before {
  content: "\e90e";
}
.gn-icon_user_profile-menu:before {
  content: "\e90b";
}
.gn-icon_user_bills-menu:before {
  content: "\e90a";
}
.gn-icon-hp-lob:before {
  content: "\e9b3";
}
.gn-icon-internet-lob:before {
  content: "\e9b5";
}
.gn-icon-tv-lob:before {
  content: "\e9b6";
}
.gn-icon-mobility-lob:before {
  content: "\e9b7";
}
.gn.icon-small_icon_expand:before {
  content: "\10fff";
}
.gn.icon-small_icon_collapse:before {
  content: "\10fffa";
}
.gn-icon-location-pin:before {
  content: "\e620";
  font-family: mybell-icon2 !important;
}

footer.gf-mybell .footer-icon .icon-o-chat-bubble, footer .footer-icon .icon-o.icon-o-handset, footer .footer-icon .icon-o.icon-o-location {
    top: -5px;
    left: 0;
}

/*MyBell Global Nav*/
.gn-mybell a.trigger-dropdown .icon.icon-chevron.chevron-down, 
.gn-mybell a.trigger-popup .icon.icon-chevron.chevron-down {
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
    display: inline-block;
    font-size: 13px;
    margin-left: 10px;
}
.gn-mybell a.trigger-dropdown .icon.icon-chevron.chevron-down{
    position:relative;
    top:-2px;
    left:-1px;
}
.gn-mybell .connector-active-lob ul a {
    padding-top: 8px;
}
.gn-mybell .login-ellipsis ~ .icon.icon-chevron.chevron-down {
    position: relative;
    top:-5px;
    margin-right:15px;
}
.gn-mybell .login-ellipsis:hover{
    text-decoration:underline
}
.gn-mybell .button.connector-log-out-button, 
.gn-mybell .button.connector-profile-button {
    padding-right: 0px;
}

.global-navigation.gn-mybell .popup.caret:after {
    border-width:8px;
    left: calc(50% + 65px);
}
.gn-mybell .connector-login-modal.user-control-menu {
    padding: 0px;
}
.gn-mybell .connector-login-modal.user-control-menu a {
    padding: 17px 15px 17px 8px;
    display: block;
    border-bottom: 1px solid #d4d4d4;
}
.gn-mybell .container-flex-box {
    display: flex;
    flex-wrap: nowrap;
}
.gn-mybell .connector-active-secondary-nav {
    background: #003778;
    padding: 20px 0;
    position: relative;
    -webkit-box-shadow: inset 0px 10px 18px -2px rgba(0,0,0,0.02);
    -moz-box-shadow: inset 0px 10px 18px -2px rgba(0,0,0,0.02);
    box-shadow: inset 0px 10px 18px -2px rgba(0,0,0,0.02);
    font-family: "Helvetical",Arial, sans-serif; 
}
.gn-mybell .connector-active-lob {
    background: #001f60;
}
.gn-mybell .connector-active-secondary-nav ul > li {
    display: table-cell;
    vertical-align: middle;
    white-space: nowrap;
}
.gn-mybell .connector a, 
.gn-mybell .connector a:link, 
.gn-mybell .connector a:visited, 
.gn-mybell .connector a:hover, 
.gn-mybell .connector a:active,
.global-navigation.gn-mybell .connector-settings a:hover{
    text-decoration: none;
}
.connector-log-out-button:hover{
    text-decoration: underline !important;
}
.gn-mybell .connector-active-secondary-nav ul a {
    display: block;
    position: relative;
    font-size: 18px;
    line-height: 1;
    color: #c2cedf;
    text-decoration: none;
    margin-right: 30px;
}
.gn-mybell li.active a.trigger-dropdown .icon.icon-chevron.chevron-down:after {
    background-color: #003778;
    bottom: 10px;
    content: "";
    display: block;
    height: 8px;
    left: -8px;
    position: absolute;
    right: -34px;
    top: -4px;
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
}
.gn-mybell .connector-active-secondary-nav li.active a span,
.gn-mybell .connector-active-secondary-nav li.active a span.icon.icon-chevron.chevron-down{
    color:#fff;
}
.gn-mybell .connector-active-secondary-nav .icon.icon-chevron.chevron-down{
    top: 10px;
}
.gn-mybell .outer-shadow {
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.3);
}
.gn-mybell .secondary-nav-dropdown {
    display: none;
    position: absolute;
    top: 17px;
    width: 250px;
    z-index: 9999;
    padding-top: 23px;
}
.gn-mybell .secondary-nav-dropdown a.services-selection.active {
    color: #c2cedf;
    background-color: #00549a;
}
.gn-mybell .secondary-nav-dropdown a.services-selection.active,
.gn-mybell .secondary-nav-dropdown a.services-selection.active:hover{
    color: #c2cedf;
    background-color: #00549a;
}
.gn-mybell .secondary-nav-dropdown a.services-selection:hover, .secondary-nav-dropdown a.services-selection:focus {
        background-color: #f4f4f4;
        color: #555;
}
.gn-mybell .connector-active-secondary-nav ul > li .services-selection .my-service {
    white-space: normal;
    padding-right: 20px;
    line-height: 15px;
}
.gn-mybell .lob-nickname {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 160px;
    display: block;
}
.gn-mybell .secondary-nav-dropdown a.services-selection.active .my-service, 
.gn-mybell .secondary-nav-dropdown a.services-selection.active .my-service:hover {
    color: #fff !important;
}
.gn-mybell .secondary-nav-dropdown a.services-selection .my-service {
    color: #000;
}
.gn-mybell .secondary-nav-dropdown a.services-selection:hover .my-service {
    color: #00549a;
}
.gn-mybell .lineH-22 {
    line-height: 22px;
}
.gn-mybell .services-selection .icon-pos.gn-menu span {
    font-size: 42px;
    color: #00549a;
    position:relative;
}
.gn-mybell .services-selection {
    height:60px
}
.gn-mybell .services-selection.active .icon-pos.gn-menu span {
    color: #fff;
}
.gn-mybell .icon-pos {
    position: absolute;
    left: 10px;
    top: 8px;
    color: #00549a;
    text-align: center;
    width: 40px;
    display: table;
    height: 100%;
}
.gn-mybell .desc-pos {
    margin-left: 50px;
    display: table;
    height: 100%;
}
.gn-mybell .middle-align {
    display: table-cell;
    vertical-align: middle;
}
.gn-mybell .secondary-nav-dropdown a.services-selection {
    padding: 10px 25px 10px 10px;
    color: #555;
    border-bottom: 1px solid #d4d4d4;
    margin-right: 0;
}
.gn-mybell .secondary-nav-dropdown a.services-selection:after {
    font-family: 'bell-icon';
    content: "\e012";
    color: #00549a;
    font-size: 17px;
    font-style: normal;
    speak: none;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    right: 8px;
    opacity: 1;
}
.gn-mybell .trigger-dropdown.secondary-nav-lob:hover{
    color:#fff;
}
.gn-mybell .current-lob-shown {
    position: absolute;
    top: 10px;
    display: table;
    height: 45px;
    float:left;
}
.gn-mybell .spacer60{
    height:60px;
}
.gn-mybell #fixedHeader{
    padding-top:0;
    padding-bottom:0;
}
.gn-mybell .current-lob-icon {
    float: left;
    margin-right: 15px;
    margin-top:-3px;
}
.gn-mybell .current-lob-desc {
    float: left;
    display: table;
    height: 40px;
}
.gn-mybell .connector-active-lob-title.bellSlim {
    top: 5px;
    font-size: 15px;
}
.gn-mybell .exclusive-offers-notif{
    border-radius:50%;
    height:10px;
    width:10px;
    background-color:#0075FF;
    display:inline-block;
    position:relative;
    top:-10px;
    left: -5px;
}

/*Search styles*/
.gn-mybell #connector-search #voice_search:after {
    content: "";
}
.gn-mybell #sr-header-search,
#sr-placerholder-label{
    position:absolute;
}
.global-navigation.gn-mybell #connector-search [type="submit"]::after {
    content: "\e615";
}
.global-navigation.gn-mybell #connector-search [type="search"] {
    margin-top: 1px;
}
.global-navigation.gn-mybell #connector-search [type="search"]::placeholder {
    color: #777;
}
.global-navigation.gn-mybell #connector-search .voice-search-btn:after {
    content: unset;
}
.global-navigation.gn-mybell #connector-search .voice-search-btn{
    right:40px;
}
.global-navigation.gn-mybell .icon-voice-search::before {
    right: 3px;
}
.global-navigation.gn-mybell #connector-search .voice-search-btn {
    width: 20px;
}
/*Footer styles*/
.gf-mybell .search-bar-footer.search-bar-rounded [type="search"] {
    background-color: #fff;
}
.gf-mybell a.btn.btn-primary.call-to-action:hover span:nth-child(2n) {
    text-decoration: underline
}
.gf-mybell a.btn.btn-primary.call-to-action:hover span.footer-icon{
     background-color:#003778;
 }
.gf-mybell .search-bar-footer [type="search"] {
    border-radius:5px;
    padding-right:108px;
}
footer.gf-mybell .search-bar-footer .hideAutocomplete.active {
    display: block;
    margin-top:-30px;
    left:-80px;
    cursor:pointer;
}
footer.gf-mybell a.call-to-action.d-sm-flex {
    display: inline-flex;
}

/*Focus outline styling*/
body.is_tabbing main a:focus, 
body.is_tabbing main button:focus,
body.is_tabbing .federal-bar-store-locator-popup a:focus, 
body.is_tabbing .federal-bar-store-locator-popup button:focus,
body.is_tabbing .federal-bar-select-provinces-popup a:focus,
body.is_tabbing #accessible-connector .menu-flyout a:focus, 
body.is_tabbing #accessible-connector .connector-login-modal a:focus,
body.is_tabbing #accessible-connector .voice-search-btn:focus,
body.is_tabbing #accessible-connector .hideAutocomplete:focus, 
body.is_tabbing #accessible-connector [type="submit"]:focus, 
body.is_tabbing #accessible-secondary-nav.connector-active-secondary-nav .secondary-nav-dropdown.connector-drop-down a:focus{
  outline: 0!important;
  box-shadow: 0 0 0 3px #fff, 0 0 2px 3px #fff, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc!important;
}
body.is_tabbing footer a:focus, 
body.is_tabbing footer button:focus,
body.is_tabbing footer main .bgGray19 a:focus,
body.is_tabbing footer main .bgGray19 button:focus,
body.is_tabbing footer .search-bar-footer [type="search"]:focus,
body.is_tabbing #accessible-connector .voice-search-btn:focus,
body.is_tabbing #accessible-connector .hideAutocomplete:focus, 
body.is_tabbing #accessible-connector [type="submit"]:focus{
  outline: 0!important;
  box-shadow: 0 0 0 3px #f4f4f4, 0 0 2px 3px #f4f4f4, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc!important;
}
body.is_tabbing #accessible-connector a:focus,
body.is_tabbing .global-navigation.gn-mybell #connector-search input.ui-autocomplete-input:focus{
  outline: 0!important;
  box-shadow: 0 0 0 3px #00549a, 0 0 2px 3px #00549a, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc!important;
}
body.is_tabbing .global-navigation .connector-active-secondary-nav a:focus{
  outline: 0!important;
  box-shadow: 0 0 0 3px #003778, 0 0 2px 3px #003778, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc!important;
}
body.is_tabbing .global-navigation .connector-active-lob a:focus{
  outline: 0!important;
  box-shadow: 0 0 0 3px #001f60, 0 0 2px 3px #001f60, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc!important;
}connector-active-lob

body.is_tabbing .federal-bar .federal-bar-links a:focus,
.global-navigation #maincontent:focus,
.global-navigation .federal-bar-links a:focus{
  outline: 0!important;
  box-shadow: 0 0 0 3px #2d2e33, 0 0 2px 3px #2d2e33, 0 0 4px 5px #5fb0fc, 0 0 2px 5px #8ec6fc!important;
}


/*Media Queries for MyBell GN*/
@media (max-width: 767.98px){
footer.gf-mybell .btn.btn-primary.call-to-action, 
footer.gf-mybell .btn.btn-primary.call-to-action:active, 
footer.gf-mybell .btn.btn-primary.call-to-action:focus {
    color: #003778;
    background-color: transparent;
    border: 2px solid #003778;
    font-size: 15px;
    padding: 7px 30px;
    text-align: center;
    cursor: pointer;
    width:auto;
}
}
@media(max-width:991.98px){
.gn-mybell .login-button.user-options,
.gn-mybell .connector-active-secondary-nav,
.gn-mybell .current-lob-shown{
    display:none;
}
 .gf-mybell a.btn.btn-primary.call-to-action:hover span:nth-child(2n) {
    text-decoration: none;
}
}
@media (min-width: 992px){
.gn-mybell .sub-nav-item {
    overflow-y: hidden;
}
.global-navigation .connector .menu-flyout-visible .menu-flyout-root {
    min-height: 300px;
}
.global-navigation .connector .menu-flyout-visible .sub-nav-group.sub-nav-large {
    width: 320%;
}
}