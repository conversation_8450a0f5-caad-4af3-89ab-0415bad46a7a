import * as React from "react";
import { PBEHeader } from "singleban-components";
import { InjectedIntlProps, injectIntl } from "react-intl";
import { IPBE } from "../../models";

interface Props {
    pbe: IPBE;
}

const PBEGeneric = (props: Props & InjectedIntlProps) => {
    const { intl: { formatMessage }, pbe } = props;
    const title = formatMessage({ id: pbe?.titleKey ?? "" });
    const description = formatMessage({ id: pbe?.descriptionKey1 ?? "" });
    const imageClassName = pbe?.pbeIconName;

    return (
        <>
            <PBEHeader descriptionKey={description} description2Key={pbe?.descriptionKey2} titleKey={title} iconClassName={imageClassName} />
        </>
    );
};


export default (injectIntl(PBEGeneric));