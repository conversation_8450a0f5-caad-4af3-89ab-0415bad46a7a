import { Epic } from "redux-observable";
import { AjaxResponse } from "bwtk";
import { IFetchBillsResPayload, IFetchBillsState } from "./IBillExplainerBill";

export type IFetchBillsEpic = Epic<ReduxActions.Action<IFetchBillsState>, any>;
export interface IFetchBillsEpicResponse extends AjaxResponse {
  data: IFetchBillsResPayload;
}

export type IFetchPBEActionURLDataEpic = Epic<ReduxActions.Action<any>, any>;