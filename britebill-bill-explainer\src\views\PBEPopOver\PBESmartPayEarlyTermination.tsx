import * as React from "react";
import { InjectedIntlProps, injectIntl } from "react-intl";
import { P<PERSON><PERSON>eader, PBEFooter } from "singleban-components";
import { IPBE } from "../../models";
import { CURRENCY_OPTIONS, DATE_OPTIONS, modalOpenedOmniture } from "../../utils/Utility";

interface Props {
    pbe: IPBE;
    isPBEModalLinkDisabled: boolean;
}

const PBESmartPayEarlyTermination = (props: Props & InjectedIntlProps) => {
    const { intl: { formatMessage, formatNumber, formatDate }, pbe } = props;
    const title = formatMessage({ id: "SMARTPAY_EARLY_TERMINATION_TITLE" });
    const description = formatMessage({ id: "SMARTPAY_EARLY_TERMINATION_DESC" }, {
        agreementEndDate: formatDate(new Date(pbe?.pbeDataBag?.actualAgreementEndDate), DATE_OPTIONS),
        originalEndDate: formatDate(new Date(pbe?.pbeDataBag?.originalAgreementEndDate), DATE_OPTIONS),
        monthsRemaining: formatNumber(pbe?.pbeDataBag?.monthsRemaining)
    });
    const imageClassName = "icon-06_no_bill_no_contract";
    const PBEFooterItems = [{
        ctaLink: formatMessage({ id: "SMARTPAY_EARLY_TERMINATION_SEE_AGREEMENT_LINK" }, {
            acctNo: pbe?.pbeDataBag?.encryptAcctNo,
            subNo: pbe?.pbeDataBag?.subNo,
        }),
        iconClassName: "icon-07_bill",
        titleKey: formatMessage({ id: "SMARTPAY_EARLY_TERMINATION_SEE_AGREEMENT_TITLE" }),
        ctaTitleKey: formatMessage({ id: "SMARTPAY_EARLY_TERMINATION_SEE_AGREEMENT_SUBTITLE" }),
        isFirstRow: true,
        id: "pbe-smartpay-early-termination-see-agreement"
    }];

    React.useEffect(() => {
        modalOpenedOmniture(props?.pbe?.triggerPbeOmniture, title, description);
    },[title, description]);
    return (
        <>
            <PBEHeader descriptionKey={description} titleKey={title} iconClassName={imageClassName} />
            <div className="margin-b-30 margin-h-xs-15 margin-h-30">
                <div className="box-round-grey pad-xs-15 pad-20">
                    <div className="d-block relative">
                        <div className="d-flex">
                            <span className="margin-r-xs-15 margin-r-20">
                                <span className="surtitle-black">{formatMessage({ id: "SMARTPAY_EARLY_TERMINATION_FINANCED_AMOUNT_BALANCE" })}</span>
                            </span>
                            <span className="d-flex container-flex-grow-fill justify-end">
                                <span className="d-inline noTxt txtRight">
                                    <span className="text-nowrap txtSize14 line-height-18" aria-hidden="true">{formatNumber(pbe?.pbeDataBag?.totalFinancedAmountBalance, CURRENCY_OPTIONS)}</span>
                                    <span className="sr-only">{formatNumber(pbe?.pbeDataBag?.totalFinancedAmountBalance, CURRENCY_OPTIONS)}</span>
                                </span>
                            </span>
                        </div>
                    </div>
                    <hr className="margin-v-15 border-lightGray" aria-hidden="true" />
                    <div className="d-block relative">
                        <div className="d-flex">
                            <span className="margin-r-xs-15 margin-r-20">
                                <span className="surtitle-black">{formatMessage({ id: "SMARTPAY_EARLY_TERMINATION_AGREEMENT_CREDIT_BALANCE" })}</span>
                            </span>
                            <span className="d-flex container-flex-grow-fill justify-end">
                                <span className="d-inline noTxt txtRight">
                                    <span className="text-nowrap txtSize14 line-height-18" aria-hidden="true">{formatNumber(pbe?.pbeDataBag?.totalAgreementCreditBalance, CURRENCY_OPTIONS)}</span>
                                    <span className="sr-only">{formatNumber(pbe?.pbeDataBag?.totalAgreementCreditBalance, CURRENCY_OPTIONS)}</span>
                                </span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            {pbe?.pbeDataBag?.isSubscriberCancelled ? null : <PBEFooter footerItems={PBEFooterItems} isPBEModalLinkDisabled={props.isPBEModalLinkDisabled} />}
        </>
    );
};


export default (injectIntl(PBESmartPayEarlyTermination));